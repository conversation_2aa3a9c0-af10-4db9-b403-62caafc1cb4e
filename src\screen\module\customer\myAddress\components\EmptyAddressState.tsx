import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import EmptyPage from '../../../../../project-component/empty-page';
import { EmptyAddressStateProps } from '../types';
import { TEXTS } from '../constants';

/**
 * Component to display empty state when no addresses are available
 */
export const EmptyAddressState: React.FC<EmptyAddressStateProps> = ({ 
  title = TEXTS.EMPTY_ADDRESS_TITLE 
}) => {
  return (
    <View style={styles.container}>
      <EmptyPage title={title} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: Dimensions.get('window').height / 2,
  },
});
