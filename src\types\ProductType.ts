export interface ProductItem {
  Id: string;
  Name?: string;
  Price?: number;
  Detail?: string;
  Description?: string;
  Img?: string;
  Imgs?: string;
  Source?: number;
  Guarantee?: number;
  Preserve?: number;
  Specifications?: string;
  Unit?: string;
  IsPublic?: boolean;
  Type?: number;
  CategoryId?: string;
  BrandsId?: string;
  ConsumeId?: string;
  DateCreated?: number;
  IsFavorite?: boolean;
  Discount?: number;
  rating?: number;
  Sold?: number;
}

export const ProductType = {
  bio: 1,
  material: 2,
  equipment: 3,
  other: 4,
};

export const ProductTypeStringData = [
  {key: 1, title: '<PERSON>ế phẩm sinh học'},
  {key: 2, title: '<PERSON><PERSON>ên liệu'},
  {key: 3, title: 'Thiết bị'},
  {key: 4, title: '<PERSON>h<PERSON><PERSON>'},
];
