import {useEffect, useState} from 'react';
import {DataController} from '../../../../../base-controller';
import {BaseDA} from '../../../../../baseDA';
import {Ultis} from '../../../../../../utils/Utils';
import {
  extractCustomerIds,
  parseTicketDetails,
} from 'screen/module/ticket/utils/ticketDetailParser';
import {TicketDa} from 'screen/module/ticket/ticketDa';

export interface TicketDataHookReturn {
  customers: Array<any>;
  files: Array<any>;
  loading: boolean;
  error: string | null;
}

export const useTicketData = (item: any): TicketDataHookReturn => {
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [files, setFiles] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!item) return;

    const fetchTicketData = async () => {
      setLoading(true);
      setError(null);

      try {
        const ticketDa = new TicketDa();
        const ticketData = await ticketDa.getById(item.Id);

        if (ticketData) {
          const details = parseTicketDetails(ticketData.Detail);

          // Fetch customers data
          if (ticketData.CustomerId || details.length) {
            const customerIds = extractCustomerIds(details);
            if (ticketData.CustomerId) customerIds.push(ticketData.CustomerId);

            // Remove duplicates and filter out null/undefined
            const uniqueCustomerIds = [...new Set(customerIds)].filter(Boolean);

            if (uniqueCustomerIds.length > 0) {
              const customerController = new DataController('Customer');
              const cusRes =
                await customerController.getByListId(uniqueCustomerIds);

              if (cusRes.code === 200) {
                setCustomers(
                  cusRes.data
                    .filter((e: any) => e !== undefined && e !== null)
                    .map((e: any) => ({
                      ...e,
                      bgColor: Ultis.generateDarkColorRgb(),
                    })),
                );
              }
            }
          }

          // Fetch files data
          const ticketWithFiles = ticketData as any;
          if (ticketWithFiles.File?.length) {
            const fileRes = await BaseDA.getFilesInfor(
              ticketWithFiles.File.split(','),
            );

            if (fileRes.code === 200) {
              setFiles(
                fileRes.data.filter((e: any) => e !== undefined && e !== null),
              );
            }
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchTicketData();
  }, [item]);

  return {
    customers,
    files,
    loading,
    error,
  };
};
