import React, {useEffect} from 'react';
import {View, ScrollView, StyleSheet} from 'react-native';
import FAQSearchHeader from './components/FAQSearchHeader';
import FAQItem from './components/FAQItem';
import FLoading from '../../../../component/Loading/FLoading';
import {useFAQData} from './hooks/useFAQData';
import {useFAQSearch} from './hooks/useFAQSearch';
import {useFAQExpansion} from './hooks/useFAQExpansion';
import TitleHeader from 'screen/layout/headers/TitleHeader';
import {ColorThemes} from 'assets/skin/colors';

export default function FAQView() {
  // Custom hooks
  const {faqCategory, dataFaq, isLoading, getData} = useFAQData();
  const {searchValue, setSearchValue, filterMethods, buildSearchQuery} =
    useFAQSearch();
  const {toggleFAQExpand, isExpanded} = useFAQExpansion();

  // Effect to fetch data when search or filter changes
  useEffect(() => {
    const searchQuery = buildSearchQuery();
    getData(searchQuery, filterMethods);
  }, [filterMethods.watch('AttributeId'), searchValue]);

  const handleFilterApply = (attributeIds: any[]) => {
    filterMethods.setValue('AttributeId', attributeIds);
  };

  return (
    <View style={styles.container}>
      <FLoading visible={isLoading} />
      <TitleHeader title="Câu hỏi thường gặp" />
      <FAQSearchHeader
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        filterMethods={filterMethods}
        faqCategory={faqCategory}
        onFilterApply={handleFilterApply}
      />
      <ScrollView style={styles.container}>
        {dataFaq.data.map((item: any, index: number) => {
          return (
            <FAQItem
              key={index}
              item={item}
              index={index}
              isExpanded={filterMethods.watch(`faq${item.Id}`)}
              isContentExpanded={isExpanded(item.Id)}
              onToggle={() => {
                filterMethods.setValue(
                  `faq${item.Id}`,
                  !filterMethods.getValues(`faq${item.Id}`),
                );
              }}
              onToggleContent={() => toggleFAQExpand(item.Id)}
            />
          );
        })}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
});
