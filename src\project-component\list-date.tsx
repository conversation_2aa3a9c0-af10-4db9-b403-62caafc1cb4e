import { differenceInDays } from "date-fns"
import { useState } from "react"
import { TouchableOpacity, View } from "react-native"
import { FlatList } from "react-native-gesture-handler"
import { ColorSkin } from "../assets/skin/colors"
import { TypoSkin } from "../assets/skin/typography"
import { Text } from "react-native-paper"

export const ListDate = ({ onChangeDate }: { onChangeDate: (date: Date) => void }) => {
    const today = new Date()
    const schedule = Array.from({ length: 31 }).map((_, i) => (new Date(today.getFullYear(), today.getMonth(), today.getDate() + i)))
    const [selectDate, setSelectDate] = useState(today)

    return <FlatList
        style={{ width: '100%', paddingTop: 4, paddingBottom: 16 }}
        contentContainerStyle={{ alignItems: 'stretch' }}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(_, index) => index.toString()}
        ItemSeparatorComponent={() => <View style={{ width: 10 }} />}
        data={schedule}
        renderItem={({ item, index }) => {
            const isSelected = differenceInDays(item, selectDate) === 0 && item.getDate() === selectDate.getDate()
            switch (item.getDay()) {
                case 0:
                    var weekdayTitle = 'CN'
                    break
                case 1:
                    weekdayTitle = 'T2'
                    break
                case 2:
                    weekdayTitle = 'T3'
                    break
                case 3:
                    weekdayTitle = 'T4'
                    break
                case 4:
                    weekdayTitle = 'T5'
                    break
                case 5:
                    weekdayTitle = 'T6'
                    break
                case 6:
                    weekdayTitle = 'T7'
                    break
                default:
                    weekdayTitle = ''
                    break
            }
            return <TouchableOpacity onPress={() => {
                if (!isSelected) {
                    setSelectDate(item)
                    onChangeDate(item)
                }
            }}>
                <View style={{ gap: 2, paddingVertical: 2, paddingHorizontal: 6, borderRadius: 4, backgroundColor: isSelected ? ColorSkin.primary : '#00000000' }}>
                    <Text style={[TypoSkin.title5, { color: isSelected ? '#fff' : '#00204D', textAlign: 'center' }]}>{weekdayTitle}</Text>
                    <Text style={[TypoSkin.subtitle4, { color: isSelected ? '#fff' : ColorSkin.subtitle }]}>{`${item.getDate()}/${item.getMonth() + 1}`}</Text>
                </View>
            </TouchableOpacity>
        }}
    />
}