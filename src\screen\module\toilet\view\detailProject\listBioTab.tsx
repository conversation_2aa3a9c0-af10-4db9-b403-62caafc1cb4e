import React, {useRef, useState, useMemo, useEffect, forwardRef} from 'react';
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import ListTile from '../../../../../component/list-tile/list-tile';
import EmptyPage from '../../../../../project-component/empty-page';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import {
  DeviceBioStatus,
  ToiletServiceStatus,
} from '../../../service/components/da';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import {CardToiletHoriSkeleton} from '../../../../../project-component/skeletonCard';
import {BaseDA} from '../../../../baseDA';
import {
  FDialog,
  FTextField,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import AppButton from '../../../../../component/button';
import WScreenFooter from '../../../../layout/footer';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {useForm} from 'react-hook-form';
import DatePicker from 'react-native-date-picker';
import ImageCropPicker from 'react-native-image-crop-picker';
import {TextFieldForm} from '../../../../../project-component/component-form';
import ScreenHeader from '../../../../layout/header';
import {PopupSelectDevices} from '../../../workplace/components/popup/PopupSelectDevices';

interface Props {
  data: any;
  serviceData: any;
  refreshing: any;
  onRefresh: any;
  disabled?: any;
  formId?: any;
}

export default function ToiletListBioProductTab(props: Props) {
  const {data, serviceData, refreshing, onRefresh, disabled, formId} = props;

  const user = useSelectorCustomerState().data;
  const popupRef = useRef<any>();
  const [devices, setDevices] = useState<any>({
    data: [],
    totalCount: undefined,
  });
  const [pageDetails, setPageDetails] = useState({page: 1, size: 10});
  const [isLoading, setLoading] = useState(false);

  const [selected, setSelected] = useState<Array<any>>([]);
  const [products, setProducts] = useState<Array<any>>([]);
  const [files, setFiles] = useState<Array<any>>([]);
  const [services, setServices] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');
  const dialogRef = useRef<any>();

  const getData = async ({page, size}: any) => {
    setLoading(true);
    const bioController = new DataController('BioProduct');
    let query = `@ToiletId:{${data.Id}}`;
    if (searchValue?.length) query += ` @Name:(*${searchValue}*)`;
    const res = await bioController.aggregateList({
      page: page ?? 1,
      size: size ?? 20,
      searchRaw: query,
    });
    if (res.code === 200) {
      const serviceIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      const toiletServicesController = new DataController('ToiletServices');
      toiletServicesController.getByListId(serviceIds).then(servicesRes => {
        if (servicesRes.code === 200) {
          setServices(servicesRes.data);
        }
      });
      const productIds = res.data
        .filter((e: any) => e?.ProductId)
        .map((e: any) => e?.ProductId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      const _tmpFileIds = res.data
        .map((e: any) => e?.Img?.split(','))
        .flat(Infinity)
        .filter(
          (id: any) =>
            id?.length &&
            files.every((e: any) => e.Id !== id) &&
            !id.startsWith('http'),
        );
      if (productIds?.length) {
        const productController = new DataController('Product');
        const productRes = await productController.getByListId(productIds);
        if (productRes.code === 200) {
          setProducts(productRes.data);
          _tmpFileIds.push(
            ...productRes.data
              .map((e: any) => e?.Img?.split(','))
              .flat(Infinity)
              .filter(
                (id: any) =>
                  id?.length &&
                  files.every((e: any) => e.Id !== id) &&
                  !id.startsWith('http'),
              ),
          );
        }
      }
      BaseDA.getFilesInfor(_tmpFileIds).then(fileRes => {
        if (fileRes.code === 200)
          setFiles([
            ...files,
            ...fileRes.data.filter((e: any) => e !== undefined && e !== null),
          ]);
      });
      setLoading(false);

      setDevices({data: res.data, totalCount: res.totalCount});
    }
    setLoading(false);
  };

  useEffect(() => {
    if (data) getData({});
  }, [data]);

  const deleteItem = (ids = Array<any>()) => {
    const controller = new DataController('BioProduct');
    controller.delete(ids).then(res => {
      if (res.code === 200) {
        setSelected(selected.filter((id: any) => !ids.includes(id)));
        showSnackbar({
          message: 'Đã xóa thành công',
          status: ComponentStatus.SUCCSESS,
        });
        setDevices({
          data: devices.data.filter((e: any) => !ids.includes(e.Id)),
        });
        getData({});
      } else
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
    });
  };

  const showPopupSelectProduct = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupSelectDevices
          ref={popupRef}
          isBio={true}
          selectOther={() => {
            closePopup(popupRef);
            showAddEdit(undefined, true);
          }}
          onSubmit={async (selectedProducts: any) => {
            const controller = new DataController('BioProduct');
            const newListDevice = selectedProducts.map((p: any) => {
              return {
                Id: randomGID(),
                Name: p.Name,
                DateCreated: Date.now(),
                ToiletId: data.Id,
                ProductId: p.Id,
                Quantity: p['_Quantity'],
                Price: p.Price,
                Unit: p.Unit,
                Img: p.Img,
                Description: p.Description,
                Specifications: p.Specifications,
                Status: formId
                  ? DeviceBioStatus.inactive
                  : DeviceBioStatus.active,
              };
            });
            const res = await controller.add(newListDevice);
            if (res.code === 200) getData({});
          }}
        />
      ),
    });
  };

  const showAddEdit = (item: any, isAddNew = false) => {
    if (item || isAddNew) {
      showPopup({
        ref: popupRef,
        enableDismiss: true,
        children: (
          <PopupAddEditDevice
            ref={popupRef}
            id={item?.Id}
            formId={formId}
            toiletId={data.Id}
            onSubmit={() => {
              getData({});
            }}
          />
        ),
      });
    } else {
      showPopupSelectProduct();
    }
  };

  return (
    <View
      style={{
        flex: 1,
        height: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      {!formId ? (
        <View
          style={{flexDirection: 'row', width: '100%', paddingHorizontal: 16}}>
          <FTextField
            style={{paddingHorizontal: 16, width: '100%'}}
            onChange={(vl: any) => {
              setSearchValue(vl);
            }}
            value={searchValue}
            onBlur={() => getData({})}
            onSubmit={() => getData({})}
            placeholder="Tìm kiếm chế phẩm sinh học"
          />
        </View>
      ) : null}
      <FlatList
        nestedScrollEnabled
        data={devices.data}
        scrollEnabled={formId ? false : true}
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={() => {
              if (onRefresh) onRefresh();
            }}
          />
        }
        style={{flex: 1, gap: 8, marginHorizontal: 16, marginVertical: 16}}
        ItemSeparatorComponent={() => <View style={{height: 8}} />}
        renderItem={({item, index}: {item: any; index: number}) => {
          const discount =
            (item?.Price * item?.Quantity * (item?.Discount ?? 0)) / 100;
          let devTotalPrice = item?.Price * item?.Quantity - discount;
          const vat = devTotalPrice * ((item.Vat ?? 0) / 100);
          if (item.ProductId) {
            var _product = products?.find(e => e?.Id === item?.ProductId);
          }
          const _fileInfor = files
            .filter(e => (_product ?? item).Img?.includes(e.Id))
            .filter((e, i, a) => a.findIndex(eL => eL.Id === e.Id) === i);

          return (
            <ListTile
              key={`${item?.Id} bio ${index}`}
              leading={
                _fileInfor.length ? (
                  <SkeletonImage
                    source={{uri: ConfigAPI.imgUrlId + _fileInfor[0]?.Id}}
                    style={{
                      width: 55,
                      height: 60,
                      borderRadius: 4,
                      overflow: 'hidden',
                      paddingTop: 6,
                    }}
                  />
                ) : (
                  <View />
                )
              }
              style={{
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
                borderRadius: 8,
              }}
              listtileStyle={{gap: 16}}
              title={`${index + 1}. ${item?.Name ?? '-'}`}
              titleStyle={{...TypoSkin.title3, paddingBottom: 8}}
              bottom={
                <View
                  style={{
                    flex: 1,
                    alignItems: 'flex-start',
                    width: '100%',
                    paddingTop: 4,
                    gap: 4,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`Ngày tạo: ${item.DateCreated ? Ultis.datetoString(new Date(item.DateCreated)) : '-'}`}</Text>
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`Số lượng: ${item?.Quantity ?? '-'} ${item.Unit ?? _product?.Unit ?? '-'}`}</Text>
                  {!formId ? (
                    <View
                      style={{
                        flex: 1,
                        alignItems: 'flex-start',
                        width: '100%',
                        gap: 4,
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Giá tiền (VNĐ): ${Ultis.money(item.Price) ?? '-'}`}</Text>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Giảm giá (VNĐ): ${Ultis.money(discount) ?? '-'} - Thuế VAT (VNĐ): ${Ultis.money(vat) ?? '-'}`}</Text>
                      <Text
                        style={{
                          ...TypoSkin.body2,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Thành tiền: ${Ultis.money(devTotalPrice + vat)}`}</Text>
                    </View>
                  ) : null}
                  {!disabled ? (
                    <View
                      style={{
                        alignSelf: 'flex-end',
                        flex: 1,
                        gap: 16,
                        paddingTop: 16,
                        flexDirection: 'row',
                      }}>
                      <Winicon
                        src="outline/user interface/s-edit"
                        size={16}
                        onClick={() => {
                          showAddEdit(item);
                        }}
                      />
                      {data?.CustomerId === user?.Id ||
                      item.Status !== DeviceBioStatus.active ? (
                        <Winicon
                          src="outline/user interface/trash-can"
                          size={16}
                          onClick={() => {
                            showDialog({
                              ref: dialogRef,
                              status: ComponentStatus.WARNING,
                              title: 'Bạn chắc chắn muốn xóa',
                              onSubmit: () => {
                                deleteItem([item?.Id]);
                              },
                            });
                          }}
                        />
                      ) : null}
                    </View>
                  ) : null}
                </View>
              }
              subtitle={`${Ultis.money(item.Price)} VNĐ`}
            />
          );
        }}
        ListEmptyComponent={() =>
          isLoading && searchValue != '' ? (
            Array.from(Array(10)).map((_, index) => (
              <View key={index} style={{marginBottom: 16}}>
                <CardToiletHoriSkeleton />
              </View>
            ))
          ) : (
            <View
              style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
              <EmptyPage title="Nhà vệ sinh chưa có chế phẩm sinh học nào" />
            </View>
          )
        }
        ListFooterComponent={() => <View style={{height: 65}} />}
      />
      {!disabled ? (
        <WScreenFooter style={{paddingHorizontal: 16}}>
          <AppButton
            title={'Thêm chế phẩm sinh học'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              showAddEdit(null);
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      ) : null}
    </View>
  );
}

const PopupAddEditDevice = forwardRef(function PopupAddEditDevice(
  data: {toiletId: any; id: any; onSubmit: any; formId: any},
  ref: any,
) {
  const {toiletId, id, onSubmit, formId} = data;
  const dialogRef = useRef<any>();
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      ToiletId: toiletId,
      Status: formId ? DeviceBioStatus.inactive : DeviceBioStatus.active,
      Price: undefined,
      Discount: undefined,
      Vat: undefined,
    },
  });

  const controller = new DataController('BioProduct');
  const popupRef = useRef<any>();
  const [img, setImg] = useState<Array<any>>([]);

  useEffect(() => {
    if (id) {
      controller.getById(id).then(async res => {
        if (res.code === 200) {
          if (res.data.ProductId) {
            const productController = new DataController('Product');
            const product = await productController.getById(res.data.ProductId);
            if (product.code === 200) {
              res.data.Img = product.data.Img;
              res.data.Description = product.data.Specifications;
              res.data.Unit = product.data.Unit;
            }
          }
          Object.keys(res.data).forEach(key => {
            if (key === 'Price') {
              methods.setValue(key, Ultis.money(res.data[key]));
            } else {
              methods.setValue(key, `${res.data[key] ?? ''}`);
            }
          });
          const _tmpFileIds = res.data.Img;
          BaseDA.getFilesInfor([_tmpFileIds]).then(resFile => {
            if (resFile.code === 200)
              setImg([
                ...resFile.data.filter(
                  (e: any) => e !== undefined && e !== null,
                ),
              ]);
          });
        }
      });
    }
  }, []);

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 1,
    });
    if (image) {
      _uploadFiles({
        name: image.filename ?? 'new file img',
        type: image.mime,
        uri: image.path,
      });
    }
  };

  const _uploadFiles = async (files: {
    uri: string;
    type: string;
    name: string;
  }) => {
    const res = await BaseDA.uploadFiles([files]);
    if (res?.length) {
      setImg([res[0]]);
      methods.setValue('Img', res.map((e: any) => e.Id).join(','));
    }
  };

  const _onSubmit = async () => {
    var item = {
      ...methods.getValues(),
      Img: methods.watch('Img') ? methods.watch('Img') : undefined,
      Quantity: methods.getValues('Quantity')
        ? parseInt(methods.getValues('Quantity'))
        : undefined,
      Vat: methods.getValues('Vat')
        ? parseInt(methods.getValues('Vat'))
        : undefined,
      Discount: methods.getValues('Discount')
        ? parseInt(methods.getValues('Discount'))
        : undefined,
      Price: parseInt(methods.getValues('Price').replace(/,/g, '')),
      DateCreated: Date.now(),
    };

    if (id) {
      controller.edit([item]).then(() => {
        onSubmit();
      });
    } else {
      controller.add([item]).then(() => {
        onSubmit();
      });
    }
    closePopup(ref);
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={
          id ? `Chỉnh sửa chế phẩm sinh học` : 'Thêm mới chế phẩm sinh học'
        }
        prefix={<View />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 75 : 0}
        style={{height: '100%', width: '100%', paddingHorizontal: 16}}>
        <ScrollView>
          <View style={{gap: 18, paddingBottom: 156}}>
            <TextFieldForm
              required
              label="Tên chế phẩm"
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Name"
            />
            <View style={{gap: 8}}>
              <Text numberOfLines={1} style={TypoSkin.label3}>
                Ảnh
              </Text>
              {methods.watch('Img') && img?.length > 0 ? null : (
                <TouchableOpacity
                  onPress={() => {
                    pickerImg();
                  }}
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    borderWidth: 0.4,
                    borderColor: ColorThemes.light.neutral_main_border,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                  }}>
                  <SkeletonImage
                    source={{
                      uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                    }}
                    style={{width: 35, height: 35, objectFit: 'cover'}}
                  />
                  <Text
                    numberOfLines={1}
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    Thêm ảnh
                  </Text>
                </TouchableOpacity>
              )}
              {methods.watch('Img') &&
                img?.map((item: any, index: number) => {
                  return (
                    <ListTile
                      key={`${index}`}
                      leading={
                        <SkeletonImage
                          source={{uri: ConfigAPI.imgUrlId + item.Id}}
                          style={{width: 65, height: 65, objectFit: 'cover'}}
                        />
                      }
                      title={item?.Name ?? `Ảnh ${index + 1}`}
                      titleStyle={[
                        TypoSkin.heading7,
                        {color: ColorThemes.light.neutral_text_title_color},
                      ]}
                      subtitle={`${Math.round(item.Size / (1024 * 1024))}MB`}
                      listtileStyle={{gap: 16}}
                      style={{
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                        padding: 8,
                      }}
                      trailing={
                        <TouchableOpacity
                          onPress={async () => {
                            methods.setValue('Img', undefined);
                            setImg([]);
                          }}
                          style={{padding: 4}}>
                          <FontAwesomeIcon
                            icon={faMinusCircle}
                            size={20}
                            color="#D72525FF"
                            style={{backgroundColor: '#fff', borderRadius: 20}}
                          />
                        </TouchableOpacity>
                      }
                    />
                  );
                })}
            </View>
            <TextFieldForm
              required
              label="Số lượng"
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              type="number-pad"
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Quantity"
            />
            <TextFieldForm
              label="Đơn vị"
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Unit"
            />
            <TextFieldForm
              label="Giá"
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Price"
              type="money"
              returnKeyType="done"
              onBlur={async (value: any) => {
                if (!value) return;
                let newPrice = parseInt(value.replaceAll(',', ''));
                if (!isNaN(newPrice)) {
                  value = Ultis.money(newPrice);
                } else value = Ultis.money(methods.getValues('Price'));
              }}
            />
            <TextFieldForm
              control={methods.control}
              name="Description"
              label="Mô tả"
              errors={methods.formState.errors}
              placeholder={'Mô tả khác'}
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 100,
                width: '100%',
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              register={methods.register}
            />
            {formId ? (
              <View style={{flex: 1, gap: 18}}>
                <TextFieldForm
                  label="Vat"
                  textFieldStyle={{padding: 16}}
                  style={{width: '100%'}}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Vat"
                />
                <TextFieldForm
                  label="Discount"
                  textFieldStyle={{padding: 16}}
                  style={{width: '100%'}}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Discount"
                />
              </View>
            ) : null}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
        }}>
        <AppButton
          title={id ? 'Sửa' : 'Thêm mới'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            methods.handleSubmit(_onSubmit, _onError)();
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
