import {
  Alert,
  Dimensions,
  FlatList,
  Linking,
  PermissionsAndroid,
  Platform,
  Pressable,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  ToiletStatus,
  ToiletServiceStatus,
  ToiletFileType,
  TaskType,
} from '../../../service/components/da';
import {useRef, useState, useMemo, useEffect} from 'react';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {
  CustomerType,
  CustomerRole,
} from '../../../../../redux/reducers/user/da';
import {DataController} from '../../../../base-controller';
import {BaseDA} from '../../../../baseDA';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import ImageCropPicker from 'react-native-image-crop-picker';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import ListTile from '../../../../../component/list-tile/list-tile';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {
  FDialog,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import ScreenHeader from '../../../../layout/header';
import DocumentPicker, {types} from 'react-native-document-picker';
import ConfigAPI from '../../../../../config/configApi';
import WebView from 'react-native-webview';
import {ButtonViewRejectReason} from '../popup/DialogCustomize';
import {popupReject} from './QuoteTable';
import {useForm} from 'react-hook-form';

export default function DesignTab({
  toiletData,
  serviceData,
  onSubmit,
  onReject,
  setServiceData,
  isRefreshing,
  onRefreshing,
  customer,
}: any) {
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const userRole = useSelectorCustomerState().role;
  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();
  const [files, setFiles] = useState({data: [], totalCount: undefined});
  const [pageDetails, setPageDetails] = useState({page: 1, size: 10});
  const [consultantTask, setConsultantTask] = useState<any>();
  const data = useMemo(() => toiletData[0] ?? undefined, [toiletData]);
  const isEditable = useMemo(() => {
    return (
      serviceData?.Status === ToiletServiceStatus.design &&
      (user?.Type === CustomerType.partner ||
        consultantTask?.CustomerId === user?.Id ||
        userRole?.Role?.includes(CustomerRole.Coordinator) ||
        userRole?.Role?.includes(CustomerRole.Owner))
    );
  }, [user, data, serviceData, userRole, consultantTask]);

  const rejectReasons = useMemo(
    () =>
      serviceData?.RejectReason ? JSON.parse(serviceData.RejectReason) : [],
    [serviceData],
  );
  const methods = useForm({shouldFocusError: false});

  const _uploadFiles = async (files: {
    uri: string;
    type: string;
    name: string;
  }) => {
    if (files) {
      const controller = new DataController('ToiletFile');
      const res = await BaseDA.uploadFiles([files]);
      if (res.length) {
        const fileRes = await controller.add(
          res.map((e: any) => {
            return {
              Id: randomGID(),
              DateCreated: Date.now(),
              Name: e.Name,
              File: e.Id,
              Size: e.Size,
              Url: e.Url,
              Type: ToiletFileType.design,
              ToiletServicesId: serviceData.Id,
              // Description: `${e.Type} - ${e.Url}`,
              Description: '',
              Sort: 1,
            };
          }),
        );
        if (fileRes.code === 200)
          getData({page: pageDetails.page, size: pageDetails.size});
      }
    }
  };

  const getData = async ({page, size}: any) => {
    const taskController = new DataController('Task');

    const controller = new DataController('ToiletFile');
    const res = await controller.aggregateList({
      page: page ?? 1,
      size: size ?? 10,
      searchRaw: `@ToiletServicesId:{${serviceData?.Id}} @Type:[${ToiletFileType.design} ${ToiletFileType.design}]`,
      sortby: [
        {prop: 'Sort', direction: 'DESC'},
        {prop: 'DateCreated', direction: 'DESC'},
      ],
    });

    if (res.code === 200)
      setFiles({data: res.data, totalCount: res.totalCount});
    taskController
      .aggregateList({
        page: 1,
        size: 1,
        searchRaw: `@ToiletServicesId:{${serviceData?.Id}} @Type:[${TaskType.design} ${TaskType.design}]`,
      })
      .then(res => {
        if (res.code === 200) setConsultantTask(res.data[0] ?? 'Empty');
      });
  };

  useEffect(() => {
    if (serviceData) getData({});
  }, [serviceData]);

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 5,
    });
    if (image) {
      _uploadFiles({
        name: image.filename ?? 'new file img',
        type: image.mime,
        uri: image.path,
      });
      closePopup(popupRef);
    }
  };

  const pickerFile = async () => {
    try {
      const result = await DocumentPicker.pick({
        // allowMultiSelection: false,
        // type: [DocumentPicker.types.pdf],
      });
      if (result) {
        const {name, size, type, uri} = result[0];
        _uploadFiles({
          name: name ?? 'new file img',
          type: type ?? 'unknown',
          uri: uri,
        });
        closePopup(popupRef);

        return {
          name,
          type,
          uri,
          size,
        };
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the document picker
        console.log('Document picker cancelled by user');
      } else {
        // Handle other errors
        console.log('Error picking document:', err);
      }
      return null;
    }
  };
  if (!data) return null;

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      {consultantTask === 'Empty' ? (
        <EmptyPage title="Đơn hàng bỏ qua bước thiết kế" />
      ) : (
        <View style={{flex: 1}}>
          {rejectReasons.length > 0 ? (
            <View style={{width: '100%'}}>
              <ButtonViewRejectReason
                customers={[customer.data]}
                rejectReasons={rejectReasons}
              />
            </View>
          ) : null}
          <FlatList
            contentContainerStyle={{
              paddingBottom: isEditable ? 56 : 16,
              paddingTop: 16,
            }}
            data={files.data}
            style={{flex: 1, paddingHorizontal: 16}}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing ?? false}
                onRefresh={() => {
                  onRefreshing();
                  getData({page: pageDetails.page, size: pageDetails.size});
                }}
              />
            }
            keyExtractor={(item, i) => item + '-' + i}
            ItemSeparatorComponent={() => <View style={{width: 12}} />}
            ListEmptyComponent={() => (
              <EmptyPage title="Không có bản thiết kế nào" />
            )}
            ListHeaderComponent={() => {
              if (!isEditable) return null;
              return (
                <TouchableOpacity
                  onPress={() => {
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            height: Dimensions.get('window').height / 3,
                            borderTopLeftRadius: 12,
                            borderTopRightRadius: 12,
                          }}>
                          <ScreenHeader
                            style={{
                              backgroundColor: ColorThemes.light.transparent,
                              flexDirection: 'row',
                              paddingVertical: 4,
                            }}
                            title={`Thêm mới`}
                            prefix={<View />}
                            action={
                              <View
                                style={{
                                  flexDirection: 'row',
                                  padding: 12,
                                  alignItems: 'center',
                                }}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  onClick={() => closePopup(popupRef)}
                                  size={20}
                                  color={
                                    ColorThemes.light.neutral_text_body_color
                                  }
                                />
                              </View>
                            }
                          />
                          <ListTile
                            onPress={() => {
                              pickerImg();
                            }}
                            title={'Thêm ảnh, video'}
                          />
                          <ListTile
                            onPress={() => {
                              pickerFile();
                            }}
                            title={'Thêm tệp'}
                          />
                        </View>
                      ),
                    });
                  }}
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    borderWidth: 0.4,
                    borderColor: ColorThemes.light.neutral_main_border,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                    marginTop: 12,
                  }}>
                  <SkeletonImage
                    source={{
                      uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                    }}
                    style={{width: 35, height: 35, objectFit: 'cover'}}
                  />
                  <Text
                    numberOfLines={1}
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    Thêm ảnh, tệp
                  </Text>
                </TouchableOpacity>
              );
            }}
            renderItem={({item, index}: any) => {
              return (
                <ListTile
                  key={`${item.Url}`}
                  onPress={async () => {
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            height: Dimensions.get('window').height - 65,
                            borderTopLeftRadius: 12,
                            borderTopRightRadius: 12,
                          }}>
                          <ScreenHeader
                            style={{
                              backgroundColor: ColorThemes.light.transparent,
                              flexDirection: 'row',
                              paddingVertical: 4,
                            }}
                            title={`Xem nhanh`}
                            prefix={<View />}
                            action={
                              <TouchableOpacity
                                onPress={() => closePopup(popupRef)}
                                style={{padding: 12, alignItems: 'center'}}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  size={20}
                                  color={
                                    ColorThemes.light.neutral_text_body_color
                                  }
                                />
                              </TouchableOpacity>
                            }
                          />
                          <WebView
                            onHttpError={() => {
                              console.log('onHttpError');
                            }}
                            style={{
                              flex: 1,
                              backgroundColor:
                                ColorThemes.light
                                  .neutral_absolute_background_color,
                            }}
                            source={{
                              uri:
                                ConfigAPI.url.replace('/api/', '') + item.Url,
                            }}
                          />
                        </View>
                      ),
                    });
                  }}
                  leading={
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      {index + 1}
                    </Text>
                  }
                  title={item.Name ?? `Ảnh ${index + 1}`}
                  titleStyle={[
                    TypoSkin.heading7,
                    {color: ColorThemes.light.neutral_text_title_color},
                  ]}
                  subtitle={
                    <View style={{gap: 4}}>
                      <Text
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>
                        {Ultis.formatFileSize(item.Size)}
                      </Text>
                      <Text
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>
                        {item.DateCreated
                          ? Ultis.datetoString(
                              new Date(item.DateCreated),
                              'dd/mm/yyyy hh:mm',
                            )
                          : '-'}
                      </Text>
                      <Text
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}
                        numberOfLines={3}>
                        {item.Description}
                      </Text>
                    </View>
                  }
                  listtileStyle={{gap: 16}}
                  style={{
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderWidth: 1,
                    marginTop: 16,
                    padding: 16,
                  }}
                  trailing={
                    isEditable ? (
                      <TouchableOpacity
                        onPress={() => {
                          showDialog({
                            status: ComponentStatus.WARNING,
                            ref: dialogRef,
                            title: 'Bạn chắc chắn muốn xóa tệp này?',
                            onSubmit: async () => {
                              const controller = new DataController(
                                'ToiletFile',
                              );
                              const res = await controller.delete([item.Id]);
                              if (res.code === 200) {
                                showSnackbar({
                                  message: `Xóa file ${item.Name} thành công!`,
                                  status: ComponentStatus.SUCCSESS,
                                });
                                getData({});
                              } else
                                showSnackbar({
                                  message: res.message,
                                  status: ComponentStatus.ERROR,
                                });
                            },
                          });
                        }}
                        style={{padding: 4}}>
                        <FontAwesomeIcon
                          icon={faMinusCircle}
                          size={20}
                          color="#D72525FF"
                          style={{backgroundColor: '#fff', borderRadius: 20}}
                        />
                      </TouchableOpacity>
                    ) : (
                      <View />
                    )
                  }
                />
              );
            }}
          />
          {user?.Id === data?.CustomerId &&
            data?.Status !== ToiletStatus.liquid &&
            serviceData?.Status === ToiletServiceStatus.sendCompleteDesign && (
              <View style={styles.bottomButtons}>
                <TouchableOpacity
                  style={{
                    ...styles.button,
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                  }}
                  onPress={() => {
                    popupReject({
                      ref: dialogRef,
                      title: 'Bạn chắc chắn muốn từ chối thiết kế này?',
                      methods: methods,
                      onSubmit: async (ev: any) => {
                        const newRejectReason = {
                          DateCreated: Date.now(),
                          Content: ev,
                        };
                        const controller = new DataController('ToiletServices');
                        const res = await controller.edit([
                          {
                            ...serviceData,
                            Status: ToiletServiceStatus.design,
                            RejectReason: JSON.stringify([
                              ...rejectReasons,
                              newRejectReason,
                            ]),
                          },
                        ]);
                        if (res.code === 200)
                          setServiceData({
                            ...serviceData,
                            Status: ToiletServiceStatus.design,
                            RejectReason: JSON.stringify([
                              ...rejectReasons,
                              newRejectReason,
                            ]),
                          });
                      },
                    });
                  }}>
                  <Text
                    style={{
                      ...styles.buttonText,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    Từ chối
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.button}
                  onPress={() => {
                    showDialog({
                      status: ComponentStatus.SUCCSESS,
                      ref: dialogRef,
                      title: 'Bạn chắc chắn muốn thống nhất thiết kế này?',
                      content:
                        'Đơn hàng sẽ được thực hiện hợp đồng theo thiết kế này',
                      onSubmit: onSubmit,
                    });
                  }}>
                  <Text style={styles.buttonText}>Thống nhất thiết kế</Text>
                </TouchableOpacity>
              </View>
            )}
          {isEditable ? (
            <View style={styles.bottomButtons}>
              <TouchableOpacity
                style={styles.button}
                onPress={() => {
                  showDialog({
                    ref: dialogRef,
                    status: ComponentStatus.INFOR,
                    title: 'Bạn chắc chắn muốn gửi thiết kế này?',
                    onSubmit: async () => {
                      const controller = new DataController('ToiletServices');
                      controller.edit([
                        {
                          ...serviceData,
                          Status: ToiletServiceStatus.sendCompleteDesign,
                        },
                      ]);
                      setServiceData({
                        ...serviceData,
                        Status: ToiletServiceStatus.sendCompleteDesign,
                      });
                    },
                  });
                }}>
                <Text style={styles.buttonText}>Gửi thiết kế</Text>
              </TouchableOpacity>
            </View>
          ) : null}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  bottomButtons: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 10,
    height: 60,
    gap: 8,
    marginHorizontal: 10,
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_background_color,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
