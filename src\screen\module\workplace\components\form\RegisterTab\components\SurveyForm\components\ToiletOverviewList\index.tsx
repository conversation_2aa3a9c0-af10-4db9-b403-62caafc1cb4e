import {FlatList, View} from 'react-native';
import {useEffect, useState} from 'react';
import ToiletSelectionCard from 'screen/module/toilet/components/card/ToiletSelectionCard';
import {ToiletOverviewBottomSheet} from '../ToiletOverviewBottomSheet';
import {DataController} from 'screen/base-controller';
import {ToiletServiceItem} from 'types/toiletServiceType';
import {ToiletItem} from 'types/toiletType';

const ToiletOverviewList = ({
  toiletIds,
  dataService,
}: {
  toiletIds: string[];
  dataService: ToiletServiceItem;
}) => {
  const [listToilet, setListToilet] = useState<ToiletItem[]>([]);
  const [visible, setVisible] = useState(false);
  const [toiletSelected, setToiletSelected] = useState<string>('');

  useEffect(() => {
    const toiletController = new DataController('Toilet');
    toiletController.getByListId(toiletIds).then(res => {
      setListToilet(res.data);
    });
  }, [toiletIds]);

  const handleToiletCardPress = (item: ToiletItem) => {
    setToiletSelected(item.Id);
    setVisible(true);
  };

  return (
    <View>
      {listToilet.length > 0 && (
        <FlatList
          data={listToilet}
          scrollEnabled={false}
          renderItem={({item}) => (
            <ToiletSelectionCard
              item={item}
              showSelect={false}
              onPress={() => handleToiletCardPress(item)}
            />
          )}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          style={{marginBottom: 16}}
        />
      )}
      <ToiletOverviewBottomSheet
        visible={visible}
        onClose={() => setVisible(false)}
        toiletId={toiletSelected}
        dataService={dataService}
      />
    </View>
  );
};

export default ToiletOverviewList;
