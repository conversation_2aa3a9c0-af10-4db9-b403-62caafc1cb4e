import React from 'react';
import {Text, View, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {Ultis} from '../../../../../../utils/Utils';
import {RootScreen} from '../../../../../../router/router';

interface TicketInfoProps {
  item: any;
  customer?: any;
  relativeData?: any;
}

export const TicketInfo: React.FC<TicketInfoProps> = ({
  item,
  customer,
  relativeData,
}) => {
  const navigation = useNavigation<any>();

  const handleRelativeDataPress = () => {
    if (relativeData) {
      navigation.push(RootScreen.detailProject, {
        item: {
          ToiletId: relativeData.ToiletId ?? relativeData.Id,
        },
      });
    }
  };

  return (
    <View style={styles.container}>
      {customer && (
        <Text style={styles.infoText}>
          {`Người tạo: ${customer?.Name ?? '-'}`}
        </Text>
      )}
      {customer && (
        <Text style={styles.infoText}>
          {`Số điện thoại: ${customer?.Mobile ?? '-'}`}
        </Text>
      )}
      <Text style={styles.infoText}>
        {`Ngày tạo: ${
          item.DateCreated ? Ultis.datetoString(new Date(item.DateCreated)) : ''
        }`}
      </Text>
      <Text
        onPress={relativeData ? handleRelativeDataPress : undefined}
        style={[styles.infoText, relativeData && styles.linkText]}
        numberOfLines={2}
        ellipsizeMode="tail">
        Đối tượng liên quan:{' '}
        {relativeData ? `${relativeData?.Name ?? '-'}` : 'Phản hồi về KTX'}
      </Text>
      <Text style={styles.infoText} numberOfLines={2} ellipsizeMode="tail">
        {`Mô tả: ${item?.Description?.trim() ?? '-'}`}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
    width: '100%',
    gap: 8,
    paddingTop: 16,
  },
  infoText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  linkText: {
    textDecorationLine: 'underline',
  },
});
