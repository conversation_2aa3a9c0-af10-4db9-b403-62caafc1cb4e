import React, {useEffect} from 'react';
import {View} from 'react-native';
import TitleHeader from '../../layout/headers/TitleHeader';
import OperationalStatisticsDetail from './Component/OperationalStatisticsDetail';
import {ColorThemes} from '../../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import OperationalStatisticsActionForCustomer from './Component/OperationalStatisticsActionForCustomer';

const OperationalStaticDetailPage = () => {
  const route = useRoute<any>();
  const item = route.params?.item;
  const listToilet = route.params?.listToilet;

  return (
    <View style={{flex: 1, backgroundColor: ColorThemes.light.white}}>
      <TitleHeader title="Thống kê vận hành" />
      <View style={{flex: 1}}>
        {[2].includes(item?.ApproveStatus) ? (
          <OperationalStatisticsDetail item={item} />
        ) : (
          <OperationalStatisticsActionForCustomer
            item={item}
            listToilet={listToilet}
          />
        )}
      </View>
    </View>
  );
};

export default OperationalStaticDetailPage;
