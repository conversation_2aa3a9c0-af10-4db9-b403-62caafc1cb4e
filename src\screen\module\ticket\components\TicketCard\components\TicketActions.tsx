import React from 'react';
import {View, TouchableOpacity, StyleSheet, Text} from 'react-native';
import {ColorSkin} from 'assets/skin/colors';

interface TicketActionsProps {
  checkEdit: boolean;
  onEditPress: () => void;
}

export const TicketActions: React.FC<TicketActionsProps> = ({
  checkEdit,
  onEditPress,
}) => {
  return (
    <View style={styles.container}>
      {checkEdit && (
        <TouchableOpacity style={styles.actionButton} onPress={onEditPress}>
          <Text style={styles.actionText}>Phản hồi</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    flex: 1,
    justifyContent: 'flex-end',
  },
  actionButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: ColorSkin.primary,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionText: {
    color: ColorSkin.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
});
