import React from 'react';
import {
  Button,
  DimensionValue,
  FlexStyle,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import {SvgXml} from 'react-native-svg';

interface Props {
  /**
   * <PERSON><PERSON>ồn ảnh SVG
   */
  SvgSrc: any;
  /**
   * <PERSON><PERSON>ch thước
   */
  size: number;
  style?: ViewStyle;
  /**
   * Màu
   */
  color?: string;
  height?: number;
  width?: number;
  isSquare?: boolean;
}

function AppSvg(props: Props) {
  const {
    SvgSrc = ``,
    size = 24,
    style = {},
    color = '',
    height,
    width,
    isSquare = true,
  } = props;

  const modifiedColor = color
    ? SvgSrc.replaceAll('fill="black"', `fill="${color}"`).replaceAll(
        'fill="currentColor"',
        `fill="${color}"`,
      )
    : SvgSrc;

  return (
    <View
      style={{
        height: isSquare ? size : height,
        width: isSquare ? size : width,
        justifyContent: 'center',
        alignItems: 'center',
        ...style,
      }}>
      {SvgSrc ? (
        <SvgXml xml={modifiedColor} width={size} height={size} />
      ) : (
        <View />
      )}
    </View>
  );
}

export default AppSvg;
