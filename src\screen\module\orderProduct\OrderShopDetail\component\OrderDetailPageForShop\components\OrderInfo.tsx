import React from 'react';
import {View, Text, TouchableOpacity, Clipboard, StyleSheet} from 'react-native';
import {Winicon, ComponentStatus} from 'wini-mobile-components';
import {StatusOrder} from '../../../../../../../config/Contanst';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import {Ultis} from '../../../../../../../utils/Utils';
import {paymentData} from '../../../../../../pages/CheckoutPage/hooks/useCheckout';
import {showSnackbar} from '../../../../../../../component/export-component';

interface OrderInfoProps {
  order: any;
  currentStatus: number;
  CancelReason?: string;
}

const OrderInfo: React.FC<OrderInfoProps> = ({
  order,
  currentStatus,
  CancelReason,
}) => {
  return (
    <View style={styles.orderInfoContainer}>
      {currentStatus == StatusOrder.cancel && (
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon
              src="outline/user interface/bookmark-delete"
              size={16}
              color="#000000"
            />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              Lý do hủy đơn:{' '}
              {order?.CancelReason || CancelReason || 'Không có lý do'}
            </Text>
          </View>
        </View>
      )}

      {/* Mã đơn hàng */}
      <View style={styles.orderInfoRow}>
        <View style={styles.orderInfoIcon}>
          <Winicon src="fill/shopping/box" size={16} color="#000000" />
        </View>
        <View style={styles.orderInfoContent}>
          <Text style={styles.orderInfoLabel}>
            Mã đơn hàng: #{order?.Code || ''}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            Clipboard.setString(order?.Code || '');
            showSnackbar({
              message: 'Đã sao chép mã đơn hàng',
              status: ComponentStatus.SUCCSESS,
            });
          }}
          style={styles.copyButton}>
          <Winicon src="fill/files/document-copy" size={16} color="#2962FF" />
        </TouchableOpacity>
      </View>

      {/* Thời gian */}
      <View style={styles.orderInfoRow}>
        <View style={styles.orderInfoIcon}>
          <Winicon
            src="fill/user interface/clock"
            size={16}
            color="#000000"
          />
        </View>
        <View style={styles.orderInfoContent}>
          <Text style={styles.orderInfoLabel}>
            Thời gian:{' '}
            {order?.DateCreated
              ? Ultis.formatDateTime(order.DateCreated, true)
              : ''}
          </Text>
        </View>
      </View>

      {/* Phương thức thanh toán */}
      <View style={styles.orderInfoRow}>
        <View style={styles.orderInfoIcon}>
          <Winicon src="fill/business/wallet-90" size={16} color="#000000" />
        </View>
        <View style={styles.orderInfoContent}>
          <Text style={styles.orderInfoLabel}>
            PTT:{' '}
            {paymentData.find(item => item.id === order?.PaymentType)?.name}
          </Text>
        </View>
      </View>

      {/* Thông tin người nhận */}
      <View style={styles.orderInfoRow}>
        <View style={styles.orderInfoIcon}>
          <Winicon
            src="fill/shopping/shop-location"
            size={20}
            color="#000000"
          />
        </View>
        <View style={styles.orderInfoContent}>
          <Text style={styles.orderInfoLabel}>
            {order?.Address?.Name} - {order?.Address?.Mobile}
          </Text>
          <Text style={styles.orderInfoValue}>{order?.Address?.Email}</Text>
          <Text style={styles.orderInfoValue}>
            {order?.Address?.Address ?? ''}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  orderInfoContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    marginBottom: 8,
  },
  orderInfoRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  orderInfoIcon: {
    width: 24,
    marginRight: 12,
  },
  orderInfoContent: {
    flex: 1,
  },
  orderInfoLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  orderInfoValue: {
    ...TypoSkin.body3,
    color: '#757575',
  },
  copyButton: {
    padding: 4,
  },
});

export default OrderInfo;
