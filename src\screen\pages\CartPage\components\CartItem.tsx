import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {
  Winicon,
  ComponentStatus,
  showSnackbar,
  Checkbox,
  showDialog,
} from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import {CartItem as CartItemType} from '../../../../types/cartTypes';
import {useCartActions} from '../../../../redux/hooks/cartHook';
import ConfigAPI from '../../../../config/configApi';
import {Ultis} from '../../../../utils/Utils';
import {TypoSkin} from '../../../../assets/skin/typography';
import {RootScreen} from '../../../../router/router';

interface CartItemProps {
  item: CartItemType;
  dialogRef: React.RefObject<any>;
}

const CartItem: React.FC<CartItemProps> = ({item, dialogRef}) => {
  const navigation = useNavigation<any>();
  const cartActions = useCartActions();

  const deleteProd = (id: string) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?',
      onSubmit: async () => {
        cartActions.removeItem(id);
        showSnackbar({
          message: 'Xóa sản phẩm thành công',
          status: ComponentStatus.SUCCSESS,
        });
      },
    });
  };

  const handleQuantityDecrease = async () => {
    if (item.Quantity === 1) {
      deleteProd(item.id);
      return;
    }
    if (item.Quantity > 1) {
      await cartActions.updateItemQuantity(item.id, item.Quantity - 1);
    }
  };

  const handleQuantityIncrease = async () => {
    await cartActions.updateItemQuantity(item.id, item.Quantity + 1);
  };

  const handleToggleSelection = () => {
    cartActions.toggleItemSelection(item.id);
  };

  const handleNavigateToProduct = () => {
    navigation.push(RootScreen.DetailProductPage, {
      id: item.ProductId,
    });
  };

  const getImageUri = () => {
    return item.Img?.startsWith('http')
      ? item.Img
      : `${ConfigAPI.imgUrlId}${item.Img}`;
  };

  const renderPrice = () => {
    if (item.Discount && item.Discount > 0) {
      return (
        <View style={styles.priceContainer}>
          <Text style={styles.originalPrice}>
            {Ultis.money(item.Price ?? 0)} đ
          </Text>
          <Text style={styles.currentPrice}>
            {Ultis.money(item.Price - (item.Price * item.Discount) / 100)} đ
          </Text>
        </View>
      );
    }
    return (
      <Text style={styles.currentPrice}>{Ultis.money(item.Price ?? 0)} đ</Text>
    );
  };

  return (
    <View style={styles.cartItemContainer}>
      <View style={styles.checkboxContainer}>
        <Checkbox value={item.selected} onChange={handleToggleSelection} />
      </View>

      <View style={styles.itemImageContainer}>
        <FastImage
          style={styles.itemImage}
          source={{
            uri: getImageUri(),
            priority: FastImage.priority.normal,
          }}
          resizeMode={FastImage.resizeMode.cover}
        />
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteProd(item.id)}>
          <Winicon src="outline/layout/trash" size={20} color="red" />
        </TouchableOpacity>
      </View>

      <View style={styles.itemDetails}>
        <TouchableOpacity
          style={styles.productInfo}
          onPress={handleNavigateToProduct}>
          <Text style={styles.itemDescription} numberOfLines={2}>
            {item.Name}
          </Text>
          {renderPrice()}
        </TouchableOpacity>

        <View style={styles.quantityContainer}>
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={handleQuantityDecrease}>
            <Winicon src="outline/layout/minus" size={16} color="#2962FF" />
          </TouchableOpacity>

          <Text style={styles.quantityText}>{item.Quantity}</Text>

          <TouchableOpacity
            style={styles.quantityButton}
            onPress={handleQuantityIncrease}>
            <Winicon src="outline/layout/plus" size={16} color="#2962FF" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cartItemContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  checkboxContainer: {
    marginRight: 12,
    alignSelf: 'center',
  },
  itemImageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  itemImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#fff',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  deleteButton: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: '#fff',
    width: 32,
    height: 32,
    borderRadius: 12,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'space-between',
  },
  productInfo: {
    flex: 1,
  },
  itemDescription: {
    ...TypoSkin.title3,
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  currentPrice: {
    ...TypoSkin.title3,
    color: '#FF3B30',
    marginRight: 8,
    fontWeight: '700',
  },
  originalPrice: {
    ...TypoSkin.title5,
    color: '#000',
    textDecorationLine: 'line-through',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  quantityButton: {
    width: 23,
    height: 23,
    borderRadius: 14,
    borderColor: '#1C33FF',
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    ...TypoSkin.body2,
    marginHorizontal: 12,
    minWidth: 20,
    textAlign: 'center',
  },
});

export default CartItem;
