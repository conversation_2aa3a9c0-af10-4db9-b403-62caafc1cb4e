import React from 'react';
import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
} from '../../../../../module/service/components/da';
import EmptyPage from '../../../../../../project-component/empty-page';
import QuoteTable from '../../../../../module/workplace/components/form/QuoteTable';
import {DataController} from '../../../../../base-controller';
import {randomGID} from '../../../../../../utils/Utils';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface ConsultantTabContentProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  methods: any;
  guest: any;
  onChangeStatus: (status: any, setServicesValue?: any) => void;
}

export default function ConsultantTabContent({
  workData,
  serviceData,
  setServiceData,
  onRefresh,
  isRefreshing,
  methods,
  guest,
  onChangeStatus,
}: ConsultantTabContentProps) {
  const handleSubmit = async () => {
    if (!serviceData || !workData) return;

    if (serviceData.Status < ToiletServiceStatus.contract)
      await onChangeStatus(ToiletStatus.contract);

    const taskController = new DataController('Task');

    // Complete consultant tasks
    taskController
      .getListSimple({
        page: 1,
        size: 1,
        query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${TaskType.consultant} ${TaskType.consultant}] @Status:[${TaskStatus.open} ${TaskStatus.overdue}]`,
      })
      .then(res => {
        if (res.code === 200 && res.data.length) {
          taskController.edit(
            res.data.map((e: any) => ({
              ...e,
              Status: TaskStatus.done,
            })),
          );
        }
      })
      .catch(console.error);

    // Create new contract task
    const now = new Date();
    const newTaskContract = {
      Id: randomGID(),
      Name: 'Làm hợp đồng',
      DateCreated: now.getTime(),
      Type: TaskType.contract,
      Status: TaskStatus.open,
      DateStart: now.getTime(),
      Day: 3,
      DateEnd: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 3,
        23,
        59,
      )?.getTime(),
      CustomerId: serviceData.CustomerId,
      ToiletServicesId: serviceData.Id,
      ToiletId: workData[0]?.Id,
    };
    taskController.add([newTaskContract]).catch(console.error);
  };

  if (
    serviceData?.Status != null &&
    serviceData.Status < ToiletServiceStatus.consultant
  ) {
    return <EmptyPage title="Đơn hàng đang trong quá trình khảo sát" />;
  }

  return (
    <QuoteTable
      toilet={workData}
      setToilet={setServiceData}
      toiletServices={serviceData}
      setToiletServices={setServiceData}
      onRefreshing={onRefresh}
      isRefreshing={isRefreshing}
      customer={guest}
      methods={methods}
      onSubmit={handleSubmit}
    />
  );
}
