import { useCallback } from 'react';

interface UseTicketRefreshProps {
  setRefreshing: (refreshing: boolean) => void;
  fetchTickets: () => Promise<void>;
}

export const useTicketRefresh = ({ setRefreshing, fetchTickets }: UseTicketRefreshProps) => {
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTickets().then(() => {
      setRefreshing(false);
    });
  }, [setRefreshing, fetchTickets]);

  return {
    handleRefresh,
  };
};
