import {Pressable, Text, View} from 'react-native';
import {Winicon} from '../component/export-component';
import {ColorThemes} from '../assets/skin/colors';
import {SkeletonImage} from './skeleton-img';

export default function EmptyPage(props: any) {
  return (
    <Pressable
      style={{
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}>
      {props.hideImg ? null : (
        <SkeletonImage
          source={{
            uri: 'https://redis.ktxgroup.com.vn/api/file/img/ce5cc92f4b67415bb2622cf40d0693e8',
          }}
          style={{height: 200, width: 200}}
        />
      )}
      {props.title ? (
        <Text
          style={{
            color: '#161C24',
            marginTop: props.hideImg ? 0 : 20,
            fontSize: 16,
            fontWeight: 'bold',
            textAlign: 'center',
          }}>
          {props.title ?? 'Không có dữ liệu'}
        </Text>
      ) : null}
      {props.subtitle ? (
        <Text
          style={{
            color: ColorThemes.light.neutral_text_subtitle_color,
            marginTop: props.hideImg ? 0 : 20,
            fontSize: 14,
            textAlign: 'center',
          }}>
          {props.subtitle}
        </Text>
      ) : null}
      {props.button ? props.subtitle : null}
    </Pressable>
  );
}
