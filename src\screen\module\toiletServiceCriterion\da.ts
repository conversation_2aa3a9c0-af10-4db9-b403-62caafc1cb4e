import {DataController} from '../../base-controller';

class ToiletServiceCriterionDA {
  toiletServiceCriterionController;

  constructor() {
    this.toiletServiceCriterionController = new DataController(
      'ToiletServiceCriterion',
    );
  }

  async create(data: any) {
    const res = await this.toiletServiceCriterionController.add([data]);
    if (res.code !== 200) {
      throw new Error(res.message);
    }
    return res.data;
  }

  async update(data: any) {
    const res = await this.toiletServiceCriterionController.edit([data]);
    if (res.code !== 200) {
      throw new Error(res.message);
    }
    return res.data;
  }
}

export default ToiletServiceCriterionDA;
