import { View, TouchableOpacity, Text, Dimensions, ScrollView } from "react-native";
import { ColorThemes } from "../../../../assets/skin/colors";
import { TypoSkin } from "../../../../assets/skin/typography";
import { showSnackbar, Winicon } from "../../../../component/export-component";
import ListTile from "../../../../component/list-tile/list-tile";
import ScreenHeader from "../../../layout/header";
import { useNavigation } from "@react-navigation/native";
import { showPopup, closePopup } from "../../../../component/popup/popup";
import { useMemo, useState } from "react";
import { useSelectorCustomerState } from "../../../../redux/hooks/hooks";
import { useDispatch } from "react-redux";
import { ToiletActions } from "../../../../redux/reducers/toilet/reducer";
import { DataController } from "../../../base-controller";
import { ComponentStatus } from "../../../../component/component-status";

interface HeaderDetailProjectProps {
    title: string
    tab?: number
    setTab?: (tab: number) => void
    popupRef?: any
    workData: any,
    setWorkData: any,
    serviceData: any,
    setServiceData: any,
}

export default function HeaderDetailProject(props: HeaderDetailProjectProps) {
    const { title = '-', popupRef, setTab, tab, workData, setWorkData, serviceData, setServiceData } = props

    const navigation = useNavigation<any>();
    const user = useSelectorCustomerState().data;
    const dispatch = useDispatch<any>()
    const favor = useMemo(() => {
        if (workData?.CustomerId === user?.Id || user?.Id === serviceData?.CustomerId) if (workData?.Favor || serviceData?.Favor) return true
        return false
    }, [workData, serviceData])

    const viewList = [
        { id: 0, title: "Tổng quan", key: "overview", icon: "outline/user interface/home" },
        { id: 1, title: "Báo cáo", key: "dasdboard", icon: "outline/files/folder-chart-bar" },
        { id: 2, title: "Công việc", key: "task", icon: "outline/development/bullet-list-67" },
        { id: 3, title: "Lịch", key: "calendar", icon: "outline/user interface/calendar-date-2" },
        { id: 4, title: "Thiết bị", key: "devices", icon: "outline/user interface/bolt" },
        { id: 5, title: "Chế phẩm sinh học", key: "bios", icon: "outline/education/chemistry" },
        { id: 6, title: "Tệp", key: "file", icon: "outline/text/attach" },
        { id: 7, title: "Tài liệu", key: "contract", icon: "outline/files/document-copy" },
        { id: 8, title: "Đánh giá", key: "comment", icon: "outline/user interface/b-comment" },
    ]

    // useEffect(() => {
    //     if (data?.Status >= ToiletStatus.contract) {
    //         if (viewList.find((item: any) => item.key === "contract")) return
    //         setList([...viewList, { id: 6, title: "Hợp đồng", key: "contract", icon: "outline/files/document-copy" },].sort((a: any, b: any) => a.id - b.id))
    //     } else {
    //         setList((e: any) => [...e.filter((item: any) => item.key !== "contract")])
    //     }
    // }, [data])

    const switchFavor = async () => {
        if (workData?.CustomerId === user?.Id) {
            ToiletActions.edit(dispatch, [{ ...workData, Favor: !workData.Favor }])
            setWorkData({ ...workData, Favor: !workData.Favor })
            showSnackbar({ message: "Thao tác thành công", status: ComponentStatus.SUCCSESS })
        }
        if (user?.Id === serviceData?.CustomerId) {
            const controller = new DataController("ToiletServices")
            const newItem = { ...serviceData, Favor: !serviceData.Favor }
            const res = await controller.edit([newItem])
            if (res.code === 200) {
                setServiceData(newItem)
                showSnackbar({ message: "Thao tác thành công", status: ComponentStatus.SUCCSESS })
            }
        }
    }

    return <ScreenHeader
        style={{
            shadowColor: "rgba(0, 0, 0, 0.03)",
            shadowOffset: {
                width: 0,
                height: 4
            },
            shadowRadius: 20,
            elevation: 20,
            shadowOpacity: 1,
        }}
        children={
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1, width: "100%" }}>
                    <TouchableOpacity
                        style={{ padding: 12, gap: 8, flexDirection: 'row', alignItems: 'center', }}
                        onPress={() => {
                            navigation.goBack();
                        }}>
                        <Winicon
                            src="outline/arrows/left-arrow"
                            color={ColorThemes.light.neutral_text_subtitle_color}
                            size={20}
                        />
                        <Text
                            style={[
                                TypoSkin.heading8,
                                { color: ColorThemes.light.neutral_text_title_color, flex: 1 },
                            ]}>
                            {title}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 20,
                        paddingRight: 16,

                    }}>
                    {(user?.Id === serviceData?.CustomerId || workData?.CustomerId === user?.Id) ? <Winicon src={favor ? "fill/user interface/fav-list" : "outline/user interface/fav-list"} onClick={switchFavor} size={18} color={favor ? ColorThemes.light.secondary1_main_color : ColorThemes.light.neutral_text_subtitle_color} /> : null}
                    {/* {[0, 1, 2, 3].map((item, index) => {
                        return (
                            <View
                                key={`user - ${index}`}
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                <View
                                    style={{
                                        position: 'absolute',
                                        right: 0,
                                        backgroundColor:
                                            ColorThemes.light.neutral_disable_background_color,
                                        height: 24,
                                        width: 24,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        borderRadius: 100,
                                    }}>
                                    <Winicon
                                        src="fill/user interface/star"
                                        color={ColorThemes.light.neutral_text_subtitle_color}
                                        size={20}
                                    />
                                </View>
                                {index === 3 && (
                                    <View
                                        style={{
                                            position: 'absolute',
                                            right: 0,
                                            backgroundColor:
                                                ColorThemes.light.neutral_disable_background_color,
                                            height: 24,
                                            width: 24,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderRadius: 100,
                                        }}>
                                        <Winicon
                                            src="fill/user interface/menu-dots"
                                            color={ColorThemes.light.neutral_text_subtitle_color}
                                            size={12}
                                        />
                                    </View>
                                )}
                            </View>
                        );
                    })} */}
                </View>
            </View>
        }
        bottom={
            <ListTile
                style={{
                    padding: 0,
                    height: 47,
                    paddingHorizontal: 16,
                    gap: 8,
                    alignItems: 'center',
                }}
                onPress={() => {
                    showPopup({
                        ref: popupRef,
                        enableDismiss: true,
                        children: <View style={{ height: Dimensions.get('window').height - 100, backgroundColor: ColorThemes.light.neutral_absolute_background_color, borderTopLeftRadius: 12, borderTopRightRadius: 12, }}>
                            <ScreenHeader
                                style={{
                                    backgroundColor: ColorThemes.light.transparent,
                                    flexDirection: 'row',
                                    paddingVertical: 4
                                }}
                                title={`Chọn màn hình`}
                                prefix={<View />}
                                action={<TouchableOpacity onPress={() => closePopup(popupRef)} style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                                    <Winicon src="outline/layout/xmark" size={20} color={ColorThemes.light.neutral_text_body_color} />
                                </TouchableOpacity>}
                            />
                            <ScrollView style={{}} >
                                {viewList.map((item, index) => {
                                    return <ListTile leading={<Winicon
                                        src={item.icon}
                                        color={ColorThemes.light.neutral_text_subtitle_color}
                                        size={16}
                                    />} style={{ borderBottomColor: ColorThemes.light.neutral_main_background_color, borderBottomWidth: 1 }} key={`${item.id}`} onPress={() => {
                                        if (setTab) setTab(item.id)
                                        closePopup(popupRef)
                                    }} title={item.title} trailing={(item.id === tab) ?
                                        <Winicon
                                            src={'fill/layout/circle-check'}
                                            size={20}
                                            color={ColorThemes.light.primary_main_color}
                                        /> : <View />} />
                                })}
                            </ScrollView>
                        </View>
                    })
                }}
                title={viewList.find((e: any) => e.id === tab)?.title ?? ''}
                titleStyle={[
                    TypoSkin.heading6,
                    { color: ColorThemes.light.neutral_text_title_color },
                ]}
                leading={
                    <View
                        style={{
                            backgroundColor:
                                ColorThemes.light.neutral_disable_background_color,
                            height: 24,
                            width: 24,
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 100,
                        }}>
                        <Winicon
                            src={viewList.find((e: any) => e.id === tab)?.icon ?? "outline/layout/grid-list"}
                            color={ColorThemes.light.neutral_text_subtitle_color}
                            size={16}
                        />
                    </View>
                }
                trailing={
                    <Winicon
                        src="fill/user interface/menu-8"
                        color={ColorThemes.light.neutral_text_subtitle_color}
                        size={16}
                    />
                }
            />
        }
    />
}