import React, {useEffect} from 'react';
import {FieldValues, UseFormReturn} from 'react-hook-form';
import {View, Text, TouchableOpacity} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import {RegisterPartnerFormStyles} from '../styles/RegisterPartnerFormStyles';
import {TypoSkin} from 'assets/skin/typography';
import iconSvg from 'svgs/iconSvg';
import {validatePhoneNumber} from 'utils/validate';
import {
  FAddressPickerForm,
  TextFieldForm,
} from 'project-component/component-form';
import {PartnerDa} from '../../partnerDa';
import {store} from 'redux/store/store';
import {CustomerRole, CustomerType} from 'redux/reducers/user/da';
import {useSelectorCustomerState} from 'redux/hooks/hooks';

const GeneralInformationForm = ({
  methods,
  getDataCompany,
}: {
  methods: UseFormReturn<FieldValues, any, FieldValues>;
  getDataCompany: any;
}) => {
  const cusInfo = store.getState().customer.data;
  const [companyData, setCompanyData] = React.useState<any[]>([]);
  const partnerDa = new PartnerDa();
  const userRole = useSelectorCustomerState().role;

  useEffect(() => {
    if (getDataCompany) {
      methods.setValue('Name', getDataCompany.Name);
      methods.setValue('ShortName', getDataCompany.ShortName);
      methods.setValue('TaxCode', getDataCompany.TaxCode);
      methods.setValue(
        'Mobile',
        getDataCompany.Phone ? getDataCompany.Phone : getDataCompany.Mobile,
      );
      methods.setValue('CompanyMail', getDataCompany.Email);
      methods.setValue('Address', getDataCompany.Address);
      methods.setValue('Lat', getDataCompany.Lat);
      methods.setValue('Long', getDataCompany.Long);
      methods.setValue('Industry', getDataCompany.Industry);
      methods.setValue('NoteShort', getDataCompany.Note);
    }
    if (!getDataCompany) {
      methods.setValue('Name', cusInfo?.Name);
      methods.setValue('Mobile', cusInfo?.Mobile);
      methods.setValue('Address', cusInfo?.Address);
    }
  }, [getDataCompany]);

  const getAllCompany = async () => {
    const respone = await partnerDa.getAllCompanyInfo();
    if (respone.code === 200) {
      setCompanyData(respone.data);
    }
  };

  useEffect(() => {
    getAllCompany();
  }, []);

  useEffect(() => {}, []);
  const prefix = (icon: string) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          height: 32,
          width: 32,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <AppSvg SvgSrc={icon} size={18} />
      </View>
    );
  };

  return (
    <TouchableOpacity>
      <View style={{gap: 20, marginBottom: 10}}>
        <Text
          style={{
            color: ColorThemes.light.neutral_text_title_color,
            ...TypoSkin.title2,
            fontWeight: 'bold',
          }}>
          Thông tin chung
        </Text>
        <TextFieldForm
          control={methods.control}
          name="Name"
          placeholder="Tên doanh nghiệp/Hộ kinh doanh/Đối tác *"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formNameGroup)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
        <TextFieldForm
          control={methods.control}
          name="ShortName"
          placeholder="Tên rút gọn"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formNameGroup)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
        <TextFieldForm
          control={methods.control}
          name="TaxCode"
          placeholder="Mã số thuế"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formTaxCode)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
        <TextFieldForm
          control={methods.control}
          name="Mobile"
          placeholder="Số điện thoại *"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          type="number-pad"
          prefix={prefix(iconSvg.formGroupPhone)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
          onBlur={async (ev: string) => {
            if (ev === undefined || ev.length == 0) {
              methods.setError('Mobile', {
                message: 'Số điện thoại không hợp lệ',
              });
              return;
            }
            var mobile = ev.trim();
            // Check if the number doesn't already start with 0 or +84
            if (!/^(\+84|0)/.test(mobile)) {
              mobile = '0' + mobile; // Add 0 at the beginning
            }
            const val = validatePhoneNumber(mobile);
            if (val) methods.clearErrors('Mobile');
            else
              methods.setError('Mobile', {
                message: 'Số điện thoại không hợp lệ',
              });
          }}
        />
        <TextFieldForm
          control={methods.control}
          name="CompanyMail"
          placeholder="Company Mail"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formEmail)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
          onBlur={async (ev: string) => {
            if (ev === undefined || ev.length == 0) {
              methods.setError('CompanyMail', {
                message: 'Email không hợp lệ',
              });
              return;
            }
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            var email = ev.trim();
            let checkMail = emailRegex.test(email);
            if (checkMail) methods.clearErrors('CompanyMail');
            else
              methods.setError('CompanyMail', {
                message: 'Email không hợp lệ',
              });
          }}
          onSubmit={(ev: string) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            var email = ev.trim();
            let checkMail = emailRegex.test(email);
            if (checkMail) methods.clearErrors('CompanyMail');
            else
              methods.setError('CompanyMail', {
                message: 'Email không hợp lệ',
              });
          }}
        />

        <FAddressPickerForm
          control={methods.control}
          errors={methods.formState.errors}
          name="Address"
          placeholder="Nhập địa chỉ"
          placeName={''}
          style={{
            ...RegisterPartnerFormStyles.textFieldStyleInputShopInfo,
            paddingHorizontal: 0,
          }}
          onChange={value => {
            console.log('==============AddressAddress======================');
            console.log(value.formatted_address);
            console.log('====================================');
            methods.setValue('Long', value.geometry.location.lng);
            methods.setValue('Lat', value.geometry.location.lat);
            methods.setValue('Address', value.formatted_address);
            return value.formatted_address;
          }}
          placeIcon={prefix(iconSvg.formAddress)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />

        <TextFieldForm
          control={methods.control}
          name="Industry"
          placeholder="Lĩnh vực kinh doanh *"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formBusinessAreas)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />

        <TextFieldForm
          control={methods.control}
          name="NoteShort"
          placeholder="Giới thiệu ngắn"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          prefix={prefix(iconSvg.formDescription)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
      </View>
    </TouchableOpacity>
  );
};
export default GeneralInformationForm;
