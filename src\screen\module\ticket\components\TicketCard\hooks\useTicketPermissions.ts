import {useMemo} from 'react';
import {useSelectorCustomerCompanyState} from '../../../../../../redux/hooks/hooks';
import {TicketStatus} from '../../../../service/components/da';
import ConfigAPI from '../../../../../../config/configApi';

export interface TicketPermissionsHookReturn {
  checkEdit: boolean;
  listStatus: Array<any>;
}

export const useTicketPermissions = (
  item: any,
): TicketPermissionsHookReturn => {
  const company = useSelectorCustomerCompanyState().data;

  const checkEdit = useMemo(() => {
    if (!item || !company) return false;

    return (
      (company?.Id === ConfigAPI.ktxCompanyId &&
        item.Status !== TicketStatus.done &&
        item.Status !== TicketStatus.end) ||
      (company?.Id !== ConfigAPI.ktxCompanyId &&
        (item.Status === TicketStatus.init ||
          item.Status === TicketStatus.processing))
    );
  }, [item, company]);

  const listStatus = useMemo(() => {
    if (!company) return [];

    // Import StatusTicketData from the main component file
    const StatusTicketData = [
      {
        id: TicketStatus.init,
        name: 'Đang mở',
      },
      {
        id: TicketStatus.processing,
        name: 'Đang xử lý',
      },
      {
        id: TicketStatus.done,
        name: 'Hoàn thành',
      },
      {
        id: TicketStatus.cancel,
        name: 'Hủy',
      },
      {
        id: TicketStatus.end,
        name: 'Kết thúc',
      },
    ];

    return company?.Id !== ConfigAPI.ktxCompanyId
      ? StatusTicketData.slice(0, StatusTicketData.length - 1)
      : StatusTicketData;
  }, [company]);

  return {
    checkEdit,
    listStatus,
  };
};
