import {DataController} from '../../base-controller';
import {randomGID} from '../../../utils/Utils';
import {ToiletItem} from '../../../types/toiletType';
import {getImage} from '../../../redux/actions/rootAction';

export class ToiletDa {
  private toiletController: DataController;
  private tasketController: DataController;
  private ticketController: DataController;
  private customerController: DataController;
  private toiletServicesController = new DataController('ToiletServices');

  constructor() {
    this.toiletController = new DataController('Toilet');
    this.tasketController = new DataController('Task');
    this.ticketController = new DataController('Ticket');
    this.customerController = new DataController('Customer');
    this.toiletServicesController = new DataController('ToiletServices');
  }

  /**
   * Get all toilets with pagination
   * @param options - Pagination and filter options
   * @returns Promise<{code: number, data?: any[], message?: string}>
   */
  async getAll(options?: {
    page?: number;
    size?: number;
    query?: string;
    sortby?: {BY: string; DIRECTION?: 'ASC' | 'DESC'};
  }): Promise<{code: number; data?: any[]; message?: string}> {
    try {
      const config = {
        page: options?.page || 1,
        size: options?.size || 10,
        query: options?.query || '*',
        sortby: options?.sortby || {
          BY: 'DateCreated',
          DIRECTION: 'DESC' as const,
        },
      };

      const response = await this.toiletController.getListSimple(config);

      if (response.code === 200) {
        return {
          code: 200,
          data: response.data,
        };
      } else {
        return {
          code: response.code || 500,
          message:
            response.message || 'Có lỗi xảy ra khi lấy danh sách nhà vệ sinh',
        };
      }
    } catch (error) {
      console.error('Error getting all toilets:', error);
      return {
        code: 500,
        message: 'Có lỗi xảy ra khi lấy danh sách nhà vệ sinh',
      };
    }
  }

  /**
   * Get toilet by ID
   * @param toiletId - The toilet ID
   * @returns Promise<{code: number, data?: any, message?: string}>
   */
  async getById(
    toiletId: string,
  ): Promise<{code: number; data?: any; message?: string}> {
    try {
      if (!toiletId) {
        return {
          code: 400,
          message: 'Toilet ID là bắt buộc',
        };
      }

      const response = await this.toiletController.getById(toiletId);

      if (response.code === 200) {
        return {
          code: 200,
          data: response.data,
        };
      } else {
        return {
          code: response.code || 404,
          message: response.message || 'Không tìm thấy nhà vệ sinh',
        };
      }
    } catch (error) {
      console.error('Error getting toilet by ID:', error);
      return {
        code: 500,
        message: 'Có lỗi xảy ra khi lấy thông tin nhà vệ sinh',
      };
    }
  }
  /**
   * Create a new toilet
   * @param toiletData - Toilet data to create
   * @returns Promise<{code: number, data?: any, message?: string}>
   */
  async create(
    toiletData: Partial<ToiletItem>,
  ): Promise<{code: number; data?: any; message?: string}> {
    try {
      // Validate required fields
      if (!toiletData.Name) {
        return {
          code: 400,
          message: 'Tên nhà vệ sinh là bắt buộc',
        };
      }

      if (!toiletData.CustomerId) {
        return {
          code: 400,
          message: 'CustomerId là bắt buộc',
        };
      }

      // Prepare toilet data with default values
      const newToilet: Partial<ToiletItem> = {
        Id: toiletData.Id || randomGID(),
        Name: toiletData.Name,
        CustomerId: toiletData.CustomerId,
        DateCreated: toiletData.DateCreated || Date.now(),
        Status: toiletData.Status || 1, // Default status
        Certificate: toiletData.Certificate || 1, // Default certificate
        Description: toiletData.Description || '',
        Address: toiletData.Address || '',
        Lat: toiletData.Lat || 0,
        Long: toiletData.Long || 0,
        Mobile: toiletData.Mobile || '',
        ...toiletData, // Allow override of any additional fields
      };

      // Call the DataController add method
      const response = await this.toiletController.add([newToilet]);

      if (response.code === 200) {
        return {
          code: 200,
          data: newToilet,
          message: 'Tạo nhà vệ sinh thành công',
        };
      } else {
        return {
          code: response.code || 500,
          message: response.message || 'Có lỗi xảy ra khi tạo nhà vệ sinh',
        };
      }
    } catch (error) {
      console.error('Error creating toilet:', error);
      return {
        code: 500,
        message: 'Có lỗi xảy ra khi tạo nhà vệ sinh',
      };
    }
  }

  /**
   * Update an existing toilet
   * @param toiletData - Toilet data to update (must include Id)
   * @returns Promise<{code: number, data?: any, message?: string}>
   */
  async update(
    toiletData: Partial<ToiletItem> & {Id: string},
  ): Promise<{code: number; data?: any; message?: string}> {
    try {
      // Validate required fields
      if (!toiletData.Id) {
        return {
          code: 400,
          message: 'Id là bắt buộc để cập nhật',
        };
      }

      // Get existing toilet to verify it exists
      const existingToilet = await this.toiletController.getById(toiletData.Id);
      if (existingToilet.code !== 200) {
        return {
          code: 404,
          message: 'Không tìm thấy nhà vệ sinh',
        };
      }

      // Prepare updated toilet data
      const updatedToilet = {
        ...existingToilet.data,
        ...toiletData,
        // Ensure Id is preserved
        Id: toiletData.Id,
      };

      // Call the DataController edit method
      const response = await this.toiletController.edit([updatedToilet]);

      if (response.code === 200) {
        return {
          code: 200,
          data: updatedToilet,
          message: 'Cập nhật nhà vệ sinh thành công',
        };
      } else {
        return {
          code: response.code || 500,
          message: response.message || 'Có lỗi xảy ra khi cập nhật nhà vệ sinh',
        };
      }
    } catch (error) {
      console.error('Error updating toilet:', error);
      return {
        code: 500,
        message: 'Có lỗi xảy ra khi cập nhật nhà vệ sinh',
      };
    }
  }

  /**
   * Delete toilet by ID
   * @param toiletId - The toilet ID to delete
   * @returns Promise<{code: number, message?: string}>
   */
  async delete(toiletId: string): Promise<{code: number; message?: string}> {
    try {
      if (!toiletId) {
        return {
          code: 400,
          message: 'Toilet ID là bắt buộc',
        };
      }

      // Check if toilet exists first
      const existingToilet = await this.toiletController.getById(toiletId);
      if (existingToilet.code !== 200) {
        return {
          code: 404,
          message: 'Không tìm thấy nhà vệ sinh',
        };
      }

      const response = await this.toiletController.delete([toiletId]);

      if (response.code === 200) {
        return {
          code: 200,
          message: 'Xóa nhà vệ sinh thành công',
        };
      } else {
        return {
          code: response.code || 500,
          message: response.message || 'Có lỗi xảy ra khi xóa nhà vệ sinh',
        };
      }
    } catch (error) {
      console.error('Error deleting toilet:', error);
      return {
        code: 500,
        message: 'Có lỗi xảy ra khi xóa nhà vệ sinh',
      };
    }
  }

  async fetchTaskToday(toiletId: string) {
    const config = {
      query: `@ToiletId:{${toiletId}} `,
      page: 1,
      size: 1000,
    };
    const response = await this.tasketController.getListSimple(config);
    if (response.code === 200) {
      let filter = response.data.filter((e: any) => {
        const taskDate = new Date(e.DateStart);
        const taskDateString = `${taskDate.getDate().toString().padStart(2, '0')}/${(taskDate.getMonth() + 1).toString().padStart(2, '0')}/${taskDate.getFullYear()}`;
        const today = new Date();
        const todayString = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
        return taskDateString === todayString;
      });
      return filter;
    }
    return response;
  }
  async fetchTicket(toiletId: string) {
    const config = {
      page: 1,
      size: 10000,
      query: `@ToiletId:{${toiletId}} `,
      pattern: {
        CustomerId: ['Id', 'Name', 'Img'],
        ToiletId: ['Id', 'Name'],
        ToiletServicesId: ['Id', 'Name'],
      },
    };
    const response = await this.ticketController.getPatternList(config);
    if (response.code === 200) {
      const dataMap = response.data.map((item: any) => {
        item.Customer = response.Customer?.find?.(
          (cus: any) => cus.Id === item.CustomerId,
        );
        item.Toilet = response.Toilet?.find?.(
          (toilet: any) => toilet.Id === item.ToiletId,
        );
        item.ToiletServices = response.ToiletServices?.find?.(
          (toiletServices: any) => toiletServices.Id === item.ToiletServicesId,
        );
        return item;
      });
      let data = await getImage({items: dataMap});
      return data;
    }
  }

  async fetchALlTicketrByCustomer(customerId: string) {
    const res = await this.toiletController.getListSimple({
      page: 1,
      size: 10000,
      query: `@CustomerId:{${customerId}}`,
    });
    if (res.code === 200) {
      const getToiletID = res.data.map((e: any) => e.Id);
      if (getToiletID.length > 0) {
        const response = await this.ticketController.getListSimple({
          page: 1,
          size: 10000,
          query: `@ToiletId:{${getToiletID.join(' | ')}} `,
        });
        if (response.code === 200) {
          return response;
        }
      }
      return res;
    }
  }
  async fetchAllTaskByToiletId(toiletId: string) {
    try {
      const config = {
        query: `@ToiletId:{${toiletId}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'Mobile'],
        },
      };

      const response = await this.tasketController.getPatternList(config);
      if (response && response.code === 200) {
        let dataMap = response.data.map((item: any) => {
          item.Customer = response.Customer?.find?.(
            (cus: any) => cus.Id === item.CustomerId,
          );
          return item;
        });
        return dataMap;
      }
      return [];
    } catch (error) {
      return [];
    }
  }

  async fetchByCustomer(customerId: string) {
    const config = {
      page: 1,
      size: 10000,
      query: `@CustomerId:{${customerId}}`,
    };
    const response = await this.toiletController.getListSimple(config);
    if (response.code === 200) {
      return response.data;
    }
    return [];
  }
  async fetchByCustomerAndCheckRegister(
    customerId: string,
    levelCateCriterion: number,
  ) {
    try {
      const config = {
        page: 1,
        size: 1000000,
        query: `@CustomerId:{${customerId}}`,
      };
      const response = await this.toiletController.getListSimple(config);

      if (response.code === 200 && response.data?.length > 0) {
        // For each toilet, check if it has any ToiletServices with CateCriterion
        const toiletsWithRegistrationStatus = await Promise.all(
          response.data.map(async (toilet: any) => {
            try {
              // Query ToiletServices for this toilet with CateCriterion pattern to get Sort field
              const servicesResponse =
                await this.toiletServicesController.getPatternList({
                  query: `@ToiletId:{${toilet.Id}}`,
                  pattern: {
                    CateCriterionId: [
                      'Id',
                      'Name',
                      'Description',
                      'Sort',
                      'TypeCriterion',
                      'DateCreated',
                      'Content',
                    ],
                  },
                });

              let IsRegistered = false;

              if (
                servicesResponse.code === 200 &&
                servicesResponse.data?.length > 0
              ) {
                // Map CateCriterion to ToiletServices
                const servicesWithCriterion = servicesResponse.data.map(
                  (service: any) => ({
                    ...service,
                    CateCriterion: servicesResponse.CateCriterion?.find(
                      (cate: any) => cate.Id === service.CateCriterionId,
                    ),
                  }),
                );

                // Check registration logic based on CateCriterion Sort vs levelCateCriterion
                const hasMatchingLevel = servicesWithCriterion.some(
                  (service: any) => {
                    const cateCriterionSort = service.CateCriterion?.Sort;
                    return (
                      service.Status >= 1 && // Service is not rejected
                      cateCriterionSort === levelCateCriterion
                    );
                  },
                );

                // IsRegistered = true only if toilet has service with exactly matching level
                IsRegistered = hasMatchingLevel;
              }

              return {
                ...toilet,
                IsRegistered,
              };
            } catch (error) {
              console.error(
                `Error checking registration for toilet ${toilet.Id}:`,
                error,
              );
              // If error occurs, default to not registered
              return {
                ...toilet,
                IsRegistered: false,
              };
            }
          }),
        );

        return toiletsWithRegistrationStatus;
      }

      return response.data || [];
    } catch (error) {
      console.error('Error in fetchByCustomerAndCheckRegister:', error);
      return [];
    }
  }
}
