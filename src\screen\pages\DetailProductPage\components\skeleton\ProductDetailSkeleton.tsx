import React from 'react';
import {<PERSON>, ScrollView, StyleSheet} from 'react-native';
import SkeletonBox from './SkeletonBox';
import {commonStyles} from '../../styles';

const ProductDetailSkeleton: React.FC = () => (
  <View style={commonStyles.container}>
    {/* Header Skeleton */}
    <View style={styles.header}>
      <SkeletonBox width={40} height={40} style={commonStyles.skeletonCircle} />
      <View style={styles.headerRight}>
        <SkeletonBox
          width={40}
          height={40}
          style={commonStyles.skeletonCircle}
        />
        <SkeletonBox
          width={40}
          height={40}
          style={commonStyles.skeletonCircle}
        />
      </View>
    </View>

    <ScrollView style={commonStyles.scrollView}>
      {/* Image Skeleton */}
      <SkeletonBox
        width="100%"
        height={350}
        style={commonStyles.skeletonImage}
      />

      {/* Price Skeleton */}
      <View style={styles.priceContainer}>
        <SkeletonBox width={120} height={24} />
        <View style={styles.actionButtons}>
          <SkeletonBox width={24} height={24} />
          <SkeletonBox width={24} height={24} />
        </View>
      </View>

      {/* Title Skeleton */}
      <View style={styles.skeletonTitleContainer}>
        <SkeletonBox width="100%" height={20} />
        <SkeletonBox width="70%" height={20} style={styles.skeletonTitle2} />
      </View>

      {/* Details Skeleton */}
      <View style={styles.detailsContainer}>
        <View style={styles.detailsHorizontalContainer}>
          <SkeletonBox width={60} height={16} />
          <SkeletonBox width={60} height={16} />
          <SkeletonBox width={60} height={16} />
        </View>
      </View>

      {/* Shipping Skeleton */}
      <View style={styles.shippingContainer}>
        <SkeletonBox width={20} height={20} />
        <SkeletonBox
          width={100}
          height={16}
          style={styles.skeletonShippingText}
        />
      </View>

      {/* Rating Skeleton */}
      <View style={styles.ratingContainer}>
        <SkeletonBox width={100} height={20} />
        <SkeletonBox width={60} height={16} style={styles.skeletonRatingText} />
      </View>

      {/* Seller Skeleton */}
      <View style={styles.sellerContainer}>
        <SkeletonBox
          width={40}
          height={40}
          style={commonStyles.skeletonCircle}
        />
        <View style={styles.skeletonSellerInfo}>
          <SkeletonBox width={120} height={16} />
          <SkeletonBox
            width={80}
            height={12}
            style={styles.skeletonSellerSubText}
          />
        </View>
      </View>

      {/* Shop Info Skeleton */}
      <View style={styles.skeletonShopInfoContainer}>
        <View style={styles.skeletonShopInfoItem}>
          <SkeletonBox width={30} height={16} />
          <SkeletonBox
            width={50}
            height={12}
            style={styles.skeletonShopInfoSubText}
          />
        </View>
        <View style={styles.skeletonShopInfoItem}>
          <SkeletonBox width={30} height={16} />
          <SkeletonBox
            width={60}
            height={12}
            style={styles.skeletonShopInfoSubText}
          />
        </View>
        <View style={styles.skeletonShopInfoItem}>
          <SkeletonBox width={30} height={16} />
          <SkeletonBox
            width={40}
            height={12}
            style={styles.skeletonShopInfoSubText}
          />
        </View>
      </View>
    </ScrollView>

    {/* Bottom Actions Skeleton */}
    <View style={styles.bottomActions}>
      <SkeletonBox
        width="33%"
        height={64}
        style={styles.skeletonBottomAction}
      />
      <SkeletonBox
        width="33%"
        height={64}
        style={styles.skeletonBottomAction}
      />
      <SkeletonBox
        width="34%"
        height={64}
        style={styles.skeletonBottomAction}
      />
    </View>
  </View>
);

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 30,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    marginTop: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  skeletonTitleContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skeletonTitle2: {
    marginTop: 8,
  },
  detailsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailsHorizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    flex: 1,
  },
  shippingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  skeletonShippingText: {
    marginLeft: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  skeletonRatingText: {
    marginLeft: 'auto',
  },
  sellerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skeletonSellerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  skeletonSellerSubText: {
    marginTop: 4,
  },
  skeletonShopInfoContainer: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  skeletonShopInfoItem: {
    alignItems: 'center',
  },
  skeletonShopInfoSubText: {
    marginTop: 4,
  },
  bottomActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    height: 64,
  },
  skeletonBottomAction: {
    borderRadius: 0,
  },
});

export default ProductDetailSkeleton;
