import React, { useMemo } from "react";
import { regexGetVariables, Ultis } from "../../../../../utils/Utils";
import { DataController } from "../../../../base-controller";
import { ContractType, TaskType, AddendumType, ContractStatus, signTickImg } from "../../../service/components/da";

export const quoteTableModelPrint = ({ methods, cateServices }: { methods: any, cateServices: Array<any> }) => {
    const checkedDiscount = includesDicount(methods)
    const caculateVatMoney = () => {
        const vatDevices = methods.watch("devices").reduce((a: number, b: { Price: number; Quantity: number; Discount: any; Vat: any }) => a + (b.Price * b.Quantity * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        const vatBios = methods.watch("bioProducts").reduce((a: number, b: { Price: number; Quantity: number; Discount: any; Vat: any }) => a + (b.Price * b.Quantity * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        const vatMaterials = methods.watch("materials").reduce((a: number, b: { Price: number; Quantity: number; Discount: any; Vat: any }) => a + (b.Price * b.Quantity * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        const vatTasks = methods.watch("tasks").reduce((a: number, b: { Price: number; Day: number; Discount: any; Vat: any }) => a + (b.Price * b.Day * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        return vatDevices + vatBios + vatMaterials + vatTasks
    }
    const caculateDiscountMoney = () => {
        const discountDevices = methods.watch("devices").reduce((a: number, b: { Price: number; Quantity: number; Discount: any }) => a + (b.Price * b.Quantity * ((b.Discount ?? 0) / 100)), 0)
        const discountBios = methods.watch("bioProducts").reduce((a: number, b: { Price: number; Quantity: number; Discount: any }) => a + (b.Price * b.Quantity * ((b.Discount ?? 0) / 100)), 0)
        const discountMaterials = methods.watch("materials").reduce((a: number, b: { Price: number; Quantity: number; Discount: any }) => a + (b.Price * b.Quantity * ((b.Discount ?? 0) / 100)), 0)
        const discountTasks = methods.watch("tasks").reduce((a: number, b: { Price: number; Day: number; Discount: any }) => a + (b.Price * b.Day * ((b.Discount ?? 0) / 100)), 0)
        return discountDevices + discountBios + discountMaterials + discountTasks
    }

    const totalBeforeDiscountAndVat = () => {
        const totalDevices = methods.watch("devices").reduce((a: number, b: { Price: number; Quantity: number }) => a + (b.Price * b.Quantity), 0)
        const totalBios = methods.watch("bioProducts").reduce((a: number, b: { Price: number; Quantity: number }) => a + (b.Price * b.Quantity), 0)
        const totalMaterials = methods.watch("materials").reduce((a: number, b: { Price: number; Quantity: number }) => a + (b.Price * b.Quantity), 0)
        const totalTasks = methods.watch("tasks").reduce((a: number, b: { Price: number; Day: number }) => a + (b.Price * b.Day), 0)
        return totalDevices + totalBios + totalMaterials + totalTasks
    }

    return `<table class="table-quote-html" style="font-size: 12px; border-collapse: collapse; width: 100%;">
    <thead class="table-quote-row-html" style="height: 36px;">
        <tr>
            <th style="width: 48px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600;">STT</th>
            <th style="min-width: 120px; height: 100%;">
                <div class="row-comptext" style="display: flex; justify-content: center;">
                    <div style="font-size: 12px; font-weight: 600; padding: 4px; width: 100px; text-align: center">NỘI DUNG CÔNG VIỆC</div>
                </div>
            </th>
            <th style="width: 56px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600; text-align: center;">ĐVT</th>
            <th style="width: 40px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600; text-align: center;">SỐ LƯỢNG</th>
            <th style="width: 100px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600; text-align: center;">
                <div class="row-comptext" style="display: flex; justify-content: center;">
                    <div style="font-size: 12px; font-weight: 600; padding: 4px; width: 88px; text-align: center">ĐƠN GIÁ (VNĐ)</div>
                </div>
            </th>
            ${checkedDiscount ? `<th style="width: 80px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600; text-align: center;">
                <div class="row-comptext" style="display: flex; justify-content: center;">
                    <div style="font-size: 12px; font-weight: 600; padding: 4px; width: 72px; text-align: center">GIẢM GIÁ (VNĐ)</div>
                </div>
            </th>` : ""}
            <th style="width: 80px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600; text-align: center;">
                <div class="row-comptext" style="display: flex; justify-content: center;">
                    <div style="font-size: 12px; font-weight: 600; padding: 4px; width: 56px; text-align: center">VAT (VNĐ)</div>
                </div>
            </th>
            <th style="width: 100px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600; text-align: center;">
                <div class="row-comptext" style="display: flex; justify-content: center;">
                    <div style="font-size: 12px; font-weight: 600; padding: 4px; width: 100px; text-align: center">THÀNH TIỀN (VNĐ)</div>
                </div>
            </th>
        </tr>
    </thead>
    <tbody>
        <!-- Loop through cateServices dynamically in JS -->
    ${cateServices.map((e) => { return cateTile({ item: e, methods: methods }) }).join('\n')}    
        <tr class="table-quote-row-html" style="height: 36px; background-color: #9bc2e6;">
            <td style="height: 100%;" colspan="5">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;">THÀNH TIỀN TRƯỚC THUẾ</div>
            </td>
            <td style="width: 268px; height: 100%;" colspan="${checkedDiscount ? 3 : 2}">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;text-align: end;">${Ultis.money(totalBeforeDiscountAndVat())}</div>
            </td>
        </tr>
        ${checkedDiscount ? `<tr class="table-quote-row-html" style="height: 40px;background-color: #9bc2e6;">
            <td style="height: 100%;" colspan="5">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;">GIẢM GIÁ</div>
            </td>
            <td style="width: 268px; height: 100%;" colspan="${checkedDiscount ? 3 : 2}">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;text-align: end;">${Ultis.money(caculateDiscountMoney())}</div>
            </td>
        </tr>` : ""}
        <tr class="table-quote-row-html" style="height: 40px;background-color: #9bc2e6;">
            <td style="height: 100%;" colspan="5">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;">THUẾ VAT</div>
            </td>
            <td style="width: 268px; height: 100%;" colspan="${checkedDiscount ? 3 : 2}">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;text-align: end;">${Ultis.money(caculateVatMoney())}</div>
            </td>
        </tr>
        <tr class="table-quote-row-html" style="height: 40px;background-color: #9bc2e6; border-bottom: none">
            <td style="height: 100%;" colspan="5">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;">TỔNG CỘNG BAO GỒM VAT</div>
            </td>
            <td style="width: 268px; height: 100%;" colspan="${checkedDiscount ? 3 : 2}">
                <div style="font-size: 12px; font-weight: 600; padding: 4px;text-align: end;">${Ultis.money(totalBeforeDiscountAndVat() - caculateDiscountMoney() + caculateVatMoney())}</div>
            </td>
        </tr>
    </tbody>
</table>
`
}

const includesDicount = (methods: any) => {
    return [...methods.watch("devices"), ...methods.watch("bioProducts"), ...methods.watch("materials"), ...methods.watch("tasks")].some(e => e.Discount)
}

const cateTile = ({ item, methods }: any) => {
    const checkedDiscount = includesDicount(methods)
    const listDevice = methods.watch("devices").filter((e: { CateServicesId: any; }) => e.CateServicesId === item.Id) ?? []
    const listBio = methods.watch("bioProducts").filter((e: { CateServicesId: any; }) => e.CateServicesId === item.Id) ?? []
    const listTask = methods.watch("tasks").filter((e: { CateServicesId: any; }) => e.CateServicesId === item.Id) ?? []
    const listMaterial = methods.watch("materials").filter((e: { CateServicesId: any; }) => e.CateServicesId === item.Id) ?? []
    const cateList = []
    if (listDevice.length) cateList.push({
        name: "Thiết bị",
        list: listDevice.map((item: { ProductId: any; Id: any; Quantity: any; }, i: any) => {
            const product = methods.watch("products")?.find((e: { Id: any; }) => e.Id === item.ProductId)
            return quoteItem({ i: i, checkedDiscount: checkedDiscount, item: { ...item, "_Img": product?.Img, "_Quantity": item.Quantity, "_Unit": product?.Unit ?? "cái" } })
        })
    })
    if (listBio.length) cateList.push({
        name: "Chế phẩm sinh học",
        list: listBio.map((item: { ProductId: any; Id: any; Quantity: any; }, i: any) => {
            const product = methods.watch("products")?.find((e: { Id: any; }) => e.Id === item.ProductId)
            return quoteItem({ i: i, checkedDiscount: checkedDiscount, item: { ...item, "_Img": product?.Img, "_Quantity": item.Quantity, "_Unit": product?.Unit ?? "cái" } })
        })
    })
    if (listMaterial.length) cateList.push({
        name: "Vật tư",
        list: listMaterial.map((item: { MaterialId: any; Id: any; Quantity: any; }, i: any) => {
            const materialPartner = methods.watch("materialPartner")?.find((e: { Id: any; }) => e.Id === item.MaterialId)
            return quoteItem({ i: i, checkedDiscount: checkedDiscount, item: { ...item, "_Img": materialPartner?.Img, "_Quantity": item.Quantity, "_Unit": materialPartner?.Unit } })
        })
    })
    if (listTask.length) cateList.push({
        name: "Nhân công",
        list: listTask.map((item: { Id: any; Day: any; }, i: any) => {
            return quoteItem({ i: i, checkedDiscount: checkedDiscount, item: { ...item, "_Quantity": item.Day, "_Unit": "MD" } })
        })
    })

    return cateList.length ? `
    <tr style="height: 40px;">
      <td colspan="${checkedDiscount ? 8 : 7}">
        <div style="font-size: 14px; padding: 8px 4px; font-weight: 600; width: 100%;">${item.Name}</div>
    </td>
    </tr>
    ${cateList.map((e, i) => { return parentContent({ data: e, i: i, checkedDiscount: checkedDiscount }) }).join('\n')} ` : ''
}


const parentContent = ({ data, i, checkedDiscount }: any) => {
    var stt = ""
    switch (i) {
        case 0:
            stt = "I."
            break
        case 1:
            stt = "II."
            break
        case 2:
            stt = "III."
            break
        case 3:
            stt = "IV."
            break
        default:
            break;
    }

    return `
       <tr class="table-quote-row-html" style="height: 40px;">
    <td style="width: 48px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600;text-align: center;">${stt}</td>
    <td style="min-width: 120px; height: 100%; padding: 4px; font-size: 12px; font-weight: 600;">${data.name}</td>
    <td style="width: 56px; height: 100%;"></td>
    <td style="width: 40px; height: 100%;"></td>
    <td style="width: 100px; height: 100%;"></td>
    ${checkedDiscount ? `<td style="width: 80px; height: 100%;"></td>` : ""}
    <td style="width: 80px; height: 100%;"></td>
    <td style="width: 100px; height: 100%;"></td>
</tr>
        ${data.list.join('\n')}
    `
}


const quoteItem = ({ item, i, checkedDiscount }: any) => {
    const discount = item.Price * item._Quantity * ((item.Discount ?? 0) / 100)
    const vat = item.Price * item._Quantity * ((100 - (item.Discount ?? 0)) / 100) * ((item.Vat ?? 0) / 100)
    const totalPrice = item.Price * item._Quantity - discount + vat
    return `<tr class="table-quote-row-html" style="align-items: stretch;">
    <td style="width: 48px; min-height: 40px; padding: 4px; font-size: 12px;text-align: center;">${i + 1}</td>
    <td style="min-width: 120px; min-height: 40px; max-height: 120px; padding: 4px; font-size: 12px;">${item.Name}</td>
    <td style="width: 56px; min-height: 40px; padding: 4px; font-size: 12px; text-align: center;">${item._Unit}</td>
    <td style="width: 40px; min-height: 40px; padding: 4px; font-size: 12px; text-align: center;">${item._Quantity}</td>
    <td style="width: 100px; min-height: 40px; padding: 4px; font-size: 12px;text-align: end;">${Ultis.money(item.Price)}</td>
    ${checkedDiscount ? `<td style="width: 80px; min-height: 40px; padding: 4px; font-size: 12px;text-align: end;">${item.Discount ? Ultis.money(discount) : ""}</td>` : ""}
    <td style="width: 80px; min-height: 40px; padding: 4px; font-size: 12px;text-align: end;">${item.Vat ? Ultis.money(vat) : ""}</td>
    <td style="width: 100px; min-height: 40px; padding: 4px; font-size: 12px;text-align: end;">${Ultis.money(totalPrice)}</td>
    </tr>`
}


export const mapContractData = async (props: { partnerData: { data: any, company?: any }, customer: { data: any, company?: any }, servicesData: any, data: any, ktxgroup?: any, methods: any, cateServices: Array<any>, sign?: boolean, payment?: string }) => {
    const templateController = new DataController("Documents")
    const templateContractId = "********************************"
    const templateRes = await templateController.getById(templateContractId)
    const now = new Date()
    if (templateRes.code === 200) {
        const totalPriceValue = caculateTotalServicesValue({ methods: props.methods })
        return (templateRes.data?.Content ?? "").replace(regexGetVariables, (m: string, key: string) => {
            switch (key) {
                case "CateServicesTitle":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name.toUpperCase()
                case "CateServices":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name
                case "ContractNumber":
                    return props.servicesData.Name
                case "CustomerName":
                    return props.customer.company?.Name ?? props.customer.data?.Name ?? ""
                case "CustomerAddress":
                    return props.customer.company?.Address ?? props.customer.data?.Address ?? ""
                case "CustomerTaxCode":
                    return props.customer.company?.TaxCode ?? ""
                case "CustomerRepresentative":
                    return props.customer.company?.Representative ?? ""
                case "CustomerPosition":
                    return props.customer.company?.Position ?? ""
                case "CustomerMobile":
                    return props.customer.company?.Mobile ?? props.customer.data?.Mobile ?? ""
                case "PartnerName":
                    return props.partnerData?.company?.Name ?? props.partnerData.data?.Name
                case "PartnerMobile":
                    return props.partnerData.company?.Mobile ?? props.partnerData.data?.Mobile ?? ""
                case "PartnerAddress":
                    return props.partnerData?.company?.Address ?? props.partnerData.data?.Address
                case "PartnerTaxCode":
                    return props.partnerData?.company?.TaxCode ?? ""
                case "PartnerRepresentative":
                    return props.partnerData?.company?.Representative ?? ""
                case "PartnerPosition":
                    return props.partnerData?.company?.Position ?? ""
                case "KTXRepresentative":
                    return props.ktxgroup?.Representative ?? ""
                case "KTXPosition":
                    return props.ktxgroup?.Position ?? ""
                case "day":
                    return now.getDate() > 9 ? now.getDate() : `0${now.getDate()}`
                case "month":
                    return (now.getMonth() + 1) > 9 ? (now.getMonth() + 1) : `0${now.getMonth() + 1}`
                case "year":
                    return now.getFullYear()
                case "QuoteTable":
                    return quoteTableModelPrint({ methods: props.methods, cateServices: props.cateServices }) ?? ""
                case "TotalPrice":
                    return Ultis.money(totalPriceValue)
                case "TotalPriceByText":
                    return Ultis.to_vietnamese(totalPriceValue)
                case "PartnerSign":
                    return props.sign ? signTickImg : m
                case "PartnerSignName":
                    return props.sign ? props.partnerData.data.Name : m
                case "PartnerDateSign":
                    return props.sign ? Ultis.datetoString(new Date()) : m
                case "ContractNumber":
                    return props.servicesData.Name
                case "Payment":
                    return props.payment
                default:
                    return m
            }
        })
    }
    return ""
}

export const mapAddendumContractData = async (props: { addendum: any, partnerData: { data: any, company?: any }, customer: { data: any, company?: any }, servicesData: any, data: any, ktxgroup?: any, methods: any, cateServices: Array<any>, sign?: boolean, payment?: string }) => {
    const templateController = new DataController("Documents")
    const templateContractId = "639e03b574ec4b0ca6dd4a55408e8884"
    const templateRes = await templateController.getById(templateContractId)
    const now = new Date()
    if (templateRes.code === 200) {
        const totalPriceValue = caculateTotalServicesValue({ methods: props.methods })
        const controller = new DataController("Addendum")
        const addendumSigned = await controller.getListSimple({ page: 1, size: 100, query: `@ToiletServicesId:{${props.servicesData.Id}} @Type:[${AddendumType.contract} ${AddendumType.contract}] @Status:[${ContractStatus.guestSigned} ${ContractStatus.guestSigned}]` })
        if (addendumSigned.code === 200) var signedAddendums = addendumSigned.data.map((e: any) => e.Name)
        return (templateRes.data?.Content ?? "").replace(regexGetVariables, (m: string, key: string) => {
            switch (key) {
                case "AddendumName":
                    return props.addendum.Name
                case "CateServicesTitle":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name.toUpperCase()
                case "CateServices":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name
                case "ContractNumber":
                    return props.servicesData.Name
                case "CustomerName":
                    return props.customer.company?.Name ?? props.customer.data?.Name ?? ""
                case "CustomerAddress":
                    return props.customer.company?.Address ?? props.customer.data?.Address ?? ""
                case "CustomerTaxCode":
                    return props.customer.company?.TaxCode ?? ""
                case "CustomerRepresentative":
                    return props.customer.company?.Representative ?? ""
                case "CustomerPosition":
                    return props.customer.company?.Position ?? ""
                case "CustomerMobile":
                    return props.customer.company?.Mobile ?? props.customer.data?.Mobile ?? ""
                case "PartnerName":
                    return props.partnerData?.company?.Name ?? props.partnerData.data?.Name
                case "PartnerMobile":
                    return props.partnerData.company?.Mobile ?? props.partnerData.data?.Mobile ?? ""
                case "PartnerAddress":
                    return props.partnerData?.company?.Address ?? props.partnerData.data?.Address
                case "PartnerTaxCode":
                    return props.partnerData?.company?.TaxCode ?? ""
                case "PartnerRepresentative":
                    return props.partnerData?.company?.Representative ?? ""
                case "PartnerPosition":
                    return props.partnerData?.company?.Position ?? ""
                case "KTXRepresentative":
                    return props.ktxgroup?.Representative ?? ""
                case "KTXPosition":
                    return props.ktxgroup?.Position ?? ""
                case "day":
                    return now.getDate() > 9 ? now.getDate() : `0${now.getDate()}`
                case "month":
                    return (now.getMonth() + 1) > 9 ? (now.getMonth() + 1) : `0${now.getMonth() + 1}`
                case "year":
                    return now.getFullYear()
                case "QuoteTable":
                    return quoteTableModelPrint({ methods: props.methods, cateServices: props.cateServices }) ?? ""
                case "TotalPrice":
                    return Ultis.money(totalPriceValue)
                case "TotalPriceByText":
                    return Ultis.to_vietnamese(totalPriceValue)
                case "TotalContract":
                    return Ultis.money(props.servicesData.Value + totalPriceValue)
                case "TotalContractByText":
                    return Ultis.to_vietnamese(props.servicesData.Value + totalPriceValue)
                case "Payment":
                    return props.payment
                case "AddendumIndex":
                    return (signedAddendums ? [...signedAddendums, props.addendum.Name] : [props.addendum.Name]).join(" + ")
                case "PartnerSign":
                    return props.sign ? signTickImg : m
                case "PartnerSignName":
                    return props.sign ? props.partnerData.data.Name : m
                case "PartnerDateSign":
                    return props.sign ? Ultis.datetoString(new Date()) : m
                default:
                    return m
            }
        })
    }
    return ""
}

export const mapCheckBuildContractData = async (props: { partnerData: { data: any, company?: any }, customer: { data: any, company?: any }, servicesData: any, data: any, ktxgroup?: any, cateServices: Array<any>, methods?: any, bankAccount?: string, bankAccountName?: string, bankName?: string }) => {
    const templateController = new DataController("Documents")
    const templateContractId = "6bc2c41f5e834560afdaaaa1e74af07d"
    const templateRes = await templateController.getById(templateContractId)
    const now = new Date()
    if (templateRes.code === 200) {
        const controller = new DataController("Contract")
        const contractSignDate = await controller.getListSimple({ page: 1, size: 1, query: `@ToiletServicesId:{${props.servicesData.Id}} @Type:[${ContractType.contract} ${ContractType.contract}]`, returns: ["DateSign"] })
        const deviceController = new DataController("Device")
        const bioController = new DataController("BioProduct")
        const materialController = new DataController("MaterialToilet")
        const taskController = new DataController("Task")
        const res = await Promise.all([
            deviceController.aggregateList({ page: 1, size: 1000, searchRaw: `@ToiletServicesId:{${props.servicesData.Id}}` }),
            bioController.aggregateList({ page: 1, size: 1000, searchRaw: `@ToiletServicesId:{${props.servicesData.Id}}` }),
            materialController.aggregateList({ page: 1, size: 1000, searchRaw: `@ToiletServicesId:{${props.servicesData.Id}}` }),
            taskController.aggregateList({ page: 1, size: 1000, searchRaw: `@ToiletServicesId:{${props.servicesData.Id}} (-@Type:[${TaskType.consultant} ${TaskType.liquid}]) @Price:[0 +inf]` })
        ])
        if (res.every(e => e.code === 200)) {
            const mergeDevices = res[0].data.filter((dev: any, i: number, arr: Array<any>) => {
                return arr.findIndex(e => e.ProductId === dev.ProductId) === i
            }).map((dev: any) => {
                return {
                    ...dev,
                    Quantity: res[0].data.filter((item: any) => item.ProductId === dev.ProductId).length
                }
            })
            const productIds = [...mergeDevices, ...res[1].data].map(e => e.ProductId).filter((id, i, arr) => id?.length && arr.indexOf(id) === i)
            const materialIds = res[2].data.map((item: any) => item.MaterialId).filter((id: any, i: number, arr: Array<string>) => id?.length && arr.indexOf(id) === i)
            const productController = new DataController("Product")
            const productRes = await productController.getByListId(productIds)
            props.methods.setValue("products", productRes.data.filter((e: any) => e !== undefined && e !== null))
            const materialPartnerController = new DataController("Material")
            const materialPartner = await materialPartnerController.getByListId(materialIds)
            props.methods.setValue("materialPartner", materialPartner.data.filter((e: any) => e !== undefined && e !== null))
            props.methods.setValue("devices", res[0].data)
            props.methods.setValue("bioProducts", res[1].data)
            props.methods.setValue("materials", res[2].data)
            props.methods.setValue("tasks", res[3].data)
        }
        return (templateRes.data?.Content ?? "").replace(regexGetVariables, (m: string, key: string) => {
            switch (key) {
                case "CateServicesTitle":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name.toUpperCase()
                case "CateServices":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name
                case "ContractNumber":
                    return props.servicesData.Name
                case "CustomerName":
                    return props.customer.company?.Name ?? props.customer.data?.Name ?? ""
                case "CustomerAddress":
                    return props.customer.company?.Address ?? props.customer.data?.Address ?? ""
                case "CustomerTaxCode":
                    return props.customer.company?.TaxCode ?? ""
                case "CustomerRepresentative":
                    return props.customer.company?.Representative ?? ""
                case "CustomerPosition":
                    return props.customer.company?.Position ?? ""
                case "CustomerMobile":
                    return props.customer.company?.Mobile ?? props.customer.data?.Mobile ?? ""
                case "PartnerName":
                    return props.partnerData?.company?.Name ?? props.partnerData.data?.Name
                case "PartnerMobile":
                    return props.partnerData.company?.Mobile ?? props.partnerData.data?.Mobile ?? ""
                case "PartnerAddress":
                    return props.partnerData?.company?.Address ?? props.partnerData.data?.Address
                case "PartnerTaxCode":
                    return props.partnerData?.company?.TaxCode ?? ""
                case "PartnerRepresentative":
                    return props.partnerData?.company?.Representative ?? ""
                case "PartnerPosition":
                    return props.partnerData?.company?.Position ?? ""
                case "KTXRepresentative":
                    return props.ktxgroup?.Representative ?? ""
                case "KTXPosition":
                    return props.ktxgroup?.Position ?? ""
                case "day":
                    return now.getDate() > 9 ? now.getDate() : `0${now.getDate()}`
                case "month":
                    return (now.getMonth() + 1) > 9 ? (now.getMonth() + 1) : `0${now.getMonth() + 1}`
                case "year":
                    return now.getFullYear()
                case "DateSign":
                    return contractSignDate.data?.[0]?.DateSign ? Ultis.datetoString(new Date(typeof contractSignDate.data[0].DateSign === "string" ? parseInt(contractSignDate.data[0].DateSign) : contractSignDate.data[0].DateSign)) : ""
                case "TotalPrice":
                    return Ultis.money(props.servicesData.Value)
                case "TotalPriceByText":
                    return Ultis.to_vietnamese(props.servicesData.Value)
                case "QuoteTable":
                    return quoteTableModelPrint({ methods: props.methods, cateServices: props.cateServices }) ?? ""
                case "BankAccount":
                    return props.bankAccount
                case "BankAccountName":
                    return props.bankAccountName
                case "BankName":
                    return props.bankName
                default:
                    return m
            }
        })
    }
    return ""
}

export const mapLiquidContractData = async (props: { partnerData: { data: any, company?: any }, customer: { data: any, company?: any }, servicesData: any, data: any, ktxgroup?: any, cateServices: Array<any> }) => {
    const templateController = new DataController("Documents")
    const templateContractId = "e048a66efcae49139ab577f26a46561c"
    const templateRes = await templateController.getById(templateContractId)
    const now = new Date()
    if (templateRes.code === 200) {
        const controller = new DataController("Contract")
        const contractSignDate = await controller.getListSimple({ page: 1, size: 1, query: `@ToiletServicesId:{${props.servicesData.Id}} @Type:[${ContractType.contract} ${ContractType.contract}]`, returns: ["DateSign"] })
        return (templateRes.data?.Content ?? "").replace(regexGetVariables, (m: string, key: string) => {
            switch (key) {
                case "CateServicesTitle":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name.toUpperCase()
                case "CateServices":
                    return props.cateServices.find(e => props.servicesData.CateServicesId === e.Id)?.Name
                case "ContractNumber":
                    return props.servicesData.Name
                case "CustomerName":
                    return props.customer.company?.Name ?? props.customer.data?.Name ?? ""
                case "CustomerAddress":
                    return props.customer.company?.Address ?? props.customer.data?.Address ?? ""
                case "CustomerTaxCode":
                    return props.customer.company?.TaxCode ?? ""
                case "CustomerRepresentative":
                    return props.customer.company?.Representative ?? ""
                case "CustomerPosition":
                    return props.customer.company?.Position ?? ""
                case "CustomerMobile":
                    return props.customer.company?.Mobile ?? props.customer.data?.Mobile ?? ""
                case "PartnerName":
                    return props.partnerData?.company?.Name ?? props.partnerData.data?.Name
                case "PartnerMobile":
                    return props.partnerData.company?.Mobile ?? props.partnerData.data?.Mobile ?? ""
                case "PartnerAddress":
                    return props.partnerData?.company?.Address ?? props.partnerData.data?.Address
                case "PartnerTaxCode":
                    return props.partnerData?.company?.TaxCode ?? ""
                case "PartnerRepresentative":
                    return props.partnerData?.company?.Representative ?? ""
                case "PartnerPosition":
                    return props.partnerData?.company?.Position ?? ""
                case "KTXRepresentative":
                    return props.ktxgroup?.Representative ?? ""
                case "KTXPosition":
                    return props.ktxgroup?.Position ?? ""
                case "day":
                    return now.getDate() > 9 ? now.getDate() : `0${now.getDate()}`
                case "month":
                    return (now.getMonth() + 1) > 9 ? (now.getMonth() + 1) : `0${now.getMonth() + 1}`
                case "year":
                    return now.getFullYear()
                case "DateSign":
                    return contractSignDate.data?.[0]?.DateSign ? Ultis.datetoString(new Date(typeof contractSignDate.data[0].DateSign === "string" ? parseInt(contractSignDate.data[0].DateSign) : contractSignDate.data[0].DateSign)) : ""
                case "TotalPrice":
                    return Ultis.money(props.servicesData.Value)
                case "TotalPriceByText":
                    return Ultis.to_vietnamese(props.servicesData.Value)
                default:
                    return m
            }
        })
    }
    return ""
}


export const caculateTotalServicesValue = ({ methods }: { methods: any }) => {
    const caculateVatMoney = () => {
        const vatDevices = methods.watch("devices").reduce((a: number, b: { Price: number; Quantity: number; Discount: any; Vat: any }) => a + (b.Price * b.Quantity * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        const vatBios = methods.watch("bioProducts").reduce((a: number, b: { Price: number; Quantity: number; Discount: any; Vat: any }) => a + (b.Price * b.Quantity * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        const vatMaterials = methods.watch("materials").reduce((a: number, b: { Price: number; Quantity: number; Discount: any; Vat: any }) => a + (b.Price * b.Quantity * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        const vatTasks = methods.watch("tasks").reduce((a: number, b: { Price: number; Day: number; Discount: any; Vat: any }) => a + (b.Price * b.Day * ((100 - (b.Discount ?? 0)) / 100) * ((b.Vat ?? 0) / 100)), 0)
        return vatDevices + vatBios + vatMaterials + vatTasks
    }
    const caculateDiscountMoney = () => {
        const discountDevices = methods.watch("devices").reduce((a: number, b: { Price: number; Quantity: number; Discount: any }) => a + (b.Price * b.Quantity * ((b.Discount ?? 0) / 100)), 0)
        const discountBios = methods.watch("bioProducts").reduce((a: number, b: { Price: number; Quantity: number; Discount: any }) => a + (b.Price * b.Quantity * ((b.Discount ?? 0) / 100)), 0)
        const discountMaterials = methods.watch("materials").reduce((a: number, b: { Price: number; Quantity: number; Discount: any }) => a + (b.Price * b.Quantity * ((b.Discount ?? 0) / 100)), 0)
        const discountTasks = methods.watch("tasks").reduce((a: number, b: { Price: number; Day: number; Discount: any }) => a + (b.Price * b.Day * ((b.Discount ?? 0) / 100)), 0)
        return discountDevices + discountBios + discountMaterials + discountTasks
    }

    const totalBeforeDiscountAndVat = () => {
        const totalDevices = methods.watch("devices").reduce((a: number, b: { Price: number; Quantity: number }) => a + (b.Price * b.Quantity), 0)
        const totalBios = methods.watch("bioProducts").reduce((a: number, b: { Price: number; Quantity: number }) => a + (b.Price * b.Quantity), 0)
        const totalMaterials = methods.watch("materials").reduce((a: number, b: { Price: number; Quantity: number }) => a + (b.Price * b.Quantity), 0)
        const totalTasks = methods.watch("tasks").reduce((a: number, b: { Price: number; Day: number }) => a + (b.Price * b.Day), 0)
        return totalDevices + totalBios + totalMaterials + totalTasks
    }
    return totalBeforeDiscountAndVat() - caculateDiscountMoney() + caculateVatMoney()
}