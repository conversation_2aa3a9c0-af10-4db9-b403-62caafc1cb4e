import {StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollView: {
    flex: 1,
  },
  // Shop Profile Styles
  profileContainer: {
    alignItems: 'center',
    paddingVertical: 24,
    backgroundColor: 'white',
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#E8F5E8',
    marginBottom: 16,
    overflow: 'hidden',
  },
  avatarImage: {
    width: 100,
    height: 100,
  },
  avatarPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  shopName: {
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginRight: 6,
  },
  statusText: {
    color: '#4CAF50',
  },
  // Action Buttons Styles
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 20,
  },
  actionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Shop Details Styles
  detailsContainer: {
    backgroundColor: 'white',
    marginTop: 8,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  detailRowNoBorder: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  detailText: {
    marginLeft: 16,
  },
  // Statistics Styles
  statisticsContainer: {
    backgroundColor: 'white',
  },
  statisticsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statisticsItem: {
    alignItems: 'center',
    flex: 1,
  },
  statisticsValue: {
    marginBottom: 4,
  },
  statisticsLabel: {
    color: ColorThemes.light.primary_main_color,
  },
  statisticsDivider: {
    width: 1,
    height: 40,
    backgroundColor: ColorThemes.light.neutral_main_border_color,
  },
  productStatistics: {
    color: '#2196F3',
    marginBottom: 4,
  },
});
