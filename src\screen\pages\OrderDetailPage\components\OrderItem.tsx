import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import FastImage from 'react-native-fast-image';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import ConfigAPI from '../../../../config/configApi';

interface OrderItemProps {
  item: any;
  onProductPress: (productId: string) => void;
}

const OrderItem: React.FC<OrderItemProps> = ({item, onProductPress}) => {
  return (
    <TouchableOpacity
      onPress={() => onProductPress(item.ProductId)}
      key={item.Id}
      style={styles.orderItemRow}>
      <FastImage
        style={styles.productImage}
        source={{
          uri: item.Img?.startsWith('http')
            ? item.Img
            : `${ConfigAPI.imgUrlId}${item.Img}`,
          priority: FastImage.priority.normal,
        }}
        resizeMode={FastImage.resizeMode.cover}
      />
      <View style={styles.productDetails}>
        <Text style={styles.productName}>{item?.Name || 'Sản phẩm'}</Text>
        <View style={styles.productPriceRow}>
          {item?.Discount ? (
            <View style={styles.discountContainer}>
              <Text style={styles.originalPrice}>
                {Ultis.money(item.Price ?? 0)} đ
              </Text>
              <Text style={styles.productPrice}>
                {Ultis.money(item.Price - (item.Price * item.Discount) / 100)} đ
              </Text>
            </View>
          ) : (
            <Text style={styles.productPrice}>
              {Ultis.money(item.Price ?? 0)} đ
            </Text>
          )}
        </View>
        {/* <Text style={styles.rewardText}>
          Hoàn tiền: {Ultis.money(item.Reward ?? 0)} CANPOINT
        </Text> */}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  orderItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.body3,
    color: '#212121',
    marginBottom: 4,
  },
  productPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  discountContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  productPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
    marginRight: 8,
  },
  originalPrice: {
    ...TypoSkin.body3,
    color: '#757575',
    textDecorationLine: 'line-through',
  },
  rewardText: {
    ...TypoSkin.buttonText5,
  },
});

export default OrderItem;
