# CartPage Module

## Tổng quan
Module CartPage đã được refactor để có cấu trúc tốt hơn, d<PERSON> bảo trì và tái sử dụng. Các component lớn đã được tách thành các component nhỏ hơn với trách nhiệm rõ ràng.

## Cấu trúc thư mục

```
CartPage/
├── CartPage.tsx          # Component chính
├── index.ts              # Export tất cả components và utilities
├── README.md             # Tài liệu này
├── components/           # Các component con
│   ├── CartItem.tsx      # Component hiển thị một sản phẩm trong giỏ hàng
│   ├── StoreGroup.tsx    # Component hiển thị nhóm sản phẩm theo cửa hàng
│   ├── CartBottomBar.tsx # Component thanh bottom với tổng tiền và nút thanh toán
│   ├── RecipientInfo.tsx # Component thông tin người nhận
│   └── CartHeader.tsx    # Component header của cart
├── hooks/                # Custom hooks
│   └── useCartPage.ts    # Hook quản lý logic business của CartPage
└── styles/               # Styles
    └── CartPageStyles.ts # Styles chung cho CartPage
```

## Components

### CartPage.tsx
Component chính của trang giỏ hàng, sử dụng các component con và custom hook.

### CartItem.tsx
- Hiển thị thông tin một sản phẩm trong giỏ hàng
- Xử lý checkbox chọn sản phẩm
- Xử lý tăng/giảm số lượng
- Xử lý xóa sản phẩm
- Navigate đến trang chi tiết sản phẩm

### StoreGroup.tsx
- Hiển thị nhóm sản phẩm theo cửa hàng
- Xử lý checkbox chọn tất cả sản phẩm của cửa hàng
- Render danh sách CartItem

### CartBottomBar.tsx
- Hiển thị tổng tiền của các sản phẩm đã chọn
- Nút thanh toán với validation
- Disable/enable dựa trên việc có sản phẩm được chọn

### RecipientInfo.tsx
- Hiển thị thông tin người nhận
- Nút chỉnh sửa địa chỉ
- Xử lý trường hợp chưa có địa chỉ

### CartHeader.tsx
- Header đơn giản cho trang cart
- Wrapper cho TitleHeader

## Hooks

### useCartPage.ts
Custom hook quản lý toàn bộ logic business của CartPage:
- State management
- Cart actions
- Checkout logic
- Navigation logic
- Customer data

## Styles

### CartPageStyles.ts
Chứa styles chung cho component chính CartPage. Mỗi component con có styles riêng được định nghĩa trong file component đó.

## Cách sử dụng

### Import component chính
```typescript
import CartPage from './CartPage';
// hoặc
import {CartPage} from './CartPage';
```

### Import component con
```typescript
import {CartItem, StoreGroup, CartBottomBar} from './CartPage';
```

### Import hook
```typescript
import {useCartPage} from './CartPage';
```

## Backward Compatibility

Để đảm bảo tương thích ngược, các component cũ vẫn được export:
- `RenderRecipientInfo` -> `RecipientInfo`
- `RenderHeaderCart` -> `CartHeader`

## Lợi ích của việc refactor

1. **Tách biệt trách nhiệm**: Mỗi component có một trách nhiệm cụ thể
2. **Dễ test**: Có thể test từng component riêng biệt
3. **Tái sử dụng**: Các component có thể được sử dụng ở nơi khác
4. **Dễ bảo trì**: Code được tổ chức rõ ràng, dễ tìm và sửa
5. **Performance**: Có thể optimize từng component riêng biệt
6. **Type safety**: Props interface rõ ràng cho từng component

## Migration Guide

Nếu bạn đang sử dụng CartPage ở nơi khác:
1. Import vẫn hoạt động như cũ
2. Các export component cũ vẫn có sẵn
3. Không cần thay đổi code hiện tại
4. Có thể dần dần migrate sang sử dụng component mới nếu cần
