import React from 'react';
import {View, Text, ActivityIndicator, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {LoadingIndicatorProps} from '../types';
import {ColorThemes} from '../../../../assets/skin/colors';

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  isLoading,
  text = 'Đang tính toán...',
}) => {
  if (!isLoading) {
    return null;
  }

  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator
        size="large"
        color={ColorThemes.light.primary_main_color}
      />
      <Text style={styles.loadingText}>{text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: '#666666',
    marginTop: 12,
  },
});

export default LoadingIndicator;
