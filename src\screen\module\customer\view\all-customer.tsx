import { FlatList, View, Text, ImageBackground, Pressable } from "react-native";
import { ColorThemes } from "../../../../assets/skin/colors";
import ListTile from "../../../../component/list-tile/list-tile";
import AppButton from "../../../../component/button";
import { TypoSkin } from "../../../../assets/skin/typography";
import { Winicon } from "../../../../component/export-component";
import { SkeletonImage } from "../../../../project-component/skeleton-img";
import { DataController } from "../../../base-controller";
import { useEffect, useState } from "react";
import ConfigAPI from "../../../../config/configApi";
import { useNavigation } from "@react-navigation/native";
import { RootScreen } from "../../../../router/router";

export default function AllCustomers({ customers }: any) {
    const navigation = useNavigation<any>()

    return <View style={{ flex: 1, backgroundColor: ColorThemes.light.transparent, marginTop: 32, borderRadius: 8, borderColor: ColorThemes.light.neutral_main_border_color, borderWidth: 1 }}>
        <Pressable style={{ height: 189, borderTopLeftRadius: 8, borderTopRightRadius: 8, overflow: 'hidden' }}>
            {/* background img */}
            <SkeletonImage
                source={{ uri: 'https://file-mamager.wini.vn/Upload/2024/11/Page header_90ad.png' }}
                style={{
                    position: 'absolute',
                    width: '100%',
                    height: 189,
                    objectFit: 'cover',
                    aspectRatio: 1,
                    overflow: 'hidden',
                }}
            />
            <ListTile
                style={{
                    padding: 0,
                    backgroundColor: ColorThemes.light.transparent,
                    justifyContent: 'flex-end',
                    paddingVertical: 24, paddingHorizontal: 16,
                    shadowColor: '#000000',
                    height: '100%',
                    width: '100%',
                    shadowOffset: {
                        width: 0,
                        height: 3
                    },
                    shadowRadius: 5,
                    shadowOpacity: 1.0,
                }}
                title="Đối tác uy tín"
                titleStyle={[TypoSkin.heading6, { color: ColorThemes.light.neutral_text_stable_color }]}
                subtitle={`+${customers.length} đối tác`}
                subTitleStyle={[TypoSkin.subtitle4, { color: ColorThemes.light.neutral_text_label_reverse_color }]}
            />
        </Pressable>
        <View style={{ flex: 1, }}>
            <FlatList
                scrollEnabled={false}
                // keyExtractor={item => "_" + item.id}
                data={customers}
                style={{ flex: 1, gap: 8, marginVertical: 16 }}
                ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
                renderItem={({ item, index }) => <ListTile
                    key={`customer ${index}`}
                    onPress={item?.Url ? () => {
                        navigation.navigate(RootScreen.WebViewServiceFlow, { type: "project", url: `${item?.Url}` })
                    } : undefined}
                    style={{ padding: 0, paddingBottom: 10, paddingHorizontal: 16, backgroundColor: ColorThemes.light.white }}
                    leading={<SkeletonImage
                        source={{ uri: ConfigAPI.fileManagerUrl + item.Logo }}
                        style={{
                            width: 64,
                            height: 64,
                            borderColor: ColorThemes.light.neutral_bolder_border_color,
                            borderWidth: 0.5,
                            objectFit: 'cover',
                            borderRadius: 8
                        }}
                    />}
                    listtileStyle={{ gap: 16, }}
                    title={item?.Name ?? "-"}
                    titleStyle={[TypoSkin.heading8, { color: ColorThemes.light.neutral_text_title_color, paddingBottom: 4 }]}
                    subtitle={item?.Address ?? "-"}
                    subTitleStyle={[TypoSkin.subtitle4, { color: ColorThemes.light.neutral_text_subtitle_color }]}
                />}
            />
        </View>
    </View>
}