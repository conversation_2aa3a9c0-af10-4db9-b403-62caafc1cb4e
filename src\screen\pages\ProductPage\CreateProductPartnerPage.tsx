/* eslint-disable react-native/no-inline-styles */
import React, {useEffect} from 'react';
import {View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {StyleSheet} from 'react-native';
import TitleHeader from '../../layout/headers/TitleHeader';
import CreatePartnerProductForm from '../../module/product/component/form/CreatePartnerProductForm';
import {useRoute} from '@react-navigation/native';
import productDA from '../../module/product/productDA';

const CreateProductPartnerPage = () => {
  const route = useRoute<any>();
  const title = route.params?.title;
  const [dataEdit, setDataEdit] = React.useState<any[]>([]);
  const [dataCopy, setDataCopy] = React.useState<any[]>([]);

  const GetProductData = async () => {
    let res = await productDA.getProductById(route.params?.Id);

    if (res && res.code === 200) {
      if (title === 'Chỉnh sửa sản phẩm') setDataEdit(res.data);
      if (title === 'Sao chép sản phẩm') setDataCopy(res.data);
    } else {
      setDataEdit([]);
      setDataCopy([]);
    }
  };

  useEffect(() => {
    GetProductData();
  }, [title]);
  return (
    <View style={styles.container}>
      <TitleHeader title={title} />
      <CreatePartnerProductForm
        title={title}
        dataEdit={dataEdit}
        dataCopy={dataCopy}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_text_stable_color,
  },
});

export default CreateProductPartnerPage;
