/**
 * T<PERSON><PERSON> tổng các số trong một mảng
 * @param numbers Mảng các số cần tính tổng
 * @returns Tổng của các số trong mảng
 */
export const sumArray = (numbers) => {
  return numbers.reduce((sum, current) => sum + current, 0);
};

/**
 * Cập nhật một mảng với các object từ mảng khác và trả về một mảng mới.
 * Sử dụng Map để tối ưu hiệu suất tra cứu.
 *
 * @param {Array<Object>} originalArray Mảng gốc cần được cập nhật.
 * @param {Array<Object>} updatesArray Mảng chứa các object đã được cập nhật.
 * @returns {Array<Object>} Một mảng mới đã được cập nhật.
 */
export const updateArrayWithObjects = (
  originalArray,
  updatesArray,
) => {
  const updatesMap = new Map(updatesArray.map((obj) => [obj.Id, obj]));

  const newArray = originalArray.map((originalObj) => {
    return updatesMap.get(originalObj.Id) || originalObj;
  });

  return newArray;
};

/**
 * Get n random elements from an array
 * @param arr The array to get random elements from
 * @param n The number of random elements to get
 * @returns An array of n random elements
 */
export const getRandomObjects = (arr, n) => {
  if (n >= arr.length) {
    return arr;
  }

  const result = [];
  const len = arr.length;
  const taken = new Array(len);
  let count = n;

  while (count > 0) {
    const x = Math.floor(Math.random() * len);
    if (!taken[x]) {
      result.push(arr[x]);
      taken[x] = true;
      count--;
    }
  }
  return result;
};

/**
 * @param arr The array to get random elements from
 * @param newObject The new object to replace the old object
 */
export function replaceObjectById(arr, newObject) {
  return arr.map(item => 
    item.id === newObject.id ? newObject : item
  );
}
