import React, {forwardRef, useImperativeHandle} from 'react';
import {View, StyleSheet} from 'react-native';
import LoadingIndicator from 'screen/pages/CheckoutPage/components/LoadingIndicator';
import {
  useTicketData,
  useTicketForm,
  useTicketRefresh,
  useTicketUtils,
} from './hooks';
import {TicketList} from './components';

interface MyTicketComponentProps {
  setResult: (result: any[]) => void;
  searchValue: string;
  setRefreshing: (refreshing: boolean) => void;
  isRefreshing: boolean;
}

export interface MyTicketComponentRef {
  refreshTickets: () => void;
}

const MyTicketComponent = forwardRef<
  MyTicketComponentRef,
  MyTicketComponentProps
>(({setResult, searchValue, setRefreshing, isRefreshing}, ref) => {
  // Initialize hooks
  const {methods} = useTicketForm();
  const {isLoading, data, relatives, fetchTickets, onUpdateTicket} =
    useTicketData({
      searchValue,
      setResult,
      methods,
    });
  const {handleRefresh} = useTicketRefresh({
    setRefreshing,
    fetchTickets,
  });
  const {getTypeLabel} = useTicketUtils();

  // Expose refreshTickets method to parent component
  useImperativeHandle(
    ref,
    () => ({
      refreshTickets: () => {
        fetchTickets();
      },
    }),
    [fetchTickets],
  );

  if (isLoading)
    return <LoadingIndicator isLoading text="Đang tải dữ liệu..." />;

  return (
    <View style={styles.container}>
      <TicketList
        data={data.data}
        relatives={relatives}
        methods={methods}
        isRefreshing={isRefreshing}
        onRefresh={handleRefresh}
        onUpdateTicket={onUpdateTicket}
        getTypeLabel={getTypeLabel}
      />
    </View>
  );
});

MyTicketComponent.displayName = 'MyTicketComponent';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default MyTicketComponent;
