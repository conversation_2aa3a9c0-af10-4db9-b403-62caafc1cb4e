import React, {useState, useRef} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {FTextField, Winicon} from '../../../../component/export-component';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {FPopup} from '../../../../component/popup/popup';
import Task from '../../../module/toilet/view/detailProject/task';

const Work: React.FC<{dataTask?: any[]; toiletId: string; data: any}> = ({
  data,
}) => {
  const [search, setSearch] = useState('');
  const popupRef = useRef<FPopup>(null!);
  return (
    <View style={{flex: 1}}>
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <FTextField
            style={styles.searchInput}
            onChange={vl => {
              setSearch(vl.trim());
            }}
            prefix={<Winicon src="outline/development/zoom" size={14} />}
            value={search}
            placeholder="Tìm kiếm công việc..."
          />
          {/* <TouchableOpacity style={styles.filterButton}>
            <Text style={styles.filterText}>Bộ lọc công việc</Text>
            <Winicon src="outline/user interface/setup-preferences" size={14} />
          </TouchableOpacity> */}
        </View>
      </View>
      <Task data={data} search={'no'} />
    </View>
  );
};

const styles = StyleSheet.create({
  searchSection: {
    paddingVertical: 10,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingLeft: 16,
    paddingRight: 25,
  },
  searchInput: {
    width: '100%',
    paddingHorizontal: 10,
    paddingVertical: 10,
    height: 50,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
  },

  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.white,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    gap: 6,
    height: 50,
    width: '40%',
  },
  filterText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_disabled_color,
    flex: 1,
  },
});

export default Work;
