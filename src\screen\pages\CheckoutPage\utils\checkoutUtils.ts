import {randomGID, Ultis} from '../../../../utils/Utils';
import {StatusOrder} from '../../../../config/Contanst';
import {CartItem} from '../../../../types/cartTypes';
import {StoreGroup, Order, OrderDetail, PaymentMethod} from '../types';

/**
 * Calculate the total price for a list of cart items
 */
export const calculateItemsTotal = (items: CartItem[]): number => {
  return items.reduce((sum, item) => {
    const price = item.Price || 0;
    const quantity = item.Quantity || 1;
    const discount = item.Discount || 0;
    return sum + price * quantity * (1 - discount / 100);
  }, 0);
};

/**
 * Calculate reward amount for a single item
 */
export const calculateItemReward = (
  item: CartItem,
  rewardPercent: number,
): number => {
  const price = item.Price || 0;
  const quantity = item.Quantity || 1;
  const discount = item.Discount || 0;
  return (price * quantity * (1 - discount / 100) * rewardPercent) / 100;
};

/**
 * Create order object for a store group
 */
export const createOrderForStoreGroup = (
  storeGroup: StoreGroup,
  customer: any,
  addressId: string,
  payment: PaymentMethod,
): Order => {
  return {
    Id: randomGID(),
    CustomerId: customer?.Id,
    Name: customer?.Name,
    ShopId: storeGroup.ShopId,
    Code: Ultis.randomString(10).toLocaleUpperCase(),
    DateCreated: new Date().getTime(),
    DateUpdated: new Date().getTime(),
    Status: StatusOrder.new,
    Value: storeGroup.totalPrice,
    AddressId: addressId,
    PaymentType: payment.id,
    Description: 'Giao hàng nhanh',
  };
};

/**
 * Create order detail object for a cart item
 */
export const createOrderDetailForItem = (
  item: CartItem,
  orderId: string,
): OrderDetail => {
  const price = parseFloat(item.Price?.toString() ?? '0');
  const discount = parseFloat(item.Discount?.toString() ?? '0');
  const quantity = item.Quantity ?? 1;

  return {
    Id: randomGID(),
    Name: item.Name,
    OrderId: orderId,
    ProductId: item.ProductId,
    DateCreated: new Date().getTime(),
    Quantity: quantity,
    Price: price,
    Discount: discount,
    Status: StatusOrder.new,
    Total: price * quantity * (1 - (discount ?? 0) / 100),
  };
};

/**
 * Format out of stock message
 */
export const formatOutOfStockMessage = (outOfStockItems: string[]): string => {
  if (outOfStockItems.length === 1) {
    return `Sản phẩm "${outOfStockItems[0]}" đã hết hàng hoặc không đủ số lượng.`;
  }
  
  return `Các sản phẩm sau đã hết hàng hoặc không đủ số lượng:\n${outOfStockItems
    .map(name => `• ${name}`)
    .join('\n')}`;
};

/**
 * Get default address from customer addresses
 */
export const getDefaultAddress = (customerAddresses: any[]): any => {
  return customerAddresses?.find((item: any) => item.IsDefault);
};

/**
 * Validate checkout requirements
 */
export const validateCheckoutRequirements = (
  customer: any,
  customerAddress: any[],
): {isValid: boolean; message?: string} => {
  if (!customer) {
    return {
      isValid: false,
      message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
    };
  }

  if (customerAddress?.length === 0) {
    return {
      isValid: false,
      message: 'Vui lòng cập nhật điểm giao hàng để tiếp tục thanh toán',
    };
  }

  return {isValid: true};
};
