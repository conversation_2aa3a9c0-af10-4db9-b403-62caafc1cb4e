import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  ReturnKeyTypeOptions,
  ViewStyle,
  KeyboardTypeOptions,
} from 'react-native';
import {
  Controller,
  Control,
  FieldErrors,
  FieldValues,
  Path,
  PathValue,
} from 'react-hook-form';
import {Ultis} from '../../utils/Utils';
import {FTextField} from '../../component/export-component';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {convertErrors} from '../component-form';
import {ColorThemes} from '../../assets/skin/colors';

interface BaseTextFieldProps<T extends FieldValues = FieldValues> {
  // Required props
  control: Control<T>;
  name: Path<T>;
  errors: FieldErrors<T>;

  // Optional props
  loading?: boolean;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  defaultValue?: string;

  // Text input props
  secureTextEntry?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  maxLength?: number;
  autoFocus?: boolean;
  returnKeyType?: ReturnKeyTypeOptions;
  type?: KeyboardTypeOptions | 'money';

  // Styling props
  style?: ViewStyle;
  textStyle?: TextStyle;

  // Icon and decorations
  icon?: React.ReactNode;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;

  // Event handlers
  onFocus?: (value: string) => void;
  onBlur?: (value: string) => void;
  onSubmit?: (value: string) => void;
}

export const BaseTextField = <T extends FieldValues = FieldValues>({
  // Required props
  control,
  name,
  errors,

  // Optional props with defaults
  loading = false,
  placeholder,
  disabled = false,
  required = false,

  // Text input props
  defaultValue,
  secureTextEntry,
  multiline = false,
  numberOfLines,
  maxLength,
  autoFocus = false,
  returnKeyType = 'done',
  type,

  // Styling props
  style = {},
  textStyle = {},

  // Icon and decorations
  icon,
  prefix,
  suffix,

  // Event handlers
  onFocus,
  onBlur,
  onSubmit,
}: BaseTextFieldProps<T>) => {
  // State and helper functions
  const [isFocused, setIsFocused] = useState(false);
  const hasError = convertErrors(errors, name);

  const handleFocus = (field: any) => {
    setIsFocused(true);
    if (type === 'money' && field.value) {
      field.onChange(field.value.replaceAll(',', ''));
    }
    onFocus?.(field.value);
  };

  const handleBlur = (field: any) => {
    setIsFocused(false);
    if (type === 'money') {
      if (field.value && isNaN(parseFloat(field.value?.replaceAll(',', '')))) {
        field.onChange(0);
      } else {
        field.onChange(Ultis.money(field.value));
      }
    }
    onBlur?.(field.value);
  };

  // Dynamic styles based on state
  const getContainerStyle = () => ({
    backgroundColor: 'white',
    minHeight: 48,
  });

  const getTextStyle = () => ({
    ...styles.textInput,
    color: '#000000',
    textAlignVertical: multiline ? 'top' : 'center',
    ...textStyle,
    ...(hasError && styles.inputError),
  });

  return (
    <Controller
      control={control}
      name={name}
      rules={{required}}
      defaultValue={(defaultValue ?? '') as PathValue<T, Path<T>>}
      render={({field}) => (
        <View style={[style]}>
          {loading ? (
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item
                height={56}
                width={'100%'}
                borderRadius={8}
              />
            </SkeletonPlaceholder>
          ) : (
            <View style={styles.inputContainer}>
              {/* Input Field Container */}
              <View style={[styles.inputWrapper, getContainerStyle()]}>
                {/* Prefix */}
                {prefix && <View style={styles.prefixContainer}>{prefix}</View>}

                {/* Text Input */}
                <FTextField
                  style={getTextStyle()}
                  placeholder={placeholder || ''}
                  value={field.value}
                  numberOfLines={numberOfLines}
                  onChange={field.onChange}
                  onFocus={() => handleFocus(field)}
                  onBlur={() => handleBlur(field)}
                  returnKeyType={returnKeyType}
                  secureTextEntry={secureTextEntry}
                  disabled={disabled}
                  disabledBg={'white'}
                  multiline={multiline}
                  type={type === 'money' ? 'number-pad' : type}
                  autoFocus={autoFocus}
                  onSubmit={onSubmit}
                  maxLength={maxLength}
                />

                {/* Suffix */}
                {(suffix || type === 'money') && (
                  <View style={styles.suffixContainer}>
                    {suffix ||
                      (type === 'money' && (
                        <Text style={styles.currencyText}>VNĐ</Text>
                      ))}
                  </View>
                )}
              </View>

              {/* Error Message */}
              {hasError && (
                <Text style={styles.errorText}>
                  {hasError.message ||
                    `Vui lòng nhập ${placeholder || 'giá trị'}`}
                </Text>
              )}
            </View>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    position: 'relative',
    backgroundColor: 'transparent',
    borderBottomWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },

  // Icon and decorations
  iconContainer: {
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    width: 24,
    height: 24,
  },
  prefixContainer: {
    justifyContent: 'center',
  },
  suffixContainer: {
    justifyContent: 'center',
  },

  // Text input styles
  textInput: {
    flex: 1,
    fontSize: 16,
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  inputError: {
    color: '#E14337',
  },

  // Text styles
  currencyText: {
    fontSize: 16,
    color: '#8E8E93',
    fontWeight: '400',
  },
  errorText: {
    fontSize: 12,
    color: '#E14337',
    marginTop: 4,
    fontWeight: '400',
  },
});
