import { forwardRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { closePopup } from "../../../../../component/popup/popup";
import { Dimensions, SafeAreaView, View } from "react-native";
import { ScreenFooter } from "react-native-screens";
import { ColorThemes } from "../../../../../assets/skin/colors";
import AppButton from "../../../../../component/button";
import { Winicon } from "../../../../../component/export-component";
import ScreenHeader from "../../../../layout/header";
import SelectProductStep from "../../../service/components/form/SelectProductStep";
import WScreenFooter from "../../../../layout/footer";

export const PopupSelectDevices = forwardRef(function PopupSelectDevices(data: { devices?: any, onSubmit: any, isBio?: boolean, selectOther?: any }, ref: any) {
    const { devices, onSubmit, isBio = false, selectOther } = data
    const methods = useForm({ shouldFocusError: false, defaultValues: { devices: [] } })

    useEffect(() => {
        if (devices) methods.setValue("devices", devices)
    }, [devices])

    const _onSubmit = (ev: any) => {
        onSubmit(ev.devices)
        closePopup(ref)
    }
    return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height - 65, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: '#fff' }}>
        <ScreenHeader
            style={{
                backgroundColor: ColorThemes.light.transparent,
                flexDirection: 'row',
                height: 35,
                paddingTop: 8
            }}
            title={isBio ? 'Chọn Chế phẩm sinh học' : `Chọn thiết bị`}
            prefix={<View />}
            action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                <Winicon src="outline/layout/xmark" onClick={() => closePopup(ref)} size={20} color={ColorThemes.light.neutral_text_body_color} />
            </View>}
        />
        <View style={{ flex: 1, height: "100%", paddingBottom: 45 }}>
            <SelectProductStep display="flex" forConsultant methods={methods} type={isBio ? "bio" : "device"} />
        </View>
        <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16, paddingBottom: 16 }}>
            {selectOther ? <AppButton
                title={`${isBio ? 'Chế phẩm khác' : `Thiết bị khác`} `}
                backgroundColor={ColorThemes.light.neutral_main_background_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={selectOther}
                textColor={ColorThemes.light.neutral_text_body_color}
            /> : null}
            <AppButton
                title={'Xong'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={methods.handleSubmit(_onSubmit)}
                textColor={ColorThemes.light.neutral_absolute_background_color}
            />
        </WScreenFooter>
    </SafeAreaView>
});
