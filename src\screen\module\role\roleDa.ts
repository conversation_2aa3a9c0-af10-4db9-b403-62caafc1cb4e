import {DataController} from '../../base-controller';
import {CompanyItem} from '../../../types/companyType';
import {RoleItem} from '../../../types/roleType';

export interface GetCustomerRoleResult {
  isKtx: boolean;
  role: 'customer' | 'partner';
  isCompany: boolean;
  company: CompanyItem | null;
  roles: RoleItem[];
}

export class RoleDa {
  private customerController: DataController;
  private customerCompanyController: DataController;
  private companyController: DataController;

  constructor() {
    this.customerController = new DataController('Customer');
    this.customerCompanyController = new DataController('CustomerCompany');
    this.companyController = new DataController('CompanyProfile');
  }

  /**
   * Get customer role information including company and role details
   * @param customerId - The ID of the customer
   * @returns Promise<GetCustomerRoleResult>
   */
  async getCustomerRole(customerId: string): Promise<GetCustomerRoleResult> {
    try {
      // Default result
      const result: GetCustomerRoleResult = {
        isKtx: false,
        role: 'customer',
        isCompany: false,
        company: null,
        roles: [],
      };

      if (!customerId) {
        return result;
      }

      // Step 1: Get customer information
      const customerRes = await this.customerController.getById(customerId);
      if (customerRes.code !== 200 || !customerRes.data) {
        return result;
      }

      const customer = customerRes.data;

      if (customer.IsKtx) result.isKtx = true;

      // Set role based on customer.Type
      // Type = 1 => customer, Type = 2 => partner
      // CustomerType.partner
      if (customer.Type === 2) {
        result.role = 'partner';
      } else {
        result.role = 'customer';
      }

      // Step 2: Check if customer has a company role (CustomerCompany relationship)
      const customerCompanyRes =
        await this.customerCompanyController.getListSimple({
          page: 1,
          size: 1000,
          query: `@CustomerId:{${customerId}} @Status:[1]`, // Only active roles
        });

      if (
        customerCompanyRes.code === 200 &&
        customerCompanyRes.data.length > 0
      ) {
        // Customer has a company role
        const customerCompany = customerCompanyRes.data[0];
        result.isCompany = true;

        // Get company information
        const companyRes = await this.companyController.getById(
          customerCompany.CompanyProfileId,
        );
        if (companyRes.code === 200 && companyRes.data) {
          result.company = companyRes.data as CompanyItem;
        }

        // Map CustomerCompany to RoleItem format
        result.roles = customerCompanyRes.data.map(
          (cc: any) =>
            ({
              Id: cc.Id,
              Name: cc.Name || cc.Role,
              DateCreated: cc.DateCreated,
              Status: cc.Status,
              Role: cc.Role,
              Sort: cc.Sort,
              Description: cc.Description,
              CustomerId: cc.CustomerId,
              CompanyProfileId: cc.CompanyProfileId,
            }) as RoleItem,
        );
      } else if (customer.CompanyProfileId) {
        // Customer has direct company relationship (owner)
        result.isCompany = true;

        // Get company information
        const companyRes = await this.companyController.getById(
          customer.CompanyProfileId,
        );
        if (companyRes.code === 200 && companyRes.data) {
          result.company = companyRes.data as CompanyItem;
        }

        // Create role item for owner
        result.roles = [
          {
            Id: customer.Id,
            Name: 'Owner',
            DateCreated: customer.DateCreated || Date.now(),
            Status: 1,
            Role: 'Owner',
            Sort: 1,
            Description: 'Company Owner',
            CustomerId: customer.Id,
            CompanyProfileId: customer.CompanyProfileId,
          } as RoleItem,
        ];
      }

      return result;
    } catch (error) {
      console.error('Error in getCustomerRole:', error);
      return {
        isKtx: false,
        role: 'customer',
        isCompany: false,
        company: null,
        roles: [],
      };
    }
  }

  /**
   * Get customer role information for multiple customers including company and role details
   * @param customerIds - Array of customer IDs
   * @returns Promise<GetCustomerRoleResult[]>
   */
  async getCustomersRole(
    customerIds: string[],
  ): Promise<GetCustomerRoleResult[]> {
    try {
      if (!customerIds || customerIds.length === 0) {
        return [];
      }

      // Use Promise.all to process all customers concurrently for better performance
      const results = await Promise.all(
        customerIds.map(async customerId => {
          try {
            return await this.getCustomerRole(customerId);
          } catch (error) {
            console.error(`Error processing customer ${customerId}:`, error);
            // Return default result for failed customer
            return {
              isKtx: false,
              role: 'customer' as const,
              isCompany: false,
              company: null,
              roles: [],
            };
          }
        }),
      );

      return results;
    } catch (error) {
      console.error('Error in getCustomersRole:', error);
      return [];
    }
  }
}
