import {ComponentStatus} from 'wini-mobile-components';
import {showSnackbar} from '../../../../../component/export-component';
import {randomGID} from '../../../../../utils/Utils';
import {NetZeroDa} from '../../../service/Da/netzeroDa';
import CetificateAchievemenDa from '../../../../pages/CetificateAchievementPage/CetificateAchievemenDa';
export const CreateToiletOperationFor12Mounth = async (
  data: any,
  serviceData: any,
) => {
  const netZeroDa = new NetZeroDa();

  let dataInit = {
    DateCreated: Date.now(),
    Name: '',
    Sort: 0,
    ElectricityConsumption: 0,
    WaterUsage: 0,
    Bioproducts: 0,
    CountCleaning: 0,
    Approver: '',
    Status: 1,
    Reason: '',
    Note: '',
    ApproveStatus: 0,
    DateStart: 0,
    CateCriterionId: '',
  };
  const checkNestZeroService = await netZeroDa.getNestZeroService();
  if (checkNestZeroService?.code !== 200) {
    return;
  } else {
    if (serviceData?.CateServicesId !== checkNestZeroService?.data[0]?.Id) {
      return;
    } else {
      if (data && data?.length > 0) {
        const dataCreate: any[] = [];
        let IdtoiletList = serviceData.ToiletId.split(',');
        // Tạo 12 bản ghi cho mỗi toilet trong IdtoiletList
        IdtoiletList.forEach((toiletId: string) => {
          // Tạo 12 bản ghi cho 12 tháng tiếp theo cho mỗi toilet
          for (let i = 0; i < 12; i++) {
            const currentDate = new Date();
            // Bắt đầu từ tháng sau (i + 1) thay vì tháng hiện tại (i)
            const targetMonth = currentDate.getMonth() + i + 1; // Tháng hiện tại + i + 1 (bắt đầu từ tháng sau)
            // Tính năm và tháng chính xác
            // Khi targetMonth >= 12, năm sẽ tăng lên (ví dụ: tháng 13 = tháng 1 năm sau)
            const targetYear =
              currentDate.getFullYear() + Math.floor(targetMonth / 12);
            const actualMonth = targetMonth % 12; // Tháng thực tế (0-11)

            // Tạo ngày mùng 1 của tháng đích
            const dateStart = new Date(targetYear, actualMonth, 1);
            const record = {
              ...dataInit,
              Id: randomGID(),
              Name: `Thống kê vận hành Tháng ${actualMonth + 1}/${targetYear} của NVS ${data.find((item: any) => item.Id === toiletId.trim())?.Name || 'N/A'}`,
              Sort: actualMonth + 1,
              DateStart: dateStart.getTime(),
              ToiletId: toiletId.trim(),
              CustomerId: data?.CustomerId,
              CateCriterionId: serviceData?.CateCriterionId,
              ToiletServicesId: serviceData?.Id,
            };
            dataCreate.push(record);
          }
        });
        if (dataCreate.length > 0) {
          const res = await netZeroDa.createToiletOperational(dataCreate);
          if (res?.code === 200) {
            await netZeroDa.createAcceptanceByToiletId(
              IdtoiletList,
              serviceData?.Id,
              serviceData?.CateCriterionId,
            );
            await CetificateAchievemenDa.getLogByToiletId(
              serviceData?.ToiletId.split(','),
              'Tạo mới 12 công việc thống kê vận hành thành công',
            );
            showSnackbar({
              message: 'Tạo công việc thành công',
              status: ComponentStatus.SUCCSESS,
            });
          } else {
            showSnackbar({
              message: 'Tạo công việc thất bại',
              status: ComponentStatus.ERROR,
            });
          }
        }
      }
    }
  }
};
