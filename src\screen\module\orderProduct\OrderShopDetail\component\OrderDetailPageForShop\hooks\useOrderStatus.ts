import {useState} from 'react';

export const useOrderStatus = () => {
  const [isCancelPopupVisible, setCancelPopupVisible] = useState(false);
  const [isUpdateStatusPopupVisible, setUpdateStatusPopupVisible] = useState(false);

  const handleRejectOrder = () => {
    setCancelPopupVisible(true);
  };

  const handleUpdateStatusOrder = () => {
    setUpdateStatusPopupVisible(true);
  };

  const closeCancelPopup = () => {
    setCancelPopupVisible(false);
  };

  const closeUpdateStatusPopup = () => {
    setUpdateStatusPopupVisible(false);
  };

  return {
    isCancelPopupVisible,
    isUpdateStatusPopupVisible,
    handleRejectOrder,
    handleUpdateStatusOrder,
    closeCancelPopup,
    closeUpdateStatusPopup,
    setCancelPopupVisible,
    setUpdateStatusPopupVisible,
  };
};
