import { Dimensions, Pressable, RefreshC<PERSON><PERSON>, ScrollView } from "react-native";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

export const PageSkeleton = ({ refreshing, onRefresh }: any) => {
    const size = Dimensions.get('window')
    return (
        <ScrollView style={{ marginHorizontal: 16, flex: 1, height: "100%" }} refreshControl={
            <RefreshControl
                refreshing={refreshing ?? false}
                onRefresh={() => {
                    if (onRefresh) onRefresh()
                }} />}>
            <Pressable>
                <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item flexDirection="column" marginTop={8} gap={4}>
                        <SkeletonPlaceholder.Item height={20} width={150} />
                        <SkeletonPlaceholder.Item height={18} width={100} />
                        <SkeletonPlaceholder.Item height={12} width={size.width / 1.5} />
                        <SkeletonPlaceholder.Item height={100} marginTop={4} width={size.width} />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item flexDirection="column" marginTop={8} gap={4}>
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2} />
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2} />
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2} />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item height={40} marginTop={16} width={100} borderRadius={8} />
                </SkeletonPlaceholder>
                <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item flexDirection="column" marginTop={8} gap={4}>
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2} />
                        <SkeletonPlaceholder.Item height={22} width={size.width / 1.5} />
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2.5} />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item flexDirection="column" marginTop={16} gap={4}>
                        <SkeletonPlaceholder.Item height={22} width={size.width / 1.5} />
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2} />
                        <SkeletonPlaceholder.Item height={22} width={size.width / 2.5} />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
            </Pressable>
        </ScrollView>
    );
};