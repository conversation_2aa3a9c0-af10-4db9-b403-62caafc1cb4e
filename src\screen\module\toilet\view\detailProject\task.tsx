import {differenceInDays} from 'date-fns';
import React, {useRef, useState, useMemo, useEffect, forwardRef} from 'react';
import {useForm} from 'react-hook-form';
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  showSnackbar,
  Winicon,
  FDialog,
  showDialog,
  FTextField,
  FCheckbox,
  HashTag,
} from '../../../../../component/export-component';
import ListTile from '../../../../../component/list-tile/list-tile';
import {
  FPopup,
  showPopup,
  closePopup,
} from '../../../../../component/popup/popup';
import {
  Fselect1Form,
  TextFieldForm,
} from '../../../../../project-component/component-form';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {
  CustomerType,
  CustomerRole,
} from '../../../../../redux/reducers/user/da';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
  TaskRepeatTypeStrings,
  CateServicesType,
} from '../../../service/components/da';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import FLoading from '../../../../../component/Loading/FLoading';

export default function Task({
  data,
  serviceData,
  refreshing,
  onRefresh,
  search,
}: any) {
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const userRole = useSelectorCustomerState().role;
  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();
  const [tasks, setTasks] = useState<Array<any>>([]);

  const methods = useForm({shouldFocusError: false});
  const [searchValue, setSearchValue] = useState('');

  const [toiletServices, setToiletServices] = useState<Array<any>>([]);

  const getData = async ({page, size}: any) => {
    const taskController = new DataController('Task');
    let query = `@ToiletId:{${data.Id}}`;
    if (searchValue?.length) query += ` @Name:(*${searchValue}*)`;

    const res = await taskController.aggregateList({
      page: page ?? 1,
      size: size ?? 100,
      searchRaw: query,
    });
    if (res.code === 200) {
      const servicesIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }
      let list = res.data.map((e: any) => {
        if (differenceInDays(new Date(), new Date(e.DateEnd)) > 0)
          return {...e, Status: TaskStatus.overdue};
        return e;
      });

      setTasks(list);
    }
  };

  const returnButtonChangeStatus = (item: any) => {
    if (!item) return <View />;

    switch (item?.Status) {
      case TaskStatus.open:
        return (
          <Text
            style={{
              color: ColorThemes.light.infor_main_color,
              backgroundColor: ColorThemes.light.infor_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Đang mở
          </Text>
        );
      case TaskStatus.done:
        return (
          <Text
            style={{
              color: ColorThemes.light.success_main_color,
              backgroundColor: ColorThemes.light.success_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Hoàn thành
          </Text>
        );
      case TaskStatus.overdue:
        return (
          <Text
            style={{
              color: ColorThemes.light.error_main_color,
              backgroundColor: ColorThemes.light.error_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Quá hạn
          </Text>
        );
      case TaskStatus.closed:
        return (
          <Text
            style={{
              color: ColorThemes.light.neutral_text_title_color,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Đã đóng
          </Text>
        );
      default:
        return (
          <Text
            style={{
              color: ColorThemes.light.neutral_text_title_color,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            -
          </Text>
        );
    }
  };

  const onChangeTask = async (t: any) => {
    const controller = new DataController('Task');
    const res = await controller.edit([t]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    setTasks((data: any) => data.map((e: any) => (e.Id === t.Id ? t : e)));
    showSnackbar({
      message: 'Chỉnh sửa công việc thành công',
      status: ComponentStatus.SUCCSESS,
    });
  };

  useEffect(() => {
    if (data) getData({});
  }, [data]);

  useEffect(() => {
    if (tasks.length) {
      const customerController = new DataController('Customer');
      customerController
        .getByListId(tasks.map((e: any) => e.CustomerId))
        .then(cusRes => {
          if (cusRes.code === 200) setCustomers(cusRes.data);
        });
    }
  }, [tasks]);

  const [customers, setCustomers] = useState<Array<any>>([]);

  const addNewTask = async () => {
    const taskController = new DataController('Task');
    const res = await taskController.add([
      {
        Id: randomGID(),
        Name: 'Công việc mới',
        DateCreated: Date.now(),
        Description: 'Công việc mới',
        Type: TaskType.other,
        ToiletId: data.Id,
        Status: TaskStatus.open,
        CustomerId: user?.Id,
      },
    ]);
    if (res.code === 200) getData({});
  };

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          paddingHorizontal: 16,
          gap: 8,
          paddingBottom: 16,
        }}>
        {!search ? (
          <FTextField
            style={{paddingHorizontal: 16, flex: 1, height: 40}}
            onChange={vl => {
              setSearchValue(vl.trim());
            }}
            onBlur={() => getData({})}
            onSubmit={() => getData({})}
            prefix={
              <Winicon
                src="outline/development/zoom"
                size={14}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
            }
            value={searchValue}
            placeholder="Tìm kiếm"
          />
        ) : (
          <></>
        )}
      </View>
      <KeyboardAvoidingView style={{paddingHorizontal: 16, flex: 1}}>
        {tasks.length == 0 ? (
          <EmptyPage title={'Nhà vệ sinh chưa có công việc nào'} />
        ) : (
          <FlatList
            data={tasks}
            style={{flex: 1}}
            refreshControl={
              <RefreshControl
                refreshing={refreshing ?? false}
                onRefresh={() => {
                  if (onRefresh) onRefresh();
                  getData({});
                }}
              />
            }
            keyExtractor={(item, i) => item + '-' + i}
            renderItem={({item, index}: any) => {
              const _customer = customers.find(
                (e: any) => e?.Id === item?.CustomerId,
              ) as any;
              let checkEditable = undefined;
              if (item.ToiletServicesId)
                var toiletServicesData = toiletServices.find(
                  e => e.Id === item.ToiletServicesId,
                );
              if (toiletServicesData) {
                if (toiletServicesData.Status < ToiletServiceStatus.run) {
                  checkEditable =
                    (owner?.Id === toiletServicesData?.CustomerId &&
                      userRole?.Role.includes(CustomerRole.Coordinator)) ||
                    user?.Id === toiletServicesData?.CustomerId;
                } else checkEditable = false;
              } else {
                checkEditable =
                  (owner?.Id === data?.CustomerId &&
                    userRole?.Role.includes(CustomerRole.Coordinator)) ||
                  user?.Id === data?.CustomerId ||
                  item.CustomerId === user?.Id;
              }

              var startValue = undefined;
              var endValue = undefined;
              if (item.DateStart) startValue = new Date(item.DateStart);
              if (item.DateEnd) endValue = new Date(item.DateEnd);

              return (
                <ListTile
                  key={`${item.Id} ${index}`}
                  onPress={() => {
                    if (item.DateStart) {
                      methods.setValue('dateStart', new Date(item?.DateStart));
                    } else {
                      methods.setValue('dateStart', undefined);
                    }

                    if (item.DateEnd) {
                      methods.setValue('dateEnd', new Date(item?.DateEnd));
                    } else {
                      methods.setValue('dateEnd', undefined);
                    }

                    methods.setValue(
                      'Description',
                      `${item?.Description ?? ''}`,
                    );
                    methods.setValue(
                      'CateServicesId',
                      `${item?.CateServicesId ?? 'undefined'}`,
                    );
                    if (checkEditable && item.Type === TaskType.other) {
                      methods.setValue('Name', `${item?.Name}`);
                    }

                    var options = [
                      {id: TaskStatus.open, name: 'Mở'},
                      // {id: TaskStatus.doing, name: "Đang làm"},
                      {id: TaskStatus.done, name: 'Hoàn thành'},
                      // { id: TaskStatus.overdue, name: "Quá hạn" },
                      {id: TaskStatus.closed, name: 'Đóng'},
                    ];
                    if (item?.Status < TaskStatus.overdue) {
                      options.filter(e => e.id < TaskStatus.overdue);
                    }
                    methods.setValue(
                      'status',
                      options.find(e => e.id === item?.Status)?.id ??
                        options[0].id,
                    );

                    if (checkEditable && item.Status !== TaskStatus.closed) {
                      showPopup({
                        ref: popupRef,
                        enableDismiss: true,
                        children: (
                          <PopupEditTask
                            methods={methods}
                            options={options}
                            toiletId={data.Id}
                            ref={popupRef}
                            item={item}
                            onChange={onChangeTask}
                            checkEditable={checkEditable}
                          />
                        ),
                      });
                    } else {
                      showPopup({
                        ref: popupRef,
                        enableDismiss: true,
                        children: (
                          <PopupEditTask
                            onlyView={true}
                            methods={methods}
                            options={options}
                            toiletId={data.Id}
                            ref={popupRef}
                            item={item}
                            onChange={onChangeTask}
                            checkEditable={checkEditable}
                          />
                        ),
                      });
                    }
                  }}
                  title={`${index + 1}. ${item?.Name ?? ''}`}
                  titleStyle={[
                    TypoSkin.heading7,
                    {color: ColorThemes.light.neutral_text_title_color},
                  ]}
                  subtitle={
                    <View style={{gap: 4, paddingTop: 4}}>
                      <Text
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Người thực hiện: ${_customer?.Name ?? ''}`}</Text>
                      <Text
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Số điện thoại: ${_customer?.Mobile ?? ''}`}</Text>
                      {startValue && endValue ? (
                        <View
                          style={{
                            flex: 1,
                            width: '100%',
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 8,
                          }}>
                          <Text
                            style={{
                              ...TypoSkin.buttonText4,
                              color:
                                ColorThemes.light.neutral_text_subtitle_color,
                            }}>
                            {startValue
                              ? `${Ultis.datetoString(startValue, startValue.getSeconds() === 1 ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy')} - `
                              : ''}{' '}
                            {endValue
                              ? Ultis.datetoString(
                                  endValue,
                                  endValue.getSeconds() === 59
                                    ? 'dd/mm/yyyy hh:mm'
                                    : 'dd/mm/yyyy',
                                )
                              : ''}
                          </Text>
                          {item.RepeatValue ? (
                            <Winicon src="outline/arrows/loop-2" size={12} />
                          ) : null}
                        </View>
                      ) : null}
                      {item.DateEnd && item.DateStart ? (
                        <Text
                          style={{
                            ...TypoSkin.buttonText4,
                            color:
                              ColorThemes.light.neutral_text_subtitle_color,
                          }}>{`Số ngày: ${item.DateEnd && item.DateStart ? differenceInDays(new Date(item.DateEnd), new Date(item.DateStart)) : '-'}`}</Text>
                      ) : null}
                      {item.Description ? (
                        <Text
                          style={{
                            ...TypoSkin.buttonText4,
                            color:
                              ColorThemes.light.neutral_text_subtitle_color,
                          }}
                          numberOfLines={3}>
                          Mô tả: {item.Description ?? ''}
                        </Text>
                      ) : (
                        <View />
                      )}
                    </View>
                  }
                  listtileStyle={{gap: 16}}
                  style={{
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderWidth: 1,
                    marginTop: 16,
                    padding: 16,
                  }}
                  trailing={
                    <View style={{padding: 4}}>
                      {checkEditable && item.Status !== TaskStatus.closed ? (
                        <Winicon
                          src="outline/user interface/d-edit"
                          size={16}
                        />
                      ) : (
                        <Winicon src="outline/user interface/view" size={16} />
                      )}
                    </View>
                  }
                  bottom={
                    <View
                      style={{
                        flexDirection: 'row',
                        alignContent: 'space-between',
                        width: '100%',
                        paddingTop: 12,
                        flex: 1,
                      }}>
                      {returnButtonChangeStatus(item)}
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                          flex: 1,
                        }}>
                        {(!toiletServicesData && checkEditable) ||
                        (((owner?.Id === data?.CustomerId &&
                          userRole?.Role.includes(CustomerRole.Coordinator)) ||
                          user?.Id === data?.CustomerId) &&
                          toiletServicesData.Status ===
                            ToiletServiceStatus.run) ? (
                          <TouchableOpacity
                            style={{padding: 4}}
                            onPress={() => {
                              showDialog({
                                ref: dialogRef,
                                status: ComponentStatus.WARNING,
                                title: 'Bạn chắc chắn muốn xóa',
                                onSubmit: async () => {
                                  const controller = new DataController('Task');
                                  controller.delete([item.Id]).then(res => {
                                    if (res.code === 200) {
                                      showSnackbar({
                                        message: 'Xóa công việc thành công!',
                                        status: ComponentStatus.SUCCSESS,
                                      });
                                      getData({});
                                    }
                                  });
                                },
                              });
                            }}>
                            <Winicon
                              src="outline/user interface/trash-can"
                              size={16}
                            />
                          </TouchableOpacity>
                        ) : null}
                      </View>
                    </View>
                  }
                />
              );
            }}
            ListFooterComponent={() => <View style={{height: 100}} />}
          />
        )}
      </KeyboardAvoidingView>
      {((owner?.Id === data?.CustomerId &&
        userRole?.Role.includes(CustomerRole.Coordinator)) ||
        user?.Id === data?.CustomerId) &&
      data.Status === ToiletStatus.run ? (
        <WScreenFooter style={{paddingHorizontal: 16, paddingBottom: 8}}>
          <AppButton
            title={'Thêm công việc'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={addNewTask}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      ) : null}
    </View>
  );
}

// #region PopupEditTask
export const PopupEditTask = forwardRef(function PopupEditTask(
  data: {
    onlyView?: any;
    item: any;
    methods: any;
    toiletId: any;
    options: Array<any>;
    onChange: any;
    checkEditable: any;
  },
  ref: any,
) {
  const {
    item,
    onChange,
    methods,
    options,
    toiletId,
    checkEditable,
    onlyView = false,
  } = data;
  const dialogRef = useRef<any>();
  const popupRef = useRef<any>();

  const [open, setOpen] = useState(false);
  const [open1, setOpen1] = useState(false);
  const [isTime, setTime] = useState(
    new Date(item?.DateStart).getSeconds() === 1 ? true : false,
  );
  const [isRepeat, setRepeat] = useState(false);
  const [isRequireCheckin, setRequireCheckin] = useState(
    item?.RequireCheckin ? true : false,
  );
  const [cateServices, setCateServices] = useState<Array<any>>([]);
  const [isOpen, setIsOpen] = useState(true);

  const [repeatData, setRepeatData] = useState<{
    type: any;
    value: Array<string | number>;
  }>({type: undefined, value: ['everyday']}); // 1: daily, 2: weekly, 3: monthly
  const today = new Date();
  const [checkList, setCheckList] = useState({
    dev: Array<any>(),
    bio: Array<any>(),
  });

  const user = useSelectorCustomerState().data;

  const company = useSelectorCustomerCompanyState().data;
  const userRole = useSelectorCustomerState().role;
  const [assignees, setAssignees] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (
      company &&
      (user?.CompanyProfileId === company.Id ||
        (userRole?.CompanyProfileId === company.Id &&
          userRole?.Role?.includes(CustomerRole.Coordinator)))
    ) {
      setLoading(true);
      const roleController = new DataController('CustomerCompany');
      roleController
        .aggregateList({
          page: 1,
          size: 1000,
          searchRaw: `@CompanyProfileId:{${company.Id}} @Status:[1 1]`,
        })
        .then(res => {
          if (res.code === 200) {
            const customerController = new DataController('Customer');
            customerController
              .getByListId(res.data.map((e: any) => e.CustomerId))
              .then(resCustomer => {
                if (resCustomer.code === 200 && resCustomer.data.length) {
                  switch (data.item?.Type) {
                    case TaskType.consultant:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return (
                            _role?.Role?.includes(CustomerRole.Consultant) ||
                            _role?.Role?.includes(CustomerRole.Coordinator)
                          );
                        }),
                      );
                      break;
                    case TaskType.contract:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return _role?.Role?.includes(
                            CustomerRole.Coordinator,
                          );
                        }),
                      );
                      break;
                    case TaskType.design:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return (
                            _role &&
                            [
                              CustomerRole.SCBD,
                              CustomerRole.Coordinator,
                              CustomerRole.Owner,
                              CustomerRole.Consultant,
                            ].some(rl => _role.Role.includes(rl))
                          );
                        }),
                      );
                      break;
                    default:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return _role !== undefined;
                        }),
                      );
                      break;
                  }
                } else setAssignees([user]);
              });
            setLoading(false);
          }
        });
    } else {
      setLoading(false);
      return setAssignees([user]);
    }
  }, [company, user]);

  useEffect(() => {
    if (assignees?.length)
      methods.setValue(
        'assignee',
        assignees.find(e => e.Id === item?.CustomerId)?.Id ?? assignees[0].Id,
      );
  }, [assignees, item]);

  useEffect(() => {
    if (item?.RepeatValue && item.Repeat) {
      setRepeat(true);
      setRepeatData({type: item.Repeat, value: JSON.parse(item.RepeatValue)});
      methods.setValue('repeatValue', item.Repeat);
    }
  }, []);

  useEffect(() => {
    if (item?.DateStart) {
      const initStart = new Date(item?.DateStart);
      if (initStart.getSeconds() === 1) {
        methods.setValue(
          'time-start',
          `${initStart.getHours() < 9 ? `0${initStart.getHours()}` : initStart.getHours()}:${initStart.getMinutes() < 9 ? `0${initStart.getMinutes()}` : initStart.getMinutes()}`,
        );
      }
    }
    if (item?.DateEnd) {
      const initEnd = new Date(item?.DateEnd);
      if (initEnd.getSeconds() === 59)
        methods.setValue(
          'time-end',
          `${initEnd.getHours() < 9 ? `0${initEnd.getHours()}` : initEnd.getHours()}:${initEnd.getMinutes() < 9 ? `0${initEnd.getMinutes()}` : initEnd.getMinutes()}`,
        );
    }
  }, []);

  useEffect(() => {
    if (methods.getValues('dateStart')) {
      let dateStartValue = new Date(methods.getValues('dateStart'));
      let timeStartValue = isTime
        ? (methods.getValues('time-start') ?? '00:00')
        : '00:00';
      dateStartValue.setHours(
        parseInt(timeStartValue.split(':')[0]),
        parseInt(timeStartValue.split(':')[1]),
        isTime ? 1 : 0,
        0,
      );
      methods.setValue('dateStart', dateStartValue.getTime());
    }
    if (methods.getValues('dateEnd')) {
      let dateEndValue = new Date(methods.getValues('dateEnd'));
      let timeEndValue = isTime
        ? (methods.getValues('time-end') ?? '23:59')
        : '23:59';
      dateEndValue.setHours(
        parseInt(timeEndValue.split(':')[0]),
        parseInt(timeEndValue.split(':')[1]),
        isTime ? 59 : 0,
        0,
      );
      methods.setValue('dateEnd', dateEndValue.getTime());
    }
  }, [isTime]);

  useEffect(() => {
    const controller = new DataController('CateServices');
    controller.getAll().then(res => {
      if (res.code === 200) setCateServices(res.data);
    });
  }, []);

  const _onSubmit = async (ev: any) => {
    if (isRepeat && repeatData?.type == undefined) {
      return showSnackbar({
        message: 'Vui lòng nhập sự kiện lặp lại',
        status: ComponentStatus.WARNING,
      });
    }
    var dateStart = ev?.dateStart ? ev?.dateStart : undefined;
    var dateEnd = ev?.dateEnd ? ev?.dateEnd : undefined;
    delete ev?.dateStart;
    delete ev?.dateEnd;
    var obj = {
      ...item,
      ...ev,
      DeviceId: ev?.DeviceId ? ev?.DeviceId : undefined,
      BioProductId: ev?.BioProductId ? ev?.BioProductId : undefined,
      RequireCheckin: isRequireCheckin,
      Status: ev?.status === TaskStatus.closed ? TaskStatus.closed : ev?.status,
      CustomerId: ev?.assignee ? ev?.assignee : undefined,
      DateStart: dateStart,
      DateEnd: dateEnd,
      Repeat: isRepeat ? repeatData?.type : undefined,
      RepeatValue: isRepeat ? JSON.stringify(repeatData.value) : undefined,
    };

    if (ev?.status === TaskStatus.closed) {
      showDialog({
        ref: dialogRef,
        status: ComponentStatus.WARNING,
        title: 'Bạn chắc chắn muốn đóng công việc này?',
        content:
          'Bạn sẽ không thể cập nhật trạng thái hay thông tin của công việc sau khi thực hiện thao tác này.',
        onCancel: () => {
          methods.setValue('status', ev?.status);
          return;
        },
        onSubmit: () => {
          setTimeout(() => {
            closePopup(ref);
          }, 300);
          onChange(obj);
        },
      });
    } else {
      setTimeout(() => {
        closePopup(ref);
      }, 300);
      onChange(obj);
    }
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  const startValue = useMemo(() => {
    if (!item || !methods.watch('dateStart')) return '';
    return `${Ultis.datetoString(new Date(methods.watch('dateStart')), isTime ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy')}`;
  }, [item, open, isTime, methods.watch('dateStart')]);

  const endValue = useMemo(() => {
    if (!item || !methods.watch('dateEnd')) return '';
    return Ultis.datetoString(
      new Date(methods.watch('dateEnd')),
      isTime ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy',
    );
  }, [item, open1, isTime, methods.watch('dateStart')]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={onlyView ? 'Xem chi tiết' : `Chỉnh sửa`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        behavior="padding"
        style={{height: '100%', width: '100%', paddingHorizontal: 16}}>
        <ScrollView
          style={{flex: 1, height: '100%', width: '100%'}}
          showsVerticalScrollIndicator={false}>
          <View style={{height: '100%', flexDirection: 'column'}}>
            {checkEditable && item.Type === TaskType.other ? (
              <TextFieldForm
                label="Tên công việc"
                required
                disabled={onlyView}
                textFieldStyle={{padding: 16}}
                style={{width: '100%', marginBottom: 16}}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Name"
              />
            ) : null}
            <Fselect1Form
              label="Trạng thái"
              control={methods.control}
              disabled={onlyView}
              errors={methods.formState.errors}
              style={{marginBottom: 8}}
              name="status"
              options={options.map((e: any) => {
                return {
                  id: e.id,
                  name: e.name,
                };
              })}
            />
            <Fselect1Form
              label="Loại công việc"
              control={methods.control}
              disabled={onlyView}
              errors={methods.formState.errors}
              style={{marginBottom: 16, marginTop: 8}}
              name="CateServicesId"
              onChange={(e: any) => {
                if (e.id === 'undefined') {
                  methods.setValue('CateServicesId', undefined);
                  return;
                }
                methods.setValue('CateServicesId', e.id);
              }}
              options={[
                {Id: 'undefined', Name: 'Công việc khác'},
                ...cateServices,
              ].map((item: any) => {
                return {
                  id: item.Id,
                  name: (
                    <View pointerEvents="none">
                      <ListTile
                        style={{padding: 0, backgroundColor: 'transparent'}}
                        leading={
                          <Winicon
                            src={
                              item.Id == 'undefined'
                                ? 'outline/development/todo'
                                : ConfigAPI.imgUrlId + item?.Img
                            }
                            size={item.Id == 'undefined' ? 16 : 24}
                          />
                        }
                        title={item?.Name ?? '-'}
                      />
                    </View>
                  ),
                };
              })}
            />
            <Fselect1Form
              label="Người thực hiện"
              control={methods.control}
              loading={loading}
              disabled={onlyView}
              errors={methods.formState.errors}
              style={{height: 80}}
              name="assignee"
              options={assignees.map((customer: any) => {
                return {
                  id: customer.Id,
                  name: (
                    <View pointerEvents="none">
                      <ListTile
                        style={{padding: 0, backgroundColor: 'transparent'}}
                        leading={
                          customer?.Img ? (
                            <SkeletonImage
                              source={{
                                uri: customer.Img.startsWith('https')
                                  ? customer.Img
                                  : ConfigAPI.imgUrlId + customer?.Img,
                              }}
                              style={{
                                width: 32,
                                height: 32,
                                borderRadius: 50,
                                objectFit: 'cover',
                              }}
                            />
                          ) : (
                            <View
                              style={{
                                width: 32,
                                height: 32,
                                borderRadius: 50,
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: Ultis.generateDarkColorRgb(),
                              }}>
                              <Text
                                style={{
                                  color: '#fff',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}>
                                {customer?.Name?.substring(0, 1)}
                              </Text>
                            </View>
                          )
                        }
                        title={customer?.Name ?? '-'}
                        subtitle={customer?.Mobile ?? '-'}
                      />
                    </View>
                  ),
                };
              })}
            />
            <View
              pointerEvents={onlyView ? 'none' : 'auto'}
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                gap: 8,
                marginTop: 45,
                marginBottom: 8,
              }}>
              <View style={{flex: 1, height: 65, gap: 8}}>
                <Text
                  style={{
                    fontWeight: 'bold',
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Ngày bắt đầu
                </Text>
                <TouchableOpacity
                  onPress={() => setOpen(true)}
                  style={{
                    padding: 8,
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderRadius: 8,
                    gap: 8,
                    width: '100%',
                    flex: 1,
                    borderWidth: 1,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    {startValue}
                  </Text>
                  {isRepeat ? (
                    <Winicon src="outline/arrows/loop-2" size={12} />
                  ) : null}
                </TouchableOpacity>
                <DatePicker
                  modal
                  open={open}
                  date={new Date(methods.getValues('dateStart') ?? new Date())}
                  title={'Ngày bắt đầu'}
                  mode={isTime ? 'datetime' : 'date'}
                  locale="vi"
                  dividerColor={'#f2f5f8'}
                  maximumDate={
                    methods.watch('dateEnd')
                      ? new Date(methods.getValues('dateEnd'))
                      : undefined
                  }
                  confirmText="Xác nhận"
                  theme="light"
                  cancelText="Hủy"
                  onConfirm={date => {
                    setOpen(false);
                    methods.setValue('dateStart', date.getTime());
                  }}
                  onCancel={() => {
                    setOpen(false);
                  }}
                />
              </View>
              <View style={{flex: 1, height: 65, gap: 8}}>
                <Text
                  style={{
                    fontWeight: 'bold',
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Ngày kết thúc
                </Text>
                <TouchableOpacity
                  onPress={() => setOpen1(true)}
                  style={{
                    padding: 8,
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderRadius: 8,
                    width: '100%',
                    flex: 1,
                    gap: 8,
                    borderWidth: 1,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    {endValue}
                  </Text>
                  {isRepeat ? (
                    <Winicon src="outline/arrows/loop-2" size={12} />
                  ) : null}
                </TouchableOpacity>
                <DatePicker
                  modal
                  open={open1}
                  mode={isTime ? 'datetime' : 'date'}
                  locale="vi"
                  date={new Date(methods.getValues('dateEnd') ?? new Date())}
                  title={'Ngày kết thúc'}
                  confirmText="Xác nhận"
                  cancelText="Hủy"
                  theme="light"
                  minimumDate={
                    methods.watch('dateStart')
                      ? new Date(methods.getValues('dateStart'))
                      : undefined
                  }
                  dividerColor={'#f2f5f8'}
                  onConfirm={date => {
                    setOpen1(false);
                    methods.setValue('dateEnd', date.getTime());
                  }}
                  onCancel={() => {
                    setOpen1(false);
                  }}
                />
              </View>
            </View>
            {/* cham cong */}
            <TouchableOpacity
              disabled={onlyView}
              onPress={() => {
                setRequireCheckin(!isRequireCheckin);
              }}
              style={{
                alignSelf: 'baseline',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
              }}>
              <FCheckbox
                disable={onlyView}
                value={isRequireCheckin}
                onChange={() => {
                  setRequireCheckin(!isRequireCheckin);
                }}
              />
              <Winicon src="outline/user interface/calendar-event" size={16} />
              <Text
                style={{
                  ...TypoSkin.label3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                Yêu cầu chấm công
              </Text>
            </TouchableOpacity>
            {/* time | lap lai*/}
            <TouchableOpacity
              disabled={onlyView}
              onPress={() => {
                setTime(!isTime);
              }}
              style={{
                alignSelf: 'baseline',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
                marginTop: 8,
              }}>
              <FCheckbox
                disable={onlyView}
                value={isTime}
                onChange={() => {
                  setTime(!isTime);
                }}
              />
              <Winicon src="outline/user interface/time-alarm" size={16} />
              <Text
                style={{
                  ...TypoSkin.label3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                Thêm giờ
              </Text>
            </TouchableOpacity>
            {/* lap lai */}
            <View style={{gap: 8, paddingVertical: 8}}>
              {/* time */}
              <TouchableOpacity
                disabled={onlyView}
                onPress={() => {
                  setRepeat(!isRepeat);
                }}
                style={{
                  alignSelf: 'baseline',
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                }}>
                <FCheckbox
                  disable={onlyView}
                  value={isRepeat}
                  onChange={() => {
                    setRepeat(!isRepeat);
                  }}
                />
                <Winicon src="outline/arrows/loop-2" size={16} />
                <Text
                  style={{
                    ...TypoSkin.label3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Thêm Lặp lại
                </Text>
              </TouchableOpacity>
              {isRepeat ? (
                <View
                  style={{
                    gap: 16,
                    padding: 8,
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderRadius: 8,
                    borderWidth: 1,
                  }}>
                  <Fselect1Form
                    control={methods.control}
                    required={isRepeat}
                    disabled={onlyView}
                    label="Lặp lại"
                    errors={methods.formState.errors}
                    name="repeatValue"
                    onChange={v => {
                      if (v.id == 1) {
                        setRepeatData({type: v.id, value: ['everyday']});
                      }
                      if (v.id == 2) {
                        var newValue = today.getDay();
                        setRepeatData({type: v.id, value: [newValue]});
                      }
                      if (v.id == 3) {
                        var newValue = today.getDate();
                        methods.setValue('monthlyValue', newValue);
                        setRepeatData({type: v.id, value: [newValue]});
                      }
                    }}
                    options={TaskRepeatTypeStrings.map(e => {
                      return {
                        id: e.key,
                        name: e.title,
                      };
                    })}
                  />
                  {isRepeat && repeatData?.type != 1 ? (
                    <View style={{width: '100%', flex: 1}}>
                      {repeatData?.type === 2 ? (
                        <View
                          style={{
                            width: '100%',
                            flex: 1,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                          }}>
                          {Array.from({length: 7}).map((_, i) => {
                            var weekdayTitle = '';
                            switch (i) {
                              case 0:
                                weekdayTitle = 'CN';
                                break;
                              case 1:
                                weekdayTitle = 'T2';
                                break;
                              case 2:
                                weekdayTitle = 'T3';
                                break;
                              case 3:
                                weekdayTitle = 'T4';
                                break;
                              case 4:
                                weekdayTitle = 'T5';
                                break;
                              case 5:
                                weekdayTitle = 'T6';
                                break;
                              case 6:
                                weekdayTitle = 'T7';
                                break;
                              default:
                                weekdayTitle = '';
                                break;
                            }
                            return (
                              <View
                                pointerEvents={onlyView ? 'none' : 'auto'}
                                key={'weekday-' + i}
                                style={{gap: 4, alignItems: 'center'}}>
                                <FCheckbox
                                  value={repeatData.value.includes(i)}
                                  disable={
                                    repeatData.value.includes(i) &&
                                    repeatData.value.length === 1
                                  }
                                  onChange={v => {
                                    if (v)
                                      setRepeatData({
                                        type: 2,
                                        value: [...repeatData.value, i],
                                      });
                                    else
                                      setRepeatData({
                                        type: 2,
                                        value: repeatData.value.filter(
                                          id => id !== i,
                                        ),
                                      });
                                  }}
                                />
                                <Text
                                  style={{
                                    ...TypoSkin.buttonText4,
                                    color:
                                      ColorThemes.light
                                        .neutral_text_subtitle_color,
                                  }}>
                                  {weekdayTitle}
                                </Text>
                              </View>
                            );
                          })}
                        </View>
                      ) : (
                        <View>
                          {/* monthly */}
                          <Fselect1Form
                            label="Lặp lại vào ngày"
                            control={methods.control}
                            disabled={onlyView}
                            errors={methods.formState.errors}
                            style={{marginBottom: 8}}
                            name="monthlyValue"
                            onChange={v => {
                              setRepeatData({
                                type: 3,
                                value: [v.id === 28 ? 'last' : v.id],
                              });
                            }}
                            options={Array.from({length: 29}).map(
                              (e: any, num: number) => {
                                var label = '';
                                switch (num) {
                                  case 28:
                                    label = 'Cuối tháng';
                                    break;
                                  default:
                                    label = `${num + 1}`;
                                    break;
                                }
                                return {
                                  id: num + 1,
                                  name: label,
                                };
                              },
                            )}
                          />
                        </View>
                      )}
                    </View>
                  ) : null}
                </View>
              ) : null}
            </View>

            <TextFieldForm
              control={methods.control}
              name="Description"
              label="Mô tả"
              disabled={onlyView}
              errors={methods.formState.errors}
              placeholder={'Mô tả ngắn gọn...'}
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 100,
                width: '100%',
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              register={methods.register}
            />
            <View style={{marginVertical: 16, flex: 1, gap: 16}}>
              {item ? (
                <CheckListTable
                  title={`Thiết bị cần ${(cateServices.find(e => e.Id === item.CateServicesId)?.Name ?? 'kiểm tra').toLowerCase()}`}
                  toiletId={toiletId}
                  onlyView={onlyView}
                  ids={item.DeviceId?.split(',').filter(
                    (id: any) => id?.length,
                  )}
                  setCheckList={setCheckList}
                  onChange={(ids: any) => {
                    methods.setValue('DeviceId', ids.join(','));
                  }}
                />
              ) : null}
              {item ? (
                <CheckListTable
                  controlModule="BioProduct"
                  title="Kiểm tra việc sử dụng chế phẩm sinh học"
                  onlyView={onlyView}
                  toiletId={toiletId}
                  setCheckList={setCheckList}
                  ids={item.BioProductId?.split(',').filter(
                    (id: any) => id?.length,
                  )}
                  onChange={(ids: any) => {
                    methods.setValue('BioProductId', ids.join(','));
                  }}
                />
              ) : null}
            </View>
            <View style={{flex: 1, paddingBottom: 150, gap: 16}}>
              <ListTile
                style={{
                  padding: 0,
                  borderColor: ColorThemes.light.neutral_main_border_color,
                  borderWidth: 1,
                }}
                onPress={() => setIsOpen(!isOpen)}
                listtileStyle={{padding: 8, gap: 8}}
                title={'Xem chi tiết'}
                trailing={
                  <Winicon
                    src={
                      isOpen
                        ? 'outline/arrows/arrow-sm-down'
                        : 'fill/arrows/arrow-sm-right'
                    }
                    size={28}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                }
                bottom={
                  !isOpen ? null : (
                    <View style={{flex: 1, width: '100%'}}>
                      <ListTile
                        style={{padding: 0, paddingHorizontal: 6}}
                        listtileStyle={{padding: 8, gap: 8}}
                        leading={
                          <Winicon
                            src="outline/arrows/time-machine"
                            size={16}
                          />
                        }
                        title={'Tiến độ'}
                        onPress={() =>
                          showPopup({
                            ref: popupRef,
                            enableDismiss: true,
                            children: (
                              <PopupActivityView
                                ref={popupRef}
                                item={item}
                                checkList={checkList}
                                onlyView={onlyView}
                              />
                            ),
                          })
                        }
                        trailing={
                          <Winicon
                            src={'outline/user interface/view'}
                            size={18}
                            color={
                              ColorThemes.light.neutral_text_subtitle_color
                            }
                          />
                        }
                      />
                      <ListTile
                        style={{padding: 0, paddingHorizontal: 6}}
                        listtileStyle={{padding: 8, gap: 8}}
                        leading={
                          <Winicon src="outline/files/file-history" size={16} />
                        }
                        title={'Lịch sử'}
                        trailing={
                          <Winicon
                            src={'outline/user interface/view'}
                            size={18}
                            color={
                              ColorThemes.light.neutral_text_subtitle_color
                            }
                          />
                        }
                      />
                      <ListTile
                        style={{padding: 0, paddingHorizontal: 6}}
                        listtileStyle={{padding: 8, gap: 8}}
                        leading={
                          <Winicon
                            src="outline/user interface/f-comment"
                            size={16}
                          />
                        }
                        title={'Thảo luận'}
                        trailing={
                          <Winicon
                            src={'outline/user interface/view'}
                            size={18}
                            color={
                              ColorThemes.light.neutral_text_subtitle_color
                            }
                          />
                        }
                      />
                    </View>
                  )
                }
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      {onlyView ? null : (
        <WScreenFooter
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingBottom: 16,
          }}>
          <AppButton
            title={'Xong'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            disabled={onlyView}
            onPress={methods.handleSubmit(_onSubmit, _onError)}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      )}
    </SafeAreaView>
  );
});

const CheckListTable = ({
  controlModule = 'Device',
  title,
  ids = [],
  onChange,
  toiletId,
  onlyView,
  setCheckList,
}: any) => {
  const controller = new DataController(controlModule);
  const [devices, setDevices] = useState<Array<any>>([]);
  const [products, setProducts] = useState<Array<any>>([]);
  const [selectedProds, setSelectProds] = useState<Array<any>>([]);
  const popupRef = useRef<any>();

  useEffect(() => {
    if (ids.length || selectedProds.length) {
      controller
        .getByListId(ids.length ? ids : selectedProds)
        .then(async res => {
          if (res.code === 200) {
            const productIds = res.data
              .filter((e: any) => e?.ProductId)
              .map((e: any) => e?.ProductId)
              .filter(
                (v: any, i: any, a: string | any[]) => a.indexOf(v) === i,
              );
            if (productIds.length) {
              const productController = new DataController('Product');
              const productRes =
                await productController.getByListId(productIds);
              if (productRes.code === 200) setProducts(productRes.data);
            }
            setDevices(res.data);
            if (setCheckList)
              setCheckList((cl: any) =>
                controlModule === 'Device'
                  ? {...cl, dev: res.data}
                  : {...cl, bio: res.data},
              );
          }
        });
    } else {
      setDevices([]);
    }
  }, [ids.length, selectedProds.length]);

  return (
    <View style={{}}>
      <FPopup ref={popupRef} />
      <ListTile
        style={{
          padding: 0,
          backgroundColor: ColorThemes.light.neutral_main_border_color,
        }}
        listtileStyle={{padding: 8, gap: 8}}
        leading={<Winicon src="outline/user interface/bolt" size={16} />}
        title={title}
        onPress={() =>
          showPopup({
            ref: popupRef,
            enableDismiss: true,
            children: (
              <PopupSelectCheckList
                ref={popupRef}
                controlModule={controlModule}
                toiletId={toiletId}
                selectedProds={devices.map((e: any) => e.Id)}
                onlyView={onlyView}
                onSubmit={(vl: any) => {
                  setSelectProds(vl);
                  onChange(vl);
                }}
              />
            ),
          })
        }
        trailing={
          <Winicon
            src={'fill/layout/circle-plus'}
            size={18}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        }
      />
      {devices.length
        ? devices.map((dev: any, i: any) => {
            if (dev.ProductId)
              var _product = products.find(e => e.Id === dev.ProductId);
            return (
              <ListTile
                key={`${dev.Id} ${i}`}
                leading={
                  <Winicon
                    src="fill/user interface/c-check"
                    size={16}
                    color={ColorThemes.light.success_main_color}
                  />
                }
                title={dev.Name ?? '-'}
                subtitle={`Số lượng: ${dev.Quantity} ${_product?.Unit ?? dev.Unit}`}
                trailing={
                  <TouchableOpacity
                    disabled={onlyView}
                    style={{padding: 4}}
                    onPress={() => {
                      setSelectProds(
                        devices
                          .map((e: any) => e.Id)
                          .filter((e: any) => e !== dev.Id),
                      );
                      onChange(
                        devices
                          .map((e: any) => e.Id)
                          .filter((e: any) => e !== dev.Id),
                      );
                    }}>
                    <Winicon src="outline/user interface/trash-can" size={16} />
                  </TouchableOpacity>
                }
              />
            );
          })
        : null}
    </View>
  );
};

const PopupSelectCheckList = forwardRef(function PopupSelectCheckList(
  data: {
    controlModule: any;
    toiletId: any;
    selectedProds: Array<any>;
    onSubmit: any;
    onlyView: any;
  },
  ref: any,
) {
  const {controlModule, toiletId, selectedProds, onSubmit, onlyView} = data;
  const controller = new DataController(controlModule);
  const [contentData, setContentData] = useState<any>({
    data: Array<any>(),
    totalCount: undefined,
  });
  const [products, setProducts] = useState<Array<any>>([]);
  const [selected, setSelected] = useState<Array<any>>([]);

  const getData = async () => {
    const res = await controller.getListSimple({
      page: 1,
      size: 1000,
      query: `@ToiletId:{${toiletId}}`,
      returns: ['Id', 'Name', 'Img', 'Quantity', 'Unit', 'ProductId'],
    });
    const productIds = res.data
      .filter((e: any) => e?.ProductId)
      .map((e: any) => e?.ProductId)
      .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
    if (productIds.length) {
      const productController = new DataController('Product');
      const productRes = await productController.getByListId(productIds);
      if (productRes.code === 200) setProducts(productRes.data);
    }
    setContentData({data: res.data, totalCount: res.totalCount});
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (selectedProds?.length) setSelected(selectedProds);
  }, [selectedProds]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          height: 35,
          paddingTop: 8,
        }}
        title={`Chọn thiết bị`}
        prefix={<View />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <ScrollView style={{flex: 1, height: '100%', paddingBottom: 45}}>
        {contentData.totalCount === 0 ? (
          <EmptyPage
            title={`Nhà vệ sinh không có ${controlModule === 'Device' ? 'thiết bị' : 'chế phẩm sinh học'} nào`}
          />
        ) : (
          <View style={{flex: 1, paddingBottom: 75}}>
            {contentData.data.map((dev: any, i: number) => {
              const checked = selected.includes(dev?.Id);
              if (dev?.ProductId) {
                var _product = products.find(e => e.Id === dev?.ProductId);
              }
              return (
                <ListTile
                  onPress={
                    onlyView
                      ? undefined
                      : () => {
                          if (!selected.includes(dev.Id))
                            setSelected([...selected, dev.Id]);
                          else setSelected(s => s.filter(id => id !== dev.Id));
                        }
                  }
                  key={dev.Id}
                  leading={
                    <SkeletonImage
                      source={{
                        uri: ConfigAPI.imgUrlId + (_product?.Img ?? dev.Img),
                      }}
                      style={{height: 46, width: 46}}
                    />
                  }
                  title={dev.Name ?? '-'}
                  subtitle={
                    controlModule === 'Device'
                      ? `Mã số: ${dev.Code ?? ''}`
                      : `Số lượng: ${dev.Quantity} ${_product?.Unit ?? dev.Unit}`
                  }
                  trailing={
                    <View>
                      {checked ? (
                        <Winicon
                          src="fill/user interface/c-check"
                          size={16}
                          color={ColorThemes.light.success_main_color}
                        />
                      ) : null}
                    </View>
                  }
                />
              );
            })}
          </View>
        )}
      </ScrollView>
      {onlyView ? null : (
        <WScreenFooter
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingBottom: 16,
          }}>
          <AppButton
            title={'Bỏ chọn tất cả'}
            disabled={onlyView}
            backgroundColor={ColorThemes.light.neutral_main_background_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              setSelected([]);
            }}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
          />
          <AppButton
            title={selected.length ? `Đã chọn ${selected.length}` : 'Xong'}
            backgroundColor={ColorThemes.light.primary_main_color}
            disabled={onlyView}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              closePopup(ref);
              onSubmit(selected);
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      )}
    </SafeAreaView>
  );
});

const PopupActivityView = forwardRef(function PopupActivityView(
  data: {item: any; checkList: any; onlyView: any},
  ref: any,
) {
  const {item, checkList, onlyView} = data;
  const [activities, setActivities] = useState<any>({
    data: Array<any>(),
    totalCount: undefined,
  });
  const [customers, setCustomers] = useState<Array<any>>([]);
  const activityController = new DataController('Activity');
  const user = useSelectorCustomerState().data;
  const bgColor = useSelectorCustomerState().bgColor;

  const getData = async () => {
    const res = await activityController.getListSimple({
      page: 1,
      size: 1000,
      query: `@TaskId:{${item.Id}}`,
    });
    if (res.code === 200) {
      const customerIds = res.data
        .filter((e: any) => e.CustomerId)
        .map((e: any) => e.CustomerId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      if (customerIds.length) {
        const customerController = new DataController('Customer');
        const customerRes = await customerController.getByListId(customerIds);
        if (customerRes.code === 200)
          setCustomers(
            customerRes.data.map((e: any) => ({
              ...e,
              bgColor:
                user?.Id === e.Id ? bgColor : Ultis.generateDarkColorRgb(),
            })),
          );
      }
      setActivities({data: res.data, totalCount: res.totalCount});
    }
  };

  useEffect(() => {
    if (item) getData();
  }, [item]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          height: 35,
          paddingTop: 8,
        }}
        title={`Chi tiết tiến độ`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <ScrollView
        style={{
          flex: 1,
          height: '100%',
          paddingBottom: 45,
          paddingHorizontal: 16,
          paddingTop: 16,
        }}>
        {activities.totalCount === 0 ? (
          <EmptyPage title={`Nhà vệ sinh không có tiến độ nào`} />
        ) : (
          <View style={{flex: 1, paddingBottom: 75}}>
            {activities.data.map((act: any, i: number) => {
              const _customer = customers.find(e => e.Id === act.CustomerId);
              var startValue = undefined;
              var endValue = undefined;
              if (act.DateStart) startValue = new Date(act.DateStart);
              if (act.DateEnd) endValue = new Date(act.DateEnd);
              return (
                <ActivityTile
                  key={act.Id}
                  act={act}
                  customer={_customer}
                  checkList={checkList}
                />
              );
            })}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
});

const ActivityTile = ({act, customer, checkList}: any) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <ListTile
      style={{
        padding: 0,
        paddingVertical: 16,
        borderBottomColor: ColorThemes.light.neutral_main_border_color,
        borderBottomWidth: 1,
      }}
      onPress={() => {
        setIsOpen(!isOpen);
      }}
      leading={
        customer?.Img ? (
          <SkeletonImage
            source={{
              uri: customer.Img.startsWith('https')
                ? customer.Img
                : ConfigAPI.imgUrlId + customer?.Img,
            }}
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              objectFit: 'cover',
            }}
          />
        ) : (
          <View
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Ultis.generateDarkColorRgb(),
            }}>
            <Text
              style={{
                color: '#fff',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              {customer?.Name?.substring(0, 1)}
            </Text>
          </View>
        )
      }
      title={customer?.Name ?? '-'}
      titleStyle={{paddingBottom: 4, ...TypoSkin.label3}}
      subtitle={(() => {
        var startValue = undefined;
        var endValue = undefined;
        if (act.DateCreated) startValue = new Date(act.DateCreated);
        if (act.EndTime) endValue = new Date(act.EndTime);
        return startValue ? (
          <View
            style={{
              flex: 1,
              width: '100%',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
            }}>
            <Text
              style={{
                ...TypoSkin.buttonText4,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              Thời gian:
              {startValue
                ? `${Ultis.datetoString(startValue, startValue.getSeconds() === 1 ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy')} - `
                : ''}
              {endValue
                ? Ultis.datetoString(
                    endValue,
                    endValue.getSeconds() === 59
                      ? 'dd/mm/yyyy hh:mm'
                      : 'dd/mm/yyyy',
                  )
                : ''}
            </Text>
          </View>
        ) : null;
      })()}
      trailing={
        <Winicon
          src={`outline/arrows/${isOpen ? 'down' : 'right'}-arrow`}
          size={16}
        />
      }
      bottom={
        isOpen ? (
          <View style={{flex: 1, width: '100%', paddingVertical: 16, gap: 8}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                Đủ sức khỏe làm việc:
              </Text>
              <Winicon
                src="fill/layout/circle-check"
                size={18}
                color={ColorThemes.light.primary_main_color}
              />
            </View>
            <Text
              style={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              Chi tiết công việc:
            </Text>
            {[...checkList.dev, ...checkList.bio].map((e, i) => {
              const checked =
                act.DeviceId?.includes(e?.Id) ||
                act.BioProductId?.includes(e?.Id);
              return (
                <ListTile
                  key={i}
                  style={{padding: 0, paddingVertical: 4}}
                  leading={
                    <Winicon
                      src={
                        checked
                          ? 'outline/layout/circle-check'
                          : 'outline/layout/circle-half-dashed-check'
                      }
                      size={16}
                      color={
                        checked
                          ? ColorThemes.light.success_main_color
                          : ColorThemes.light.warning_main_color
                      }
                    />
                  }
                  title={e.Name}
                  subtitle={`Số lượng: ${e?.Quantity ?? ''} ${e?.Unit ?? 'chưa kết ca'}`}
                  trailing={
                    <HashTag
                      title={checked ? 'Hoàn thành' : 'Chưa hoàn thành'}
                      textStyles={{
                        color: checked
                          ? ColorThemes.light.success_main_color
                          : ColorThemes.light.warning_main_color,
                      }}
                      styles={{
                        ...TypoSkin.subtitle3,
                        backgroundColor: checked
                          ? ColorThemes.light.success_background
                          : ColorThemes.light.warning_background,
                      }}
                    />
                  }
                />
              );
            })}
          </View>
        ) : null
      }
    />
  );
};
