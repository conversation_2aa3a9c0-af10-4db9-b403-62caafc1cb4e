/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {View, Image, Pressable} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import type {ImageProps} from 'react-native';
import {Winicon} from '../component/export-component';

export interface SkeletonImageProps extends ImageProps {}

export const SkeletonImage: React.FC<SkeletonImageProps> = props => {
  const isRemoteImage = typeof props.source === 'object';

  const [isLoading, setIsLoading] = useState<boolean>(isRemoteImage);
  const [isError, setIsError] = useState<boolean>(false);

  useEffect(() => {
    if (isRemoteImage) {
      setIsError(false);
    }
  }, [isRemoteImage]);

  return (
    <View>
      <Pressable pointerEvents="none">
        <Image
          {...props}
          source={isError ? require('../assets/logo.png') : props.source}
          style={
            isLoading && props.style
              ? {
                  ...(typeof props.style === 'object' ? props.style : {}),
                  position: 'absolute',
                  opacity: 0,
                }
              : props.style
          }
          onLoadEnd={() => setIsLoading(false)}
          onError={() => setIsError(true)}
        />
      </Pressable>
      {isLoading && (
        <SkeletonPlaceholder backgroundColor="#e0e0e0" highlightColor="#f5f5f5">
          <View style={props.style} />
        </SkeletonPlaceholder>
      )}
    </View>
  );
};
