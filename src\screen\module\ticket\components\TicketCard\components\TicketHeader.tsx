import React from 'react';
import {Text, StyleSheet} from 'react-native';
import {SkeletonImage} from '../../../../../../project-component/skeleton-img';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import ConfigAPI from '../../../../../../config/configApi';

interface TicketHeaderProps {
  item: any;
  index: number;
  typeLabel?: string;
}

export const TicketHeader: React.FC<TicketHeaderProps> = ({
  item,
  index,
  typeLabel,
}) => {
  return (
    <>
      <SkeletonImage
        source={{uri: ConfigAPI.imgUrlId + item?.Img}}
        style={styles.leadingImage}
      />
      <Text style={styles.title}>{`${index + 1}. ${item?.Name ?? '-'}`}</Text>
      <Text style={styles.subtitle}><PERSON><PERSON><PERSON> yêu cầu: {typeLabel ?? '-'}</Text>
    </>
  );
};

const styles = StyleSheet.create({
  leadingImage: {
    width: 32,
    height: 32,
    borderRadius: 8,
  },
  title: {
    ...TypoSkin.title3,
  },
  subtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});
