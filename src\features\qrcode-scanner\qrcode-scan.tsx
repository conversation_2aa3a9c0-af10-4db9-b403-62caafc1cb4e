import React, {useEffect, useRef, useState} from 'react';

import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Dimensions,
  View,
  Pressable,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {
  FDialog,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../component/export-component';
import {closePopup} from '../../component/popup/popup';
import ScreenHeader from '../../screen/layout/header';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../router/router';
import {DataController} from '../../screen/base-controller';
import {ComponentStatus} from '../../component/component-status';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import RNQRGenerator from 'rn-qr-generator';
import FLoading from '../../component/Loading/FLoading';
import ImagePicker from 'react-native-image-crop-picker';
import {TypoSkin} from '../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';

export const PopupQrcodeScan = ({popupRef}: {popupRef: any}) => {
  const navigation = useNavigation<any>();
  const camera = useRef<Camera>(null);

  const [isTorchOn, setIsTorchOn] = useState<any>('off');
  const [isLoading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const dialogRef = React.useRef<any>(null);
  const device = useCameraDevice('back');
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: async (codes: any) => {
      handleQRCodeResult(codes);
    },
  });

  useEffect(() => {
    // exception case
    setRefresh(!refresh);
  }, [device, hasPermission]);

  useEffect(() => {
    const requestCameraPermission = async () => {
      const permission = await Camera.requestCameraPermission();
      // console.log("Camera.requestCameraPermission ", permission);
      setHasPermission(permission === 'granted');
    };

    requestCameraPermission();

    // if it is idle for 15 secs, it will be closed
    // setTimeout(() => {
    //     if (ref) closePopup(ref);
    // }, 15 * 1000);
  }, []);

  if (device == null || !hasPermission) {
    return (
      <View
        style={{
          flex: 1,
          position: 'absolute',
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text style={{backgroundColor: 'white'}}>
          Camera not available or not permitted
        </Text>
      </View>
    );
  }

  const handleQRCodeResult = async (codes: any) => {
    if (codes) {
      if (isLoading == false) {
        setLoading(true);
        const toiletController = new DataController('Toilet');
        let toiletId = JSON.parse(codes[0].value ?? codes)?.toiletId;
        if (toiletId) var item = await toiletController.getById(toiletId);
        if (item.code == 200) {
          closePopup(popupRef);
          if (item.data) {
            navigation.navigate(RootScreen.ResultScanQrcode, {
              toiletItem: item.data,
            });
          }

          setLoading(false);
        } else {
          closePopup(popupRef);

          showSnackbar({
            message: 'Đã có lỗi xảy ra',
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
        }
      }
    } else {
      showSnackbar({
        message: 'Dữ liệu quét không đúng',
        status: ComponentStatus.ERROR,
      });
      closePopup(popupRef);

      setLoading(false);
    }
  };

  // Function to pick image from gallery and scan QR code
  const pickImageAndScanQR = async () => {
    try {
      const image = await ImagePicker.openPicker({
        cropping: false,
        mediaType: 'photo',
        multiple: false,
      });
      if (image) {
        try {
          setLoading(true);
          console.log('Image object:', image);
          console.log('Original path:', image.path);

          let response = null;
          let lastError = null;
          let uri = image.sourceURL || image.path;
          try {
            response = await RNQRGenerator.detect({uri});
            console.log('QR detection response:', response);
          } catch (err) {
            console.log('Failed with URI:', uri, 'Error:', err);
            lastError = err;
          }

          if (response && response.values && response.values.length > 0) {
            console.log('QR scan result:', response.values[0]);
            handleQRCodeResult(response.values[0]);
            setLoading(false);
          } else {
            showDialog({
              ref: dialogRef,
              title: 'Không tìm thấy mã QR',
              content: 'Vui lòng thử lại với ảnh khác.',
              onSubmit: () => setLoading(false),
            });
            setLoading(false);
          }
        } catch (error) {
          console.error('QR scan error:', error);
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          showSnackbar({
            message:
              'Không thể đọc mã QR từ hình ảnh này. Lỗi: ' + errorMessage,
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    } catch (error) {
      if (error !== 'E_PICKER_CANCELLED') {
        console.error('Image picker error:', error);
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể chọn ảnh từ thư viện',
        });
      }
    }
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: '#fff',
      }}>
      <FDialog ref={dialogRef} />
      <FLoading visible={isLoading} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          flexDirection: 'row',
          paddingVertical: 4,
          paddingHorizontal: 8,
        }}
        title={`Quét Qrcode`}
        prefix={
          <View style={{flexDirection: 'row', gap: 4}}>
            {/* Flashlight button */}
            <TouchableOpacity
              onPress={() => setIsTorchOn(isTorchOn == 'on' ? 'off' : 'on')}
              style={{padding: 8, alignItems: 'center'}}>
              <Winicon
                src={
                  isTorchOn == 'on'
                    ? 'fill/buildings/flashlight'
                    : 'outline/buildings/flashlight'
                }
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => pickImageAndScanQR()}
              style={{padding: 8}}>
              <Winicon
                src="fill/editing/image"
                size={24}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          </View>
        }
        action={
          <TouchableOpacity
            onPress={() => {
              closePopup(popupRef);
            }}
            style={{padding: 8, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <View
        style={{
          flex: 1,
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Camera
          ref={camera}
          codeScanner={codeScanner}
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={true}
          torch={isTorchOn}
        />
      </View>
    </SafeAreaView>
  );
};
