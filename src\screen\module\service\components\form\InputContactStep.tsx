
import { KeyboardAvoidingView, Pressable, <PERSON><PERSON><PERSON><PERSON><PERSON>, Text, View } from "react-native";
import { FAddressPickerForm, TextFieldForm } from "../../../../../project-component/component-form";
import { ColorThemes } from "../../../../../assets/skin/colors";
import { <PERSON><PERSON><PERSON>kin } from "../../../../../assets/skin/typography";
import { Winicon } from "../../../../../component/export-component";
import { validatePhoneNumber, ValidateType } from "../../../../../utils/validate";
import { useSelectorCustomerState } from "../../../../../redux/hooks/hooks";
import { useEffect } from "react";

interface Props {
    methods?: any;
    forConsultant?: boolean;
    display?: string;
    editable?: boolean
}

export default function InputContactStep(props: Props) {
    const { methods, forConsultant = false, display = "flex" } = props;
    const { data } = useSelectorCustomerState()

    useEffect(() => {
        if (methods.watch("guest")) {
            methods.setValue("Name", methods.watch("guest").Name)
            methods.setValue("Mobile", methods.watch("guest").Mobile)
            if (methods.watch("guest").Address) methods.setValue("Address", methods.watch("guest").Address)
            if (methods.watch("guest").Lat) methods.setValue("Lat", methods.watch("guest").Lat)
            if (methods.watch("guest").Long) methods.setValue("Long", methods.watch("guest").Long)
        } else if (data) {
            methods.setValue("Name", data.Name)
            methods.setValue("Mobile", data.Mobile)
            // methods.setValue("Address", data.Address)
            // methods.setValue("Long", data.Long)
            // methods.setValue("Lat", data.Lat)
        }
    }, [data, methods.watch("guest")])

    return <ScrollView style={{ flex: 1, padding: 16, display: display === "flex" ? 'flex' : 'none' }}>
        <Pressable
            style={{
                borderColor: 'transparent',
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
                marginBottom: 16
            }}>
            <Text
                style={[
                    TypoSkin.heading5,
                    {
                        color: ColorThemes.light.neutral_text_title_color,
                        paddingBottom: 4,
                    },
                ]}>
                Thông tin liên hệ
            </Text>
            <Text
                style={[
                    TypoSkin.body3,
                    {
                        color: ColorThemes.light.neutral_text_body_color,
                        paddingBottom: 8,
                    },
                ]}>
                Chúng tôi sẽ dùng thông tin này để liên lạc lại
            </Text>
        </Pressable>
        <KeyboardAvoidingView style={{ gap: 16 }}>
            <TextFieldForm
                label="Họ và tên"
                textFieldStyle={{ padding: 16 }}
                style={{ width: '100%' }}
                disabled={data && !methods.watch("serviceData") || methods.watch("guest")?.Name}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Name"
            />
            <TextFieldForm
                control={methods.control}
                name='Mobile'
                label="Số điện thoại"
                disabled={data && !methods.watch("serviceData") || methods.watch("guest")?.Name}
                errors={methods.formState.errors}
                textFieldStyle={{ backgroundColor: ColorThemes.light.transparent, marginBottom: 6 }}
                register={methods.register}
                prefix={<View style={{ flexDirection: 'row', height: "100%", paddingHorizontal: 8, alignItems: 'center', justifyContent: 'center', gap: 8, backgroundColor: ColorThemes.light.neutral_main_background_color, borderRadius: 8 }}>
                    <Text style={{ ...TypoSkin.buttonText3, color: ColorThemes.light.neutral_text_title_color }}>+84</Text>
                    <Winicon src='outline/arrows/down-arrow' size={16} />
                </View>}
                type="number-pad"
                onBlur={async (ev: any) => {
                    var mobile = ev.trim()
                    // Check if the number doesn't already start with 0 or +84
                    if (!/^(\+84|0)/.test(mobile)) {
                        mobile = '0' + mobile; // Add 0 at the beginning
                    }
                    const val = validatePhoneNumber(mobile)
                    if (val) methods.clearErrors("Mobile"); else methods.setError("Mobile", { message: "Số điện thoại không hợp lệ" });
                }}
            />
            <View pointerEvents={props.editable != undefined && !props.editable ? "none" : "auto"}>
                {methods.watch("guest")?.Address ?
                    <FAddressPickerForm
                        control={methods.control}
                        errors={methods.formState.errors}
                        name="Address"
                        label="Địa chỉ"
                        disabled
                        placeholder="Nhập địa chỉ của bạn"
                        placeName={""}
                        onChange={(value) => {
                            console.log('==============AddressAddress======================');
                            console.log(value.formatted_address);
                            console.log('====================================');
                            methods.setValue('Long', value.geometry.location.lng)
                            methods.setValue('Lat', value.geometry.location.lat)
                            methods.setValue('Address', value.formatted_address)
                            return value.formatted_address
                        }}
                    />
                    : <FAddressPickerForm
                        control={methods.control}
                        errors={methods.formState.errors}
                        disabled={props.editable != undefined ? !props.editable : false}
                        name="Address"
                        label="Địa chỉ"
                        placeholder="Nhập địa chỉ của bạn"
                        placeName={""}
                        onChange={(value) => {
                            console.log('==============AddressAddress======================');
                            console.log(value.formatted_address);
                            console.log('====================================');
                            methods.setValue('Long', value.geometry.location.lng)
                            methods.setValue('Lat', value.geometry.location.lat)
                            methods.setValue('Address', value.formatted_address)
                            return value.formatted_address
                        }}
                    />}
            </View>
        </KeyboardAvoidingView>
    </ScrollView>
}
