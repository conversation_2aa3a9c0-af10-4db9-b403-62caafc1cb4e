import {
  ActivityIndicator,
  Dimensions,
  Platform,
  Pressable,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import WebView from 'react-native-webview';
import {useEffect, useMemo, useRef, useState} from 'react';
import {
  AddendumType,
  ContractStatus,
  ContractType,
  signTickImg,
  ToiletServiceStatus,
} from '../../../service/components/da';
import {DataController} from '../../../../base-controller';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import {CustomerRole} from '../../../../../redux/reducers/user/da';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {
  FDialog,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {FPopup, showPopup} from '../../../../../component/popup/popup';
import {PopupCheckOtp} from '../../../../../project-component/popup-otp';
import EmptyPage from '../../../../../project-component/empty-page';
import {Fselect1Form} from '../../../../../project-component/component-form';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {mapContractData} from './PrintForm';
import ConfigAPI from '../../../../../config/configApi';
import {popupReject} from './QuoteTable';
import {ButtonViewRejectReason} from '../popup/DialogCustomize';
import {CreateToiletOperationFor12Mounth} from '../Ulits';
import CetificateAchievemenDa from 'screen/pages/CetificateAchievementPage/CetificateAchievemenDa';

export default function ContractTab({
  data,
  serviceData,
  setServiceData,
  onSubmit,
  methods,
  customer,
}: any) {
  if (!data || !data?.length) return null;
  // const [listTemplate, setListTemplate] = useState<any[]>([]);
  const quoteTemplateId = '7bf6069a5286414ca50b850c72e969dc';
  const templateAddendumId = '639e03b574ec4b0ca6dd4a55408e8884';
  const [selectedContent, setSelectedContent] = useState<any>();
  const [addendums, setAddendums] = useState<Array<any>>([]);
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
  const userRole = useSelectorCustomerState().role;
  const [contract, setContract] = useState<any>();
  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();
  const [isLoading, setLoading] = useState(false);
  const cateServices = useMemo(
    () => methods.watch('cateServices'),
    [methods.watch('cateServices')],
  );
  const regexGetVariables = /\{\{(\w+)\}\}/g;
  const isEditable = useMemo(() => {
    if (
      selectedContent?.Status === ContractStatus.init ||
      selectedContent?.Status === ContractStatus.reject ||
      (!selectedContent &&
        (contract?.Status === ContractStatus.init ||
          contract?.Status === ContractStatus.reject))
    ) {
      if (user?.Id === serviceData?.CustomerId) return true;
      else if (
        serviceData?.CustomerId === owner?.Id &&
        userRole?.Role?.includes(CustomerRole.Coordinator)
      ) {
        return true;
      }
    }
    return false;
  }, [data, serviceData, user, userRole, contract, selectedContent]);

  useEffect(() => {
    if (serviceData) getData();
  }, [serviceData]);

  const getData = async () => {
    if (serviceData.Status === ToiletServiceStatus.contract) {
      setAddendums([]);
      setTimeout(() => {
        methods.setValue('contractAddendumId', 'contract');
      }, 100);
    } else {
      getAddendums();
    }
    const controller = new DataController('Contract');
    const res = await controller.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${ContractType.contract} ${ContractType.contract}]`,
    });
    let tmpContract = res.data[0];
    setContract(tmpContract);
  };

  const getAddendums = async () => {
    const controller = new DataController('Addendum');
    const res = await controller.getListSimple({
      page: 1,
      size: 100,
      query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${AddendumType.contract} ${AddendumType.contract}]`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (res.code === 200 && res.data) {
      const tmp = res.data;
      setAddendums(tmp);
      setSelectedContent(tmp[0]);

      // methods.setValue("contractAddendumId", tmp[0]?.Id)
    }
  };

  useEffect(() => {
    if (selectedContent)
      methods.setValue('contractAddendumId', selectedContent?.Id);
    else methods.setValue('contractAddendumId', 'contract');
  }, [selectedContent]);

  const contractHTML = useMemo(
    () => (selectedContent ?? contract)?.Content ?? '',
    [contract, selectedContent],
  );

  const rejectReasons = useMemo(
    () =>
      (selectedContent ?? contract)?.RejectReason
        ? JSON.parse((selectedContent ?? contract).RejectReason)
        : [],
    [selectedContent, contract],
  );

  const submitOtp = async (onSuccess: any) => {
    // check OTP
    if (__DEV__) return onSuccess();
    if (!user?.Mobile) return;
    showPopup({
      ref: popupRef,
      children: (
        <PopupCheckOtp
          ref={popupRef}
          phone={user?.Mobile}
          onSuccess={onSuccess}
          isLoading={isLoading}
          setLoading={setLoading}
        />
      ),
    });
  };

  const handleUserApprove = async () => {
    //======= Update trạng thái đơn khi GUEST xác nhận ký duyệt và OTP thành công =========
    const _dateSign = Date.now();
    if (selectedContent) {
      const controller = new DataController('Addendum');
      const updateAddendum = {
        ...selectedContent,
        Status: ContractStatus.guestSigned,
        ToiletId: data.Id,
        DateSign: _dateSign,
        Content: contractHTML.replace(regexGetVariables, (m: any, key: any) => {
          switch (key) {
            case 'GuestSign':
              return signTickImg;
            case 'GuestSignName':
              return user?.Name;
            case 'DateSign':
              return Ultis.datetoString(new Date());
            default:
              return m ?? '';
          }
        }),
      };
      const res = await controller.edit([updateAddendum]);
      if (res.code === 200) {
        setAddendums(adde =>
          adde.map(e => (e.Id === updateAddendum.Id ? updateAddendum : e)),
        );
        setSelectedContent(updateAddendum);
        showSnackbar({
          message: 'Ký và gửi phụ lục hợp đồng thành công',
          status: ComponentStatus.SUCCSESS,
        });
        await onSubmit();
        await CreateToiletOperationFor12Mounth(data, serviceData);
      }
    } else {
      const controller = new DataController('Contract');
      const res = await controller.edit([
        {
          ...contract,
          Status: ContractStatus.guestSigned,
          ToiletId: data.Id,
          DateSign: _dateSign,
          Content: contractHTML.replace(
            regexGetVariables,
            (m: any, key: any) => {
              switch (key) {
                case 'GuestSign':
                  return signTickImg;
                case 'GuestSignName':
                  return user?.Name;
                case 'DateSign':
                  return Ultis.datetoString(new Date());
                default:
                  return m ?? '';
              }
            },
          ),
        },
      ]);
      if (res.code === 200) {
        showSnackbar({
          message: 'Ký hợp đồng thành công',
          status: ComponentStatus.SUCCSESS,
        });
        setTimeout(() => {
          methods.setValue('contractAddendumId', 'contract');
        }, 100);
        await onSubmit();
        await CreateToiletOperationFor12Mounth(data, serviceData);
      }
    }
  };

  const sendPartnerContract = async () => {
    if (selectedContent) {
      const controller = new DataController('Addendum');
      const content = contractHTML.replace(
        regexGetVariables,
        (m: string, key: string) => {
          switch (key) {
            case 'KTXSign':
              return signTickImg;
            case 'PartnerSign':
              return signTickImg;
            case 'PartnerSignName':
              return user?.Name;
            case 'PartnerDateSign':
              return Ultis.datetoString(new Date());
            default:
              return m ?? '';
          }
        },
      );
      const updateAddendum = {
        ...selectedContent,
        Status: ContractStatus.partnerSigned,
        DateSign: Date.now(),
        Content: content,
      };
      const res = await controller.edit([updateAddendum]);
      if (res.code === 200) {
        setAddendums(adde =>
          adde.map(e => (e.Id === updateAddendum.Id ? updateAddendum : e)),
        );

        setSelectedContent(updateAddendum);
      }
      await CetificateAchievemenDa.getLogByToiletId(
        serviceData?.ToiletId.split(','),
        `Đơn hàng NetZero đã được ký phụ lục hợp đồng thành công`,
      );
      showSnackbar({
        message: 'Ký và gửi phụ lục hợp đồng thành công',
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      const controller = new DataController('Contract');
      var reMapContractData = undefined;
      if (contract.Status === ContractStatus.reject)
        reMapContractData = await mapContractData({
          methods: methods,
          cateServices: cateServices,
          customer: customer,
          data: data,
          servicesData: serviceData,
          partnerData: {data: user, company: company},
          ktxgroup: ktxgroup,
          sign: true,
        });

      const content = (reMapContractData ?? contractHTML).replace(
        regexGetVariables,
        (m: string, key: string) => {
          switch (key) {
            case 'KTXSign':
              return signTickImg;
            case 'PartnerSign':
              return signTickImg;
            case 'PartnerSignName':
              return user?.Name;
            case 'PartnerDateSign':
              return Ultis.datetoString(new Date());
            default:
              return m ?? '';
          }
        },
      );

      const res = await controller.edit([
        {
          ...contract,
          Name:
            'HỢP ĐỒNG HỢP TÁC TRIỂN KHAI ' +
            cateServices
              ?.find((e: any) => e.Id === serviceData.CateServicesId)
              ?.Name.toUpperCase(),
          Status: ContractStatus.partnerSigned,
          DateSign: Date.now(),
          Content: content,
        },
      ]);
      if (res.code !== 200)
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
      const servicesController = new DataController('ToiletServices');
      servicesController.edit([
        {...serviceData, Status: ToiletServiceStatus.sendCompleteContract},
      ]);
      setServiceData({
        ...serviceData,
        Status: ToiletServiceStatus.sendCompleteContract,
      });
      await CetificateAchievemenDa.getLogByToiletId(
        serviceData?.ToiletId.split(','),
        `Đơn hàng NetZero đã được ký hợp đồng thành công`,
      );
      showSnackbar({
        message: 'Ký và gửi hợp đồng thành công',
        status: ComponentStatus.SUCCSESS,
      });
    }
    //===================end======================
  };

  const handleUserReject = async () => {
    popupReject({
      ref: dialogRef,
      title: `Bạn chắc chắn muốn từ chối ${selectedContent ? 'phụ lục' : 'hợp đồng'} này?`,
      methods: methods,
      onSubmit: async (ev: any) => {
        const newRejectReason = {DateCreated: Date.now(), Content: ev};
        if (selectedContent) {
          const controller = new DataController('Addendum');
          const updateAddendum = {
            ...selectedContent,
            Status: ContractStatus.reject,
            RejectReason: JSON.stringify([...rejectReasons, newRejectReason]),
          };
          const res = await controller.edit([updateAddendum]);
          if (res.code === 200) {
            setAddendums(adde =>
              adde.map(e => (e.Id === updateAddendum.Id ? updateAddendum : e)),
            );
            setSelectedContent(updateAddendum);
          }
        } else {
          const controller = new DataController('Contract');
          const res = await controller.edit([
            {
              ...contract,
              Status: ContractStatus.reject,
              RejectReason: JSON.stringify([...rejectReasons, newRejectReason]),
            },
          ]);
          if (res.code === 200) {
            const controller = new DataController('ToiletServices');
            controller.edit([
              {...serviceData, Status: ToiletServiceStatus.contract},
            ]);
            setServiceData({
              ...serviceData,
              Status: ToiletServiceStatus.contract,
            });
          }
        }
        await CetificateAchievemenDa.getLogByToiletId(
          serviceData?.ToiletId.split(','),
          `Đơn hàng NetZero đã bị từ chối ký hợp đồng `,
        );
      },
    });
  };

  const {width, height} = Dimensions.get('window');

  return !isEditable &&
    (contract?.Status === ContractStatus.init ||
      contract?.Status === ContractStatus.reject) ? (
    <View style={{flex: 1, justifyContent: 'center', paddingHorizontal: 24}}>
      <EmptyPage
        title={
          'Chúng tôi đang chuẩn bị Hợp đồng và sẽ gửi tới bạn trong thời gian sớm nhất'
        }
      />
    </View>
  ) : (
    <View style={styles.container}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      <View style={styles.container}>
        {serviceData?.Status >= ToiletServiceStatus.design ? (
          <View
            style={{
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 8,
              gap: 8,
            }}>
            {(user?.Id === serviceData?.CustomerId ||
              (serviceData.CustomerId === owner?.Id &&
                userRole?.Role?.includes(CustomerRole.Coordinator))) &&
            addendums?.every(
              e =>
                e.Status === ContractStatus.guestSigned &&
                serviceData?.Status < ToiletServiceStatus.sendCompleteBuild,
            ) ? (
              <AppButton
                backgroundColor={ColorThemes.light.transparent}
                borderColor={ColorThemes.light.neutral_bolder_border_color}
                containerStyle={{
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                prefixIcon={'outline/user interface/e-add'}
                prefixIconSize={16}
                title={'Tạo phụ lục hợp đồng'}
                textColor={ColorThemes.light.neutral_text_body_color}
                onPress={async () => {
                  const addendumController = new DataController('Addendum');
                  const newAddendum = {
                    Id: randomGID(),
                    Name: `PHỤ LỤC HỢP ĐỒNG ${addendums.length + 2}`,
                    DateCreated: Date.now(),
                    Type: AddendumType.contract,
                    Sort: 1,
                    Status: ContractStatus.init,
                    ToiletServicesId: serviceData.Id,
                    DocumentsId: templateAddendumId,
                    ContractId: contract?.Id,
                  };
                  const newQuoteAddendum = {
                    Id: randomGID(),
                    Name: `PHỤ LỤC BÁO GIÁ ${addendums.length + 2}`,
                    DateCreated: Date.now(),
                    Type: AddendumType.quote,
                    Sort: 1,
                    Status: ContractStatus.init,
                    ToiletServicesId: serviceData.Id,
                    DocumentsId: quoteTemplateId,
                  };
                  const res = await addendumController.add([
                    newAddendum,
                    newQuoteAddendum,
                  ]);
                  if (res.code === 200) {
                    setAddendums(ad => [...ad, newAddendum]);
                    setSelectedContent(newAddendum);
                  }
                }}
              />
            ) : null}
            <Fselect1Form
              control={methods.control}
              errors={methods.formState.errors}
              name="contractAddendumId"
              onChange={async value => {
                if (value.id === 'contract') {
                  setSelectedContent(undefined);
                  methods.setValue('contractAddendumId', value.id);
                  return;
                }
                if (value) {
                  setSelectedContent(
                    addendums.find((e: any) => e.Id === value.id),
                  );
                  methods.setValue('contractAddendumId', value.id);
                }
              }}
              options={[
                {
                  id: 'contract',
                  name: (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 8,
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}>
                      <Winicon
                        src="fill/layout/circle-check"
                        color={ColorThemes.light.success_main_color}
                        size={16}
                      />
                      <Text
                        numberOfLines={1}
                        style={{
                          ...TypoSkin.heading7,
                          color: ColorThemes.light.neutral_text_title_color,
                        }}>
                        {contract?.Name ?? '-'}
                      </Text>
                    </View>
                  ),
                },
                ...addendums?.map((e: any) => {
                  const isSigned = e.Status === ContractStatus.guestSigned;
                  return {
                    id: e.Id,
                    name: (
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                        }}>
                        {isSigned ? (
                          <Winicon
                            src="fill/layout/circle-check"
                            color={ColorThemes.light.success_main_color}
                            size={16}
                          />
                        ) : null}
                        <Text
                          numberOfLines={1}
                          style={{
                            ...TypoSkin.heading7,
                            color: isSigned
                              ? ColorThemes.light.success_main_color
                              : ColorThemes.light.neutral_text_title_color,
                          }}>
                          {e?.Name ?? '-'}
                        </Text>
                      </View>
                    ),
                  };
                }),
              ]}
            />
          </View>
        ) : null}

        {selectedContent &&
        (!selectedContent.Content ||
          (!isEditable &&
            (selectedContent?.Status === ContractStatus.init ||
              selectedContent?.Status === ContractStatus.reject))) ? (
          <EmptyPage
            title={`Đơn hàng đang chờ đối tác gửi ${selectedContent?.Name}`}
            subtitle={
              !selectedContent.Content
                ? 'Quay lại bước báo giá để biết thêm thông tin chi tiết'
                : ''
            }
          />
        ) : (
          <View style={{flex: 1}}>
            {rejectReasons.length > 0 ? (
              <View style={{width: '100%'}}>
                <ButtonViewRejectReason
                  customers={[customer.data]}
                  rejectReasons={rejectReasons}
                />
              </View>
            ) : null}
            <ViewWeb selectedContent={selectedContent} contract={contract} />
            {user?.Id === data[0]?.CustomerId &&
            (selectedContent ?? contract)?.Status ===
              ContractStatus.partnerSigned ? (
              <SafeAreaView style={styles.bottomButtons}>
                <TouchableOpacity
                  style={{
                    ...styles.button,
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                  }}
                  onPress={handleUserReject}>
                  <Text
                    style={{
                      ...styles.buttonText,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    Từ chối
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.button}
                  onPress={() => submitOtp(handleUserApprove)}>
                  <Text style={styles.buttonText}>
                    {selectedContent ? 'Ký phụ lục' : 'Ký hợp đồng'}
                  </Text>
                </TouchableOpacity>
              </SafeAreaView>
            ) : null}
            {isEditable &&
            ((selectedContent ?? contract)?.Status === ContractStatus.init ||
              (selectedContent ?? contract)?.Status ===
                ContractStatus.reject) ? (
              <View style={styles.bottomButtons}>
                <TouchableOpacity
                  style={styles.button}
                  onPress={() => submitOtp(sendPartnerContract)}>
                  <Text style={styles.buttonText}>Ký và Gửi</Text>
                </TouchableOpacity>
              </View>
            ) : null}
          </View>
        )}
      </View>
    </View>
  );
}

const ViewWeb = ({selectedContent, contract}: any) => {
  const {width, height} = Dimensions.get('window');

  return (
    <Pressable
      style={{
        flex: 1,
        height: Dimensions.get('screen').height * 10,
      }}>
      {(selectedContent?.Id ?? contract?.Id) ? (
        <WebView
          key={`${selectedContent?.Id ?? contract?.Id} ${selectedContent?.Status || contract?.Status}`}
          renderError={() => <EmptyPage />}
          startInLoadingState
          onLoadEnd={() => {
            console.log('WebView contract finished loading');
          }}
          textZoom={100}
          bounces={false}
          nestedScrollEnabled
          limitsNavigationsToAppBoundDomains
          renderLoading={() => (
            <ActivityIndicator
              style={{
                backgroundColor: 'transparent',
                position: 'absolute',
                left: 0,
                right: 0,
                top: height / 3,
                zIndex: 9,
              }}
              color={ColorThemes.light.primary_main_color}
              size="large"
            />
          )}
          style={{
            height: Dimensions.get('screen').height * 10,
            width: width,
            flex: 1,
            paddingBottom: 25,
          }}
          injectedJavaScript='document.body.querySelector(".innerhtml-view").style.paddingBottom = "100px";'
          originWhitelist={['*']}
          javaScriptEnabled={true}
          source={{
            uri: `${ConfigAPI.urlWeb}contract-web-view?type=${selectedContent ? 'addendum' : 'contract'}&id=${selectedContent?.Id ?? contract?.Id}`,
          }}
        />
      ) : null}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
    width: '100%',
    marginTop: 10,
  },
  bottomButtons: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    height: 60,
    gap: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
