// import { ActivityIndicator, Alert, Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
// import { authorize, refresh } from 'react-native-app-auth';
// import { ColorSkin } from '../../../assets/skin/colors';
// import { TypoSkin } from '../../../assets/skin/typography';
// import AppSvg from '../../../component/AppSvg';
// import { AppIcons } from '../../../component/AppSvg/AppIcons';
// import { getAuth, OAuthProvider, signInWithPopup } from "firebase/auth";
// import auth from '@react-native-firebase/auth';
// import firebase from '@react-native-firebase/app';
// import { useEffect } from 'react';

// export default function MicrosoftSignIn(props: any) {

//     const { onAuthSuccess, onLoading, isLoading } = props;

//     async function signIn() {
//         // try {
//         //     const provider = new auth.OAuthProvider('microsoft.com'); // Use Firebase Microsoft OAuth Provider

//         //     // Sign in the user with the OAuth provider
//         //     const result = await auth().signInWithRedirect(provider);

//         //     console.log('User Info:', result.user); // User object
//         //     console.log('Access Token:', result); // Microsoft access token

//         //     Alert.alert(`Welcome, ${result.user.displayName}!`);
//         // } catch (error) {
//         //     console.error('Microsoft Sign-In Error:', error);
//         //     Alert.alert('Authentication failed. Please try again.');
//         // }
//         const config = {
//             serviceConfiguration: {
//                 authorizationEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
//                 tokenEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
//             },
//             clientId: '404b98f1-5be3-405f-986f-47ea251e3133',
//             redirectUrl: 'https://rncore-noti.firebaseapp.com/__/auth/handler',
//             scopes: ['openid', 'profile', 'email', 'offline_access'],
//         };

//         // Log in to get an authentication token
//         const authState = await authorize(config);

//         console.log('===============authState=====================');
//         console.log(authState);
//         console.log('====================================');

//         // Refresh token
//         const refreshedState = await refresh(config, {
//             refreshToken: authState.refreshToken,
//         });
//     }

//     return <TouchableOpacity onPress={signIn} style={styles.TouchStyle}>
//         {isLoading ? (
//             <View style={styles.TouchStyle}>
//                 <View style={styles.loading}>
//                     <ActivityIndicator size="large" />
//                 </View>
//             </View>

//         ) : (
//             <View style={styles.TouchStyle}>
//                 <AppSvg SvgSrc={AppIcons.iconThumb_up} size={20} />
//                 <Text
//                     style={[
//                         TypoSkin.buttonText1,
//                         { color: ColorSkin.body, fontSize: 18, paddingLeft: 8 },
//                     ]}>
//                     Đăng nhập với Microsoft
//                 </Text>
//             </View>
//         )}
//     </TouchableOpacity>
// }


// const styles = StyleSheet.create({
//     TouchStyle: {
//         flexDirection: 'row',
//         width: '100%',
//         height: 44,
//         borderRadius: 25,
//         borderWidth: 1,
//         borderColor: ColorSkin.border1,
//         alignItems: 'center',
//         justifyContent: 'center',
//     },
//     loading: {
//         alignItems: 'center',
//         justifyContent: 'center',
//     },
// });