import React, {useState, useRef} from 'react';
import {View, StyleSheet} from 'react-native';
import {ColorThemes} from 'assets/skin/colors';
import {FPopup, showPopup} from 'component/popup/popup';
import AppButton from 'component/button';
import {TicketType} from 'types/ticketType';

// Import components
import TabSelector from './components/TabSelector';
import MyTicketComponent, {
  MyTicketComponentRef,
} from '../MyTicketComponent/MyTicketComponent';
import AddTicketPopup from './components/AddTicketPopup';
import AssignTicketComponent from '../AssignTicketComponent';

export default function MyTicketList() {
  const popupRef = useRef<any>();
  const myTicketComponentRef = useRef<MyTicketComponentRef>(null);

  const [searchValue, setSearchValue] = useState('');
  const [tab, setTab] = useState(0);
  const [isRefreshing, setRefreshing] = useState(false);

  const handleAddTicket = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <AddTicketPopup
          type={undefined}
          toiletServicesId={undefined}
          parentPopupRef={popupRef}
          onDone={(newTicket: TicketType) => {
            // Switch to My Tickets tab
            setTab(1);
            // Trigger refresh of MyTicketComponent to show the new ticket
            if (myTicketComponentRef.current?.refreshTickets) {
              myTicketComponentRef.current.refreshTickets();
            }
          }}
        />
      ),
    });
  };

  return (
    <View style={styles.container}>
      <FPopup ref={popupRef} />

      <TabSelector tab={tab} setTab={setTab} />
      {tab === 0 ? (
        <AssignTicketComponent />
      ) : (
        <View>
          <AppButton
            title="Tạo ticket"
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={styles.addButton}
            onPress={handleAddTicket}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
          <MyTicketComponent
            ref={myTicketComponentRef}
            setResult={() => {}} // This will be handled by the hook
            searchValue={searchValue}
            setRefreshing={setRefreshing}
            isRefreshing={isRefreshing}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    width: '100%',
  },
  headerBottom: {
    paddingBottom: 16,
  },
  footer: {
    paddingHorizontal: 16,
  },
  addButton: {
    width: 100,
    borderRadius: 10,
    paddingHorizontal: 12,
    marginHorizontal: 16,
    alignSelf: 'flex-end',
  },
});
