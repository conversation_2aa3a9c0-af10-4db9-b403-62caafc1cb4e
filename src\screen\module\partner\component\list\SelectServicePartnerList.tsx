import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {RegisterPartnerFormStyles} from '../styles/RegisterPartnerFormStyles';
import AllServices from '../../../service/services';
import PagerView from 'react-native-pager-view';

const SelectServicePartnerList: React.FC<{
  dataCateService: any[];
  setDataCateService: (value: any[]) => void;
  setCurrentStep: (value: number) => void;
  currentStep: number;
  pagerRef: React.RefObject<PagerView>;
  changeStep2: () => void;
}> = ({
  dataCateService,
  setDataCateService,
  setCurrentStep,
  currentStep,
  pagerRef,
  changeStep2,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.titleText}>L<PERSON>a chọn dịch vụ cung cấp</Text>
        <View style={styles.servicesWrapper}>
          <AllServices
            selectedCate={dataCateService}
            setSelectedCate={setDataCateService}
            createService={true}
          />
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={RegisterPartnerFormStyles.buyButtonInputShopInfo}
          onPress={() => changeStep2()}>
          <Text style={RegisterPartnerFormStyles.buyActionButtonInputShopInfo}>
            Bước tiếp
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  titleText: {
    color: ColorThemes.light.neutral_text_title_color,
    ...TypoSkin.title2,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 16,
    textAlign: 'left',
    paddingLeft: 16,
  },
  servicesWrapper: {
    flex: 1,
    width: '100%',
  },
  buttonContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  title: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
});

export default SelectServicePartnerList;
