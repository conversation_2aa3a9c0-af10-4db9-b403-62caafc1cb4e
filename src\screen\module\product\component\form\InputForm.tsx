import {Controller} from 'react-hook-form';
import {Text, TextInput, View} from 'react-native';
import {CreateInputFeildProps} from '../types';
import {CreatePartnerProductFormStyles} from '../styles/CreatePartnerProductFormStyles';

export const InputForm = (props: CreateInputFeildProps) => {
  return (
    <>
      <View style={CreatePartnerProductFormStyles.section}>
        <View style={CreatePartnerProductFormStyles.textField}>
          <Text style={CreatePartnerProductFormStyles.label}>
            {props.nameFeild} *
          </Text>
          {props.maxlength ? (
            <Text style={CreatePartnerProductFormStyles.limit}>
              {props.checkLengthText}/{props.maxlength}
            </Text>
          ) : null}
        </View>
        <Controller
          control={props.control}
          name={props.name}
          rules={{
            required: props.rule,
          }}
          render={({field: {value, onChange}}) => {
            const textStyle = props.large
              ? CreatePartnerProductFormStyles.textHeightLarge
              : CreatePartnerProductFormStyles.textHeight;

            return props.maxlength ? (
              <TextInput
                style={[textStyle, {color: '#000000'}]}
                placeholder={props.placeholder}
                placeholderTextColor="#DDDDDD"
                maxLength={props.maxlength}
                value={value}
                onChange={e => onChange(e.nativeEvent.text)}
                multiline={props.mutiline}
              />
            ) : (
              <TextInput
                style={[textStyle, {color: '#000000'}]}
                placeholder={props.placeholder}
                placeholderTextColor="#DDDDDD"
                value={value}
                onChange={e => onChange(e.nativeEvent.text)}
                multiline={props.mutiline}
              />
            );
          }}
        />
      </View>
    </>
  );
};
