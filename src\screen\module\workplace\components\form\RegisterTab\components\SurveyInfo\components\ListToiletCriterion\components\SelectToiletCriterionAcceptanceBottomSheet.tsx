import {useState, useEffect, useMemo} from 'react';
import {
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {CustomBottomSheet} from 'project-component/form/DateRangePicker/CustomBottomSheet';
import {CateCriterionItem} from 'types/cateCriteriaType';
import {ToiletCriterionSurveyTask} from 'types/toiletCriterionSurveyTask';
import {randomGID} from 'utils/Utils';
import {SurveySection} from 'screen/pages/CreateSurveyStepPage/components/ReviewToilet/components';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import Checkbox from 'screen/pages/CreateSurveyStepPage/components/ReviewToilet/components/Checkbox';
import ListToiletCriterionDA from '../da';
import {ToiletCriterion} from 'types/toiletCriterionType';
import {CateCriterion} from 'types/cateCriterionType';

interface SelectToiletCriterionBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  cateCriterionData: CateCriterionItem[];
  toiletId: string;
  toiletName: string;
  toiletCriterion?: ToiletCriterion;
  toiletServiceId: string;
  cateCriterion: CateCriterion;
  cateCriterionId?: string;
  onConfirmSuccess?: () => void;
  toiletSelected?: any[]; // Danh sách toilet được chọn để xử lý "tất cả"
  toiletCriterions?: any[]; // Danh sách toilet criterions hiện tại
  markToiletAsUpdated?: (toiletIds: string[]) => void;
  disabled?: boolean;
  selectedToilet?: any;
}

export default function SelectToiletCriterionAcceptanceBottomSheet({
  visible,
  onClose,
  disabled = false,
  cateCriterionData,
  cateCriterion,
  toiletName,
  toiletCriterion,
  onConfirmSuccess,
  selectedToilet,
}: SelectToiletCriterionBottomSheetProps) {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const listToiletCriterionDA = useMemo(() => new ListToiletCriterionDA(), []);

  // Tính toán tất cả các criterion IDs
  const allCriterionIds = useMemo(() => {
    const ids: string[] = [];
    cateCriterionData.forEach(category => {
      if (category.Criterions) {
        category.Criterions.forEach(criterion => {
          ids.push(criterion.Id);
        });
      }
    });
    return ids;
  }, [cateCriterionData]);

  // Kiểm tra xem có phải tất cả đều được chọn không
  const isAllSelected = useMemo(() => {
    return (
      allCriterionIds.length > 0 &&
      allCriterionIds.every(id => checkedItems.includes(id))
    );
  }, [allCriterionIds, checkedItems]);

  // Hàm toggle chọn tất cả
  const toggleSelectAll = () => {
    if (isAllSelected) {
      setCheckedItems([]);
    } else {
      setCheckedItems([...allCriterionIds]);
    }
  };
  const handleConfirm = async () => {
    try {
      setLoading(true);

      let res = await listToiletCriterionDA.updateToiletCriterion([
        {
          ...selectedToilet,
          CriterionId: checkedItems.join(','),
          Status: 3,
        },
      ]);
      if (res.code === 200) {
        const resCertificate =
          await listToiletCriterionDA.createNewToiletCertificate([
            {
              Id: randomGID(),
              DateCreated: new Date().getTime(),
              Name: `Chứng nhận nghiệm thu NVS ${selectedToilet.Name} đã đạt tiêu chí ${cateCriterion.Name}`,
              ToiletId: selectedToilet.ToiletId,
              CateCriterionId: cateCriterion.Id,
              Img: 'https://dev.wini.vn/uploads//2025/8/Cetificate.png',
            },
          ]);
        if (resCertificate.code === 200) {
          showSnackbar({
            message: 'Cập nhật nghiệm thu và cấp chứng chỉ thành công',
            status: ComponentStatus.SUCCSESS,
          });
          onConfirmSuccess?.();
          setLoading(false);
          onClose();
        } else {
          showSnackbar({
            message: 'Cập nhật nghiệm thu thành công, cấp chứng chỉ thất bại',
            status: ComponentStatus.ERROR,
          });
          onConfirmSuccess?.();
          setLoading(false);
          onClose();
        }
      } else {
        setLoading(false);
        showSnackbar({
          message: 'Cập nhật nghiệm thu thất bại',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error saving toilet criterion:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi lưu tiêu chí',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleCheck = (itemId: string) => {
    const index = checkedItems.indexOf(itemId);
    if (index === -1) {
      setCheckedItems(prev => [...prev, itemId]);
    } else {
      setCheckedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  useEffect(() => {
    if (
      selectedToilet &&
      typeof selectedToilet.CriterionId === 'string' &&
      selectedToilet.CriterionId.trim().length > 0
    ) {
      const parsed = selectedToilet.CriterionId.split(',')
        .map((s: string) => s.trim())
        .filter(Boolean);
      // Chỉ bật những checkbox có trong danh sách tiêu chí hiện có
      const validIds = parsed.filter((id: string) =>
        allCriterionIds.includes(id),
      );
      setCheckedItems(validIds);
    } else {
      // Không có dữ liệu -> bỏ chọn tất cả
      setCheckedItems([]);
    }
  }, [selectedToilet, allCriterionIds]);

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title={`Chọn tiêu chí`}
      subTitle={`${toiletName}`}
      onCancel={onClose}
      onConfirm={handleConfirm}
      cancelText="Hủy"
      isShowConfirm={!disabled}
      confirmText={loading ? 'Đang lưu...' : 'Xác nhận'}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Checkbox chọn tất cả */}
        {allCriterionIds.length > 0 && (
          <View style={styles.selectAllContainer}>
            <TouchableOpacity
              style={styles.selectAllItem}
              onPress={disabled ? undefined : toggleSelectAll}>
              <Checkbox
                isChecked={isAllSelected}
                onToggle={toggleSelectAll}
                disabled={disabled}
              />
              <Text
                style={[styles.selectAllText, disabled && styles.disabledText]}>
                Chọn tất cả
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {cateCriterionData.map((category: any) => (
          <SurveySection
            key={category.Id}
            title={`${category.Name || 'Tiêu chí'}`}
            items={category.Criterions}
            checkedItems={checkedItems}
            onToggleCheck={toggleCheck}
            disabled={disabled}
          />
        ))}
      </ScrollView>
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  selectAllContainer: {
    marginBottom: 16,
    paddingVertical: 12,
  },
  selectAllItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 6,
  },
  disabledText: {
    color: '#999',
  },
});
