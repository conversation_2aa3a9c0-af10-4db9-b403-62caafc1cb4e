import {DataController} from '../../screen/base-controller';
import {getImage} from './rootAction';
import {showSnackbar} from '../../component/snackbar/snackbar';
import {ComponentStatus} from '../../component/component-status';
import {Category} from '../../types/CategoryType';

export const categoryAction = {
  find: async (config: {
    page?: number;
    size?: number;
    sortby?: any;
    searchRaw?: string;
  }) => {
    const controller = new DataController('Category');
    const res = await controller.aggregateList(config);
    if (res.code === 200) {
      let newData = await getImage({items: res.data});
      return newData;
    }
    return [];
  },
  findOne: async (id: string) => {
    const controller = new DataController('Category');
    const res = await controller.getById(id);
    if (res.code === 200 && res.data) {
      let data = await getImage({items: [res.data]});
      return data[0];
    }
    return null;
  },
  update: async (products: Category[]) => {
    const controller = new DataController('Category');
    try {
      const res = await controller.edit(products);
      if (res.code === 200) {
        return res.data;
      }
    } catch (error: any) {
      showSnackbar({message: error.message, status: ComponentStatus.ERROR});
    }
  },
};
