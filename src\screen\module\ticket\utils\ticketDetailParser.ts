/**
 * Utility functions for parsing ticket detail data
 * Handles complex nested JSON structures in ticket details
 */

export interface TicketDetailItem {
  CustomerId?: string;
  DateCreated?: number;
  Content?: string;
  [key: string]: any;
}

/**
 * Parse nested JSON structure for ticket details
 * Handles cases where Detail contains nested JSON strings
 *
 * Example input: "[\"[\\\"abc\\\",{\\\"CustomerId\\\":\\\"123\\\"}]\",{\"CustomerId\":\"456\"}]"
 *
 * @param detailString - The Detail string from ticket data
 * @returns Array of parsed detail items
 */
export const parseTicketDetails = (
  detailString: string | null | undefined,
): TicketDetailItem[] => {
  if (!detailString || typeof detailString !== 'string') {
    return [];
  }

  let parsed: any;
  try {
    parsed = JSON.parse(detailString);
  } catch {
    // Treat invalid JSON as plain string content
    return [{Content: detailString}];
  }

  if (!Array.isArray(parsed)) {
    if (typeof parsed === 'object' && parsed !== null) {
      return [parsed];
    }
    return [];
  }

  // Handle nested JSON strings within the array
  const flattened = parsed
    .map((item: any) => {
      if (typeof item === 'string') {
        try {
          // Try to parse nested JSON string
          const nestedParsed = JSON.parse(item);
          return Array.isArray(nestedParsed) ? nestedParsed : [nestedParsed];
        } catch {
          // If parsing fails, treat as plain string content
          return [{Content: item}];
        }
      }
      return [item];
    })
    .flat(); // Flatten all nested arrays

  // Filter out invalid items and ensure they have the expected structure
  return flattened.filter((item: any) => {
    return item && typeof item === 'object';
  });
};

/**
 * Extract customer IDs from parsed ticket details
 * @param details - Array of parsed detail items
 * @returns Array of unique customer IDs
 */
export const extractCustomerIds = (details: TicketDetailItem[]): string[] => {
  if (!details.length) return [];
  const customerIds = details
    .map(detail => detail.CustomerId)
    .filter((id): id is string => Boolean(id));

  // Return unique IDs
  return [...new Set(customerIds)];
};

/**
 * Get the latest detail item by DateCreated
 * @param details - Array of parsed detail items
 * @returns Latest detail item or null
 */
export const getLatestDetail = (
  details: TicketDetailItem[],
): TicketDetailItem | null => {
  if (!details.length) return null;

  return details.reduce((latest, current) => {
    const latestDate = latest.DateCreated || 0;
    const currentDate = current.DateCreated || 0;
    return currentDate > latestDate ? current : latest;
  });
};

/**
 * Format detail content for display
 * @param detail - Detail item
 * @returns Formatted content string
 */
export const formatDetailContent = (detail: TicketDetailItem): string => {
  if (!detail) return '';

  if (detail.Content) {
    return detail.Content;
  }

  // Fallback to stringifying the object if no Content field
  return JSON.stringify(detail);
};
