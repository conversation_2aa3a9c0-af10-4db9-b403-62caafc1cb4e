import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
  Modal,
} from 'react-native';
import BaseSurveyTaskCard from '../card/BaseSurveyTaskCard';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../router/router';
import {SurveyTaskDa} from '../surveyTaskDa';
import {SurveyTask} from '../../../../types/surveyTaskType';
import {CustomerItem} from '../../../../redux/reducers/user/da';
import {ColorThemes} from '../../../../assets/skin/colors';

import SurveyTaskFilterModal from '../components/SurveyTaskFilterModal';
import {SurveyTaskFilter, DEFAULT_FILTER} from '../constants/filterConstants';
import {Winicon} from '../../../../component/export-component';

// Extended SurveyTask type with Customer and Executor objects
type ExtendedSurveyTask = SurveyTask & {
  Customer?: CustomerItem | null;
  Executor?: CustomerItem | null;
};

// Main App using TaskCard
const ListSurveyTab = ({isRefresh}: {isRefresh: boolean}) => {
  const navigation = useNavigation<any>();
  const [surveyTasks, setSurveyTasks] = useState<ExtendedSurveyTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filter, setFilter] = useState<SurveyTaskFilter>(DEFAULT_FILTER);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const pageSize = 10;

  const surveyTaskDa = new SurveyTaskDa();

  const nextToDetail = (id: string) => {
    navigation.navigate(RootScreen.CreateSurveyStepPage, {
      id,
      type: 'view',
    });
  };

  const handleAddNewTask = () => {
    navigation.navigate(RootScreen.CreateSurveyStepPage, {
      id: '',
      type: 'create',
    });
  };

  const fetchSurveyTasks = async (
    pageNum: number,
    refresh = false,
    currentFilter?: SurveyTaskFilter,
  ) => {
    if (loading) return;

    setLoading(true);
    setError(null);

    try {
      const filterToUse = currentFilter || filter;

      const data = (await surveyTaskDa.fetchWithFilter(
        filterToUse,
        pageNum,
        pageSize,
      )) as ExtendedSurveyTask[];

      if (refresh) {
        setSurveyTasks(data || []);
        setPage(1);
        setHasMore(data && data.length === pageSize);
      } else {
        setSurveyTasks(prev => [...prev, ...(data || [])]);
        setHasMore(data && data.length === pageSize);
      }
    } catch (error) {
      console.error('Error fetching survey tasks:', error);
      setError('Có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại.');

      // If it's a refresh and there's an error, keep existing data
      if (!refresh) {
        setSurveyTasks([]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchSurveyTasks(nextPage);
    }
  };

  const handleFilterApply = useCallback((newFilter: SurveyTaskFilter) => {
    setFilter(newFilter);
    setShowFilterModal(false);
    setError(null); // Clear any existing errors
    setPage(1); // Reset page to 1
    fetchSurveyTasks(1, true, newFilter);
  }, []);

  useEffect(() => {
    fetchSurveyTasks(1, true);
  }, []);

  // Refresh when parent toggles isRefresh
  useEffect(() => {
    if (isRefresh) {
      fetchSurveyTasks(1, true);
    }
  }, [isRefresh]);

  const renderItem = ({item}: {item: ExtendedSurveyTask}) => (
    <BaseSurveyTaskCard
      data={item}
      onPress={nextToDetail}
      style={{marginBottom: 12}}
    />
  );

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Search and Filter Section */}

      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchSurveyTasks(1, true)}>
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      )}
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-end',
        }}>
        <View style={styles.addButtonContainer}>
          <TouchableOpacity style={styles.addButton} onPress={handleAddNewTask}>
            <Text style={styles.addButtonText}>Thêm công việc mới</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.searchFilterContainer}>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilterModal(true)}>
            <Winicon
              src="outline/user interface/setup-preferences"
              size={20}
              color={ColorThemes.light.neutral_text_placeholder_color}
            />
          </TouchableOpacity>
        </View>
      </View>

      <FlatList
        data={surveyTasks}
        renderItem={renderItem}
        scrollEnabled={false}
        keyExtractor={item => item.Id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="slide"
        presentationStyle="pageSheet">
        <SurveyTaskFilterModal
          filter={filter}
          onApply={handleFilterApply}
          onClose={() => setShowFilterModal(false)}
        />
      </Modal>
    </SafeAreaView>
  );
};

export default ListSurveyTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  searchFilterContainer: {
    marginVertical: 10,
    flexDirection: 'row',
    paddingRight: 16,
    paddingLeft: 3,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },

  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.white,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    backgroundColor: ColorThemes.light.error_background,
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  errorText: {
    flex: 1,
    color: ColorThemes.light.error_main_color,
    fontSize: 14,
    marginRight: 12,
  },
  retryButton: {
    backgroundColor: ColorThemes.light.error_main_color,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  retryButtonText: {
    color: ColorThemes.light.white,
    fontSize: 12,
    fontWeight: '600',
  },
  addButtonContainer: {
    alignItems: 'flex-end',
  },
  addButton: {
    width: 170,
    marginTop: 12,
    marginRight: 16,
    paddingVertical: 6,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    color: ColorThemes.light.white,
    fontSize: 14,
    fontWeight: '600',
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  footer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});
