import React, {useState, useCallback, useEffect} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ScrollView,
} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {RootScreen} from '../../../../router/router';
import EmptyPage from '../../../../project-component/empty-page';
import {store} from '../../../../redux/store/store';
import {useSelectorCustomerCompanyState} from '../../../../redux/hooks/hooks';
import ConfigAPI from '../../../../config/configApi';
import {CustomBottomSheet} from '../../../../project-component/form/DateRangePicker/CustomBottomSheet';

const OperationalStatistics: React.FC<{
  toiletId?: string;
  type: string;
  toiletServicesId?: string;
  setCountStatistics: (value: number) => void;
  getOperationalStatistics: (
    toiletId: string,
    toiletServicesId: string,
  ) => Promise<any>;
}> = ({
  toiletId,
  type,
  setCountStatistics,
  getOperationalStatistics,
  toiletServicesId,
}) => {
  const navigation = useNavigation<any>();
  const cusInfo = store.getState().customer.data;
  const cusId = store.getState().customer.data?.Id;
  const company = useSelectorCustomerCompanyState().data;
  const [operationalDataMonth, setOperationalDataMonth] = useState<any>([]);
  const [operationalDataOtherMonth, setOperationalDataOtherMonth] =
    useState<any>([]);
  const [operationalDataAll, setOperationalDataAll] = useState<any>([]);
  const [listToilet, setListToilet] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isMonthFilterVisible, setIsMonthFilterVisible] =
    useState<boolean>(false);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [filteredOtherMonthData, setFilteredOtherMonthData] = useState<any>([]);
  const [tempSelectedMonth, setTempSelectedMonth] = useState<number | null>(
    null,
  );

  // Function để đếm số lượng theo trạng thái từ data gốc chưa filter
  const getStatusCount = (status: string) => {
    const currentDate = new Date();
    const currentTime = currentDate.getTime();

    // Sử dụng operationalDataOtherMonth (data gốc) thay vì operationalDataMonth đã được filter
    const rawData = operationalDataAll || [];

    return rawData.filter((item: any) => {
      const startDate = new Date(item.DateStart);
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 1); // Thêm 1 tháng

      // Khai báo các biến chung để tránh redeclare
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();
      const itemMonth = startDate.getMonth();
      const itemYear = startDate.getFullYear();

      switch (status) {
        case 'notStarted':
          // Chưa thực hiện:
          // 1. Tất cả các item có StartDate lớn hơn thời gian hiện tại
          // 2. Các item có tháng và năm trùng với thời gian hiện tại nhưng Status = 1

          // Điều kiện 1: StartDate lớn hơn thời gian hiện tại
          const isFutureDate =
            startDate.getTime() > currentTime && item.Status === 1;

          // Điều kiện 2: Cùng tháng và năm hiện tại với Status = 1
          const isCurrentMonthYearWithStatus1 =
            itemMonth === currentMonth &&
            itemYear === currentYear &&
            item.Status === 1;

          return isFutureDate || isCurrentMonthYearWithStatus1;

        case 'Done':
          // Hoàn thành:
          // 1. Item có startDate là tháng và năm hiện tại có Status = 2
          // 2. Item có startDate nhỏ hơn thời gian hiện tại và có Status = 2

          // Điều kiện 1: Cùng tháng và năm hiện tại với Status = 2
          const isCurrentMonthYear =
            itemMonth === currentMonth &&
            itemYear === currentYear &&
            item.Status === 2;

          // Điều kiện 2: StartDate nhỏ hơn thời gian hiện tại với Status = 2
          const isPastWithStatus2 =
            startDate.getTime() < currentTime && item.Status === 2;

          return isCurrentMonthYear || isPastWithStatus2;

        case 'overdue':
          // Quá hạn:
          // 1) Nếu StartDate thuộc tháng/năm trước và Status = 1
          // 2) Hoặc nếu cùng tháng/năm hiện tại, ngày của StartDate > 6 và Status = 1
          const isEarlierMonthYear =
            itemYear < currentYear ||
            (itemYear === currentYear && itemMonth < currentMonth);

          const isSameMonthYearDayGT6 =
            itemYear === currentYear &&
            itemMonth === currentMonth &&
            currentTime > 6 &&
            item.Status === 1;

          return (
            (isEarlierMonthYear && item.Status === 1) || isSameMonthYearDayGT6
          );

        case 'upcoming':
          // Chưa đến thời gian: Chỉ lấy item có thời gian lớn hơn thời gian hiện tại
          return startDate.getTime() > currentTime;

        default:
          return false;
      }
    }).length;
  };

  const getOperationalStatisticsData = async () => {
    try {
      let response;
      setLoading(true);
      response = await getOperationalStatistics(
        toiletId as string,
        toiletServicesId as string,
      );
      console.log('response-getOperationalStatisticsData', response);
      if (response && response?.code === 200) {
        const currentTime = Date.now();
        setCountStatistics(
          response.data.filter((item: any) => {
            return item.Status === 1 && item.DateStart < currentTime;
          }).length,
        );
        setOperationalDataAll(response.data);
        // Lọc tất cả data có DateStart <= ngày hiện tại
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);
        const filteredData = response.data.filter((item: any) => {
          if (item?.DateStart) {
            const itemStartDate = new Date(item.DateStart);
            itemStartDate.setHours(0, 0, 0, 0);
            // Chỉ lấy những item có DateStart <= ngày hiện tại
            return itemStartDate <= currentDate;
          }
          // Nếu không có DateStart, vẫn bao gồm item đó
          return true;
        });

        setListToilet(filteredData);
        const currentMonth = new Date().getMonth() + 1;
        // Filter data for current month based on Sort field
        let dataCurrentMonth = filteredData.filter((item: any) => {
          return item?.Sort === currentMonth;
        });

        let dataOtherMonth = filteredData.filter((item: any) => {
          return item?.Sort !== currentMonth;
        });

        // Sort other months data by Sort field (month order) and then by DateCreated
        if (dataOtherMonth && dataOtherMonth.length > 0) {
          dataOtherMonth = dataOtherMonth.sort((a: any, b: any) => {
            // First sort by Sort field (month)
            if (a.Sort !== b.Sort) {
              return (a.Sort || 0) - (b.Sort || 0);
            }
            // Then sort by DateCreated (newest first within same month)
            const dateA = new Date(a.DateCreated || 0);
            const dateB = new Date(b.DateCreated || 0);
            return dateB.getTime() - dateA.getTime();
          });
        }
        setOperationalDataMonth(dataCurrentMonth || []);
        setOperationalDataOtherMonth(dataOtherMonth || []);
        setFilteredOtherMonthData(dataOtherMonth || []);
      }
    } catch (error) {
      setOperationalDataMonth([]);
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      getOperationalStatisticsData();
      console.log('toiletId-listToilet', listToilet);
    }, [toiletId, company?.Id]),
  );

  // Hàm lấy danh sách các tháng có sẵn (nhỏ hơn tháng hiện tại)
  const getAvailableMonths = (): {value: number; label: string}[] => {
    const currentMonth = new Date().getMonth() + 1;
    const months: {value: number; label: string}[] = [];

    // Lấy tất cả các tháng từ data
    const allMonths = new Set<number>();
    operationalDataOtherMonth.forEach((item: any) => {
      if (item?.Sort && item.Sort < currentMonth) {
        allMonths.add(item.Sort);
      }
    });

    // Chuyển đổi thành array và sắp xếp
    const sortedMonths = Array.from(allMonths).sort((a, b) => b - a); // Sắp xếp giảm dần

    // Tạo danh sách với tên tháng
    const monthNames = [
      'Tháng 1',
      'Tháng 2',
      'Tháng 3',
      'Tháng 4',
      'Tháng 5',
      'Tháng 6',
      'Tháng 7',
      'Tháng 8',
      'Tháng 9',
      'Tháng 10',
      'Tháng 11',
      'Tháng 12',
    ];

    sortedMonths.forEach(month => {
      months.push({
        value: month,
        label: monthNames[month - 1],
      });
    });

    return months;
  };

  // Hàm xử lý khi chọn tháng tạm thời trong modal
  const handleTempMonthSelect = (month: number | null) => {
    if (month === null) {
      // Nếu chọn "Tất cả các tháng", áp dụng ngay và đóng modal
      setSelectedMonth(null);
      setFilteredOtherMonthData(operationalDataOtherMonth);
      setIsMonthFilterVisible(false);
    } else {
      // Nếu chọn tháng cụ thể, chỉ lưu tạm thời
      setTempSelectedMonth(month);
    }
  };

  // Hàm xử lý khi nhấn "Xác nhận" trong modal
  const handleConfirmMonthFilter = () => {
    setSelectedMonth(tempSelectedMonth);
    if (tempSelectedMonth === null) {
      // Hiển thị tất cả các tháng khác
      setFilteredOtherMonthData(operationalDataOtherMonth);
    } else {
      // Lọc theo tháng đã chọn
      const filtered = operationalDataOtherMonth.filter(
        (item: any) => item?.Sort === tempSelectedMonth,
      );
      setFilteredOtherMonthData(filtered);
    }
    setIsMonthFilterVisible(false);
  };

  // Hàm mở modal chọn tháng
  const openMonthFilter = () => {
    setTempSelectedMonth(selectedMonth); // Khởi tạo với giá trị hiện tại
    setIsMonthFilterVisible(true);
  };

  // Hàm đóng modal mà không áp dụng thay đổi
  const closeMonthFilter = () => {
    setTempSelectedMonth(null);
    setIsMonthFilterVisible(false);
  };

  // Hàm xóa bộ lọc và lấy lại toàn bộ dữ liệu
  const clearMonthFilter = () => {
    setSelectedMonth(null);
    setFilteredOtherMonthData(operationalDataOtherMonth);
  };
  // Map API status to display status
  const getImplementationStatus = (item: any) => {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    // Check if DateStart exists and is in the past
    if (item?.DateStart) {
      // ✅ Xử lý DateStart an toàn - có thể là timestamp hoặc ISO string
      let itemStartDate;
      if (typeof item.DateStart === 'number') {
        // Nếu là timestamp (milliseconds)
        itemStartDate = new Date(item.DateStart);
      } else if (typeof item.DateStart === 'string') {
        // Nếu là ISO string
        itemStartDate = new Date(item.DateStart);
      } else {
        return null; // Skip invalid dates
      }

      // Kiểm tra date có hợp lệ không
      if (isNaN(itemStartDate.getTime())) {
        return null;
      }

      // Set về đầu ngày để so sánh chính xác
      itemStartDate.setHours(0, 0, 0, 0);
      const itemMonth = itemStartDate.getMonth() + 1;
      const itemDate = itemStartDate.getDate();

      // If DateStart is less than current date and month is less than current month
      if (
        itemStartDate < currentDate &&
        itemMonth < currentMonth &&
        item.Status == 1
      ) {
        return {
          status: 'expired',
          text: 'Quá hạn',
        };
      }
      if (
        itemMonth === currentMonth &&
        itemStartDate.getFullYear() === currentYear &&
        currentDate.getDate() >= 6 &&
        item.Status === 1
      ) {
        return {
          status: 'expired',
          text: 'Quá hạn',
        };
      }
    }

    // Check item status
    if (item.Status == 1) {
      return {
        status: 'notImplemented',
        text: 'Chưa thực hiện',
      };
    } else {
      return {
        status: 'implemented',
        text: 'Đã thực hiện',
      };
    }
  };
  const getApprovalStatus = (
    item: any,
  ): {status: string; text: string} | null => {
    if (item?.ApproveStatus === 2) {
      return {
        status: 'approved',
        text: 'Đã duyệt',
      };
    } else if (item?.ApproveStatus === 1) {
      return {
        status: 'rejected',
        text: 'Từ chối',
      };
    }

    // If no ApproveStatus or other values, return null (empty status)
    return null;
  };

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'notImplemented':
        return [styles.statusBadge, styles.notImplemented];
      case 'implemented':
        return [styles.statusBadge, styles.implemented];
      case 'approved':
        return [styles.statusBadge, styles.approved];
      case 'rejected':
        return [styles.statusBadge, styles.rejected];
      case 'pending':
        return [styles.statusBadge, styles.pending];
      case 'expired':
        return [styles.statusBadge, styles.expired];
      default:
        return [styles.statusBadge];
    }
  };

  const getStatusTextStyle = (status: string) => {
    switch (status) {
      case 'notImplemented':
        return styles.statusText;
      case 'implemented':
      case 'approved':
        return styles.statusTextSucces;
      case 'rejected':
      case 'expired':
        return styles.statusTextCancel;
      default:
        return styles.statusText;
    }
  };

  const renderToiletItem = (toilet: any) => {
    const implementationStatus = getImplementationStatus(toilet);
    const approvalStatus = getApprovalStatus(toilet);
    console.log('check-toilet', toilet);
    console.log('check-cusInfo', cusInfo?.Mobile);

    return (
      <TouchableOpacity
        style={styles.toiletItem}
        onPress={() => {
          if (toilet && !toilet?.ApproveStatus) {
            if (toilet?.ToiletServices?.CustomerMobile !== cusInfo?.Mobile) {
              navigation.navigate(
                RootScreen.OperationalStatisticsActionForKtx,
                {
                  item: toilet,
                  listToilet: listToilet,
                },
              );
            } else {
              navigation.navigate(RootScreen.OperationalStaticDetailPage, {
                item: toilet,
                listToilet: listToilet,
              });
            }
          } else {
            navigation.navigate(RootScreen.OperationalStaticDetailPage, {
              item: toilet,
              listToilet: listToilet,
            });
          }
        }}>
        <Text style={styles.toiletCode}>{toilet.Name || 'N/A'}</Text>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Trạng thái thực hiện:</Text>
          {implementationStatus ? (
            <View style={getStatusStyle(implementationStatus.status)}>
              <Text style={getStatusTextStyle(implementationStatus.status)}>
                {implementationStatus.text}
              </Text>
            </View>
          ) : (
            <View style={getStatusStyle('default')}>
              <Text style={getStatusTextStyle('default')}>Không xác định</Text>
            </View>
          )}
        </View>

        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Trạng thái phê duyệt:</Text>
          {approvalStatus ? (
            <View style={getStatusStyle(approvalStatus.status)}>
              <Text style={getStatusTextStyle(approvalStatus.status)}>
                {approvalStatus.text}
              </Text>
            </View>
          ) : (
            <View style={styles.emptyStatus} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderItem = ({item, index}: {item: any; index: number}) => {
    // Add section header for the first item
    if (index === 0) {
      return (
        <View>
          <Text style={styles.sectionTitle}>Tháng hiện tại</Text>
          {renderToiletItem(item)}
        </View>
      );
    }

    // Render toilet items normally
    return renderToiletItem(item);
  };
  const renderOtherMonthItem = ({item}: {item: any}) => {
    return <View>{renderToiletItem(item)}</View>;
  };
  const renderListFooter = () => {
    return (
      <View>
        <View style={styles.otherMonthsHeader}>
          <Text style={styles.sectionTitle}>
            {selectedMonth
              ? `Các tháng khác - Tháng ${selectedMonth}`
              : 'Các tháng khác'}
          </Text>
          <View style={styles.filterButtonGroup}>
            {selectedMonth && selectedMonth > 0 ? (
              <TouchableOpacity
                style={styles.clearFilterButton}
                onPress={clearMonthFilter}>
                <Text style={styles.clearFilterText}>Xóa bộ lọc</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.filterButton}
                onPress={openMonthFilter}>
                <Text style={styles.filterText}>Bộ lọc tháng</Text>
                <Winicon
                  src={'outline/user interface/setup-preferences'}
                  size={15}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* FlatList for other months data */}
        {filteredOtherMonthData && filteredOtherMonthData.length > 0 ? (
          <FlatList
            data={filteredOtherMonthData}
            renderItem={renderOtherMonthItem}
            keyExtractor={(item, index) =>
              `other_${item.Id?.toString() || index.toString()}`
            }
            showsVerticalScrollIndicator={false}
            scrollEnabled={false} // Disable scroll since it's inside another FlatList
            style={styles.otherMonthsList}
          />
        ) : (
          <View style={styles.emptyOtherMonths}>
            <Text style={styles.emptyOtherMonthsText}>
              {selectedMonth
                ? `Không có dữ liệu tháng ${selectedMonth}`
                : 'Không có dữ liệu các tháng khác'}
            </Text>
          </View>
        )}
      </View>
    );
  };
  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.loadingText}>Đang tải dữ liệu...</Text>
      </View>
    );
  }

  // Check if we have any data at all (current month or other months)
  const hasAnyData =
    (operationalDataMonth && operationalDataMonth.length > 0) ||
    (operationalDataOtherMonth && operationalDataOtherMonth.length > 0);

  if (!hasAnyData) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <EmptyPage />
      </View>
    );
  }

  // If we have current month data, render with FlatList
  if (operationalDataMonth && operationalDataMonth.length > 0) {
    return (
      <>
        {/* Thống kê số lượng theo trạng thái - Hàng ngang */}
        <View style={styles.statisticsContainer}>
          <View style={styles.statisticsRow}>
            <View style={styles.statisticsCard}>
              <Text style={styles.statisticsNumber}>
                {getStatusCount('notStarted')}
              </Text>
              <Text style={styles.statisticsLabel} numberOfLines={2}>
                Chưa thực hiện
              </Text>
            </View>

            <View style={styles.statisticsCard}>
              <Text style={styles.statisticsNumber}>
                {getStatusCount('Done')}
              </Text>
              <Text style={styles.statisticsLabel} numberOfLines={2}>
                Đã thực hiện
              </Text>
            </View>

            <View style={styles.statisticsCard}>
              <Text style={styles.statisticsNumber}>
                {getStatusCount('overdue')}
              </Text>
              <Text style={styles.statisticsLabel} numberOfLines={2}>
                Quá hạn
              </Text>
            </View>

            <View style={styles.statisticsCard}>
              <Text style={styles.statisticsNumber}>
                {getStatusCount('upcoming')}
              </Text>
              <Text style={styles.statisticsLabel} numberOfLines={2}>
                Chưa đến
              </Text>
            </View>
          </View>
        </View>

        <FlatList
          style={styles.container}
          data={operationalDataMonth}
          renderItem={renderItem}
          keyExtractor={(item, index) =>
            item.Id?.toString() || index.toString()
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
          ListFooterComponent={renderListFooter}
        />

        {/* Modal chọn tháng */}
        <MonthFilterModal
          visible={isMonthFilterVisible}
          onClose={closeMonthFilter}
          onConfirm={handleConfirmMonthFilter}
          availableMonths={getAvailableMonths()}
          tempSelectedMonth={tempSelectedMonth}
          onSelectMonth={handleTempMonthSelect}
        />
      </>
    );
  }

  // If we only have other months data, render them directly
  return (
    <>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {/* Show message for no current month data */}
          <Text style={styles.sectionTitle}>Tháng hiện tại</Text>
          <View style={styles.emptyCurrentMonth}>
            <Text style={styles.emptyCurrentMonthText}>
              Không có dữ liệu tháng hiện tại
            </Text>
          </View>

          <View style={styles.otherMonthsHeader}>
            <Text style={styles.sectionTitle}>
              {selectedMonth
                ? `Các tháng khác - Tháng ${selectedMonth}`
                : 'Các tháng khác'}
            </Text>
            <View style={styles.filterButtonGroup}>
              <TouchableOpacity
                style={styles.filterButton}
                onPress={openMonthFilter}>
                <Text style={styles.filterText}>Bộ lọc tháng</Text>
                <Winicon
                  src={'outline/user interface/setup-preferences'}
                  size={15}
                />
              </TouchableOpacity>
              {selectedMonth && selectedMonth > 0 && (
                <TouchableOpacity
                  style={styles.clearFilterButton}
                  onPress={clearMonthFilter}>
                  <Text style={styles.clearFilterText}>Xóa bộ lọc</Text>
                  <Winicon src={'outline/user interface/close'} size={15} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* FlatList for other months data */}
          {filteredOtherMonthData && filteredOtherMonthData.length > 0 ? (
            <FlatList
              data={filteredOtherMonthData}
              renderItem={renderOtherMonthItem}
              keyExtractor={(item, index) =>
                `other_${item.Id?.toString() || index.toString()}`
              }
              showsVerticalScrollIndicator={false}
              style={styles.otherMonthsList}
            />
          ) : (
            <View style={styles.emptyOtherMonths}>
              <Text style={styles.emptyOtherMonthsText}>
                {selectedMonth
                  ? `Không có dữ liệu tháng ${selectedMonth}`
                  : 'Không có dữ liệu các tháng khác'}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Modal chọn tháng */}
      <MonthFilterModal
        visible={isMonthFilterVisible}
        onClose={closeMonthFilter}
        onConfirm={handleConfirmMonthFilter}
        availableMonths={getAvailableMonths()}
        tempSelectedMonth={tempSelectedMonth}
        onSelectMonth={handleTempMonthSelect}
      />
    </>
  );
};

// Component để render modal chọn tháng
const MonthFilterModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  availableMonths: {value: number; label: string}[];
  tempSelectedMonth: number | null;
  onSelectMonth: (month: number | null) => void;
}> = ({
  visible,
  onClose,
  onConfirm,
  availableMonths,
  tempSelectedMonth,
  onSelectMonth,
}) => {
  const renderMonthItem = (month: {value: number; label: string}) => (
    <TouchableOpacity
      key={month.value}
      style={[
        styles.monthItem,
        tempSelectedMonth === month.value && styles.selectedMonthItem,
      ]}
      onPress={() => onSelectMonth(month.value)}>
      <Text
        style={[
          styles.monthText,
          tempSelectedMonth === month.value && styles.selectedMonthText,
        ]}>
        {month.label}
      </Text>
    </TouchableOpacity>
  );

  // Kiểm tra xem có tháng cụ thể nào được chọn không (không phải "Tất cả các tháng")
  // Nút "Xác nhận" chỉ được enable khi chọn một tháng cụ thể (tempSelectedMonth !== null)
  const isConfirmDisabled = tempSelectedMonth === null;

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title="Chọn tháng"
      onCancel={onClose}
      onConfirm={isConfirmDisabled ? undefined : onConfirm}
      cancelText="Hủy"
      confirmText="Xác nhận"
      height={400}
      isShowConfirm={!isConfirmDisabled}>
      <View style={styles.monthFilterContent}>
        <ScrollView
          style={styles.monthScrollView}
          showsVerticalScrollIndicator={false}
          bounces={false}>
          {/* Danh sách các tháng */}
          {availableMonths.map(renderMonthItem)}
        </ScrollView>
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    marginTop: 8,
  },
  toiletItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  toiletCode: {
    ...TypoSkin.buttonText2,
    fontWeight: '600',
    marginBottom: 12,
    lineHeight: 20,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    ...TypoSkin.buttonText3,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    minWidth: 80,
    alignItems: 'center',
  },
  statusText: {
    ...TypoSkin.buttonText5,
    fontWeight: '500',
    color: '#CC731E',
  },
  statusTextSucces: {
    ...TypoSkin.buttonText5,
    fontWeight: '500',
    color: ColorThemes.light.success_main_color,
  },
  statusTextCancel: {
    ...TypoSkin.buttonText5,
    fontWeight: '500',
    color: '#BA2D2B',
  },
  notImplemented: {
    backgroundColor: '#FFF3E0',
  },
  implemented: {
    backgroundColor: '#E8F5E8',
  },
  rejected: {
    backgroundColor: '#FFEBEE',
  },
  pending: {
    backgroundColor: '#F5F5F5',
  },
  approved: {
    backgroundColor: '#E8F5E8',
  },
  expired: {
    backgroundColor: '#FFEBEE',
  },
  emptyStatus: {
    width: 80,
    height: 28,
  },
  otherMonthsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  filterButtonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F5F5F5',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  filterText: {
    fontSize: 12,
    color: '#666',
    marginRight: 4,
  },
  clearFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#ffebee',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#f44336',
  },
  clearFilterText: {
    fontSize: 12,
    color: '#f44336',
    marginRight: 4,
    fontWeight: '500',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    ...TypoSkin.buttonText3,
    color: '#666',
    textAlign: 'center',
  },
  otherMonthsList: {
    marginTop: 10,
  },
  emptyOtherMonths: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    marginTop: 10,
  },
  emptyOtherMonthsText: {
    ...TypoSkin.buttonText3,
    textAlign: 'center',
  },
  emptyCurrentMonth: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    marginBottom: 20,
  },
  emptyCurrentMonthText: {
    ...TypoSkin.buttonText3,
    color: '#999',
    textAlign: 'center',
  },
  monthFilterContent: {
    paddingHorizontal: 16,
    maxHeight: 400,
  },
  monthScrollView: {
    maxHeight: 300,
  },
  monthItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginVertical: 4,
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedMonthItem: {
    backgroundColor: ColorThemes.light.secondary2_sub_color,
    borderColor: ColorThemes.light.secondary2_main_color,
  },
  monthText: {
    ...TypoSkin.buttonText3,
    color: '#333',
    textAlign: 'center',
    fontWeight: '500',
  },
  selectedMonthText: {
    color: ColorThemes.light.secondary2_main_color,
    fontWeight: '600',
  },
  allMonthsItem: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
  },
  allMonthsText: {
    color: '#2196f3',
    fontWeight: '600',
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 8,
    marginHorizontal: 20,
  },
  // Statistics styles - Horizontal layout
  statisticsContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 8,
  },
  statisticsTitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
    textAlign: 'center',
  },
  statisticsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statisticsCard: {
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 2,
  },
  statisticsNumber: {
    ...TypoSkin.body2,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  statisticsLabel: {
    fontSize: 9,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    lineHeight: 12,
  },
});

export default OperationalStatistics;
