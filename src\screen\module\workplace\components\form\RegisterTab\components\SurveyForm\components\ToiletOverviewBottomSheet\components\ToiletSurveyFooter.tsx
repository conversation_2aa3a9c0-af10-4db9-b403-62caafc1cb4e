import React, {memo} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {TypoSkin} from '../../../../../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../../../../../assets/skin/colors';

interface ToiletSurveyFooterProps {
  loading: boolean;
  onCompleteSurvey: () => void;
}

export const ToiletSurveyFooter: React.FC<ToiletSurveyFooterProps> = memo(
  ({loading, onCompleteSurvey}) => {
    const onPress = () => {
      onCompleteSurvey();
    };
    return (
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.confirmButton,
            loading && styles.confirmButtonDisabled,
          ]}
          onPress={onPress}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator
              size="small"
              color={ColorThemes.light.neutral_absolute_background_color}
            />
          ) : (
            <Text style={styles.confirmButtonText}>Xá<PERSON> nhận</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  footer: {
    paddingVertical: 16,
    borderTopWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  confirmButton: {
    marginTop: 8,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  confirmButtonDisabled: {
    opacity: 0.6,
  },
  confirmButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
});
