/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useCallback, useState} from 'react';
import {
  StyleSheet,
  View,
  ActivityIndicator,
  FlatList,
  RefreshControl,
} from 'react-native';
import {useRoute} from '@react-navigation/native';
import {FLoading} from 'wini-mobile-components';
import {DataController} from '../../../base-controller';
import {Title} from '../../../../config/Contanst';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {navigate, RootScreen} from '../../../../router/router';
import {OrderItem} from './types';
import {useOrderData, useOrderSearch} from './hooks';
import {
  OrderListItem,
  OrderInfo,
  LoadingFooter,
  SearchIndicator,
} from './components';

const OrderCustomerDetail = () => {
  const route = useRoute<any>();
  const {idRefresh} = route?.params || {};
  const [searchData, setSearchData] = useState<OrderItem[]>([]);

  const orderDetailController = new DataController('OrderDetailProduct');

  // Custom hooks
  const {
    data: orderData,
    isLoading,
    refreshing,
    loadingMore,
    hasMoreData,
    currentPage,
    getData,
    onRefresh: handleRefresh,
  } = useOrderData();

  // Handle search results
  const handleSearchResults = useCallback((results: OrderItem[]) => {
    setSearchData(results);
  }, []);

  const handleLoadingChange = useCallback((_loading: boolean) => {
    // Handle loading state if needed
  }, []);

  const {searchTerm, isSearching, setSearchTerm} = useOrderSearch(
    route?.params?.status,
    handleSearchResults,
    handleLoadingChange,
  );

  // Get the data to display (search results or regular data)
  const displayData = searchTerm.trim() ? searchData : orderData;

  useEffect(() => {
    getData(route?.params?.status, 1, true);
  }, [route?.params?.status, idRefresh]);

  // Handle load more data
  const handleLoadMore = useCallback(async () => {
    if (!loadingMore && hasMoreData && !searchTerm.trim()) {
      await getData(route?.params?.status, currentPage + 1, false);
    }
  }, [
    loadingMore,
    hasMoreData,
    searchTerm,
    getData,
    route?.params?.status,
    currentPage,
  ]);

  // Handle refresh
  const handleRefreshData = useCallback(async () => {
    setSearchTerm('');
    setSearchData([]);
    await handleRefresh();
  }, [handleRefresh, setSearchTerm]);

  const getTitleHeader = () => {
    switch (route?.params?.status) {
      case 1:
        return Title.New;
      case 2:
        return Title.Processing;
      case 3:
        return Title.Done;
      case 4:
        return Title.Cancel;
      default:
        return '';
    }
  };

  // Handle rating button press
  const handleRatingPress = useCallback(
    async (item: OrderItem) => {
      const res = await orderDetailController.getListSimple({
        page: 1,
        size: 1000,
        query: `@OrderProductId: {${item?.Id}}`,
      });
      if (res.code === 200) {
        item.ProductId = res.data.map(
          (orderDetail: any) => orderDetail.ProductId,
        );
        navigate(RootScreen.CreateReviewOrder, {
          Data: item,
        });
      }
    },
    [orderDetailController],
  );

  // Handle order item press
  const handleOrderPress = useCallback(
    (orderId: string, type: 'shop' | 'customer') => {
      navigate(RootScreen.OrderDetailPage, {
        orderId: orderId,
        type: type,
      });
    },
    [],
  );

  return (
    <View style={styles.container}>
      <FLoading visible={isLoading} />

      <TitleHeader
        title={getTitleHeader()}
        showSearch
        onSearch={setSearchTerm}
      />

      <View style={{position: 'relative'}}>
        <SearchIndicator isVisible={isSearching} />
      </View>

      <OrderInfo
        isLoading={isLoading}
        isSearching={isSearching}
        dataLength={displayData.length}
        searchTerm={searchTerm}
      />
      {isLoading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <FlatList
          data={displayData}
          style={{flex: 1, height: '100%'}}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          contentContainerStyle={{paddingHorizontal: 16, gap: 16}}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefreshData}
              colors={['#0000ff']} // Android
              tintColor={'#0000ff'} // iOS
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() => <LoadingFooter isVisible={loadingMore} />}
          renderItem={({item}) => (
            <OrderListItem
              item={item}
              status={route?.params?.status}
              onPress={orderId => handleOrderPress(orderId, 'customer')}
              onRatingPress={
                route?.params?.status === 3 ? handleRatingPress : undefined
              }
            />
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default OrderCustomerDetail;
