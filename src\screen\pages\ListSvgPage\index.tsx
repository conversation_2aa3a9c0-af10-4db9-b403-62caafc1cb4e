import React, {useState, useMemo} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svgs/iconSvg';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../layout/header';
import {useNavigation} from '@react-navigation/native';
import Clipboard from '@react-native-clipboard/clipboard';
import {SafeAreaView} from 'react-native-safe-area-context';

interface SvgItem {
  name: string;
  svgContent: string;
}

const ListSvgPage = () => {
  const [searchText, setSearchText] = useState('');
  const navigation = useNavigation<any>();

  // Extract all SVG icons from iconSvg class
  const allSvgIcons = useMemo(() => {
    const icons: SvgItem[] = [];

    // Get all static properties from iconSvg class
    const iconKeys = Object.getOwnPropertyNames(iconSvg);

    iconKeys.forEach(key => {
      const iconValue = (iconSvg as any)[key];
      if (typeof iconValue === 'string' && iconValue.includes('<svg')) {
        icons.push({
          name: key,
          svgContent: iconValue,
        });
      }
    });

    return icons.sort((a, b) => a.name.localeCompare(b.name));
  }, []);

  // Filter icons based on search text
  const filteredIcons = useMemo(() => {
    if (!searchText.trim()) {
      return allSvgIcons;
    }

    return allSvgIcons.filter(icon =>
      icon.name.toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [allSvgIcons, searchText]);

  const copyToClipboard = (iconName: string) => {
    const copyText = `iconSvg.${iconName}`;
    Clipboard.setString(copyText);
    Alert.alert('Đã sao chép!', `${copyText} đã được sao chép vào clipboard.`);
  };

  const renderSvgItem = ({item}: {item: SvgItem}) => (
    <TouchableOpacity
      style={styles.iconContainer}
      onPress={() => copyToClipboard(item.name)}
      activeOpacity={0.7}>
      <View style={styles.iconWrapper}>
        <AppSvg SvgSrc={item.svgContent} size={32} />
      </View>
      <Text style={styles.iconName} numberOfLines={2}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        title="Danh sách SVG Icons"
        onBack={() => navigation.goBack()}
      />
      <View style={styles.header}>
        <Text style={styles.subtitle}>
          Tổng cộng: {filteredIcons.length} icons
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Tìm kiếm icon..."
          value={searchText}
          onChangeText={setSearchText}
          placeholderTextColor={ColorThemes.light.neutral_text_subtitle_color}
        />
      </View>

      <FlatList
        data={filteredIcons}
        renderItem={renderSvgItem}
        keyExtractor={item => item.name}
        numColumns={3}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              Không tìm thấy icon nào với từ khóa "{searchText}"
            </Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    padding: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  subtitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  searchInput: {
    height: 48,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_title_color,
  },
  listContainer: {
    padding: 16,
    paddingTop: 0,
  },
  iconContainer: {
    flex: 1,
    margin: 4,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconWrapper: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    backgroundColor: ColorThemes.light.neutral_lighter_background_color,
    borderRadius: 8,
  },
  iconName: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    fontSize: 11,
    lineHeight: 14,
  },
  separator: {
    height: 8,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
});

export default ListSvgPage;
