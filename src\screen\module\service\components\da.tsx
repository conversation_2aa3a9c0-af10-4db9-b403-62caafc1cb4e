import ConfigAPI from 'config/configApi';
import {CustomerType} from 'redux/reducers/user/da';
import {DataController} from 'screen/base-controller';
import {GetCustomerRoleResult, RoleDa} from 'screen/module/role/roleDa';
import {Role, RoleItem} from 'types/roleType';

export enum ToiletSurveyType {
  clean = 1, //sach
  green = 2, //xanh
  cyclic = 3, // tuan hoan
}

export const ToiletSurveyTypeStrings = [
  {key: ToiletSurveyType.clean, title: 'Sạch'},
  {key: ToiletSurveyType.green, title: 'Xanh'},
  {key: ToiletSurveyType.cyclic, title: 'Tuần hoàn'},
];

export enum ToiletType {
  forWorker = 1,
  forOffice = 2,
  forBuilding = 3,
  forFamily = 4,
}

export const TypeStringData = [
  {key: ToiletType.forWorker, title: '<PERSON> ngườ<PERSON> lao động/Sản xuất'},
  {key: ToiletType.forOffice, title: 'Văn phòng'},
  {key: ToiletType.forBuilding, title: 'Chung cư/ Toà nhà'},
  {key: ToiletType.forFamily, title: 'Hộ gia đình'},
];

export enum ToiletPlace {
  outDoor = 1,
  inDoor = 2,
}

export const PlaceStringData = [
  {key: ToiletPlace.outDoor, title: 'Ngoài trời'},
  {key: ToiletPlace.inDoor, title: 'Gắn liền toà nhà'},
];

export enum ToiletStatus {
  register = 1,
  consultant = 2,
  contract = 3,
  design = 4,
  build = 5,
  liquid = 6,
  run = 7,
}

export enum ToiletCertificateStatus {
  clean = 1,
  registerGreen = 2,
  green = 3,
  registerRecycle = 4,
  recycle = 5,
}

export const CertificateStringData = [
  {key: ToiletCertificateStatus.clean, title: 'Sạch'},
  {key: ToiletCertificateStatus.registerGreen, title: 'Đăng ký Xanh'},
  {key: ToiletCertificateStatus.green, title: 'Xanh'},
  {key: ToiletCertificateStatus.registerRecycle, title: 'Đăng ký Tuần hoàn'},
  {key: ToiletCertificateStatus.recycle, title: 'Tuần hoàn'},
];

export enum ToiletServiceStatus {
  reject = 0,
  register = 1,
  research = 2,
  consultant = 3,
  sendCompleteQuote = 4,
  contract = 5,
  sendCompleteContract = 6,
  design = 7,
  sendCompleteDesign = 8,
  build = 9,
  sendCompleteBuild = 10,
  liquid = 11,
  run = 12,
}

export enum ToiletFileType {
  design = 1,
  other = 2,
}

export enum ContractStatus {
  init = 0,
  partnerSigned = 1,
  guestSigned = 2,
  reject = 3,
}

export enum ContractType {
  quote = 1,
  contract = 2,
  report = 3,
  liquid = 4,
}

export enum CategoryType {
  device = 1,
  material = 2,
}

export enum CateServicesType {
  contact = '59b185ae05214e91b007562f555385a3',
  create = 'b6e8c1b758894318a8c9bd631c6847bb',
  upgrade = 'da9ad437b6514f42aac4808cad5dd46f',
  repair = 'b9efa26164e94f0a875f20e370fcde0b',
  clean = 'c6bf8cff25654b428d2ef6df82a25d95',
  edu = '166381744eba4183abc861edf725f9a5',
  netzero = 'cc69d67fbcf34db1af46d04afb333ba1',
}

export enum TaskStatus {
  open = 1,
  // doing = 2,
  overdue = 3,
  done = 4,
  closed = 5,
}

export enum TaskType {
  consultant = 1,
  contract = 2,
  design = 3,
  build = 4,
  liquid = 5,
  other = 6, // task when toilet is in use
}

export enum ProductSourceType {
  outside = 1,
  inside = 2,
}

export const ProductSourceStringData = [
  {key: ProductSourceType.outside, title: 'Nhập khẩu'},
  {key: ProductSourceType.inside, title: 'Nội địa'},
];

export enum ProductType {
  bio = 1,
  other = 2,
}

export const ProductTypeStringData = [
  {key: ProductType.bio, title: 'Chế phẩm sinh học'},
  {key: ProductType.other, title: 'Thiết bị'},
];

export enum AddendumType {
  quote = 1,
  contract = 2,
}

export enum TicketType {
  services = 1,
  feedback = 2,
  accident = 3,
}

export enum TicketStatus {
  cancel = 0,
  init = 1,
  processing = 2,
  done = 3,
  end = 4,
}

export const TicketStatusStrings = [
  {key: TicketStatus.cancel, title: 'Hủy'},
  {key: TicketStatus.init, title: 'Đang mở'},
  {key: TicketStatus.processing, title: 'Đang xử lý'},
  {key: TicketStatus.done, title: 'Hoàn thành'},
  {key: TicketStatus.end, title: 'Kết thúc'},
];

export enum TaskRepeatType {
  everyday = 1,
  everyWeek = 2,
  everyMonth = 3,
}

export const TaskRepeatTypeStrings = [
  {key: TaskRepeatType.everyday, title: 'Hàng ngày'},
  {key: TaskRepeatType.everyWeek, title: 'Hàng tuần'},
  {key: TaskRepeatType.everyMonth, title: 'Hàng tháng'},
];

export enum DeviceBioStatus {
  inactive = 1,
  active = 2,
}

export enum SurveyStatus {
  rejectSurvey = 0,
  sendSurvey = 1,
  doneSurvey = 2,
}

/** phân phối, chia đơn hàng toilet */
export const handleToiletDistribute = async (params: {
  lat: number;
  long: number;
  toiletId?: string;
  rejectCustomerId?: string;
  cateIds: Array<string>;
}) => {
  const customerController = new DataController('Customer');
  const rejectIds = [ConfigAPI.adminKtxId]; // adminKtx
  if (params.rejectCustomerId?.length) rejectIds.push(params.rejectCustomerId);
  // tìm các đối tác có lat long &  cách đơn hàng <= 10km
  const closestConsultant = await customerController.group({
    searchRaw: `@Type:[${CustomerType.partner} ${CustomerType.partner}] @CateServicesId:{${params.cateIds.map(id => `*${id}*`).join(' | ')}} @Lat:[0 +inf] @Long:[0 +inf] -@Id:{${rejectIds.join(' | ')}}`,
    reducers: `LOAD * APPLY geodistance(@Lat,@Long,${params.lat},${params.long}) AS __dist FILTER (@__dist <= 10000) SORTBY 1 @__dist LIMIT 0 50`,
  });

  if (closestConsultant?.code === 200 && closestConsultant?.data?.length > 0) {
    // check roleda.getCustomerRole check role owner
    // 1. role owner cua company, 2. partner nhung k co role gi trong company
    const roleDa = new RoleDa();
    let validUsers: any = [];
    const cusIds = closestConsultant.data?.map((cus: any) => cus.Id);
    const users = await roleDa.getCustomersRole(cusIds);
    if (users) {
      users?.forEach((res: GetCustomerRoleResult) => {
        if (
          res.roles.some(
            (role: RoleItem) =>
              (role.Role === Role.Owner && res.isCompany == true) ||
              (res?.role === 'partner' && !role.Role),
          )
        ) {
          validUsers.push(res);
        }
      });
    }

    if (validUsers?.length > 0) {
      const servicesController = new DataController('ToiletServices');

      const res = await servicesController.group({
        searchRaw: `@CustomerId:{${validUsers.map((e: any) => e.Id).join(' | ')}} @Status:[${params.toiletId ? ToiletServiceStatus.reject : ToiletServiceStatus.register} ${ToiletServiceStatus.sendCompleteBuild}]`,
        reducers: params.toiletId
          ? `LOAD * APPLY contains(@ToiletId,"${params.toiletId}") AS __exist GROUPBY 2 @CustomerId @__exist REDUCE COUNT 0 AS __count`
          : 'LOAD * GROUPBY 1 @CustomerId REDUCE COUNT 0 AS __count SORTBY 1 @__count',
      });
      if (res.code === 200) {
        const rejectedCustomerIds: Array<string> = params.toiletId
          ? res.data
              .filter((e: any) => e['__exist'] === '1')
              .map((e: any) => e.CustomerId)
          : [];
        let customers: Array<any> = validUsers.filter((e: any) =>
          rejectedCustomerIds.every(id => e.Id !== id),
        );
        return customers[0];
      }
    } else {
      const getAdminKtxData = await customerController.getById(
        ConfigAPI.adminKtxId,
      );
      if (getAdminKtxData.code === 200) return getAdminKtxData.data;
      return null;
    }
  } else {
    const getAdminKtxData = await customerController.getById(
      ConfigAPI.adminKtxId,
    );
    if (getAdminKtxData.code === 200) return getAdminKtxData.data;
    return null;
  }
};

/** Quy tắc random tên đơn hàng */
export const randomToiletServiceName = async (
  service: 'TV' | 'XM' | 'CT' | 'SCBD' | 'VSLD' | 'ĐTCC' | 'NZR',
) => {
  const servicesController = new DataController('ToiletServices');
  const now = new Date();
  const firstDateOfYear = new Date(now.getFullYear(), 0);
  const res = await servicesController.group({
    searchRaw: `@DateCreated:[${firstDateOfYear.getTime()} +inf]`,
    reducers: 'GROUPBY 0 REDUCE COUNT 0 AS _count',
  });
  let orderIndex: string | number = 0;
  if (res.code === 200 && res.data?.length)
    orderIndex = parseInt(res.data[0]['_count']);
  let auto5 = `${orderIndex + 1}`;
  while (auto5.length < 5) auto5 = `0${auto5}`;
  return `${now.getFullYear().toString().substring(2)}${now.getMonth() < 9 ? `0${now.getMonth() + 1}` : now.getMonth() + 1}${service}${auto5}`;
};

export const signTickImg = `<img style="width:40px" src="https://redis.ktxgroup.com.vn/api/file/img/eeffe6797dcc4ecaa6ba699510501922"/>`;
