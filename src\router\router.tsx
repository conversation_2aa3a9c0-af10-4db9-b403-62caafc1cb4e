import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
import ServicesWorkFlow from '../screen/module/service/components/ServicesWorkFlow';

export enum RootScreen {
  navigateKtxView = 'navigateKtxView',
  navigateCommunityView = 'navigateCommunityView',
  // Groups
  CommunityLayout = 'CommunityLayout',
  GroupIndex = 'GroupIndex',
  SocialGroups = 'SocialGroups',
  AllGroupsLoadMore = 'AllGroupsLoadMore',
  PostDetail = 'PostDetail',
  Certificate = 'Certificate',
  ProfileCommunity = 'ProfileCommunity',
  NotifCommunity = 'NotifCommunity',
  createPost = 'createPost',
  //
  login = 'Login',
  ForgotPass = 'ForgotPass',
  navigateView = 'NavigateView',
  // partner manage
  ToiletList = 'ToiletList',
  ProductList = 'ProductList',
  MaterialList = 'MaterialList',
  MyTicketList = 'MyTicketList',
  ManageProduct = 'ManageProduct',
  CreateProductPartnerPage = 'CreateProductPartnerPage',
  PromorionProductPage = 'PromorionProductPage',
  ToiletDetailPage = 'ToiletDetailPage',
  //
  detailProject = 'DetailProject',
  // services call
  contact = 'contact', //"Tư vấn",
  create = 'create', //"Xây mới",
  upgrade = 'upgrade', //"Nâng cấp"
  repair = 'repair', //"Sửa chữa"
  clean = 'clean', //"Vệ sinh"
  edu = 'edu', //"Đào tạo"
  // notfication
  NotificationIndex = 'NotificationIndex',
  //
  // services
  ServicesWorkFlow = 'ServicesWorkFlow',
  ContactFlow = 'ContactFlow',
  CreateFlow = 'CreateFlow',
  RepairFlow = 'RepairFlow',
  CleanFlow = 'CleanFlow',
  EduFlow = 'EduFlow',
  WebViewServiceFlow = 'WebViewServiceFlow',
  //
  ResultScanQrcode = 'ResultScanQrcode',
  // workplace
  DetailWorkView = 'DetailToiletServicePage',
  //
  // Profile
  SettingProfile = 'SettingProfile',
  PolicyView = 'PolicyView',
  FAQView = 'FAQView',
  ProductView = 'ProductView',
  DetailProduct = 'DetailProduct',
  CompanyView = 'CompanyView',
  MyAddress = 'MyAddress',
  EditAddress = 'EditAddress',
  // map
  mapViewIndex = 'MapViewIndex',
  mapGeolocationView = 'MapGeolocationView',

  // product
  ProductIndex = 'ProductIndex',
  ProductListByCategory = 'ProductListByCategory',
  FavoriteProductPage = 'FavoriteProductPage',
  DetailProductPage = 'DetailProductPage',
  RatingPage = 'RatingPage',

  //cart
  CartPage = 'CartPage',
  CheckoutPage = 'CheckoutPage',
  TabRegisterPartner = 'TabRegisterPartner',
  OrderDetailPage = 'OrderDetailPage',
  CreateReviewOrder = 'CreateReviewOrder',
  OrderCustomerDetail = 'OrderCustomerDetail',
  OrderDetail = 'OrderDetail',
  OrderDetailPageForShop = 'OrderDetailPageForShop',
  OrderShopDetail = 'OrderShopDetail',
  Review = 'Review',

  RegisterCateCriterionPage = 'RegisterCateCriterionPage',
  ListSvgPage = 'ListSvgPage',
  ProductShopPage = 'ProductShopPage',
  InforShopView = 'InforShopView',
  CetificateAchievementPage = 'CetificateAchievementPage',
  OperationalStaticDetailPage = 'OperationalStaticDetailPage',
  CreateSurveyStepPage = 'CreateSurveyStepPage',
  OperationalStatisticsActionForKtx = 'OperationalStatisticsActionForKtx',
  OperationalStatisticsDetail = 'OperationalStatisticsDetail',
  ListToiletCriterion = 'ListToiletCriterion',
  ShopInfo = 'ShopInfo',
  ManagePayment = 'ManagePayment',
  DetailNews = 'DetailNews',
  DetailBrand = 'DetailBrand',
  DetailTypicalProject = 'DetailTypicalProject',
  CompanyInfoPage = 'CompanyInfoPage',
}

export let svdata = [
  {
    RouteName: 'contact',
    Id: '59b185ae05214e91b007562f555385a3',
    Img: 'aa6c5e76c56b42e7af2e31b5eaff4287',
    Name: 'Tư vấn',
    name: RootScreen.contact,
    view: ServicesWorkFlow,
    isShow: false,
  },
  {
    RouteName: 'create',
    Id: 'b6e8c1b758894318a8c9bd631c6847bb',
    Img: '2e6b226241b24642b31d360298d7b884',
    Name: 'Xây mới',
    name: RootScreen.create,
    view: ServicesWorkFlow,
    isShow: false,
  },
  {
    RouteName: 'upgrade',
    Id: 'da9ad437b6514f42aac4808cad5dd46f',
    Img: '6fe1300562504ed9994885fdc30c31e6',
    Name: 'Cải tạo',
    name: RootScreen.upgrade,
    view: ServicesWorkFlow,
    isShow: false,
  },
  {
    RouteName: 'repair',
    Id: 'b9efa26164e94f0a875f20e370fcde0b',
    Img: '8bc89b0644bd4ce6abbeb48b130703bc',
    Name: 'Sửa chữa',
    name: RootScreen.repair,
    view: ServicesWorkFlow,
    isShow: false,
  },
  {
    RouteName: 'clean',
    Id: 'c6bf8cff25654b428d2ef6df82a25d95',
    Img: 'b0c3a33933ba4aa8addd37bf402f1159',
    Name: 'Vệ sinh',
    name: RootScreen.clean,
    view: ServicesWorkFlow,
    isShow: false,
  },
  {
    RouteName: 'edu',
    Id: '166381744eba4183abc861edf725f9a5',
    Img: '75255209c99b421f95d3fbf701d269c8',
    Name: 'Đào tạo',
    name: RootScreen.edu,
    view: ServicesWorkFlow,
    isShow: false,
  },
];

const Stack = createNativeStackNavigator();
export const navigationRef = createNavigationContainerRef();

export function navigate(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.navigate(routeName, params));
  }
}

export function navigateBack() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.goBack());
  }
}

export function navigateReset(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: routeName, params}],
      }),
    );
  }
}

// export default function Router() {
//     const [isReady, setIsReady] = useState(true);
//     const dispatch = useDispatch<any>()
//     const navigation = useNavigation<any>()
//     const serviceData = useSelectorCateServiceState().data
//     const [services, setServices] = useState<Array<any>>([])

//     const { data } = useSelectorCustomerState()

//     useEffect(() => {
//         if (serviceData?.length > 0) {
//             serviceData.forEach(item => {
//                 for (let index = 0; index < svdata.length; index++) {
//                     const element = svdata[index];
//                     if (element.RouteName.toLowerCase() === item.RouteName.toLowerCase()) {
//                         element.isShow = true;
//                         if (services.findIndex(x => x.RouteName === item.RouteName) === -1) {
//                             setServices([...services, element])
//                         }
//                     }
//                 }
//             })
//         }
//     }, [serviceData])

//     const stackServices = () => {
//         return services.map((item, index) => {
//             return <Stack.Screen key={item.RouteName} name={item.name} component={item.view} />
//         })
//     }

//     const rnBiometrics = new ReactNativeBiometrics({ allowDeviceCredentials: true })
//     useEffect(() => {
//         rnBiometrics.isSensorAvailable()
//             .then((resultObject) => {
//                 const { available, biometryType } = resultObject
//                 if (available && biometryType === BiometryTypes.TouchID) {
//                     console.log('TouchID is supported')
//                     saveDataToAsyncStorage("spBiometrics", "true")
//                     saveDataToAsyncStorage("biometryType", "TouchID")
//                 } else if (available && biometryType === BiometryTypes.FaceID) {
//                     console.log('FaceID is supported')
//                     saveDataToAsyncStorage("spBiometrics", "true")
//                     saveDataToAsyncStorage("biometryType", "FaceID")
//                 } else if (available && biometryType === BiometryTypes.Biometrics) {
//                     console.log('Biometrics is supported')
//                     saveDataToAsyncStorage("spBiometrics", "true")
//                     saveDataToAsyncStorage("biometryType", "true")
//                 } else {
//                     console.log('Biometrics not supported')
//                     saveDataToAsyncStorage("spBiometrics", "false")
//                     saveDataToAsyncStorage("biometryType", "false")
//                 }
//             })
//     }, [])

//     useEffect(() => {
//         ServiceActions.getCateServices(dispatch)

//     }, []);

//     return <Splash isAppReady={isReady}>
//         <TouchableNativeFeedback onPress={Keyboard.dismiss} style={{ flex: 1, width: "100%" }}>
//             <View style={{ flex: 1, width: "100%" }} >
//                 <FSnackbar />
//                 <Stack.Navigator initialRouteName={data ? RootScreen.navigateView : RootScreen.login} screenOptions={{ headerShown: false, orientation: "portrait" }} >
//                     <Stack.Screen name={RootScreen.navigateView} component={MainLayout} />
//                     <Stack.Screen name={RootScreen.login} component={Login} />
//                     <Stack.Screen name={RootScreen.ForgotPass} component={ForgotPass} />
//                     {/* notification */}
//                     <Stack.Screen name={RootScreen.NotificationIndex} component={NotificationIndex} />
//                     {/* services call */}
//                     {stackServices()}
//                     {/* services flow */}
//                     <Stack.Screen name={RootScreen.ServicesWorkFlow} component={ServicesWorkFlow} />
//                     <Stack.Screen name={RootScreen.WebViewServiceFlow} component={WebViewServiceFlow} />
//                     <Stack.Screen name={RootScreen.ContactFlow} component={ContactFlow} />
//                     <Stack.Screen name={RootScreen.CreateFlow} component={CreateFlow} />
//                     <Stack.Screen name={RootScreen.RepairFlow} component={RepairFlow} />
//                     <Stack.Screen name={RootScreen.CleanFlow} component={CleanFlow} />
//                     {/* screens */}
//                     <Stack.Screen name={RootScreen.detailProject} component={DetailProject} />
//                     {/* manages */}
//                     <Stack.Screen name={RootScreen.ToiletList} component={ToiletList} />
//                     <Stack.Screen name={RootScreen.ProductList} component={ProductList} />
//                     <Stack.Screen name={RootScreen.MaterialList} component={MaterialList} />
//                     <Stack.Screen name={RootScreen.MyTicketList} component={MyTicketList} />
//                     {/*  */}
//                     <Stack.Screen name={RootScreen.mapViewIndex} component={MapViewIndex} />
//                     {/* workplace */}
//                     <Stack.Screen name={RootScreen.DetailWorkView} component={DetailWorkView} />
//                     {/* profile setting */}
//                     <Stack.Screen name={RootScreen.SettingProfile} component={SettingProfile} />
//                     <Stack.Screen name={RootScreen.PolicyView} component={PolicyView} />
//                     <Stack.Screen name={RootScreen.FAQView} component={FAQView} />
//                     <Stack.Screen name={RootScreen.ProductView} component={TrustProductView} />
//                     <Stack.Screen name={RootScreen.DetailProduct} component={DetailProduct} />
//                     <Stack.Screen name={RootScreen.CompanyView} component={CompanyView} />
//                     <Stack.Screen name={RootScreen.ResultScanQrcode} component={ResultScanQrcode} />
//                     {/*  */}
//                 </Stack.Navigator>
//             </View>
//         </TouchableNativeFeedback>
//     </Splash>
// }
