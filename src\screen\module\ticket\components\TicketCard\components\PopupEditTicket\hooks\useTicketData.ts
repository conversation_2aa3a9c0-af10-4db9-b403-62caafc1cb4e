import {useState, useEffect} from 'react';
import {CustomerItem} from 'redux/reducers/user/da';
import {TicketDa} from '../../../../../ticketDa';
import {DataController} from 'screen/base-controller';
import {BaseDA} from 'screen/baseDA';
import {TicketType} from 'types/ticketType';
import {Ultis} from 'utils/Utils';
import {
  parseTicketDetails,
  extractCustomerIds,
} from '../../../../../utils/ticketDetailParser';

interface UseTicketDataProps {
  item: TicketType;
}

interface UseTicketDataReturn {
  customers: Array<CustomerItem>;
  files: Array<any>;
  details: Array<any>;
  loading: boolean;
}

export const useTicketData = ({
  item,
}: UseTicketDataProps): UseTicketDataReturn => {
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [files, setFiles] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(false);

  const details = item?.Detail ? parseTicketDetails(item?.Detail) : [];
  const ticketDa = new TicketDa();

  useEffect(() => {
    if (!item) return;

    const fetchTicketData = async () => {
      setLoading(true);
      try {
        // Use TicketDa to get ticket data
        const ticketData = await ticketDa.getById(item.Id);

        if (ticketData) {
          // Extract customer IDs from ticket details and main ticket
          const customerIds = extractCustomerIds(details);
          if (ticketData.CustomerId) customerIds.push(ticketData.CustomerId);

          if (customerIds.length > 0) {
            // Remove duplicates and filter out null/undefined
            const uniqueCustomerIds = [...new Set(customerIds)].filter(Boolean);

            // Use DataController to fetch customers
            const customerController = new DataController('Customer');
            const cusRes =
              await customerController.getByListId(uniqueCustomerIds);

            if (cusRes.code === 200) {
              const data = cusRes.data;
              const filteredData = data.filter(Boolean);
              if (!filteredData.length) return setCustomers([]);
              setCustomers(
                filteredData.map((e: any) => ({
                  ...e,
                  bgColor: Ultis.generateDarkColorRgb(),
                })),
              );
            }
          }

          // Fetch files data if ticket has files
          // Note: File property might be added by enrichment in TicketDa
          const ticketWithFiles = ticketData as any;
          if (ticketWithFiles.File?.length) {
            const fileRes = await BaseDA.getFilesInfor(
              ticketWithFiles.File.split(','),
            );
            if (fileRes.code === 200) {
              setFiles(
                fileRes.data.filter((e: any) => e !== undefined && e !== null),
              );
            }
          }
        }
      } catch (error) {
        console.error('Error fetching ticket data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTicketData();
  }, [item]);

  return {
    customers,
    files,
    details,
    loading,
  };
};
