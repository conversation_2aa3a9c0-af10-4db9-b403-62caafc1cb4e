import {StyleSheet, Dimensions} from 'react-native';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../assets/skin/colors';

const {width} = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const ListManageProductDetailStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
  },
  flatListContainer: {
    flex: 1,
  },
  flatListContentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Product item styles
  productItem: {
    marginTop: 10,
    marginBottom: 10,
    flex: 1,
  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 5,
    padding: 10,
    marginVertical: 10,
    borderBottomWidth: 0.3,
    borderColor: ColorThemes.light.infor_sub_color,
  },

  // Image container styles
  imageContainer: {
    position: 'relative',
  },
  productImage: {
    width: 64,
    height: 64,
    marginRight: 16,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: ColorThemes.light.primary_main_color,
  },
  discountBadge: {
    position: 'absolute',
    top: 4,
    left: 5,
    fontSize: 8,
    paddingHorizontal: 2,
    borderRadius: 2,
    backgroundColor: ColorThemes.light.secondary5_main_color,
    color: ColorThemes.light.secondary6_darker_color,
  },

  // Product info styles
  productInfo: {
    flex: 1,
    gap: 18,
  },
  productName: {
    ...TypoSkin.title3,
    fontWeight: '500',
    marginBottom: 8,
  },
  productInfoRow: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  // Rating styles
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    color: '#FFD700',
    marginRight: 5,
  },
  ratingIcon: {
    paddingRight: 6,
  },
  ratingValue: {
    ...TypoSkin.body3,
  },
  reviews: {
    color: '#000',
  },

  // Status styles
  statusPending: {
    fontSize: 15,
    color: '#f28C28',
  },
  statusViolation: {
    ...TypoSkin.body3,
    color: '#E53935',
  },

  // Review and sell info styles
  reviewSell: {
    flexDirection: 'row',
    display: 'flex',
    height: 90,
    paddingTop: 5,
    borderBottomWidth: 0.2,
    borderColor: ColorThemes.light.infor_sub_color,
  },
  statusContainer: {
    flexDirection: 'column',
    marginLeft: 20,
    gap: 11,
    alignItems: 'flex-start',
    flex: 1,
  },
  action: {
    display: 'flex',
    gap: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 3,
    paddingBottom: 3,
  },
  actionIcon: {
    marginRight: 10,
  },
  actionText: {
    ...TypoSkin.body3,
  },

  // Action buttons styles
  actionButtons: {
    flexDirection: 'row',
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 10,
  },
  actionButton: {
    padding: 5,
    borderRadius: 5,
    marginVertical: 5,
    width: 100,
    alignItems: 'center',
    borderColor: ColorThemes.light.primary_darker_color,
    borderWidth: 1,
  },
  deleteButton: {},
  actionButtonText: {
    color: '#00474f',
    fontSize: 12,
  },
});
