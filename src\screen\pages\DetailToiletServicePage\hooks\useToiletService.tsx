import {useState, useEffect, useCallback} from 'react';
import {useForm} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import {DataController} from '../../../base-controller';
import {
  ToiletServiceStatus,
  CateServicesType,
  AddendumType,
  TaskType,
  handleToiletDistribute,
  ContractStatus,
  ToiletStatus,
  TaskStatus,
} from '../../../module/service/components/da';
import {caculateTotalServicesValue} from '../../../module/workplace/components/form/PrintForm';
import {showSnackbar} from '../../../../component/export-component';
import {ComponentStatus} from '../../../../component/component-status';
import {randomGID} from '../../../../utils/Utils';
import {CompanyProfileActions} from '../../../../redux/reducers/company/reducer';
import {ToiletServiceItem} from '../../../../types/toiletServiceType';

// Type definitions
interface User {
  Id: string;
  [key: string]: any;
}

interface WorkData {
  Id: string;
  Status: number;
  Lat: number;
  Long: number;
  [key: string]: any;
}

interface UseToiletServiceProps {
  toiletServiceId: string;
  user: any;
}

export default function useToiletService({
  toiletServiceId,
  user,
}: UseToiletServiceProps) {
  const dispatch = useDispatch<any>();
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      devices: [],
      products: [],
      bioProducts: [],
      materials: [],
      tasks: [],
      materialPartner: [],
    },
  });

  const [workData, setWorkData] = useState<WorkData[] | null>(null);
  const [serviceData, setServiceData] = useState<ToiletServiceItem | null>(
    null,
  );
  const [index, setIndex] = useState(0);
  const [isRefreshing, setRefreshing] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [guest, setGuest] = useState<{data: any; company: any}>({
    data: null,
    company: null,
  });

  useEffect(() => {
    CompanyProfileActions.getKTXGroupInfor(dispatch);
    if (user && toiletServiceId) {
      getData().catch(console.error);
    }
  }, [user, toiletServiceId]);

  useEffect(() => {
    if (serviceData && workData) {
      getServicesData().catch(console.error);
    }
  }, [serviceData, workData]);

  useEffect(() => {
    const cateController = new DataController('CateServices');
    cateController
      .getAll()
      .then(res => methods.setValue('cateServices', res.data ?? []))
      .catch(console.error);
  }, [methods]);

  useEffect(() => {
    if (
      index === ToiletServiceStatus.consultant &&
      serviceData?.CateServicesId !== CateServicesType.contact
    ) {
      getServicesData().catch(console.error);
    }
  }, [index, serviceData?.CateServicesId]);

  useEffect(() => {
    if (serviceData) {
      getCustomerData().catch(console.error);
    }
  }, [serviceData]);

  const getData = async () => {
    if (!toiletServiceId) return;
    setLoading(true);

    try {
      const servicesController = new DataController('ToiletServices');
      const controller = new DataController('Toilet');

      const serviceRes = await servicesController.getById(toiletServiceId);
      if (serviceRes.code === 200 && serviceRes.data) {
        setServiceData(serviceRes.data);
        setTimeout(() => {
          updateIndexBasedOnStatus(serviceRes.data.Status);
        }, 100);
        if (serviceRes.data?.ToiletId?.split(',')) {
          const res = await controller.getByListId(
            serviceRes.data.ToiletId.split(','),
          );
          if (res.code === 200 && res.data) setWorkData(res.data);
        }
      } else {
        console.error('Failed to fetch service data:', serviceRes.message);
      }
    } catch (error) {
      console.error('Error in getData:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateIndexBasedOnStatus = (status: number) => {
    switch (status) {
      case ToiletServiceStatus.register:
      case ToiletServiceStatus.research:
        setIndex(ToiletStatus.register - 1);
        break;
      case ToiletServiceStatus.consultant:
      case ToiletServiceStatus.sendCompleteQuote:
        setIndex(ToiletStatus.consultant - 1);
        break;
      case ToiletServiceStatus.contract:
      case ToiletServiceStatus.sendCompleteContract:
        setIndex(ToiletStatus.contract - 1);
        break;
      case ToiletServiceStatus.design:
      case ToiletServiceStatus.sendCompleteDesign:
        setIndex(ToiletStatus.design - 1);
        break;
      case ToiletServiceStatus.build:
      case ToiletServiceStatus.sendCompleteBuild:
        setIndex(ToiletStatus.build - 1);
        break;
      case ToiletServiceStatus.liquid:
        setIndex(ToiletStatus.liquid - 1);
        break;
      default:
        break;
    }
  };

  const getServicesData = async () => {
    if (!serviceData) {
      console.warn('getServicesData called without serviceData');
      return;
    }

    setLoading(true);

    try {
      const contractController = new DataController('Addendum');
      const getLastContract = await contractController.getListSimple({
        page: 1,
        size: 1,
        query: `@ToiletServicesId:{*${serviceData.Id}*} @Type:[${AddendumType.contract} ${AddendumType.contract}]`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      let limitTime;
      if (getLastContract.code === 200 && getLastContract.totalCount) {
        limitTime = getLastContract.data[0].DateCreated;
      } else if (serviceData.Status >= ToiletServiceStatus.design) {
        limitTime = Date.now();
      }

      await Promise.all([
        loadDevicesAndProducts(limitTime),
        loadMaterials(limitTime),
        loadTasks(limitTime),
      ]);
    } catch (error) {
      console.error('Error in getServicesData:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadDevicesAndProducts = async (limitTime?: number) => {
    if (!serviceData) return;

    const deviceController = new DataController('Device');
    const res = await deviceController.aggregateList({
      page: 1,
      size: 1000,
      searchRaw: `@ToiletServicesId:{${serviceData.Id}}${limitTime ? ` @DateCreated:[${limitTime} +inf]` : ''}`,
      sortby: [{prop: 'DateCreated', direction: 'ASC'}],
    });

    const bioController = new DataController('BioProduct');
    const bioRes = await bioController.aggregateList({
      page: 1,
      size: 1000,
      searchRaw: `@ToiletServicesId:{*${serviceData.Id}*}${limitTime ? ` @DateCreated:[${limitTime} +inf]` : ''}`,
      sortby: [{prop: 'DateCreated', direction: 'ASC'}],
    });

    const productIds = [];

    if (bioRes.code === 200) {
      const bioList = bioRes.data
        .filter((e: any) => e.ProductId?.length)
        .map((e: any) => {
          e.Vat ??= 10;
          return e;
        });
      productIds.push(...bioList.map((e: any) => e.ProductId));
      methods.setValue('bioProducts', bioList);
    }

    if (res.code === 200) {
      const devices = res.data
        .filter((e: any) => e.ProductId?.length)
        .map((e: any) => {
          e.Vat ??= 10;
          return e;
        });
      productIds.push(...devices.map((e: any) => e.ProductId));
      methods.setValue('devices', devices);
    }

    if (productIds.length) {
      const productController = new DataController('Product');
      const productRes = await productController.getByListId(productIds);
      methods.setValue(
        'products',
        productRes.data.filter((e: any) => e !== undefined && e !== null),
      );
    }
  };

  const loadMaterials = async (limitTime?: number) => {
    if (!serviceData) return;

    const materialController = new DataController('MaterialToilet');
    const res = await materialController.aggregateList({
      page: 1,
      size: 1000,
      searchRaw: `@ToiletServicesId:{${serviceData.Id}}${limitTime ? ` @DateCreated:[${limitTime} +inf]` : ''}`,
      sortby: [{prop: 'DateCreated', direction: 'ASC'}],
    });

    if (res.code === 200) {
      const materialPartnerController = new DataController('Material');
      const materials = res.data.filter((e: any) => e.MaterialId?.length);
      const materialPartner = await materialPartnerController.getByListId(
        materials.map((e: any) => e.MaterialId),
      );
      methods.setValue(
        'materialPartner',
        materialPartner.data.filter((e: any) => e !== undefined && e !== null),
      );
      methods.setValue(
        'materials',
        res.data.filter((e: any) => e !== undefined && e !== null),
      );
    }
  };

  const loadTasks = async (limitTime?: number) => {
    if (workData && serviceData) {
      const taskController = new DataController('Task');
      const res = await taskController.aggregateList({
        page: 1,
        size: 1000,
        searchRaw: `@ToiletServicesId:{${serviceData.Id}}${limitTime ? ` @DateCreated:[${limitTime} +inf]` : ''} (-@Type:[${TaskType.consultant} ${TaskType.liquid}]) (-@ToiletId:{${workData[0].Id}})`,
        sortby: [{prop: 'DateCreated', direction: 'ASC'}],
      });

      if (res.code === 200)
        methods.setValue(
          'tasks',
          res.data.filter((e: any) => e !== undefined && e !== null),
        );
    }
  };

  const getCustomerData = async () => {
    if (!serviceData?.CustomerMobile) return;
    const customerController = new DataController('Customer');
    const res = await customerController.getListSimple({
      page: 1,
      size: 100,
      query: `@Mobile:(*${serviceData.CustomerMobile}*)`,
    });
    if (res.code === 200 && res.data[0]) {
      const tmp = res.data[0];
      let customerCompany;

      if (tmp.CompanyProfileId) {
        const companyController = new DataController('CompanyProfile');
        const companyRes = await companyController.getById(
          tmp.CompanyProfileId,
        );
        if (companyRes.code === 200) customerCompany = companyRes.data;
      }
      setGuest({data: tmp, company: customerCompany});
    }
  };

  const onChangeStatus = async (status: any, setServicesValue?: any) => {
    if (!serviceData) {
      console.warn('onChangeStatus called without required data');
      return;
    }

    try {
      const servicesController = new DataController('ToiletServices');

      // Update service status
      const serviceStatus = getServiceStatusFromToiletStatus(status);
      let updateServices = {...serviceData, Status: serviceStatus};

      if (setServicesValue) {
        updateServices.Value =
          (updateServices.Value ?? 0) +
          caculateTotalServicesValue({methods: methods});
      }

      const serviceRes = await servicesController.edit([updateServices]);
      if (serviceRes.code === 200) {
        setServiceData({...serviceData, Status: serviceStatus});
        if (status !== ToiletStatus.run) {
          setIndex(status - 1);
        }
      } else {
        console.error('Failed to update service status:', serviceRes.message);
      }
    } catch (error) {
      console.error('Error in onChangeStatus:', error);
    }
  };

  const getServiceStatusFromToiletStatus = (status: number) => {
    switch (status) {
      case ToiletStatus.consultant:
        return ToiletServiceStatus.consultant;
      case ToiletStatus.contract:
        return ToiletServiceStatus.contract;
      case ToiletStatus.design:
        return ToiletServiceStatus.design;
      case ToiletStatus.build:
        return ToiletServiceStatus.build;
      case ToiletStatus.liquid:
        return ToiletServiceStatus.liquid;
      case ToiletStatus.run:
        return ToiletServiceStatus.run;
      default:
        return status;
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([getData(), getServicesData()]);
    } catch (error) {
      console.error('Error in onRefresh:', error);
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  }, []);

  const handleReject = async () => {
    if (!workData || !serviceData) {
      console.warn('handleReject called without required data');
      return;
    }

    const cateIds = serviceData.CateServicesId?.split(',') || [];
    const closestConsultant = await handleToiletDistribute({
      lat: workData[0].Lat,
      long: workData[0].Long,
      toiletId: workData[0].Id,
      rejectCustomerId: serviceData.CustomerId,
      cateIds,
    });

    if (!closestConsultant)
      return showSnackbar({
        message: 'Không tìm thấy tư vấn viên khác!',
        status: ComponentStatus.ERROR,
      });

    const servicesController = new DataController('ToiletServices');
    const changeServiceStatus = await servicesController.edit([
      {...serviceData, Status: ToiletServiceStatus.reject},
    ]);

    if (changeServiceStatus.code !== 200)
      return showSnackbar({
        message: changeServiceStatus.message,
        status: ComponentStatus.ERROR,
      });

    // add new service
    const newServiceData = {
      ...serviceData,
      Id: randomGID(),
      CustomerId: closestConsultant.Id,
    };
    const res = await servicesController.add([newServiceData]);

    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });

    if (methods.watch('devices').length) {
      const deviceController = new DataController('Device');
      deviceController.edit(
        methods.watch('devices').map((e: any) => ({
          ...e,
          ToiletServicesId: newServiceData.Id,
        })),
      );
    }
  };

  const handleSubmitRegister = async () => {
    console.log('handleSubmitRegister', serviceData);

    if (!serviceData) {
      console.warn('handleSubmitRegister called without serviceData');
      return;
    }

    const addendumController = new DataController('Addendum');
    const quoteTemplateId = '7bf6069a5286414ca50b850c72e969dc';
    const newQuoteAddendum = {
      Id: randomGID(),
      Name: `BÁO GIÁ HỢP ĐỒNG`,
      DateCreated: Date.now(),
      Type: AddendumType.quote,
      Sort: 1,
      Status: ContractStatus.init,
      ToiletServicesId: serviceData.Id,
      DocumentsId: quoteTemplateId,
      CateServicesId: serviceData.CateServicesId,
    };

    const res = await addendumController.add([newQuoteAddendum]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });

    await onChangeStatus(ToiletStatus.consultant);

    const taskController = new DataController('Task');
    const now = new Date();

    const newTaskConsultant = {
      Id: randomGID(),
      Name: 'Tư vấn/Báo giá',
      DateCreated: now.getTime(),
      Type: TaskType.consultant,
      Status: TaskStatus.open,
      DateStart: now.getTime(),
      Day: 7,
      DateEnd: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 7,
      ).getTime(),
      CustomerId: methods.watch('AssigneeId'),
      ToiletServicesId: serviceData.Id,
      ToiletId: serviceData.ToiletId,
    };

    await taskController.add([newTaskConsultant]);
  };

  return {
    workData,
    serviceData,
    setServiceData,
    index,
    setIndex,
    isRefreshing,
    isLoading,
    guest,
    methods,
    onRefresh,
    onChangeStatus,
    getServicesData,
    handleReject,
    handleSubmitRegister,
  };
}
