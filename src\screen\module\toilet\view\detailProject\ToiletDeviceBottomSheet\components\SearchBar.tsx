import React from 'react';
import {StyleSheet, View} from 'react-native';
import {FTextField} from '../../../../../../../component/export-component';

interface Props {
  value: string;
  onChange: (v: string) => void;
  onSubmit: () => void;
}

export default function SearchBar({value, onChange, onSubmit}: Props) {
  return (
    <View style={styles.container}>
      <FTextField
        style={styles.textField}
        onChange={(vl: any) => onChange(vl)}
        value={value}
        onBlur={onSubmit}
        onSubmit={onSubmit}
        placeholder="Tìm kiếm thiết bị"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {flexDirection: 'row', width: '100%'},
  textField: {paddingHorizontal: 16, width: '100%'},
});
