import {StatusOrder} from '../config/Contanst';

export interface OrderProduct {
  Id: string;
  CustomerId: string;
  Name: string;
  ShopId: string;
  Code: string;
  DateCreated: number;
  DateUpdated: number;
  Status: StatusOrder;
  Value: number;
  AddressId: string;
  PaymentType: number; // 1: cod, 2: vnpay
  Description: string;
}

export interface OrderDetailItem {
  Id: string;
  Name: string;
  OrderId: string;
  ProductId: string;
  ProductName: string;
  ProductImage: string;
  ProductPrice: number;
  ProductQuantity: number;
  ProductTotal: number;
}
