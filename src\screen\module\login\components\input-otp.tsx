import React, {useState, useRef, useCallback, useEffect} from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

interface OTPInputProps {
  length?: number;
  height?: number;
  phone?: string;
  autoFocus?: boolean;
  disabled?: boolean;
  isResend?: boolean;
  onSubmit: (otp: string) => void;
  onReSendOtp?: () => void;
}

const OTPInput: React.FC<OTPInputProps> = ({
  length = 6,
  onSubmit,
  onReSendOtp,
  height = 48,
  phone,
  autoFocus,
  disabled = false,
  isResend = true,
}) => {
  // timer restart
  const [timer, setTimer] = useState(60);
  const timeOutCallback = useCallback(() => {
    setTimer(currTimer => currTimer - 1);
  }, []);

  useEffect(() => {
    if (disabled) {
      autoFocus = false;
    }
  }, [disabled]);

  useEffect(() => {
    if (timer > 0) setTimeout(timeOutCallback, 1000);
  }, [timer, timeOutCallback]);

  // State to hold OTP input
  const [otp, setOtp] = useState<string[]>(Array(length).fill(''));

  // Ref to manage focus for each TextInput
  const inputsRef = useRef<(TextInput | null)[]>([]);

  // Handle OTP change and move focus to next input
  const handleChange = (text: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Move to next input if it's filled
    if (text && index < length - 1) {
      inputsRef.current[index + 1]?.focus();
    }

    // Submit OTP if all fields are filled
    if (newOtp.join('').length === length) {
      onSubmit(newOtp.join(''));
      // Hide that keyboard!
      Keyboard.dismiss();
      // clear inputs
      setOtp(Array(length).fill(''));
    }
  };

  const reSendingOtp = () => {
    if (disabled) return;
    setTimer(60);
    setOtp(Array(length).fill(''));
    inputsRef.current[0]?.focus();
    if (onReSendOtp) onReSendOtp();
  };

  // Handle backspace key press to move focus to previous field
  const handleBackspace = (index: number) => {
    if (index > 0 && otp[index] === '') {
      inputsRef.current[index - 1]?.focus();
    }
  };

  return (
    <View style={styles.container} pointerEvents={disabled ? 'none' : 'auto'}>
      {disabled ? (
        <Text style={styles.disabledText}>
          Số điện thoại của bạn đã bị khóa, vui lòng liên hệ với quản trị viên
          để được hỗ trợ.
        </Text>
      ) : null}
      <View style={styles.otpContainer}>
        {Array.from({length}).map((_, index) => (
          <TextInput
            key={`otp${index}`}
            ref={el => (inputsRef.current[index] = el)}
            autoFocus={autoFocus && index === 0}
            style={[
              styles.input,
              {
                height: height,
                color: ColorThemes.light.neutral_text_title_color,
              },
            ]}
            textContentType="oneTimeCode"
            keyboardType="number-pad"
            returnKeyType="done"
            maxLength={1}
            value={otp[index]}
            onChangeText={text => handleChange(text, index)}
            onKeyPress={({nativeEvent}) => {
              if (nativeEvent.key === 'Backspace') {
                handleBackspace(index);
              }
            }}
          />
        ))}
      </View>
      {!isResend ? null : (
        <TouchableOpacity onPress={() => reSendingOtp()} disabled={timer > 0}>
          <Text
            style={[
              styles.resendText,
              {color: timer > 0 ? '#D4D4DDFF' : '#003580'},
            ]}>{`Gửi lại OTP ${timer > 0 ? timer : ''}${timer > 0 ? 's' : ''}`}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    gap: 16,
  },
  disabledText: {
    ...TypoSkin.body3,
    textAlign: 'center',
    color: ColorThemes.light.error_main_color,
    marginBottom: 32,
    marginTop: 16,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    width: 40,
    borderWidth: 1,
    borderColor: '#EAEAEC',
    borderRadius: 8,
    textAlign: 'center',
    fontSize: 20,
    fontFamily: 'Inter',
    marginHorizontal: 4,
  },
  resendText: {
    fontSize: 13,
    fontWeight: '600',
  },
});

export default OTPInput;
