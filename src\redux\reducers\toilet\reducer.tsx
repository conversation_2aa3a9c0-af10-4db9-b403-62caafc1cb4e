import { Dispatch, PayloadAction, UnknownAction, createSlice } from '@reduxjs/toolkit'
import { DataController } from '../../../screen/base-controller'

interface ToiletSimpleResponse {
    myToilet?: Array<{ [k: string]: any }>,
    onLoading?: boolean,
    type?: string
}

const initState: ToiletSimpleResponse = {
    myToilet: [],
    onLoading: true
}

export const toiletSlice = createSlice({
    name: 'toilet',
    initialState: initState,
    reducers: {
        handleActions: (state, action: PayloadAction<any>) => {
            switch (action.payload.type) {
                case 'GETALL':
                    state.myToilet = action.payload.data
                    break;
                case 'ADD':
                    state.myToilet = [...(state.myToilet ?? []), ...action.payload.data]
                    break;
                case "EDIT":
                    state.myToilet = state.myToilet?.map(e => {
                        const _tmp = action.payload.data.find((el: any) => el.Id === e.Id)
                        return _tmp ?? e
                    })
                    break;
                case "DELETE":
                    state.myToilet = state.myToilet?.filter(e => action.payload.data.every((id: string) => e.Id !== id))
                    break;
                default:
                    break;
            }
            state.onLoading = false
        },
        onFetching: (state) => {
            state.onLoading = true
        },
        onReset: (state) => {
            state.myToilet = undefined
        }
    },
})

const { handleActions, onFetching, onReset } = toiletSlice.actions

export default toiletSlice.reducer

export class ToiletActions {
    static getAll = async (dispatch: Dispatch<UnknownAction>, customerId: string) => {
        dispatch(onFetching())
        const controller = new DataController("Toilet")
        const res = await controller.aggregateList({ page: 1, size: 1000, searchRaw: `@CustomerId:{${customerId}}` })
        if (res.code === 200) {
            dispatch(handleActions({
                type: 'GETALL',
                data: res.data,
            }))
        }
    }

    static add = async (dispatch: Dispatch<UnknownAction>, toilets: Array<{ [k: string]: any }>) => {
        dispatch(onFetching())
        const controller = new DataController("Toilet")
        const res = await controller.add(toilets)
        if (res.code === 200) {
            dispatch(handleActions({
                type: 'ADD',
                data: toilets,
            }))
        }
    }

    static edit = async (dispatch: Dispatch<UnknownAction>, toilets: Array<{ [k: string]: any }>) => {
        dispatch(onFetching())
        const controller = new DataController("Toilet")
        const res = await controller.edit(toilets)
        if (res.code === 200) {
            dispatch(handleActions({
                type: 'EDIT',
                data: toilets,
            }))
        }
    }

    static delete = async (dispatch: Dispatch<UnknownAction>, toiletIds: Array<string>) => {
        dispatch(onFetching())
        const controller = new DataController("Toilet")
        const res = await controller.delete(toiletIds)
        if (res.code === 200) {
            dispatch(handleActions({
                type: 'DELETE',
                data: toiletIds,
            }))
        }
    }
}