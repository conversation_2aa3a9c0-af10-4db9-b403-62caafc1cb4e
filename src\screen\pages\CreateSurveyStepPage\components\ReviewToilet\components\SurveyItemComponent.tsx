import React from 'react';
import {Text, View, TouchableOpacity, StyleSheet} from 'react-native';
import {SurveyItemComponentProps} from '../types';
import Checkbox from './Checkbox';

const SurveyItemComponent: React.FC<SurveyItemComponentProps> = ({
  item,
  isChecked,
  onToggle,
  disabled = false,
}) => {
  const handlePress = () => {
    if (!disabled) {
      onToggle(item.Id);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.surveyItem]}
      onPress={handlePress}
      disabled={disabled}>
      <Checkbox
        isChecked={isChecked}
        onToggle={handlePress}
        disabled={disabled}
      />

      <View style={styles.itemContent}>
        <Text style={[styles.itemTitle, disabled && styles.disabledText]}>
          {item.Name}
        </Text>
        <Text style={[styles.itemDescription, disabled && styles.disabledText]}>
          {item.Content}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  surveyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  disabledText: {
    color: '#999',
  },
});

export default SurveyItemComponent;
