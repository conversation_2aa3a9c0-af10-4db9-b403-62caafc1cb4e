import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import CateCriterionDA from '../../../module/cateCriterion/cateCriterionDa';
import {CateCriterionItem} from '../../../../types/cateCriteriaType';

const ServiceSelection = ({
  onChoose,
}: {
  onChoose: (cateCriterion: CateCriterionItem) => void;
}) => {
  const [selectedService, setSelectedService] = useState<string>('');
  const [expandedService, setExpandedService] = useState<string | null>(null);
  const cateCriterionDA = new CateCriterionDA();
  const [data, setData] = React.useState<CateCriterionItem[]>([]);

  const initData = async () => {
    const data = await cateCriterionDA.getAllWithCriterion();
    setData(data);
    // Set first item as selected by default
    if (data.length > 0) {
      setSelectedService(data[0].Id);
      onChoose(data[0]);
    }
  };

  useEffect(() => {
    initData();
  }, []);

  const toggleExpand = (id: string | null) => {
    setExpandedService(id === expandedService ? null : id);
  };

  const handleChoose = (id: string) => {
    setSelectedService(id);
    const cateCriterion = data.find(item => item.Id === id);
    if (cateCriterion) {
      onChoose(cateCriterion);
    }
  };

  return (
    <View>
      <Text style={{...TypoSkin.heading9, marginVertical: 8}}>
        Chọn 1 trong {data.length} dịch vụ sau
      </Text>
      {data.map(service => (
        <View
          key={service.Id}
          style={[
            styles.serviceCard,
            selectedService === service.Id && styles.selectedCard,
          ]}>
          <TouchableOpacity style={{}} onPress={() => handleChoose(service.Id)}>
            <View style={styles.radioContainer}>
              <View style={styles.radioOuter}>
                {selectedService === service.Id && (
                  <View style={styles.radioInner} />
                )}
              </View>
              <Text style={styles.title}>{service.Name}</Text>
            </View>

            {service.Description && (
              <Text
                style={styles.description}
                numberOfLines={expandedService === service.Id ? undefined : 2}>
                {service.Description}
              </Text>
            )}
            {service?.Criterions &&
              service.Criterions.length > 0 &&
              expandedService === service.Id && (
                <View style={styles.criteriaContainer}>
                  <Text style={styles.criteriaTitle}>Tiêu chí:</Text>
                  <View style={styles.criteriaList}>
                    {service.Criterions.map((criterion, index) => (
                      <View key={criterion.Id} style={styles.criterionItem}>
                        <View style={styles.checkmark}>
                          <Text style={styles.checkmarkText}>✓</Text>
                        </View>
                        <Text style={styles.criterionText}>
                          {criterion.Name || `Tiêu chí ${index + 1}`}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

            {service?.Criterions && service.Criterions.length > 0 && (
              <TouchableOpacity
                style={styles.toggleButton}
                onPress={() => toggleExpand(service.Id)}>
                <Text style={styles.toggleButtonText}>
                  {expandedService === service.Id ? 'Ẩn bớt' : 'Xem chi tiết'}
                </Text>
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  serviceCard: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: ColorThemes.light.white,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_background,
    marginBottom: 20,
  },
  selectedCard: {
    backgroundColor: ColorThemes.light.primary_background,
    borderColor: ColorThemes.light.primary_border_color,
  },
  iconContainer: {
    marginLeft: 8,
  },
  radioContainer: {
    marginRight: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioOuter: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#95A5A6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  criteriaList: {
    flex: 1,
  },
  title: {
    ...TypoSkin.heading7,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  description: {
    marginTop: 12,
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 20,
  },
  criteriaContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#EAECEE',
  },
  criteriaTitle: {
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 10,
    fontSize: 15,
  },
  criterionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  checkmarkText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  criterionText: {
    flex: 1,
    fontSize: 14,
    color: '#34495E',
    lineHeight: 20,
  },
  toggleButton: {
    alignSelf: 'flex-end',
    marginTop: 10,
  },
  toggleButtonText: {
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
});

export default ServiceSelection;
