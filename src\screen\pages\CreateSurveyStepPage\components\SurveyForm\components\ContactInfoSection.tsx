import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {ContactInfoSectionProps} from '../types';
import {LABELS} from '../../../constants';
import iconSvg from '../../../../../../svgs/iconSvg';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {Winicon} from '../../../../../../component/export-component';
import {styles} from '../styles';
import {CustomerDropdownForm} from '../../../../../../component/dropdown/CustomerDropdown';
import {Controller} from 'react-hook-form';
import InfoDisplay from './InfoDisplay';

const ContactInfoSection: React.FC<ContactInfoSectionProps> = ({
  isEdit,
  control,
  errors,
  onCreateCustomer,
}) => {
  const handleAddToilet = () => {
    if (onCreateCustomer) {
      onCreateCustomer();
    }
  };

  return (
    <View style={styles.section}>
      <Text style={{...TypoSkin.title3}}>{LABELS.CONTACT_INFO_SECTION}</Text>

      <View
        style={[
          styles.locationRow,
          {
            marginTop: 12,
            borderBottomWidth: 1,
            paddingBottom: 12,
            borderColor: ColorThemes.light.primary_border_color,
          },
        ]}>
        <View style={styles.locationInput}>
          <CustomerDropdownForm
            control={control}
            name="Customer"
            errors={errors}
            placeholder="Khách hàng"
            disabled={!isEdit}
            typeCustomer={1}
            required={true}
          />
        </View>
        <TouchableOpacity
          style={[styles.btnAdd, !isEdit && {opacity: 0.5}]}
          onPress={isEdit ? handleAddToilet : undefined}
          disabled={!isEdit}>
          <Winicon
            src="outline/user interface/c-add"
            size={16}
            color={ColorThemes.light.white}
          />
          <Text style={styles.btnAddText}>Tạo mới KH</Text>
        </TouchableOpacity>
      </View>

      <Controller
        control={control}
        name="Customer"
        render={({field}) => (
          <>
            <InfoDisplay
              icon={iconSvg.address}
              label="Địa chỉ"
              value={field.value?.Address}
            />
            <InfoDisplay
              icon={iconSvg.phoneNumber}
              label="Số điện thoại"
              value={field.value?.Mobile}
            />
            <InfoDisplay
              icon={iconSvg.formEmail}
              label="Email"
              value={field.value?.Email}
            />
          </>
        )}
      />
    </View>
  );
};

export default ContactInfoSection;
