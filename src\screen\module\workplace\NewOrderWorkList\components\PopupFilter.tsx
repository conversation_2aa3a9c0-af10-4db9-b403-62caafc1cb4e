import React, {forwardRef, useEffect, useState} from 'react';
import {
  Dimensions,
  FlatList,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import {useSelectorCateServiceState} from '../../../../../redux/hooks/hooks';
import ScreenHeader from '../../../../layout/header';
import {FCheckbox, Winicon} from '../../../../../component/export-component';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {closePopup} from '../../../../../component/popup/popup';
import WScreenFooter from '../../../../layout/footer';

interface PopupFilterProps {
  filterMethods: any;
  onApply: (values: any) => void;
  selectedAttributes: any[];
  CateServicesId: any[];
  listStatus: any;
}

export const PopupFilter = forwardRef<any, PopupFilterProps>(
  function PopupFilter(
    {onApply, selectedAttributes, listStatus, filterMethods, CateServicesId},
    ref: any,
  ) {
    const [selected, setSelected] = useState<Array<any>>([]);
    const [selectedCate, setSelectedCate] = useState<Array<any>>([]);
    const cateServices = useSelectorCateServiceState().data;

    useEffect(() => {
      if (selectedAttributes.length) setSelected(selectedAttributes);
    }, [selectedAttributes.length]);

    useEffect(() => {
      if (CateServicesId.length) setSelectedCate(CateServicesId);
    }, [CateServicesId.length]);

    const handleCateServicePress = (itemId: string) => {
      if (!selectedCate.includes(itemId)) {
        setSelectedCate([...selectedCate, itemId]);
      } else {
        setSelectedCate(selectedCate.filter((id: any) => id !== itemId));
      }
    };

    const handleCateServiceChange = (itemId: string, value: boolean) => {
      if (value) {
        setSelectedCate([...selectedCate, itemId]);
      } else {
        setSelectedCate(selectedCate.filter((id: any) => id !== itemId));
      }
    };

    const handleStatusPress = (itemKey: string) => {
      if (!selected.includes(itemKey)) {
        setSelected([...selected, itemKey]);
      } else {
        setSelected(selected.filter((id: any) => id !== itemKey));
      }
    };

    const handleStatusChange = (itemKey: string, value: boolean) => {
      if (value) {
        setSelected([...selected, itemKey]);
      } else {
        setSelected(selected.filter((id: any) => id !== itemKey));
      }
    };

    const handleReset = () => {
      setSelected([]);
      setSelectedCate([]);
    };

    const handleApply = () => {
      closePopup(ref);
      onApply({AttributeId: selected, CateServicesId: selectedCate});
    };

    const renderCateServiceItem = ({
      item,
      index,
    }: {
      item: any;
      index: number;
    }) => (
      <TouchableOpacity
        key={item.Id}
        onPress={() => handleCateServicePress(item.Id)}
        style={styles.checkboxItem}>
        <FCheckbox
          value={selectedCate.includes(item.Id)}
          onChange={v => handleCateServiceChange(item.Id, v)}
        />
        <Text style={styles.checkboxText}>{item?.Name ?? '-'}</Text>
      </TouchableOpacity>
    );

    const renderStatusItem = ({item, index}: {item: any; index: number}) => (
      <TouchableOpacity
        key={item.key}
        onPress={() => handleStatusPress(item.key)}
        style={styles.checkboxItem}>
        <FCheckbox
          value={selected.includes(item.key)}
          onChange={v => handleStatusChange(item.key, v)}
        />
        <Text style={styles.checkboxText}>{item?.title ?? '-'}</Text>
      </TouchableOpacity>
    );

    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          style={styles.header}
          title={`Bộ lọc`}
          prefix={<View style={styles.headerPrefix} />}
          action={
            <View style={styles.headerAction}>
              <Winicon
                src="outline/layout/xmark"
                onClick={() => closePopup(ref)}
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </View>
          }
        />
        <ScrollView style={styles.scrollView}>
          <Text style={styles.sectionTitle}>Loại dịch vụ</Text>
          <FlatList
            data={cateServices}
            scrollEnabled={false}
            renderItem={renderCateServiceItem}
            numColumns={2}
            style={styles.flatList}
            contentContainerStyle={styles.flatListContent}
            keyExtractor={(item, index) => index.toString()}
          />
          <Text style={[styles.sectionTitle, styles.statusTitle]}>
            Trạng thái
          </Text>
          <FlatList
            data={listStatus}
            scrollEnabled={false}
            renderItem={renderStatusItem}
            numColumns={2}
            style={styles.flatList}
            contentContainerStyle={styles.flatListContent}
            keyExtractor={(item, index) => index.toString()}
          />
        </ScrollView>
        <WScreenFooter style={styles.footer}>
          <AppButton
            title={'Làm mới'}
            backgroundColor={ColorThemes.light.neutral_main_background_color}
            borderColor="transparent"
            containerStyle={styles.resetButton}
            onPress={handleReset}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
          />
          <AppButton
            title={'Áp dụng'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={styles.applyButton}
            onPress={handleApply}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 146,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    borderBottomColor: ColorThemes.light.neutral_main_background_color,
    borderBottomWidth: 0.5,
    shadowColor: 'rgba(0, 0, 0, 0.03)',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowRadius: 20,
    elevation: 20,
    shadowOpacity: 1,
  },
  headerPrefix: {
    width: 50,
  },
  headerAction: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    ...TypoSkin.label3,
    marginBottom: 8,
  },
  statusTitle: {
    marginTop: 16,
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '50%',
  },
  checkboxText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  flatList: {
    width: '100%',
  },
  flatListContent: {
    gap: 16,
  },
  footer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  resetButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
  applyButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
