import React from 'react';
import {ScrollView, View, StyleSheet, Pressable} from 'react-native';
import {CompanyProfileItem} from 'redux/reducers/company/da';
import {CustomerCompanyItem, CustomerRole} from 'redux/reducers/user/da';
import {CompanyInfoItem} from './CompanyInfoItem';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';

interface CompanyInfoViewProps {
  companyProfile: CompanyProfileItem;
  userRole: CustomerCompanyItem | undefined;
  onEdit: () => void;
}

export const CompanyInfoView: React.FC<CompanyInfoViewProps> = ({
  companyProfile,
  userRole,
  onEdit,
}) => {
  const isOwner = userRole?.Role?.includes(CustomerRole.Owner);

  return (
    <Pressable style={styles.wrapper}>
      <ScrollView style={styles.scrollContainer}>
        <Pressable style={styles.container}>
          <CompanyInfoItem
            label="Tên doanh nghiệp"
            value={companyProfile?.Name}
          />
          <CompanyInfoItem
            label="Ngành nghề"
            value={companyProfile?.Industry}
          />
          <CompanyInfoItem
            label="Số điện thoại"
            value={companyProfile?.Mobile}
          />
          <CompanyInfoItem label="Email" value={companyProfile?.Email} />
          <CompanyInfoItem label="Địa chỉ" value={companyProfile?.Address} />
          <CompanyInfoItem label="Mã số thuế" value={companyProfile?.TaxCode} />
          <CompanyInfoItem
            label="Số tài khoản"
            value={companyProfile?.BankAccount}
          />
          <CompanyInfoItem
            label="Tên chủ tài khoản"
            value={companyProfile?.BankAccountName}
          />
          <CompanyInfoItem
            label="Người đại diện"
            value={companyProfile?.Representative}
          />
          <CompanyInfoItem
            label="SĐT người đại diện"
            value={companyProfile?.Phone}
          />
          <CompanyInfoItem label="Chức vụ" value={companyProfile?.Position} />
          <CompanyInfoItem label="Ghi chú" value={companyProfile?.Note} />
        </Pressable>
      </ScrollView>
      {isOwner && (
        <View style={styles.footer}>
          <AppButton
            title="Sửa thông tin doanh nghiệp"
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={styles.editButton}
            onPress={onEdit}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  container: {
    padding: 16,
  },
  footer: {
    padding: 16,
    marginBottom: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  editButton: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
});
