import React, {useEffect} from 'react';
import {Platform, SafeAreaView, StatusBar} from 'react-native';
import {navigationRef} from './src/router/router';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';
import {store} from './src/redux/store/store';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {NavigationContainer} from '@react-navigation/native';
import {
  initNotificationPermission,
  registerListenerWithFCM,
} from './src/features/notifications/fcm/fcm_helper';
import {PaperProvider} from 'react-native-paper';
import notifee from '@notifee/react-native';
import {initPhoneOtp} from './src/features/otp-loginwFirebase/PhoneSignIn';
import {DrawerMain} from './src/screen/layout/navigation/drawerNavigation';

function App(): React.JSX.Element {
  useEffect(() => {
    notifee.setBadgeCount(0).then(() => console.log('Badge count removed'));
  }, []);

  /** setup firebase cloud message */
  useEffect(() => {
    initNotificationPermission();
  }, []);

  useEffect(() => {
    initPhoneOtp();
  }, []);

  // Setup status bar
  useEffect(() => {
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('transparent');
      }
    };
  }, []);

  useEffect(() => {
    const unsubscribe = registerListenerWithFCM();
    return unsubscribe;
  }, []);
  return (
    <Provider store={store} stabilityCheck="always">
      <PaperProvider>
        <GestureHandlerRootView style={{flex: 1}}>
          <SafeAreaProvider>
            <NavigationContainer ref={navigationRef}>
              <DrawerMain />
            </NavigationContainer>
          </SafeAreaProvider>
        </GestureHandlerRootView>
      </PaperProvider>
    </Provider>
  );
}

export default App;
