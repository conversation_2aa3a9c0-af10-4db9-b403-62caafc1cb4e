import {CateCriterionItem} from '../../../../../types/cateCriteriaType';

export interface RouteParams {
  type: ServiceType;
  serviceId?: string;
  customer?: any;
}

export interface ServicesWorkFlowProps {
  // Props interface for the main component
}

export interface ToiletItem {
  Id: string;
  Name: string;
  Address?: string;
  Status: number;
  CustomerId?: string;
}

export interface User {
  Id: string;
  Name?: string;
  Email?: string;
}

export interface Company {
  Id: string;
  Name?: string;
}

export interface UserRole {
  Role: string[];
}

export interface CustomerState {
  data: User | null;
  role: UserRole | null;
}

export interface ToiletState {
  myToilet: ToiletItem[];
  onLoading: boolean;
}

export interface CustomerCompanyState {
  data: Company | null;
}

export type ServiceType =
  | 'create'
  | 'repair'
  | 'upgrade'
  | 'clean'
  | 'edu'
  | 'contact'
  | 'netzero';

export interface ServiceConfig {
  title: string;
  subtitle: string;
  submitTitle: string;
  placeholder: string;
  thumbUrl: string;
  type: string;
  requiresToiletSelection?: boolean;
}

export interface UseServicesWorkFlowReturn {
  // Hook return type
  tab: number;
  setTab: (tab: number) => void;
  selected: ToiletItem | undefined;
  setSelected: (toilet: ToiletItem) => void;
  dataCriterion: CateCriterionItem[];
  popupRef: React.RefObject<any>;
  myToilet: any[];
  loadingToilet: boolean;
  user: any;
  routeParams: RouteParams;
  navigation: any;
}

export interface ServiceHeaderProps {
  onBack: () => void;
  onShowCriterion: () => void;
}

export interface ServiceContentProps {
  serviceType: ServiceType;
  serviceId?: string;
  customer?: any;
  selectedToilet?: ToiletItem;
}

export interface ToiletSelectorProps {
  toilets: ToiletItem[];
  selectedToilet?: ToiletItem;
  onSelectToilet: (toilet: ToiletItem) => void;
  onSkip: () => void;
  serviceType: ServiceType;
}

export interface CriterionPopupProps {
  data: CateCriterionItem[];
  onClose: () => void;
}
