import React, {useRef, useState} from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  ActivityIndicator,
  FlatList,
  Text,
  Pressable,
} from 'react-native';
import {Header, ToiletSelectionBottomSheet} from './components';
import {useStep2} from './hooks';
import {closePopup, FPopup, showPopup} from 'wini-mobile-components';
import {AddEditToiletPopup} from 'screen/module/toilet/components/form/AddEditToilet';
import ToiletSelectionCard from 'screen/module/toilet/components/card/ToiletSelectionCard';
import SelectToiletCriterionBottomSheet from './components/SelectToiletCriterionBottomSheet';
import ActionBottom from './components/ActionBottom';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';

interface ReviewToiletProps {
  customerId: string;
  customerPhone: string;
  surveyTaskId: string;
}

const ReviewToilet: React.FC<ReviewToiletProps> = ({
  customerId,
  customerPhone,
  surveyTaskId,
}) => {
  const popupRef = useRef<any>();
  const [isBsSelectToilet, setIsBsSelectToilet] = useState(false);
  const [isBsSelectCriterion, setIsBsSelectCriterion] = useState(false);
  const {
    cateCriterionData,
    toiletCustomer,
    toiletSelected,
    toiletCriterions,
    loading,
    handleAddNew,
    setSelectedToilets,
    selectedToilet,
    handleToiletPress,
    findToiletCriterionByToiletId,
    refreshToiletCriterions,
    markToiletAsUpdated,
  } = useStep2(customerId, surveyTaskId);

  const handleAddNvs = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <AddEditToiletPopup
          ref={popupRef}
          onDone={(data: any) => {
            handleAddNew(data);
            closePopup(popupRef);
          }}
          customerId={customerId}
          phone={customerPhone}
          toiletId={''}
        />
      ),
    });
  };

  const handleToiletCardPress = (toilet: any) => {
    handleToiletPress(toilet);
    setIsBsSelectCriterion(true);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#81C784" />
      </View>
    );
  }

  return (
    <Pressable style={styles.container}>
      <FPopup ref={popupRef} />
      <ScrollView style={styles.scrollContainer}>
        <Header
          title="Bảng đánh giá tiêu chuẩn Sạch - Xanh"
          subtitle="Lưu ý: Ấn Lưu ở mỗi lần khảo sát ở từng nhà vệ sinh"
          onAddNew={handleAddNvs}
          onSelectExisting={() => setIsBsSelectToilet(true)}
        />
        {toiletSelected.length > 0 && (
          <FlatList
            data={toiletSelected}
            scrollEnabled={false}
            ListHeaderComponent={() => {
              if (toiletSelected.length <= 0) return null;
              return (
                <ToiletSelectionCard
                  item={{
                    Id: 'all',
                    Name: 'Chọn tất cả',
                    Address: 'Chọn tất cả nhà vệ sinh',
                    CustomerId: '',
                    Status: 1,
                  }}
                  showSelect={false}
                  isSelected={true}
                  onPress={() =>
                    handleToiletCardPress({
                      Id: 'all',
                      Name: 'Chọn tất cả',
                      Address: 'Chọn tất cả nhà vệ sinh',
                      CustomerId: '',
                      Status: 1,
                      DateCreated: 0,
                      Certificate: 0,
                      Description: '',
                      Lat: 0,
                      Long: 0,
                      Mobile: '',
                    })
                  }
                />
              );
            }}
            renderItem={({item}) => (
              <ToiletSelectionCard
                item={item}
                showSelect={false}
                onPress={() => handleToiletCardPress(item)}
              />
            )}
            keyExtractor={item => item.Id}
            showsVerticalScrollIndicator={false}
            style={{marginBottom: 16}}
          />
        )}
        {toiletSelected.length <= 0 && (
          <View style={{marginTop: 50, alignItems: 'center'}}>
            <Text
              style={{
                ...TypoSkin.title3,
                width: '80%',
                textAlign: 'center',
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              Để tiếp tục hãy chọn nhà vệ sinh có sẵn hoặc tạo nhà vệ sinh mới
            </Text>
          </View>
        )}
      </ScrollView>

      <ActionBottom surveyTaskId={surveyTaskId} />

      <SelectToiletCriterionBottomSheet
        visible={isBsSelectCriterion}
        onClose={() => setIsBsSelectCriterion(false)}
        cateCriterionData={cateCriterionData}
        toiletId={selectedToilet?.Id || ''}
        toiletName={selectedToilet?.Name || ''}
        toiletCriterion={
          selectedToilet
            ? findToiletCriterionByToiletId(selectedToilet.Id)
            : null
        }
        surveyTaskId={surveyTaskId}
        onConfirmSuccess={async () => {
          // Refresh toilet criterions data after successful save
          await refreshToiletCriterions();
        }}
        toiletSelected={toiletSelected}
        toiletCriterions={toiletCriterions}
        markToiletAsUpdated={markToiletAsUpdated}
      />
      <ToiletSelectionBottomSheet
        visible={isBsSelectToilet}
        itemsSelected={toiletSelected}
        onClose={() => setIsBsSelectToilet(false)}
        data={toiletCustomer}
        onConfirm={data => {
          setSelectedToilets(data);
          setIsBsSelectToilet(false);
        }}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
    padding: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ReviewToilet;
