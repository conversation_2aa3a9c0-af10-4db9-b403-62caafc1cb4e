import React, {useState, useEffect} from 'react';
import BannerSection, {BannerItem} from './BannerSection';

const fakeBannerData: BannerItem[] = [
  {
    Id: '1',
    Img: 'https://picsum.photos/400/200?random=1',
    Name: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi đặc biệt tháng 7',
    Sort: 1,
    Type: 1,
    Position: 1,
  },
  {
    Id: '2',
    Img: 'https://picsum.photos/400/200?random=2',
    Name: 'Ưu đãi sinh viên mới',
    Sort: 2,
    Type: 1,
    Position: 1,
  },
  {
    Id: '3',
    Img: 'https://picsum.photos/400/200?random=3',
    Name: 'Ch<PERSON>ơng trình học bổng',
    Sort: 3,
    Type: 1,
    Position: 1,
  },
  {
    Id: '4',
    Img: 'https://picsum.photos/400/200?random=4',
    Name: 'Sự kiện văn hóa KTX',
    Sort: 4,
    Type: 1,
    Position: 1,
  },
  {
    Id: '5',
    Img: 'https://picsum.photos/400/200?random=5',
    Name: 'Thông báo quan trọng',
    Sort: 5,
    Type: 1,
    Position: 1,
  },
];

const DefaultBanner = () => {
  const [bannerData, setBannerData] = useState<BannerItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchBannerData = async () => {
      try {
        setLoading(true);

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // TODO: Replace with real API call when ready
        // const data = await bannerAction.find({
        //   page: 1,
        //   size: 5,
        //   searchRaw: `@Type: [${query.type}] @Position: [${query.position}]`,
        //   sortby: [{prop: 'DateCreated', direction: 'DESC'}],
        // });
        // setBannerData(
        //   data.sort((a: BannerItem, b: BannerItem) => a.Sort - b.Sort),
        // );
      } catch (error) {
        console.error('Error fetching banner data:', error);
        setBannerData([]);
      } finally {
        setLoading(false);
      }
    };

    // fetchBannerData();
  }, []);

  return <BannerSection bannerData={fakeBannerData} loading={loading} />;
};

export default DefaultBanner;
