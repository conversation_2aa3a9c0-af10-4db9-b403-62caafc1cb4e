import { <PERSON><PERSON><PERSON>, RefreshControl, View } from "react-native";
import ScreenHeader from "../../../layout/header";
import { useNavigation } from "@react-navigation/native";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useSelectorCustomerState, useSelectorCustomerCompanyState } from "../../../../redux/hooks/hooks";
import { FTextField, showSnackbar, Winicon } from "../../../../component/export-component";
import { ComponentStatus } from "../../../../component/component-status";
import { DataController } from "../../../base-controller";
import { ColorThemes } from "../../../../assets/skin/colors";
import { CardToiletHoriSkeleton } from "../../../../project-component/skeletonCard";
import EmptyPage from "../../../../project-component/empty-page";
import { FPopup } from "../../../../component/popup/popup";
import ProductCard from "../component/card/ProductCard";
import AppButton from "../../../../component/button";
import { CustomerRole } from "../../../../redux/reducers/user/da";
import WScreenFooter from "../../../layout/footer";

export default function ProductList() {
    const navigation = useNavigation<any>()
    const [managerData, setManagerData] = useState({ data: [], totalCount: undefined })
    const user = useSelectorCustomerState().data;
    const company = useSelectorCustomerCompanyState().data;
    const owner = useSelectorCustomerCompanyState().owner;
    const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
    const userRole = useSelectorCustomerState().role;
    const methods = useForm({ shouldFocusError: false });
    const quoteRef = useRef<any>();
    const popupRef = useRef<any>();
    const dialogRef = useRef<any>();
    const [isLoading, setLoading] = useState(false);
    const [isRefreshing, setRefreshing] = useState(false);
    const [listBrand, setListBrand] = useState({ data: [], totalCount: undefined })
    const [consume, setConsume] = useState({ data: [], totalCount: undefined })

    const getData = async ({ page, size }: any) => {
        setLoading(true)
        const controller = new DataController("Product")
        let _query = []
        if (userRole || user?.CompanyProfileId) _query.push(`(@CompanyProfileId:{${userRole?.CompanyProfileId}}) | (@CustomerId:{${user?.Id}})`)
        else _query.push(`(@CustomerId:{${user?.Id}})`)

        let bodyJson = {
            page: page ?? 1,
            size: size ?? 1000,
            searchRaw: _query.length ? _query.join(" ") : "*",
        }
        const res = await controller.aggregateList(bodyJson)
        // 
        if (res.code === 200) {
            setManagerData({ data: res.data, totalCount: res.totalCount })
            setLoading(false)
        } else {
            showSnackbar({ message: res.message, status: ComponentStatus.ERROR })
            setLoading(false)
        }
    }

    const getOthers = async () => {
        // thuong hieu
        const brandController = new DataController("Brands")
        // tieu thu
        const consumeController = new DataController("Consume")
        const res = await brandController.getListSimple({ page: 1, size: 1000, returns: ["Id", "Name", "ParentId"] })
        const resConsume = await consumeController.getListSimple({ page: 1, size: 1000, returns: ["Id", "Name"] })
        if (res.code === 200) setListBrand({ data: res.data, totalCount: res.totalCount })
        if (resConsume.code === 200) setConsume({ data: resConsume.data, totalCount: resConsume.totalCount })

    }

    useEffect(() => {
        if (user) getData({})
        getOthers()
    }, [user])

    const [searchValue, setSearchValue] = useState("")
    const filterMethods = useForm({ shouldFocusError: false })

    return <View style={{ flex: 1, height: "100%", width: "100%" }}>
        <FPopup ref={popupRef} />
        <ScreenHeader title="Quản lý sản phẩm" onBack={() => { navigation.pop() }} bottom={<View style={{ paddingHorizontal: 16, paddingBottom: 16 }}><FTextField style={{ paddingHorizontal: 16, width: "100%", height: 40 }} onChange={(vl) => {
            setSearchValue(vl.trim())
        }} returnKeyType="search" prefix={<Winicon src='outline/development/zoom' size={14} color={ColorThemes.light.neutral_text_subtitle_color} />} value={searchValue} placeholder="Tìm kiếm" />
        </View>} />
        <FlatList
            data={searchValue.length ? managerData.data.filter((e: any) => e.Name.toLowerCase().includes(searchValue.toLowerCase())) : managerData.data}
            refreshControl={
                <RefreshControl refreshing={isRefreshing} onRefresh={() => {
                    setRefreshing(true); getData({ page: 1, size: 10 }).then(() => {
                        setLoading(false)
                        setRefreshing(false)
                    })
                }} />
            }
            style={{ flex: 1, gap: 8, marginHorizontal: 16, marginVertical: 16 }}
            ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
            renderItem={({ item, index }: any) => <ProductCard key={index} index={index} item={item} user={user} listBrand={listBrand} consume={consume} setManagerData={setManagerData} />
            }
            ListEmptyComponent={() => isLoading ? Array.from(Array(10)).map((_, index) => <View key={index} style={{ gap: 16 }}>
                <CardToiletHoriSkeleton />
            </View>
            ) : <EmptyPage />}
            ListFooterComponent={() => <View style={{ height: 100 }} />}
        />
        {/* <WScreenFooter style={{ paddingHorizontal: 16 }}>
            <AppButton
                title={'Thêm sản phẩm'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={() => { }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
            />
        </WScreenFooter> */}
    </View>
}
