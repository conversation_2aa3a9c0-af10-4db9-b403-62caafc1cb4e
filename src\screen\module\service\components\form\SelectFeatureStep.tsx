import { FlatList, Pressable, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { ColorThemes } from "../../../../../assets/skin/colors";
import { TypoSkin } from "../../../../../assets/skin/typography";
import { ToiletPlace, ToiletType } from "../da";
import FRadioButton from "../../../../../component/radio-button/radio-button";
import { FRadioForm } from "../../../../../project-component/component-form";
import { useEffect, useState } from "react";

interface Props {
    methods?: any
    forConsultant?: boolean;
    display?: string;
}

const type1Data = [
    { value: ToiletType.forWorker, label: "Cho người lao động/Sản xuất" },
    { value: ToiletType.forOffice, label: "Văn phòng" },
    { value: ToiletType.forBuilding, label: "Chung cư/ Toà nhà" },
    { value: ToiletType.forFamily, label: "Hộ gia đình" },
]

const placeData = [
    { value: ToiletPlace.outDoor, label: "Ngoài trời" },
    { value: ToiletPlace.inDoor, label: "Gắn liền toà nhà" },
]

export default function SelectFeatureStep(props: Props) {
    const { methods, forConsultant = false, display = "flex" } = props;

    return <View style={{ flex: 1, padding: 16, display: display === "flex" ? 'flex' : 'none' }}>
        <Pressable
            style={{
                borderColor: 'transparent',
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
                marginBottom: 16
            }}>
            <Text
                style={[
                    TypoSkin.heading5,
                    {
                        color: ColorThemes.light.neutral_text_title_color,
                        paddingBottom: 4,
                    },
                ]}>
                Thông tin nhà vệ sinh
            </Text>
            <Text
                style={[
                    TypoSkin.body3,
                    {
                        color: ColorThemes.light.neutral_text_body_color,
                        paddingBottom: 8,
                    },
                ]}>
                Giúp chúng tôi hiểu rõ hơn mong muốn của bạn
            </Text>
        </Pressable>
        {/* <FeatureStep */}

        <ScrollView style={{}}>
            <View style={{ gap: 16 }}>
                <Text
                    style={[
                        TypoSkin.heading7,
                        {
                            color: ColorThemes.light.neutral_text_title_color,
                            paddingBottom: 4,
                        },
                    ]}>
                    Loại nhà vệ sinh
                </Text>
                {type1Data.map((item: any, i: number) => {
                    return <FRadioForm key={`type ${item.value}`} value={`${item.value}`} label={item.label} control={methods.control} name={`Type`} />
                })}
                <Text
                    style={[
                        TypoSkin.heading7,
                        {
                            color: ColorThemes.light.neutral_text_title_color,
                            paddingBottom: 4,
                        },
                    ]}>
                    Loại nhà vệ sinh
                </Text>
                {placeData.map((item: any, i: number) => {
                    return <FRadioForm key={`place ${item.value}`} value={`${item.value}`} label={item.label} control={methods.control} name={`Place`} />
                })}
            </View>
        </ScrollView>
        {/*  */}

    </View>
}
