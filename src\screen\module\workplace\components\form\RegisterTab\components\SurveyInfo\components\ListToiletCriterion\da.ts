import {DataController} from 'screen/base-controller';
import {CateCriterionItem} from 'types/cateCriteriaType';
import {CateCriterion} from 'types/cateCriterionType';
import {CriterionItem} from 'types/criterionType';
import {ToiletCriterionSurveyTask} from 'types/toiletCriterionSurveyTask';
import {ToiletCriterion} from 'types/toiletCriterionType';
import {ToiletItem} from 'types/toiletType';

class ListToiletCriterionDA {
  toiletController;
  toiletCriterionController;
  toiletServicesController;
  cateCriterionController;
  criterionController;
  toiletCertificateController;

  constructor() {
    this.toiletController = new DataController('Toilet');
    this.toiletCriterionController = new DataController(
      'ToiletServiceCriterion',
    );
    this.toiletServicesController = new DataController('ToiletServices');
    this.cateCriterionController = new DataController('CateCriterion');
    this.criterionController = new DataController('Criterion');
    this.toiletCertificateController = new DataController('ToiletCertificate');
  }

  async createToiletServiceCriterion(data: any[]) {
    const res = await this.toiletCriterionController.add(data);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }

  async getToiletCriterion(
    toiletServiceId: string,
    type?: number,
  ): Promise<{
    toilets: ToiletItem[];
    cateCriterions: CateCriterion[];
    criterions: CriterionItem[];
    toiletCriterions: ToiletCriterion[];
  } | null> {
    try {
      // Validate input
      if (!toiletServiceId) {
        throw new Error('toiletServiceId is required');
      }
      // Get toilet service
      const toiletServiceRes =
        await this.toiletServicesController.getById(toiletServiceId);
      if (toiletServiceRes.code !== 200) {
        throw new Error(
          `Failed to get toilet service: ${toiletServiceRes.message || 'Unknown error'}`,
        );
      }
      const toiletService = toiletServiceRes.data;

      // Validate toilet service data
      if (!toiletService.ToiletId || !toiletService.CateCriterionId) {
        throw new Error(
          'Invalid toilet service data: missing ToiletId or CateCriterionId',
        );
      }

      // Get toilets
      const toiletRes = await this.toiletController.getListSimple({
        query: `@Id:{${toiletService.ToiletId.split(',').join(' | ')}}`,
      });
      if (toiletRes.code !== 200) {
        throw new Error(
          `Failed to get toilets: ${toiletRes.message || 'Unknown error'}`,
        );
      }

      const toilets = toiletRes.data || [];

      // Get category criterion
      const cateCriterionRes = await this.cateCriterionController.getById(
        toiletService.CateCriterionId,
      );
      if (cateCriterionRes.code !== 200) {
        throw new Error(
          `Failed to get category criterion: ${cateCriterionRes.message || 'Unknown error'}`,
        );
      }

      const cateCriterion = cateCriterionRes.data;
      const cateCriterions = [cateCriterion];

      // Logic mới: Nếu cateCriterion.Sort > 1, lấy thêm các cateCriterion có Sort nhỏ hơn và criterions của chúng
      if (cateCriterion.Sort && cateCriterion.Sort > 1) {
        // Lấy tất cả cateCriterion có Sort nhỏ hơn Sort hiện tại
        const lowerCateCriterionRes =
          await this.cateCriterionController.getListSimple({
            query: `@Sort:[1 ${cateCriterion.Sort - 1}]`,
          });

        if (lowerCateCriterionRes.code === 200 && lowerCateCriterionRes.data) {
          const lowerCateCriterions = lowerCateCriterionRes.data;
          // Thêm các cateCriterion có Sort nhỏ hơn vào đầu mảng (để sắp xếp theo thứ tự Sort)
          cateCriterions.push(...lowerCateCriterions);
        }
      }

      // Get criterions cho tất cả cateCriterion (bao gồm cả cateCriterion gốc và các cateCriterion có Sort nhỏ hơn)
      const criterionRes = await this.criterionController.getListSimple({
        query: `@CateCriterionId:{${cateCriterions.map(cateCriterion => cateCriterion.Id).join(' | ')}}`,
      });
      if (criterionRes.code !== 200) {
        throw new Error(
          `Failed to get criterions: ${criterionRes.message || 'Unknown error'}`,
        );
      }

      const criterions = criterionRes.data || [];

      // Get toilet criterions
      const toiletCriterionRes =
        await this.toiletCriterionController.getListSimple({
          query: `@ToiletServicesId:{${toiletServiceId}} @Type: [${type || 1}]`,
        });

      if (toiletCriterionRes.code !== 200) {
        throw new Error(
          `Failed to get toilet criterions: ${toiletCriterionRes.message || 'Unknown error'}`,
        );
      }

      const toiletCriterions = toiletCriterionRes.data || [];
      return {
        toilets,
        cateCriterions: cateCriterions.sort((a, b) => a.Sort - b.Sort),
        criterions: criterions,
        toiletCriterions,
      };
    } catch (error) {
      console.error('Error in getToiletCriterion:', error);
      throw error; // Re-throw để component có thể handle
    }
  }
  async getCriterionsBytoiletId(toiletId: string) {
    try {
      const formattedToiletId = toiletId.includes(',')
        ? toiletId
            .split(',')
            .map(id => id.trim())
            .join(' | ')
        : toiletId.trim();

      // Thử nhiều format query khác nhau
      let toiletCriterionRes;

      // Thử format 1: @ToiletId:{id1 | id2}
      try {
        toiletCriterionRes = await this.toiletCriterionController.getListSimple(
          {
            page: 1,
            size: 100000,
            query: `@ToiletId:{${formattedToiletId}} @Type: [2]`,
          },
        );

        if (toiletCriterionRes.code === 200) {
          return toiletCriterionRes.data;
        }
      } catch (error) {
        console.log('Query format 1 failed:', error);
      }

      // Thử format 2: @ToiletId:(id1|id2)
      try {
        const orQuery = formattedToiletId.replace(/ \| /g, '|');
        console.log('Trying query format 2:', `@ToiletId:(${orQuery})`);
        toiletCriterionRes = await this.toiletCriterionController.getListSimple(
          {
            page: 1,
            size: 100000,
            query: `@ToiletId:(${orQuery})`,
          },
        );

        if (toiletCriterionRes.code === 200) {
          return toiletCriterionRes.data;
        }
      } catch (error) {
        console.log('Query format 2 failed:', error);
      }

      // Thử format 3: ToiletId:(id1|id2) - không có @
      try {
        const orQuery = formattedToiletId.replace(/ \| /g, '|');
        console.log('Trying query format 3:', `ToiletId:(${orQuery})`);
        toiletCriterionRes = await this.toiletCriterionController.getListSimple(
          {
            page: 1,
            size: 100000,
            query: `ToiletId:(${orQuery})`,
          },
        );

        if (toiletCriterionRes.code === 200) {
          return toiletCriterionRes.data;
        }
      } catch (error) {
        console.log('Query format 3 failed:', error);
      }

      // Nếu tất cả đều fail, return empty array
      console.log('All query formats failed');
      return [];
    } catch (error) {
      console.log('check-error', error);
    }
  }
  async updateToiletCriterion(data: any[]) {
    const res = await this.toiletCriterionController.edit(data);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }
  async createNewToiletCertificate(data: any[]) {
    const res = await this.toiletCertificateController.add(data);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }
}

export default ListToiletCriterionDA;
