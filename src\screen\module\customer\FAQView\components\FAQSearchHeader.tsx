import React, {useRef} from 'react';
import {StyleProp, View, ViewStyle} from 'react-native';
import {FTextField, Winicon} from '../../../../../component/export-component';
import {FPopup, showPopup} from '../../../../../component/popup/popup';
import AppButton from '../../../../../component/button';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import PopupFilter from './PopupFilter';

interface FAQSearchHeaderProps {
  style?: StyleProp<ViewStyle>;
  searchValue: string;
  onSearchChange: (value: string) => void;
  filterMethods: any;
  faqCategory: any[];
  onFilterApply: (attributeIds: any[]) => void;
}

const FAQSearchHeader: React.FC<FAQSearchHeaderProps> = ({
  style,
  searchValue,
  onSearchChange,
  filterMethods,
  faqCategory,
  onFilterApply,
}) => {
  const popupRef = useRef<any>();

  const styles = {
    container: {
      flexDirection: 'row' as const,
      width: '100%' as const,
      paddingHorizontal: 16,
      gap: 8,
      paddingBottom: 16,
      marginTop: 16,
    },
    searchField: {
      paddingHorizontal: 16,
      flex: 1,
      height: 40,
    },
    filterButton: {
      height: 40,
      borderRadius: 8,
      paddingHorizontal: 8,
    },
  };

  return (
    <View style={[styles.container, style]}>
      <FPopup ref={popupRef} />
      <FTextField
        style={styles.searchField}
        onChange={vl => {
          onSearchChange(vl.trim());
        }}
        value={searchValue}
        placeholder="Tìm kiếm"
        prefix={
          <Winicon
            src="outline/development/zoom"
            size={14}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        }
      />
      <AppButton
        onPress={() => {
          showPopup({
            ref: popupRef,
            enableDismiss: true,
            children: (
              <PopupFilter
                ref={popupRef}
                filterMethods={filterMethods}
                listStatus={faqCategory}
                selectedAttributes={filterMethods.watch('AttributeId') ?? []}
                onApply={onFilterApply}
              />
            ),
          });
        }}
        backgroundColor={ColorThemes.light.transparent}
        borderColor={
          filterMethods.watch('AttributeId')?.length
            ? ColorThemes.light.primary_main_color
            : ColorThemes.light.neutral_main_border_color
        }
        containerStyle={styles.filterButton}
        prefixIconSize={18}
        prefixIcon={'outline/user interface/setup-preferences'}
        textColor={
          filterMethods.watch('AttributeId')?.length
            ? ColorThemes.light.primary_main_color
            : ColorThemes.light.neutral_text_subtitle_color
        }
        title={'Phân loại'}
        textStyle={TypoSkin.subtitle3}
      />
    </View>
  );
};

export default FAQSearchHeader;
