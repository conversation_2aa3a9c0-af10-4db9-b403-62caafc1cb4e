import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {TicketStatus} from '../../../../service/components/da';
import {ColorThemes} from '../../../../../../assets/skin/colors';

const StatusTicketData = [
  {
    id: TicketStatus.init,
    name: '<PERSON><PERSON> mở',
    backgrColor: ColorThemes.light.infor_background,
    color: ColorThemes.light.infor_main_color,
  },
  {
    id: TicketStatus.processing,
    name: '<PERSON><PERSON> xử lý',
    backgrColor: ColorThemes.light.warning_background,
    color: ColorThemes.light.warning_main_color,
  },
  {
    id: TicketStatus.done,
    name: '<PERSON><PERSON><PERSON> thà<PERSON>',
    backgrColor: ColorThemes.light.success_background,
    color: ColorThemes.light.success_main_color,
  },
  {
    id: TicketStatus.cancel,
    name: 'Hủy',
    backgrColor: ColorThemes.light.neutral_disable_background_color,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  {
    id: TicketStatus.end,
    name: '<PERSON><PERSON><PERSON> thúc',
    backgrColor: ColorThemes.light.neutral_disable_background_color,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
];

interface TicketStatusProps {
  status: any;
}

export const Status: React.FC<TicketStatusProps> = ({status}) => {
  const st = StatusTicketData.find(e => e.id === status);

  if (!st) {
    return (
      <View style={styles.defaultContainer}>
        <Text style={styles.defaultText}>-</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, {backgroundColor: st.backgrColor}]}>
      <Text
        style={[
          styles.text,
          {color: st.color ?? ColorThemes.light.neutral_text_title_color},
        ]}>
        {st.name ?? status}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
  },
  defaultContainer: {
    backgroundColor: ColorThemes.light.neutral_text_subtitle_color,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
  },
  text: {},
  defaultText: {
    color: ColorThemes.light.neutral_absolute_background_color,
  },
});

export {StatusTicketData};
