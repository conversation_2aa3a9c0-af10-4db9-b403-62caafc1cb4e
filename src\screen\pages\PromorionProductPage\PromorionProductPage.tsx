/* eslint-disable react-native/no-inline-styles */
import React, {useEffect} from 'react';
import {View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {StyleSheet} from 'react-native';
import ManageItemProduct from '../../module/product/component/ManageItemProduct';
import TitleHeader from '../../layout/headers/TitleHeader';
import {store} from '../../../redux/store/store';
import ShopPromortionComponent from '../../module/partner/component/ShopPromortionComponent';

const PromorionProductPage = () => {
  return (
    <View style={styles.container}>
      <TitleHeader title="Quản lý khuyến mãi" />
      <ShopPromortionComponent />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_text_stable_color,
  },
});

export default PromorionProductPage;
