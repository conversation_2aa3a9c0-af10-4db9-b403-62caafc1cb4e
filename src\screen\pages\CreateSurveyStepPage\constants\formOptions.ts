// Form dropdown options for CreateSurveyStepPage

export interface SelectOption {
  id: number;
  label: string;
}

export const accessibilityOptions: SelectOption[] = [
  {id: 1, label: 'C<PERSON>'},
  {id: 2, label: 'Không'},
  {id: 3, label: '<PERSON>ột phần'},
];

export const cleaningProcessOptions: SelectOption[] = [
  {id: 1, label: 'Hàng ngày'},
  {id: 2, label: 'Hàng tuần'},
  {id: 3, label: 'Hàng tháng'},
  {id: 4, label: '<PERSON>hi cần thiết'},
];

export const treatmentTechnologyOptions: SelectOption[] = [
  {id: 1, label: 'Xử lý sinh học'},
  {id: 2, label: 'X<PERSON> lý hóa học'},
  {id: 3, label: 'Xử lý vật lý'},
  {id: 4, label: '<PERSON><PERSON> lý kết hợp'},
];

export const kitchenOptions: SelectOption[] = [
  {id: 1, label: 'Bếp gas'},
  {id: 2, label: '<PERSON>ế<PERSON> điện'},
  {id: 3, label: '<PERSON>ế<PERSON> từ'},
  {id: 4, label: 'Bếp củi'},
];

export const renewableEnergyOptions: SelectOption[] = [
  {id: 1, label: 'Năng lượng mặt trời'},
  {id: 2, label: 'Năng lượng gió'},
  {id: 3, label: 'Khí sinh học'},
  {id: 4, label: 'Không sử dụng'},
];
