import {useState} from 'react';
import {UsePaginationReturn} from '../types';

export const usePagination = (): UsePaginationReturn => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMoreData, setHasMoreData] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);

  return {
    currentPage,
    hasMoreData,
    loadingMore,
    setCurrentPage,
    setHasMoreData,
    setLoadingMore,
  };
};
