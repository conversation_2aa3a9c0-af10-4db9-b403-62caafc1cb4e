import React, {ReactNode, useEffect, useRef, useState} from 'react';
import {Animated, StyleSheet} from 'react-native';
import {CustomerActions} from '../../redux/reducers/user/reducer';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';

const LOADING_IMAGE = 'Loading image';
const FADE_IN_IMAGE = 'Fade in image';
const WAIT_FOR_APP_TO_BE_READY = 'Wait for app to be ready';
const FADE_OUT = 'Fade out';
const HIDDEN = 'Hidden';

const Splash = ({
  isAppReady,
  children,
}: {
  isAppReady: boolean;
  children: ReactNode;
}) => {
  const containerOpacity = useRef(new Animated.Value(1)).current;
  const imageOpacity = useRef(new Animated.Value(0)).current;

  const [state, setState] = useState<string>(LOADING_IMAGE);
  const dispatch = useDispatch<any>();
  const navigation = useNavigation<any>();

  useEffect(() => {
    getDataToAsyncStorage('accessToken').then(_token => {
      if (_token) {
        CustomerActions.getInfor(dispatch, navigation);
      }
    });
  }, []);

  useEffect(() => {
    if (state === FADE_IN_IMAGE) {
      Animated.timing(imageOpacity, {
        toValue: 1,
        duration: 1000, // Fade in duration
        useNativeDriver: true,
      }).start(async () => {
        setState(WAIT_FOR_APP_TO_BE_READY);
      });
    }
  }, [imageOpacity, state]);

  useEffect(() => {
    if (state === WAIT_FOR_APP_TO_BE_READY) {
      if (isAppReady) {
        setState(FADE_OUT);
      }
    }
  }, [isAppReady, state]);

  useEffect(() => {
    if (state === FADE_OUT) {
      Animated.timing(containerOpacity, {
        toValue: 0,
        duration: 1000, // Fade out duration
        delay: 1000, // Minimum time the logo will stay visible
        useNativeDriver: true,
      }).start(() => {
        setState(HIDDEN);
      });
    }
  }, [containerOpacity, state]);

  return state === HIDDEN ? (
    children
  ) : (
    <Animated.View
      collapsable={false}
      style={[style.container, {opacity: containerOpacity}]}>
      <Animated.Image
        source={require('../../assets/logo.png')}
        fadeDuration={0}
        onLoad={() => {
          setState(FADE_IN_IMAGE);
        }}
        style={[style.image, {opacity: imageOpacity}]}
        resizeMode="contain"
      />
    </Animated.View>
  );
};

const style = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  image: {
    width: 100,
    height: 100,
  },
});

export default Splash;
