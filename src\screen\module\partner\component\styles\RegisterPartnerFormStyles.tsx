import {StyleSheet, Dimensions} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';

const {width} = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const RegisterPartnerFormStyles = StyleSheet.create({
  // css phần manage_shop
  containerManageShop: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  navigaterManageShop: {
    flex: 1,
    width: '100%',
  },
  footerManageShop: {
    height: 85,
  },
  // css phần haveshop
  containerHaveShop: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 16,
  },
  navBarHaveShop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 6,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginBottom: 10,
    width: '100%',
    marginVertical: 10,
  },
  ShopMoney: {display: 'flex', marginTop: 8, gap: 12},
  ShopMoneyContent: {
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 10,
    borderWidth: 0.3,
    borderColor: '#1890FF4D',
  },
  ShopMoneyTitle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  ShopMoneyTitleText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '700',
    paddingTop: 8,
  },
  ShopMoneyTitleTextNumber: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    paddingTop: 8,
  },
  ShopMoneyTitleButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 5,
    width: 64,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginRight: 10,
    marginBottom: 10,
    zIndex: 1999,
  },
  ShopMoneyTitleButtonText: {
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  //css phần notshop
  containetNotShop: {
    flex: 1,
    alignItems: 'center',
  },
  illustrationNotShop: {
    width: 231,
    height: 203.54,
  },
  descriptionNotShop: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginTop: 50,
  },
  boldTextNotShop: {
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  buyButtonNotShop: {
    backgroundColor: ColorThemes.light.primary_main_color,
    width: 325,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 25,
    height: 50,
    borderRadius: 30,
    marginBottom: 30,
  },
  actionButtonTextNotShop: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
  },
  // css phần register_shop
  containerRegisterShop: {
    flex: 1,
    backgroundColor: ColorThemes.light.secondary1_background,
  },
  // css menushop
  containerMenuShop: {
    marginVertical: 16,
    marginHorizontal: 16,
  },
  //css header shop
  containerHeaderShop: {
    maxHeight: 70,
  },
  headerShopImage: {width: '100%', height: 120},
  // css inputShopInfo
  containerInputShopInfo: {
    paddingHorizontal: 16,
    paddingTop: 16,
    backgroundColor: ColorThemes.light.neutral_text_stable_color,
    height: '100%',
  },
  inputContainer: {
    width: '100%',
    gap: 24,
    marginBottom: 100,
  },
  selectMapInputShopInfo: {
    flexDirection: 'row',
    height: 32,
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buyButtonInputShopInfo: {
    backgroundColor: ColorThemes.light.primary_main_color,
    width: '80%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
    margin: 'auto',
    marginTop: 20,
    marginBottom: 30,
  },
  buyActionButtonInputShopInfo: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  textFieldStyleInputShopInfo: {
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
    borderWidth: 0,
    borderBottomWidth: 0.8,
    borderBlockColor: ColorThemes.light.primary_main_color,
  },
});
