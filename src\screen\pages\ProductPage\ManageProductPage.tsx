/* eslint-disable react-native/no-inline-styles */
import React, {useEffect} from 'react';
import {View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {StyleSheet} from 'react-native';
import ManageItemProduct from '../../module/product/component/ManageItemProduct';
import TitleHeader from '../../layout/headers/TitleHeader';
import {store} from '../../../redux/store/store';

const ManageProductPage = () => {
  return (
    <View style={styles.container}>
      <TitleHeader title="Sản phẩm của tôi" />
      <ManageItemProduct />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_text_stable_color,
  },
});

export default ManageProductPage;
