import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Pressable,
  Platform,
} from 'react-native';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {ColorThemes} from '../../../../assets/skin/colors';
import GeneralInformationForm from './form/GeneralInformationForm';
import RepresentativeInformationForm from './form/RepresentativeInformationForm';
import {RegisterPartnerFormStyles} from './styles/RegisterPartnerFormStyles';
import {useForm} from 'react-hook-form';
import BankInformationForm from './form/BankInformationForm';
import {PartnerDa} from '../partnerDa';
import {store} from '../../../../redux/store/store';
import {CustomerRole, CustomerType} from '../../../../redux/reducers/user/da';
import {useNavigation} from '@react-navigation/native';
import FLoading from '../../../../component/Loading/FLoading';
import {showSnackbar} from '../../../../component/export-component';
import {ComponentStatus} from '../../../../component/component-status';
import AllServices from '../../service/services';
import {randomGID} from '../../../../utils/Utils';
import {useSelectorCustomerState} from 'redux/hooks/hooks';

interface TabMenuProps {
  activeTab: string;
  onTabPress: (tab: string) => void;
}
const TabMenu: React.FC<TabMenuProps> = ({activeTab, onTabPress}) => {
  const tabs = [
    {key: 'info', label: 'Thông tin đối tác'},
    {key: 'services', label: 'Dịch vụ shop'},
  ];

  return (
    <View style={styles.tabContainer}>
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabButton,
            activeTab === tab.key && styles.activeTabButton,
          ]}
          onPress={() => onTabPress(tab.key)}>
          <Text
            style={[
              styles.tabText,
              activeTab === tab.key && styles.activeTabText,
            ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const ShopInfo: React.FC<{select?: any}> = ({}) => {
  const [activeTab, setActiveTab] = useState('info');
  const methods = useForm({shouldFocusError: false});
  const [getDataCompany, setGetDataCompany] = useState<any>();
  const [bankData, setBankData] = useState<any[]>([]);
  const [dataShop, setDataShop] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [dataCateService, setDataCateService] = useState<any[]>([]);
  const [haveShop, setHaveShop] = useState(false);
  const navigation = useNavigation<any>();
  const cusInfo = store.getState().customer.data;
  const partnerDa = new PartnerDa();
  const userRole = useSelectorCustomerState().role;
  const getCompanyInfo = async () => {
    const response = await partnerDa.getInforCompanyById(
      cusInfo?.CompanyProfileId as string,
    );
    if (response.code === 200) {
      setGetDataCompany(response.data);
    }
  };
  const getBank = async () => {
    const res = await partnerDa.getBank();
    if (res.code === 200 && res.data.length) setBankData(res.data);
    else setBankData([]);
  };
  const getInfoShop = async () => {
    const response = await partnerDa.GetShopById(cusInfo?.Id as string);
    if (response.code === 200) {
      let dataCate = response.data[0]?.CateServicesId?.split(',')?.map(
        (item: any) => {
          return {
            Id: item,
          };
        },
      );
      if (response.data.length > 0) {
        setHaveShop(true);
      } else {
        setHaveShop(false);
      }
      setDataShop(response.data);
      setDataCateService(dataCate);
      setLoading(false);
    }
  };

  useEffect(() => {
    getInfoShop();
  }, [activeTab]);

  useEffect(() => {
    setLoading(true);
    getInfoShop();
    if (cusInfo?.CompanyProfileId && cusInfo?.Type !== CustomerType.partner)
      getCompanyInfo();
  }, [cusInfo]);

  // Set dữ liệu mặc định cho tất cả các trường input từ dataShop
  useEffect(() => {
    console.log('check-dataShop', dataShop);
    if (dataShop && dataShop.length > 0) {
      // Lấy item đầu tiên nếu dataShop là array
      const shopData = Array.isArray(dataShop) ? dataShop[0] : dataShop;

      // Thông tin chung (GeneralInformationForm)
      methods.setValue('Name', shopData.Name);
      methods.setValue('ShortName', shopData.ShortName);
      methods.setValue('TaxCode', shopData.TaxCode);
      methods.setValue('Mobile', shopData.GroupPhone);
      methods.setValue('CompanyMail', shopData.GroupEmail);
      methods.setValue('Address', shopData.GroupAddress);
      // add lat log
      methods.setValue('Lat', shopData.Lat);
      methods.setValue('Long', shopData.Long);
      methods.setValue('Industry', shopData.BusinessAreas);
      methods.setValue('NoteShort', shopData.Description);
      methods.setValue('Representative', shopData.RepresentativeUser);
      methods.setValue('Phone', shopData.RepresentativePhone);
      methods.setValue('Email', shopData.RepresentativeEmail);
      methods.setValue('Position', shopData.RepresentativePosition);
      methods.setValue('BankId', shopData.BankId);
      methods.setValue('BankAccount', shopData.BankAccount);
      methods.setValue('BankAccountName', shopData.BankAccountOwner);
    }
  }, [dataShop, methods]);

  useEffect(() => {
    getBank();
  }, []);

  const renderContent = () => {
    switch (activeTab) {
      case 'info':
        return (
          <View style={styles.contentContainer}>
            <FLoading visible={loading} />
            <ScrollView
              style={[RegisterPartnerFormStyles.containerInputShopInfo]}>
              <Pressable>
                <KeyboardAvoidingView
                  behavior={'padding'}
                  keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
                  style={RegisterPartnerFormStyles.inputContainer}>
                  <GeneralInformationForm
                    methods={methods}
                    getDataCompany={getDataCompany}
                  />
                  <RepresentativeInformationForm
                    methods={methods}
                    getDataCompany={getDataCompany}
                  />
                  <BankInformationForm
                    methods={methods}
                    BankData={bankData}
                    getDataCompany={getDataCompany}
                  />
                </KeyboardAvoidingView>
              </Pressable>
            </ScrollView>
          </View>
        );
      case 'services':
        return (
          <Pressable style={{...styles.contentContainer, padding: 16}}>
            <FLoading visible={loading} />
            <Text style={styles.contentTitle}>Dịch vụ</Text>
            <AllServices
              selectedCate={dataCateService}
              setSelectedCate={setDataCateService}
              createService={true}
            />
          </Pressable>
        );
      default:
        return null;
    }
  };

  const ButtonHandle = () => {
    const handleExit = () => {
      navigation.goBack();
    };

    const handleUpdate = async (data: any) => {
      try {
        if (activeTab == 'info') {
          console.log('check-data', data);
          setLoading(true);
          // Validation các trường bắt buộc
          if (!data?.Name || !data?.Mobile || !data?.Address) {
            showSnackbar({
              message:
                'Vui lòng điền đầy đủ thông tin bắt buộc (Tên, Số điện thoại, Địa chỉ)',
              status: ComponentStatus.ERROR,
            });
            setLoading(false);
            return;
          }

          if (!cusInfo?.Id) {
            showSnackbar({
              message:
                'Không tìm thấy thông tin khách hàng. Vui lòng đăng nhập lại.',
              status: ComponentStatus.ERROR,
            });
            setLoading(false);
            return;
          }
          // Lấy Thông tin đối tác hiện tại
          const currentShop = Array.isArray(dataShop) ? dataShop[0] : '';
          // Chuẩn bị dữ liệu cập nhật
          const updateData = {
            Id: haveShop ? currentShop.Id : randomGID(),
            Name: data?.Name,
            DateCreated: haveShop
              ? currentShop?.DateCreated
              : new Date().getTime(),
            DateUpdated: new Date().getTime(),
            CustomerId: cusInfo?.Id,
            CompanyProfileId: cusInfo?.CompanyProfileId,
            // Thông tin chung của shop
            ShortName: data?.ShortName,
            GroupEmail: data?.CompanyMail,
            GroupPhone: data?.Mobile,
            GroupAddress: data?.Address,
            Lat: data?.Lat,
            Long: data?.Long,
            BusinessAreas: data?.Industry,
            Description: data?.NoteShort,
            TaxCode: data?.TaxCode,
            // Thông tin người đại diện
            RepresentativeUser: data?.Representative,
            RepresentativePhone: data?.Phone,
            RepresentativeEmail: data?.Email,
            RepresentativePosition: data?.Position,
            // Thông tin ngân hàng
            BankAccount: data?.BankAccount,
            BankAccountOwner: data?.BankAccountName,
            BankId: data?.BankId,
            // Các trường bắt buộc
            IsAgreeTerm: true,
            Status: haveShop ? currentShop?.Status : 1,
            CateServicesId: haveShop ? currentShop?.CateServicesId : null, // Giữ nguyên category services nếu có
          };

          console.log('check-updateData before API call:', updateData);

          let response;
          // Gọi API cập nhật
          if (haveShop) {
            response = await partnerDa.updateShopData(updateData);
          } else {
            response = await partnerDa.registerPartner(updateData);
          }
          console.log('check-API response:', response);

          if (response && response.code === 200) {
            showSnackbar({
              message: haveShop
                ? 'Cập nhật Thông tin đối tác thành công!'
                : 'Tạo mới shop thành công!',
              status: ComponentStatus.SUCCSESS,
            });

            // Refresh lại dữ liệu shop
            await getInfoShop();

            // Nếu là tạo mới và có CompanyProfileId, cập nhật customer info
            if (
              !haveShop &&
              cusInfo &&
              response.data &&
              response.data.length > 0
            ) {
              // Có thể cần cập nhật customer với shop ID mới tạo
              console.log('New shop created with ID:', response.data[0].Id);
            }

            navigation.goBack();
          } else {
            console.error('API Error:', response);
            showSnackbar({
              message:
                response?.message ||
                `Có lỗi xảy ra khi ${haveShop ? 'cập nhật' : 'tạo mới'} shop`,
              status: ComponentStatus.ERROR,
            });
          }
        } else if (activeTab == 'services') {
          // Validate dataCateService
          if (!dataCateService || dataCateService.length === 0) {
            showSnackbar({
              message: 'Vui lòng chọn ít nhất một dịch vụ',
              status: ComponentStatus.ERROR,
            });
            setLoading(false);
            return;
          }
          let shopData = Array.isArray(dataShop) ? dataShop[0] : dataShop;

          if (!shopData?.Id) {
            showSnackbar({
              message:
                'Không tìm thấy Thông tin đối tác để cập nhật , vui lòng tạo shop trước khi cập nhật dịch vụ',
              status: ComponentStatus.ERROR,
            });
            setLoading(false);
            return;
          }

          let datamap = dataCateService.map((e: any) => e.Id);

          // Get current shop data to update
          const currentShop = Array.isArray(dataShop) ? dataShop[0] : dataShop;

          const response = await partnerDa.updateShopData({
            ...currentShop,
            CateServicesId: datamap.join(','),
          });

          if (response && response.code === 200) {
            showSnackbar({
              message: 'Cập nhật dịch vụ thành công!',
              status: ComponentStatus.SUCCSESS,
            });

            // Refresh shop data to get updated information
            await getInfoShop();
            navigation.goBack();
          } else {
            showSnackbar({
              message:
                response?.message || 'Có lỗi xảy ra khi cập nhật dịch vụ',
              status: ComponentStatus.ERROR,
            });
          }
        }
      } catch (error) {
        console.error('Error updating shop:', error);
        showSnackbar({
          message: 'Có lỗi xảy ra khi cập nhật thông tin',
          status: ComponentStatus.ERROR,
        });
      } finally {
        setLoading(false);
      }
    };

    return (
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.exitButton} onPress={handleExit}>
          <Text style={styles.exitButtonText}>Thoát</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.updateButton}
          onPress={methods.handleSubmit(handleUpdate)}>
          <Text style={styles.updateButtonText}>
            {' '}
            {haveShop ? 'Cập nhật' : 'Tạo mới'}{' '}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <TitleHeader title={'Thông tin đối tác'} />
      <TabMenu activeTab={activeTab} onTabPress={setActiveTab} />
      {renderContent()}
      {!userRole?.Role?.includes(CustomerRole.Owner) ? null : <ButtonHandle />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTabButton: {
    backgroundColor: ColorThemes.light.secondary2_sub_color,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_body_color,
  },
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
  contentText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_body_color,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 20,
    gap: 12,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  exitButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  exitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_body_color,
  },
  updateButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: ColorThemes.light.secondary2_sub_color,
  },
  updateButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default ShopInfo;
