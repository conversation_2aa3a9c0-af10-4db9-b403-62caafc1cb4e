import React, {useEffect, useCallback, memo, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ImageBackground,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import {ColorThemes} from '../../../assets/skin/colors';
import iconSvg from '../../../svgs/iconSvg';
import AppSvg from '../../../component/AppSvg';
import {ComponentStatus} from '../../../component/component-status';
import {showDialog} from '../../../component/export-component';
import {useSelectorCustomerState} from '../../../redux/hooks/hooks';
import SearchInput from '../../../component/textfield/SearchInput';
import {PopupQrcodeScan} from '../../../features/qrcode-scanner/qrcode-scan';
import {FPopup, showPopup} from '../../../component/popup/popup';
// Types
interface TitleHeaderProps {
  title?: string;
  showSearch?: boolean;
  onSearch?: (value: string) => void;
  showActions?: boolean;
  onBack?: () => void;
  actionView?: React.ReactNode;
  noBack?: boolean;
}

// Header Icon Button Component
const HeaderIconButton = memo<{
  onPress?: () => void;
  children: React.ReactNode;
}>(({onPress, children}) => (
  <TouchableOpacity style={styles.iconButton} onPress={onPress}>
    <View style={styles.iconCircle}>{children}</View>
  </TouchableOpacity>
));

const TitleHeader: React.FC<TitleHeaderProps> = ({
  title = '',
  showSearch = false,
  onSearch,
  showActions = true,
  onBack,
  actionView,
  noBack = false,
}) => {
  const navigation = useNavigation();
  const customer = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  const popupRef = useRef<any>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Navigation handlers
  const onBackFunc = useCallback(() => {
    if (onBack) onBack();
    else navigation.goBack();
  }, [navigation]);

  // Authentication check
  const handleProtectedAction = useCallback(
    (action: () => void) => {
      if (!customer?.Id) {
        showDialog({
          ref: dialogRef,
          status: ComponentStatus.WARNING,
          title: 'Vui lòng đăng nhập để sử dụng!',
          onSubmit: async () => {
            navigate(RootScreen.login);
          },
        });
        return;
      }
      action();
    },
    [customer?.Id],
  );

  // Action handlers
  const handleNotificationPress = useCallback(() => {
    handleProtectedAction(() => {
      navigate(RootScreen.NotificationIndex);
    });
  }, [handleProtectedAction]);

  const handleQrPress = useCallback(() => {
    handleProtectedAction(() => {
      showPopup({
        ref: popupRef,
        children: <PopupQrcodeScan popupRef={popupRef} />,
      });
    });
  }, [handleProtectedAction]);

  const handleMenuPress = useCallback(() => {
    handleProtectedAction(() => {
      // TODO: Implement menu functionality
    });
  }, [handleProtectedAction]);

  // Search handler
  const handleSearch = useCallback(
    (text: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        onSearch && onSearch(text);
      }, 1000);
    },
    [onSearch],
  );

  return (
    <>
      <FPopup ref={popupRef} />
      <ImageBackground
        source={require('../../../assets/background-header.png')}>
        <View style={styles.headerContent}>
          {noBack ? (
            <View style={styles.leftSection}>
              <Text style={{...styles.titleText, fontSize: 16}}>
                {title ?? ''}
              </Text>
            </View>
          ) : (
            <TouchableOpacity onPress={onBackFunc} style={styles.leftSection}>
              <AppSvg SvgSrc={iconSvg.arrowLeft} size={16} />
              <Text style={styles.titleText}>{title ?? ''}</Text>
            </TouchableOpacity>
          )}

          {actionView ? (
            <View style={styles.rightIcons}>{actionView}</View>
          ) : (
            showActions && (
              <View style={styles.rightIcons}>
                <HeaderIconButton onPress={handleQrPress}>
                  <AppSvg SvgSrc={iconSvg.qr} size={16} />
                </HeaderIconButton>
                <HeaderIconButton onPress={handleNotificationPress}>
                  <AppSvg SvgSrc={iconSvg.notification} size={16} />
                </HeaderIconButton>
                <HeaderIconButton onPress={handleMenuPress}>
                  <AppSvg SvgSrc={iconSvg.menu} size={16} />
                </HeaderIconButton>
              </View>
            )
          )}
        </View>
        <View style={styles.searchSection}>
          {showSearch && <SearchInput setDataSearch={handleSearch} />}
        </View>
      </ImageBackground>
    </>
  );
};

const styles = StyleSheet.create({
  headerContent: {
    marginTop: 60,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    paddingHorizontal: 12,
  },
  leftSection: {
    width: '65%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 10,
    color: ColorThemes.light.neutral_text_title_color,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 4,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  searchSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 10,
    paddingHorizontal: 12,
  },
});

export default TitleHeader;
