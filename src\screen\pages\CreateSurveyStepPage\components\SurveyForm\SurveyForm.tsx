import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
import {useEffect, useRef, useState} from 'react';
import {TypoSkin} from '../../../../../assets/skin/typography';
import ActionButtons from './components/ActionButtons';
import ContactInfoSection from './components/ContactInfoSection';
import ToiletInfoSection from './components/ToiletInfoSection';
import {CreateSurveyStepProps} from './types';
import {PersonalInfoSection} from './components';
import {styles} from './styles';
import {useCreateSurveyForm} from './hooks';
import CreateCustomerBottomSheet from './components/CreateCustomerBottomSheet';

export default function SurveyForm({
  step,
  changeStep,
  initialData,
}: CreateSurveyStepProps) {
  const [showCreateCustomerSheet, setShowCreateCustomerSheet] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
        // Tự động cuộn xuống cuối khi keyboard xuất hiện
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({animated: true});
        }, 100);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidHideListener?.remove();
      keyboardDidShowListener?.remove();
    };
  }, []);

  const {control, errors, register, handleSubmit, handleCancel, setCustomer} =
    useCreateSurveyForm({
      changeStep,
      initialData,
      step,
    });

  const handleCreateCustomer = () => {
    setShowCreateCustomerSheet(true);
  };

  const handleCustomerCreated = (customer: any) => {
    setCustomer(customer);
    setShowCreateCustomerSheet(false);
  };

  return (
    <View style={{flex: 1}}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* Personal Information Section */}
          <Pressable>
            <PersonalInfoSection
              isEdit={step == 'edit' || step == 'create'}
              control={control}
              errors={errors}
              register={register}
            />
          </Pressable>

          <Pressable>
            <View style={{marginTop: 20}}>
              <Text style={{...TypoSkin.title2}}>
                Khảo sát thông tin nhà vệ sinh chung
              </Text>

              <ContactInfoSection
                isEdit={step == 'edit' || step == 'create'}
                control={control}
                errors={errors}
                register={register}
                onCreateCustomer={handleCreateCustomer}
              />

              <ToiletInfoSection
                isEdit={
                  step == 'edit' || step == 'create' || step == 'editInfo'
                }
                control={control}
                errors={errors}
                register={register}
              />
            </View>
          </Pressable>
          {/* {isKeyboardVisible && <View style={{height: 240}} />} */}
        </ScrollView>
        {/* Bottom Action Buttons */}
      </KeyboardAvoidingView>
      <ActionButtons
        style={{paddingHorizontal: 12, paddingBottom: 24}}
        step={step}
        onCancel={handleCancel}
        onConfirm={handleSubmit}
      />

      {/* Create Customer Bottom Sheet */}
      <CreateCustomerBottomSheet
        visible={showCreateCustomerSheet}
        onClose={() => setShowCreateCustomerSheet(false)}
        onCustomerCreated={handleCustomerCreated}
      />
    </View>
  );
}
