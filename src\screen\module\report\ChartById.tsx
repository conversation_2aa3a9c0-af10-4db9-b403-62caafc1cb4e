import {forwardRef, useEffect, useMemo, useRef, useState} from 'react';
import {
  DataController,
  SettingDataController,
  TableController,
} from '../../base-controller';
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  FRating,
  FSelect1,
  FTextField,
  showSnackbar,
  Winicon,
} from '../../../component/export-component';
import RenderChartByType, {EChartType} from './RenderChartByType';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {closePopup, FPopup, showPopup} from '../../../component/popup/popup';
import ListTile from '../../../component/list-tile/list-tile';
import {Ultis} from '../../../utils/Utils';
import ScreenHeader from '../../layout/header';
import {useForm} from 'react-hook-form';
import {ComponentStatus} from '../../../component/component-status';
import {
  StatusData,
  StatusToiletServiceData,
} from '../workplace/components/card/NewWorkCard';
import {SkeletonImage} from '../../../project-component/skeleton-img';
import ConfigAPI from '../../../config/configApi';
import {
  CertificateStringData,
  TicketStatusStrings,
  TicketType,
  TypeStringData,
} from '../service/components/da';
import {StatusTicketData} from '../ticket/components/TicketCard';
import EmptyPage from '../../../project-component/empty-page';
import {useSelectorCateServiceState} from '../../../redux/hooks/hooks';
import {CardToiletHoriSkeleton} from '../../../project-component/skeletonCard';

export default function ChartById({
  id,
  searchRaw = '',
  style,
  chartStyle,
  filterAll = false,
  onPress = true,
}: any) {
  const now = new Date();

  const [chartItem, setChartItem] = useState<any>(undefined);
  const [result, setResult] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const listTime = useMemo(() => {
    if (!chartItem) return [];
    switch (chartItem?.Type) {
      case EChartType.line:
      case EChartType.bar:
      case EChartType.horizontalBar:
        return [
          {id: 'thisWeek', name: 'Tuần này'},
          {id: 'lastWeek', name: 'Tuần trước'},
          {id: 'thisMonth', name: 'Tháng này'},
          {id: 'lastMonth', name: 'Tháng trước'},
          {id: 'lastThreeMonth', name: '3 tháng này'},
          {id: 'lastSixMonth', name: '6 tháng này'},
          // { id: "thisYear", name: "Cả năm" },
        ];
      default:
        return [7, 30, 45, 60, 90, ...(filterAll ? [Infinity] : [])].map(
          (e, i) => ({id: e, name: i === 5 ? 'Tất cả' : `${e} ngày qua`}),
        );
    }
  }, [chartItem?.Type]);
  const [selectedTime, setSelectedTime] = useState<any>();
  const groupByRegex = /(GROUPBY\s+\d+\s+(?:@\w+\s*)+)/g;

  const getData = async () => {
    let querySearch = chartItem.Query.trim() === '*' ? '' : chartItem.Query;
    if (searchRaw.length) querySearch += ` ${searchRaw}`;
    var startDate = undefined;
    var endDate = undefined;
    var reducers = undefined;
    switch (selectedTime) {
      case 'thisWeek':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) + 1,
        ).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) + 7,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(
          groupByRegex,
          (m: string, _: any) => {
            const splitV = m.split(' ');
            return `APPLY "dayofweek(@DateCreated / 1000)" AS _dayofweek ${splitV[0].trim()} ${parseInt(splitV[1]) + 1} ${['@_dayofweek', ...splitV.slice(2)].join(' ')}`;
          },
        );
        break;
      case 'lastWeek':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) - 6,
        ).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7),
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(
          groupByRegex,
          (m: string, _: any) => {
            const splitV = m.split(' ');
            return `APPLY "dayofweek(@DateCreated / 1000)" AS _dayofweek ${splitV[0].trim()} ${parseInt(splitV[1]) + 1} ${['@_dayofweek', ...splitV.slice(2)].join(' ')}`;
          },
        );
        break;
      case 'thisMonth':
        startDate = new Date(now.getFullYear(), now.getMonth()).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(
          groupByRegex,
          (m: string, _: any) => {
            const splitV = m.split(' ');
            return `APPLY "floor(dayofmonth(@DateCreated / 1000) / 7)" AS _dayofmonth ${splitV[0].trim()} ${parseInt(splitV[1]) + 1} ${['@_dayofmonth', ...splitV.slice(2)].join(' ')}`;
          },
        );
        break;
      case 'lastMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(groupByRegex, (m: any, _: any) => {
          const splitV = m.split(' ');
          return `APPLY "floor(dayofmonth(@DateCreated / 1000) / 7)" AS _dayofmonth ${splitV[0].trim()} ${parseInt(splitV[1]) + 1} ${['@_dayofmonth', ...splitV.slice(2)].join(' ')}`;
        });
        break;
      case 'lastThreeMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(groupByRegex, (m: any, _: any) => {
          const splitV = m.split(' ');
          return `APPLY "monthofyear(@DateCreated / 1000)" AS _monthofyear ${splitV[0].trim()} ${parseInt(splitV[1]) + 1} ${['@_monthofyear', ...splitV.slice(2)].join(' ')}`;
        });
        break;
      case 'lastSixMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 6).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(groupByRegex, (m: any, _: any) => {
          const splitV = m.split(' ');
          return `APPLY "monthofyear(@DateCreated / 1000)" AS _monthofyear ${splitV[0].trim()} ${parseInt(splitV[1]) + 1} ${['@_monthofyear', ...splitV.slice(2)].join(' ')}`;
        });
        break;
      default:
        if (selectedTime === Infinity) startDate = undefined;
        else
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDay() - selectedTime,
          ).getTime();
        reducers = chartItem.Group;
        break;
    }
    querySearch += ` ${startDate ? `@DateCreated:[${startDate} ${endDate ?? Date.now()}]` : ''}`;
    querySearch = querySearch.trim();

    const controller = new DataController(chartItem.TbName);

    const res = await controller.group({
      searchRaw: querySearch.length ? querySearch : '*',
      reducers: reducers,
    });

    if (res.code === 200) setResult(res.data);
  };

  const getMonthName = (m: any) => {
    switch (m) {
      case 0:
        return 'T1';
      case 1:
        return 'T2';
      case 2:
        return 'T3';
      case 3:
        return 'T4';
      case 4:
        return 'T5';
      case 5:
        return 'T6';
      case 6:
        return 'T7';
      case 7:
        return 'T8';
      case 8:
        return 'T9';
      case 9:
        return 'T10';
      case 10:
        return 'T11';
      case 11:
        return 'T12';
      default:
        return '';
    }
  };

  const getxAxisName = () => {
    switch (selectedTime) {
      case 'thisWeek':
      case 'lastWeek':
        return ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
      case 'thisMonth':
      case 'lastMonth':
        return ['1 - 7', '8 - 14', '15 - 21', '22 - cuối'];
      case 'lastThreeMonth':
        return [
          new Date(now.getFullYear(), now.getMonth() - 2).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 1).getMonth(),
          now.getMonth(),
        ].map(num => getMonthName(num));
      case 'lastSixMonth':
        return [
          new Date(now.getFullYear(), now.getMonth() - 5).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 4).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 3).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 2).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 1).getMonth(),
          now.getMonth(),
        ].map(num => getMonthName(num));
      case 'thisYear':
        return Array.from({length: 12}).map((_, index) => index + 1);
      default:
        return [];
    }
  };

  useEffect(() => {
    if (listTime.length) setSelectedTime(listTime[0].id);
  }, [listTime]);

  useEffect(() => {
    if (selectedTime) getData();
  }, [selectedTime]);

  useEffect(() => {
    if (id) {
      const controller = new SettingDataController('chart');
      controller.getByIds([id]).then(async res => {
        if (res.code === 200) {
          const tmp = res.data[0];

          tmp.Setting = JSON.parse(tmp.Setting);

          setChartItem(tmp);
        }
      });
    }
  }, [id]);

  const showDetail = (ev: any) => {
    switch (chartItem.TbName) {
      case 'Toilet':
        var mId = '7b0423ba3e394fd99951e98fb32849bc';
        break;
      case 'ToiletServices':
        mId = '495cb7c211ee4da7ad9a1ed7efc081d7';
        break;
      case 'Ticket':
        mId = 'f6c52a06549f4c5185ce8ef0b1902a87';
        break;
      case 'Customer':
        mId = '56f373108bc84b94a08a05505d66479b';
        break;
      default:
        mId = '';
        break;
    }
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupViewDetail
          title={chartItem.Name}
          filterName={ev}
          searchRaw={searchRaw}
          chartItem={chartItem}
          selectedTime={selectedTime}
          ref={popupRef}
          menuId={mId}
          filter={
            ['line', 'bar'].some(e => chartItem.Type.includes(e))
              ? chartItem.Setting.datasets.find(
                  (e: any) =>
                    (ev?.name === e.name || ev.name === e.title) &&
                    ev.color === e.color,
                )
              : chartItem.Setting.datasets.find(
                  (e: any) =>
                    (ev?.name === e.name || ev.name === e.title) &&
                    ev.color === e.color,
                )
          }
        />
      ),
    });
  };

  return (
    <View
      style={{
        ...style,
        borderColor: ColorThemes.light.neutral_main_border_color,
        borderRadius: 8,
        borderWidth: 1,
        padding: 16,
      }}>
      <FPopup ref={popupRef} />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        {chartItem ? (
          <Text numberOfLines={2} style={{...TypoSkin.title5}}>
            {chartItem?.Name ?? ''}
          </Text>
        ) : null}
        {listTime?.length ? (
          <FSelect1
            style={{width: '45%'}}
            value={selectedTime}
            data={listTime}
            onChange={(v: any) => {
              setSelectedTime(v.id);
            }}
          />
        ) : null}
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        {chartItem ? (
          <RenderChartByType
            onPressSelected={onPress ? ev => showDetail(ev) : undefined}
            style={chartStyle ?? {height: 150, gap: 24}}
            type={chartItem.Type}
            xAxisName={
              typeof listTime[0] === 'number' ? undefined : getxAxisName()
            }
            datasets={chartItem.Setting.datasets.map((e: any) => {
              try {
                const data = result;
                var listData = Array<any>();
                var filterByTime = undefined;
                switch (selectedTime) {
                  case 'thisWeek':
                  case 'lastWeek':
                    listData = [1, 2, 3, 4, 5, 6, 0];
                    filterByTime = (ev: any, num: number) =>
                      ev?.['_dayofweek'] &&
                      parseInt(ev?.['_dayofweek']) === num;
                    break;
                  case 'thisMonth':
                  case 'lastMonth':
                    listData = [0, 1, 2, 3];
                    filterByTime = (ev: any, num: number) =>
                      num === 3
                        ? parseInt(ev._dayofmonth) >= num
                        : parseInt(ev._dayofmonth) === num;
                    break;
                  case 'lastThreeMonth':
                    listData = [
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 2,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 1,
                      ).getMonth(),
                      now.getMonth(),
                    ];
                    filterByTime = (ev: any, num: number) =>
                      parseInt(ev._monthofyear) === num;
                    break;
                  case 'lastSixMonth':
                    listData = [
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 5,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 4,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 3,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 2,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 1,
                      ).getMonth(),
                      now.getMonth(),
                    ];
                    filterByTime = (ev: any, num: number) =>
                      parseInt(ev._monthofyear) === num;
                    break;
                  case 'thisYear':
                    break;
                  default:
                    break;
                }
                var evalValue = new Function(
                  'data',
                  'listData',
                  'filterByTime',
                  `return ${e?.value}`,
                )(data, listData, filterByTime);
              } catch (error) {
                console.log('=======error=======');
                console.log(error);
                console.log('===================');
                evalValue = 0;
              }
              return {
                ...e,
                value: evalValue,
              };
            })}
            unit={chartItem.Setting.unit}
            legend={chartItem.Setting.legend}
          />
        ) : null}
      </View>
    </View>
  );
}

const PopupViewDetail = forwardRef(function PopupViewDetail(
  data: {
    title: any;
    chartItem: any;
    searchRaw: any;
    selectedTime: any;
    filter: any;
    menuId: any;
    filterName: any;
  },
  ref: any,
) {
  const popupRef = useRef<any>();
  const {
    title,
    chartItem,
    selectedTime,
    filter,
    menuId,
    filterName,
    searchRaw,
  } = data;
  const methods = useForm({shouldFocusError: false});
  const controller = new DataController(chartItem.TbName);
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');

  const [managerData, setManagerData] = useState({
    data: Array<any>(),
    totalCount: undefined,
  });
  const cateServices = useSelectorCateServiceState().data;
  const now = new Date();
  const regexFindEmptyVariable = /@([a-zA-Z_][a-zA-Z0-9_]*):\{(exist|empty)\}/;
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const checkSearchEmpty = filter.link.match(regexFindEmptyVariable);
    var emptyKey = undefined;
    if (checkSearchEmpty?.[0]) emptyKey = checkSearchEmpty[1];

    let querySearch = chartItem.Query.trim() === '*' ? '' : chartItem.Query;
    if (searchRaw.length) querySearch += ` ${searchRaw}`;

    if (searchValue.length) querySearch += ` ${searchValue}`;
    var startDate = undefined;
    var endDate = undefined;
    switch (selectedTime) {
      case 'thisWeek':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) + 1,
        ).getTime();
        if (chartItem.Type == 'bar')
          endDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() - (now.getDay() ? now.getDay() : 7) + 7,
            23,
            59,
            59,
            999,
          ).getTime();
        else
          endDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() - (now.getDay() ? now.getDay() : 7) + 1,
            23,
            59,
            59,
            999,
          ).getTime();
        break;
      case 'lastWeek':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) - 6,
        ).getTime();
        if (chartItem.Type == 'bar')
          endDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() - (now.getDay() ? now.getDay() : 7),
            23,
            59,
            59,
            999,
          ).getTime();
        else
          endDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() - (now.getDay() ? now.getDay() : 7) - 6,
            23,
            59,
            59,
            999,
          ).getTime();
        break;
      case 'thisMonth':
        startDate = new Date(now.getFullYear(), now.getMonth()).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        break;
      case 'lastMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        break;
      case 'lastThreeMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        break;
      case 'lastSixMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 6).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        break;
      default:
        if (selectedTime === Infinity) startDate = undefined;
        else
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDay() - selectedTime,
          ).getTime();
        break;
    }
    querySearch += ` ${startDate ? `@DateCreated:[${startDate} ${endDate ?? Date.now()}]` : ''} ${emptyKey ? filter.link.replace(regexFindEmptyVariable, '').trim() : (filter.link ?? '')}`;
    var res = undefined;
    if (emptyKey) {
      res = await controller.filterByEmptyKey({
        page: 1,
        size: 1000,
        searchRaw: querySearch,
        key: emptyKey,
        notEmpty: checkSearchEmpty[2] === 'exist',
      });
    } else {
      res = await controller.aggregateList({
        page: 1,
        size: 1000,
        searchRaw: querySearch,
      });
    }

    if (res.code === 200) {
      const customerIds = res.data
        .map((e: any) => e.CustomerId)
        .filter(
          (id: any, i: any, arr: string | any[]) => id && arr.indexOf(id) === i,
        );
      if (customerIds.length) {
        const customerController = new DataController('Customer');
        customerController.getByListId(customerIds).then(cusRes => {
          if (cusRes.code === 200)
            setCustomers(
              cusRes.data.map((e: any) => ({
                ...e,
                bgColor: Ultis.generateRandomColor(),
              })),
            );
        });
      }
      setManagerData({data: res.data, totalCount: res.totalCount});
      setLoading(false);
    } else {
      showSnackbar({message: res.message, status: ComponentStatus.ERROR});
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const returnCard = ({item, index}: {item: any; index: number}) => {
    switch (chartItem.TbName) {
      case 'Toilet':
        const status = StatusData.find(e => e.key === item?.Status);
        const customer = customers.find(e => e.Id === item?.CustomerId);
        return (
          <ListTile
            style={{
              borderBottomColor: ColorThemes.light.neutral_main_border_color,
              borderBottomWidth: 1,
            }}
            key={item.Id}
            title={
              <Text
                style={{
                  ...TypoSkin.title3,
                  color: ColorThemes.light.neutral_text_title_color,
                  paddingBottom: 4,
                }}
                numberOfLines={4}>
                {index + 1}. {item?.Name ?? ''}
              </Text>
            }
            subtitle={`Chứng chỉ: ${CertificateStringData.find((e: any) => e.key === item?.Certificate)?.title ?? ''}`}
            trailing={
              <View
                style={{
                  backgroundColor: status?.backgrColor,
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 20,
                }}>
                <Text
                  style={{
                    color:
                      status?.color ??
                      ColorThemes.light.neutral_text_title_color,
                  }}>
                  {status?.title ?? ''}
                </Text>
              </View>
            }
            bottom={
              <View
                style={{
                  flex: 1,
                  paddingTop: 16,
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Ngày tạo:{' '}
                  {Ultis.datetoString(
                    new Date(item?.DateCreated),
                    'dd/MM/yyyy hh:mm',
                  )}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Người sở hữu: {customer?.Name}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Số điện thoại: {item?.Mobile}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Phân loại:{' '}
                  {TypeStringData.find((e: any) => e.key === item?.Type)
                    ?.title ?? ''}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Mô tả: {item?.Description}
                </Text>
              </View>
            }
          />
        );
      case 'ToiletServices':
        const st = StatusToiletServiceData.find(e => e.key === item?.Status);
        const cateService = cateServices.find(
          e => e.Id === item?.CateServicesId,
        );
        return (
          <ListTile
            style={{
              borderBottomColor: ColorThemes.light.neutral_main_border_color,
              borderBottomWidth: 1,
            }}
            key={item.Id}
            title={
              <Text
                style={{
                  ...TypoSkin.title3,
                  color: ColorThemes.light.neutral_text_title_color,
                  paddingBottom: 4,
                }}
                numberOfLines={4}>
                {index + 1}. {item?.Name ?? ''}
              </Text>
            }
            subtitle={`Giá trị ĐH: ${item?.Value ? Ultis.money(item?.Value) + ' (VNĐ)' : ''}`}
            trailing={
              <View
                style={{
                  backgroundColor: st?.backgrColor,
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 20,
                }}>
                <Text
                  style={{
                    color:
                      st?.color ?? ColorThemes.light.neutral_text_title_color,
                  }}>
                  {st?.title ?? ''}
                </Text>
              </View>
            }
            bottom={
              <View
                style={{
                  flex: 1,
                  paddingTop: 16,
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                {cateService ? (
                  <Text
                    style={{
                      ...TypoSkin.subtitle3,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Loại dịch vụ: {cateService?.Name ?? ''}
                  </Text>
                ) : null}
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Ngày tạo: {Ultis.datetoString(new Date(item?.DateCreated))}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Số điện thoại KH: {item?.CustomerMobile}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  ĐỐi tác: {item?.CustomerMobile}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Mô tả: {item?.Description}
                </Text>
              </View>
            }
          />
        );
      case 'Ticket':
        const stTicket = StatusTicketData.find(e => e.id === item?.Status);
        const customerTicket = customers.find(e => e.Id === item?.CustomerId);
        const details = item?.Detail ? JSON.parse(item.Detail) : [];
        var typeLabel = '';
        switch (item.Type) {
          case TicketType.services:
            typeLabel = 'Phản ánh chất lượng dịch vụ';
            break;
          case TicketType.feedback:
            typeLabel = 'Phản hồi/góp ý';
            break;
          case TicketType.accident:
            typeLabel = 'Tai nạn sự cố';
            break;
          default:
            break;
        }
        return (
          <ListTile
            style={{
              borderBottomColor: ColorThemes.light.neutral_main_border_color,
              borderBottomWidth: 1,
            }}
            key={item.Id}
            onPress={
              details.length
                ? () => {
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <PopupViewTicketDetail
                          item={item}
                          customer={customerTicket}
                          ref={popupRef}
                        />
                      ),
                    });
                  }
                : undefined
            }
            title={
              <View
                style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                    paddingBottom: 4,
                  }}
                  numberOfLines={4}>
                  {index + 1}. {item?.Name ?? ''}
                </Text>{' '}
                {details.length ? (
                  <Winicon src="outline/user interface/view" size={16} />
                ) : null}
              </View>
            }
            subtitle={`Loại yêu cầu: ${typeLabel ?? ''}`}
            trailing={
              <View
                style={{
                  backgroundColor: stTicket?.backgrColor,
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 20,
                }}>
                <Text
                  style={{
                    color:
                      stTicket?.color ??
                      ColorThemes.light.neutral_text_title_color,
                  }}>
                  {stTicket?.name ?? ''}
                </Text>
              </View>
            }
            bottom={
              <View
                style={{
                  flex: 1,
                  paddingTop: 16,
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Ngày tạo: {Ultis.datetoString(new Date(item?.DateCreated))}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Người tạo: {customerTicket?.Name}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Số điện thoại: {customerTicket?.Mobile}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Mô tả: {item?.Description}
                </Text>
              </View>
            }
          />
        );
      case 'Customer':
        return (
          <ListTile
            style={{
              borderBottomColor: ColorThemes.light.neutral_main_border_color,
              borderBottomWidth: 1,
            }}
            key={item.Id}
            leading={
              item?.Img ? (
                <SkeletonImage
                  source={{uri: ConfigAPI.imgUrlId + item?.Img}}
                  style={{width: 45, height: 45, borderRadius: 100}}
                />
              ) : null
            }
            title={
              <Text
                style={{
                  ...TypoSkin.title3,
                  color: ColorThemes.light.neutral_text_title_color,
                  paddingBottom: 4,
                }}
                numberOfLines={4}>
                {index + 1}. {item?.Name ?? ''}
              </Text>
            }
            subtitle={`SĐT: ${item.Mobile}`}
            bottom={
              <View
                style={{
                  flex: 1,
                  paddingTop: 16,
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Ngày tạo: {Ultis.datetoString(new Date(item?.DateCreated))}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Địa chỉ: {item?.Address}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Dịch vụ cung cấp: {item?.CustomerMobile}
                </Text>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Công ty: {item?.Description}
                </Text>
              </View>
            }
          />
        );
      default:
        return <View />;
    }
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 75,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={
          title
            ? `${title}/${filterName.name ?? filterName.title ?? ''}`
            : `Xem chi tiết`
        }
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          paddingHorizontal: 16,
          gap: 8,
          paddingTop: 8,
          paddingBottom: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <FTextField
          style={{paddingHorizontal: 16, height: 40}}
          onChange={vl => {
            setSearchValue(vl.trim());
          }}
          value={searchValue}
          placeholder={`Tìm tên ${filterName.name ?? filterName.title ?? ''}`}
          prefix={
            <Winicon
              src="outline/development/zoom"
              size={14}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
          }
        />
      </View>
      <View style={{height: '100%', width: '100%', gap: 16}}>
        <FlatList
          data={
            searchValue.length
              ? managerData.data.filter((e: any) =>
                  e.Name.toLowerCase().includes(searchValue.toLowerCase()),
                )
              : managerData.data
          }
          keyExtractor={item => item.Id.toString()}
          ListFooterComponent={() => <View style={{height: 200}} />}
          renderItem={returnCard}
          ListEmptyComponent={() =>
            loading ? (
              Array.from(Array(10)).map((_, index) => (
                <View key={index} style={{gap: 16}}>
                  <CardToiletHoriSkeleton />
                </View>
              ))
            ) : (
              <EmptyPage title="Không có kết quả phù hợp" />
            )
          }
        />
      </View>
    </SafeAreaView>
  );
});

const PopupViewTicketDetail = forwardRef(function PopupViewTicketDetail(
  data: {item: any; customer: any},
  ref: any,
) {
  const {item, customer} = data;
  const details = useMemo(
    () => (item?.Detail ? JSON.parse(item.Detail) : []),
    [item],
  );

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 75,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={item ? `${item?.Name}` : `Xem chi tiết`}
        prefix={<View />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <ScrollView>
        <KeyboardAvoidingView
          style={{
            height: '100%',
            width: '100%',
            paddingHorizontal: 16,
            gap: 16,
          }}>
          {details?.length > 0 ? (
            <Text
              style={{
                ...TypoSkin.label3,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              Chi tiết xử lý:
            </Text>
          ) : null}
          {details.map((dt: any, i: number) => {
            return (
              <ListTile
                key={i}
                style={{padding: 0}}
                leading={
                  customer?.Img ? (
                    <SkeletonImage
                      source={{
                        uri: customer.Img.startsWith('https')
                          ? customer.Img
                          : ConfigAPI.imgUrlId + customer?.Img,
                      }}
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 50,
                        objectFit: 'cover',
                      }}
                    />
                  ) : (
                    <View
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: Ultis.generateDarkColorRgb(),
                      }}>
                      <Text
                        style={{
                          color: '#fff',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        {customer?.Name?.substring(0, 1)}
                      </Text>
                    </View>
                  )
                }
                title={`${customer?.Name ?? '-'} - ${dt.DateCreated ? Ultis.datetoString(new Date(dt.DateCreated)) : ''}`}
                subtitle={dt.Content ?? '-'}
              />
            );
          })}
        </KeyboardAvoidingView>
      </ScrollView>
    </SafeAreaView>
  );
});
