import {useNavigation} from '@react-navigation/native';
import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
} from 'date-fns';
import {useState, useEffect} from 'react';
import {TouchableOpacity, Image, View, Text} from 'react-native';
import {ColorSkin, ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ComponentStatus} from '../../../../../component/component-status';
import {showSnackbar, Winicon} from '../../../../../component/export-component';
import {decrementBadgeCount} from '../../../../../features/notifications/fcm/fcm_helper';
import {PrimaryButton} from '../../../../../project-component/button';
import {
  NotificationActions,
  NotificationItem,
} from '../../../../../redux/reducers/notification/reducer';
import {Ultis} from '../../../../../utils/Utils';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {useDispatch} from 'react-redux';
import {RootScreen} from '../../../../../router/router';
import AppButton from '../../../../../component/button';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import {CustomerActions} from '../../../../../redux/reducers/user/reducer';
import {DataController} from '../../../../base-controller';
import {CustomerRole} from '../../../../../redux/reducers/user/da';
import ConfigAPI from '../../../../../config/configApi';

interface Props {
  item: NotificationItem | any;
  tab: number;
}

export default function NotificationCard(props: Props) {
  const {tab} = props;
  const navigation = useNavigation<any>();
  const userRole = useSelectorCustomerState().role;
  const company = useSelectorCustomerCompanyState().data;
  const dispatch = useDispatch<any>();
  const [item, setItem] = useState<NotificationItem>(props.item);

  const _onPress = async () => {
    NotificationActions.edit(dispatch, [{...item, Status: 1}]).then(res => {
      if (res.code === 200) {
        setItem({...item, Status: 1});
        decrementBadgeCount();
      }
    });
    switch (item?.LinkApp) {
      case RootScreen.DetailWorkView:
        navigation.push(RootScreen.DetailWorkView, {Id: item.ToiletServicesId});
        break;
      case RootScreen.detailProject:
        navigation.push(RootScreen.detailProject, {
          item: {...item, ToiletId: item.ToiletServicesId},
        });
        break;
      case RootScreen.MyTicketList:
        navigation.push(RootScreen.MyTicketList);
        break;
      case RootScreen.CompanyView:
        if (userRole?.Id === item.CustomerCompanyId && userRole?.Status === 0) {
          navigation.push(RootScreen.CompanyView, {Id: item.ToiletServicesId});
        }
        break;
      default:
        break;
    }
  };

  const getDiffrentTime = () => {
    var time = '';
    const min = differenceInMinutes(new Date(), new Date(item.DateCreated));
    if (min >= 60) {
      const hours = differenceInHours(new Date(), new Date(item.DateCreated));
      if (hours >= 24) {
        const day = differenceInDays(new Date(), new Date(item.DateCreated));
        if (day >= 10)
          var time = Ultis.datetoString(new Date(item.DateCreated), 'dd/mm');
        else time = `${day} ngày trước`;
      } else time = `${hours} giờ trước`;
    } else time = `${min ? min : 1} phút trước`;
    return time;
  };

  return (
    <TouchableOpacity
      activeOpacity={0.5}
      onPress={
        company?.Id !== ConfigAPI.ktxCompanyId ||
        userRole?.Role.includes(CustomerRole.Coordinator)
          ? _onPress
          : undefined
      }
      style={{
        flexDirection: 'row',
        paddingVertical: 16,
        paddingHorizontal: 16,
        marginVertical: 5,
        marginHorizontal: 5,
        backgroundColor:
          item?.Status == 1
            ? ColorSkin.white
            : ColorThemes.light.primary_background,
        borderRadius: 8,
        gap: 12,
        alignItems: 'center',
      }}>
      <Winicon
        src="outline/business/briefcase-26"
        size={24}
        color={ColorThemes.light.neutral_text_subtitle_color}
      />
      <View style={{flex: 1}}>
        <Text
          numberOfLines={2}
          style={[TypoSkin.semibold2, {paddingBottom: 4}]}>
          {item?.Content ?? '-'}
        </Text>
        <Text style={[TypoSkin.regular1, {color: ColorSkin.textColorGrey2}]}>
          {getDiffrentTime()}
        </Text>
        {/* actions */}
        {userRole?.Id === item.CustomerCompanyId && userRole?.Status === 0 ? (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: 8,
              paddingTop: 12,
              alignItems: 'center',
            }}>
            <AppButton
              title={'Từ chối'}
              backgroundColor={
                item?.Status != 1
                  ? ColorThemes.light.neutral_absolute_background_color
                  : ColorThemes.light.neutral_main_background_color
              }
              borderColor="transparent"
              containerStyle={{
                height: 35,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={async () => {
                CustomerActions.deleteRole(dispatch, userRole?.Id ?? '');
                NotificationActions.edit(dispatch, [{...item, Status: 1}]);
                const customerCompanyController = new DataController(
                  'CustomerCompany',
                );
                await customerCompanyController.getListSimple({
                  page: 1,
                  size: 100,
                  query: `CompanyProfileId:{${userRole?.CompanyProfileId}} @Role:(*${CustomerRole.Owner}* | *${CustomerRole.Coordinator}*)`,
                  returns: ['CustomerId'],
                });
              }}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
            />
            <AppButton
              title={'Chấp nhận'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={{
                height: 35,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={async () => {
                if (!userRole) return;
                CustomerActions.editRole(dispatch, {...userRole, Status: 1});
                NotificationActions.edit(dispatch, [{...item, Status: 1}]);
                const customerCompanyController = new DataController(
                  'CustomerCompany',
                );
                await customerCompanyController.getListSimple({
                  page: 1,
                  size: 100,
                  query: `CompanyProfileId:{${userRole.CompanyProfileId}} @Role:(*${CustomerRole.Owner}* | *${CustomerRole.Coordinator}*)`,
                  returns: ['CustomerId'],
                });
                navigation.push(RootScreen.CompanyView, {
                  Id: item.ToiletServicesId,
                });
              }}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );
}
