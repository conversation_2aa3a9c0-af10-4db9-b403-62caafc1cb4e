import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {PermissionsAndroid, Platform} from 'react-native';
import {useDispatch} from 'react-redux';
import {NotificationActions} from '../../../redux/reducers/notification/reducer';
import {store} from '../../../redux/store/store';
import {navigate, navigationRef, RootScreen} from '../../../router/router';
import {CustomerActions} from '../../../redux/reducers/user/reducer';
import {getApp, initializeApp} from '@react-native-firebase/app';
import {getAuth} from '@react-native-firebase/auth';

// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: 'AIzaSyDota18L3EjWb9D1WtZIxFccAb_FiCAEAY',
  authDomain: 'so-hoa-nvs.firebaseapp.com',
  projectId: 'so-hoa-nvs',
  storageBucket: 'so-hoa-nvs.firebasestorage.app',
  messagingSenderId: '1032879675338',
  appId: '1:1032879675338:android:8ff72f1d4e07c2a741a133',
};

let initFirebase;
try {
  initFirebase = getApp(); // This will get the default app if it's already initialized
} catch (error) {
  initFirebase = initializeApp(firebaseConfig); // Initialize app with the provided config
}

// Now you can access the Firebase services
export const auth = getAuth();

export const initNotificationPermission = async () => {
  try {
    // Wait for Firebase to be fully initialized
    await initFirebase;

    await registerAppWithFCM();

    const authStatus = await messaging().requestPermission();
    console.log('====================================');
    console.log(Platform.OS);
    console.log('====================================');
    // ANDROID
    if (Platform.OS === 'android') {
      try {
        await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        ).then(response => {
          if (response) {
            return getFcmToken();
          }
        });
        // requestNotificationPermission();
      } catch (err) {
        console.warn('requestNotificationPermission error: ', err);
      }
    } else {
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      // Request permissions (required for iOS)
      const settings = await notifee.requestPermission();
      if (settings.authorizationStatus === AuthorizationStatus.DENIED) {
        console.log('User denied permissions request');
      } else if (
        settings.authorizationStatus === AuthorizationStatus.AUTHORIZED
      ) {
        // console.log('User granted permissions request');
      } else if (
        settings.authorizationStatus === AuthorizationStatus.PROVISIONAL
      ) {
        console.log('User provisionally granted permissions request');
      }
      if (enabled) {
        return getFcmToken();
      }
    }
  } catch (error) {
    console.log('Firebase initialization error:', error);
    throw error;
  }
};

//method was called to listener events from firebase for notification triger
// main hàm xử lý thông tin và logic
export function registerListenerWithFCM() {
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    // console.log('onMessage Received : ', JSON.stringify(remoteMessage));
    if (
      remoteMessage?.notification?.title &&
      remoteMessage?.notification?.body
    ) {
      // Increment badge count
      notifee
        .incrementBadgeCount()
        .then(() => notifee.getBadgeCount())
        .then(count => {
          console.log('Badge count incremented to: ', count);
        });
      NotificationActions.setBadge(store.dispatch);
      // show
      onDisplayNotification(
        remoteMessage.notification?.title,
        remoteMessage.notification?.body,
        remoteMessage?.data,
      );
    }
  });

  // xu ly noti click 2 luong
  notifee.onForegroundEvent(({type, detail}) => {
    // click khi hiển thị app hoac treo app
    switch (type) {
      case EventType.DISMISSED:
        notifee
          .decrementBadgeCount()
          .then(() => notifee.getBadgeCount())
          .then(count =>
            console.log('Badge count decremented by 1 to: ', count),
          );
        console.log('User dismissed notification', detail.notification);
        break;
      case EventType.PRESS:
        notifee
          .decrementBadgeCount()
          .then(() => notifee.getBadgeCount())
          .then(count =>
            console.log('Badge count decremented by 1 to: ', count),
          );
        console.log(
          'User pressed notification khi hiển thị app',
          detail.notification,
        );
        CustomerActions.getInfor(store.dispatch);
        navigate(RootScreen.NotificationIndex);
        break;
    }
    NotificationActions.setBadge(store.dispatch);
  });

  notifee.onBackgroundEvent(async ({type, detail}) => {
    // click khi k sd app
    const {notification, pressAction} = detail;
    if (type === EventType.PRESS) {
      notifee
        .decrementBadgeCount()
        .then(() => notifee.getBadgeCount())
        .then(count => console.log('Badge count decremented by 1 to: ', count));
      NotificationActions.setBadge(store.dispatch);
      CustomerActions.getInfor(store.dispatch);
      navigate(RootScreen.NotificationIndex);
      return;
    }
  });

  messaging().onNotificationOpenedApp(async remoteMessage => {
    console.log(
      'onNotificationOpenedApp Received',
      JSON.stringify(remoteMessage),
    );
  });

  // Check whether an initial notification is available
  messaging()
    .getInitialNotification()
    .then(remoteMessage => {
      if (remoteMessage) {
        console.log(
          'Notification caused app to open from quit state:',
          remoteMessage.notification,
        );
      }
    });

  return unsubscribe;
}

//method was called to get FCM tiken for notification
export const getFcmToken = async () => {
  let token = '';
  try {
    token = await messaging().getToken();
    console.log('getFcmToken-->', token);
    await AsyncStorage.setItem('fcmToken', token);
  } catch (error) {
    console.log('getFcmToken Device Token error ', error);
  }
  return token;
};

//method was called on  user register with firebase FCM for notification
export async function registerAppWithFCM() {
  // console.log(
  //     'registerAppWithFCM status',
  //     firebase.messaging().isDeviceRegisteredForRemoteMessages,
  // );

  if (!messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .registerDeviceForRemoteMessages()
      .then(status => {
        console.log('registerDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('registerDeviceForRemoteMessages error ', error);
      });
  }
  messaging().subscribeToTopic('all-devices');
}

//method was called on un register the user from firebase for stoping receiving notifications
export async function unRegisterAppWithFCM() {
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );

  if (messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .unregisterDeviceForRemoteMessages()
      .then(status => {
        console.log('unregisterDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('unregisterDeviceForRemoteMessages error ', error);
      });
  }
  await messaging().deleteToken();
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );
}

export const decrementBadgeCount = async () => {
  notifee
    .decrementBadgeCount()
    .then(() => notifee.getBadgeCount())
    .then(count => console.log('Badge count decremented by 1 to: ', count));
  NotificationActions.setBadge(store.dispatch);
};

//method was called to display notification
async function onDisplayNotification(
  title: string,
  body: string,
  data:
    | {
        [key: string]: string | number | object;
      }
    | any
    | undefined,
) {
  // Create a channel (required for Android)
  const channelId = await notifee.createChannel({
    id: 'default',
    name: 'Default Channel',
  });

  // Display a notification
  await notifee.displayNotification({
    title: title,
    body: body,
    data: data,
    android: {
      channelId,
      importance: AndroidImportance.HIGH,
      // pressAction is needed if you want the notification to open the app when pressed
      pressAction: {
        id: 'default',
      },
    },
  });
}
