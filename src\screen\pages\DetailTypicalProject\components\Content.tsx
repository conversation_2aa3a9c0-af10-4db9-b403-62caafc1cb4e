import {StyleSheet, View} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {useWindowDimensions} from 'react-native';
import {useMemo, useCallback} from 'react';
import {ColorThemes} from '../../../../assets/skin/colors';

const Content = ({data}: {data: any}) => {
  const {width} = useWindowDimensions();

  // Tính toán contentWidth với padding
  const contentWidth = useMemo(() => width - 32, [width]);

  // Cấu hình renderersProps cho tối ưu hình ảnh
  const renderersProps = useMemo(
    () => ({
      img: {
        enableExperimentalPercentWidth: true,
      },
    }),
    [],
  );

  // Cấu hình tagsStyles cho mobile-friendly
  const tagsStyles = useMemo(
    () => ({
      body: {
        margin: 0,
        padding: 0,
        color: ColorThemes.light.neutral_text_body_color,
        fontSize: 16,
        lineHeight: 24,
        fontFamily: 'Inter',
        fontWeight: '400' as const,
      },
      p: {
        margin: 0,
        marginBottom: 12,
        fontSize: 16,
        lineHeight: 24,
        color: ColorThemes.light.neutral_text_body_color,
      },
      div: {
        margin: 0,
        padding: 0,
      },
      h1: {
        fontSize: 24,
        lineHeight: 32,
        fontWeight: 'bold' as const,
        marginBottom: 16,
        marginTop: 16,
        color: ColorThemes.light.neutral_text_title_color,
      },
      h2: {
        fontSize: 20,
        lineHeight: 28,
        fontWeight: 'bold' as const,
        marginBottom: 12,
        marginTop: 12,
        color: ColorThemes.light.neutral_text_title_color,
      },
      h3: {
        fontSize: 18,
        lineHeight: 26,
        fontWeight: 'bold' as const,
        marginBottom: 10,
        marginTop: 10,
        color: ColorThemes.light.neutral_text_title_color,
      },
      h4: {
        fontSize: 16,
        lineHeight: 24,
        fontWeight: 'bold' as const,
        marginBottom: 8,
        marginTop: 8,
        color: ColorThemes.light.neutral_text_title_color,
      },
      h5: {
        fontSize: 14,
        lineHeight: 22,
        fontWeight: 'bold' as const,
        marginBottom: 6,
        marginTop: 6,
        color: ColorThemes.light.neutral_text_title_color,
      },
      h6: {
        fontSize: 12,
        lineHeight: 20,
        fontWeight: 'bold' as const,
        marginBottom: 4,
        marginTop: 4,
        color: ColorThemes.light.neutral_text_title_color,
      },
      strong: {
        fontWeight: 'bold' as const,
      },
      b: {
        fontWeight: 'bold' as const,
      },
      em: {
        fontStyle: 'italic' as const,
      },
      i: {
        fontStyle: 'italic' as const,
      },
      u: {
        textDecorationLine: 'underline' as const,
      },
      a: {
        color: ColorThemes.light.primary_main_color,
        textDecorationLine: 'underline' as const,
      },
      ul: {
        marginBottom: 12,
        paddingLeft: 0,
      },
      ol: {
        marginBottom: 12,
        paddingLeft: 0,
      },
      li: {
        marginBottom: 4,
        fontSize: 16,
        lineHeight: 24,
        color: ColorThemes.light.neutral_text_body_color,
      },
      blockquote: {
        borderLeftWidth: 4,
        borderLeftColor: ColorThemes.light.primary_main_color,
        paddingLeft: 16,
        marginLeft: 0,
        marginBottom: 12,
        fontStyle: 'italic' as const,
        backgroundColor: ColorThemes.light.neutral_lighter_background_color,
        padding: 12,
        borderRadius: 4,
      },
      pre: {
        backgroundColor: ColorThemes.light.neutral_lighter_background_color,
        padding: 12,
        borderRadius: 4,
        marginBottom: 12,
        fontSize: 14,
        fontFamily: 'monospace',
      },
      code: {
        backgroundColor: ColorThemes.light.neutral_lighter_background_color,
        padding: 2,
        borderRadius: 2,
        fontSize: 14,
        fontFamily: 'monospace',
      },
      img: {
        marginBottom: 12,
      },
      table: {
        marginBottom: 12,
        borderWidth: 1,
        borderColor: ColorThemes.light.neutral_main_border_color,
      },
      th: {
        backgroundColor: ColorThemes.light.neutral_lighter_background_color,
        padding: 8,
        fontWeight: 'bold' as const,
        borderWidth: 1,
        borderColor: ColorThemes.light.neutral_main_border_color,
      },
      td: {
        padding: 8,
        borderWidth: 1,
        borderColor: ColorThemes.light.neutral_main_border_color,
      },
    }),
    [],
  );

  // Tính toán embedded max width cho media
  const computeEmbeddedMaxWidth = useCallback(
    () => contentWidth,
    [contentWidth],
  );

  return (
    <View style={styles.container}>
      <RenderHTML
        contentWidth={contentWidth}
        source={{html: data ?? ''}}
        tagsStyles={tagsStyles}
        renderersProps={renderersProps}
        defaultTextProps={{
          selectable: true,
        }}
        enableExperimentalMarginCollapsing={true}
        enableCSSInlineProcessing={false}
        systemFonts={['Inter']}
        ignoredDomTags={['script', 'style', 'iframe', 'font', 'zone']}
        computeEmbeddedMaxWidth={computeEmbeddedMaxWidth}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
});

export default Content;
