import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {AddressItemProps} from '../types';
import {AddressActions} from './AddressActions';
import {TEXTS, COLORS, DIMENSIONS} from '../constants';

/**
 * Component to display individual address item
 */
export const AddressItem: React.FC<AddressItemProps> = ({
  item,
  index,
  chooseAddress,
  onEdit,
  onDelete,
  onSelect,
}) => {
  const handlePress = () => {
    if (chooseAddress && onSelect) {
      onSelect(item);
    }
  };

  return (
    <TouchableOpacity
      onPress={chooseAddress ? handlePress : undefined}
      key={index}
      style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.nameLabel}>
          {item.Name} - {item.Mobile}
        </Text>

        {item.Email && <Text style={styles.valueText}>{item.Email}</Text>}

        <Text style={styles.valueText}>{item.Address}</Text>

        {item.IsDefault && (
          <Text style={styles.defaultText}>{TEXTS.DEFAULT_LABEL}</Text>
        )}
      </View>

      <AddressActions
        item={item}
        chooseAddress={chooseAddress}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    borderRadius: 10,
    backgroundColor: ColorThemes.light.primary_background,
    flexDirection: 'row',
    padding: DIMENSIONS.CONTAINER_PADDING,
    borderBottomColor: COLORS.BORDER_COLOR,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  content: {
    flex: 1,
    gap: DIMENSIONS.CONTENT_GAP,
  },
  nameLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  valueText: {
    ...TypoSkin.body3,
    color: COLORS.SECONDARY_TEXT_COLOR,
  },
  defaultText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.secondary6_darker_color,
    fontWeight: 'bold',
  },
});
