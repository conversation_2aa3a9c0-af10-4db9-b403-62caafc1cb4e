import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useForm} from 'react-hook-form';
import ImageCropPicker from 'react-native-image-crop-picker';
import DocumentPicker from 'react-native-document-picker';
import {showPopup, closePopup} from 'component/popup/popup';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'wini-mobile-components';
import {FilePicker} from 'screen/module/workplace/components/form/RegisterTab/components/SurveyForm/components/FilePicker';
import {FileViewer} from 'screen/module/workplace/components/form/RegisterTab/components/SurveyForm/components/FileViewer';
import {randomGID} from 'utils/Utils';
import {DataController} from 'screen/base-controller';
import {BaseDA} from 'screen/baseDA';

/**
 * Default values for survey form
 * Provides sensible defaults for all form fields to improve user experience
 */
const getDefaultSurveyValues = (): SurveyFormData => ({
  // Basic information
  Name: 'Khảo sát nhà vệ sinh',
  Type: 1, // ToiletSurveyType.clean - "Sạch"
  ToiletType: 1, // ToiletType.forWorker - "<PERSON>ời lao động/Sản xuất"
  PassDesign: false, // Don't skip design by default

  // Operational details
  Frequency: 1, // "Thường xuyên" - Regular frequency
  Condition: '', // User input required
  DeviceCondition: '', // User input required
  Age: '', // User input required

  // Files and media
  Design: [], // No files initially

  // Additional details
  Clean: '', // User input for cleaning frequency
  Feature: '', // User input for functionality
  Position: 1, // "Ngoài trời" - Outdoor location
  Maintain: 1, // "Định kỳ" - Regular maintenance
  Size: '', // User input required
  Description: '', // Optional description
});

// Types
interface FileData {
  Id: string;
  Url: string;
  Name: string;
  Type: string;
  Size: number;
}

interface SurveyFormData {
  Name: string;
  Type: number;
  ToiletType: number;
  PassDesign: boolean;
  Frequency: number;
  Condition: string;
  DeviceCondition: string;
  Age: string;
  Design: FileData[];
  Clean: string;
  Feature: string;
  Position: number;
  Maintain: number;
  Size: string;
  Description: string;
}

interface UseToiletSurveyProps {
  visible: boolean;
  toiletId: string;
  onSuccess?: () => void;
  onClose: () => void;
  dataService: any;
}

interface UseToiletSurveyReturn {
  noDesign: boolean;
  loading: boolean;
  initialLoading: boolean;
  fileUploadLoading: boolean;
  methods: any; // Keep as any for now due to react-hook-form complexity
  popupRef: any;
  updateDesign: any;
  handleTogglePassDesign: () => void;
  handleShowFilePicker: () => void;
  handleRemoveFile: (item: FileData) => void;
  handleOpenFileViewer: (item: FileData) => void;
  handleCompleteSurvey: () => Promise<void>;
}

export const useToiletSurvey = ({
  visible,
  toiletId,
  onSuccess,
  onClose,
  dataService,
}: UseToiletSurveyProps): UseToiletSurveyReturn => {
  const popupRef = useRef<any>();
  const [noDesign, setNoDesign] = useState<boolean>(false);
  const [currentSurvey, setCurrentSurvey] = useState<any | null>(null);
  const [updateDesign, setUpdateDesign] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(false);
  const [fileUploadLoading, setFileUploadLoading] = useState<boolean>(false);
  const methods = useForm<SurveyFormData>({
    shouldFocusError: false,
    defaultValues: getDefaultSurveyValues(),
  });

  // Fetch existing Survey by ToiletId when opened
  useEffect(() => {
    const fetchSurvey = async () => {
      try {
        if (!visible || !toiletId) return;

        setInitialLoading(true);
        const surveyController = new DataController('Survey');
        const res = await surveyController.aggregateList({
          page: 1,
          size: 1,
          searchRaw: `@ToiletServicesId:{${dataService?.Id}} @ToiletId:{${toiletId}}`,
        });
        if (res.code === 200 && res.data?.[0]) {
          let tmp = res.data[0];
          if (tmp.Design?.length) {
            const fileInfor = await BaseDA.getFilesInfor(tmp.Design.split(','));
            if (fileInfor?.code === 200) tmp.Design = fileInfor.data;
          } else {
            tmp.Design = [];
          }
          setCurrentSurvey(tmp);
          Object.keys(tmp).forEach((key: any) => {
            if (key === 'PassDesign') setNoDesign(!!tmp[key]);
            methods.setValue(key, tmp[key]);
          });
        } else {
          // initialize defaults
          setCurrentSurvey(null);
          setNoDesign(false);
          methods.reset(getDefaultSurveyValues());
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('Error fetching survey by toiletId:', error);
        showSnackbar({
          message: 'Có lỗi xảy ra khi tải dữ liệu khảo sát',
          status: ComponentStatus.ERROR,
        });
      } finally {
        setInitialLoading(false);
      }
    };
    fetchSurvey();
  }, [visible, toiletId, methods]);

  const handleTogglePassDesign = useCallback(() => {
    const newValue = !noDesign;
    setNoDesign(newValue);
    methods.setValue('PassDesign', newValue);
  }, [noDesign, methods]);

  const uploadFiles = useCallback(
    async (files: any[]) => {
      if (!files.length) return;

      try {
        setFileUploadLoading(true);
        const res = await BaseDA.uploadFiles(files);
        if (res && Array.isArray(res)) {
          const currentFiles = methods.getValues('Design') || [];
          const newFiles: FileData[] = [];

          res.forEach((item: any) => {
            if (item?.Id && item?.Url) {
              const newFile: FileData = {
                Id: item.Id,
                Url: item.Url,
                Name: item.Name ?? 'new file',
                Type: item.Type ?? '',
                Size: item.Size ?? 0,
              };

              // Check if file already exists
              const exists = currentFiles.some(
                (e: FileData) =>
                  e.Id === item.Id ||
                  (item.Name === e?.Name && item.Url === e?.Url),
              );

              if (!exists) {
                newFiles.push(newFile);
              }
            }
          });

          if (newFiles.length > 0) {
            const updatedFiles = [...currentFiles, ...newFiles];
            methods.setValue('Design', updatedFiles, {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
            // Force trigger re-render
            methods.trigger('Design');
          }
        }
      } catch (error) {
        console.error('Error uploading files:', error);
        showSnackbar({
          message: 'Lỗi khi tải file lên',
          status: ComponentStatus.ERROR,
        });
      } finally {
        setFileUploadLoading(false);
      }
    },
    [methods],
  );

  // Watch for Design changes and log them
  const designFiles = methods.watch('Design');
  useEffect(() => {
    setUpdateDesign(designFiles);
  }, [designFiles]);

  const pickImage = useCallback(async () => {
    try {
      const image = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        maxFiles: 5,
      });
      if (image) {
        const imgs = image.map((e: any) => ({
          name: e.filename ?? 'new img',
          type: e.mime,
          uri: e.path,
          size: e.size,
        }));
        await uploadFiles(imgs);
        closePopup(popupRef);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Error picking image:', error);
    }
  }, [uploadFiles, popupRef]);

  const pickFile = useCallback(async () => {
    try {
      const result = await DocumentPicker.pick({
        allowMultiSelection: true,
      });
      if (result) {
        const files = result.map((e: any) => ({
          name: e.name ?? 'new file img',
          type: e.type,
          uri: e.uri,
          size: e.size,
        }));
        await uploadFiles(files);
        closePopup(popupRef);
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // eslint-disable-next-line no-console
        console.log('Document picker cancelled by user');
      } else {
        // eslint-disable-next-line no-console
        console.log('Error picking document:', err);
      }
      return null;
    }
  }, [uploadFiles, popupRef]);

  const handleShowFilePicker = useCallback(() => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <FilePicker
          onPickImage={pickImage}
          onPickFile={pickFile}
          popupRef={popupRef}
        />
      ),
    });
  }, [pickImage, pickFile, popupRef]);

  const handleRemoveFile = useCallback(
    (item: FileData) => {
      try {
        const currentFiles = methods.getValues('Design') || [];
        const filteredFiles = currentFiles.filter(
          (e: FileData) => e.Url !== item.Url,
        );
        methods.setValue('Design', filteredFiles, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        });
        // Force trigger re-render
        methods.trigger('Design');
      } catch (error) {
        console.error('Error removing file:', error);
      }
    },
    [methods],
  );

  const handleOpenFileViewer = useCallback(
    (item: FileData) => {
      if (!item?.Url) {
        console.warn('Cannot open file viewer: invalid file data');
        return;
      }

      showPopup({
        ref: popupRef,
        enableDismiss: true,
        children: <FileViewer item={item} popupRef={popupRef} />,
      });
    },
    [popupRef],
  );

  const handleCompleteSurvey = useCallback(async (): Promise<void> => {
    if (loading) return; // Prevent double submission
    setLoading(true);
    try {
      const controller = new DataController('Survey');
      const formValues = methods.getValues();
      // Validate required fields
      if (!formValues.Name?.trim()) {
        showSnackbar({
          message: 'Vui lòng nhập tên khảo sát',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      // Process file IDs
      const Files =
        formValues.Design?.map((e: FileData) => e.Id).join(',') || '';

      // Prepare payload
      const payload = {
        Name: formValues.Name.trim(),
        Type: formValues.Type,
        ToiletType: formValues.ToiletType,
        PassDesign: formValues.PassDesign,
        Frequency: formValues.Frequency,
        Condition: formValues.Condition.trim(),
        DeviceCondition: formValues.DeviceCondition.trim(),
        Age: formValues.Age.trim(),

        Clean: formValues.Clean.trim(),
        Feature: formValues.Feature.trim(),
        Position: formValues.Position,
        Maintain: formValues.Maintain,
        Size: formValues.Size.trim(),
        Description: formValues.Description.trim(),
        Design: Files,
        ToiletId: toiletId,
        CustomerId: dataService?.CustomerId,
        ToiletServicesId: dataService?.Id,
        CateCriterionId: dataService?.CateCriterionId,
        Id: currentSurvey?.Id || randomGID(),
        DateCreated: currentSurvey?.DateCreated ?? Date.now(),
        Status: 2,
      };
      let res;
      if (currentSurvey?.Id) {
        res = await controller.edit([payload]);
      } else {
        res = await controller.add([payload]);
      }
      if (res?.code !== 200) {
        showSnackbar({
          message: res?.message || 'Có lỗi xảy ra khi lưu khảo sát',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      showSnackbar({
        message: 'Lưu khảo sát thành công',
        status: ComponentStatus.SUCCSESS,
      });
      onClose();
    } catch (error) {
      console.error('Error completing survey:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi lưu khảo sát',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  }, [methods, onClose, onSuccess, toiletId, currentSurvey, loading]);

  return {
    // State
    noDesign,
    loading,
    initialLoading,
    fileUploadLoading,
    methods,
    popupRef,
    updateDesign,

    // Handlers
    handleTogglePassDesign,
    handleShowFilePicker,
    handleRemoveFile,
    handleOpenFileViewer,
    handleCompleteSurvey,
  };
};
