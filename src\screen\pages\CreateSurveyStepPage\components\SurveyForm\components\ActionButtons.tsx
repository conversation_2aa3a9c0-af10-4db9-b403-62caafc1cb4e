import {View, StyleSheet, ViewStyle} from 'react-native';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {SurveyTaskStep} from '../../..';

export default function ActionButtons({
  style,
  step,
  onConfirm,
  onCancel,
}: {
  style?: ViewStyle;
  step: SurveyTaskStep;
  onConfirm: () => void;
  onCancel: () => void;
}) {
  const getTextConfirm = () => {
    switch (step) {
      case 'create':
      case 'edit':
        return 'Lưu';
      case 'view':
        return 'Thực hiện';
      case 'editInfo':
        return 'Tiếp tục';
      default:
        return 'Lưu';
    }
  };
  const getTextCancel = () => {
    switch (step) {
      case 'create':
      case 'edit':
        return 'Hủy';
      case 'view':
        return 'Cập nhật';
      case 'editInfo':
        return 'Lưu nháp';
      default:
        return 'Hủy';
    }
  };
  return (
    <View style={[styles.container, style]}>
      {/* Skip Button */}
      <AppButton
        title={getTextCancel()}
        textStyle={styles.skipButtonText}
        containerStyle={styles.skipButtonContainer}
        onPress={onCancel}
      />

      {/* Continue Button */}
      <AppButton
        title={getTextConfirm()}
        textStyle={styles.continueButtonText}
        containerStyle={styles.continueButtonContainer}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor={ColorThemes.light.primary_main_color}
        onPress={() => {
          onConfirm();
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  skipButtonText: {
    ...TypoSkin.buttonText1,
  },
  skipButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  continueButtonText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
  },
  continueButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
  },
});
