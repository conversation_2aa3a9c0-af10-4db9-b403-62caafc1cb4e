import {useNavigation, DrawerActions} from '@react-navigation/native';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
  TouchableWithoutFeedback,
  Keyboard,
  Text,
} from 'react-native';
import {FlatList, RefreshControl} from 'react-native-gesture-handler';
import SocialGroups from '../groups/listview/groups';
import FollowingGroup from '../groups/listview/followingGroup';
import CreatorGroup from '../groups/listview/creatorGroup';
import {useRef, useState} from 'react';
import {useDispatch} from 'react-redux';
import {GroupActions} from '../reducers/groupReducer';
import {followingGroupsActions} from '../reducers/followingGroupsReducer';
import {myGroupsActions} from '../reducers/myGroupsReducer';
import {useForm} from 'react-hook-form';
import ImagePicker from 'react-native-image-crop-picker';
import {ProfileView} from './Chat';
import {SearchGroupIndex} from '../groups/listview/search';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import AppButton from '../../../../component/button';
import {
  hideBottomSheet,
  Winicon,
  showBottomSheet,
  FDialog,
  FBottomSheet,
} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import ConfigAPI from '../../../../config/configApi';
import {TextFieldForm} from '../../../../project-component/component-form';
import {SkeletonImage} from '../../../../project-component/skeleton-img';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {RootScreen, navigate} from '../../../../router/router';
import {randomGID} from '../../../../utils/Utils';
import {BaseDA} from '../../../baseDA';

export default function Groups() {
  const navigation = useNavigation<any>();
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const dispatch = useDispatch<any>();
  const user = useSelectorCustomerState().data;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID(), DateCreated: new Date()},
  });
  const onRefresh = async () => {
    setRefreshing(true);
    dispatch(GroupActions.getAllGroups(1, 10));
    dispatch(followingGroupsActions.getFollowingGroups(1, 10));
    dispatch(myGroupsActions.getMyGroups(1, 10));

    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const errorForm = (error: any) => {
    console.log('error', error);
  };
  const submitForm = async (data: any) => {
    await dispatch(GroupActions.addGroup(data));
    methods.reset();
    hideBottomSheet(bottomSheetRef);
    navigation.push(RootScreen.GroupIndex, {Id: data.Id});
  };

  const renderHeader = () => (
    <ListTile
      style={{
        padding: 0,
        paddingBottom: 8,
        paddingHorizontal: 16,
      }}
      isClickLeading
      leading={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{padding: 4}}>
          <Winicon src="fill/user interface/apps" size={20} />
        </TouchableOpacity>
      }
      title="Group"
      trailing={
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#f0f0f0',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 8,
            }}
            onPress={() => {
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                title: 'Create group',
                suffixAction: (
                  <AppButton
                    title={'Tạo'}
                    backgroundColor={ColorThemes.light.transparent}
                    textColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{padding: 4}}
                    onPress={methods.handleSubmit(submitForm, errorForm)}
                  />
                ),
                prefixAction: (
                  <TouchableOpacity
                    onPress={() => hideBottomSheet(bottomSheetRef)}
                    style={{padding: 6, alignItems: 'center'}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={20}
                      color={ColorThemes.light.neutral_text_body_color}
                    />
                  </TouchableOpacity>
                ),

                children: <CreateGroup methods={methods} />,
              });
            }}>
            <Winicon
              src="outline/layout/plus"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#f0f0f0',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 8,
            }}
            onPress={() => {
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                children: <SearchGroupIndex ref={bottomSheetRef} />,
              });
            }}>
            <Winicon
              src="outline/user interface/search"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </TouchableOpacity>
          <ProfileView />
        </View>
      }
    />
  );

  const sections = [
    {
      component: (
        <FollowingGroup
          id="Following"
          titleList="Following"
          horizontal
          isSeeMore
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore, {id: 'Following'});
          }}
        />
      ),
    },
    {
      component: (
        <CreatorGroup
          id="Moderration"
          titleList="Moderration"
          horizontal
          isSeeMore
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore, {id: 'Moderration'});
          }}
        />
      ),
    },
    {
      component: (
        <SocialGroups
          titleList="Groups"
          isSeeMore={true}
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore);
          }}
        />
      ),
    },
  ];

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      {renderHeader()}
      <FlatList
        data={sections}
        style={{flex: 1, paddingTop: 24}}
        renderItem={({item}) => item.component}
        keyExtractor={(_, index) => index.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
    </View>
  );
}

const CreateGroup = ({methods}: any) => {
  const [img, setImg] = useState<any>(null);
  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: false,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);

      if (resImgs) {
        setImg(resImgs[0].Id);
        methods.setValue('Thumb', resImgs[0].Id);
      }
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View
        style={{
          height: Dimensions.get('window').height * 0.9, // Tăng chiều cao lên gần như toàn màn hình
          width: '100%',
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <KeyboardAvoidingView
          style={{flex: 1, width: '100%'}}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}>
          <ScrollView
            style={{flex: 1}}
            contentContainerStyle={{
              padding: 16,
            }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            keyboardDismissMode="on-drag">
            <View style={{gap: 24, width: '100%'}}>
              <TextFieldForm
                required
                style={{
                  width: '100%',
                  backgroundColor: '#fff',
                  borderRadius: 8,
                }}
                placeholder="Tên Group"
                label="Tên Group"
                control={methods.control}
                errors={methods.formState.errors}
                register={methods.register}
                name="Name"
                textFieldStyle={{padding: 16}}
              />
              <Text style={{...TypoSkin.buttonText4}}>Ảnh group</Text>
              {img ? (
                <View
                  style={{
                    width: Dimensions.get('window').width - 32,
                    height: 100,
                    borderWidth: 1.3,
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                  }}>
                  <TouchableOpacity
                    onPress={async () => {
                      setImg(null);
                    }}
                    style={{
                      padding: 4,
                      position: 'absolute',
                      top: 8,
                      right: 8,
                    }}>
                    <Winicon
                      src="fill/user interface/c-remove"
                      size={20}
                      color={ColorThemes.light.error_main_color}
                    />
                  </TouchableOpacity>
                  <SkeletonImage
                    source={{uri: ConfigAPI.imgUrlId + img}}
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  />
                </View>
              ) : (
                <TouchableOpacity
                  onPress={pickerImg}
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    borderWidth: 1.3,
                    borderColor: ColorThemes.light.neutral_main_border_color,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                  }}>
                  <SkeletonImage
                    source={{
                      uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                    }}
                    style={{
                      width: 35,
                      height: 35,
                      objectFit: 'cover',
                    }}
                  />
                  <Text
                    numberOfLines={1}
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    Thêm ảnh
                  </Text>
                </TouchableOpacity>
              )}
              <TextFieldForm
                label="Mô tả"
                textStyle={{textAlignVertical: 'top'}}
                numberOfLines={10}
                multiline={true}
                textFieldStyle={{
                  paddingHorizontal: 16,
                  paddingTop: 16,
                  paddingBottom: 16,
                  height: 100,
                  justifyContent: 'flex-start',
                  backgroundColor:
                    ColorThemes.light.neutral_absolute_background_color,
                }}
                style={{width: '100%'}}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Description"
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </TouchableWithoutFeedback>
  );
};
