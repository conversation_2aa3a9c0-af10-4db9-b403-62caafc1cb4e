import React from 'react';
import {Text, View, StyleSheet} from 'react-native';
import ListTile from 'component/list-tile/list-tile';
import {SkeletonImage} from 'project-component/skeleton-img';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import {Ultis} from 'utils/Utils';
import ConfigAPI from 'config/configApi';
import {CustomerItem} from 'redux/reducers/user/da';
import {TicketDetailItem} from '../../../../../utils/ticketDetailParser';

interface TicketDetailsProps {
  details: Array<TicketDetailItem>;
  customers: Array<CustomerItem>;
}

export const TicketDetails: React.FC<TicketDetailsProps> = ({
  details,
  customers,
}) => {
  const renderDetailItem = (dt: TicketDetailItem, index: number) => {
    const customer = customers.find(e => e.Id === dt.CustomerId);

    // Generate a more unique key
    const key =
      dt.CustomerId && dt.DateCreated
        ? `${dt.CustomerId}-${dt.DateCreated}-${index}`
        : `detail-${index}`;

    // Skip rendering if no customer found
    if (!customer) {
      return null;
    }

    return (
      <ListTile
        key={key}
        style={styles.listTile}
        leading={
          customer.Img ? (
            <SkeletonImage
              source={{
                uri: customer.Img.startsWith('https')
                  ? customer.Img
                  : ConfigAPI.imgUrlId + customer.Img,
              }}
              style={styles.customerAvatar}
            />
          ) : (
            <View
              style={[
                styles.customerAvatarPlaceholder,
                {backgroundColor: Ultis.generateDarkColorRgb()},
              ]}>
              <Text style={styles.customerAvatarText}>
                {customer.Name?.substring(0, 1) ?? '?'}
              </Text>
            </View>
          )
        }
        title={customer.Name || 'Không xác định'}
        subtitle={
          <View style={styles.detailContent}>
            {dt.Content && <Text style={styles.detailText}>{dt.Content}</Text>}
            {dt.DateCreated && (
              <Text style={styles.dateText}>
                {new Date(dt.DateCreated).toLocaleString('vi-VN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            )}
          </View>
        }
      />
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionLabel}>Chi tiết xử lý:</Text>
      <View style={styles.detailsList}>{details.map(renderDetailItem)}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 8,
  },
  detailsList: {
    gap: 4,
  },
  sectionLabel: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
  },
  listTile: {
    padding: 0,
    marginBottom: 8,
  },
  customerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  customerAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customerAvatarText: {
    ...TypoSkin.subtitle4,
    color: '#fff',
    textAlign: 'center',
  },
  detailContent: {
    flex: 1,
    alignItems: 'flex-start',
    width: '100%',
    paddingTop: 4,
    gap: 4,
  },
  detailText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 20,
  },
  dateText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontStyle: 'italic',
  },
});
