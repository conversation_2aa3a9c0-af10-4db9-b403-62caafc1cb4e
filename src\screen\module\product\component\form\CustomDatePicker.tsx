import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Controller, FieldValues} from 'react-hook-form';
import DatePicker from 'react-native-date-picker';
import {formatDateForDisplay} from '../../utils/ProductFormUtils';
import {SingleDatePickerProps} from '../../../../../project-component/form/DateRangePicker/DatePickerTypes';
import {datePickerStyles} from '../../../../../project-component/form/DateRangePicker/DatePickerStyles';
import {CustomBottomSheet} from '../../../../../project-component/form/DateRangePicker/CustomBottomSheet';
import {useState, useRef} from 'react';

export const CustomDatePicker = <T extends FieldValues = FieldValues>({
  control,
  name,
  errors,
  placeholder = 'Chọn ngày',
  disabled = false,
  required = false,
  style = {},
  textStyle = {},
  icon,
  prefix,
  minDate,
  maxDate,
  onDateChange,
}: Omit<SingleDatePickerProps<T>, 'useTimestamp' | 'onTimestampChange'>) => {
  const [tempDate, setTempDate] = useState<Date>(new Date());
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState<boolean>(false);
  const onConfirmCallbackRef = useRef<((date: Date) => void) | null>(null);

  // Utility functions for timestamp conversion
  const timestampToDate = (timestamp?: number): Date | undefined => {
    if (!timestamp || timestamp === 0) return undefined;
    try {
      const date = new Date(timestamp);
      return isNaN(date.getTime()) ? undefined : date;
    } catch {
      return undefined;
    }
  };

  const dateToTimestamp = (date?: Date): number => {
    if (!date) return 0;
    try {
      return date.getTime();
    } catch {
      return 0;
    }
  };

  const showDatePicker = (
    currentValue?: Date,
    onConfirm?: (date: Date) => void,
  ) => {
    setTempDate(currentValue || new Date());
    onConfirmCallbackRef.current = onConfirm || null;
    setIsBottomSheetVisible(true);
  };

  const handleBottomSheetClose = () => {
    setIsBottomSheetVisible(false);
    onConfirmCallbackRef.current = null;
  };

  const handleConfirm = () => {
    onConfirmCallbackRef.current?.(tempDate);
    onDateChange?.(tempDate);
    handleBottomSheetClose();
  };

  const renderBottomSheetContent = () => {
    return (
      <View style={datePickerStyles.bottomSheetContent}>
        <View style={datePickerStyles.pickerContainer}>
          <DatePicker
            date={tempDate}
            mode="date"
            style={datePickerStyles.datePicker}
            minimumDate={minDate}
            maximumDate={maxDate}
            dividerColor={'#f2f5f8'}
            theme="auto"
            onDateChange={setTempDate}
          />
        </View>
      </View>
    );
  };

  return (
    <>
      <CustomBottomSheet
        visible={isBottomSheetVisible}
        onClose={handleBottomSheetClose}
        title="Chọn ngày"
        onCancel={handleBottomSheetClose}
        onConfirm={handleConfirm}
        cancelText="Hủy"
        confirmText="Xác nhận">
        {renderBottomSheetContent()}
      </CustomBottomSheet>

      <Controller
        control={control}
        name={name as any}
        rules={{required}}
        render={({field}) => {
          // Convert timestamp to Date for display
          const currentDate = timestampToDate(field.value);
          
          // Use our custom format function for consistent display
          const displayValue = field.value ? formatDateForDisplay(field.value) : '';

          return (
            <View style={[datePickerStyles.container, style]}>
              <TouchableOpacity
                style={[datePickerStyles.inputWrapper]}
                onPress={() => {
                  if (!disabled) {
                    showDatePicker(currentDate, selectedDate => {
                      // Always save as timestamp
                      const timestamp = dateToTimestamp(selectedDate);
                      field.onChange(timestamp);
                      console.log(`${name} date changed:`, {
                        selectedDate,
                        timestamp,
                        formatted: formatDateForDisplay(timestamp),
                      });
                    });
                  }
                }}
                disabled={disabled}>
                {(prefix || icon) && (
                  <View style={[datePickerStyles.iconContainer]}>
                    {prefix || icon}
                  </View>
                )}

                <Text
                  style={[
                    datePickerStyles.displayText,
                    !displayValue && datePickerStyles.placeholderText,
                    textStyle,
                  ]}>
                  {displayValue || placeholder}
                </Text>
              </TouchableOpacity>

              {errors[name] && (
                <Text style={datePickerStyles.errorText}>
                  {(errors[name]?.message as string) || 'Vui lòng chọn ngày'}
                </Text>
              )}
            </View>
          );
        }}
      />
    </>
  );
};

// Component wrapper với props đơn giản hơn cho ProductAllFields
interface ProductDatePickerProps {
  control: any;
  name: string;
  errors: any;
  placeholder: string;
  required?: boolean;
  style?: any;
  prefix?: React.ReactNode;
  onDateChange?: (date?: Date) => void;
}

export const ProductDatePicker: React.FC<ProductDatePickerProps> = ({
  control,
  name,
  errors,
  placeholder,
  required = true,
  style = {},
  prefix,
  onDateChange,
}) => {
  return (
    <CustomDatePicker
      control={control}
      name={name}
      errors={errors}
      placeholder={placeholder}
      required={required}
      style={style}
      prefix={prefix}
      onDateChange={onDateChange}
    />
  );
};

export default CustomDatePicker;
