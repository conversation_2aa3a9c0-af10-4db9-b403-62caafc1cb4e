import { Dimensions, KeyboardAvoidingView, SafeAreaView, Text, TouchableOpacity, View } from 'react-native';
import ListTile from '../../../../../component/list-tile/list-tile';
import { Ultis } from '../../../../../utils/Utils';
import { TypoSkin } from '../../../../../assets/skin/typography';
import { FDialog, showSnackbar, Winicon } from '../../../../../component/export-component';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { useNavigation } from '@react-navigation/native';
import { TaskStatus, TaskType, ToiletServiceStatus } from '../../../service/components/da';
import { useSelectorCustomerCompanyState, useSelectorCustomerState } from '../../../../../redux/hooks/hooks';
import { useDispatch } from 'react-redux';
import { closePopup, FPopup, showPopup } from '../../../../../component/popup/popup';
import AppButton from '../../../../../component/button';
import { Fselect1Form } from '../../../../../project-component/component-form';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import { useForm } from 'react-hook-form';
import { differenceInDays } from 'date-fns';
import { RootScreen } from '../../../../../router/router';
import { PopupEditTask } from '../../../toilet/view/detailProject/task';
import { CustomerRole, CustomerType } from '../../../../../redux/reducers/user/da';
import { DataController } from '../../../../base-controller';
import { ComponentStatus } from '../../../../../component/component-status';

export default function MyWorkCard({ item, index, project, onChangeTask, toiletServices }: any) {
    const dialogDelAccRef = useRef<any>()
    const navigation = useNavigation<any>()
    const owner = useSelectorCustomerCompanyState().owner;
    const userRole = useSelectorCustomerState().role;
    const dispatch = useDispatch<any>()
    const popupRef = useRef<any>();
    const methods = useForm({ shouldFocusError: false })
    const [assignees, setAssignees] = useState<Array<any>>([])

    const company = useSelectorCustomerCompanyState().data;
    const user = useSelectorCustomerState().data

    useEffect(() => {
        if (company && (user?.CompanyProfileId === company.Id || (userRole?.CompanyProfileId === company.Id && userRole?.Role?.includes(CustomerRole.Coordinator)))) {
            const roleController = new DataController("CustomerCompany")
            roleController.aggregateList({ page: 1, size: 1000, searchRaw: `@CompanyProfileId:{${company.Id}} @Status:[1 1]` }).then(res => {
                if (res.code === 200) {
                    const customerController = new DataController("Customer")
                    customerController.getByListId(res.data.map((e: any) => e.CustomerId)).then(resCustomer => {
                        if (resCustomer.code === 200 && resCustomer.data.length) {
                            switch (project?.Type) {
                                case TaskType.consultant:
                                    setAssignees(resCustomer.data.filter((e: any) => {
                                        if (e.CompanyProfileId) return true
                                        if (e.Type === CustomerType.partner) return false
                                        const _role = res.data.find((r: any) => r.CustomerId === e.Id)
                                        return _role?.Role?.includes(CustomerRole.Consultant) || _role?.Role?.includes(CustomerRole.Coordinator)
                                    }))
                                    break;
                                case TaskType.contract:
                                    setAssignees(resCustomer.data.filter((e: any) => {
                                        if (e.CompanyProfileId) return true
                                        if (e.Type === CustomerType.partner) return false
                                        const _role = res.data.find((r: any) => r.CustomerId === e.Id)
                                        return _role?.Role?.includes(CustomerRole.Coordinator)
                                    }))
                                    break;
                                case TaskType.design:
                                    setAssignees(resCustomer.data.filter((e: any) => {
                                        if (e.CompanyProfileId) return true
                                        if (e.Type === CustomerType.partner) return false
                                        const _role = res.data.find((r: any) => r.CustomerId === e.Id)
                                        return _role && [CustomerRole.SCBD, CustomerRole.Coordinator, CustomerRole.Owner, CustomerRole.Consultant].some(rl => _role.Role.includes(rl))
                                    }))
                                    break;
                                default:
                                    setAssignees(resCustomer.data.filter((e: any) => {
                                        if (e.CompanyProfileId) return true
                                        if (e.Type === CustomerType.partner) return false
                                        const _role = res.data.find((r: any) => r.CustomerId === e.Id)
                                        return _role !== undefined
                                    }))
                                    break;
                            }
                        } else setAssignees([user])
                    })
                }
            })
        } else return setAssignees([user])
    }, [company, user])

    return (
        <View>
            <FDialog ref={dialogDelAccRef} />
            <FPopup ref={popupRef} />
            <ListTile
                key={item.Id}
                onPress={item.ToiletServicesId && item.Status == TaskStatus.closed ? undefined : () => {
                    let checkEditable = undefined
                    if (item.ToiletServicesId) {
                        if (project) {
                            if (project.Status < ToiletServiceStatus.run) {
                                checkEditable = (owner?.Id === project?.CustomerId && userRole?.Role.includes(CustomerRole.Coordinator) || user?.Id === project?.CustomerId)
                            } else checkEditable = false
                        }
                    } else {
                        checkEditable = (owner?.Id === item?.CustomerId && userRole?.Role.includes(CustomerRole.Coordinator) || user?.Id === item?.CustomerId) || item.CustomerId === user?.Id
                    }

                    if (item.DateStart) { methods.setValue('dateStart', new Date(item.DateStart)) } else { methods.setValue('dateStart', undefined) };
                    if (item.DateEnd) { methods.setValue('dateEnd', new Date(item.DateEnd)) } else { methods.setValue('dateEnd', undefined) };

                    methods.setValue("Description", `${item?.Description ?? ""}`)
                    methods.setValue("CateServicesId", `${item?.CateServicesId ?? "undefined"}`)
                    if (item.Type === TaskType.other) {
                        methods.setValue("Name", `${item?.Name}`)
                    }

                    var options = [{ id: TaskStatus.open, name: "Mở" },
                    // {id: TaskStatus.doing, name: "Đang làm"},
                    { id: TaskStatus.done, name: "Hoàn thành" },
                    // { id: TaskStatus.overdue, name: "Quá hạn" },
                    { id: TaskStatus.closed, name: "Đóng" }]
                    if (item?.Status < TaskStatus.overdue) {
                        options.filter(e => e.id < TaskStatus.overdue)
                    }
                    methods.setValue("status", options.find(e => e.id === item?.Status)?.id ?? options[0].id)
                    if (assignees.length) methods.setValue("assignee", assignees.find(e => e.Id === user?.Id)?.Id ?? assignees[0].Id)

                    showPopup({
                        ref: popupRef,
                        enableDismiss: true,
                        children: <PopupEditTask methods={methods} options={options} assignees={assignees} toiletId={item.ToiletId} ref={popupRef} item={item} onChange={onChangeTask} checkEditable={checkEditable} />
                    })
                }}
                title={<Text style={{ ...TypoSkin.title3, paddingBottom: 8 }} numberOfLines={3}>{`${index + 1}. ${item?.Name ?? '-'}`}</Text>}
                subtitle={<Text onPress={() => item.ToiletServicesId ? navigation.navigate(RootScreen.DetailWorkView, { Id: item.ToiletServicesId, Status: item.Status, Name: project?.Name ?? "-" }) : navigation.navigate(RootScreen.detailProject, { item: { ToiletId: item.ToiletId ?? item.Id } })} style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color, textDecorationLine: 'underline', alignSelf: "baseline" }}>Nhà vệ sinh: {project?.Name ?? "-"}</Text>}
                listtileStyle={{ gap: 16 }}
                trailing={item.ToiletServicesId ? null :
                    <Winicon src="outline/user interface/d-edit" size={16} />
                }
                bottom={<View style={{ alignItems: "flex-start", width: "100%", gap: 8, paddingTop: 16 }}>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>Ngày: {`${item.DateStart ? Ultis.datetoString(new Date(item.DateStart), "dd/mm/yyyy") : "-"} → ${item.DateEnd ? Ultis.datetoString(new Date(item.DateEnd), "dd/mm/yyyy") : "-"}`}</Text>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Số ngày: ${item.DateEnd && item.DateStart ? differenceInDays(new Date(item.DateEnd), new Date(item.DateStart)) : "-"}`}</Text>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }} numberOfLines={3}>{`Mô tả: ${item?.Description ?? "-"}`}</Text>
                    <View style={{ paddingTop: 8 }}>
                        <Status status={item.Status} />
                    </View>
                </View>}
            />
        </View>
    );
}

// const PopupEditTask = forwardRef(function PopupEditTask(data: { methods: any, item: any, options: Array<any>, onChange: any }, ref: any) {
//     const { methods, options, onChange, item } = data

//     return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height / 1.5, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: '#fff' }}>
//         <ScreenHeader
//             style={{
//                 backgroundColor: ColorThemes.light.transparent,
//                 flexDirection: 'row',
//                 paddingVertical: 4
//             }}
//             title={`Chỉnh sửa`}
//             prefix={<View />}
//             action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
//                 <Winicon src="outline/layout/xmark" onClick={() => closePopup(ref)} size={20} color={ColorThemes.light.neutral_text_body_color} />
//             </View>}
//         />
//         <KeyboardAvoidingView style={{ height: "100%", width: "100%", paddingHorizontal: 16 }}>
//             <View style={{ flex: 1, gap: 8 }}>
//                 <Fselect1Form
//                     label="Trạng thái"
//                     control={methods.control}
//                     errors={methods.formState.errors}
//                     name="Status"
//                     options={options.map((e: any) => {
//                         return {
//                             id: e.id,
//                             name: e.name
//                         }
//                     })}
//                 />
//             </View>
//         </KeyboardAvoidingView>
//         <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16, paddingBottom: 16 }}>
//             <AppButton
//                 title={'Xong'}
//                 backgroundColor={ColorThemes.light.primary_main_color}
//                 borderColor="transparent"
//                 containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
//                 onPress={() => {
//                     onChange({ Status: methods.getValues("Status") })
//                     setTimeout(() => {
//                         closePopup(ref)
//                     }, 200)
//                 }}
//                 textColor={ColorThemes.light.neutral_absolute_background_color}
//             />
//         </WScreenFooter>
//     </SafeAreaView>
// });

export const StatusTicketData = [
    { id: TaskStatus.open, name: "Đang mở", backgrColor: ColorThemes.light.infor_background, color: ColorThemes.light.infor_main_color },
    // { id: TaskStatus.processing, name: "Đang xử lý", backgrColor: ColorThemes.light.warning_background, color: ColorThemes.light.warning_main_color },
    { id: TaskStatus.done, name: "Hoàn thành", backgrColor: ColorThemes.light.success_background, color: ColorThemes.light.success_main_color },
    { id: TaskStatus.overdue, name: "Hủy", backgrColor: ColorThemes.light.neutral_disable_background_color, color: ColorThemes.light.neutral_text_subtitle_color },
]

export function Status({ status }: any) {
    var st = StatusTicketData.find(e => e.id === status)

    if (!st) return <View style={{ backgroundColor: ColorThemes.light.neutral_text_subtitle_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 20 }}>
        <Text style={{ color: ColorThemes.light.neutral_absolute_background_color }}>-</Text>
    </View>
    if (st) return <View style={{ backgroundColor: st.backgrColor, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 20 }}>
        <Text style={{ color: st.color ?? ColorThemes.light.neutral_text_title_color }}>{st.name ?? status}</Text>
    </View>
}

