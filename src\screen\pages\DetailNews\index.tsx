import React, {useEffect, useState} from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Pressable,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppSvg, FLoading} from 'wini-mobile-components';
import {NewsItem} from '../../../types/newsType';
import newsDa from '../../module/news/newsDa';
import iconSvg from '../../../svgs/iconSvg';
import BasicInfoNews from './components/BasicInfoNews';
import Content from './components/Content';
import {ColorThemes} from '../../../assets/skin/colors';

const DetailNews = () => {
  const route = useRoute();
  const {id} = route.params as {id: string};
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [news, setNews] = useState<NewsItem | null>(null);

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    try {
      setLoading(true);
      const res = await newsDa.fetchById(id);
      console.log('res', res);
      if (res) {
        setNews(res);
      }
    } catch (error) {
      console.error('Error loading news data:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <FLoading
        visible={loading}
        avt={require('../../../assets/appstore.png')}
      />
      {/* Button back luôn hiển thị ở góc */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}>
        <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
      </TouchableOpacity>

      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <Pressable>
          <Image source={{uri: news?.Img}} style={styles.mainImage} />

          <View style={styles.contentContainer}>
            {news && <BasicInfoNews postData={news} />}

            {news && <Content data={news?.Content} />}
          </View>
          <View style={{height: 60}}></View>
        </Pressable>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  mainImage: {
    width: '100%',
    height: 400,
    resizeMode: 'cover',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    backgroundColor: ColorThemes.light.secondary1_sub_color,
    width: 35,
    height: 35,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    elevation: 5, // cho Android
  },
  contentContainer: {
    marginHorizontal: 16,
    marginTop: 16,
  },
});

export default DetailNews;
