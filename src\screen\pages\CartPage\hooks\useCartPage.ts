import React from 'react';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {ComponentStatus} from 'wini-mobile-components';
import {useCartActions, useCartState} from '../../../../redux/hooks/cartHook';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {CustomerDA} from '../../../../redux/reducers/user/da';
import {RootScreen} from '../../../../router/router';
import {dialogCheckAcc} from '../../../layout/main-layout';
import {showSnackbar} from '../../../../component/export-component';

export const useCartPage = () => {
  const navigation = useNavigation<any>();
  const cartState = useCartState();
  const cartActions = useCartActions();
  const customerDA = new CustomerDA();
  const customer = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const customerAddress = useSelectorCustomerState().myAddress;
  const address = customerAddress?.find((item: any) => item.IsDefault);
  const dialogRef = React.useRef<any>(null);

  // Initialize customer addresses
  React.useEffect(() => {
    if (customer) {
      dispatch(CustomerActions.getAddresses(customer.Id));
    }
  }, [customer, dispatch]);

  // Log cart state for debugging
  React.useEffect(() => {
    console.log('check-cartState', cartState);
  }, [cartState]);

  // Group products by store
  const storeGroups = cartActions.groupItemsByStore(cartState.items);

  // Calculate total price of selected items
  const totalSelectedPrice = cartActions.calculateSelectedTotal(
    cartState.items,
  );

  // Check if any items are selected
  const hasSelectedItems = cartActions.hasSelectedItems(cartState.items);

  // Handle checkout process
  const handleCheckout = () => {
    if (!customer) {
      dialogCheckAcc({ref: dialogRef});
      showSnackbar({
        message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }

    if (!address) {
      showSnackbar({
        message: 'Vui lòng cập nhật điểm giao hàng để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }

    if (!hasSelectedItems) {
      showSnackbar({
        message: 'Vui lòng chọn ít nhất một sản phẩm để thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }

    // Filter selected items
    const selectedItems = cartState.items.filter((item: any) => item.selected);
    // Navigate to checkout page with selected items
    navigation.navigate(RootScreen.CheckoutPage, {
      items: selectedItems,
      address: address,
    });
  };

  // Navigate to product list
  const handleNavigateToProductList = () => {
    navigation.navigate(RootScreen.ProductListByCategory);
  };

  return {
    // State
    cartState,
    storeGroups,
    totalSelectedPrice,
    hasSelectedItems,
    customer,
    address,
    dialogRef,

    // Actions
    handleCheckout,
    handleNavigateToProductList,

    // Cart actions
    cartActions,
  };
};
