import React from 'react';
import {Text, TouchableOpacity, View, StyleSheet} from 'react-native';

import WebView from 'react-native-webview';
import {FLoading} from 'wini-mobile-components';
import {RegisterPartnerFormStyles} from './styles/RegisterPartnerFormStyles';
import {FCheckbox} from 'component/export-component';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';

export default function TabTermsAndPoliciesPartnet({
  policy,
  isAgreeTerm,
  setisAgreeTerm,
  Submit,
}: {
  policy: any;
  isAgreeTerm: boolean;
  setisAgreeTerm: (value: boolean) => void;
  Submit: () => void;
}) {
  return (
    <View style={styles.container}>
      <Text style={styles.titleText}>Ch<PERSON>h sách trở thành đối tác</Text>
      <View style={styles.webViewContainer}>
        <WebView
          originWhitelist={['*']}
          source={{
            html: `
              <html>
                <head>
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <style>
                    body {
                      font-size: 18px !important;
                      line-height: 1.6;
                      padding: 16px;
                      margin: 0;
                      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    }
                    p, div, span, li, td, th {
                      font-size: 18px !important;
                      line-height: 1.6 !important;
                    }
                    h1, h2, h3, h4, h5, h6 {
                      font-size: 20px !important;
                      font-weight: bold !important;
                      margin: 16px 0 8px 0 !important;
                    }
                  </style>
                </head>
                <body>
                  ${policy || '<p>Đang tải nội dung...</p>'}
                </body>
              </html>
            `,
          }}
          style={styles.webView}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          onLoad={() => console.log('WebView loaded')}
          renderLoading={() => <FLoading visible={false} />}
          onError={syntheticEvent => {
            const {nativeEvent} = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
          }}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          injectedJavaScript={`
            // Tăng font size cho tất cả elements
            const style = document.createElement('style');
            style.innerHTML = \`
              * {
                font-size: 18px !important;
                line-height: 1.6 !important;
              }
              h1, h2, h3, h4, h5, h6 {
                font-size: 20px !important;
              }
            \`;
            document.head.appendChild(style);
            true;
          `}
        />
      </View>
      <View
        style={{paddingTop: 10, alignItems: 'center', flexDirection: 'row'}}>
        <FCheckbox
          value={isAgreeTerm}
          onChange={v => {
            setisAgreeTerm(v);
          }}
        />
        <Text style={{color: 'black', marginLeft: 16}}>
          Tôi đã đọc và đồng ý với các điều khoản như trên
        </Text>
      </View>
      <TouchableOpacity
        style={[
          RegisterPartnerFormStyles.buyButtonInputShopInfo,
          {
            backgroundColor: isAgreeTerm
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_text_subtitle_color,
            opacity: isAgreeTerm ? 1 : 0.6,
          },
        ]}
        onPress={isAgreeTerm ? Submit : undefined}
        disabled={!isAgreeTerm}>
        <Text
          style={[
            RegisterPartnerFormStyles.buyActionButtonInputShopInfo,
            {
              color: isAgreeTerm
                ? ColorThemes.light.neutral_absolute_background_color
                : ColorThemes.light.neutral_text_body_color,
            },
          ]}>
          Xác nhận
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  titleText: {
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
    ...TypoSkin.title2,
    marginBottom: 16,
    textAlign: 'left',
  },
  webViewContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  webView: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    fontSize: 16,
  },
});
