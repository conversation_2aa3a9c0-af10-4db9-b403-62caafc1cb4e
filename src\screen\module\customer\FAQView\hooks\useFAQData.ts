import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { DataController } from '../../../../base-controller';
import { showSnackbar } from '../../../../../component/export-component';
import { ComponentStatus } from '../../../../../component/component-status';

export interface FAQData {
  data: any[];
  totalCount: number | undefined;
}

export interface FAQCategory {
  Id: string;
  Name: string;
  _count?: number;
}

export const useFAQData = () => {
  const [faqCategory, setFaqCategory] = useState<FAQCategory[]>([]);
  const [dataFaq, setDataFaq] = useState<FAQData>({ data: [], totalCount: undefined });
  const [isLoading, setLoading] = useState(false);

  const getData = async (searchQuery: string[], filterMethods: any) => {
    setLoading(true);
    try {
      const _controller = new DataController('FAQ');
      
      const finalSearchQuery = [...searchQuery];
      finalSearchQuery.push(`@Type:[1 4]`);
      
      const res = await _controller.aggregateList({
        page: 1,
        size: 20,
        searchRaw: finalSearchQuery.length ? finalSearchQuery.join(' ') : '*',
      });
      
      if (res.code !== 200) {
        showSnackbar({
          message: res.message,
          status: ComponentStatus.ERROR,
        });
        return;
      }
      
      res.data.map((e: any) => {
        filterMethods.setValue(`faq${e.Id}`, false);
      });
      
      setDataFaq({ data: res.data, totalCount: res.totalCount });
    } catch (error) {
      console.error('Error fetching FAQ data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFAQCategories = async () => {
    try {
      const cateController = new DataController('CateFAQ');
      const res = await cateController.getAll();
      
      if (res.code === 200) {
        const faqController = new DataController('FAQ');
        const count = await faqController.group({
          searchRaw: `@Type:[1 4]`,
          reducers: 'GROUPBY 1 @CateFAQId REDUCE COUNT 0 AS _count',
        });
        
        if (count.code === 200) {
          setFaqCategory(
            res.data.map((e: any) => ({
              ...e,
              _count: count.data.find((f: any) => f.CateFAQId === e.Id)?._count,
            }))
          );
        } else {
          setFaqCategory(res.data);
        }
      }
    } catch (error) {
      console.error('Error loading FAQ categories:', error);
    }
  };

  useEffect(() => {
    loadFAQCategories();
  }, []);

  return {
    faqCategory,
    dataFaq,
    isLoading,
    getData,
    setLoading,
  };
};
