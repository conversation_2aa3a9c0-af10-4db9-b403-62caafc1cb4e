import React, {useEffect} from 'react';
import {ScrollView, StyleSheet} from 'react-native';

import InfoSXT from './InfoSXT';
import CetificateContent from './CetificateContent';

const Cetificate: React.FC<{toiletId?: string}> = ({toiletId}) => {
  const [getStatusForm, setGetStatusForm] = React.useState<string>('');

  return (
    <ScrollView style={styles.container}>
      <InfoSXT toiletId={toiletId || ''} setGetStatusForm={setGetStatusForm} />
      <CetificateContent
        getStatusForm={getStatusForm}
        toiletId={toiletId || ''}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
});

export default Cetificate;
