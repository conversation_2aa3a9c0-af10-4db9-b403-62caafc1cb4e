import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {GroupDA} from '../groups/da';
import {store} from '../../../../redux/store/store';
import {DataController} from '../../../base-controller';
interface GroupWithMemberCount {
  Id: string;
  Name: string;
  ImageUrl: string;
  Description: string;
  MemberCount: number;
}
interface MyGroupState {
  groups: GroupWithMemberCount[];
  isLoading: boolean;
  error: string | null;
}

const initialState: MyGroupState = {
  groups: [],
  isLoading: false,
  error: null,
};

export const myGroupsSlice = createSlice({
  name: 'myGroups',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setGroups: (state, action: PayloadAction<GroupWithMemberCount[]>) => {
      state.groups = action.payload;
    },
    updateMemberCount: (
      state,
      action: PayloadAction<{groupId: string; change: number}>,
    ) => {
      state.groups = state.groups.map(group =>
        group.Id === action.payload.groupId
          ? {...group, MemberCount: group.MemberCount + action.payload.change}
          : group,
      );
    },
    resetState: () => initialState,
  },
});
export default myGroupsSlice.reducer;

const {setLoading, setGroups, resetState} = myGroupsSlice.actions;

const groupDA = new GroupDA();

export class myGroupsActions {
  static getMyGroups =
    (page: number = 1, size: number = 10) =>
    async (dispatch: Dispatch) => {
      dispatch(setLoading(true));
      const cusId = store.getState().customer.data?.Id;

      if (cusId) {
        const groupController = new DataController('Group');
        const result = await groupController.getListSimple({
          page: page,
          size: size,
          query: `@CustomerId:{${cusId}}`,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
          returns: ['Id', 'Name', 'Thumb', 'Description'],
        });

        if (result.code === 200) {
          const groupsWithMemberCount = await Promise.all(
            result.data.map(async (group: any) => {
              const memberResult = await groupDA.getGroupMembers(group.Id);
              return {
                ...group,
                MemberCount: memberResult ? memberResult.length : 0,
              };
            }),
          );
          dispatch(setGroups(groupsWithMemberCount));
        } else {
          dispatch(setGroups([]));
        }
      }
      dispatch(setLoading(false));
    };
  static resetGroups = () => (dispatch: Dispatch) => {
    dispatch(resetState());
  };
}
