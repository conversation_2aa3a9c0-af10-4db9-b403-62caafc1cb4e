/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {FDialog} from '../../../../component/dialog/dialog';
import AppButton from '../../../../component/button';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {productAction} from '../../../../redux/actions/productAction';
import ProductCarousel from '../view/ProductCarousel';
import {CartActions} from '../../../../redux/reducers/cart/CartReducer';
import {useDispatch} from 'react-redux';
import {navigate, RootScreen} from '../../../../router/router';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  onPressSeeMore?: () => void;
  onRefresh?: boolean;
}

export default function ProductBestSeller(props: Props) {
  const dispatch = useDispatch<any>();
  const [items, setItems] = useState<Array<any>>([]);
  const dialogRef = useRef<any>(null);

  useEffect(() => {
    getData();
  }, [props.onRefresh]);

  const getData = async () => {
    let data = await productAction.find({
      page: 1,
      size: 10,
    });

    setItems(data);
  };

  const navigationToDetail = (item: any) => {
    navigate(RootScreen.DetailProductPage, {id: item.Id});
  };

  const onAddToCart = (item: any) => {
    CartActions.addItemToCart(item, 1)(dispatch);
  };

  const onFavoritePress = (item: any) => {
    //TODO: add to favorite
  };

  return (
    <View style={{marginTop: 20}}>
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View style={styles.titleContainer}>
          <Text style={styles.titleText}>{props.titleList}</Text>
          {props.isSeeMore ? (
            <AppButton
              title={'Xem thêm'}
              containerStyle={styles.seeMoreButtonContainer}
              backgroundColor={'transparent'}
              textStyle={styles.seeMoreButtonText}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={props.onPressSeeMore}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      {items ? (
        <ProductCarousel
          title={'Sản phẩm bán chạy'}
          products={items}
          onSeeAll={props.onPressSeeMore}
          onProductPress={navigationToDetail}
          onFavoritePress={item => {}}
        />
      ) : (
        <></>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  titleText: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMoreButtonContainer: {
    justifyContent: 'flex-start',
    alignSelf: 'baseline',
  },
  seeMoreButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.infor_main_color,
  },
});
