import {
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {useEffect, useMemo, useState} from 'react';
import {showSnackbar, Winicon} from '../../../component/export-component';
import {validatePhoneNumber} from '../../../utils/validate';
import AppButton from '../../../component/button';
import {TextFieldForm} from '../../../project-component/component-form';
import OTPInput from './components/input-otp';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';
import {
  confirmCode,
  signInWithPhoneFB,
} from '../../../features/otp-loginwFirebase/PhoneSignIn';
import {ComponentStatus} from '../../../component/component-status';
import {CustomerActions} from '../../../redux/reducers/user/reducer';
import {useDispatch} from 'react-redux';
import {
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import FLoading from '../../../component/Loading/FLoading';
import LocalAuthen from '../../../features/local-authen/local-authen';
import {DataController} from '../../base-controller';
import {regexPassWord} from '../../../utils/Utils';
import {CustomerStatus} from '../../../redux/reducers/user/da';

interface LoginViewProps {
  methods: any;
  bio: string;
}

export const LoginView = ({methods, bio}: LoginViewProps) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isForgotPass, setForgotPass] = useState(false);
  const [isOtp, setIsOtp] = useState(false);
  const [loading, setLoading] = useState(false);

  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const [allowErrorOpt, setAllowErrorOpt] = useState(0);
  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<any>(null);

  const validationForm = useMemo(() => {
    if (isOtp) return true;
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    isOtp,
  ]);

  const customerController = new DataController('Customer');

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 60000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const onCheckPhoneForOtp = async () => {
    var mobile = methods.watch('Mobile').trim();
    console.log('check-mobile', mobile);
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!validatePhoneNumber(mobile)) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return false;
    }

    setLoading(true);

    const checkLocked = await customerController.getListSimple({
      page: 1,
      size: 1,
      query: `@Mobile:{${mobile}} @Status:[${CustomerStatus.locked} ${CustomerStatus.locked}]`,
    });
    if (checkLocked.data.length) {
      showSnackbar({
        message:
          'Số điện thoại của bạn đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return false;
    }
    var rs = await signInWithPhoneFB(mobile);
    if (rs) {
      setIsOtp(true);
      // done
      setConfirm(rs);
      showSnackbar({
        message: 'Đã gửi mã xác thực đến số diện thoại',
        status: ComponentStatus.SUCCSESS,
      });
      setLoading(false);

      return true;
    } else {
      setLoading(false);
      return false;
    }
  };

  const submitOtp = async (value: string) => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!confirm) return false;
    setLoading(true);

    var rsotp = await confirmCode(confirm, value);

    if (rsotp === true) {
      console.log('===============otp done=====================');
      setLoading(false);
      setAllowErrorOpt(0);
      return true;
    } else {
      if (allowErrorOpt < 5) {
        setAllowErrorOpt(allowErrorOpt + 1);
      } else {
        const r = await CustomerActions.lockAccount(mobile);
        return showSnackbar({
          message: r.message,
          status: ComponentStatus.ERROR,
        });
      }
      showSnackbar({
        message:
          5 - (allowErrorOpt + 1) == 0
            ? 'Mã xác thực không chính xác'
            : `Mã xác thực không chính xác, bạn còn ${5 - (allowErrorOpt + 1)} lần nhập lại`,
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return false;
    }
  };

  const _loginAction = async (otpValue: string) => {
    try {
      let mobile = methods.watch('Mobile')?.trim();
      let password = methods.watch('Password')?.trim();
      // Check if the number doesn't already start with 0 or +84
      if (!/^(\+84|0)/.test(mobile)) {
        mobile = '0' + mobile; // Add 0 at the beginning
      }

      const val = validatePhoneNumber(mobile);
      if (!val) {
        methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        return;
      }

      if (isOtp && otpValue == '') {
        setIsOtp(false);
        return;
      }
      setLoading(true);
      // using password
      if (!isOtp && otpValue == '') {
        // check sdt da dang ky
        const resCustomers = await customerController.aggregateList({
          page: 1,
          size: 1,
          searchRaw: `@Mobile:(${mobile})`,
        });

        if (resCustomers && resCustomers?.data?.length == 0) {
          showSnackbar({
            message: 'Số điện thoại chưa được đăng ký.',
            status: ComponentStatus.ERROR,
          });
          return;
        }
        if (!password) return;
        if (password === undefined || password.length == 0) {
          methods.setError('Password', {
            message: 'Mật khẩu không được để trống',
          });
          return;
        }
        const valPass = regexPassWord.test(password);
        if (!valPass) {
          methods.setError('Password', {
            message: 'Mật khẩu sai định dạng, hãy thử lại',
          });
          return;
        }
        methods.clearErrors('Password');
        setVisiblePass(true);

        const res = await CustomerActions.login(mobile, password);
        switch (res.code) {
          case 403:
            methods.setError('Password', {
              message: 'Mật khẩu không đúng, vui lòng kiểm tra lại.',
            });
            showSnackbar({
              message: 'Mật khẩu không đúng, vui lòng kiểm tra lại.',
              status: ComponentStatus.ERROR,
            });
            break;
          case 200:
            saveDataToAsyncStorage(
              'timeRefresh',
              `${Date.now() / 1000 + 9 * 60}`,
            );
            saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
            saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
            saveDataToAsyncStorage('Mobile', `${mobile}`);
            await CustomerActions.getInfor(dispatch, navigation).then(() => {
              showSnackbar({
                message: 'Đăng nhập thành công',
                status: ComponentStatus.SUCCSESS,
              });

              navigation.replace(RootScreen.navigateView);
            });
            break;
          default:
            break;
        }
      } else {
        // using OTP
        if (isOtp) {
          // check phone
          // submitOtp
          if (otpValue == '' || !confirm) return;
          const rsOtp = await submitOtp(otpValue);
          if (rsOtp === true) {
            const res = await CustomerActions.login(mobile);

            if (res.code === 200) {
              saveDataToAsyncStorage(
                'timeRefresh',
                `${Date.now() / 1000 + 9 * 60}`,
              );
              saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
              saveDataToAsyncStorage(
                'refreshToken',
                `${res.data.refreshToken}`,
              );
              saveDataToAsyncStorage('Mobile', `${mobile}`);
              await CustomerActions.getInfor(dispatch, navigation).then(() => {
                showSnackbar({
                  message: 'Đăng nhập thành công',
                  status: ComponentStatus.SUCCSESS,
                });

                navigation.replace(RootScreen.navigateView);
              });
            } else {
              showSnackbar({
                message: res.message,
                status: ComponentStatus.ERROR,
              });
            }
          }
        }
      }
    } catch (error: any) {
      showSnackbar({
        message: error.message,
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const _forgotPassword = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile) && mobile) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    navigation.push(RootScreen.ForgotPass, {isLogin: true, mobile: mobile});
  };

  return (
    <View pointerEvents={loading ? 'none' : 'auto'} style={styles.container}>
      <FLoading visible={loading} avt={''} />
      {isOtp ? (
        <OTPInput
          autoFocus={false}
          disabled={allowErrorOpt > 5}
          length={6}
          onReSendOtp={() => {
            onCheckPhoneForOtp();
          }}
          onSubmit={_loginAction}
        />
      ) : (
        <KeyboardAvoidingView style={styles.keyboardAvoidingView}>
          <TextFieldForm
            control={methods.control}
            name="Mobile"
            placeholder="Nhập số điện thoại của bạn"
            label="Số điện thoại"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={styles.textFieldStyle}
            register={methods.register}
            prefix={
              <View style={styles.prefixContainer}>
                <Text style={styles.prefixText}>+84</Text>
                <Winicon src="outline/arrows/down-arrow" size={16} />
              </View>
            }
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Password"
            label="Mật khẩu"
            returnKeyType="done"
            placeholder={'Nhập mật khẩu của bạn'}
            errors={methods.formState.errors}
            secureTextEntry={isVisiblePass}
            textFieldStyle={styles.passwordFieldStyle}
            register={methods.register}
            suffix={
              <TouchableOpacity
                style={styles.suffixButton}
                onPress={() => {
                  setVisiblePass(!isVisiblePass);
                }}>
                <Winicon
                  src={
                    isVisiblePass
                      ? `outline/user interface/view`
                      : `outline/user interface/hide`
                  }
                  size={14}
                />
              </TouchableOpacity>
            }
            onBlur={async (ev: string) => {
              var pass = ev.trim();
              if (!regexPassWord.test(pass))
                return methods.setError('Password', {
                  message: 'Mật khẩu sai định dạng, hãy thử lại',
                });
              methods.clearErrors('Password');
            }}
          />
        </KeyboardAvoidingView>
      )}
      {isOtp ? null : (
        <View
          style={[
            styles.actionContainer,
            {paddingTop: methods.formState.errors.Password ? 12 : 0},
          ]}>
          <TouchableOpacity
            onPress={_forgotPassword}
            style={styles.actionButton}>
            <Text style={styles.forgotPasswordText}>Quên mật khẩu?</Text>
          </TouchableOpacity>
          <View />
          <View style={styles.actionButton} />
          {/* <TouchableOpacity
            onPress={async () => {
              var mobile = methods.watch('Mobile')?.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }

              const val = validatePhoneNumber(mobile);
              if (!val) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              // check sdt da dang ky
              const resCustomers = await customerController.aggregateList({
                page: 1,
                size: 1,
                searchRaw: `@Mobile:(${mobile})`,
              });
              if (resCustomers && resCustomers?.data?.length == 0) {
                showSnackbar({
                  message: 'Số điện thoại chưa được đăng ký.',
                  status: ComponentStatus.ERROR,
                });
                setLoading(false);
                return;
              }
              onCheckPhoneForOtp();
            }}
            style={styles.actionButton}>
            <Text style={styles.otpLoginText}>Đăng nhập với OTP</Text>
          </TouchableOpacity> */}
        </View>
      )}

      <View style={styles.buttonContainer}>
        <AppButton
          title={isOtp ? 'Thử số khác' : 'Đăng nhập'}
          textColor={ColorThemes.light.neutral_absolute_background_color}
          textStyle={styles.buttonLoginText}
          disabled={!validationForm}
          containerStyle={styles.primaryButtonContainer}
          borderColor={ColorThemes.light.neutral_main_border_color}
          backgroundColor={ColorThemes.light.primary_main_color}
          onPress={
            isOtp
              ? () => {
                  setIsOtp(false);
                }
              : () => {
                  _loginAction('');
                }
          }
        />
        {bio == 'true' && !isOtp ? (
          <View style={styles.biometricContainer}>
            <LocalAuthen
              isFirstTime={
                methods.watch('LastMobile') !== methods.watch('Mobile') &&
                methods.watch('Mobile')?.length != 0
              }
              onSuccess={async value => {
                if (value === true) {
                  setLoading(true);
                  const mobile = await getDataToAsyncStorage('Mobile');
                  const res = await CustomerActions.login(
                    mobile || methods.watch('Mobile'),
                  );
                  if (res.code === 200) {
                    saveDataToAsyncStorage(
                      'timeRefresh',
                      `${Date.now() / 1000 + 9 * 60}`,
                    );
                    saveDataToAsyncStorage(
                      'accessToken',
                      `${res.data.accessToken}`,
                    );
                    saveDataToAsyncStorage(
                      'refreshToken',
                      `${res.data.refreshToken}`,
                    );
                    await CustomerActions.getInfor(dispatch, navigation).then(
                      () => {
                        setLoading(false);
                        showSnackbar({
                          message: 'Đăng nhập thành công',
                          status: ComponentStatus.SUCCSESS,
                        });
                        navigation.replace(RootScreen.navigateView);
                      },
                    );
                  }
                }
              }}
            />
          </View>
        ) : null}
      </View>

      <AppButton
        title={'Bỏ qua đăng nhập'}
        textColor={ColorThemes.light.primary_main_color}
        textStyle={styles.buttonText}
        containerStyle={styles.skipButtonContainer}
        borderColor={ColorThemes.light.transparent}
        backgroundColor={ColorThemes.light.transparent}
        onPress={async () => {
          removeDataToAsyncStorage('timeRefresh');
          removeDataToAsyncStorage('accessToken');
          removeDataToAsyncStorage('refreshToken');
          removeDataToAsyncStorage('Mobile');
          removeDataToAsyncStorage('Biometrics');
          // const deviceToken = await getDataToAsyncStorage('fcmToken')
          // if (deviceToken)
          //     Clipboard.setString(deviceToken)
          navigation.reset({
            index: 0,
            routes: [{name: RootScreen.navigateView}],
          });
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  keyboardAvoidingView: {
    width: '100%',
    gap: 24,
  },
  textFieldStyle: {
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
  },
  prefixContainer: {
    flexDirection: 'row',
    height: 46,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
  },
  prefixText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  passwordFieldStyle: {
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
    paddingLeft: 16,
    paddingVertical: 16,
  },
  suffixButton: {
    padding: 12,
  },
  actionContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  actionButton: {
    paddingVertical: 8,
    alignItems: 'flex-start',
  },
  forgotPasswordText: {
    ...TypoSkin.body3,
    alignSelf: 'baseline',
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  otpLoginText: {
    ...TypoSkin.body3,
    alignSelf: 'baseline',
    color: ColorThemes.light.primary_main_color,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 8,
    alignItems: 'center',
  },
  buttonText: {
    ...TypoSkin.buttonText1,
  },
  buttonLoginText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.neutral_text_stable_color,
  },
  primaryButtonContainer: {
    height: 48,
    flex: 1,
    borderRadius: 8,
    marginTop: 8,
  },
  biometricContainer: {
    paddingTop: 8,
  },
  skipButtonContainer: {
    height: 48,
    alignSelf: 'center',
    borderRadius: 8,
    marginTop: 32,
  },
});
