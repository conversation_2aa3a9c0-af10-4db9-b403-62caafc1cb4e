/**
 * MyAddress module exports
 */

// Main component
export { default as MyAddress } from './myAddress';

// Sub-components
export { AddressItem } from './components/AddressItem';
export { AddressList } from './components/AddressList';
export { AddressActions } from './components/AddressActions';
export { EmptyAddressState } from './components/EmptyAddressState';

// Hooks
export { useMyAddress } from './hooks/useMyAddress';

// Types
export type {
  AddressItem as AddressItemType,
  MyAddressProps,
  AddressItemProps,
  AddressListProps,
  AddressActionsProps,
  EmptyAddressStateProps,
  UseMyAddressReturn,
} from './types';

// Constants
export { TEXTS, COLORS, DIMENSIONS, ICONS } from './constants';
