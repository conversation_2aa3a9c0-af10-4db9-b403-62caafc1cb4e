import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Winicon, AppButton} from 'wini-mobile-components';
import ScreenHeader from '../../../../../layout/header';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import type {ServiceHeaderProps} from '../types';
import {POPUP_STYLES} from '../constants';

export const ServiceHeader: React.FC<ServiceHeaderProps> = ({
  onBack,
  onShowCriterion,
}) => {
  return (
    <ScreenHeader
      style={POPUP_STYLES}
      children={
        <View style={styles.headerContainer}>
          <View style={styles.leftSection}>
            <TouchableOpacity style={styles.backButton} onPress={onBack}>
              <Winicon
                src="outline/arrows/left-arrow"
                color={ColorThemes.light.neutral_text_subtitle_color}
                size={20}
              />
              <Text style={[TypoSkin.heading8, styles.backButtonText]}>
                Quay lại
              </Text>
            </TouchableOpacity>
          </View>
          <AppButton
            title="Tiêu chí"
            backgroundColor={ColorThemes.light.transparent}
            borderColor="transparent"
            prefixIcon="fill/user interface/c-info"
            prefixIconSize={14}
            containerStyle={styles.criterionButton}
            onPress={onShowCriterion}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
          />
        </View>
      }
    />
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    padding: 12,
    gap: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  criterionButton: {
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
    marginRight: 8,
  },
});
