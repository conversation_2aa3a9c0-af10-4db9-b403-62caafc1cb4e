import React, {useEffect, useRef, useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {
  Alert,
  Dimensions,
  Image,
  ImageBackground,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import FLoading from 'component/Loading/FLoading';
import {
  registerKtx,
  renderCetificateA,
  renderDataRegisterKtx,
  renderTwoCetificateA,
} from '../Ultis/Index';
import CetificateAchievemenDa from '../../CetificateAchievementPage/CetificateAchievemenDa';
import {AppSvg, Winicon} from 'wini-mobile-components';
import iconSvg from 'svgs/iconSvg';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import QRCode from 'react-native-qrcode-svg';
import ViewShot from 'react-native-view-shot';
import {CameraRollHelper} from 'utils/CameraRollHelper';
import brandSvg from 'svgs/brandSvg';

const width = Dimensions.get('window').width;

interface RegisterKtxServicesProps {
  GetToiletInfo: any;
  infoService: any;
}
const RegisterKtxServices: React.FC<RegisterKtxServicesProps> = ({
  GetToiletInfo,
  infoService,
}) => {
  const navigation = useNavigation<any>();
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [dataToiletOperational, setDataToiletOperational] = useState<any[]>([]);

  const [showQRModal, setShowQRModal] = useState(false);
  const viewShotRef = useRef<ViewShot>(null);

  const [url, setUrl] = useState<any>(null);
  const [data12Month, setData12Month] = useState<number>(0);
  const [dataLog, setDataLog] = useState<any>(null);

  useEffect(() => {
    // {"toiletId":"027bee1a0e0a4b3c966dc84f73befe3b"} add to setUrl
    if (GetToiletInfo) setUrl({toiletId: `${GetToiletInfo?.Id}`});
  }, [GetToiletInfo]);
  const getData12Month = async () => {
    const check = await CetificateAchievemenDa.getAllstatisticsByToiletId(
      infoService?.ToiletId,
    );
    if (check?.code === 200) {
      if (check?.data && check.data.length === 0) {
        setData12Month(0);
        return;
      }
      const rows = Array.isArray(check.data) ? check.data : [];
      const filtered = rows.filter((item: any) => item?.Status === 2);
      setData12Month(filtered.length);
    }
  };
  const getDataLog = async () => {
    const check = await CetificateAchievemenDa.getAllLogByToiletId(
      infoService?.ToiletId,
    );
    if (check?.code === 200) {
      let data = check?.data.slice(0, 5);
      setDataLog(data);
    } else {
      setDataLog([]);
    }
  };
  useEffect(() => {
    const imageUrl =
      infoService?.ToiletCertificate?.Img?.split('/api/file/img/')[1];
    if (imageUrl) {
      infoService.ToiletCertificate.Img = imageUrl;
    }
    if (infoService?.Id) {
      setData(infoService);
      setLoading(false);
    } else {
      setLoading(false);
    }
    getData12Month();
  }, [infoService]);

  const downloadQRCode = async () => {
    try {
      if (viewShotRef.current && viewShotRef.current.capture) {
        const uri = await viewShotRef.current.capture();

        // Sử dụng CameraRollHelper để lưu ảnh vào thư viện ảnh
        const result = await CameraRollHelper.saveImage(uri, 'KTX');

        console.log('Saved to camera roll:', result);

        Alert.alert('Thành công!', 'QR Code đã được lưu vào thư viện ảnh', [
          {
            text: 'OK',
            onPress: () => {
              setShowQRModal(false);
            },
          },
        ]);
      }
    } catch (error) {
      console.error('Error downloading QR code:', error);

      // Nếu lỗi do không có quyền, hiển thị dialog yêu cầu quyền
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('quyền')) {
        CameraRollHelper.showPermissionDialog();
      } else {
        Alert.alert(
          'Lỗi',
          `Không thể lưu QR Code: ${errorMessage || 'Lỗi không xác định'}`,
          [{text: 'OK'}],
        );
      }
    }
  };

  useEffect(() => {
    getData12Month();
    getDataLog();
  }, [infoService]);

  useEffect(() => {
    console.log('data-RegisterKtxServices', data);
  }, [data]);

  return (
    <>
      <View style={styles.bannerContainer}>
        <FLoading visible={loading} />
        {data &&
          [0, 1].includes(data.Status) &&
          !data?.ToiletCertificate?.Id &&
          registerKtx(GetToiletInfo?.Id, navigation)}
        {infoService &&
          data?.CateCriterion &&
          !data?.CateCriterion?.Name &&
          !data?.ToiletCertificate?.Id &&
          registerKtx(GetToiletInfo?.Id, navigation)}
        {data &&
          [9].includes(data.Status) &&
          !['Sạch', 'Xanh'].includes(data.CateCriterion.Name) &&
          data?.CateCriterion?.Name?.length > 0 &&
          renderDataRegisterKtx(
            GetToiletInfo?.Id,
            navigation,
            infoService,
            dataToiletOperational,
            data12Month,
            dataLog,
          )}
        {data &&
          data?.ToiletCertificate?.Id &&
          !['Sạch', 'Xanh'].includes(data?.CateCriterion?.Name) &&
          renderCetificateA(
            data?.ToiletCertificate?.Img,
            GetToiletInfo?.Id,
            navigation,
          )}
        {data &&
          data?.ToiletCertificate?.Id &&
          ['Sạch', 'Xanh'].includes(data.CateCriterion.Name) &&
          renderTwoCetificateA(
            data?.ToiletCertificate?.Img,
            GetToiletInfo?.Id,
            navigation,
          )}
        {!data && registerKtx(GetToiletInfo?.Id, navigation)}
      </View>
      <View style={{marginVertical: 10, position: 'relative'}}>
        <TouchableOpacity
          style={{
            flex: 1,
            marginRight: 10,
            alignItems: 'center',
            flexDirection: 'row',
            position: 'absolute',
            right: 0,
          }}
          onPress={() => setShowQRModal(true)}>
          <View>
            <AppSvg SvgSrc={iconSvg.Qr} size={20} />
          </View>
          <Text style={{...TypoSkin.buttonText4, color: '#000'}}>
            {' '}
            Mã QR nhà vệ sinh
          </Text>
        </TouchableOpacity>
      </View>
      <Modal
        visible={showQRModal}
        transparent={true}
        statusBarTranslucent={true}
        hardwareAccelerated={true}
        animationType="fade"
        onRequestClose={() => setShowQRModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.downloadButton}
              onPress={downloadQRCode}>
              <Winicon
                src="fill/user interface/download"
                size={20}
                color="#333"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowQRModal(false)}>
              <Winicon src="fill/layout/circle-xmark" size={20} color="#333" />
            </TouchableOpacity>
          </View>
          {/* ViewShot bao bọn toàn bộ nội dung bao gồm cả ImageBackground */}
          <ViewShot
            ref={viewShotRef}
            options={{format: 'png', quality: 0.9}}
            style={styles.viewShotContainer}>
            <ImageBackground
              source={require('../../../../assets/background-header.png')}
              style={styles.modalContainer}>
              <View style={styles.qrModalContent}>
                {/* QR Code */}
                <View style={styles.modalQRContainer}>
                  <View style={styles.qrCodeWrapper}>
                    {url ? (
                      <View
                        style={{
                          position: 'relative',
                          alignItems: 'center',
                          justifyContent: 'center',
                          alignContent: 'center',
                        }}>
                        {/* add logo vào giữa */}
                        <QRCode
                          value={JSON.stringify(url)}
                          size={180}
                          backgroundColor="white"
                          color="black"
                        />
                        <View style={styles.logoContainer}>
                          <AppSvg SvgSrc={brandSvg.logo} size={28} />
                        </View>
                      </View>
                    ) : null}
                  </View>
                </View>
              </View>
              <View style={styles.descriptionContainer}>
                <Text
                  style={{
                    ...TypoSkin.buttonText4,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  Mã QR nhà vệ sinh
                </Text>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {GetToiletInfo?.Name}
                </Text>
              </View>
            </ImageBackground>
          </ViewShot>
        </View>
      </Modal>
    </>
  );
};
const styles = StyleSheet.create({
  bannerContainer: {
    height: 200,
    position: 'relative',
    overflow: 'hidden',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  qrSection: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  qrContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    overflow: 'hidden',
  },
  qrBackground: {
    width: 200,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrCodeWrapper: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 36,
  },
  linkSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  linkTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
  },
  linkText: {
    flex: 1,
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
  },
  copyButton: {
    padding: 5,
  },
  actionButtonsContainer: {
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 30,
  },
  exportButton: {
    backgroundColor: '#90C8FB',
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
  },
  exportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#343434',
  },
  transferButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
  },
  transferButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  historySection: {
    paddingHorizontal: 20,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginBottom: 15,
    width: '100%',
  },
  headerColumn: {
    flex: 1,
  },
  headerPointsColumn: {
    width: 50,
    alignItems: 'center',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  userList: {
    gap: 0,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingVertical: 12,
    marginBottom: 10,
  },
  userColumn: {
    flex: 1,
  },
  pointsColumn: {
    width: 50,
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  badgeText: {
    fontSize: 12,
    color: '#FF4444',
    fontWeight: '500',
  },
  timeLabel: {
    fontSize: 12,
    color: '#666',
  },
  phoneNumber: {
    fontSize: 12,
    color: '#999',
  },
  points: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4169E1',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewShotContainer: {
    width: width * 0.95,
    height: 350,
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent',
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    top: 170,
    left: 0,
    right: 10,
    zIndex: 10,
    height: 50,
  },
  qrModalText: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 100,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  downloadButton: {
    padding: 6,
    borderRadius: 20,
    width: 32,
    height: 32,
    backgroundColor: '#F5F5F5',
    position: 'absolute',
    top: 10,
    right: 50,
    zIndex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    padding: 6,
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrModalContent: {
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    position: 'relative',
  },
  logoContainer: {
    alignItems: 'center',
    position: 'absolute',
    borderRadius: 10,
    padding: 4,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  logoCircle: {
    justifyContent: 'center',
    alignItems: 'center',
    // marginBottom: 20,
    borderRadius: 10,
    overflow: 'hidden',
  },
  modalQRContainer: {
    alignItems: 'center',
    // marginBottom: 30,
    zIndex: 1,
  },
  modalQRBackground: {
    width: 250,
    height: 250,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalQRWrapper: {
    backgroundColor: 'white',
    // padding: 20,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  userInfoContainer: {
    alignItems: 'center',
    zIndex: 1,
    flexDirection: 'row',
    gap: 10,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 25,
    marginBottom: 10,
  },
  userNameText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  descriptionContainer: {
    alignItems: 'center',
    zIndex: 1,
    marginBottom: 20,
  },
  descriptionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default RegisterKtxServices;
