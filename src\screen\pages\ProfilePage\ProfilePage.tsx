import React, {useEffect, useState} from 'react';
import {RefreshControl, ScrollView, View} from 'react-native';
import {profilePageStyles} from './styles/ProfilePageStyles';
import UserInfo from './components/UserInfo';
import HeaderLogo from '../../layout/headers/HeaderLogo';
import ScrollTabProfile from './components/ScrollTabProfile';
import TabProfile from './components/TabProfile';
import TabPartnerManager from '../../module/partner/component/TabPartnerManager';
import {useDispatch} from 'react-redux';
import {store} from '../../../redux/store/store';
import {PartnerActions} from '../../../redux/reducers/partner/reduce';
import ListSurveyTab from '../../module/surveyTask/List/ListSurveyTab';
import MyListTicket from 'screen/module/ticket/components/MyListTicket';

export default function ProfilePage() {
  const dispatch = useDispatch();
  const [tabId, setTabId] = useState('profile');
  const cusId = store.getState().customer.data?.Id;
  const [isRefresh, setIsRefresh] = useState<boolean>(false);

  const checkPartner = async () => {
    if (!cusId) return;
    // Gọi API và lưu vào Redux store
    await PartnerActions.findOne(dispatch, {
      page: 1,
      size: 10000,
      query: `@CustomerId: {${cusId}}`,
    });
  };
  useEffect(() => {
    checkPartner();
  }, []);
  return (
    <View style={profilePageStyles.container}>
      <HeaderLogo />
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={async () => {
              setIsRefresh(true);
              setTimeout(() => {
                setIsRefresh(false);
              }, 2000);
            }}
          />
        }>
        <UserInfo />
        <ScrollTabProfile
          containerStyle={{marginHorizontal: 16}}
          onTabChange={setTabId}
        />
        {tabId === 'profile' && <TabProfile />}
        {tabId === 'partner' && <TabPartnerManager select="all" />}
        {tabId === 'ticket' && <MyListTicket />}
        {tabId === 'task' && <ListSurveyTab isRefresh={isRefresh} />}
      </ScrollView>
    </View>
  );
}
