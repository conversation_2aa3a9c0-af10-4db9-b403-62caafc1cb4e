import React from 'react';
import {FlatList, RefreshControl, StyleSheet, View} from 'react-native';
import {ColorThemes} from 'assets/skin/colors';
import EmptyPage from 'project-component/empty-page';
import {CardToiletHoriSkeleton} from 'project-component/skeletonCard';
import {FDialog, showDialog} from 'component/export-component';
import {FPopup} from 'component/popup/popup';
import {CustomBottomSheet} from 'project-component/form/DateRangePicker/CustomBottomSheet';
import DeviceListItem from './components/DeviceListItem';
import SearchBar from './components/SearchBar';
import AddDeviceFooter from './components/AddDeviceFooter';
import {useDevicesListTab} from './hooks/useDevicesListTab';

interface Props {
  visible: boolean;
  onClose: () => void;
  data: any;
  refreshing?: any;
  onRefresh?: any;
  disabled?: any;
  formId?: any;
  title?: string;
  height?: number | string;
}

export default function DevicesListTab(props: Props) {
  const {
    visible,
    onClose,
    data,
    refreshing,
    onRefresh,
    disabled,
    formId,
    title = 'Danh sách thiết bị',
    height = '85%',
  } = props;

  const {
    dialogRef,
    popupRef,
    devices,
    isLoading,
    files,
    products,
    searchValue,
    setSearchValue,
    getData,
    deleteItem,
    showAddEdit,
    onRefreshList,
    userId,
  } = useDevicesListTab({data, formId, onRefresh});

  const renderContent = () => (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />

      {!formId ? (
        <SearchBar
          value={searchValue}
          onChange={setSearchValue}
          onSubmit={() => getData({})}
        />
      ) : null}

      <FlatList
        nestedScrollEnabled
        scrollEnabled={formId ? false : true}
        data={devices.data}
        keyExtractor={(item: any) =>
          String(item?.Id ?? item?.Code ?? item?.Name ?? Math.random())
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={onRefreshList}
          />
        }
        style={styles.list}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        renderItem={({item}: any) => (
          <DeviceListItem
            item={item}
            files={files}
            products={products}
            userId={userId}
            toiletCustomerId={data?.CustomerId}
            disabled={disabled}
            formId={formId}
            onEdit={(it: any) => showAddEdit(it)}
            onDelete={(it: any) =>
              showDialog({
                ref: dialogRef,
                status: 'WARNING' as any,
                title: 'Bạn chắc chắn muốn xóa',
                onSubmit: () => {
                  deleteItem([it?.Id]);
                },
              })
            }
          />
        )}
        ListEmptyComponent={() =>
          isLoading && searchValue != '' ? (
            Array.from(Array(10)).map((_, index) => (
              <View key={index} style={styles.skeletonItem}>
                <CardToiletHoriSkeleton />
              </View>
            ))
          ) : (
            <EmptyPage title="Nhà vệ sinh chưa có thiết bị nào" />
          )
        }
        ListFooterComponent={() => <View style={styles.footerSpacer} />}
      />

      {!disabled ? <AddDeviceFooter onAdd={() => showAddEdit(null)} /> : null}
    </View>
  );

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title={title}
      height={height}
      isShowConfirm={false}>
      {renderContent()}
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  list: {flex: 1, marginVertical: 16},
  separator: {height: 16},
  skeletonItem: {marginBottom: 16},
  footerSpacer: {height: 65},
});
