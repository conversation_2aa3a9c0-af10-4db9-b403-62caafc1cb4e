import { useState } from 'react';

export const useFAQExpansion = () => {
  const [expandedFAQs, setExpandedFAQs] = useState<Set<string>>(new Set());

  const toggleFAQExpand = (itemId: string) => {
    const newExpandedFAQs = new Set(expandedFAQs);
    if (newExpandedFAQs.has(itemId)) {
      newExpandedFAQs.delete(itemId);
    } else {
      newExpandedFAQs.add(itemId);
    }
    setExpandedFAQs(newExpandedFAQs);
  };

  const isExpanded = (itemId: string) => {
    return expandedFAQs.has(itemId);
  };

  const collapseAll = () => {
    setExpandedFAQs(new Set());
  };

  const expandAll = (itemIds: string[]) => {
    setExpandedFAQs(new Set(itemIds));
  };

  return {
    expandedFAQs,
    toggleFAQExpand,
    isExpanded,
    collapseAll,
    expandAll,
  };
};
