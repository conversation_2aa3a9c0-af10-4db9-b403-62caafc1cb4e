import React from 'react';
import TicketCard from '../../TicketCard';
import {TicketType as TicketTypeInterface} from 'types/ticketType';

interface TicketListItemProps {
  item: any;
  index: number;
  relatives: any[];
  methods: any;
  typeLabel: string;
  onUpdateTicket: (updatedTicket: TicketTypeInterface) => void;
}

const TicketListItem: React.FC<TicketListItemProps> = ({
  item,
  index,
  relatives,
  methods,
  typeLabel,
  onUpdateTicket,
}) => {
  let relativeData = undefined;

  if (item?.ToiletId) {
    relativeData = relatives.find(e => e?.Id === item?.ToiletId);
  } else if (item?.ToiletServicesId) {
    relativeData = relatives.find(e => e?.Id === item?.ToiletServicesId);
  }

  const _files = methods.watch('_files') ?? [];
  const _fileInfor = _files.filter((e: any) => item.File?.includes(e.Id));

  return (
    <TicketCard
      key={index}
      index={index}
      relativeData={relativeData}
      item={item}
      edit={false}
      onUpdateTicket={onUpdateTicket}
      fileInfor={_fileInfor}
      methods={methods}
      typeLabel={typeLabel}
    />
  );
};

export default TicketListItem;
