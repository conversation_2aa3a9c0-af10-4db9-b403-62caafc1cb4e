import {FlatList, View, Text, ActivityIndicator} from 'react-native';
import SelectToiletCriterionBottomSheet from './components/SelectToiletCriterionBottomSheet';
import ToiletSelectionCard from 'screen/module/toilet/components/card/ToiletSelectionCard';
import useListToiletCriterion from './hooks/useListToiletCriterion';
import {ToiletServiceStatus} from 'screen/module/service/components/da';

const ListToiletCriterion = ({
  toiletServiceId,
  toiletServiceStatus,
}: {
  toiletServiceId: string;
  toiletServiceStatus: number;
}) => {
  const {
    isLoading,
    isError,
    toiletSelected,
    toiletCriterions,
    criterions,
    cateCriterions,
    isBsSelectCriterion,
    setIsBsSelectCriterion,
    selectedToilet,
    handleToiletCardPress,
    findToiletCriterionByToiletId,
    refreshToiletCriterions,
    markToiletAsUpdated,
  } = useListToiletCriterion({toiletServiceId});

  const getDisabled = () => {
    if (toiletSelected.length <= 0) return true;
    return toiletServiceStatus > ToiletServiceStatus.research;
  };

  // Loading state
  if (isLoading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" />
        <Text style={{marginTop: 10}}>Đang tải dữ liệu...</Text>
      </View>
    );
  }

  // Error state
  if (isError) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text style={{color: 'red', textAlign: 'center'}}>
          Có lỗi xảy ra khi tải dữ liệu
        </Text>
      </View>
    );
  }

  return (
    <View style={{flex: 1}}>
      {toiletSelected.length > 0 && (
        <FlatList
          data={toiletSelected}
          scrollEnabled={false}
          ListHeaderComponent={() => {
            if (toiletSelected.length <= 0) return null;
            return (
              <ToiletSelectionCard
                item={{
                  Id: 'all',
                  Name: 'Chọn tất cả',
                  Address: 'Chọn tất cả nhà vệ sinh',
                  CustomerId: '',
                  Status: 1,
                }}
                showSelect={false}
                isSelected={true}
                onPress={() =>
                  handleToiletCardPress({
                    Id: 'all',
                    Name: 'Chọn tất cả',
                    Address: 'Chọn tất cả nhà vệ sinh',
                    CustomerId: '',
                    Status: 1,
                    DateCreated: 0,
                    Certificate: 0,
                    Description: '',
                    Lat: 0,
                    Long: 0,
                    Mobile: '',
                    IsRegistered: false,
                  })
                }
              />
            );
          }}
          renderItem={({item}) => (
            <ToiletSelectionCard
              item={item}
              showSelect={false}
              onPress={() => handleToiletCardPress(item)}
            />
          )}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          style={{marginBottom: 16}}
        />
      )}
      <View style={{height: 100}}></View>
      <SelectToiletCriterionBottomSheet
        visible={isBsSelectCriterion}
        disabled={getDisabled()}
        onClose={() => setIsBsSelectCriterion(false)}
        cateCriterionData={
          cateCriterions?.map(cateCriterion => ({
            ...cateCriterion,
            Criterions: criterions.filter(
              criterion => criterion.CateCriterionId === cateCriterion.Id,
            ),
          })) || []
        }
        toiletId={selectedToilet?.Id || ''}
        toiletName={selectedToilet?.Name || ''}
        toiletCriterion={
          selectedToilet
            ? findToiletCriterionByToiletId(selectedToilet.Id)
            : null
        }
        cateCriterionId={cateCriterions?.[0]?.Id}
        onConfirmSuccess={async () => {
          // Refresh toilet criterions data after successful save
          await refreshToiletCriterions();
        }}
        toiletSelected={toiletSelected}
        toiletCriterions={toiletCriterions}
        markToiletAsUpdated={markToiletAsUpdated}
        toiletServiceId={toiletServiceId}
      />
    </View>
  );
};

export default ListToiletCriterion;
