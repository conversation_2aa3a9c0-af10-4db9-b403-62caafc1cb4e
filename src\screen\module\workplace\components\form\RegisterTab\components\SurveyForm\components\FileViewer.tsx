import React from 'react';
import {View, TouchableOpacity, StyleSheet, Dimensions} from 'react-native';
import {ColorThemes} from '../../../../../../../../../assets/skin/colors';
import {Winicon} from '../../../../../../../../../component/export-component';
import ScreenHeader from '../../../../../../../../layout/header';
import WebView from 'react-native-webview';
import ConfigAPI from '../../../../../../../../../config/configApi';
import {closePopup} from '../../../../../../../../../component/popup/popup';

interface FileViewerProps {
  item: {
    Url: string;
    Name?: string;
  };
  popupRef: any;
}

export const FileViewer: React.FC<FileViewerProps> = ({item, popupRef}) => {
  return (
    <View style={styles.fileViewerContainer}>
      <ScreenHeader
        style={styles.fileViewerHeader}
        title={`Xem nhanh`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(popupRef)}
            style={styles.closeButton}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <WebView
        style={styles.webView}
        source={{
          uri: ConfigAPI.url.replace('/api/', '') + item.Url,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  fileViewerContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  fileViewerHeader: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  webView: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});
