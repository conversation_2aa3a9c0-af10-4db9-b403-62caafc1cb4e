import React from 'react';
import {StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {FPopup, showPopup, closePopup} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {ServiceHeader} from './components/ServiceHeader';
import {ToiletSelector} from './components/ToiletSelector';
import {CriterionPopup} from './components/CriterionPopup';
import {TABS} from './constants';
import {useServicesWorkFlow} from './hooks/useServicesWorkFlow';
import {ServiceContent} from './components/ServiceContent';
import RegisterCateCriterionPage from '../../../../pages/RegisterCateCriterionPage/RegisterCateCriterionPage';

export default function ServicesWorkFlow() {
  const {
    tab,
    setTab,
    selected,
    setSelected,
    dataCriterion,
    popupRef,
    myToilet,
    routeParams,
    navigation,
  } = useServicesWorkFlow();

  const {type: serviceType, serviceId, customer} = routeParams;

  const handleBack = () => {
    navigation.pop();
  };

  const handleShowCriterion = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <CriterionPopup
          data={dataCriterion}
          onClose={() => closePopup(popupRef)}
        />
      ),
    });
  };

  if (serviceType === 'netzero') {
    return <RegisterCateCriterionPage serviceId={serviceId || ''} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <FPopup ref={popupRef} />

      <ServiceHeader
        onBack={handleBack}
        onShowCriterion={handleShowCriterion}
      />

      {tab === TABS.SERVICE_FORM ? (
        <ServiceContent
          serviceType={serviceType}
          serviceId={serviceId}
          customer={customer}
          selectedToilet={selected}
        />
      ) : (
        <ToiletSelector
          toilets={myToilet}
          selectedToilet={selected}
          onSelectToilet={setSelected}
          onSkip={() => setTab(TABS.SERVICE_FORM)}
          serviceType={serviceType}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});
