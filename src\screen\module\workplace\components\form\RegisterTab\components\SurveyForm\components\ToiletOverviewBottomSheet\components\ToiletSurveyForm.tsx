import React, {memo} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {TypoSkin} from '../../../../../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../../../../../assets/skin/colors';
import {
  ToiletSurveyTypeStrings,
  TypeStringData,
} from '../../../../../../../../../service/components/da';
import {FCheckbox} from '../../../../../../../../../../../component/export-component';
import {
  Fselect1Form,
  TextFieldForm,
} from '../../../../../../../../../../../project-component/component-form';
import {FileUploadSection} from '../../FileUploadSection';

interface ToiletSurveyFormProps {
  updateDesign: any;
  methods: any;
  noDesign: boolean;
  fileUploadLoading?: boolean;
  onTogglePassDesign: () => void;
  onShowFilePicker: () => void;
  onRemoveFile: (item: any) => void;
  onOpenFileViewer: (item: any) => void;
}

export const ToiletSurveyForm: React.FC<ToiletSurveyFormProps> = memo(
  ({
    updateDesign,
    methods,
    noDesign,
    fileUploadLoading = false,
    onTogglePassDesign,
    onShowFilePicker,
    onRemoveFile,
    onOpenFileViewer,
  }) => {
    const currentFiles = updateDesign;
    console.log(
      'ToiletSurveyForm - currentFiles:',
      currentFiles,
      'length:',
      currentFiles.length,
    );

    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollArea}
        contentContainerStyle={styles.scrollContent}>
        <View style={styles.formContainer}>
          <TextFieldForm
            label="Tên"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Name"
          />
          <Fselect1Form
            label="Tiêu chí nhà vệ sinh"
            placeholder="Tiêu chí nhà vệ sinh"
            name="Type"
            control={methods.control}
            errors={methods.formState.errors}
            style={styles.selectField}
            options={ToiletSurveyTypeStrings.map(item => ({
              id: item.key,
              name: item.title,
            }))}
          />
          <Fselect1Form
            label="Loại hình nhà vệ sinh"
            placeholder="Loại hình nhà vệ sinh"
            name="ToiletType"
            control={methods.control}
            errors={methods.formState.errors}
            style={styles.selectField}
            options={TypeStringData.map(item => ({
              id: item.key,
              name: item.title,
            }))}
          />
          <TouchableOpacity
            onPress={onTogglePassDesign}
            style={styles.checkboxRow}>
            <FCheckbox value={noDesign} onChange={onTogglePassDesign} />
            <Text style={styles.checkboxLabel}>Bỏ qua thiết kế</Text>
          </TouchableOpacity>
          <Fselect1Form
            label="Tần suất"
            placeholder="Tần suất"
            name="Frequency"
            control={methods.control}
            errors={methods.formState.errors}
            style={styles.selectField}
            options={[
              {id: 1, name: 'Thường xuyên'},
              {id: 2, name: 'Theo khung giờ'},
              {id: 3, name: 'Không thường xuyên'},
            ]}
          />
          <TextFieldForm
            label="Tình trạng"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Condition"
          />
          <TextFieldForm
            label="Tình trạng thiết bị"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="DeviceCondition"
          />
          <TextFieldForm
            label="Độ tuổi"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Age"
          />
          <FileUploadSection
            files={currentFiles}
            onShowFilePicker={onShowFilePicker}
            onRemoveFile={onRemoveFile}
            onOpenFileViewer={onOpenFileViewer}
            loading={fileUploadLoading}
          />
          <TextFieldForm
            label="Tần suất dọn dẹp"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Clean"
          />
          <TextFieldForm
            label="Công năng"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Feature"
          />
          <Fselect1Form
            label="Địa điểm xây dựng"
            placeholder="Địa điểm xây dựng"
            name="Position"
            control={methods.control}
            errors={methods.formState.errors}
            style={styles.selectField}
            options={[
              {id: 1, name: 'Ngoài trời'},
              {id: 0, name: 'Gắn liền với tòa nhà'},
            ]}
          />
          <Fselect1Form
            label="Bảo trì, bảo dưỡng"
            placeholder="Bảo trì, bảo dưỡng"
            name="Maintain"
            control={methods.control}
            errors={methods.formState.errors}
            style={styles.selectField}
            options={[
              {id: 1, name: 'Định kỳ'},
              {id: 2, name: 'Không bảo trì/bảo dưỡng'},
              {id: 3, name: 'Theo tình trạng hư hỏng'},
            ]}
          />
          <TextFieldForm
            label="Quy mô"
            textFieldStyle={styles.textField}
            style={styles.fullWidth}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Size"
          />
          <TextFieldForm
            control={methods.control}
            name="Description"
            label="Mô tả khác"
            errors={methods.formState.errors}
            placeholder={'Mô tả khác'}
            style={styles.descriptionField}
            textFieldStyle={styles.descriptionTextField}
            textStyle={styles.descriptionText}
            numberOfLines={10}
            multiline={true}
            register={methods.register}
          />
        </View>
      </ScrollView>
    );
  },
);

const styles = StyleSheet.create({
  scrollArea: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  formContainer: {
    flex: 1,
    gap: 16,
  },
  fullWidth: {
    width: '100%',
  },
  textField: {
    padding: 16,
  },
  selectField: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  checkboxRow: {
    alignSelf: 'baseline',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkboxLabel: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  descriptionField: {
    backgroundColor: ColorThemes.light.transparent,
  },
  descriptionTextField: {
    height: 100,
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    justifyContent: 'flex-start',
    backgroundColor: ColorThemes.light.transparent,
  },
  descriptionText: {
    textAlignVertical: 'top',
  },
});
