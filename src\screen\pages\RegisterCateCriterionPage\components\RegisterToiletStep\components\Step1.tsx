import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Pressable,
} from 'react-native';
import {
  showSnackbar,
  Winicon,
} from '../../../../../../component/export-component';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {
  closePopup,
  ComponentStatus,
  FPopup,
  showPopup,
} from 'wini-mobile-components';
import RegisterToiletFormBottom from '../../RegisterToiletFormBottom';
import {AddEditToiletPopup} from '../../../../../module/toilet/components/form/AddEditToilet';
import {ToiletItem} from '../../../../../../types/toiletType';
import {ToiletDa} from '../../../../../module/toilet/toiletDa';
import {useSelectorCustomerState} from '../../../../../../redux/hooks/hooks';
import LoadingIndicator from '../../../../CheckoutPage/components/LoadingIndicator';
import {navigateBack} from '../../../../../../router/router';

import ToiletSelectionCard from '../../../../../module/toilet/components/card/ToiletSelectionCard';

interface Step1Props {
  onNext: (toilets: string[]) => void;
  levelCateCriterion: number;
}

export default function Step1({onNext, levelCateCriterion}: Step1Props) {
  const popupRef = useRef<any>();
  const customer = useSelectorCustomerState().data;
  const toiletDa = new ToiletDa();
  const [data, setData] = useState<ToiletItem[]>([]);
  const [selected, setSelected] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const initData = async () => {
    try {
      if (!customer) return;
      setLoading(true);
      const res = await toiletDa.fetchByCustomerAndCheckRegister(
        customer.Id,
        levelCateCriterion,
      );
      if (res.length) {
        setData(res);
      }
      setLoading(false);
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra. Vui lòng thử lại',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const onCreateToilet = async (data: ToiletItem) => {
    try {
      setLoading(true);
      const res = await toiletDa.create(data);
      if (res.code === 200) {
        initData();
      }
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra. Vui lòng thử lại',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const toggle = (id: string) => {
    const copy = [...selected];
    const index = copy.indexOf(id);
    if (index > -1) {
      copy.splice(index, 1); // Remove if exists
    } else {
      copy.push(id); // Add if doesn't exist
    }
    setSelected(copy);
  };

  const handleAddToilet = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <AddEditToiletPopup
          ref={popupRef}
          onDone={(data: any) => {
            closePopup(popupRef);
            onCreateToilet(data);
          }}
          toiletId={''}
        />
      ),
    });
  };

  const handleSubmit = () => {
    if (selected.length === 0) {
      showSnackbar({
        message: 'Vui lòng chọn ít nhất 1 nhà vệ sinh',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    onNext(selected);
  };

  useEffect(() => {
    initData();
  }, []);

  return (
    <View style={styles.stepContainer}>
      <FPopup ref={popupRef} />
      <View style={styles.header}>
        <Text style={styles.title}>Chọn nhà vệ sinh bạn muốn đăng ký:</Text>
        <TouchableOpacity style={styles.btnAdd} onPress={handleAddToilet}>
          <Winicon
            src="outline/user interface/c-add"
            size={16}
            color={ColorThemes.light.white}
          />
          <Text style={styles.btnAddText}>Thêm mới NVS</Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <LoadingIndicator isLoading={true} text="Đang tải dữ liệu..." />
      ) : (
        <FlatList
          data={data}
          keyExtractor={item => item.Id}
          renderItem={({item}) => (
            <ToiletSelectionCard
              item={item}
              isSelected={selected.includes(item.Id)}
              disabled={item.IsRegistered}
              onPress={toggle}
              warningText={
                item.IsRegistered ? 'Nhà vệ sinh đã được đăng ký' : undefined
              }
            />
          )}
          showsVerticalScrollIndicator={false}
        />
      )}
      <View style={{paddingHorizontal: 12, marginBottom: 16}}>
        <RegisterToiletFormBottom
          onConfirm={handleSubmit}
          onCancel={navigateBack}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  stepContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 16,
  },
  btnAdd: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 6,
  },
  btnAddText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
});
