import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootScreen} from '../../../../router/router';
import PostDetail from '../../../module/community/detail/postDetail';
import GroupIndex from '../../../module/community/groups';
import AllGroupsLoadMore from '../../../module/community/groups/listview/allGroupsLoadMore';
import CreatePost from '../../../module/community/pages/createPost';
import CommunityLayout from '../../../module/community/pages/layout';
import ProfileCommunity from '../../../module/community/pages/profileIndex';
import NotifyCommunity from '../../../module/community/pages/Notif';

const CommunityNavigation: React.FC = () => {
  const Stack = createNativeStackNavigator();
  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      <Stack.Screen
        name={RootScreen.CommunityLayout}
        component={CommunityLayout}
      />
      {/* groups */}
      <Stack.Screen
        name={RootScreen.GroupIndex}
        component={GroupIndex}
        // options={{
        //   animation: 'slide_from_bottom',
        //   animationDuration: 250,
        // }}
      />
      <Stack.Screen
        name={RootScreen.AllGroupsLoadMore}
        component={AllGroupsLoadMore}
      />
      <Stack.Screen name={RootScreen.PostDetail} component={PostDetail} />
      <Stack.Screen
        name={RootScreen.ProfileCommunity}
        component={ProfileCommunity}
      />
      <Stack.Screen
        name={RootScreen.NotifCommunity}
        component={NotifyCommunity}
      />
      <Stack.Screen
        name={RootScreen.createPost}
        component={CreatePost}
        options={{
          animation: 'slide_from_bottom',
          animationDuration: 250,
        }}
      />
    </Stack.Navigator>
  );
};

export default CommunityNavigation;
