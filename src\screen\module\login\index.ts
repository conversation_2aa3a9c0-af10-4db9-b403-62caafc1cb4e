// Export các components ch<PERSON>h của Login module
export {default as <PERSON><PERSON>} from './login';
export {LoginView} from './LoginView';
export {RegisterView} from './RegisterView';

// Types và interfaces
export interface LoginFormData {
  Mobile: string;
  Password: string;
  LastMobile?: string;
}

export interface RegisterFormData extends LoginFormData {
  Name: string;
  ConfirmPassword: string;
}

export interface AuthResponse {
  code: number;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
  };
}
