{"name": "rncore", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-camera-roll/camera-roll": "^7.10.2", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-native-fontawesome": "^0.3.2", "@notifee/react-native": "^9.1.3", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/slider": "4.5.6", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/auth": "^21.6.1", "@react-native-firebase/messaging": "^21.6.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/drawer": "^7.5.6", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@reduxjs/toolkit": "^2.4.0", "@wuba/react-native-echarts": "^1.3.1", "axios": "^1.7.9", "date-fns": "^4.1.0", "react": "18.3.1", "react-dom": "^19.0.0", "react-hook-form": "^7.53.2", "react-i18next": "^15.4.1", "react-native": "0.76.3", "react-native-animated-spinkit": "^1.5.2", "react-native-biometrics": "^3.0.1", "react-native-blob-util": "^0.21.2", "react-native-calendars": "^1.1308.0", "react-native-date-picker": "^5.0.7", "react-native-device-info": "^14.0.2", "react-native-document-picker": "^9.3.1", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-crop-picker": "^0.41.6", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.20.1", "react-native-modal": "^13.0.1", "react-native-pager-view": "^6.7.0", "react-native-paper": "^5.12.5", "react-native-reanimated": "3.16.7", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "4.10.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-skia": "^0.0.1", "react-native-svg": "^15.11.2", "react-native-swiper": "^1.6.0-rc.1", "react-native-swiper-flatlist": "^3.2.5", "react-native-tab-view": "^4.0.5", "react-native-view-shot": "^4.0.3", "react-native-qrcode-svg": "^6.3.15", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.3", "react-native-webview": "^13.13.2", "react-redux": "^9.1.2", "uuid": "^11.0.3", "validate.js": "^0.13.1", "wini-mobile-components": "^1.0.53", "rn-qr-generator": "^1.4.4"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/runtime": "^7.26.0", "@react-native-community/cli": "15.1.2", "@react-native-community/cli-platform-android": "15.1.2", "@react-native-community/cli-platform-ios": "15.1.2", "@react-native/babel-preset": "0.76.3", "@react-native/eslint-config": "0.76.3", "@react-native/metro-config": "0.76.3", "@react-native/typescript-config": "0.76.3", "@types/react": "^18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^9.16.0", "jest": "^29.7.0", "prettier": "3.4.1", "react-test-renderer": "18.3.1", "typescript": "5.7.2", "babel-plugin-module-resolver": "^5.0.2"}, "engines": {"node": ">=18"}, "overrides": {"react-native-screens": "4.10.0", "react-native-safe-area-context": "^4.12.0", "react-native-reanimated": "3.16.7"}}