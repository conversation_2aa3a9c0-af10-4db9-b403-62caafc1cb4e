import React, {useState, useRef, useEffect, useMemo, forwardRef} from 'react';
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Pressable,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {DataController} from '../../../../base-controller';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {Ultis} from '../../../../../utils/Utils';
import ListTile from '../../../../../component/list-tile/list-tile';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import {
  FCheckbox,
  FNumberPicker,
  FSelect1,
  FTextField,
  Winicon,
} from '../../../../../component/export-component';
import AppButton from '../../../../../component/button';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import {useForm} from 'react-hook-form';
import ScreenHeader from '../../../../layout/header';
import WScreenFooter from '../../../../layout/footer';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {CardHoriSkeleton} from '../../../../../project-component/skeletonCard';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import {CustomerType} from '../../../../../redux/reducers/user/da';
import {ProductType} from '../da';
import EmptyPage from '../../../../../project-component/empty-page';

interface Props {
  methods?: any;
  forConsultant?: boolean;
  type?: string;
  display?: string;
}

const height = Dimensions.get('screen').height;

export default function SelectProductStep(props: Props) {
  const {
    methods,
    forConsultant = false,
    display = 'flex',
    type = 'all',
  } = props;

  const [selectedCate, setSelectedCate] = useState<any>();
  const [cate, setCate] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;

  useEffect(() => {
    const cateController = new DataController('Category');
    const faqList = methods.watch('FAQ') ?? [];
    cateController.getAll().then(async res => {
      if (res.code === 200) {
        const productQueryList = [];
        if (
          !user ||
          (user?.Type !== CustomerType.partner &&
            owner?.Type !== CustomerType.partner)
        ) {
          productQueryList.push(`@IsPublic:{true}`);
          productQueryList.push(`@IsPublic:{false}`);
        } else if (owner?.Type === CustomerType.partner)
          productQueryList.push(
            `(@CompanyProfileId:{${owner.CompanyProfileId}}) | (@CustomerId:{${user.Id} | ${owner.Id}}) | (@IsPublic:{true}) | (@IsPublic:{false})`,
          );
        else
          productQueryList.push(
            `(@CustomerId:{${user.Id}}) | (@IsPublic:{true}) | (@IsPublic:{false})`,
          );
        switch (type) {
          case 'bio':
            productQueryList.push(
              `@Type:[${ProductType.bio} ${ProductType.bio}]`,
            );
            break;
          case 'device':
            productQueryList.push(
              `-@Type:[${ProductType.bio} ${ProductType.bio}]`,
            );
            break;
          default:
            break;
        }
        const productController = new DataController('Product');
        const count = await productController.group({
          searchRaw: productQueryList.join(' '),
          reducers: 'GROUPBY 1 @CategoryId REDUCE COUNT 0 AS _count',
        });
        const result = res.data.map((e: any) => ({
          ...e,
          _count:
            count.data.find((el: any) => el.CategoryId === e.Id)?.['_count'] ??
            0,
        }));
        const parentCate = result
          .filter((e: any) => !e.ParentId)
          .sort((a: any, b: any) => b.Sort - a.Sort);
        if (forConsultant) {
          if (type === 'bio')
            setSelectedCate('3e4fa350dc6a4f20983e5c0d375d812a');
          else setSelectedCate('782445755c884d14ba0eb40bde42f949');
        } else {
          setSelectedCate(parentCate[0]?.Id);
        }
        const filterParentCate = Array<any>();
        parentCate.forEach((e: any) => {
          if (faqList.length) {
            var children = result
              ?.filter(
                (el: any) =>
                  el?.ParentId === e?.Id &&
                  faqList.some(
                    (faq: any) =>
                      faq?.CategoryId === el.Id || faq?.CategoryId === e.Id,
                  ),
              )
              .sort(({a, b}: any) => b?.Sort - a?.Sort);
          } else {
            children = result
              ?.filter((el: any) => el?.ParentId === e?.Id)
              .sort(({a, b}: any) => b?.Sort - a?.Sort);
          }
          methods.setValue(e.Id, {
            data: children,
            totalCount: children?.length,
          });
          if (children.length) filterParentCate.push(e);
        });
        setCate(filterParentCate);
      }
    });
  }, []);

  const formDevices = () => {
    return (
      <View style={{}}>
        {!forConsultant ? (
          <FlatList
            horizontal={true}
            data={cate}
            showsHorizontalScrollIndicator={false}
            style={{height: 35, marginVertical: forConsultant ? 3 : 6}}
            keyExtractor={(item, index) => `${item.Id}`}
            ItemSeparatorComponent={() => <View style={{width: 8}} />}
            renderItem={({item, index}: {item: any; index: number}) => {
              const childrenId = (methods.watch(item.Id)?.data ?? []).map(
                (e: {Id: any}) => e.Id,
              );
              const countDevice = methods
                .watch('devices')
                .filter(
                  (e: {CategoryId: any; Id: any}) =>
                    childrenId.includes(e.CategoryId) && e.Id,
                ).length;
              return (
                <TouchableOpacity
                  key={`cate${index}`}
                  onPress={() => {
                    setSelectedCate(item.Id);
                  }}
                  style={{
                    paddingVertical: 4,
                    paddingHorizontal: 8,
                    borderRadius: 16,
                    backgroundColor:
                      selectedCate === item.Id
                        ? ColorThemes.light
                            .neutral_absolute_reverse_background_color
                        : ColorThemes.light.neutral_absolute_background_color,
                    borderColor: ColorThemes.light.neutral_main_border_color,
                  }}>
                  <Text
                    style={[
                      TypoSkin.regular2,
                      {
                        color:
                          selectedCate === item.Id
                            ? ColorThemes.light
                                .neutral_absolute_background_color
                            : ColorThemes.light
                                .neutral_absolute_reverse_background_color,
                      },
                    ]}>
                    {item?.Name ?? ''}
                    {countDevice ? ` (${countDevice ?? ''})` : ''}
                  </Text>
                </TouchableOpacity>
              );
            }}
            onEndReachedThreshold={0.1}
            ListEmptyComponent={() =>
              [0, 1, 2, 3].map((_, index) => (
                <SkeletonPlaceholder key={index}>
                  <SkeletonPlaceholder.Item
                    height={32}
                    width={56}
                    borderRadius={16}
                    marginRight={8}
                  />
                </SkeletonPlaceholder>
              ))
            }
          />
        ) : (
          <View style={{height: 12}} />
        )}
        <FlatList
          data={methods.watch(selectedCate)?.data}
          style={{height: '100%', paddingBottom: 16}}
          keyExtractor={(item, index) => `${item.Id}`}
          renderItem={({item, index}: {item: any; index: number}) => {
            return (
              <View
                key={item.Id}
                style={{
                  marginBottom:
                    index === methods.watch(selectedCate)?.data.length - 1
                      ? 100
                      : 0,
                }}>
                <CateTile
                  item={item}
                  methods={methods}
                  showPopupSelectProduct={showPopupSelectProduct}
                />
              </View>
            );
          }}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={() =>
            [0, 1, 2, 3].map((_, index) => <CardHoriSkeleton key={index} />)
          }
        />
      </View>
    );
  };

  const showPopupSelectProduct = (item: any) => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupSelectProduct
          ref={popupRef}
          item={item}
          type={type}
          selecteds={methods
            .getValues('devices')
            .filter((e: any) => e.CategoryId === item.Id && e.Id)}
          onDone={items => {
            methods.setValue('devices', [
              ...methods
                .getValues('devices')
                .filter((e: any) => e.CategoryId !== item.Id),
              ...items.map(e => ({...e, _Quantity: e['_Quantity'] ?? 1})),
            ]);
            closePopup(popupRef);
          }}
        />
      ),
    });
  };

  return (
    <View
      style={{
        padding: 16,
        display: display === 'flex' ? 'flex' : 'none',
        flex: 1,
        paddingBottom: forConsultant ? 0 : 168,
      }}>
      <FPopup ref={popupRef} />
      {!forConsultant && (
        <Pressable
          style={{
            borderColor: 'transparent',
            borderBottomColor: ColorThemes.light.neutral_main_border_color,
            borderWidth: 1,
          }}>
          <Text
            style={[
              TypoSkin.heading5,
              {
                color: ColorThemes.light.neutral_text_title_color,
                paddingBottom: 4,
              },
            ]}>
            Chọn sản phẩm
          </Text>
          <Text
            style={[
              TypoSkin.body3,
              {
                color: ColorThemes.light.neutral_text_body_color,
                paddingBottom: 8,
              },
            ]}>
            Chọn một hoặc nhiều sản phẩm. Bạn có thể thay đổi, bổ sung khi đến
            giai đoạn ký hợp đồng cũng được
          </Text>
        </Pressable>
      )}
      {/* tổng */}
      {!forConsultant && (
        <ListTile
          style={{padding: 0, borderRadius: 0, paddingVertical: 4}}
          title={`${methods.watch('devices').length} sản phẩm đã chọn`}
          titleStyle={[
            TypoSkin.body3,
            {color: ColorThemes.light.neutral_text_body_color},
          ]}
          trailing={
            <Text
              style={[
                TypoSkin.heading6,
                {
                  color: ColorThemes.light.neutral_text_title_color,
                },
              ]}>
              {Ultis.money(
                methods
                  .watch('devices')
                  .map((e: any) => e.Price * e['_Quantity'])
                  .reduce((a: any, b: any) => a + b, 0),
              )}
              VNĐ
            </Text>
          }
        />
      )}
      {/* cate */}
      {formDevices()}
    </View>
  );
}

const CateTile = ({
  item,
  showPopupSelectProduct,
  methods,
}: {
  item: any;
  showPopupSelectProduct: any;
  methods: any;
}) => {
  const [onOpen, setOnOpen] = useState(true);
  const listDevices = useMemo(() => {
    return methods
      .getValues('devices')
      .filter((e: any) => e.CategoryId === item.Id && e.Id);
  }, [methods.watch('devices')]);
  return (
    <ListTile
      key={item.Id}
      leading={
        <SkeletonImage
          source={{uri: ConfigAPI.imgUrlId + item.Img}}
          style={{width: 32, height: 32, borderRadius: 4, objectFit: 'cover'}}
        />
      }
      title={`${item.Name ?? ''} (${item._count ?? 0})`}
      titleStyle={[
        TypoSkin.heading7,
        {color: ColorThemes.light.neutral_text_title_color},
      ]}
      subtitle={
        listDevices.length
          ? `${listDevices.length} sản phẩm đã chọn`
          : 'Chọn sản phẩm'
      }
      subTitleStyle={[
        TypoSkin.subtitle4,
        {color: ColorThemes.light.neutral_text_subtitle_color},
      ]}
      listtileStyle={{gap: 16}}
      style={{
        borderBottomColor: ColorThemes.light.neutral_main_border_color,
        borderBottomWidth: 1,
      }}
      onPress={() => {
        if (listDevices.length == 0) {
          showPopupSelectProduct(item);
        } else {
          setOnOpen(!onOpen);
        }
      }}
      trailing={
        listDevices.length == 0 ? (
          <Winicon
            src={'outline/layout/circle-plus'}
            size={23}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        ) : (
          <Winicon
            src={
              onOpen
                ? 'outline/arrows/arrow-sm-down'
                : 'fill/arrows/arrow-sm-right'
            }
            size={28}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        )
      }
      bottom={
        <View
          style={{
            marginTop: 8,
            alignContent: 'flex-start',
            width: '100%',
          }}>
          {onOpen
            ? listDevices?.map((devItem: any) => {
                return (
                  <ListTile
                    key={devItem.Id}
                    leading={
                      <SkeletonImage
                        source={{uri: ConfigAPI.imgUrlId + devItem.Img}}
                        style={{width: 56, height: 56, borderRadius: 4}}
                      />
                    }
                    style={{padding: 0, borderRadius: 0, paddingVertical: 8}}
                    title={devItem?.Name ?? ''}
                    titleStyle={[
                      TypoSkin.body3,
                      {
                        color: ColorThemes.light.neutral_text_body_color,
                        fontWeight: 'bold',
                      },
                    ]}
                    subTitleStyle={[
                      TypoSkin.subtitle4,
                      {
                        color: ColorThemes.light.neutral_text_body_color,
                        fontWeight: 'bold',
                      },
                    ]}
                    subtitle={`${Ultis.money(devItem?.Price)}VNĐ`}
                    trailing={
                      <FNumberPicker
                        buttonStyle={{padding: 2}}
                        initValue={devItem['_Quantity']}
                        style={{}}
                        onChange={ev => {
                          if (ev)
                            methods.setValue(
                              'devices',
                              methods
                                .getValues('devices')
                                .map((e: any) =>
                                  e.Id === devItem.Id
                                    ? {...e, _Quantity: ev}
                                    : e,
                                ),
                            );
                          else
                            methods.setValue(
                              'devices',
                              methods
                                .getValues('devices')
                                .filter((e: any) => e.Id !== devItem.Id),
                            );
                        }}
                      />
                    }
                  />
                );
              })
            : null}
          {listDevices.length && onOpen ? (
            <AppButton
              prefixIcon={'outline/layout/plus'}
              prefixIconSize={16}
              title={'Thêm'}
              backgroundColor={ColorThemes.light.transparent}
              borderColor={ColorThemes.light.primary_main_color}
              containerStyle={{
                borderStyle: 'dotted',
                alignSelf: 'baseline',
                paddingHorizontal: 8,
              }}
              height={32}
              textColor={ColorThemes.light.primary_main_color}
              onPress={() => showPopupSelectProduct(item)}
            />
          ) : null}
        </View>
      }
    />
  );
};

const PopupSelectProduct = forwardRef(function PopupSelectProduct(
  data: {
    item: any;
    onDone: (values: Array<any>) => void;
    selecteds: [];
    type: string;
  },
  ref: any,
) {
  const {item, onDone, selecteds, type = false} = data;
  const filterMethods = useForm({shouldFocusError: false});
  const productController = new DataController('Product');
  const brandController = new DataController('Brands');
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;

  const [listBrand, setListBrand] = useState({data: [], totalCount: undefined});
  const [products, setProducts] = useState({data: [], totalCount: undefined});
  const [selectedItems, setSelectedItems] = useState<Array<any>>([]);
  const popupRef = useRef<any>();

  const getData = async () => {
    const queryList = [`@CategoryId:{*${item.Id}*}`];
    if (
      !user ||
      (user.Type !== CustomerType.partner &&
        owner?.Type !== CustomerType.partner)
    ) {
      queryList.push(`@IsPublic:{true}`);
      queryList.push(`@IsPublic:{false}`);
    } else if (owner?.Type === CustomerType.partner)
      queryList.push(
        `(@CompanyProfileId:{${owner?.CompanyProfileId}}) | (@CustomerId:{${user.Id} | ${owner?.Id}}) | (@IsPublic:{true} | @IsPublic:{false})`,
      );
    else
      queryList.push(
        `(@CustomerId:{${user.Id}}) | (@IsPublic:{true} | @IsPublic:{false})`,
      );
    if (filterMethods.watch('Name')?.length)
      queryList.push(`@Name:(*${filterMethods.watch('Name')}*)`);
    if (filterMethods.watch('BrandsId')?.length)
      queryList.push(`@BrandsId:{*${filterMethods.watch('BrandsId')}*}`);
    if (filterMethods.watch('AttributeId')?.length)
      queryList.push(
        `@AttributeId:{${filterMethods
          .watch('AttributeId')
          .map((e: any) => `*${e}*`)
          .join(' | ')}}`,
      );
    switch (type) {
      case 'bio':
        queryList.push(`@Type:[${ProductType.bio}]`);
        break;
      case 'device':
        queryList.push(`-@Type:[${ProductType.bio}]`);
        break;
      default:
        break;
    }
    const searchRawQuery = queryList.join(' ');
    const res = await productController.aggregateList({
      page: 1,
      size: 1000,
      searchRaw: searchRawQuery,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const products = res.data?.filter((e: any) => e?.CategoryId === item.Id);
      setProducts({
        data: products ?? [],
        totalCount: products?.length ?? 0,
      });
    }
  };

  const getBrands = async () => {
    const res = await brandController.getListSimple({
      page: 1,
      size: 1000,
      returns: ['Id', 'Name', 'ParentId'],
    });
    if (res.code === 200)
      setListBrand({data: res.data, totalCount: res.totalCount});
  };

  useEffect(() => {
    if (item) {
      getBrands();
      getData();
      if (selecteds.length) setSelectedItems(selecteds);
    }
  }, [item]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={
          <View
            style={{
              flexDirection: 'column',
              paddingVertical: 4,
              alignItems: 'center',
              alignSelf: 'center',
            }}>
            <Text
              numberOfLines={2}
              style={[
                TypoSkin.title3,
              ]}>{`Chọn ${item.Name?.toLowerCase() ?? 'SP'}`}</Text>
            <Text
              numberOfLines={2}
              style={[
                TypoSkin.subtitle3,
              ]}>{`Tổng ${products.totalCount ?? 0} sản phẩm`}</Text>
          </View>
        }
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingHorizontal: 16,
              paddingTop: 4,
              paddingBottom: 16,
              gap: 8,
            }}>
            <FTextField
              returnKeyType="search"
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              }
              onBlur={(ev: any, vl: string) => {
                if (vl) {
                  filterMethods.setValue('Name', vl.trim());
                } else {
                  filterMethods.setValue('Name', '');
                }
                getData();
              }}
              placeholder="Tìm kiếm"
            />
            <AppButton
              onPress={() => {
                showPopup({
                  ref: popupRef,
                  enableDismiss: true,
                  children: (
                    <PopupFilter
                      ref={popupRef}
                      filterMethods={filterMethods}
                      categoryId={item.Id}
                      listBrand={listBrand}
                      selectedAttributes={
                        filterMethods.watch('AttributeId') ?? []
                      }
                      onApply={attributeIds => {
                        filterMethods.setValue('AttributeId', attributeIds);
                        getData();
                      }}
                    />
                  ),
                });
              }}
              backgroundColor={ColorThemes.light.transparent}
              borderColor={
                filterMethods.watch('AttributeId')?.length ||
                filterMethods.watch('BrandsId')?.length
                  ? ColorThemes.light.primary_main_color
                  : ColorThemes.light.neutral_main_border_color
              }
              containerStyle={{
                height: 40,
                borderRadius: 8,
                paddingHorizontal: 8,
              }}
              prefixIconSize={18}
              prefixIcon={'outline/user interface/setup-preferences'}
              textColor={
                filterMethods.watch('AttributeId')?.length ||
                filterMethods.watch('BrandsId')?.length
                  ? ColorThemes.light.primary_main_color
                  : ColorThemes.light.neutral_text_subtitle_color
              }
              title={'Bộ lọc'}
              textStyle={TypoSkin.subtitle3}
            />
          </View>
        }
      />
      <KeyboardAvoidingView style={{flex: 1}}>
        {products.totalCount == 0 ? (
          <KeyboardAvoidingView
            style={{
              flex: 1,
              height: '100%',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <EmptyPage title={'Không có dữ liệu'} />
          </KeyboardAvoidingView>
        ) : (
          <FlatList
            data={products.data}
            style={{height: '100%', marginVertical: 6}}
            keyExtractor={(item, index) => `${item.Id}`}
            ListFooterComponent={() => <View style={{height: 100}} />}
            renderItem={({item, index}: {item: any; index: number}) => {
              const isSelected = selectedItems.find(
                (e: any) => e.Id === item.Id,
              );
              return (
                <ListTile
                  key={`${item.Id}`}
                  onPress={() => {
                    if (isSelected)
                      setSelectedItems(
                        selectedItems.filter(e => e.Id !== item.Id),
                      );
                    else setSelectedItems([...selectedItems, item]);
                  }}
                  leading={
                    <SkeletonImage
                      source={{uri: ConfigAPI.imgUrlId + item.Img}}
                      style={{
                        width: 65,
                        height: 65,
                        borderRadius: 8,
                        objectFit: 'cover',
                        borderColor: isSelected
                          ? ColorThemes.light.primary_main_color
                          : ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                      }}
                    />
                  }
                  title={item.Name ?? ''}
                  titleStyle={[
                    TypoSkin.title3,
                    {
                      color: ColorThemes.light.neutral_text_title_color,
                      paddingBottom: 8,
                    },
                  ]}
                  subtitle={
                    <View
                      style={{
                        flexDirection: 'column',
                        gap: 8,
                        alignItems: 'flex-start',
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          alignItems: 'center',
                        }}>
                        <Winicon
                          src="outline/shopping/label"
                          size={12}
                          color={ColorThemes.light.neutral_text_body_color}
                        />
                        <Text>
                          {item.BrandsId
                            ? listBrand.data
                                .filter((e: any) =>
                                  item.BrandsId.includes(e.Id),
                                )
                                .map((e: any) => e.Name)
                                .join(' ,')
                            : ''}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          alignItems: 'center',
                        }}>
                        <Winicon
                          src="outline/files/file-money"
                          size={12}
                          color={ColorThemes.light.neutral_text_body_color}
                        />
                        <Text
                          style={[
                            TypoSkin.body1,
                            {
                              color: ColorThemes.light.neutral_text_body_color,
                              fontWeight: 'bold',
                            },
                          ]}>
                          {Ultis.money(item.Price)}VNĐ
                        </Text>
                      </View>
                    </View>
                  }
                  listtileStyle={{gap: 16}}
                  style={{
                    borderBottomColor:
                      ColorThemes.light.neutral_main_border_color,
                    borderBottomWidth: 1,
                  }}
                  trailing={
                    <View>
                      {isSelected ? (
                        <Winicon
                          src={'fill/layout/circle-check'}
                          size={20}
                          color={ColorThemes.light.primary_main_color}
                        />
                      ) : null}
                    </View>
                  }
                />
              );
            }}
            onEndReachedThreshold={0.1}
          />
        )}
      </KeyboardAvoidingView>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Làm lại'}
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            setSelectedItems([]);
          }}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
        <AppButton
          title={
            selectedItems.length > 0
              ? `Đã chọn ${selectedItems.length}`
              : 'Xong'
          }
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            onDone(
              selectedItems.map(e => {
                const tmp = selecteds.find((el: any) => el.Id === e.Id);
                if (tmp) return {...e, _Quantity: tmp['_Quantity']};
                else return e;
              }),
            );
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});

const PopupFilter = forwardRef(function PopupFilter(
  data: {
    filterMethods: any;
    categoryId: any;
    onApply: (values: Array<any>) => void;
    selectedAttributes: [];
    listBrand: any;
  },
  ref: any,
) {
  const {categoryId, onApply, selectedAttributes, listBrand, filterMethods} =
    data;
  const [cateAttribute, setCateAttribute] = useState([]);
  const [attribute, setAttribute] = useState([]);
  const [selected, setSelected] = useState<Array<any>>([]);

  useEffect(() => {
    const cateAttributeController = new DataController('CateAttribute');
    cateAttributeController
      .getListSimple({
        page: 1,
        size: 1000,
        query: `@CategoryId:{${categoryId}}`,
        returns: ['Id', 'Name'],
      })
      .then(res => {
        if (res.code === 200) setCateAttribute(res.data);
      });
  }, []);

  useEffect(() => {
    if (selectedAttributes.length) setSelected(selectedAttributes);
  }, [selectedAttributes.length]);

  useEffect(() => {
    if (cateAttribute.length) {
      const attributeController = new DataController('Attribute');
      attributeController
        .getListSimple({
          page: 1,
          size: 10000,
          query: `@CateAttributeId:{${cateAttribute.map((e: any) => e.Id).join(' | ')}}`,
          returns: ['Id', 'Name', 'CateAttributeId'],
        })
        .then(res => {
          if (res.code === 200) setAttribute(res.data);
        });
    }
  }, [cateAttribute.length]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 146,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={`Bộ lọc`}
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <ScrollView style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <Text style={{...TypoSkin.label3, marginBottom: 8}}>Thương hiệu</Text>
        <FSelect1
          placeholder="Thương hiệu"
          style={{
            width: '100%',
            backgroundColor: '#fff',
            borderRadius: 8,
            marginBottom: 16,
          }}
          onChange={ev => {
            filterMethods.setValue(
              'BrandsId',
              ev.id === null ? undefined : ev.id,
            );
          }}
          value={filterMethods.watch('BrandsId')}
          data={[
            {id: null, name: 'Tất cả'},
            ...listBrand.data.map((item: any) => {
              return {id: item.Id, name: item.Name, parentId: item.ParentId};
            }),
          ]}
        />
        {cateAttribute.map((cateItem: any, i: number) => {
          const children = attribute.filter(
            (e: any) => e.CateAttributeId === cateItem.Id,
          );
          if (!children.length) return null;
          return (
            <View style={{gap: 8}}>
              <Text style={TypoSkin.label3}>{cateItem?.Name ?? '-'}</Text>
              {children.map((item: any) => {
                return (
                  <TouchableOpacity
                    key={item.Id}
                    onPress={() => {
                      if (!selected.includes(item.Id))
                        setSelected([...selected, item.Id]);
                      else
                        setSelected(
                          selected.filter((id: any) => id !== item.Id),
                        );
                    }}
                    style={{
                      alignSelf: 'baseline',
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <FCheckbox
                      value={selected.includes(item.Id)}
                      onChange={v => {
                        if (v) setSelected([...selected, item.Id]);
                        else
                          setSelected(
                            selected.filter((id: any) => id !== item.Id),
                          );
                      }}
                    />
                    <Text
                      style={{
                        ...TypoSkin.subtitle3,
                        color: ColorThemes.light.neutral_text_title_color,
                      }}>
                      {item?.Name ?? '-'}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          );
        })}
      </ScrollView>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            filterMethods.setValue('BrandsId', undefined);
            setSelected([]);
          }}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            closePopup(ref);
            onApply(selected);
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
