import React, {useState} from 'react';
import {CartItem} from '../../../../types/cartTypes';
import {StoreGroup, UseStoreGroupsReturn} from '../types';

export const useStoreGroups = (items: CartItem[]): UseStoreGroupsReturn => {
  const [storeGroups, setStoreGroups] = useState<StoreGroup[]>([]);
  const [loadingStoreGroups, setLoadingStoreGroups] = useState(false);

  React.useEffect(() => {
    const processItems = async () => {
      if (items.length === 0) {
        setStoreGroups([]);
        return;
      }

      setLoadingStoreGroups(true);
      const storeMap = new Map<string, StoreGroup>();

      try {
        // Process all items in parallel
        await Promise.all(
          items.map(async item => {
            if (!storeMap.has(item.ShopId)) {
              storeMap.set(item.ShopId, {
                ShopId: item.ShopId,
                ShopName: item.ShopName,
                ShopAvatar: item.ShopAvatar,
                items: [],
                totalPrice: 0,
              });
            }

            const group = storeMap.get(item.ShopId)!;

            group.items.push({...item});
            group.totalPrice +=
              item.Price * item.Quantity * (1 - (item.Discount ?? 0) / 100);
          }),
        );

        setStoreGroups(Array.from(storeMap.values()));
      } catch (error) {
        console.error('Error processing store groups:', error);
        setStoreGroups([]);
      } finally {
        setLoadingStoreGroups(false);
      }
    };

    processItems();
  }, [items]);

  // Calculate total price of all products
  const totalPrice = React.useMemo(() => {
    return storeGroups.reduce((sum, group) => sum + group.totalPrice, 0);
  }, [storeGroups]);

  return {
    storeGroups,
    loadingStoreGroups,
    totalPrice,
  };
};
