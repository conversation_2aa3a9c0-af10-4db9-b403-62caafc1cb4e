import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Pressable,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {useForm} from 'react-hook-form';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {followingGroupsActions} from '../../reducers/followingGroupsReducer';
import {GroupActions} from '../../reducers/groupReducer';
import {GroupCardShimmer} from './groups';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {
  hideBottomSheet,
  FBottomSheet,
  FDialog,
  Winicon,
  showBottomSheet,
  FTextField,
  FCheckbox,
} from '../../../../../component/export-component';
import ListTile from '../../../../../component/list-tile/list-tile';
import ConfigAPI from '../../../../../config/configApi';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {AppDispatch, RootState} from '../../../../../redux/store/store';
import {navigate, RootScreen} from '../../../../../router/router';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';

export const SearchGroupIndex = forwardRef(function SearchIndex(
  data: any,
  ref: any,
) {
  const [searchValue, setSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const filterMethods = useForm({shouldFocusError: false});

  const [isRefresh, setRefresh] = useState(false);

  const navigation = useNavigation<any>();

  const onRefresh = async () => {
    setRefresh(true);
    setSearchValue('');
    loadGroups();
    setRefresh(false);
  };
  const dispatch: AppDispatch = useDispatch();
  const {groups, isLoading, error, hasMore, page} = useSelector(
    (state: RootState) => state.group,
  );
  const followingGroups = useSelector(
    (state: RootState) => state.followingGroups.groups,
  );
  const creatorGroups = useSelector(
    (state: RootState) => state.myGroups.groups,
  );

  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    loadGroups();
  }, [activeTab]);

  const loadGroups = (refresh = true) => {
    if (refresh) {
      dispatch(GroupActions.getAllGroups(1, 10));
    } else if (hasMore) {
      dispatch(GroupActions.getAllGroups(page + 1, 10));
    }
  };

  const handleJoinGroup = async (group: any) => {
    await dispatch(followingGroupsActions.followGroup(group));
  };

  const isFollowing = (groupId: string) => {
    return (
      followingGroups.some(group => group.Id === groupId) ||
      creatorGroups.some(group => group.Id === groupId)
    );
  };

  const renderItem = ({item}: any) => {
    const following = isFollowing(item.Id);

    return (
      <ListTile
        onPress={() => {
          hideBottomSheet(ref);
          navigate(RootScreen.GroupIndex, {Id: item.Id});
        }}
        style={{
          borderColor: ColorThemes.light.neutral_main_border_color,
          borderWidth: 1,
          marginBottom: 16,
        }}
        title={item.Name}
        titleStyle={{
          ...TypoSkin.heading7,
          color: ColorThemes.light.neutral_text_title_color,
        }}
        subtitle={
          <Text
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}>
            {item.MemberCount} members
          </Text>
        }
        leading={
          <SkeletonImage
            source={{
              uri: `${
                item.Thumb
                  ? ConfigAPI.imgUrlId + item.Thumb
                  : 'https://redis.ktxgroup.com.vn/api/file/img/ce5cc92f4b67415bb2622cf40d0693e8'
              }`,
            }}
            height={56}
            width={56}
            style={{
              height: 56,
              width: 56,
              borderRadius: 100,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
            }}
          />
        }
        bottom={
          <View style={{width: '100%', flex: 1, paddingTop: 8, gap: 8}}>
            {item.Description && (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_body_color,
                }}>
                {item.Description}
              </Text>
            )}
            {following ? null : (
              <AppButton
                title={following ? 'Đã tham gia' : 'Tham gia'}
                onPress={() => (following ? null : handleJoinGroup(item))}
                textStyle={{
                  ...TypoSkin.buttonText5,
                  color: following
                    ? ColorThemes.light.neutral_text_subtitle_color
                    : ColorThemes.light.infor_main_color,
                }}
                backgroundColor={ColorThemes.light.transparent}
                borderColor={
                  following
                    ? ColorThemes.light.neutral_text_subtitle_color
                    : ColorThemes.light.infor_main_color
                }
                containerStyle={{
                  borderRadius: 8,
                  paddingHorizontal: 8,
                  alignItems: 'center',
                  alignSelf: 'baseline',
                  height: 24,
                }}
              />
            )}
          </View>
        }
      />
    );
  };

  const renderEmptyComponent = () => {
    if (isLoading) {
      return (
        <View style={{gap: 16}}>
          <GroupCardShimmer />
          <GroupCardShimmer />
          <GroupCardShimmer />
        </View>
      );
    }
  };

  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          paddingVertical: 4,
        }}
        title={'Tìm kiếm'}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => hideBottomSheet(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingHorizontal: 16,
              height: 56,
              gap: 8,
              paddingTop: 8,
              paddingBottom: 16,
            }}>
            <FTextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={async (vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder="Tìm kiếm"
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              }
            />
            <AppButton
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  children: (
                    <PopupFilter
                      ref={bottomSheetRef}
                      filterMethods={filterMethods}
                      selectedAttributes={
                        filterMethods.watch('AttributeId') ?? []
                      }
                      onApply={value => {
                        filterMethods.setValue(
                          'AttributeId',
                          value.AttributeId,
                        );
                      }}
                    />
                  ),
                });
              }}
              backgroundColor={ColorThemes.light.transparent}
              borderColor={
                filterMethods.watch('AttributeId')?.length
                  ? ColorThemes.light.primary_main_color
                  : ColorThemes.light.neutral_main_border_color
              }
              containerStyle={{
                height: 40,
                borderRadius: 8,
                paddingHorizontal: 8,
              }}
              prefixIconSize={18}
              prefixIcon={'outline/user interface/setup-preferences'}
              textColor={
                filterMethods.watch('AttributeId')?.length
                  ? ColorThemes.light.primary_main_color
                  : ColorThemes.light.neutral_text_subtitle_color
              }
              textStyle={TypoSkin.subtitle3}
            />
          </View>
        }
      />
      <FlatList
        data={
          searchValue
            ? groups.filter((item: any) =>
                item.Name.toLowerCase().includes(searchValue.toLowerCase()),
              )
            : groups
        }
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        renderItem={renderItem}
        keyExtractor={item => item.Id}
        contentContainerStyle={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        onRefresh={loadGroups}
        refreshing={isLoading}
        onEndReached={() => loadGroups(false)}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={() => {
          return <View style={{height: 24}} />;
        }}
      />
    </Pressable>
  );
});

const PopupFilter = forwardRef(function PopupFilter(
  data: {
    filterMethods: any;
    onApply: (values: any) => void;
    selectedAttributes: [];
  },
  ref: any,
) {
  const {onApply, selectedAttributes} = data;
  const [selected, setSelected] = useState<Array<any>>([]);

  useEffect(() => {
    if (selectedAttributes.length) {
      setSelected(selectedAttributes);
    }
  }, [selectedAttributes.length, selectedAttributes]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 146,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={'Bộ lọc'}
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => hideBottomSheet(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <View style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <Text style={{...TypoSkin.label3, marginBottom: 8}}>Loại dịch vụ</Text>
        <FlatList
          data={[
            {Id: 1, Name: 'a'},
            {Id: 2, Name: 'b'},
          ]}
          scrollEnabled={false}
          renderItem={({item, index}) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                if (!selected.includes(item.Id)) {
                  setSelected([...selected, item.Id]);
                } else {
                  setSelected(selected.filter((id: any) => id !== item.Id));
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
                width: '50%',
              }}>
              <FCheckbox
                value={selected.includes(item.Id)}
                onChange={v => {
                  if (v) {
                    setSelected([...selected, item.Id]);
                  } else {
                    setSelected(selected.filter((id: any) => id !== item.Id));
                  }
                }}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                {item?.Name ?? '-'}
              </Text>
            </TouchableOpacity>
          )}
          //Setting the number of column
          numColumns={2}
          style={{width: '100%'}}
          contentContainerStyle={{gap: 16}}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            setSelected([]);
          }}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            hideBottomSheet(ref);
            onApply({AttributeId: selected});
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
