import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import FastImage from 'react-native-fast-image';
import {TypoSkin} from 'assets/skin/typography';
import ConfigAPI from 'config/configApi';
import {StoreGroupProps} from '../types';
import ProductItem from './ProductItem';
import PaymentMethodSelector from './PaymentMethodSelector';
import OrderSummary from './OrderSummary';

interface StoreGroupInternalProps extends StoreGroupProps {
  btsRef: React.RefObject<any>;
}

const StoreGroup: React.FC<StoreGroupInternalProps> = ({
  storeGroup,
  index,
  payment,
  isDone,
  onPaymentChange,
  btsRef,
}) => {
  return (
    <View style={styles.storeContainer} key={storeGroup.ShopId + index}>
      {/* Store Header - Hidden when order is done */}
      {!isDone && (
        <View style={styles.storeHeader}>
          <FastImage
            style={styles.storeAvatar}
            source={{
              uri: storeGroup.ShopAvatar?.startsWith('http')
                ? storeGroup.ShopAvatar
                : `${ConfigAPI.imgUrlId}${storeGroup.ShopAvatar}`,
              priority: FastImage.priority.normal,
            }}
            resizeMode={FastImage.resizeMode.cover}
          />
          <Text style={styles.storeName}>{storeGroup?.ShopName ?? ''}</Text>
        </View>
      )}

      {/* Product Items */}
      {storeGroup.items.map(item => (
        <ProductItem key={item.id} item={item} />
      ))}

      {/* Shipping, Payment, and Summary Container */}
      <View style={styles.shippingContainer}>
        {/* Payment Method Selector */}
        <PaymentMethodSelector
          payment={payment}
          onPaymentChange={onPaymentChange}
          isDone={isDone}
          btsRef={btsRef}
        />

        {/* Order Summary */}
        <OrderSummary storeGroup={storeGroup} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  storeContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  storeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storeName: {
    ...TypoSkin.heading7,
    color: '#000000',
  },
  shippingContainer: {
    marginTop: 8,
  },
});

export default StoreGroup;
