import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {PopupUpdateStatusOrderStyles} from './PopupUpdateStatusOrderStyles';

const PopupUpdateStatusOrder = ({
  visible,
  onClose,
  item,
  handleUpdateStatusProcessOrder,
}: {
  visible: boolean;
  onClose: () => void;
  item: any;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => Promise<void>;
}) => {
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [cancelReason, setCancelReason] = useState('');

  // Reset state when modal is closed
  useEffect(() => {
    if (!visible) {
      setSelectedStatus(null);
      setCancelReason('');
    }
  }, [visible]);

  useEffect(() => {
    console.log('check-item1234');
  }, []);

  const statusOptions = [
    {id: 1, name: '<PERSON><PERSON><PERSON> nhận đơn hàng', type: 'processing'},
    {id: 3, name: 'Hoàn thành', type: 'completed'},
    {id: 4, name: 'Hủy', type: 'cancelled'},
  ];

  const getFilteredStatusOptions = () => {
    if (item?.Status === 1) {
      return statusOptions.filter(
        status => status.type === 'processing' || status.type === 'cancelled',
      );
    } else if (item?.Status === 2) {
      return statusOptions.filter(
        status =>
          // status.type === 'Pending' ||
          // status.type === 'Delivery' ||
          status.type === 'completed' || status.type === 'cancelled',
      );
    }
    return statusOptions;
  };

  const handleStatusSelect = (statusType: string) => {
    setSelectedStatus(statusType);
    if (statusType !== 'cancelled') {
      setCancelReason('');
    }
  };
  const handleUpdateStatus = async () => {
    if (selectedStatus) {
      if (selectedStatus === 'cancelled' && !cancelReason.trim()) {
        return;
      }
      const itemWithCancelReason =
        selectedStatus === 'cancelled'
          ? {...item, cancelReason: cancelReason.trim()}
          : item;

      await handleUpdateStatusProcessOrder(
        itemWithCancelReason,
        selectedStatus,
      );
      onClose(); // Close modal on success
    }
  };
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={PopupUpdateStatusOrderStyles.modalOverlay}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
          style={{flex: 1, justifyContent: 'center'}}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1, justifyContent: 'center'}}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <View style={PopupUpdateStatusOrderStyles.modalContent}>
              {/* Header */}
              <View style={PopupUpdateStatusOrderStyles.modalHeader}>
                <Text style={PopupUpdateStatusOrderStyles.modalTitle}>
                  Cập nhật đơn hàng #
                  <Text style={{fontWeight: 'bold'}}>{item?.Code}</Text>
                </Text>
                <TouchableOpacity
                  style={PopupUpdateStatusOrderStyles.closeButton}
                  onPress={onClose}>
                  <Text style={PopupUpdateStatusOrderStyles.closeButtonText}>
                    ×
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Order Info */}
              <View
                style={PopupUpdateStatusOrderStyles.orderInfoContainer}></View>
              <View style={PopupUpdateStatusOrderStyles.statusList}>
                {getFilteredStatusOptions().map(status => (
                  <TouchableOpacity
                    key={status.id}
                    style={[
                      PopupUpdateStatusOrderStyles.statusOption,
                      selectedStatus === status.type &&
                        PopupUpdateStatusOrderStyles.selectedStatus,
                    ]}
                    onPress={() => handleStatusSelect(status.type)}>
                    <View
                      style={PopupUpdateStatusOrderStyles.statusOptionContent}>
                      <View
                        style={[
                          PopupUpdateStatusOrderStyles.statusIndicator,
                          selectedStatus === status.type &&
                            PopupUpdateStatusOrderStyles.selectedIndicator,
                        ]}
                      />
                      <Text
                        style={[
                          PopupUpdateStatusOrderStyles.statusOptionText,
                          selectedStatus === status.type &&
                            PopupUpdateStatusOrderStyles.selectedStatusText,
                        ]}>
                        {status.name}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}

                {/* Cancel Reason Input */}
                {selectedStatus === 'cancelled' && (
                  <View
                    style={PopupUpdateStatusOrderStyles.cancelReasonContainer}>
                    <Text
                      style={PopupUpdateStatusOrderStyles.cancelReasonLabel}>
                      Lý do hủy đơn hàng *
                    </Text>
                    <TextInput
                      style={PopupUpdateStatusOrderStyles.cancelReasonInput}
                      placeholder="Nhập lý do hủy đơn hàng..."
                      value={cancelReason}
                      onChangeText={setCancelReason}
                      multiline={true}
                      numberOfLines={3}
                      textAlignVertical="top"
                    />
                  </View>
                )}
              </View>

              {/* Action Buttons */}
              <View style={PopupUpdateStatusOrderStyles.actionButtonsContainer}>
                <View style={PopupUpdateStatusOrderStyles.additionalButtons}>
                  <TouchableOpacity
                    style={[
                      PopupUpdateStatusOrderStyles.additionalButton,
                      (!selectedStatus ||
                        (selectedStatus === 'cancelled' &&
                          !cancelReason.trim())) &&
                        PopupUpdateStatusOrderStyles.disabledButton,
                    ]}
                    onPress={handleUpdateStatus}
                    disabled={
                      !selectedStatus ||
                      (selectedStatus === 'cancelled' && !cancelReason.trim())
                    }>
                    <Text
                      style={[
                        PopupUpdateStatusOrderStyles.additionalButtonText,
                        (!selectedStatus ||
                          (selectedStatus === 'cancelled' &&
                            !cancelReason.trim())) &&
                          PopupUpdateStatusOrderStyles.disabledButtonText,
                      ]}>
                      Cập nhật trạng thái
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
};

export default PopupUpdateStatusOrder;
