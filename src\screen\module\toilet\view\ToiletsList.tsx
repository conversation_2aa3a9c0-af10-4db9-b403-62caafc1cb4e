import {
  Di<PERSON><PERSON>,
  FlatList,
  RefreshControl,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import ScreenHeader from '../../../layout/header';
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {useForm} from 'react-hook-form';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../redux/hooks/hooks';
import {
  FCheckbox,
  FTextField,
  showSnackbar,
  Winicon,
} from '../../../../component/export-component';
import {ComponentStatus} from '../../../../component/component-status';
import {DataController} from '../../../base-controller';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {CardToiletHoriSkeleton} from '../../../../project-component/skeletonCard';
import EmptyPage from '../../../../project-component/empty-page';
import ToiletCard, {
  StatusToiletData,
} from '../../workplace/components/card/ToiletCard';
import AppButton from '../../../../component/button';
import {closePopup, FPopup, showPopup} from '../../../../component/popup/popup';
import WScreenFooter from '../../../layout/footer';
import {AddEditToiletPopup} from '../components/form/AddEditToilet';
import ConfigAPI from '../../../../config/configApi';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {DropdownSelect} from 'wini-mobile-components';

export default function ToiletList() {
  const [managerData, setManagerData] = useState<any>({
    data: Array<any>(),
    totalCount: 0,
  });
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const popupRef = useRef<any>();
  const [isLoading, setLoading] = useState(false);
  const [isRefreshing, setRefreshing] = useState(false);

  const getData = async () => {
    setLoading(true);
    const controller = new DataController('Toilet');
    let _query: string[] = [];
    if (company?.Id !== ConfigAPI.ktxCompanyId) {
      _query = [`@CustomerId:{${user?.Id}${owner ? ` | ${owner.Id}` : ''}}`];
      if (filterMethods.watch('AttributeId')?.length)
        filterMethods
          .watch('AttributeId')
          .forEach((e: any, i: number) =>
            _query.push(
              `@Status:[${e} ${e}] ${i == filterMethods.watch('AttributeId').length - 1 ? '' : '|'}`,
            ),
          );
      if (filterMethods.watch('isFavor') === true) _query.push(`@Favor:{true}`);
    }
    let bodyJson = {
      page: 1,
      size: 100,
      searchRaw: _query.length ? _query.join(' ') : '*',
    };
    const res = await controller.aggregateList(bodyJson);
    //
    if (res.code === 200) {
      const cusController = new DataController('Customer');
      var customerIds = res.data
        .map((e: any) => e.CustomerId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      var resCus = await cusController.getByListId(customerIds);
      if (resCus.code === 200) {
        res.data = res.data.map((e: any) => {
          const cus = resCus.data.find((cus: any) => cus.Id === e.CustomerId);
          if (cus) {
            return {...e, CustomerName: cus?.Name};
          }
          return e;
        });
        console.log('check-res.data', res.data);
        setManagerData({data: res.data, totalCount: res.totalCount});
        setLoading(false);
        setRefreshing(false);
      }
    } else {
      showSnackbar({message: res.message, status: ComponentStatus.ERROR});
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (user) {
      getData();
    }
  }, [user]);

  const [searchValue, setSearchValue] = useState('');
  const [selectData, setSelectData] = useState<any>();

  const filterMethods = useForm({shouldFocusError: false});

  const ListData = [
    {id: 1, name: ' allal'},
    {id: 2, name: 'aaaaaaaaa'},
  ];

  return (
    <View
      style={{
        flex: 1,
        height: '100%',
        width: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FPopup ref={popupRef} />
      <TitleHeader title="Quản lý nhà vệ sinh" noBack />
      <View>
        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            paddingHorizontal: 12,
            gap: 8,
            paddingTop: 15,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <FTextField
            style={{
              paddingHorizontal: 16,
              flex: 1,
              height: 40,
            }}
            onChange={vl => {
              setSearchValue(vl.trim());
            }}
            prefix={<Winicon src="outline/development/zoom" size={14} />}
            value={searchValue}
            placeholder="Tìm kiếm"
          />
          {/* <DropdownSelect
            style={{width: 100, fontSize: 12}}
            value={selectData}
            data={ListData}
            placeholder="Sắp xếp"
            onChange={() => {}}
          /> */}
          <AppButton
            onPress={() => {
              showPopup({
                ref: popupRef,
                enableDismiss: true,
                children: (
                  <PopupFilter
                    ref={popupRef}
                    filterMethods={filterMethods}
                    listStatus={StatusToiletData}
                    selectedAttributes={
                      filterMethods.watch('AttributeId') ?? []
                    }
                    onApply={value => {
                      filterMethods.setValue('AttributeId', value?.AttributeId);
                      filterMethods.setValue('isFavor', value?.isFavor);
                      setManagerData({data: [], totalCount: undefined});
                      getData();
                    }}
                  />
                ),
              });
            }}
            backgroundColor={ColorThemes.light.transparent}
            borderColor={
              filterMethods.watch('AttributeId')?.length ||
              filterMethods.watch('isFavor') === true
                ? ColorThemes.light.primary_main_color
                : ColorThemes.light.neutral_main_border_color
            }
            containerStyle={{
              height: 40,
              borderRadius: 8,
              paddingHorizontal: 8,
              width: 80,
            }}
            prefixIconSize={18}
            prefixIcon={'outline/user interface/setup-preferences'}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
            title={'Bộ lọc'}
            textStyle={{
              fontSize: 12,
              fontFamily: 'Inter',
              color: ColorThemes.light.neutral_text_placeholder_reverse_color,
            }}
          />
        </View>
      </View>
      <FlatList
        data={
          searchValue.length
            ? managerData.data.filter((e: any) =>
                e.Name.toLowerCase().includes(searchValue.toLowerCase()),
              )
            : managerData.data
        }
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={() => {
              setRefreshing(true);
              setManagerData({data: [], totalCount: undefined});
              getData();
            }}
          />
        }
        style={{flex: 1, gap: 8, marginHorizontal: 12, marginVertical: 16}}
        ItemSeparatorComponent={() => <View style={{height: 16}} />}
        renderItem={({item, index}: any) => {
          return (
            <ToiletCard
              key={index}
              currentItem={item}
              user={user}
              workData={managerData}
              setWorkData={setManagerData}
              getData={() => {
                setManagerData({data: [], totalCount: undefined});
                getData();
                setTimeout(() => closePopup(popupRef), 100);
              }}
              onDelete={() => {
                const controller = new DataController('Toilet');
                controller.delete([item.Id]).then(res => {
                  if (res.code === 200) {
                    showSnackbar({
                      message:
                        'Xóa ' + item?.Name.toLowerCase() + ' thành công!',
                      status: ComponentStatus.SUCCSESS,
                    });
                    setManagerData({
                      data: managerData.data.filter(
                        (e: any) => e.Id !== item.Id,
                      ),
                      totalCount:
                        managerData.data.filter((e: any) => e.Id !== item.Id)
                          ?.length ?? undefined,
                    });
                  } else {
                    showSnackbar({
                      message: res.message,
                      status: ComponentStatus.ERROR,
                    });
                  }
                });
              }}
            />
          );
        }}
        ListFooterComponent={() => <View style={{height: 111}} />}
        ListEmptyComponent={() =>
          isLoading ? (
            Array.from(Array(10)).map((_, index) => (
              <View key={index} style={{gap: 16}}>
                <CardToiletHoriSkeleton />
              </View>
            ))
          ) : (
            <EmptyPage title="Bạn chưa có nhà vệ sinh nào" />
          )
        }
      />
      <WScreenFooter style={{paddingHorizontal: 16}}>
        <AppButton
          title={'Thêm nhà vệ sinh'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            showPopup({
              ref: popupRef,
              enableDismiss: true,
              children: (
                <AddEditToiletPopup
                  ref={popupRef}
                  toiletId={undefined}
                  onDone={() => {
                    setManagerData({data: [], totalCount: undefined});
                    getData();
                    setTimeout(() => closePopup(popupRef), 100);
                  }}
                />
              ),
            });
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </View>
  );
}

const PopupFilter = forwardRef(function PopupFilter(
  data: {
    filterMethods: any;
    onApply: (values: any) => void;
    selectedAttributes: [];
    listStatus: any;
  },
  ref: any,
) {
  const {onApply, selectedAttributes, listStatus, filterMethods} = data;
  const [selected, setSelected] = useState<Array<any>>([]);
  const [isFavor, setFavor] = useState(filterMethods.watch('isFavor') ?? false);

  useEffect(() => {
    if (selectedAttributes.length) setSelected(selectedAttributes);
  }, [selectedAttributes.length]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 146,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={`Bộ lọc`}
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <View style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <View style={{paddingBottom: 8}}>
          <Text style={{...TypoSkin.label3, marginBottom: 8}}>Yêu thích: </Text>
          <TouchableOpacity
            onPress={() => {
              setFavor(!isFavor);
            }}
            style={{
              alignSelf: 'baseline',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
            }}>
            <FCheckbox
              value={isFavor}
              onChange={() => {
                setFavor(!isFavor);
              }}
            />
            <Text
              style={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              Yêu thích
            </Text>
          </TouchableOpacity>
        </View>
        <Text style={{...TypoSkin.label3, marginBottom: 8}}>Trạng thái:</Text>
        <FlatList
          data={listStatus}
          renderItem={({item, index}) => (
            <TouchableOpacity
              key={item.key}
              onPress={() => {
                if (!selected.includes(item.key))
                  setSelected([...selected, item.key]);
                else setSelected(selected.filter((id: any) => id !== item.key));
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
                width: '50%',
              }}>
              <FCheckbox
                value={selected.includes(item.key)}
                onChange={v => {
                  if (v) setSelected([...selected, item.key]);
                  else
                    setSelected(selected.filter((id: any) => id !== item.key));
                }}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                {item?.title ?? '-'}
              </Text>
            </TouchableOpacity>
          )}
          //Setting the number of column
          numColumns={2}
          style={{width: '100%'}}
          contentContainerStyle={{gap: 16}}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          marginBottom: 32,
        }}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            // filterMethods.setValue("AttributeId", [])
            setSelected([]);
            setFavor(false);
            // filterMethods.setValue("isFavor", undefined)
          }}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            closePopup(ref);
            onApply({AttributeId: selected, isFavor: isFavor});
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
