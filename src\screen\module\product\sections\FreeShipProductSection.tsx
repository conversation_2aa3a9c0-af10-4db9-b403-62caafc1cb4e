import {ActivityIndicator, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {productAction} from '../../../../redux/actions/productAction';
import {ProductItem} from '../../../../types/ProductType';
import {ColorThemes} from '../../../../assets/skin/colors';
import HotProductsRow from '../view/HotProductsRow';
import {navigate, RootScreen} from '../../../../router/router';

const FreeShipProductSection = ({onRefresh}: {onRefresh: boolean}) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<ProductItem[]>([]);

  useEffect(() => {
    initData();
  }, [onRefresh]);

  const initData = async () => {
    try {
      setLoading(true);
      let data = await productAction.find({
        page: 1,
        size: 10,
      });
      const products = data.map((item: any) => ({
        Id: item.Id,
        Name: item.Name,
        Price: item.Price,
        Img: item.Img,
        rating: item.Rating,
        soldCount: item.SoldCount,
        Description: item.Description,
      }));
      setProducts(products);
    } finally {
      setLoading(false);
    }
  };

  const onSeeAll = () => {
    navigation.navigate(RootScreen.ProductListByCategory as never);
  };

  const onProductPress = (product: ProductItem) => {
    navigate(RootScreen.DetailProductPage, {id: product.Id});
  };

  if (loading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  return (
    <HotProductsRow
      title="Free Ship"
      products={products}
      onSeeAll={onSeeAll}
      showRating={false}
      onProductPress={onProductPress}
    />
  );
};

export default FreeShipProductSection;
