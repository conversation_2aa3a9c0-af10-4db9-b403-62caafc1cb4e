import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {StatusOrder} from '../../../../../../../config/Contanst';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import iconSvg from '../../../../../../../svgs/iconSvg';
import {ColorThemes} from 'assets/skin/colors';

interface ActionButtonsProps {
  currentStatus: number;
  onUpdateStatusPress: () => void;
  onConfirmOrderPress: () => void;
  onRejectOrderPress: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  currentStatus,
  onUpdateStatusPress,
  onConfirmOrderPress,
  onRejectOrderPress,
}) => {
  return (
    <View style={styles.orderInfoContainer}>
      {currentStatus !== StatusOrder.new &&
        currentStatus !== StatusOrder.cancel &&
        currentStatus !== StatusOrder.success && (
          <TouchableOpacity
            style={styles.orderInfoRow}
            onPress={onUpdateStatusPress}>
            <View style={styles.orderInfoIcon}>
              <AppSvg
                SvgSrc={iconSvg.updateStatus}
                size={20}
                color={ColorThemes.light.primary_main_color}
              />
            </View>
            <View style={styles.orderInfoContent}>
              <Text style={styles.orderInfoLabel}>
                Cập nhật trạng thái đơn hàng
              </Text>
            </View>
          </TouchableOpacity>
        )}

      {/* <TouchableOpacity style={styles.orderInfoRow}>
        <View style={styles.orderInfoIcon}>
          <AppSvg SvgSrc={iconSvg.chat} size={20} />
        </View>
        <View style={styles.orderInfoContent}>
          <Text style={styles.orderInfoLabel}>Chat với người mua</Text>
        </View>
      </TouchableOpacity> */}

      {currentStatus == StatusOrder.new && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingTop: 16,
            marginHorizontal: 16,
          }}>
          <TouchableOpacity
            onPress={onRejectOrderPress}
            style={styles.rejectButton}>
            <Text style={styles.rejectButtonText}>Từ chối đơn hàng</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onConfirmOrderPress}
            style={styles.confirmButton}>
            <Text style={styles.confirmButtonText}>Xác nhận đơn hàng</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  orderInfoContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    marginBottom: 8,
  },
  orderInfoRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  orderInfoIcon: {
    width: 24,
    marginRight: 12,
  },
  orderInfoContent: {
    flex: 1,
  },
  orderInfoLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.primary_main_color,
    fontWeight: 'bold',
  },
  confirmButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  confirmButtonText: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.white,
  },
  rejectButton: {
    backgroundColor: 'red',
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  rejectButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
});

export default ActionButtons;
