import {View} from 'react-native';
import TitleHeader from 'screen/layout/headers/TitleHeader';
import {ColorThemes} from 'assets/skin/colors';
import {useState} from 'react';
import FormChooseCriteria from './components/FormChooseCriteria';
import RegisterToiletStep from './components/RegisterToiletStep/RegisterToiletStep';
import {NetZeroDa} from 'screen/module/service/Da/netzeroDa';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'wini-mobile-components';
import FLoading from 'component/Loading/FLoading';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from 'router/router';
import {handleStartPhoneNumber} from 'utils/Utils';
import {
  randomToiletServiceName,
  ToiletServiceStatus,
} from 'screen/module/service/components/da';
import {CateCriterionItem} from 'types/cateCriteriaType';

export default function RegisterCateCriterionPage({
  serviceId,
}: {
  serviceId?: string;
}) {
  if (!serviceId) return null;
  const navigation = useNavigation<any>();
  const netZeroDa = new NetZeroDa();
  const [dataCreate, setDataCreate] = useState<any>(null);
  const [step, setStep] = useState<number>(1);
  const [loading, setLoading] = useState(false);

  const handleStep1Next = (cateCriterion: CateCriterionItem) => {
    setDataCreate({cateCriterion});
    setStep(2);
  };

  const handleComplete = async (data: any) => {
    try {
      setLoading(true);
      let createdData = {...dataCreate, ...data};
      createdData = {
        Name: await randomToiletServiceName('NZR'),
        ToiletId: data.Toilets.join(','),
        CustomerId: data.CustomerId,
        CateServicesId: serviceId,
        CateCriterionId: createdData.cateCriterion.Id,
        CompanyProfileId: data.CompanyProfileId,
        Status: ToiletServiceStatus.register,
        CustomerMobile: handleStartPhoneNumber(data.Phone),
      };
      // call api
      const res = await netZeroDa.create(createdData);
      if (res === true) {
        showSnackbar({
          message: 'Đăng ký thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigation.reset({
          index: 0,
          routes: [{name: RootScreen.navigateView}],
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra. Vui lòng thử lại',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: ColorThemes.light.white}}>
      <TitleHeader title="Đăng ký sạch - xanh - tuần hoàn" />

      <FLoading visible={loading} />

      {step == 1 && <FormChooseCriteria onNextStep={handleStep1Next} />}
      {step == 2 && (
        <RegisterToiletStep
          onCompleted={handleComplete}
          levelCateCriterion={dataCreate.cateCriterion.Sort}
          CateCriterionId={dataCreate.cateCriterion.Id}
          serviceId={serviceId}
        />
      )}
    </View>
  );
}
