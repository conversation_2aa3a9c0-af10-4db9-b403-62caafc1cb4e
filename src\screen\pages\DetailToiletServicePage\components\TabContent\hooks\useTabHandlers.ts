import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
  DeviceBioStatus,
} from '../../../../../module/service/components/da';
import {DataController} from '../../../../../base-controller';
import {randomGID} from '../../../../../../utils/Utils';
import {showSnackbar} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import {caculateTotalServicesValue} from '../../../../../module/workplace/components/form/PrintForm';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface UseTabHandlersProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  methods: any;
  onChangeStatus: (status: any, setServicesValue?: any) => void;
}

export const useTabHandlers = ({
  workData,
  serviceData,
  setServiceData,
  methods,
  onChangeStatus,
}: UseTabHandlersProps) => {
  if (!workData) {
    return {
      createTasksFromContract: async () => {},
      completeContractTasks: async () => {},
      createBuildTask: async () => {},
      createDesignTask: async () => {},
      updateServicesValue: async () => {},
    };
  }
  const createTasksFromContract = async (
    _tasks: any[],
    taskController: any,
  ) => {
    const now = new Date();
    const res = await taskController.edit(
      _tasks.map((t: any, i: any) => {
        const startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() +
            _tasks
              .slice(0, i)
              .map((e: any) => e?.Day)
              .reduce((a: any, b: any) => a + b, 0),
        );
        const endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() +
            _tasks
              .slice(0, i)
              .map((e: any) => e.Day)
              .reduce((a: any, b: any) => a + b, 0) +
            t.Day,
          23,
          59,
        );
        return {
          ...t,
          Status: TaskStatus.open,
          DateStart: startDate.getTime(),
          DateEnd: endDate.getTime(),
          CustomerId: serviceData?.CustomerId,
          ToiletServicesId: serviceData?.Id,
          ToiletId: workData[0]?.Id,
          Type: TaskType.other,
        };
      }),
    );
    if (res.code !== 200)
      showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
  };

  const completeContractTasks = async (taskController: any) => {
    if (!serviceData) return;

    const res = await taskController.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${TaskType.contract} ${TaskType.contract}] @Status:[${TaskStatus.open} ${TaskStatus.overdue}]`,
    });

    if (res.code === 200 && res.data.length) {
      taskController.edit(
        res.data.map((e: any) => ({
          ...e,
          Status: TaskStatus.done,
        })),
      );
    }
  };

  const createBuildTask = async (taskController: any) => {
    if (!serviceData || !workData) return;

    const now = new Date();
    const newTaskBuild = {
      Id: randomGID(),
      Name: 'Quản lý tiến độ thực hiện hợp đồng',
      DateCreated: now.getTime(),
      Type: TaskType.build,
      Status: TaskStatus.open,
      DateStart: now.getTime(),
      Day: 7,
      DateEnd: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 7,
        23,
        59,
      ).getTime(),
      CustomerId: serviceData.CustomerId,
      ToiletServicesId: serviceData.Id,
      ToiletId: workData[0].Id,
    };
    taskController.add([newTaskBuild]);
  };

  const createDesignTask = async (taskController: any) => {
    if (!serviceData || !workData) return;

    const now = new Date();
    const newTaskDesign = {
      Id: randomGID(),
      Name: 'Thiết kế',
      DateCreated: now.getTime(),
      Type: TaskType.design,
      Status: TaskStatus.open,
      DateStart: now.getTime(),
      Day: 7,
      DateEnd: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 7,
        23,
        59,
      ).getTime(),
      CustomerId: serviceData.CustomerId,
      ToiletServicesId: serviceData.Id,
      ToiletId: workData[0].Id,
    };
    taskController.add([newTaskDesign]);
  };

  const updateServicesValue = async () => {
    if (!serviceData) return;

    const servicesController = new DataController('ToiletServices');
    let updateServices = {...serviceData};
    updateServices.Value =
      (updateServices.Value ?? 0) +
      caculateTotalServicesValue({methods: methods});
    await servicesController.edit([updateServices]);
    setServiceData(updateServices);
  };

  return {
    createTasksFromContract,
    completeContractTasks,
    createBuildTask,
    createDesignTask,
    updateServicesValue,
  };
};
