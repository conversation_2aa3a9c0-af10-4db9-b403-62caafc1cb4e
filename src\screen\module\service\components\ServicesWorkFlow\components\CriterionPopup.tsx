import React, {useState} from 'react';
import {
  View,
  ScrollView,
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {Winicon, ListTile} from 'wini-mobile-components';
import ScreenHeader from '../../../../../layout/header';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import type {CriterionPopupProps} from '../types';

export const CriterionPopup: React.FC<CriterionPopupProps> = ({
  data,
  onClose,
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpand = (itemId: string) => {
    const newExpandedItems = new Set(expandedItems);
    if (newExpandedItems.has(itemId)) {
      newExpandedItems.delete(itemId);
    } else {
      newExpandedItems.add(itemId);
    }
    setExpandedItems(newExpandedItems);
  };

  return (
    <View style={styles.container}>
      <ScreenHeader
        style={styles.header}
        title="Tiêu chí"
        prefix={<View />}
        action={
          <View style={styles.closeButtonContainer}>
            <Winicon
              src="outline/layout/xmark"
              onClick={onClose}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <ScrollView>
        {data?.map(item => (
          <View key={item?.Id} style={styles.itemContainer}>
            <ListTile
              style={styles.listTile}
              title={item?.Name}
              bottom={
                item?.Content ? (
                  <View style={styles.contentWrapper}>
                    <View
                      style={[
                        styles.contentContainer,
                        expandedItems.has(item.Id)
                          ? {}
                          : styles.contentContainerCollapsed,
                      ]}>
                      <RenderHTML
                        contentWidth={Dimensions.get('window').width - 32}
                        source={{html: item?.Content ?? ''}}
                        baseStyle={styles.htmlContent}
                      />
                    </View>
                    <TouchableOpacity
                      style={styles.toggleButton}
                      onPress={() => toggleExpand(item.Id)}>
                      <Text style={styles.toggleButtonText}>
                        {expandedItems.has(item.Id) ? 'Ẩn bớt' : 'Xem chi tiết'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                ) : null
              }
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButtonContainer: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  listTile: {
    padding: 0,
    paddingHorizontal: 16,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'flex-start',
    paddingTop: 8,
    paddingHorizontal: 16,
  },
  htmlContent: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_body_color,
  },
  itemContainer: {
    marginBottom: 8,
  },
  contentWrapper: {
    flex: 1,
    alignItems: 'flex-start',
    paddingTop: 8,
  },
  contentContainerCollapsed: {
    maxHeight: 100,
    overflow: 'hidden',
  },
  toggleButton: {
    alignSelf: 'flex-end',
    marginTop: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  toggleButtonText: {
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
    textDecorationLine: 'underline',
    fontSize: 14,
  },
});
