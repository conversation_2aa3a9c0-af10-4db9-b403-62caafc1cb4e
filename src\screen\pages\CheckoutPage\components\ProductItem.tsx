import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import FastImage from 'react-native-fast-image';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import ConfigAPI from '../../../../config/configApi';
import {ProductItemProps} from '../types';

const ProductItem: React.FC<ProductItemProps> = ({item}) => {
  return (
    <View style={styles.productItemContainer}>
      <FastImage
        style={styles.productImage}
        source={{
          uri: item.Img?.startsWith('http')
            ? item.Img
            : `${ConfigAPI.imgUrlId}${item.Img}`,
          priority: FastImage.priority.normal,
        }}
        resizeMode={FastImage.resizeMode.cover}
      />

      <View style={styles.productDetails}>
        <View style={styles.productHeader}>
          <Text style={styles.productName} numberOfLines={2}>
            {item?.Name ?? ''}
          </Text>
          <Text style={styles.productQuantity}>x{item?.Quantity ?? ''}</Text>
        </View>

        {/* <Text style={styles.rewardText}>
          Hoàn tiền: {Ultis.money(item.reward ?? 0)} CANPOINT
        </Text> */}

        <View style={styles.priceContainer}>
          {item?.Discount && item?.Discount > 0 ? (
            <View style={styles.discountPriceContainer}>
              <Text style={styles.originalPrice}>
                {Ultis.money(item.Price ?? 0)} đ
              </Text>
              <Text style={styles.discountedPrice}>
                {Ultis.money(item.Price - (item.Price * item.Discount) / 100)} đ
              </Text>
            </View>
          ) : (
            <Text style={styles.regularPrice}>{Ultis.money(item.Price)} đ</Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  productItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  productName: {
    ...TypoSkin.title3,
    marginBottom: 4,
    paddingRight: 4,
    flex: 1,
  },
  productQuantity: {
    ...TypoSkin.title3,
    marginBottom: 4,
  },
  rewardText: {
    ...TypoSkin.body3,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  discountPriceContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  originalPrice: {
    ...TypoSkin.heading7,
    color: '#000',
    textDecorationLine: 'line-through',
    fontWeight: '700',
  },
  discountedPrice: {
    ...TypoSkin.heading7,
    color: 'red',
    fontWeight: '700',
  },
  regularPrice: {
    ...TypoSkin.heading7,
    color: 'red',
    fontWeight: '700',
  },
});

export default ProductItem;
