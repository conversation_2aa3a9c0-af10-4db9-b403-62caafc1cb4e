import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useDispatch, useSelector} from 'react-redux';
import {GroupActions} from '../../reducers/groupReducer';
import {followingGroupsActions} from '../../reducers/followingGroupsReducer';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import ListTile from '../../../../../component/list-tile/list-tile';
import ConfigAPI from '../../../../../config/configApi';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {AppDispatch, RootState} from '../../../../../redux/store/store';
import {navigate, RootScreen} from '../../../../../router/router';

interface Props {
  titleList?: string;
  isSeeMore?: boolean;
  onPressSeeMore?: () => void;
}

export default function SocialGroups(props: Props) {
  const dispatch: AppDispatch = useDispatch();
  const {groups, isLoading, error, hasMore, page} = useSelector(
    (state: RootState) => state.group,
  );
  const followingGroups = useSelector(
    (state: RootState) => state.followingGroups.groups,
  );
  const creatorGroups = useSelector(
    (state: RootState) => state.myGroups.groups,
  );

  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    loadGroups();
  }, [activeTab]);

  const loadGroups = (refresh = true) => {
    if (refresh) {
      dispatch(GroupActions.getAllGroups(1, 10));
    } else if (hasMore) {
      dispatch(GroupActions.getAllGroups(page + 1, 10));
    }
  };

  const handleJoinGroup = async (group: any) => {
    dispatch(followingGroupsActions.followGroup(group));
    dispatch(GroupActions.updateMemberCount(group.Id, 1));
  };

  const isFollowing = (groupId: string) => {
    return (
      followingGroups.some(group => group.Id === groupId) ||
      creatorGroups.some(group => group.Id === groupId)
    );
  };

  const tabs = [
    {Id: 0, Name: 'For you'},
    {Id: 1, Name: 'Popular'},
    {Id: 2, Name: 'Following'},
    {Id: 3, Name: 'Watch'},
  ];

  const TabBar = () => {
    return (
      <ScrollView
        showsHorizontalScrollIndicator={false}
        horizontal={true}
        contentContainerStyle={{
          gap: 8,
        }}
        style={styles.tabBar}>
        {/* {tabs.map((tab, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => setActiveTab(index)}
            style={[styles.tab, activeTab === index && styles.activeTab]}>
            <Text style={[styles.tabText]}>{tab.Name}</Text>
          </TouchableOpacity>
        ))} */}
      </ScrollView>
    );
  };

  const renderItem = ({item}: any) => {
    const following = isFollowing(item.Id);

    return (
      <ListTile
        key={item.Id}
        onPress={() => {
          navigate(RootScreen.GroupIndex, {Id: item.Id});
        }}
        style={{
          borderColor: ColorThemes.light.neutral_main_border_color,
          borderWidth: 1,
          marginBottom: 16,
        }}
        title={item.Name}
        titleStyle={{
          ...TypoSkin.heading7,
          color: ColorThemes.light.neutral_text_title_color,
        }}
        subtitle={
          <Text
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}>
            {item.MemberCount} members
          </Text>
        }
        leading={
          <SkeletonImage
            source={{
              uri: `${
                item.Thumb
                  ? ConfigAPI.imgUrlId + item.Thumb
                  : 'https://redis.ktxgroup.com.vn/api/file/img/ce5cc92f4b67415bb2622cf40d0693e8'
              }`,
            }}
            height={56}
            width={56}
            style={{
              height: 56,
              width: 56,
              borderRadius: 100,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
            }}
          />
        }
        bottom={
          <View style={{width: '100%', flex: 1, paddingTop: 8, gap: 8}}>
            {item.Description && (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_body_color,
                }}>
                {item.Description}
              </Text>
            )}
            {following ? null : (
              <AppButton
                title={following ? 'Đã tham gia' : 'Tham gia'}
                onPress={() => (following ? null : handleJoinGroup(item))}
                textStyle={{
                  ...TypoSkin.buttonText5,
                  color: following
                    ? ColorThemes.light.neutral_text_subtitle_color
                    : ColorThemes.light.infor_main_color,
                }}
                backgroundColor={ColorThemes.light.transparent}
                borderColor={
                  following
                    ? ColorThemes.light.neutral_text_subtitle_color
                    : ColorThemes.light.infor_main_color
                }
                containerStyle={{
                  borderRadius: 8,
                  paddingHorizontal: 8,
                  alignItems: 'center',
                  alignSelf: 'baseline',
                  height: 24,
                }}
              />
            )}
          </View>
        }
      />
    );
  };

  const renderEmptyComponent = () => {
    if (isLoading) {
      return (
        <View style={{gap: 16}}>
          <GroupCardShimmer />
          <GroupCardShimmer />
          <GroupCardShimmer />
        </View>
      );
    }
  };

  return (
    <View style={{}}>
      {props.titleList ? (
        <View
          style={{
            paddingHorizontal: 16,
            paddingBottom: 8,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <Text
            style={{
              ...TypoSkin.heading7,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            Explore other groups
          </Text>
          <TabBar />
        </View>
      ) : null}
      <FlatList
        data={groups}
        nestedScrollEnabled={true}
        renderItem={renderItem}
        keyExtractor={item => item.Id}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        onRefresh={loadGroups}
        refreshing={isLoading}
        onEndReached={() => loadGroups(false)}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={() => {
          return <View style={{height: 24}} />;
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingVertical: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 17,
    borderRadius: 20,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
    borderWidth: 1,
    height: 24,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.primary_background,
    borderColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.neutral_text_body_color,
  },
});

export const GroupCardShimmer = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          borderColor: ColorThemes.light.neutral_main_border_color,
          borderWidth: 1,
          padding: 16,
          borderRadius: 8,
          gap: 12,
        }}>
        {/* Header row with avatar and title */}
        <View style={{flexDirection: 'row', gap: 12}}>
          {/* Avatar placeholder */}
          <View
            style={{
              width: 56,
              height: 56,
              borderRadius: 28,
            }}
          />

          {/* Title and member count */}
          <View style={{gap: 4, flex: 1}}>
            <View
              style={{
                width: '60%',
                height: 20,
                borderRadius: 4,
              }}
            />
            <View
              style={{
                width: '40%',
                height: 16,
                borderRadius: 4,
              }}
            />
          </View>
        </View>

        {/* Description placeholder */}
        <View style={{gap: 4}}>
          <View
            style={{
              width: '100%',
              height: 16,
              borderRadius: 4,
            }}
          />
          <View
            style={{
              width: '80%',
              height: 16,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Join button placeholder */}
        <View
          style={{
            width: 60,
            height: 24,
            borderRadius: 8,
          }}
        />
      </View>
    </SkeletonPlaceholder>
  );
};
