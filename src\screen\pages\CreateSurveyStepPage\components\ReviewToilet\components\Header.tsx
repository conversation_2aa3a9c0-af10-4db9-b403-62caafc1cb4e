import React from 'react';
import {Text, View, TouchableOpacity, StyleSheet} from 'react-native';
import {HeaderProps} from '../types';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';

const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  onAddNew,
  onSelectExisting,
}) => {
  return (
    <View style={styles.header}>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.subtitle}>{subtitle}</Text>

      <View style={{flexDirection: 'row', justifyContent: 'flex-end'}}>
        <TouchableOpacity
          style={[styles.btnAdd, {marginRight: 10}]}
          onPress={onSelectExisting}>
          <Winicon
            src="outline/user interface/c-add"
            size={16}
            color={ColorThemes.light.white}
          />
          <Text style={styles.btnAddText}>Chọn NVS có sẵn</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.btnAdd} onPress={onAddNew}>
          <Winicon
            src="outline/user interface/c-add"
            size={16}
            color={ColorThemes.light.white}
          />
          <Text style={styles.btnAddText}>Thêm mới NVS</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c5530',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  btnAdd: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 6,
  },
  btnAddText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
});

export default Header;
