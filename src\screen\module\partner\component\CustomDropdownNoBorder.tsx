import {useState, useEffect} from 'react';
import {Controller} from 'react-hook-form';
import {
  Modal,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {Text} from 'react-native-paper';
import {AppSvg} from 'wini-mobile-components';
import {FlatList} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {FCheckbox} from '../../../../component/export-component';

export const CustomDropdownNoBorder = ({
  control,
  name,
  placeholder,
  options,
  required = false,
  errors,
  style = {},
  icon,
}: {
  control: any;
  name: string;
  placeholder: string;
  options: Array<{id: any; name: string}>;
  required?: boolean;
  errors: any;
  style?: any;
  icon?: string;
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');

  // Debounce search text for better performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchText]);

  // Ensure options is always an array
  const safeOptions = Array.isArray(options) ? options : [];

  // Filter options based on debounced search text with enhanced search
  const filteredOptions = safeOptions.filter(item => {
    if (!debouncedSearchText.trim()) return true; // Show all if no search text

    const searchLower = debouncedSearchText.toLowerCase().trim();
    const itemName = item.name.toLowerCase();

    // Search by name (contains)
    if (itemName.includes(searchLower)) return true;

    // Search by ID if it's a string
    if (item.id.toString().toLowerCase().includes(searchLower)) return true;

    return false;
  });

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        required: required
          ? `Vui lòng chọn ${placeholder.toLowerCase()}`
          : false,
        validate: value => {
          if (required) {
            if (!value || (typeof value === 'string' && value.trim() === '')) {
              return `Vui lòng chọn ${placeholder.toLowerCase()}`;
            }
          }
          return true;
        },
      }}
      render={({field, fieldState}) => {
        // Single-select display logic
        const getDisplayText = () => {
          const selectedItem = safeOptions.find(
            item => item.id === field.value,
          );
          return selectedItem ? selectedItem.name : placeholder;
        };
        const hasSelection = field.value !== undefined && field.value !== null;
        return (
          <View style={[{flex: 1, width: '100%'}, style]}>
            <View>
              <TouchableOpacity
                onPress={() => {
                  setSearchText(''); // Reset search when opening modal
                  setIsVisible(true);
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingVertical: 12,
                  paddingLeft: 36,
                  height: 48,
                }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: 13,
                    fontWeight: '400',
                    lineHeight: 22,
                    color: hasSelection
                      ? '#00474F' // Same as TypoSkin.body3.color for selected text
                      : ColorThemes.light.neutral_text_title_color, // Same as TextFieldForm placeholder color
                  }}
                  numberOfLines={2}>
                  {getDisplayText()}
                </Text>
                <AppSvg SvgSrc={icon} size={18} />
              </TouchableOpacity>
              <Modal
                visible={isVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setIsVisible(false)}>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  activeOpacity={1}
                  onPress={() => setIsVisible(false)}>
                  <View
                    style={{
                      backgroundColor: 'white',
                      borderRadius: 8,
                      width: '85%',
                      maxHeight: '70%',
                      minHeight: 400,
                      height: 500, // Fixed height to prevent collapsing
                      flexDirection: 'column',
                    }}>
                    {/* Header with Title */}
                    <View
                      style={{
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        borderBottomWidth: 1,
                        borderBottomColor: '#E0E0E0',
                        flexShrink: 0,
                      }}>
                      <Text
                        style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: '#333333',
                          textAlign: 'center',
                          marginBottom: 15,
                        }}>
                        Chọn {placeholder}
                      </Text>

                      {/* Search Bar */}
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          backgroundColor: '#f5f5f5',
                          borderRadius: 8,
                          paddingHorizontal: 12,
                          paddingVertical: 8,
                          borderWidth: searchText.length > 0 ? 1 : 0,
                          borderColor:
                            searchText.length > 0
                              ? ColorThemes.light.primary_main_color
                              : 'transparent',
                        }}>
                        <Text
                          style={{
                            marginRight: 8,
                            color:
                              searchText.length > 0
                                ? ColorThemes.light.primary_main_color
                                : '#666',
                            fontSize: 16,
                          }}>
                          🔍
                        </Text>
                        <TextInput
                          style={{
                            flex: 1,
                            fontSize: 14,
                            color: '#333',
                            paddingVertical: 0,
                          }}
                          placeholder="Tìm kiếm theo tên"
                          placeholderTextColor="#999"
                          value={searchText}
                          onChangeText={setSearchText}
                          autoCapitalize="none"
                          autoCorrect={false}
                          returnKeyType="search"
                          clearButtonMode="while-editing"
                        />
                        {searchText.length > 0 && (
                          <TouchableOpacity
                            onPress={() => setSearchText('')}
                            style={{
                              marginLeft: 8,
                              padding: 4,
                              borderRadius: 12,
                              backgroundColor: '#ddd',
                            }}>
                            <Text
                              style={{
                                color: '#666',
                                fontSize: 12,
                                fontWeight: 'bold',
                              }}>
                              ✕
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>

                      {/* Search Results Info */}
                      {searchText.length > 0 && (
                        <View style={{marginTop: 8}}>
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#666',
                              textAlign: 'center',
                            }}>
                            {filteredOptions.length > 0
                              ? `Tìm thấy ${filteredOptions.length} kết quả`
                              : 'Không tìm thấy kết quả nào'}
                          </Text>
                        </View>
                      )}
                    </View>

                    {/* Content Area - Flexible */}
                    <View style={{flex: 1, minHeight: 0}}>
                      <FlatList
                        data={filteredOptions}
                        keyExtractor={(item, index) => `${item.id}-${index}`}
                        style={{flex: 1}}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{flexGrow: 1}}
                        ListEmptyComponent={() => (
                          <View
                            style={{
                              flex: 1,
                              padding: 20,
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: '#f5f5f5',
                            }}>
                            <Text style={{color: '#666', fontSize: 14}}>
                              {searchText.length > 0
                                ? `Không tìm thấy kết quả cho "${searchText}"`
                                : 'Không có dữ liệu'}
                            </Text>
                            {searchText.length > 0 && (
                              <TouchableOpacity
                                onPress={() => setSearchText('')}
                                style={{marginTop: 10}}>
                                <Text
                                  style={{
                                    color: ColorThemes.light.primary_main_color,
                                    fontSize: 14,
                                  }}>
                                  Xóa bộ lọc
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        )}
                        renderItem={({item}) => {
                          // Safety check for item
                          if (
                            !item ||
                            typeof item.id === 'undefined' ||
                            !item.name
                          ) {
                            return null;
                          }

                          const isSelected = field.value === item.id;

                          return (
                            <TouchableOpacity
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                paddingVertical: 15,
                                paddingHorizontal: 20,
                                borderBottomWidth: 0.5,
                                borderBottomColor: '#eee',
                                backgroundColor: isSelected
                                  ? '#f0f8ff'
                                  : 'transparent',
                              }}
                              onPress={() => {
                                // Single select - set value and close modal
                                field.onChange(item.id);
                                setSearchText(''); // Reset search
                                setIsVisible(false);
                              }}>
                              {/* Checkbox at the beginning of each item */}
                              <View style={{marginRight: 12}}>
                                <FCheckbox
                                  value={isSelected}
                                  onChange={v => {
                                    if (v) {
                                      // Checkbox được check - select item and close modal
                                      field.onChange(item.id);
                                      setSearchText(''); // Reset search
                                      setIsVisible(false);
                                    }
                                    // Không cần xử lý case uncheck vì sẽ tự động close modal
                                  }}
                                />
                              </View>

                              {/* Item text with search highlight */}
                              <View style={{flex: 1}}>
                                <Text
                                  style={{
                                    fontSize: 14,
                                    fontFamily: 'Inter',
                                    fontWeight: '400',
                                    lineHeight: 22,
                                    color: isSelected
                                      ? ColorThemes.light.primary_main_color
                                      : '#00474F',
                                  }}>
                                  {item.name}
                                </Text>
                                {/* Show ID if searching */}
                                {searchText.length > 0 &&
                                  item.id
                                    .toString()
                                    .toLowerCase()
                                    .includes(searchText.toLowerCase()) && (
                                    <Text
                                      style={{
                                        fontSize: 12,
                                        fontFamily: 'Inter',
                                        fontWeight: '300',
                                        color: '#999',
                                        marginTop: 2,
                                      }}>
                                      ID: {item.id}
                                    </Text>
                                  )}
                              </View>
                            </TouchableOpacity>
                          );
                        }}
                      />
                    </View>

                    {/* Action Buttons - Always at bottom */}
                  </View>
                </TouchableOpacity>
              </Modal>
            </View>
            {(fieldState.error || errors[name]) && (
              <View
                style={{
                  width: '100%',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                }}>
                <Text
                  style={{
                    color: 'red',
                    fontSize: 12,
                    marginTop: 4,
                  }}>
                  {fieldState.error?.message ??
                    errors[name]?.message ??
                    `Vui lòng chọn ${placeholder.toLowerCase()}`}
                </Text>
              </View>
            )}
          </View>
        );
      }}
    />
  );
};

export const CustomDropdownNoBorderWithMutiSelect = ({
  control,
  name,
  placeholder,
  options,
  required = false,
  errors,
  style = {},
  icon,
}: {
  control: any;
  name: string;
  placeholder: string;
  options: Array<{id: any; name: string}>;
  required?: boolean;
  errors: any;
  style?: any;
  icon?: string;
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tempSelectedItems, setTempSelectedItems] = useState<
    {id: number; name: string}[]
  >([]);
  const [searchText, setSearchText] = useState('');

  // Ensure options is always an array
  let safeOptions = Array.isArray(options) ? options : [];

  // Filter options based on search text
  const filteredOptions = safeOptions.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase()),
  );

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        required: required
          ? `Vui lòng chọn ${placeholder.toLowerCase()}`
          : false,
        validate: value => {
          if (required) {
            if (!value || (Array.isArray(value) && value.length === 0)) {
              return `Vui lòng chọn ${placeholder.toLowerCase()}`;
            }
          }
          return true;
        },
      }}
      render={({field, fieldState}) => {
        // Multi-select display logic for 2-line display
        const getDisplayText = (): string | {line1: string; line2: string} => {
          const selectedItems = safeOptions.filter(item => {
            // Safety checks
            if (!item || typeof item.id === 'undefined') return false;
            if (!field?.value || !Array.isArray(field.value)) return false;

            return field.value.includes(item.id);
          });
          if (selectedItems.length === 0) return placeholder;
          if (selectedItems.length === 1) return selectedItems[0].name;
          const names = selectedItems.map(item => item.name).join(', ');

          return {
            line1: names, // Dòng 1: Tất cả tên của item được chọn
            line2: `Đã chọn ${selectedItems.length} Nvs`, // Dòng 2: Số lượng đã chọn
          };
        };

        return (
          <View>
            <View style={[{flex: 1}, style]}>
              <TouchableOpacity
                onPress={() => {
                  // Initialize temp selection from current field value
                  if (field.value && Array.isArray(field.value)) {
                    const selectedItems = safeOptions.filter(item =>
                      field.value.includes(item.id),
                    );
                    setTempSelectedItems(selectedItems);
                  } else {
                    setTempSelectedItems([]);
                  }
                  setSearchText(''); // Reset search when opening modal
                  setIsVisible(true);
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingVertical: 12,
                  paddingHorizontal: 8,
                  minHeight: 48,
                }}>
                <View style={{flex: 1}}>
                  {(() => {
                    const displayData = getDisplayText();
                    if (typeof displayData === 'string') {
                      // Single line for placeholder
                      return (
                        <Text
                          style={{
                            fontFamily: 'Inter',
                            fontSize: 13,
                            fontWeight: '400',
                            lineHeight: 22,
                            color: '#161C2466',
                          }}>
                          {displayData}
                        </Text>
                      );
                    } else {
                      // Two lines for selected items
                      return (
                        <View>
                          {/* Dòng 1: Tất cả tên của item được chọn */}
                          <Text
                            style={{
                              fontFamily: 'Inter',
                              fontSize: 13,
                              fontWeight: '400',
                              lineHeight: 18,
                              color: '#00474F',
                            }}
                            numberOfLines={1}
                            ellipsizeMode="tail">
                            {displayData.line1}
                          </Text>
                          {/* Dòng 2: Số lượng đã chọn */}
                          <Text
                            style={{
                              fontFamily: 'Inter',
                              fontSize: 11,
                              fontWeight: '300',
                              lineHeight: 14,
                              color: ColorThemes.light.primary_main_color,
                              marginTop: 2,
                            }}>
                            {displayData.line2}
                          </Text>
                        </View>
                      );
                    }
                  })()}
                </View>
                <AppSvg SvgSrc={icon} size={20} />
              </TouchableOpacity>
              <Modal
                visible={isVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setIsVisible(false)}>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  activeOpacity={1}
                  onPress={() => setIsVisible(false)}>
                  <View
                    style={{
                      backgroundColor: 'white',
                      borderRadius: 8,
                      width: '85%',
                      height: 500, // Fixed height to prevent resizing
                      flexDirection: 'column',
                    }}>
                    {/* Header */}
                    <View
                      style={{
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        borderBottomWidth: 1,
                        borderBottomColor: '#E0E0E0',
                        flexShrink: 0,
                      }}>
                      <Text
                        style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: '#333333',
                          textAlign: 'center',
                          marginBottom: 15,
                        }}>
                        Chọn một mục
                      </Text>

                      {/* Search Bar */}
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          backgroundColor: '#f5f5f5',
                          borderRadius: 8,
                          paddingHorizontal: 12,
                          paddingVertical: 8,
                        }}>
                        <Text
                          style={{marginRight: 8, color: '#666', fontSize: 16}}>
                          🔍
                        </Text>
                        <TextInput
                          style={{
                            flex: 1,
                            fontSize: 14,
                            color: '#333',
                            paddingVertical: 0,
                          }}
                          placeholder="Tìm kiếm..."
                          placeholderTextColor="#999"
                          value={searchText}
                          onChangeText={setSearchText}
                          autoCapitalize="none"
                          autoCorrect={false}
                        />
                        {searchText.length > 0 && (
                          <TouchableOpacity
                            onPress={() => setSearchText('')}
                            style={{marginLeft: 8}}>
                            <Text style={{color: '#666', fontSize: 16}}>✕</Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>

                    {/* Content Area - Fixed */}
                    <View style={{flex: 1, height: 320}}>
                      {filteredOptions.length > 0 ? (
                        <ScrollView
                          style={{flex: 1, marginBottom: 80}}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={{
                            paddingBottom: 20,
                          }}>
                          {filteredOptions.map(item => {
                            // Safety check for item
                            if (
                              !item ||
                              typeof item.id === 'undefined' ||
                              !item.name
                            ) {
                              return null;
                            }

                            const isSelected = tempSelectedItems.some(
                              selectedItem => selectedItem.id === item.id,
                            );

                            return (
                              <TouchableOpacity
                                key={item.id}
                                style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  paddingVertical: 15,
                                  paddingHorizontal: 20,
                                  borderBottomWidth: 0.5,
                                  borderBottomColor: '#eee',
                                  backgroundColor: isSelected
                                    ? '#f0f8ff'
                                    : 'transparent',
                                }}
                                onPress={() => {
                                  // Toggle selection for this specific item
                                  const isCurrentlySelected =
                                    tempSelectedItems.some(
                                      selectedItem =>
                                        selectedItem.id === item.id,
                                    );

                                  if (isCurrentlySelected) {
                                    // Remove item if currently selected (true -> false)
                                    console.log(
                                      `Removing item ${item.id} from selection`,
                                    );
                                    setTempSelectedItems(prev => {
                                      const newSelection = prev.filter(
                                        selectedItem =>
                                          selectedItem.id !== item.id,
                                      );
                                      console.log(
                                        'New selection after removal:',
                                        newSelection.map(i => i.id),
                                      );
                                      return newSelection;
                                    });
                                  } else {
                                    // Add item if not currently selected (false -> true)
                                    console.log(
                                      `Adding item ${item.id} to selection`,
                                    );
                                    setTempSelectedItems(prev => {
                                      const newSelection = [...prev, item];
                                      console.log(
                                        'New selection after addition:',
                                        newSelection.map(i => i.id),
                                      );
                                      return newSelection;
                                    });
                                  }
                                }}>
                                {/* Checkbox at the beginning of each item */}
                                <View
                                  style={{
                                    width: 20,
                                    height: 20,
                                    borderRadius: 3,
                                    borderWidth: 2,
                                    borderColor: isSelected
                                      ? ColorThemes.light.primary_main_color
                                      : '#ccc',
                                    backgroundColor: isSelected
                                      ? ColorThemes.light.primary_main_color
                                      : 'transparent',
                                    marginRight: 12,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <FCheckbox
                                    value={isSelected}
                                    onChange={v => {
                                      if (v) {
                                        // Checkbox được check (false -> true)
                                        // Add item to selection
                                        setTempSelectedItems(prev => [
                                          ...prev,
                                          item,
                                        ]);
                                      } else {
                                        // Checkbox được uncheck (true -> false)
                                        // Remove item from selection
                                        setTempSelectedItems(prev =>
                                          prev.filter(
                                            selectedItem =>
                                              selectedItem.id !== item.id,
                                          ),
                                        );
                                      }
                                    }}
                                  />
                                </View>

                                {/* Item text */}
                                <Text
                                  style={{
                                    flex: 1,
                                    fontSize: 14,
                                    fontFamily: 'Inter',
                                    fontWeight: '400',
                                    lineHeight: 22,
                                    color: isSelected
                                      ? ColorThemes.light.primary_main_color
                                      : '#00474F',
                                  }}>
                                  {item.name}
                                </Text>
                              </TouchableOpacity>
                            );
                          })}
                        </ScrollView>
                      ) : (
                        <View
                          style={{
                            flex: 1,
                            padding: 20,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f5f5f5',
                          }}>
                          <Text style={{color: '#666', fontSize: 14}}>
                            {searchText.length > 0
                              ? `Không tìm thấy kết quả cho "${searchText}"`
                              : 'Không có dữ liệu'}
                          </Text>
                          {searchText.length > 0 && (
                            <TouchableOpacity
                              onPress={() => setSearchText('')}
                              style={{marginTop: 10}}>
                              <Text
                                style={{
                                  color: ColorThemes.light.primary_main_color,
                                  fontSize: 14,
                                }}>
                                Xóa bộ lọc
                              </Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      )}
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        paddingHorizontal: 20,
                        paddingVertical: 15,
                        borderTopWidth: 1,
                        borderTopColor: '#E0E0E0',
                        gap: 12,
                        backgroundColor: 'white',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        borderBottomLeftRadius: 8,
                        borderBottomRightRadius: 8,
                      }}>
                      {/* Cancel Button */}
                      <TouchableOpacity
                        style={{
                          flex: 1,
                          backgroundColor: '#E5E5E5',
                          paddingVertical: 12,
                          borderRadius: 8,
                          alignItems: 'center',
                        }}
                        onPress={() => {
                          setTempSelectedItems([]); // Reset temp selection
                          setSearchText(''); // Reset search when canceling
                          setIsVisible(false);
                        }}>
                        <Text
                          style={{
                            fontSize: 14,
                            fontWeight: '600',
                            color: '#666666',
                          }}>
                          Thoát
                        </Text>
                      </TouchableOpacity>

                      {/* Confirm Button */}
                      <TouchableOpacity
                        style={{
                          flex: 1,
                          backgroundColor: ColorThemes.light.primary_main_color,
                          paddingVertical: 12,
                          borderRadius: 8,
                          alignItems: 'center',
                          opacity: tempSelectedItems.length > 0 ? 1 : 0.6,
                        }}
                        onPress={() => {
                          try {
                            // Apply selected items to field
                            const selectedIds = tempSelectedItems
                              .filter(
                                item => item && typeof item.id !== 'undefined',
                              )
                              .map(item => item.id);

                            field.onChange(selectedIds);
                            setIsVisible(false);
                            setTempSelectedItems([]); // Reset temp selection
                          } catch (error) {
                            console.error('Error confirming selection:', error);
                            setIsVisible(false);
                          }
                        }}>
                        <Text
                          style={{
                            fontSize: 14,
                            fontWeight: '600',
                            color: 'white',
                          }}>
                          Xác nhận ({tempSelectedItems.length})
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </TouchableOpacity>
              </Modal>
            </View>
            {errors[name] && (
              <Text
                style={{
                  color: 'red',
                  fontSize: 12,
                  marginTop: 4,
                  fontFamily: 'Roboto',
                }}>
                {errors[name].message ||
                  `Vui lòng chọn ${placeholder.toLowerCase()}`}
              </Text>
            )}
          </View>
        );
      }}
    />
  );
};
