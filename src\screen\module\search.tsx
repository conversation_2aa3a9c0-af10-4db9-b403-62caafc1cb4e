import { ScrollView, StyleSheet, View } from 'react-native';
import ScreenHeader from '../layout/header';
import { ColorThemes } from '../../assets/skin/colors';
import ReportAll from './report/setting';
import { useSelector } from 'react-redux';
import { useSelectorCustomerState } from '../../redux/hooks/hooks';
import { useEffect, useState } from 'react';
import { DataController } from '../base-controller';
import { TaskStatus, TaskType, ToiletStatus } from './service/components/da';
import ReportAllToilet from './report/setting';
import { FTextField } from '../../component/export-component';
import EmptyPage from '../../project-component/empty-page';

export default function SearchIndex() {
    const user = useSelectorCustomerState().data;
    const [data, setData] = useState<any>();
    const now = new Date();
    const listTime = [7, 30, 45, 60, 90];
    const [selectedTime, setSelectedTime] = useState(7);
    const [searchValue, setSearchValue] = useState("")

    useEffect(() => {
        if (user) getData();
    }, [selectedTime, user]);

    const getData = async () => { };

    return (
        <View style={{ flex: 1 }}>
            <ScreenHeader title="Tìm kiếm" bottom={<View style={{ flexDirection: "row", width: "100%", paddingHorizontal: 16, paddingBottom: 16 }}>
                <FTextField style={{ paddingHorizontal: 16, width: "100%" }} onChange={(vl: any) => {
                    setSearchValue(vl)
                }} value={searchValue} placeholder="Tìm kiếm" />
            </View>} />
            <ScrollView style={styles.contentContainer}>
                <EmptyPage />
            </ScrollView>
        </View>
    );
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: ColorThemes.light.white,
    },
    contentContainer: {
        padding: 16,
        flex: 1,
    },
});
