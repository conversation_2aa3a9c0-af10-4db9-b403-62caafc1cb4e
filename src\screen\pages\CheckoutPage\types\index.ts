import {CartItem} from '../../../../types/cartTypes';

// Route params interface
export interface CheckoutRouteParams {
  items: CartItem[];
  address: any;
}

// Store group interface
export interface StoreGroup {
  ShopId: string;
  ShopName: string;
  ShopAvatar: string;
  items: CartItem[];
  totalPrice: number;
}

// Extended cart item with reward information
export interface CheckoutCartItem extends CartItem {
  reward?: number;
}

// Payment method interface
export interface PaymentMethod {
  id: number;
  name: string;
}

// Order interface
export interface Order {
  Id: string;
  CustomerId: string;
  Name: string;
  ShopId: string;
  Code: string;
  DateCreated: number;
  DateUpdated: number;
  Status: string;
  Value: number;
  AddressId: string;
  PaymentType: number;
  Description: string;
}

// Order detail interface
export interface OrderDetail {
  Id: string;
  Name: string;
  OrderId: string;
  ProductId: string;
  DateCreated: number;
  Quantity: number;
  Price: number;
  Discount: number;
  Status: string;
  Total: number;
}

// Stock check result interface
export interface StockCheckResult {
  isValid: boolean;
  outOfStockItems: string[];
}

// Component props interfaces
export interface ProductItemProps {
  item: CheckoutCartItem;
}

export interface StoreGroupProps {
  storeGroup: StoreGroup;
  index: number;
  payment: PaymentMethod;
  isDone: boolean;
  onPaymentChange: (payment: PaymentMethod) => void;
}

export interface PaymentMethodSelectorProps {
  payment: PaymentMethod;
  onPaymentChange: (payment: PaymentMethod) => void;
  isDone: boolean;
}

export interface OrderSummaryProps {
  storeGroup: StoreGroup;
}

export interface CheckoutBottomBarProps {
  totalPrice: number;
  isProcessing: boolean;
  isDone: boolean;
  onSubmitOrder: () => void;
  onNavigateHome: () => void;
  onNavigateOrders: () => void;
  onContinueShopping: () => void;
}

export interface OrderSuccessMessageProps {
  isDone: boolean;
}

export interface LoadingIndicatorProps {
  isLoading: boolean;
  text?: string;
}

// Hook return types
export interface UseCheckoutReturn {
  // State
  items: CartItem[];
  address: any;
  customer: any;
  customerAddress: any;
  isProcessing: boolean;
  isDone: boolean;
  payment: PaymentMethod;
  
  // Actions
  setPayment: (payment: PaymentMethod) => void;
  submitOrder: () => void;
  
  // Refs
  dialogRef: React.RefObject<any>;
  btsRef: React.RefObject<any>;
}

export interface UseStoreGroupsReturn {
  storeGroups: StoreGroup[];
  loadingStoreGroups: boolean;
  totalPrice: number;
}

export interface UseOrderProcessingReturn {
  isProcessing: boolean;
  checkStock: () => Promise<StockCheckResult>;
  handlePlaceOrder: () => Promise<void>;
}
