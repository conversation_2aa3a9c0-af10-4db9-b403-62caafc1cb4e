import {Dimensions, Text, TouchableOpacity} from 'react-native';
import {View} from 'react-native';
import {useEffect, useRef, useState} from 'react';
import {useForm} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import AppButton from '../../../../component/button';
import {ComponentStatus} from '../../../../component/component-status';
import {
  FBottomSheet,
  showBottomSheet,
  hideBottomSheet,
  Winicon,
  showSnackbar,
} from '../../../../component/export-component';
import {CustomerRankType} from '../../../../config/Contanst';
import {TextFieldForm} from '../../../../project-component/component-form';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {AppDispatch} from '../../../../redux/store/store';
import {DataController} from '../../../base-controller';
import WScreenFooter from '../../../layout/footer';

export default function About({data, isProfile, onUpdate}: any) {
  const bottomSheetRef = useRef<any>(null);
  const customer = useSelectorCustomerState().data;
  const dispatch: AppDispatch = useDispatch();
  return (
    <View
      style={{
        flex: 1,
        padding: 16,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
        gap: 16,
      }}>
      <FBottomSheet ref={bottomSheetRef} />

      <View
        style={{
          borderRadius: 8,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          padding: 16,
          gap: 16,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text
            style={{
              ...TypoSkin.heading6,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            Description
          </Text>
          {!isProfile && customer?.Id === data?.CustomerId && (
            <TouchableOpacity
              style={{padding: 4}}
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  title: 'Edit description',
                  suffixAction: <View />,
                  prefixAction: (
                    <TouchableOpacity
                      onPress={() => hideBottomSheet(bottomSheetRef)}
                      style={{padding: 6, alignItems: 'center'}}>
                      <Winicon
                        src="outline/layout/xmark"
                        size={20}
                        color={ColorThemes.light.neutral_text_body_color}
                      />
                    </TouchableOpacity>
                  ),
                  children: (
                    <EditAbout
                      ref={bottomSheetRef}
                      currentData={data}
                      onUpdate={(value: any) => {
                        onUpdate(value);
                      }}
                    />
                  ),
                });
              }}>
              <Winicon src="fill/user interface/n-edit" size={16} />
            </TouchableOpacity>
          )}
        </View>
        {!isProfile && data?.Name && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Text style={TypoSkin.regular2}>Tên Group</Text>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  flex: 1,
                  color: ColorThemes.light.neutral_text_title_color,
                  textAlign: 'right',
                  paddingLeft: 32,
                },
              ]}>
              {data?.Name ?? '-'}
            </Text>
          </View>
        )}
        {!isProfile && data?.Description && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Text style={TypoSkin.regular2}>Mô tả</Text>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  flex: 1,
                  color: ColorThemes.light.neutral_text_title_color,
                  textAlign: 'right',
                  paddingLeft: 32,
                },
              ]}>
              {data?.Description ?? '-'}
            </Text>
          </View>
        )}
        {isProfile && (
          <View
            style={{
              gap: 16,
            }}>
            {data?.Name && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Họ tên</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.neutral_text_title_color,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Name ?? '-'}
                </Text>
              </View>
            )}
            {data?.Email && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Email</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.neutral_text_title_color,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Email ?? '-'}
                </Text>
              </View>
            )}
            {data?.Mobile && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Số điện thoại</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.neutral_text_title_color,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Mobile ?? '-'}
                </Text>
              </View>
            )}
            {data?.Rank && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Rank</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.neutral_text_title_color,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Rank
                    ? `Hạng ${
                        data?.Rank == CustomerRankType.normal ? 'Thường' : 'VIP'
                      }`
                    : ''}
                </Text>
              </View>
            )}
            <View
              style={{
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Địa chỉ</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {data?.Address ?? '-'}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* <View
        style={{
          borderRadius: 8,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          padding: 16,
          gap: 16,
        }}>
        <Text
          style={{
            ...TypoSkin.heading8,
            color: ColorThemes.light.neutral_text_placeholder_color,
          }}>
          Rule
        </Text>
        <View style={{gap: 8}}>
        <ListTile
              key={'index'}
              style={{
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
              }}
              leading={
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_text_placeholder_color,
                  }}>
                  #{index + 1}
                </Text>
              }
              title={`Rule ${index + 1}`}
              titleStyle={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.neutral_text_title_color,
              }}
              trailing={<Text>Rule</Text>}
            />
        </View>
      </View> */}
    </View>
  );
}

const EditAbout = ({
  ref,
  currentData,
  onUpdate,
}: {
  ref: any;
  currentData: any;
  onUpdate: any;
}) => {
  const [loading, setLoading] = useState(false);

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {...currentData},
  });
  const groupController = new DataController('Group');
  const submitForm = async (data: any) => {
    const res = await groupController.edit([{...data, memberList: undefined}]);
    if (res?.code === 200) {
      hideBottomSheet(ref);
      onUpdate({Name: data.Name, Description: data.Description});
      showSnackbar({
        message: 'Cập nhật thành công',
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      showSnackbar({
        message: res?.message ?? 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const errorForm = (errors: any) => {
    console.log(errors);
  };

  return (
    <View
      style={{
        width: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        height: Dimensions.get('window').height / 2,
      }}>
      <View style={{padding: 16, gap: 24}}>
        <TextFieldForm
          required
          style={{
            width: '100%',
            backgroundColor: '#fff',
            borderRadius: 8,
          }}
          placeholder="Tên Group"
          label="Tên Group"
          control={methods.control}
          errors={methods.formState.errors}
          register={methods.register}
          name="Name"
          textFieldStyle={{paddingHorizontal: 16}}
        />
        <TextFieldForm
          label="Mô tả"
          textStyle={{textAlignVertical: 'top'}}
          numberOfLines={10}
          multiline={true}
          textFieldStyle={{
            paddingHorizontal: 16,
            paddingTop: 16,
            paddingBottom: 16,
            height: 100,
            justifyContent: 'flex-start',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}
          style={{width: '100%'}}
          register={methods.register}
          control={methods.control}
          errors={methods.formState.errors}
          name="Description"
        />
      </View>
      <WScreenFooter style={{marginHorizontal: 16}}>
        <AppButton
          title={'Lưu'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={methods.handleSubmit(submitForm, errorForm)}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </View>
  );
};
