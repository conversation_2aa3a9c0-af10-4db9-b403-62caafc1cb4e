import {useNavigation} from '@react-navigation/native';
import {useState, useRef, useEffect} from 'react';
import {useForm} from 'react-hook-form';
import {View, FlatList, RefreshControl} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  FTextField,
  showSnackbar,
  Winicon,
} from '../../../component/export-component';
import {FPopup} from '../../../component/popup/popup';
import EmptyPage from '../../../project-component/empty-page';
import {CardToiletHoriSkeleton} from '../../../project-component/skeletonCard';
import {useSelectorCustomerState} from '../../../redux/hooks/hooks';
import {DataController} from '../../base-controller';
import ScreenHeader from '../../layout/header';
import {ComponentStatus} from '../../../component/component-status';
import MyWorkCard from './components/card/MyWorkCard';
import {TaskType, ToiletServiceStatus} from '../service/components/da';
import {LoadMoreIndicator} from './NewOrderWorkList/components';

export default function MyWorkList() {
  const user = useSelectorCustomerState().data;
  const popupRef = useRef<any>();
  const [isLoading, setLoading] = useState(false);
  const [isRefreshing, setRefreshing] = useState(false);
  const [isLoadingMore, setLoadingMore] = useState(false);

  const [data, setData] = useState<Array<any>>([]);
  const [toilet, setToilet] = useState<Array<any>>([]);
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);
  const [result, setResult] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const now = new Date();

  const PAGE_SIZE = 20;

  const getData = async (page: number = 1, isLoadMore: boolean = false) => {
    if (user) {
      try {
        if (isLoadMore) {
          setLoadingMore(true);
        } else {
          setLoading(true);
          setCurrentPage(1);
          setHasMore(true);
          if (!isRefreshing) {
            setData([]);
          }
        }

        const taskController = new DataController('Task');

        // Build search query with optional text search
        const baseQuery = `@CustomerId:{${user.Id}} @Type:[${TaskType.consultant} ${TaskType.other}]`;
        const textSearchQuery = searchValue.trim()
          ? `@Name:(*${searchValue.trim()}*)`
          : '';
        const searchRaw = [baseQuery, textSearchQuery]
          .filter(Boolean)
          .join(' ');

        let toilets: any[] = [];

        taskController
          .aggregateList({
            page: page,
            size: PAGE_SIZE,
            searchRaw: searchRaw,
            sortby: [{prop: 'DateCreated', direction: 'DESC'}],
          })
          .then(res => {
            if (res.code === 200) {
              const toiletIds = res.data
                .filter((e: any) => !e.ToiletServicesId)
                .map((e: any) => e.ToiletId)
                .filter(
                  (id: any, i: any, arr: string | any[]) =>
                    arr.indexOf(id) === i,
                );
              const toiletServiceIds = res.data
                .filter((e: any) => e.ToiletServicesId)
                .map((e: any) => e.ToiletServicesId)
                .filter(
                  (id: any, i: any, arr: string | any[]) =>
                    arr.indexOf(id) === i,
                );
              if (toiletIds.length) {
                const toiletController = new DataController('Toilet');
                toiletController.getByListId(toiletIds).then(toiletRes => {
                  if (toiletRes.code === 200) {
                    if (isLoadMore) {
                      setToilet(prev => [...prev, ...toiletRes.data]);
                    } else {
                      setToilet(toiletRes.data);
                      toilets = toilets.concat(toiletRes.data);
                    }
                  }
                });
              }
              if (toiletServiceIds.length) {
                const toiletServicesController = new DataController(
                  'ToiletServices',
                );
                toiletServicesController
                  .getByListId(toiletServiceIds)
                  .then(serviceRes => {
                    if (serviceRes.code === 200) {
                      if (isLoadMore) {
                        setToiletServices(prev => [
                          ...prev,
                          ...serviceRes.data,
                        ]);
                      } else {
                        setToiletServices(serviceRes.data);
                        toilets = toilets.concat(serviceRes.data);
                      }
                    }
                  });
              }

              // Update data with pagination logic
              if (isLoadMore) {
                setData(prev => [...prev, ...res.data]);
              } else {
                setData(res.data);
              }

              // Update pagination state
              setTotalCount(res.totalCount || 0);
              setCurrentPage(page);
              setHasMore(res.data.length === PAGE_SIZE && res.data.length > 0);
            }
          });
        taskController
          .group({
            searchRaw: `@CustomerId:{${user.Id}} @Type:[${ToiletServiceStatus.register} ${ToiletServiceStatus.run}]`,
            reducers: `LOAD * APPLY "exists(@DateEnd) && @DateEnd < ${new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 23, 59, 59, 999).getTime()}" AS _overdue GROUPBY 2 @Status @_overdue REDUCE COUNT 0 AS _count`,
          })
          .then(res => {
            if (res.code === 200) setResult(res.data);
          });
      } catch (error) {
        console.log('Error in getData:', error);
      } finally {
        setLoading(false);
        setRefreshing(false);
        setLoadingMore(false);
      }
    }
  };

  useEffect(() => {
    if (user) {
      getData();
    }
  }, [user]);

  useEffect(() => {
    if (user && searchValue !== undefined) {
      getData();
    }
  }, [searchValue]);

  const loadMore = async () => {
    if (!isLoadingMore && hasMore && !isLoading) {
      await getData(currentPage + 1, true);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    setCurrentPage(1);
    setHasMore(true);
    await getData(1, false);
  };

  const onChangeTask = async (t: any) => {
    const controller = new DataController('Task');
    const res = await controller.edit([t]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    setData(ts => ts.map(e => (e.Id === t.Id ? t : e)));
    showSnackbar({
      message: 'Chỉnh sửa công việc thành công',
      status: ComponentStatus.SUCCSESS,
    });
  };

  return (
    <View
      style={{
        flex: 1,
        height: '100%',
        width: '100%',
        backgroundColor: ColorThemes.light.white,
      }}>
      <FPopup ref={popupRef} />
      <ScreenHeader
        height={8}
        bottom={
          <View style={{paddingHorizontal: 16, paddingBottom: 16}}>
            <FTextField
              style={{paddingHorizontal: 16, width: '100%', height: 40}}
              onChange={vl => {
                setSearchValue(vl.trim());
              }}
              returnKeyType="search"
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              }
              value={searchValue}
              placeholder="Tìm kiếm"
            />
          </View>
        }
      />
      <FlatList
        data={data}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        style={{flex: 1, gap: 8, marginHorizontal: 16, marginVertical: 16}}
        ItemSeparatorComponent={() => <View style={{height: 16}} />}
        renderItem={({item, index}: any) => {
          var project = undefined;
          if (item.ToiletServicesId) {
            project = toiletServices.find(
              e => e?.Id === item?.ToiletServicesId,
            );
          } else {
            project = toilet.find(e => e?.Id === item?.ToiletId);
          }

          return (
            <MyWorkCard
              key={index}
              index={index}
              project={project}
              item={item}
              toiletServices={toiletServices}
              onChangeTask={onChangeTask}
            />
          );
        }}
        ListEmptyComponent={() =>
          isLoading && data.length === 0 ? (
            Array.from(Array(10)).map((_, index) => (
              <View key={index} style={{gap: 16}}>
                <CardToiletHoriSkeleton />
              </View>
            ))
          ) : (
            <EmptyPage title="Chưa có công việc nào được tạo" />
          )
        }
        ListFooterComponent={() => (
          <View style={{height: 100}}>
            <LoadMoreIndicator
              isLoading={isLoadingMore}
              hasMore={hasMore}
              data={data}
            />
          </View>
        )}
      />
    </View>
  );
}
