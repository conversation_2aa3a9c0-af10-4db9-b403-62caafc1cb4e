import { useState } from 'react';
import ImageCropPicker from 'react-native-image-crop-picker';
import DocumentPicker from 'react-native-document-picker';
import { UseFormReturn } from 'react-hook-form';

export const useFileUpload = (methods: UseFormReturn<any>) => {
  const [isPickerVisible, setPickerVisible] = useState(false);

  const pickImages = async () => {
    try {
      const images = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        maxFiles: 5,
      });
      
      if (images) {
        const currentFiles = methods.getValues('Files') ?? [];
        const newFiles = images.map((e: any) => ({
          name: e.filename ?? 'new img',
          type: e.mime,
          uri: e.path,
          size: e.size,
        }));
        
        methods.setValue('Files', [...currentFiles, ...newFiles]);
      }
    } catch (error) {
      console.log('Error picking images:', error);
    }
  };

  const pickFiles = async () => {
    try {
      const result = await DocumentPicker.pick({
        allowMultiSelection: true,
      });
      
      if (result) {
        const currentFiles = methods.getValues('Files') ?? [];
        const newFiles = result.map((e: any) => ({
          name: e.name ?? 'new file',
          type: e.type,
          uri: e.uri,
          size: e.size,
        }));
        
        methods.setValue('Files', [...currentFiles, ...newFiles]);
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('Document picker cancelled by user');
      } else {
        console.log('Error picking document:', err);
      }
    }
  };

  const removeFile = (fileUri: string) => {
    const currentFiles = methods.getValues('Files') ?? [];
    const updatedFiles = currentFiles.filter((e: any) => e.uri !== fileUri);
    methods.setValue('Files', updatedFiles);
  };

  const getFileSize = (sizeInBytes: number) => {
    return `${Math.round(sizeInBytes / (1024 * 1024))}MB`;
  };

  return {
    pickImages,
    pickFiles,
    removeFile,
    getFileSize,
    isPickerVisible,
    setPickerVisible,
  };
};
