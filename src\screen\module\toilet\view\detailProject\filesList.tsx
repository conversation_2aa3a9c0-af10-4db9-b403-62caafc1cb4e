import {differenceInDays} from 'date-fns';
import React, {useRef, useState, useMemo, useEffect, forwardRef} from 'react';
import {useForm} from 'react-hook-form';
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Pressable,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  showSnackbar,
  Winicon,
  FDialog,
  showDialog,
} from '../../../../../component/export-component';
import ListTile from '../../../../../component/list-tile/list-tile';
import {
  FPopup,
  showPopup,
  closePopup,
} from '../../../../../component/popup/popup';
import {Fselect1Form} from '../../../../../project-component/component-form';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {
  CustomerType,
  CustomerRole,
} from '../../../../../redux/reducers/user/da';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
  ToiletFileType,
} from '../../../service/components/da';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import ImageCropPicker from 'react-native-image-crop-picker';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import WebView from 'react-native-webview';
import ConfigAPI from '../../../../../config/configApi';
import {BaseDA} from '../../../../baseDA';
import DocumentPicker, {types} from 'react-native-document-picker';
import {CardToiletHoriSkeleton} from '../../../../../project-component/skeletonCard';

interface Props {
  data: any;
  serviceData: any;
  refreshing: any;
  onRefresh: any;
}

export default function FilesList(props: Props) {
  const {data, serviceData, refreshing, onRefresh} = props;

  const [isLoading, setLoading] = useState(false);

  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const userRole = useSelectorCustomerState().role;
  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();
  const [files, setFiles] = useState({data: [], totalCount: undefined});
  const [pageDetails, setPageDetails] = useState({page: 1, size: 10});
  const [consultantTask, setConsultantTask] = useState<any>();
  const isEditable = useMemo(() => user?.Id === data?.CustomerId, [user, data]);

  const _uploadFiles = async (files: {
    uri: string;
    type: string;
    name: string;
  }) => {
    if (files) {
      const controller = new DataController('ToiletFile');
      const res = await BaseDA.uploadFiles([files]);
      if (res.length) {
        const fileRes = await controller.add(
          res.map((e: any) => {
            return {
              Id: randomGID(),
              DateCreated: Date.now(),
              Name: e.Name,
              File: e.Id,
              Size: e.Size,
              Type: ToiletFileType.other,
              Description: `${e.Type} - ${e.Url}`,
              Sort: 1,
            };
          }),
        );
        if (fileRes.code === 200)
          getData({page: pageDetails.page, size: pageDetails.size});
      }
    }
  };

  const getData = async ({page, size}: any) => {
    setLoading(true);
    const controller = new DataController('ToiletFile');
    const res = await controller.aggregateList({
      page: page ?? 1,
      size: size ?? 10,
      searchRaw: `@ToiletId:{${data.Id}} @Type:[${ToiletFileType.other} ${ToiletFileType.other}]`,
      sortby: [
        {prop: 'Sort', direction: 'DESC'},
        {prop: 'DateCreated', direction: 'DESC'},
      ],
    });
    if (res.code === 200)
      setFiles({data: res.data, totalCount: res.totalCount});
  };

  useEffect(() => {
    if (data) getData({}).then(() => setLoading(false));
  }, [data]);

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 5,
    });
    if (image) {
      _uploadFiles({
        name: image.filename ?? 'new file img',
        type: image.mime,
        uri: image.path,
      });
      closePopup(popupRef);
    }
  };

  const pickerFile = async () => {
    try {
      const result = await DocumentPicker.pick({
        // allowMultiSelection: false,
        // type: [DocumentPicker.types.pdf],
      });
      if (result) {
        const {name, size, type, uri} = result[0];
        _uploadFiles({
          name: name ?? 'new file img',
          type: type ?? 'unknown',
          uri: uri,
        });
        closePopup(popupRef);

        return {
          name,
          type,
          uri,
          size,
        };
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the document picker
        console.log('Document picker cancelled by user');
      } else {
        // Handle other errors
        console.log('Error picking document:', err);
      }
      return null;
    }
  };

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />

      <View style={{flex: 1, paddingHorizontal: 16}}>
        <FlatList
          contentContainerStyle={{paddingBottom: 56}}
          data={files.data}
          style={{flex: 1}}
          refreshControl={
            <RefreshControl
              refreshing={refreshing ?? false}
              onRefresh={() => {
                if (onRefresh) onRefresh();
                getData({page: pageDetails.page, size: pageDetails.size});
              }}
            />
          }
          keyExtractor={(item, i) => item + '-' + i}
          ItemSeparatorComponent={() => <View style={{width: 12}} />}
          ListHeaderComponent={() => {
            return (
              <TouchableOpacity
                onPress={() => {
                  showPopup({
                    ref: popupRef,
                    enableDismiss: true,
                    children: (
                      <View
                        style={{
                          backgroundColor:
                            ColorThemes.light.neutral_absolute_background_color,
                          height: Dimensions.get('window').height / 3,
                          borderTopLeftRadius: 12,
                          borderTopRightRadius: 12,
                        }}>
                        <ScreenHeader
                          style={{
                            backgroundColor: ColorThemes.light.transparent,
                            flexDirection: 'row',
                            paddingVertical: 4,
                          }}
                          title={`Thêm mới`}
                          prefix={<View />}
                          action={
                            <View
                              style={{
                                flexDirection: 'row',
                                padding: 12,
                                alignItems: 'center',
                              }}>
                              <Winicon
                                src="outline/layout/xmark"
                                onClick={() => closePopup(popupRef)}
                                size={20}
                                color={
                                  ColorThemes.light.neutral_text_body_color
                                }
                              />
                            </View>
                          }
                        />
                        <ListTile
                          onPress={() => {
                            pickerImg();
                          }}
                          title={'Thêm ảnh, video'}
                        />
                        <ListTile
                          onPress={() => {
                            pickerFile();
                          }}
                          title={'Thêm Files'}
                        />
                      </View>
                    ),
                  });
                }}
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 8,
                  borderWidth: 0.4,
                  borderColor: ColorThemes.light.neutral_main_border,
                  borderStyle: 'dashed',
                  borderRadius: 8,
                  padding: 8,
                  marginTop: 12,
                }}>
                <SkeletonImage
                  source={{
                    uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                  }}
                  style={{width: 35, height: 35, objectFit: 'cover'}}
                />
                <Text
                  numberOfLines={1}
                  style={{
                    ...TypoSkin.buttonText4,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  Tải tệp lên
                </Text>
              </TouchableOpacity>
            );
          }}
          renderItem={({item, index}: any) => {
            return (
              <ListTile
                key={`${item.Url}`}
                onPress={async () => {
                  if (item?.Description?.split(' - ')[1])
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            height: Dimensions.get('window').height - 65,
                            borderTopLeftRadius: 12,
                            borderTopRightRadius: 12,
                          }}>
                          <ScreenHeader
                            style={{
                              backgroundColor: ColorThemes.light.transparent,
                              flexDirection: 'row',
                              paddingVertical: 4,
                            }}
                            title={`Xem nhanh`}
                            prefix={<View />}
                            action={
                              <TouchableOpacity
                                onPress={() => closePopup(popupRef)}
                                style={{padding: 12, alignItems: 'center'}}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  size={20}
                                  color={
                                    ColorThemes.light.neutral_text_body_color
                                  }
                                />
                              </TouchableOpacity>
                            }
                          />
                          <WebView
                            style={{
                              flex: 1,
                              backgroundColor:
                                ColorThemes.light
                                  .neutral_absolute_background_color,
                            }}
                            source={{
                              uri:
                                ConfigAPI.url.replace('/api/', '') +
                                item?.Description?.split(' - ')[1],
                            }}
                          />
                        </View>
                      ),
                    });
                }}
                leading={
                  <Text
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    {index + 1}
                  </Text>
                }
                title={item.Name ?? `Ảnh ${index + 1}`}
                titleStyle={[
                  TypoSkin.heading7,
                  {color: ColorThemes.light.neutral_text_title_color},
                ]}
                subtitle={
                  <View style={{gap: 4}}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      {Ultis.formatFileSize(item.Size)}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      {item.DateCreated
                        ? Ultis.datetoString(
                            new Date(item.DateCreated),
                            'dd/mm/yyyy hh:mm',
                          )
                        : '-'}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}
                      numberOfLines={3}>
                      {item.Description}
                    </Text>
                  </View>
                }
                listtileStyle={{gap: 16}}
                style={{
                  borderColor: ColorThemes.light.neutral_main_border_color,
                  borderWidth: 1,
                  marginTop: 16,
                  padding: 16,
                }}
                trailing={
                  <TouchableOpacity
                    onPress={() => {
                      showDialog({
                        status: ComponentStatus.WARNING,
                        ref: dialogRef,
                        title: 'Bạn chắc chắn muốn xóa tệp này?',
                        onSubmit: async () => {
                          const controller = new DataController('ToiletFile');
                          const res = await controller.delete([item.Id]);
                          if (res.code === 200) {
                            showSnackbar({
                              message: `Xóa file ${item.Name} thành công!`,
                              status: ComponentStatus.SUCCSESS,
                            });
                            getData({});
                          } else
                            showSnackbar({
                              message: res.message,
                              status: ComponentStatus.ERROR,
                            });
                        },
                      });
                    }}
                    style={{padding: 4}}>
                    <FontAwesomeIcon
                      icon={faMinusCircle}
                      size={20}
                      color="#D72525FF"
                      style={{backgroundColor: '#fff', borderRadius: 20}}
                    />
                  </TouchableOpacity>
                }
              />
            );
          }}
          ListEmptyComponent={() =>
            isLoading ? (
              Array.from(Array(10)).map((_, index) => (
                <View key={index} style={{marginBottom: 16}}>
                  <CardToiletHoriSkeleton />
                </View>
              ))
            ) : (
              <EmptyPage title="Nhà vệ sinh chưa có tệp thiết kế hay ảnh nào" />
            )
          }
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  bottomButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
    gap: 8,
    marginHorizontal: 10,
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
