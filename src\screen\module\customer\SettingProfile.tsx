import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../layout/header';
import {useNavigation} from '@react-navigation/native';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  FDialog,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../component/export-component';
import {RootScreen} from '../../../router/router';
import {useSelectorCustomerState} from '../../../redux/hooks/hooks';
import {useEffect, useRef, useState} from 'react';
import FLoading from '../../../component/Loading/FLoading';
import {ComponentStatus} from '../../../component/component-status';
import {CustomerActions} from '../../../redux/reducers/user/reducer';
import {useDispatch} from 'react-redux';
import {PrimaryButton} from '../../../project-component/button';
import {useForm} from 'react-hook-form';
import EditCustomer from './component/form/edit-customer';
import WScreenFooter from '../../layout/footer';
import ConfigAPI from '../../../config/configApi';
import {DataController} from '../../base-controller';
import {CustomerItem} from '../../../redux/reducers/user/da';
import {SafeAreaView} from 'react-native-safe-area-context';
import {BaseDA} from 'screen/baseDA';
import {randomGID} from 'utils/Utils';

export default function SettingProfile() {
  const navigation = useNavigation<any>();
  const userData = useSelectorCustomerState().data;
  const [onLoading, setOnLoading] = useState(false);
  const dispatch = useDispatch<any>();
  const methods = useForm({shouldFocusError: false});
  const [onEdit, setOnEdit] = useState(false);
  const [bankList, setBankList] = useState<Array<any>>([]);
  const dataBank = new DataController('Bank');

  useEffect(() => {
    fetchBanksInDataBank();
    CustomerActions.getInfor(dispatch);
  }, []);

  // get banks in vietqr
  // const fetchBanks = async () => {
  //   try {
  //     const response = await BaseDA.get(ConfigAPI.bankUrl);
  //     if (response?.code === '00') {
  //       console.log('response banks vietqr', response.data?.length);
  //     }
  //   } catch (error) {
  //     console.error('Error fetching bank data:', error);
  //   }
  // };

  // getBanks in dataBank local
  const fetchBanksInDataBank = async () => {
    try {
      const response = await dataBank.getAll();
      if (response?.code === 200) {
        let banks = [...new Set(response.data)];
        // sắp xếp alphabetically
        banks = banks.sort((a: any, b: any) =>
          a.ShortName.localeCompare(b.ShortName),
        );
        setBankList(banks);
      }
    } catch (error) {
      console.error('Error fetching bank data:', error);
    }
  };

  useEffect(() => {
    const data: any = userData ?? {};
    Object.keys(data).forEach(props => {
      if (data[props]) {
        if (props === 'Gender') {
          methods.setValue(props, data[props] ? 1 : 0);
        } else {
          methods.setValue(props, data[props]);
        }
      }
    });
  }, []);

  const submitEdit = async () => {
    showDialog({
      ref: dialogDelAccRef,
      status: ComponentStatus.INFOR,
      title: 'Bạn chắc chắn muốn lưu thông tin cá nhân?',
      onSubmit: async () => {
        showSnackbar({
          message: 'Cập nhật thông tin thành công',
          status: ComponentStatus.SUCCSESS,
        });
        setOnEdit(false);
        const newData = methods.getValues();
        CustomerActions.edit(dispatch, newData);
      },
    });
  };

  const dialogDelAccRef = useRef<any>();
  const dialogDelAcc = () => {
    showDialog({
      ref: dialogDelAccRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn muốn xóa tài khoản đang sử dụng?',
      content: 'Mọi dữ liệu sẽ bị xóa và không thể khôi phục lại.',
      onSubmit: async () => {
        if (userData?.Id) {
          setOnLoading(true);
          await CustomerActions.delete(dispatch, userData.Id, navigation).then(
            () => {
              setOnLoading(false);
            },
          );
        }
      },
    });
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading avt={''} visible={onLoading} />
      <FDialog ref={dialogDelAccRef} />
      <ScreenHeader
        title={onEdit ? 'Chỉnh sửa thông tin' : 'Hồ sơ cá nhân'}
        onBack={() => navigation.goBack()}
        action={
          !onEdit ? (
            <TouchableOpacity
              style={{padding: 16}}
              onPress={() => {
                setOnEdit(true);
              }}>
              <Winicon
                src="outline/users/user-edit"
                color={ColorThemes.light.neutral_text_subtitle_color}
                size={20}
              />
            </TouchableOpacity>
          ) : null
        }
      />
      <ScrollView>
        {onEdit ? (
          <View style={{flex: 1}}>
            <EditCustomer methods={methods} bankList={bankList} />
          </View>
        ) : (
          <View style={{backgroundColor: '#fff'}}>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Họ tên</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Name ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Số điện thoại</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Mobile ?? '-'}
              </Text>
            </View>

            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>CCCD</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.PeoplesIdentity ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Chủ tài khoản</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.BankAccountName ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Số tài khoản</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.BankAccount ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Ngân hàng</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {`${bankList?.find(item => item.Id === userData?.BankId)?.ShortName ?? ''} - ${bankList?.find(item => item.Id === userData?.BankId)?.Name ?? ''}`}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Địa chỉ</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Address ?? '-'}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
      {onEdit ? null : (
        <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
          <PrimaryButton
            isLoading={false}
            style={{
              borderRadius: 8,
              marginHorizontal: 16,
              marginBottom: 65,
              flex: 1,
              backgroundColor: ColorThemes.light.primary_main_color,
              justifyContent: 'center',
            }}
            title={!userData?.Password ? 'Tạo mật khẩu' : 'Cập nhật mật khẩu'}
            onPress={() => {
              navigation.push(RootScreen.ForgotPass);
            }}
          />
        </WScreenFooter>
      )}
      {userData?.Id !== ConfigAPI.adminKtxId ? (
        <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
          <PrimaryButton
            isLoading={false}
            style={{
              borderRadius: 8,
              marginHorizontal: 16,
              flex: 1,
              backgroundColor: onEdit
                ? ColorThemes.light.primary_main_color
                : ColorThemes.light.error_main_color,
              justifyContent: 'center',
            }}
            title={onEdit ? 'Lưu thông tin' : 'Xóa tài khoản'}
            onPress={onEdit ? submitEdit : dialogDelAcc}
          />
        </WScreenFooter>
      ) : onEdit ? (
        <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
          <PrimaryButton
            isLoading={false}
            style={{
              borderRadius: 8,
              marginHorizontal: 16,
              flex: 1,
              backgroundColor: ColorThemes.light.primary_main_color,
              justifyContent: 'center',
            }}
            title={'Lưu thông tin'}
            onPress={submitEdit}
          />
        </WScreenFooter>
      ) : null}
    </SafeAreaView>
  );
}
