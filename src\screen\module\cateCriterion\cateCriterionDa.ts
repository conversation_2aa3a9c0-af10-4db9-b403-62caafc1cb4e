import {DataController} from '../../base-controller';

class CateCriterionDA {
  controller: DataController;
  CriterionController: DataController;
  constructor() {
    this.controller = new DataController('CateCriterion');
    this.CriterionController = new DataController('Criterion');
  }

  getAll = async () => {
    const res: any = await this.controller.getAll();
    if (res.code === 200) {
      return res.data;
    }
    return [];
  };

  getAllWithCriterion = async () => {
    const res: any = await this.controller.getAll();
    if (res.code === 200) {
      const criterionRes: any = await this.CriterionController.getAll();

      if (criterionRes.code === 200) {
        res.data.forEach((item: any) => {
          item.Criterions = criterionRes.data.filter(
            (criterion: any) => criterion.CateCriterionId === item.Id,
          );
        });
      }
      return res.data;
    }
    return [];
  };
}

export default CateCriterionDA;
