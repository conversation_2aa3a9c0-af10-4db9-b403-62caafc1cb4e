import {useCallback, useEffect, useRef, useState} from 'react';
import {useSelector} from 'react-redux';
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ScrollableTabs from '../../../../component/scrollable/ScrollableTabs';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';
import {RootState} from '../../../../redux/store/store';
import iconSvg from '../../../../svgs/iconSvg';
import {AppSvg} from 'wini-mobile-components';
import {closePopup, FPopup, showPopup} from '../../../../component/popup/popup';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Winicon} from '../../../../component/export-component';
import ScreenHeader from '../../../layout/header';
import ListTile from '../../../../component/list-tile/list-tile';

const TABS_DATA = [
  {
    id: '1',
    type: 'svg' as const,
    label: 'Tổng quan',
    icon: iconSvg.All,
    size: 20,
  },
  {
    id: '2',
    type: 'svg' as const,
    label: 'Báo cáo',
    icon: iconSvg.reportToilet,
    size: 20,
  },
  {
    id: '3',
    type: 'svg' as const,
    label: 'Công việc',
    icon: iconSvg.work,
    size: 20,
  },
];

const viewList = [
  {
    id: '4',
    title: 'Lịch',
    key: 'calendar',
    icon: 'outline/user interface/calendar-date-2',
  },
  {
    id: '5',
    title: 'Thiết bị',
    key: 'devices',
    icon: 'outline/user interface/bolt',
  },
  {
    id: '6',
    title: 'Chế phẩm sinh học',
    key: 'bios',
    icon: 'outline/education/chemistry',
  },
  {
    id: '7',
    title: 'Thống kê vận hành',
    key: 'bios',
    icon: 'outline/education/chemistry',
  },
  {id: '8', title: 'Tệp', key: 'file', icon: 'outline/text/attach'},
  {
    id: '9',
    title: 'Tài liệu',
    key: 'contract',
    icon: 'outline/files/document-copy',
  },
  {
    id: '10',
    title: 'Đánh giá Sạch - Xanh - Tuần hoàn',
    key: 'comment',
    icon: 'outline/user interface/b-comment',
  },
  {
    id: '11',
    title: 'Đánh giá',
    key: 'comment',
    icon: 'outline/user interface/b-comment',
  },
];

const ScrollOptionToilet = ({
  setTabId,
  tabId,
}: {
  setTabId?: (tabId: string) => void;
  tabId?: string;
}) => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);
  const popupRef = useRef<FPopup>(null!);

  // // Handle filter change
  const handleFilterChange = useCallback(
    (filterId: string) => {
      const newActiveFilters = {
        [filterId]: true,
      };
      productByCategoryHook.setData('filter', {
        ...filter,
        activeFilters: newActiveFilters,
      });
      setTabId?.(filterId);
    },
    [filter, productByCategoryHook],
  );

  const handleOpenBottomSheet = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <View
          style={{
            height: Dimensions.get('window').height - 100,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
            borderTopLeftRadius: 12,
            borderTopRightRadius: 12,
          }}>
          <ScreenHeader
            style={{
              backgroundColor: ColorThemes.light.transparent,
              flexDirection: 'row',
              paddingVertical: 4,
            }}
            title={`Chọn màn hình`}
            prefix={<View />}
            action={
              <TouchableOpacity
                onPress={() => closePopup(popupRef)}
                style={{
                  flexDirection: 'row',
                  padding: 12,
                  alignItems: 'center',
                }}>
                <Winicon
                  src="outline/layout/xmark"
                  size={20}
                  color={ColorThemes.light.neutral_text_body_color}
                />
              </TouchableOpacity>
            }
          />
          <ScrollView style={{}}>
            {viewList.map((item, index) => {
              return (
                <ListTile
                  leading={
                    <Winicon
                      src={item.icon}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                      size={16}
                    />
                  }
                  style={{
                    borderBottomColor:
                      ColorThemes.light.neutral_main_background_color,
                    borderBottomWidth: 1,
                  }}
                  key={`${item.id}`}
                  onPress={() => {
                    if (setTabId) setTabId(item.id.toString());
                    closePopup(popupRef);
                  }}
                  title={item.title}
                  trailing={
                    item.id === tabId ? (
                      <Winicon
                        src={'fill/layout/circle-check'}
                        size={20}
                        color={ColorThemes.light.primary_main_color}
                      />
                    ) : (
                      <View />
                    )
                  }
                />
              );
            })}
          </ScrollView>
        </View>
      ),
    });
  };

  useEffect(() => {
    handleFilterChange('1');
  }, []);

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}>
        <ScrollableTabs
          onChangeTab={handleFilterChange}
          data={TABS_DATA}
          active={tabId && Number(tabId) > 3 ? false : true}
        />
        {TABS_DATA.length >= 3 && (
          <TouchableOpacity
            style={styles.LastIcon}
            onPress={handleOpenBottomSheet}>
            <AppSvg SvgSrc={iconSvg.threeLine} size={30} />
          </TouchableOpacity>
        )}
      </ScrollView>
      <FPopup ref={popupRef} />
    </View>
  );
};

export default ScrollOptionToilet;

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    marginHorizontal: 16,
  },
  scrollView: {
    flexGrow: 0, // Không cho ScrollView chiếm hết không gian
  },
  scrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 10, // Thêm padding để tránh bị cắt
  },
  LastIcon: {
    marginLeft: 4,
  },
});
