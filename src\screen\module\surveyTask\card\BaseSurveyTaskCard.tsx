import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
} from 'react-native';
import {SurveyTask} from '../../../../types/surveyTaskType';
import {CustomerItem} from '../../../../redux/reducers/user/da';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';

type BaseSurveyTaskCardProps = {
  data: SurveyTask & {
    Customer?: CustomerItem | null;
    Executor?: CustomerItem | null;
  };
  onPress?: (id: string) => void;
  style?: StyleProp<ViewStyle>;
};

const BaseSurveyTaskCard: React.FC<BaseSurveyTaskCardProps> = ({
  style,
  data,
  onPress,
}) => {
  const customer = useSelectorCustomerState().data;
  if (!data) {
    return null;
  }

  const handlePress = () => {
    if (onPress && data?.Id) {
      onPress(data.Id);
    }
  };

  const formatDateRange = (startTimestamp: number, endTimestamp: number) => {
    if (!startTimestamp || !endTimestamp) return 'Chưa có thông tin';

    const startDate = new Date(startTimestamp).toLocaleDateString('vi-VN');
    const endDate = new Date(endTimestamp).toLocaleDateString('vi-VN');

    return `${startDate} - ${endDate}`;
  };

  const getStatusText = (status?: number) => {
    switch (status) {
      case 1:
        return 'Đang thực hiện';
      case 2:
        return 'Hoàn thành';
      default:
        return 'Không xác định';
    }
  };

  const getStatusColor = (status?: number) => {
    switch (status) {
      case 1:
        return '#FF9800'; // Orange
      case 2:
        return '#4CAF50'; // Green
      default:
        return '#9E9E9E'; // Grey
    }
  };

  return (
    <TouchableOpacity onPress={handlePress}>
      <View style={[styles.card, style]}>
        <Text style={styles.title} numberOfLines={2}>
          {data.Name ||
            `Khảo sát NVS của khách hàng ${data.Customer?.Name ? data.Customer?.Name : ''}`}
        </Text>

        <View style={styles.infoRow}>
          <Text style={styles.label}>Người thực hiện: </Text>
          <Text style={styles.value}>
            {data.Executor?.Name || 'Chưa có thông tin'}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.label}>Khách hàng: </Text>
          <Text style={styles.value}>
            {data.Customer?.Name || 'Chưa có thông tin'}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.label}>Ngày thực hiện: </Text>
          <Text style={styles.value}>
            {formatDateRange(data.DateStart, data.DateEnd)}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.label}>Địa chỉ: </Text>
          <Text style={styles.value} numberOfLines={2}>
            {data.Address || data.Customer?.Address || 'Chưa có thông tin'}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.label}>Mô tả: </Text>
          <Text style={styles.value} numberOfLines={2}>
            {data.Description || 'Không có mô tả'}
          </Text>
        </View>

        <View style={styles.footerRow}>
          <TouchableOpacity
            style={[
              styles.statusButton,
              {backgroundColor: getStatusColor(data.Status) + '20'},
            ]}
            activeOpacity={0.7}>
            <Text
              style={[styles.statusText, {color: getStatusColor(data.Status)}]}>
              {getStatusText(data.Status)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_background_color,
  },
  title: {
    ...TypoSkin.title3,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    fontWeight: '600',
    color: '#004D40',
  },
  value: {
    flex: 1,
    color: '#004D40',
  },
  footerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    justifyContent: 'space-between',
  },
  statusButton: {
    backgroundColor: '#FFE0B2',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 16,
  },
  statusText: {
    color: '#FF9800',
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 12,
  },
});

export default BaseSurveyTaskCard;
