import { useState } from "react";
import { Pressable, StyleSheet, TextStyle, View } from "react-native";
import { ColorSkin } from "../../assets/skin/colors";
import Swiper from "react-native-swiper";
import { TouchableWithoutFeedback } from "react-native-gesture-handler";

export function FSwiper({ style = {}, autoPlay = true, stopAutoPlayWhenTouch = true, autoplayTimeout = 5, children }: { style?: TextStyle, autoPlay?: boolean, stopAutoPlayWhenTouch?: boolean, autoplayTimeout?: number, children: React.ReactNode }) {
    const [auto, setAuto] = useState(autoPlay)

    return <View style={{ height: 266, ...style }}>
        <Swiper
            removeClippedSubviews={false}
            dot={<View style={styles.dotStyle} />}
            autoplay={auto}
            scrollEnabled
            autoplayTimeout={autoplayTimeout}
            activeDot={<View style={styles.activeDotStyle} />}
            onTouchStart={stopAutoPlayWhenTouch ? () => {
                setAuto(false)
            } : undefined}>{children}</Swiper>
    </View>
}

const styles = StyleSheet.create({
    dotStyle: {
        backgroundColor: ColorSkin.backgroundGrey2,
        width: 8,
        height: 8,
        borderRadius: 8,
        margin: 4,
        top: 8,
        shadowColor: "rgba(0, 0, 0, 0.03)",
        shadowOffset: {
            width: 0,
            height: 4
        },
        shadowRadius: 20,
        elevation: 20,
        shadowOpacity: 1,
    },
    activeDotStyle: {
        backgroundColor: ColorSkin.primary,
        width: 24,
        height: 8,
        borderRadius: 100,
        margin: 4,
        top: 8,
        shadowColor: "rgba(0, 0, 0, 0.03)",
        shadowOffset: {
            width: 0,
            height: 4
        },
        shadowRadius: 20,
        elevation: 20,
        shadowOpacity: 1,
    },
})