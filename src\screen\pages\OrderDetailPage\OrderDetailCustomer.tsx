import React from 'react';
import {
  StyleSheet,
  ScrollView,
  RefreshControl,
  View,
  Pressable,
} from 'react-native';
import {ComponentStatus, FDialog, showSnackbar} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {navigate, RootScreen} from '../../../router/router';
import Clipboard from '@react-native-clipboard/clipboard';
import PopupCancelOrder from '../../module/orderProduct/popup/PopupCancelOrder';
import PopupUpdateStatusOrder from '../../module/orderProduct/popup/PopupUpdateStatusOrder/PopupUpdateStatusOrder';
import TitleHeader from '../../layout/headers/TitleHeader';
import {StatusOrder} from '../../../config/Contanst';
import {
  OrderStatusTimeline,
  OrderInfo,
  OrderItems,
  OrderActionButtons,
  useOrderData,
  useOrderActions,
} from './index';

const OrderDetailCustomer: React.FC<{orderId: string}> = ({orderId}) => {
  // Use custom hooks
  const {order, orderDetails, refreshing, handleRefresh, fetchOrderData} =
    useOrderData(orderId);

  const {
    isCancelPopupVisible,
    isSubmittingCancel,
    isUpdateStatusPopupVisible,
    setCancelPopupVisible,
    setUpdateStatusPopupVisible,
    handleCancelOrder,
    handleSubmitRejectOrder,
    handleSubmitUpdateStatus,
    dialogRef,
  } = useOrderActions(order, fetchOrderData);

  const currentStatus = order?.Status || StatusOrder.new;

  // Handle copy order code
  const handleCopyOrderCode = () => {
    Clipboard.setString(order?.Code || '');
    showSnackbar({
      message: 'Đã sao chép mã đơn hàng',
      status: ComponentStatus.SUCCSESS,
    });
  };

  // Handle product press
  const handleProductPress = (productId: string) => {
    navigate(RootScreen.DetailProductPage, {
      id: productId,
    });
  };

  // Handle action buttons
  const handleChatWithShop = () => {
    // TODO: Implement chat functionality
    console.log('Chat with shop');
  };

  const handleContactShop = () => {
    // TODO: Implement contact functionality
    console.log('Contact shop');
  };

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <PopupCancelOrder
        visible={isCancelPopupVisible}
        onClose={() => setCancelPopupVisible(false)}
        onSubmit={handleSubmitRejectOrder}
        loading={isSubmittingCancel}
      />
      <PopupUpdateStatusOrder
        item={order}
        visible={isUpdateStatusPopupVisible}
        onClose={() => setUpdateStatusPopupVisible(false)}
        handleUpdateStatusProcessOrder={handleSubmitUpdateStatus}
      />
      <TitleHeader title={'Chi tiết đơn hàng'} />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        <Pressable>
          <OrderStatusTimeline currentStatus={currentStatus} order={order} />
        </Pressable>
        <Pressable>
          <OrderInfo
            order={order}
            currentStatus={currentStatus}
            onCopyOrderCode={handleCopyOrderCode}
          />
        </Pressable>
        <Pressable>
          <OrderItems
            orderDetails={orderDetails}
            order={order}
            isCustomer={true}
            style={{marginTop: 16}}
            onProductPress={handleProductPress}
            onCancelOrder={handleCancelOrder}
            onRequestRefund={() => {}}
            onReviewProduct={() => {}}
          />
        </Pressable>
      </ScrollView>
      <OrderActionButtons
        currentStatus={currentStatus}
        isCustomer={true}
        onChatWithShop={handleChatWithShop}
        onContactShop={handleContactShop}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
});

export default OrderDetailCustomer;
