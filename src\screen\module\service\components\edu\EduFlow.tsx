import {useNavigation, useRoute} from '@react-navigation/native';
import {useEffect} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WebView} from 'react-native-webview';
import FLoading, {LoadingUI} from '../../../../../component/Loading/FLoading';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {Winicon} from '../../../../../component/export-component';
import ScreenHeader from '../../../../layout/header';
import EmptyPage from '../../../../../project-component/empty-page';

interface Props {
  type?: string;
  id?: string;
  url?: string;
}

export default function WebViewServiceFlow(props: Props) {
  const {type, id, url} = props;
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const {width, height} = Dimensions.get('window');

  return (
    <SafeAreaView style={{flex: 1}}>
      <View style={{flex: 1}}>
        <WebView
          source={{
            uri: url ?? route?.params?.url,
          }}
          renderError={() => <EmptyPage />}
          startInLoadingState
          textZoom={100}
          bounces={false}
          nestedScrollEnabled
          onLoadEnd={() => {
            console.log('WebView finished loading');
          }}
          limitsNavigationsToAppBoundDomains
          renderLoading={() => {
            return (
              <View
                style={{
                  backgroundColor: 'transparent',
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  top: height / 3,
                  zIndex: 9,
                }}>
                <LoadingUI />
              </View>
            );
          }}
          originWhitelist={['*']}
          javaScriptEnabled={true}
        />
      </View>
    </SafeAreaView>
  );
}

// {route?.params?.type != 'EduFlow' || type != 'EduFlow' ? (
//     <ScreenHeader
//       style={{
//         shadowColor: 'rgba(0, 0, 0, 0.03)',
//         shadowOffset: {
//           width: 0,
//           height: 4,
//         },
//         shadowRadius: 20,
//         elevation: 20,
//         shadowOpacity: 1,
//       }}
//       children={
//         <View style={{flexDirection: 'row', alignItems: 'center'}}>
//           <View
//             style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
//             <TouchableOpacity
//               style={{
//                 padding: 12,
//                 gap: 8,
//                 flexDirection: 'row',
//                 alignItems: 'center',
//               }}
//               onPress={() => {
//                 navigation.pop();
//               }}>
//               <Winicon
//                 src="outline/arrows/left-arrow"
//                 color={ColorThemes.light.neutral_text_subtitle_color}
//                 size={20}
//               />
//               <Text
//                 style={[
//                   TypoSkin.heading8,
//                   {color: ColorThemes.light.neutral_text_title_color},
//                 ]}>
//                 {/* {title} */}
//                 Quay lại
//               </Text>
//             </TouchableOpacity>
//           </View>
//         </View>
//       }
//     />
//   ) : null}
