import {
  View,
  NativeScrollEvent,
  RefreshControl,
  ScrollView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useState, useRef, useCallback} from 'react';
import HeaderLogo from '../../layout/headers/HeaderLogo';
import {RootScreen} from '../../../router/router';
import {ColorThemes} from '../../../assets/skin/colors';
import DefaultBanner from '../banner/DefaultBanner';
import HotProductsSection from './sections/HotProductsSection';
import FreeShipProductSection from './sections/FreeShipProductSection';
import MuchSearchSearch from './sections/MuchSearchSearch';
import ProductBestSeller from './sections/productBestSeller';
import CategoryGrid from '../category/views/CategoryGrid';
import SuggestionProductSection from './sections/SuggestionProductSection';

const ProductIndex = () => {
  const navigation = useNavigation<any>();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoadmore, setIsLoadmore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const lastScrollY = useRef(0);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const onSeeMore = (categoryId?: string) => {
    if (categoryId) {
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId: categoryId,
      });
    } else {
      navigation.navigate(RootScreen.ProductListByCategory);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    setHasMore(true); // Reset hasMore on refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
    setRefreshTrigger(prev => prev + 1);
  };

  const handleLoadMore = useCallback(() => {
    if (!isLoadmore && hasMore) {
      setIsLoadmore(true);
    }
  }, [isLoadmore, hasMore]);

  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }: NativeScrollEvent) => {
    const paddingToBottom = 100; // Reduced from 300 to 100
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const handleScroll = useCallback(
    (event: any) => {
      const currentScrollY = event.nativeEvent.contentOffset.y;

      // Only process if scrolling down and close to bottom
      if (
        currentScrollY > lastScrollY.current &&
        isCloseToBottom(event.nativeEvent)
      ) {
        handleLoadMore();
      }

      lastScrollY.current = currentScrollY;
    },
    [handleLoadMore],
  );

  return (
    <View
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <HeaderLogo />

      <ScrollView
        style={{height: '100%', width: '100%', paddingHorizontal: 16}}
        onScroll={handleScroll}
        scrollEventThrottle={16} // Changed from 200 to 16 for smoother detection
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <View key="banner1" style={{marginVertical: 12}}>
          <DefaultBanner key={`banner1-${refreshTrigger}`} />
        </View>

        <View style={{marginVertical: 8}}>
          <CategoryGrid
            key="categories"
            numColumns={3}
            onCategoryPress={category => {
              onSeeMore(category.Id);
            }}
          />
        </View>

        <HotProductsSection
          key={`HotProductsSection-${refreshTrigger}`} // Force re-render on refresh
          pageSize={10}
          onSeeAll={onSeeMore}
          onRefresh={refreshing}
        />
        <FreeShipProductSection
          key={`FreeShipProductSection-${refreshTrigger}`}
          onRefresh={refreshing}
        />

        {/* <MuchSearchSearch
          key={`MuchSearchSearch-${refreshTrigger}`} // Force re-render on refresh
          onSeeMore={onSeeMore}
          onRefresh={refreshing}
        /> */}
        <View key="banner2" style={{marginVertical: 12}}>
          <DefaultBanner key={`banner2-${refreshTrigger}`} />
        </View>

        <ProductBestSeller
          key={`ProductBestSeller-${refreshTrigger}`} // Force re-render on refresh
          isSeeMore
          id="best-selling"
          onPressSeeMore={onSeeMore}
          onRefresh={refreshing}
        />
        <SuggestionProductSection
          key={`SuggestionProductSection-${refreshTrigger}`} // Force re-render on refresh
          onSeeAllPress={onSeeMore}
          scrollEnabled={false}
          onRefresh={refreshing}
          isLoadmore={isLoadmore}
          onLoadMoreEnd={hasMore => {
            setIsLoadmore(false);
            setHasMore(hasMore);
          }}
        />
        <View key="footer_space" style={{height: 100}} />
      </ScrollView>
    </View>
  );
};

export default ProductIndex;
