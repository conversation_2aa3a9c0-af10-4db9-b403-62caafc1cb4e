import React from 'react';
import {Text} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {OrderStatusBadgeProps} from '../types';

const OrderStatusBadge: React.FC<OrderStatusBadgeProps> = ({status}) => {
  const getStatusColor = () => {
    switch (status) {
      case 1:
        return ColorThemes.light.primary_main_color;
      case 2:
        return ColorThemes.light.warning_main_color;
      case 3:
        return ColorThemes.light.success_main_color;
      case 4:
        return ColorThemes.light.error_main_color;
      default:
        return ColorThemes.light.primary_main_color;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 1:
        return 'Đơn hàng mới';
      case 2:
        return 'Đang xử lý';
      case 3:
        return 'Hoàn thành';
      case 4:
        return 'Đã hủy';
      default:
        return '';
    }
  };

  return (
    <Text
      style={{
        ...TypoSkin.body3,
        color: getStatusColor(),
      }}>
      {getStatusText()}
    </Text>
  );
};

export default OrderStatusBadge;
