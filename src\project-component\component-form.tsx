import {
  Image,
  KeyboardTypeOptions,
  ReturnKeyTypeOptions,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {
  DialogSearchMap,
  FSelect1,
  FTextField,
  showDialogMap,
} from '../component/export-component';
import {Text} from 'react-native-paper';
import {
  Control,
  Controller,
  FieldErrors,
  FieldValues,
  UseFormGetValues,
} from 'react-hook-form';
import {TypoSkin} from '../assets/skin/typography';
import React, {ReactNode, useRef} from 'react';
import FRadioButton from '../component/radio-button/radio-button';
import ImageCropPicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import {FlatList} from 'react-native-gesture-handler';
import {ColorSkin} from '../assets/skin/colors';
import {OutlinePLus} from '../assets/icon';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faArrowRight,
  faMapLocationDot,
  faMinusCircle,
} from '@fortawesome/free-solid-svg-icons';
import ConfigAPI from '../config/configApi';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {Ultis} from '../utils/Utils';
import {differenceInCalendarDays, differenceInMinutes} from 'date-fns';
import {BorderSkin} from '../assets/skin/borders';
import {useSelector} from 'react-redux';
import {RootState} from '../redux/store/store';
import MultiSelect from '../component/select1/multi-select';
import {MapItem} from '../features/map/da';
import WDatePicker from '../component/date-picker/date-picker';

export const TextFieldForm = ({
  loading = false,
  label,
  labelStyle,
  secureTextEntry,
  textFieldStyle = {},
  returnKeyType = 'done',
  onSubmit,
  numberOfLines,
  register,
  placeholder,
  required = false,
  control,
  name,
  style = {},
  disabled = false,
  prefix,
  suffix,
  errors,
  multiline = false,
  type,
  autoFocus = false,
  onFocus,
  onBlur,
  maxLength,
  textStyle = {},
}: {
  loading?: boolean;
  secureTextEntry?: boolean;
  register?: any;
  label?: string;
  returnKeyType?: ReturnKeyTypeOptions | undefined;
  numberOfLines?: number;
  textFieldStyle?: ViewStyle;
  labelStyle?: TextStyle;
  placeholder?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  maxLength?: number;
  style?: TextStyle;
  textStyle?: TextStyle;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  multiline?: boolean;
  type?: KeyboardTypeOptions | 'money';
  autoFocus?: boolean;
  onFocus?: (value: string) => void;
  onBlur?: (value: string) => void;
  onSubmit?: (value: string) => void;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => (
        <View
          style={[
            {gap: 8, overflow: 'visible'},
            style ?? {backgroundColor: '#fff'},
          ]}>
          {label ? (
            <View style={{flexDirection: 'row', gap: 4, alignItems: 'center'}}>
              <Text
                numberOfLines={1}
                style={[TypoSkin.label3, {...labelStyle}]}>
                {label}
              </Text>
              {required ? (
                <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
              ) : null}
              {maxLength ? (
                <Text
                  style={[
                    TypoSkin.regular1,
                    {
                      flex: 1,
                      textAlign: 'right',
                      color: ColorSkin.textColorGrey1,
                    },
                  ]}>
                  {field.value?.length ?? 0}/{maxLength}
                </Text>
              ) : undefined}
            </View>
          ) : null}
          {loading ? (
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item
                height={40}
                width={'100%'}
                borderRadius={8}></SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
          ) : (
            <FTextField
              style={{
                ...textFieldStyle,
                width: '100%',
                paddingVertical: multiline ? 4 : 0,
                ...(type === 'money' ? {paddingRight: 0} : {}),
                ...textStyle,
              }}
              placeholder={
                placeholder
                  ? placeholder
                  : label
                    ? `Nhập ${label.toLowerCase()}`
                    : ''
              }
              value={field.value}
              numberOfLines={numberOfLines}
              onChange={field.onChange}
              onFocus={
                type === 'money'
                  ? () => {
                      if (field.value)
                        field.onChange(field.value.replaceAll(',', ''));
                      if (onFocus) onFocus(field.value);
                    }
                  : onFocus
                    ? () => {
                        onFocus(field.value);
                      }
                    : undefined
              }
              returnKeyType={returnKeyType}
              secureTextEntry={secureTextEntry}
              register={
                register ??
                (register(name, {
                  required: required,
                  onBlur:
                    type === 'money'
                      ? (ev: any) => {
                          let newPrice = ev.target.value
                            .trim()
                            .replaceAll(',', '');
                          ev.target.type = 'text';
                          if (!isNaN(parseFloat(newPrice))) {
                            ev.target.value = Ultis.money(parseFloat(newPrice));
                          } else {
                            ev.target.value = '';
                          }
                        }
                      : onBlur,
                  onChange: field.onChange,
                }) as any)
              }
              onBlur={
                type === 'money'
                  ? () => {
                      if (
                        field.value &&
                        isNaN(parseFloat(field.value?.replaceAll(',', '')))
                      ) {
                        field.onChange(0);
                      } else {
                        field.onChange(Ultis.money(field.value));
                      }
                      if (onBlur) onBlur(field.value);
                    }
                  : onBlur
                    ? () => {
                        onBlur(field.value);
                      }
                    : undefined
              }
              disabled={disabled}
              multiline={multiline}
              type={type === 'money' ? 'number-pad' : type}
              autoFocus={autoFocus}
              prefix={prefix}
              onSubmit={onSubmit}
              maxLength={maxLength}
              suffix={
                suffix ??
                (type === 'money' ? (
                  <View style={[styles.unitVND, {width: 40}]}>
                    <Text style={[TypoSkin.regular2]}>VNĐ</Text>
                  </View>
                ) : undefined)
              }
              helperText={
                convertErrors(errors, name) &&
                (convertErrors(errors, name)?.message?.length
                  ? convertErrors(errors, name)?.message
                  : `Vui lòng nhập ${(placeholder ? placeholder : label ? `${label}` : 'giá trị').toLowerCase()}`)
              }
            />
          )}
        </View>
      )}
    />
  );
};

export const FRadioForm = ({
  value,
  control,
  onChange,
  name,
  label,
  style,
}: {
  value: string;
  onChange?: (value: string) => void;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  label?: string;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({field}) => (
        <TouchableOpacity
          onPress={() => field.onChange(value)}
          style={[
            {
              flexDirection: 'row',
              alignSelf: 'baseline',
              gap: 4,
              alignItems: 'center',
            },
            style ?? {},
          ]}>
          <FRadioButton
            status={field.value === value}
            value={value}
            onPress={vl => {
              if (vl) field.onChange(value);
            }}
          />
          {label ? (
            <Text numberOfLines={1} style={TypoSkin.regular2}>
              {label}
            </Text>
          ) : null}
        </TouchableOpacity>
      )}
    />
  );
};

export const FDatePickerForm = ({
  loading = false,
  label,
  placeholder,
  required = false,
  control,
  name,
  style,
  disabled = false,
  errors,
  type,
  onChange,
}: {
  loading?: boolean;
  label?: string;
  placeholder?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  type?: 'date' | 'time' | 'datetime';
  onChange?: (e?: Date) => void;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => (
        <View style={[{gap: 8, overflow: 'visible'}, style ?? {}]}>
          {label ? (
            <View style={{flexDirection: 'row', gap: 4}}>
              <Text numberOfLines={1} style={TypoSkin.label3}>
                {label}
              </Text>
              {required ? (
                <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
              ) : null}
            </View>
          ) : null}
          {loading ? (
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item
                height={40}
                width={'100%'}
                borderRadius={8}></SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
          ) : (
            <WDatePicker
              style={{
                width: '100%',
                height: 40,
                paddingHorizontal: 16,
                flex: 1,
              }}
              placeholder={
                placeholder
                  ? placeholder
                  : label
                    ? `Nhập ${label.toLowerCase()}`
                    : ''
              }
              defaultValue={field.value}
              onChange={(vl: any) => {
                field.onChange(Ultis.datetoString(vl));
                if (onChange) onChange(vl);
              }}
              editable={false}
              disabled={disabled}
              type={type}
              helperText={
                convertErrors(errors, name) &&
                (convertErrors(errors, name)?.message?.length
                  ? convertErrors(errors, name)?.message
                  : `Vui lòng nhập ${(placeholder ? placeholder : label ? `${label}` : 'gía trị').toLowerCase()}`)
              }
            />
          )}
        </View>
      )}
    />
  );
};

export const FAddressPickerForm = ({
  loading = false,
  label,
  placeName,
  placeIcon,
  placeholder,
  required = false,
  control,
  name,
  style = {},
  disabled = false,
  errors,
  onChange,
  suggestList = [],
}: {
  loading?: boolean;
  label?: string;
  placeName?: string;
  placeIcon?: ReactNode;
  placeholder?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  onChange: (ev: MapItem) => any;
  suggestList?: Array<MapItem>;
}) => {
  const location = useSelector((state: RootState) => state.location);
  const dialogMapRef = useRef<any>();

  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => (
        <View
          style={[{gap: 8, overflow: 'visible', position: 'relative'}, style]}>
          <DialogSearchMap ref={dialogMapRef} />
          {label ? (
            <View style={{flexDirection: 'row', gap: 4}}>
              <Text numberOfLines={1} style={TypoSkin.label3}>
                {label}
              </Text>
              {required ? (
                <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
              ) : null}
            </View>
          ) : null}
          {loading ? (
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item
                height={40}
                width={'100%'}
                borderRadius={8}></SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
          ) : (
            <TouchableOpacity
              disabled={disabled}
              style={[
                BorderSkin.border1,
                {
                  backgroundColor: disabled ? '#F4F4F5' : '#fff',
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 16,
                  height: 48,
                  overflow: 'visible',
                  borderRadius: 8,
                  width: '100%',
                  borderColor: '#00358014',
                  ...style,
                },
              ]}
              onPress={() => {
                showDialogMap({
                  ref: dialogMapRef,
                  title: 'Tìm kiếm ' + (label?.toLocaleLowerCase() ?? ''),
                  onSubmit: value => {
                    const newValue = onChange(value);
                    field.onChange(newValue);
                  },
                  milestoneLocation: {
                    latitude: location?.latitude ?? 0,
                    longitude: location.longitude ?? 0,
                  },
                  suggestList: suggestList,
                });
              }}>
              {placeIcon ? (
                placeIcon
              ) : (
                <Text numberOfLines={1} style={TypoSkin.body2}>
                  {placeName ? `${placeName}: ` : ''}
                </Text>
              )}

              <Text
                numberOfLines={1}
                style={[
                  TypoSkin.body3,
                  {
                    flex: 1,
                    width: '100%',
                    opacity: field.value && field.value.trim() !== '' ? 1 : 0.8,
                    color:
                      field.value && field.value.trim() !== ''
                        ? '#00204D'
                        : ColorSkin.textColorGrey2,
                  },
                ]}>
                {field.value && field.value.trim() !== ''
                  ? field.value
                  : placeholder
                    ? placeholder
                    : label
                      ? `Chọn ${label.toLowerCase()}`
                      : ''}
              </Text>
              <View style={{width: 8}} />
              <FontAwesomeIcon
                icon={faMapLocationDot}
                size={16}
                color={ColorSkin.successColor}
              />
            </TouchableOpacity>
          )}
          {convertErrors(errors, name) ? (
            <Text
              numberOfLines={1}
              style={[
                TypoSkin.subtitle3,
                {
                  color: ColorSkin.errorColor,
                  position: 'absolute',
                  bottom: 0,
                  left: 2,
                  transform: [{translateY: 22}],
                },
              ]}>
              {convertErrors(errors, name)?.message?.length
                ? convertErrors(errors, name)?.message
                : `Vui lòng nhập ${(placeholder ? placeholder : label ? `${label}` : 'gía trị').toLowerCase()}`}
            </Text>
          ) : null}
        </View>
      )}
    />
  );
};

export const Fselect1Form = ({
  loading = false,
  label,
  placeholder,
  required = false,
  control,
  name,
  style = {},
  disabled = false,
  errors,
  options = [],
  onSearch,
  allowSearch = false,
  loadMore,
  onChange,
  onTap,
  prefix,
}: {
  loading?: boolean;
  label?: string;
  placeholder?: string;
  required?: boolean;
  onChange?: (vl: any) => void;
  onTap?: () => void;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  options: Array<{id: string | number; name: string | ReactNode}>;
  allowSearch?: boolean;
  onSearch?: (
    value?: string,
  ) => Promise<
    Array<{id: string | number; name: string | ReactNode}> | undefined
  >;
  loadMore?: (
    value?: string,
  ) => Promise<
    Array<{id: string | number; name: string | ReactNode}> | undefined
  >;
  prefix?: ReactNode;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => {
        return (
          <View style={[{gap: 8, overflow: 'visible'}, style]}>
            {label ? (
              <View style={{flexDirection: 'row', gap: 4}}>
                <Text numberOfLines={1} style={TypoSkin.label3}>
                  {label}
                </Text>
                {required ? (
                  <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
                ) : null}
              </View>
            ) : null}
            {loading ? (
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item
                  height={style.height ?? 40}
                  width={'100%'}
                  borderRadius={8}></SkeletonPlaceholder.Item>
              </SkeletonPlaceholder>
            ) : (
              <View style={{position: 'relative', width: '100%'}}>
                {prefix && (
                  <View
                    style={{
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      bottom: 0,
                      justifyContent: 'center',
                      alignItems: 'center',
                      zIndex: 1,
                    }}>
                    {prefix}
                  </View>
                )}
                <FSelect1
                  onTap={
                    disabled
                      ? undefined
                      : () => {
                          if (onTap) onTap();
                        }
                  }
                  style={{
                    width: '100%',
                    height: style.height ?? 40,
                    paddingLeft: prefix ? 52 : 12,
                  }}
                  placeholder={
                    placeholder
                      ? placeholder
                      : label
                        ? `Chọn ${label.toLowerCase()}`
                        : ''
                  }
                  value={field.value}
                  onChange={vl => {
                    field.onChange(vl.id);
                    if (onChange) onChange(vl);
                  }}
                  allowSearch={allowSearch}
                  onSearch={onSearch}
                  loadMore={loadMore}
                  data={options}
                  disabled={disabled}
                  helperText={
                    convertErrors(errors, name) &&
                    (convertErrors(errors, name)?.message?.length
                      ? convertErrors(errors, name)?.message
                      : `Vui lòng nhập ${(placeholder ? placeholder : label ? `Nhập ${label}` : 'gía trị').toLowerCase()}`)
                  }
                />
              </View>
            )}
          </View>
        );
      }}
    />
  );
};

export const FMultiSelectForm = ({
  loading = false,
  label,
  placeholder,
  required = false,
  control,
  name,
  style = {},
  disabled = false,
  errors,
  options = [],
  onSearch,
  loadMore,
}: {
  loading?: boolean;
  label?: string;
  placeholder?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  options: Array<{id: string | number; name: string | ReactNode}>;
  allowSearch?: boolean;
  onSearch?: (
    value?: string,
  ) => Promise<
    Array<{id: string | number; name: string | ReactNode}> | undefined
  >;
  loadMore?: (
    value?: string,
  ) => Promise<
    Array<{id: string | number; name: string | ReactNode}> | undefined
  >;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => (
        <View style={[{gap: 8, overflow: 'visible'}, style]}>
          {label ? (
            <View style={{flexDirection: 'row', gap: 4}}>
              <Text numberOfLines={1} style={TypoSkin.label3}>
                {label}
              </Text>
              {required ? (
                <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
              ) : null}
            </View>
          ) : null}
          {loading ? (
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item
                height={40}
                width={'100%'}
                borderRadius={8}></SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
          ) : (
            <MultiSelect
              style={{width: '100%'}}
              placeholder={
                placeholder
                  ? placeholder
                  : label
                    ? `Chọn ${label.toLowerCase()}`
                    : ''
              }
              value={field.value}
              onChange={item => {
                field.onChange(item);
              }}
              onSearch={onSearch}
              loadMore={loadMore}
              data={options}
              disabled={disabled}
              helperText={
                convertErrors(errors, name) &&
                (convertErrors(errors, name)?.message?.length
                  ? convertErrors(errors, name)?.message
                  : `Vui lòng nhập ${(placeholder ? placeholder : label ? `Nhập ${label}` : 'gía trị').toLowerCase()}`)
              }
            />
          )}
        </View>
      )}
    />
  );
};

export const FImportMultipleImg = ({
  loading = false,
  multipleImgs = true,
  linkImgs = [],
  label,
  required = false,
  control,
  name,
  style = {},
  errors,
  handlePickImg,
  pickerStyle = {},
}: {
  loading?: boolean;
  multipleImgs?: boolean;
  linkImgs: Array<string>;
  label?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  pickerStyle?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  handlePickImg: (value: Array<ImageOrVideo>) => Promise<any>;
}) => {
  async function pickerImg() {
    if (multipleImgs) {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        maxFiles: 5,
      });
      const res = await handlePickImg(img);
      return res;
    } else {
      const img = await ImageCropPicker.openPicker({
        multiple: false,
        cropping: false,
        maxFiles: 5,
      });
      const res = await handlePickImg([img]);
      return res;
    }
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => {
        return (
          <View style={[{overflow: 'visible', position: 'relative'}, style]}>
            {label ? (
              <View style={{flexDirection: 'row', gap: 4}}>
                <Text numberOfLines={1} style={TypoSkin.label3}>
                  {label}
                </Text>
                {required ? (
                  <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
                ) : null}
              </View>
            ) : null}
            {loading ? (
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item
                  height={pickerStyle.height ?? 104}
                  width={'100%'}
                  borderRadius={
                    pickerStyle.borderRadius ?? 8
                  }></SkeletonPlaceholder.Item>
              </SkeletonPlaceholder>
            ) : (
              <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{paddingVertical: 12}}
                data={linkImgs}
                keyExtractor={(item, i) => item + '-' + i}
                ItemSeparatorComponent={() => <View style={{width: 12}} />}
                ListHeaderComponent={() => {
                  if (multipleImgs == false && linkImgs.length != 0)
                    return null;
                  return (
                    <TouchableOpacity
                      onPress={async () => {
                        const res = await pickerImg();
                        field.onChange(res);
                      }}
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 8,
                        borderWidth: 0.4,
                        borderColor: ColorSkin.subtitle,
                        borderStyle: 'dashed',
                        borderRadius: 8,
                        height: 104,
                        width: 104,
                        marginTop: 12,
                        ...pickerStyle,
                      }}>
                      <OutlinePLus size={24} />
                      <Text
                        numberOfLines={1}
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorSkin.subtitle,
                        }}>
                        Thêm ảnh
                      </Text>
                    </TouchableOpacity>
                  );
                }}
                renderItem={({item, index}) => {
                  return (
                    <View
                      style={{
                        position: 'relative',
                        overflow: 'visible',
                        padding: multipleImgs ? 12 : 0,
                        paddingBottom: 0,
                      }}>
                      <Image
                        source={{uri: item}}
                        style={{
                          height: pickerStyle.height ?? 104,
                          minWidth: 104,
                          maxWidth: 132,
                          borderRadius: pickerStyle.borderRadius ?? 8,
                        }}
                      />
                      <TouchableOpacity
                        onPress={() => {
                          // let newValue = field.value.replace(item.replace(ConfigAPI.imgUrlId, ''), '')
                          // newValue = newValue.split(',').filter((e: string) => e.length > 0)
                          // field.onChange(newValue.join(','))
                        }}
                        style={{
                          padding: multipleImgs ? 4 : 0,
                          position: 'absolute',
                          top: 0,
                          right: 0,
                        }}>
                        <FontAwesomeIcon
                          icon={faMinusCircle}
                          size={20}
                          color="#667994"
                          style={{backgroundColor: '#fff', borderRadius: 20}}
                        />
                      </TouchableOpacity>
                    </View>
                  );
                }}
              />
            )}
            {errors[name] ? (
              <Text
                numberOfLines={1}
                style={[
                  TypoSkin.subtitle3,
                  {
                    color: ColorSkin.errorColor,
                    position: 'absolute',
                    bottom: 0,
                    left: 2,
                    transform: [{translateY: 22}],
                  },
                ]}>
                {convertErrors(errors, name)?.message?.length
                  ? convertErrors(errors, name)?.message
                  : `Vui lòng chọn ít nhất 1 ảnh`}
              </Text>
            ) : null}
          </View>
        );
      }}
    />
  );
};

/** type: number | date | datetime | money */
export function FRangeForm({
  loading = false,
  required = false,
  startName,
  endName,
  type = 'number',
  label,
  label1,
  label2,
  placeholderStart,
  placeholderEnd,
  style = {},
  disabled = false,
  errors,
  control,
  getValues,
}: {
  loading?: boolean;
  required?: boolean;
  startName: string;
  endName: string;
  type?: 'number' | 'date' | 'datetime' | 'money' | 'time';
  label?: string;
  label1?: string;
  label2?: string;
  placeholderStart?: string;
  placeholderEnd?: string;
  style?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  control: Control<FieldValues>;
  getValues: UseFormGetValues<any>;
}) {
  return (
    <View style={{gap: 8, width: '100%', overflow: 'visible', ...style}}>
      {label ? (
        <View style={{flexDirection: 'row', gap: 4}}>
          <Text numberOfLines={1} style={TypoSkin.label3}>
            {label}
          </Text>
          {required ? (
            <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
          ) : null}
        </View>
      ) : null}
      {label1 && label2 ? (
        <View
          style={{
            gap: 10,
            width: '100%',
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <View style={{flexDirection: 'row', gap: 4, flex: 1}}>
            <Text numberOfLines={1} style={TypoSkin.label3}>
              {label1}
            </Text>
            {required ? (
              <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
            ) : null}
          </View>
          <View style={{width: 16}} />
          <View style={{flexDirection: 'row', gap: 4, flex: 1}}>
            <Text numberOfLines={1} style={TypoSkin.label3}>
              {label2}
            </Text>
            {required ? (
              <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
            ) : null}
          </View>
        </View>
      ) : null}
      <View
        style={{
          gap: 10,
          width: '100%',
          flexDirection: 'row',
          alignItems: 'center',
          overflow: 'visible',
        }}>
        {loading ? (
          <View>
            <View style={{flex: 1}}>
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item
                  height={40}
                  width={'100%'}
                  borderRadius={8}
                />
              </SkeletonPlaceholder>
            </View>
            <FontAwesomeIcon icon={faArrowRight} size={16} color="#667994" />
            <View style={{flex: 1}}>
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item
                  height={40}
                  width={'100%'}
                  borderRadius={8}
                />
              </SkeletonPlaceholder>
            </View>
          </View>
        ) : type === 'number' || type === 'money' ? (
          <View>
            <Controller
              control={control}
              name={startName}
              rules={{required: required}}
              render={({field}) => {
                return (
                  <FTextField
                    style={{
                      width: '100%',
                      flex: 1,
                      height: 40,
                      paddingRight: type === 'money' ? 0 : undefined,
                    }}
                    placeholder={placeholderStart ?? 'Từ'}
                    value={field.value}
                    onChange={field.onChange}
                    onFocus={
                      type === 'money'
                        ? () => {
                            if (field.value)
                              field.onChange(field.value.replaceAll(',', ''));
                          }
                        : undefined
                    }
                    onBlur={() => {
                      const newValue = parseFloat(field.value);
                      if (!isNaN(newValue)) {
                        if (getValues(endName)?.length) {
                          const endValue = parseFloat(
                            getValues(endName).replaceAll(',', ''),
                          );
                          if (endValue < newValue) {
                            control.setError(startName, {
                              message:
                                'Giá trị bắt đầu phải nhỏ hơn giá trị kết thúc',
                            });
                          }
                        }
                        if (type === 'money')
                          field.onChange(Ultis.money(newValue));
                      } else {
                        field.onChange(undefined);
                      }
                    }}
                    disabled={disabled}
                    type="number-pad"
                    suffix={
                      type === 'money' ? (
                        <View style={[styles.unitVND, {width: 40}]}>
                          <Text style={[TypoSkin.regular1]}>VNĐ</Text>
                        </View>
                      ) : undefined
                    }
                    helperText={
                      (convertErrors(errors, startName) ||
                        convertErrors(errors, endName)) &&
                      (((convertErrors(errors, startName)?.message as string)
                        ?.length
                        ? convertErrors(errors, startName)?.message
                        : `Vui lòng nhập ${label?.toLowerCase() ?? 'giá trị'}`) as string)
                    }
                  />
                );
              }}
            />
            <FontAwesomeIcon icon={faArrowRight} size={16} color="#667994" />
            <Controller
              control={control}
              name={endName}
              rules={{required: required}}
              render={({field}) => {
                return (
                  <FTextField
                    style={{
                      width: '100%',
                      flex: 1,
                      height: 40,
                      paddingRight: type === 'money' ? 0 : undefined,
                    }}
                    placeholder={placeholderStart ?? 'Đến'}
                    value={field.value}
                    onChange={field.onChange}
                    onFocus={
                      type === 'money'
                        ? () => {
                            if (field.value)
                              field.onChange(field.value.replaceAll(',', ''));
                          }
                        : undefined
                    }
                    onBlur={() => {
                      const newValue = parseFloat(field.value);
                      if (!isNaN(newValue)) {
                        if (getValues(startName)?.length) {
                          const startValue = parseFloat(
                            getValues(startName).replace(/,/g, ''),
                          );
                          if (startValue > newValue) {
                            control.setError(startName, {
                              message:
                                'Giá trị bắt đầu phải nhỏ hơn giá trị kết thúc',
                            });
                          }
                        }
                        if (type === 'money')
                          field.onChange(Ultis.money(newValue));
                      } else {
                        field.onChange(undefined);
                      }
                    }}
                    disabled={disabled}
                    type="number-pad"
                    suffix={
                      type === 'money' ? (
                        <View style={[styles.unitVND, {width: 40}]}>
                          <Text style={[TypoSkin.regular1]}>VNĐ</Text>
                        </View>
                      ) : undefined
                    }
                    helperText={convertErrors(errors, endName) && ' '}
                  />
                );
              }}
            />
          </View>
        ) : (
          <View>
            <Controller
              name={startName}
              control={control}
              rules={{required: required}}
              render={({field}) => {
                return (
                  <WDatePicker
                    style={{
                      width: '100%',
                      flex: 1,
                      height: 40,
                      paddingHorizontal: 16,
                    }}
                    placeholder={placeholderStart ?? 'Từ'}
                    disabled={disabled}
                    defaultValue={field.value}
                    type={type}
                    // max={getValues(endName) ? new Date(parseInt(getValues(endName))) : undefined}
                    editable={false}
                    onChange={date => {
                      field.onChange(Ultis.datetoString(date));
                      // if (getValues(endName) && date) {
                      //     if (type === 'date' && differenceInCalendarDays(getValues(endName), date) < 0) {
                      //         control.setError(startName, { message: 'Thời điểm bắt đầu phải sớm hơn thời điểm kết thúc' })
                      //     } else if (differenceInMinutes(getValues(endName), date) < 0) {
                      //         control.setError(startName, { message: 'Giờ bắt đầu phải sớm hơn Giờ kết thúc' })
                      //     }
                      // }
                    }}
                    helperText={
                      (convertErrors(errors, startName) ||
                        convertErrors(errors, endName)) &&
                      (((convertErrors(errors, startName)?.message as string)
                        ?.length
                        ? convertErrors(errors, startName)?.message
                        : `Vui lòng nhập ${label?.toLowerCase() ?? 'giá trị'}`) as string)
                    }
                  />
                );
              }}
            />
            <Controller
              name={endName}
              control={control}
              rules={{required: required}}
              render={({field}) => {
                return (
                  <WDatePicker
                    style={{
                      width: '100%',
                      flex: 1,
                      height: 40,
                      paddingHorizontal: 16,
                    }}
                    placeholder={placeholderEnd ?? 'Đến'}
                    disabled={disabled}
                    editable={false}
                    defaultValue={field.value}
                    // min={getValues(startName) ? new Date(parseInt(getValues(startName))) : undefined}
                    type={type}
                    onChange={date => {
                      field.onChange(Ultis.datetoString(date));
                      // if (getValues(startName) && date) {
                      //     if (type === 'date' && differenceInCalendarDays(date, getValues(startName)) < 0) {
                      //         control.setError(startName, { message: 'Thời điểm bắt đầu phải sớm hơn thời điểm kết thúc' })
                      //     } else if (differenceInMinutes(date, getValues(startName)) < 0) {
                      //         control.setError(startName, { message: 'Giờ bắt đầu phải sớm hơn Giờ kết thúc' })
                      //     }
                      // }
                    }}
                    helperText={convertErrors(errors, endName) && ' '}
                  />
                );
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
}

export const convertErrors = (errors: any, name: string) => {
  if (errors && Object.keys(errors).length) {
    const props = name.split(/[.\[\]]/).filter(e => e?.length > 0);
    var value = errors;
    for (let p of props) {
      if (value) value = value[p];
    }
    return value;
  }
  return undefined;
};

const styles = StyleSheet.create({
  unitVND: {
    backgroundColor: ColorSkin.backgroundGrey1,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    width: 56,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
});
