// Order related types
export interface OrderItem {
  Id: string;
  Code: string;
  Value: number;
  DateCreated: number;
  Status: number;
  ShopId: string;
  CustomerId: string;
  AddressId: string;
  Shop?: ShopInfo;
  isRated?: boolean;
  ProductId?: string[];
}

export interface ShopInfo {
  Id: string;
  Name: string;
  Avatar?: string;
}

export interface CustomerInfo {
  Id: string;
  Name: string;
  Mobile: string;
  Email: string;
  AvatarUrl?: string;
}

export interface AddressInfo {
  Id: string;
  Address: string;
}

// Route and navigation types
export interface RouteParams {
  status: number;
}

export interface OrderCustomerDetailRouteProps {
  params: RouteParams;
}

// API response types
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  Shop?: ShopInfo[];
  [key: string]: any;
}

export interface PatternListOptions {
  page: number;
  size: number;
  query: string;
  pattern: {
    CustomerId: string[];
    ShopId: string[];
    AddressId: string[];
  };
}

// Component props types
export interface OrderListItemProps {
  item: OrderItem;
  status: number;
  onPress: (orderId: string) => void;
  onRatingPress?: (item: OrderItem) => void;
}

export interface OrderStatusBadgeProps {
  status: number;
}

export interface OrderInfoProps {
  isLoading: boolean;
  isSearching: boolean;
  dataLength: number;
  searchTerm: string;
}

export interface LoadingFooterProps {
  isVisible: boolean;
}

export interface SearchIndicatorProps {
  isVisible: boolean;
}

export interface RatingButtonProps {
  item: OrderItem;
  onPress: (item: OrderItem) => void;
  disabled?: boolean;
}

// Hook types
export interface UseOrderDataReturn {
  data: OrderItem[];
  isLoading: boolean;
  refreshing: boolean;
  loadingMore: boolean;
  hasMoreData: boolean;
  currentPage: number;
  getData: (status: number, page?: number, isRefresh?: boolean) => Promise<void>;
  loadMoreData: () => Promise<void>;
  onRefresh: () => Promise<void>;
}

export interface UseOrderSearchReturn {
  searchTerm: string;
  isSearching: boolean;
  setSearchTerm: (term: string) => void;
  searchWithAPI: (term: string) => Promise<void>;
}

export interface UsePaginationReturn {
  currentPage: number;
  hasMoreData: boolean;
  loadingMore: boolean;
  setCurrentPage: (page: number) => void;
  setHasMoreData: (hasMore: boolean) => void;
  setLoadingMore: (loading: boolean) => void;
}

// Constants
export const ORDER_STATUS = {
  NEW: 1,
  PROCESSING: 2,
  COMPLETED: 3,
  CANCELLED: 4,
} as const;

export const PAGE_SIZE = 10;

export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];
