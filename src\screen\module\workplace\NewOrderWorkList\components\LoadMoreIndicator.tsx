import React from 'react';
import {View, ActivityIndicator, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';

interface LoadMoreIndicatorProps {
  isLoading: boolean;
  hasMore: boolean;
  data: any[];
}

export const LoadMoreIndicator: React.FC<LoadMoreIndicatorProps> = ({
  isLoading,
  hasMore,
  data,
}) => {
  if (!hasMore && !isLoading && data.length > 0 && data.length > 10) {
    return (
      <View style={styles.container}>
        <Text style={styles.endText}><PERSON><PERSON> hiển thị tất cả kết quả</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
        <Text style={styles.loadingText}><PERSON><PERSON> tải thêm...</Text>
      </View>
    );
  }

  return null;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  loadingText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  endText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontStyle: 'italic',
  },
});
