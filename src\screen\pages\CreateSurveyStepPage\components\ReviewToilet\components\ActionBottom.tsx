import {View, StyleSheet, ViewStyle} from 'react-native';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {RootScreen} from '../../../../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {SurveyTaskDa} from '../../../../../module/surveyTask/surveyTaskDa';
import {showSnackbar} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';

interface ActionButtonsProps {
  style?: ViewStyle;
  surveyTaskId: string;
}

export default function ActionButtons({
  style,
  surveyTaskId,
}: ActionButtonsProps) {
  const navigation = useNavigation<any>();
  const surveyTaskDa = new SurveyTaskDa();

  const handleConfirm = async () => {
    try {
      // Update survey task status to 2
      await surveyTaskDa.updateSurveyTask({
        Id: surveyTaskId,
        Status: 2,
      });

      showSnackbar({
        message: 'Hoàn thành khảo sát thành công',
        status: ComponentStatus.SUCCSESS,
      });

      navigation.reset({
        index: 0,
        routes: [{name: RootScreen.navigateView}],
      });
    } catch (error) {
      console.error('Error updating survey task status:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi hoàn thành khảo sát',
        status: ComponentStatus.ERROR,
      });
    }
  };
  return (
    <View style={[styles.container, style]}>
      <AppButton
        title="Hoàn thành"
        textStyle={styles.continueButtonText}
        containerStyle={styles.continueButtonContainer}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor={ColorThemes.light.primary_main_color}
        onPress={handleConfirm}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 12,
    padding: 12,
    paddingBottom: 24,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  skipButtonText: {
    ...TypoSkin.buttonText1,
  },
  skipButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  continueButtonText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
  },
  continueButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
  },
});
