import type {ServiceType, ToiletItem} from '../types';
import {SERVICES_REQUIRING_TOILET_SELECTION, SERVICE_CONFIGS} from '../constants';

/**
 * Check if a service type requires toilet selection
 */
export const requiresToiletSelection = (serviceType: ServiceType): boolean => {
  return SERVICES_REQUIRING_TOILET_SELECTION.includes(serviceType);
};

/**
 * Get service configuration by type
 */
export const getServiceConfig = (serviceType: ServiceType) => {
  return SERVICE_CONFIGS[serviceType];
};

/**
 * Filter running toilets from toilet list
 */
export const getRunningToilets = (toilets: ToiletItem[], runningStatus: number): ToiletItem[] => {
  return toilets?.filter(toilet => toilet.Status === runningStatus) || [];
};

/**
 * Check if user has running toilets
 */
export const hasRunningToilets = (toilets: ToiletItem[], runningStatus: number): boolean => {
  return getRunningToilets(toilets, runningStatus).length > 0;
};

/**
 * Validate service type
 */
export const isValidServiceType = (type: string): type is ServiceType => {
  return ['create', 'repair', 'upgrade', 'clean', 'edu', 'contact'].includes(type);
};

/**
 * Get toilet selection title based on service type
 */
export const getToiletSelectionTitle = (serviceType: ServiceType): string => {
  switch (serviceType) {
    case 'repair':
      return 'Chọn nhà vệ sinh bạn muốn sửa chữa:';
    case 'upgrade':
      return 'Chọn nhà vệ sinh bạn muốn nâng cấp:';
    case 'clean':
      return 'Chọn nhà vệ sinh bạn muốn lau dọn:';
    default:
      return '';
  }
};
