import {useNavigation} from '@react-navigation/native';
import {Text, View} from 'react-native';
import ScreenHeader from './header';
import React from 'react';

import {Winicon} from '../../component/export-component';
import AppButton from '../../component/button';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';

interface Props {
  title?: string;
  children?: React.ReactNode;
  action?: React.ReactNode;
  bottom?: React.ReactNode;
  iconAction?: string;
  iconActionPress?: () => void;
  noAction?: boolean;
  prefix?: React.ReactNode;
}

export default function TitleWithBottom(props: Props) {
  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <ScreenHeader
        style={{paddingLeft: 16}}
        prefix={
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
            {props.prefix ? props.prefix : null}
            <Text
              style={{
                ...TypoSkin.heading5,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              {props.title ?? 'Header'}
            </Text>
          </View>
        }
        action={
          props.noAction ? null : (
            <View style={{flexDirection: 'row', gap: 8, paddingRight: 16}}>
              {props.action ? props.action : null}
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={props.iconActionPress}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 32,
                  width: 32,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}
                title={
                  <Winicon
                    src={props.iconAction ?? 'fill/user interface/bell'}
                    size={18}
                    color={ColorThemes.light.neutral_text_title_color}
                  />
                }
              />
            </View>
          )
        }
        bottom={
          <View style={{width: '100%'}}>
            {props.bottom ? props.bottom : null}
          </View>
        }
      />
      {props.children ? props.children : null}
    </SafeAreaView>
  );
}
