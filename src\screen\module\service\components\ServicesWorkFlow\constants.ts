import type {ServiceConfig, ServiceType} from './types';

export const DEFAULT_THUMB_URL =
  'https://file-mamager.wini.vn/Upload/2024/12/media1_7d48.png';

export const SERVICE_TYPES: Record<ServiceType, ServiceType> = {
  create: 'create',
  repair: 'repair',
  upgrade: 'upgrade',
  clean: 'clean',
  edu: 'edu',
  contact: 'contact',
  netzero: 'netzero',
} as const;

export const SERVICES_REQUIRING_TOILET_SELECTION: ServiceType[] = [
  'repair',
  'upgrade',
  'clean',
];

export const SERVICE_CONFIGS: Record<ServiceType, ServiceConfig | null> = {
  create: {
    title: 'Chia sẻ thông tin về nhà vệ sinh bạn mong muốn xây dựng',
    subtitle:
      'Trong bước này, chúng tôi sẽ hỏi xem bạn mong muốn xây nhà vệ sinh như nào. Từ đó chúng tôi có thể từ mong muốn của bạn để tư vấn thiết kế xây dựng phù hợp với mong muốn, chi phí của bạn',
    submitTitle: 'Tự xây NVS',
    placeholder: 'Mô tả thông tin của bạn',
    thumbUrl: DEFAULT_THUMB_URL,
    type: 'CreateFlow',
    requiresToiletSelection: false,
  },
  repair: {
    title: 'Cung cấp thông tin vấn đề cần sửa chữa, nâng cấp',
    subtitle:
      'Thông tin càng chi tiết giúp quá trình tiếp nhận và sửa chữa nhanh chóng hơn.',
    submitTitle: 'Thêm thông tin',
    placeholder: 'Mô tả thông tin vấn đề cần sửa chữa',
    thumbUrl: DEFAULT_THUMB_URL,
    type: 'RepairFlow',
    requiresToiletSelection: true,
  },
  upgrade: {
    title: 'Cung cấp thông tin nhà vệ sinh cần nâng cấp',
    subtitle:
      'Thông tin càng chi tiết giúp quá trình tiếp nhận và nâng cấp nhanh chóng hơn.',
    submitTitle: 'Thêm thông tin',
    placeholder: 'Mô tả thông tin vấn đề cần nâng cấp',
    thumbUrl: DEFAULT_THUMB_URL,
    type: 'RepairFlow',
    requiresToiletSelection: true,
  },
  clean: {
    title: 'Cung cấp thông tin nhà vệ sinh cần lau dọn',
    subtitle: 'Giúp chúng tôi hiểu rõ hơn về vấn đề của bạn',
    submitTitle: 'Gửi thông tin',
    placeholder: 'Mô tả thông tin vấn đề cần lau dọn',
    thumbUrl: DEFAULT_THUMB_URL,
    type: 'CleanFlow',
    requiresToiletSelection: true,
  },
  edu: {
    title: 'Giáo dục',
    subtitle: 'Nội dung giáo dục',
    submitTitle: 'Xem nội dung',
    placeholder: '',
    thumbUrl: DEFAULT_THUMB_URL,
    type: 'EduFlow',
    requiresToiletSelection: false,
  },
  contact: {
    title: 'Chia sẻ thông tin về vấn đề bạn gặp phải',
    subtitle: '',
    submitTitle: 'Gửi thông tin',
    placeholder: 'Mô tả vấn đề của bạn',
    thumbUrl: DEFAULT_THUMB_URL,
    type: 'ContactFlow',
    requiresToiletSelection: false,
  },
  netzero: null,
};

export const TOILET_SELECTION_TITLES: Record<ServiceType, string> = {
  repair: 'Chọn nhà vệ sinh bạn muốn sửa chữa:',
  upgrade: 'Chọn nhà vệ sinh bạn muốn nâng cấp:',
  clean: 'Chọn nhà vệ sinh bạn muốn lau dọn:',
  create: '',
  edu: '',
  contact: '',
  netzero: '',
};

export const EDU_FLOW_URL = 'https://ebig.co/Edu';

export const TABS = {
  TOILET_SELECTION: 0,
  SERVICE_FORM: 1,
} as const;

export const POPUP_STYLES = {
  shadowColor: 'rgba(0, 0, 0, 0.03)',
  shadowOffset: {
    width: 0,
    height: 4,
  },
  shadowRadius: 20,
  elevation: 20,
  shadowOpacity: 1,
} as const;
