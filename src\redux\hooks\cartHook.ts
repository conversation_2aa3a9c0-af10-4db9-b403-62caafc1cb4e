import {useSelector, useDispatch} from 'react-redux';
import {AppDispatch, RootState} from '../store/store';
import {ComponentStatus} from '../../component/component-status';
import {showSnackbar} from '../../component/export-component';
import {DataController} from '../../screen/base-controller';
import {
  clearCart,
  removeFromCart,
  removeItemsByIds,
  selectStoreItems,
  toggleSelectItem,
  updateQuantity,
} from '../reducers/cart/CartReducer';
import {CartItem} from '../../types/cartTypes';

// Hook để lấy state của giỏ hàng
export const useCartState = () => useSelector((state: RootState) => state.cart);

// Hook để lấy các actions của giỏ hàng
export const useCartActions = () => {
  // const dispatch = useDispatch();
  const dispatch: AppDispatch = useDispatch();
  const cartState = useCartState();

  return {
    // Cập nhật số lượng sản phẩm
    updateItemQuantity: async (id: string, quantity: number) => {
      // Tìm sản phẩm trong giỏ hàng
      const cartItem = cartState.items.find(item => item.id === id);
      if (!cartItem) {
        showSnackbar({
          message: 'Không tìm thấy sản phẩm trong giỏ hàng',
          status: ComponentStatus.ERROR,
        });
        return;
      }

      try {
        // Gọi API để lấy thông tin tồn kho
        const productController = new DataController('Product');
        const productResponse = await productController.getListSimple({
          query: `@Id:{${cartItem.ProductId}}`,
        });

        if (
          productResponse?.code === 200 &&
          productResponse?.data?.length > 0
        ) {
          const product = productResponse.data[0];
          const currentStock = product?.InStock || 0;

          // Kiểm tra nếu sản phẩm hết hàng
          if (currentStock <= 0) {
            showSnackbar({
              message: `Sản phẩm "${cartItem.Name}" đã hết hàng. Không thể cập nhật số lượng.`,
              status: ComponentStatus.ERROR,
            });
            return;
          }

          // Kiểm tra nếu số lượng muốn cập nhật vượt quá tồn kho
          if (quantity > currentStock) {
            showSnackbar({
              message: `Sản phẩm "${cartItem.Name}" chỉ còn ${currentStock} sản phẩm trong kho. Không thể tăng số lượng lên ${quantity}.`,
              status: ComponentStatus.ERROR,
            });
            return;
          }

          // Nếu hợp lệ, thực hiện cập nhật
          dispatch(updateQuantity({id, quantity}));
        } else {
          showSnackbar({
            message: 'Không tìm thấy thông tin sản phẩm',
            status: ComponentStatus.ERROR,
          });
        }
      } catch (error) {
        console.error('Error checking product stock:', error);
        showSnackbar({
          message: 'Có lỗi xảy ra khi kiểm tra tồn kho sản phẩm',
          status: ComponentStatus.ERROR,
        });
      }
    },

    // Xóa sản phẩm khỏi giỏ hàng
    removeItem: (id: string) => {
      dispatch(removeFromCart(id));
    },

    // Chọn/bỏ chọn sản phẩm
    toggleItemSelection: (id: string) => {
      dispatch(toggleSelectItem(id));
    },

    // Chọn/bỏ chọn tất cả sản phẩm của một cửa hàng
    toggleStoreSelection: (storeId: string, selected: boolean) => {
      dispatch(selectStoreItems({storeId, selected}));
    },

    // Xóa các sản phẩm theo danh sách ID
    removeItemsById: (ids: string[]) => {
      dispatch(removeItemsByIds(ids));
    },

    // Xóa tất cả sản phẩm trong giỏ hàng
    clearAllItems: () => {
      dispatch(clearCart());
    },

    // Tính tổng tiền của các sản phẩm đã chọn
    calculateSelectedTotal: (items: CartItem[]) => {
      return items
        .filter(item => item.selected)
        .reduce(
          (total, item) =>
            total +
            item.Price * item.Quantity * (1 - (item.Discount ?? 0) / 100),
          0,
        );
    },

    // Kiểm tra xem có sản phẩm nào được chọn không
    hasSelectedItems: (items: CartItem[]) => {
      return items.some(item => item.selected);
    },

    // Nhóm các sản phẩm theo cửa hàng
    groupItemsByStore: (items: CartItem[]) => {
      const storeMap = new Map<
        string,
        {
          ShopId: string;
          ShopName: string;
          ShopAvatar: string;
          items: CartItem[];
        }
      >();

      items.forEach(item => {
        if (!storeMap.has(item.ShopId)) {
          storeMap.set(item.ShopId, {
            ShopId: item.ShopId,
            ShopName: item.ShopName,
            ShopAvatar: item.ShopAvatar,
            items: [],
          });
        }

        storeMap.get(item.ShopId)?.items.push(item);
      });

      return Array.from(storeMap.values());
    },
  };
};
