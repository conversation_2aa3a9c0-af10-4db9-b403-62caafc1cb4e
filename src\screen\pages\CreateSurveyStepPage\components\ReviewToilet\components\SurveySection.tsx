import React from 'react';
import {Text, View, StyleSheet} from 'react-native';
import {SurveySectionProps} from '../types';
import SurveyItemComponent from './SurveyItemComponent';

const SurveySection: React.FC<SurveySectionProps> = ({
  title,
  items,
  checkedItems,
  onToggleCheck,
  disabled = false,
}) => {
  return (
    <View style={styles.surveySection}>
      <Text style={[styles.sectionTitle, disabled && styles.disabledTitle]}>
        {title}
      </Text>

      {items.map(item => (
        <SurveyItemComponent
          key={item.Id}
          item={item}
          isChecked={checkedItems.includes(item.Id)}
          onToggle={onToggleCheck}
          disabled={disabled}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  surveySection: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c5530',
  },
  disabledTitle: {
    color: '#999',
    opacity: 0.6,
  },
});

export default SurveySection;
