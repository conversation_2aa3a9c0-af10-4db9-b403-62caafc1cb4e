import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';

const SpacingDemo = () => {
  const demoItems = [
    'Item 1 - Detail Field',
    'Item 2 - Price Field', 
    'Item 3 - Description Field',
    'Item 4 - Source Field',
    'Item 5 - Preserve Field',
    'Item 6 - Guarantee Field',
    'Item 7 - Specifications Field',
    'Item 8 - InStock Field',
    'Item 9 - Unit Field',
    'Item 10 - Color Field',
    'Item 11 - Brand Field',
    'Item 12 - Consume Field',
    'Item 13 - FreeShip Field',
  ];

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Demo Khoảng Cách Đều Giữa Các Item</Text>
      
      {demoItems.map((item, index) => (
        <View key={index} style={styles.option}>
          <Text style={styles.itemText}>{item}</Text>
          <View style={styles.placeholder}>
            <Text style={styles.placeholderText}>Content</Text>
          </View>
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.neutral_text_title_color,
  },
  option: {
    marginVertical: 6, // Khoảng cách đều trên và dưới
    marginHorizontal: 10,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    minHeight: 60,
    justifyContent: 'center',
  },
  itemText: {
    fontSize: 14,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
  },
  placeholder: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 8,
    borderRadius: 5,
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default SpacingDemo;
