import React, {useState, useEffect, useRef} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Controller, FieldValues} from 'react-hook-form';
import DatePicker from 'react-native-date-picker';
import {Ultis} from '../../../utils/Utils';
import {DateRangePickerProps} from './DatePickerTypes';
import {datePickerStyles, datePickerColors} from './DatePickerStyles';
import {CustomBottomSheet} from './CustomBottomSheet';

export const DateRangePicker = <T extends FieldValues = FieldValues>({
  control,
  startDateName,
  endDateName,
  errors,
  placeholder = 'Chọn khoảng thời gian',
  disabled = false,
  required = false,
  style = {},
  textStyle = {},
  icon,
  prefix,
  minDate,
  maxDate,
  onDateRangeChange,
  useTimestamp = false,
  onTimestampRangeChange,
}: DateRangePickerProps<T>) => {
  const [tempStartDate, setTempStartDate] = useState<Date>(new Date());
  const [tempEndDate, setTempEndDate] = useState<Date>(new Date());
  const [currentPicker, setCurrentPicker] = useState<'start' | 'end'>('start');
  const [isBottomSheetVisible, setIsBottomSheetVisible] =
    useState<boolean>(false);
  const onConfirmCallbackRef = useRef<
    ((startDate: Date, endDate: Date) => void) | null
  >(null);

  // Utility functions for timestamp conversion
  const timestampToDate = (timestamp?: number): Date | undefined => {
    if (!timestamp) return undefined;
    return new Date(timestamp);
  };

  const dateToTimestamp = (date?: Date): number | undefined => {
    if (!date) return undefined;
    return date.getTime();
  };

  // Debug: Log khi currentPicker thay đổi
  console.log('DateRangePicker render, currentPicker:', currentPicker);

  useEffect(() => {
    console.log('currentPicker changed to:', currentPicker);
  }, [currentPicker]);

  const formatDateRange = (startDate?: Date, endDate?: Date): string => {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
      return `${Ultis.datetoString(startDate, 'dd/MM/yyyy')} - ${Ultis.datetoString(endDate, 'dd/MM/yyyy')}`;
    }
    if (startDate) {
      return Ultis.datetoString(startDate, 'dd/MM/yyyy');
    }
    return '';
  };

  const validateAndFixDateRange = (start: Date, end: Date) => {
    if (start > end) {
      return {startDate: end, endDate: start};
    }
    return {startDate: start, endDate: end};
  };

  const showDateRangePicker = (
    startValue?: Date,
    endValue?: Date,
    onConfirm?: (startDate: Date, endDate: Date) => void,
  ) => {
    setTempStartDate(startValue || new Date());
    setTempEndDate(endValue || new Date());
    onConfirmCallbackRef.current = onConfirm || null;
    setIsBottomSheetVisible(true);
  };

  const handleBottomSheetClose = () => {
    setIsBottomSheetVisible(false);
    onConfirmCallbackRef.current = null;
  };

  const handleConfirm = () => {
    const {startDate, endDate} = validateAndFixDateRange(
      tempStartDate,
      tempEndDate,
    );
    onConfirmCallbackRef.current?.(startDate, endDate);
    onDateRangeChange?.(startDate, endDate);

    // Call timestamp callback if using timestamp format
    if (useTimestamp && onTimestampRangeChange) {
      const startTimestamp = dateToTimestamp(startDate);
      const endTimestamp = dateToTimestamp(endDate);
      onTimestampRangeChange(startTimestamp, endTimestamp);
    }
    handleBottomSheetClose();
  };

  const renderBottomSheetContent = () => {
    return (
      <View style={datePickerStyles.bottomSheetContent}>
        <View style={datePickerStyles.tabContainer}>
          <TouchableOpacity
            style={[
              datePickerStyles.tab,
              currentPicker === 'start' && datePickerStyles.activeTab,
            ]}
            onPress={() => {
              console.log('Start tab pressed, current picker:', currentPicker);
              setCurrentPicker('start');
              console.log('After setting to start');
            }}
            activeOpacity={0.7}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Text
              style={[
                datePickerStyles.tabText,
                currentPicker === 'start' && datePickerStyles.activeTabText,
              ]}>
              Ngày bắt đầu
            </Text>
            <Text
              style={[
                datePickerStyles.tabDate,
                currentPicker === 'start' && datePickerStyles.activeTabDate,
              ]}>
              {Ultis.datetoString(tempStartDate, 'dd/MM/yyyy')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              datePickerStyles.tab,
              currentPicker === 'end' && datePickerStyles.activeTab,
            ]}
            onPress={() => {
              console.log('End tab pressed, current picker:', currentPicker);
              setCurrentPicker('end');
              console.log('After setting to end');
            }}
            activeOpacity={0.7}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Text
              style={[
                datePickerStyles.tabText,
                currentPicker === 'end' && datePickerStyles.activeTabText,
              ]}>
              Ngày kết thúc
            </Text>
            <Text
              style={[
                datePickerStyles.tabDate,
                currentPicker === 'end' && datePickerStyles.activeTabDate,
              ]}>
              {Ultis.datetoString(tempEndDate, 'dd/MM/yyyy')}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={datePickerStyles.pickerContainer}>
          <DatePicker
            date={currentPicker === 'start' ? tempStartDate : tempEndDate}
            mode="date"
            style={datePickerStyles.datePicker}
            minimumDate={minDate}
            maximumDate={maxDate}
            dividerColor={'#f2f5f8'}
            theme="auto"
            onDateChange={date => {
              console.log('Date changed:', date, 'for picker:', currentPicker);
              if (currentPicker === 'start') {
                setTempStartDate(date);
              } else {
                setTempEndDate(date);
              }
            }}
          />
        </View>
      </View>
    );
  };

  return (
    <>
      <CustomBottomSheet
        visible={isBottomSheetVisible}
        onClose={handleBottomSheetClose}
        title="Chọn khoảng thời gian"
        onCancel={handleBottomSheetClose}
        onConfirm={() => handleConfirm()}
        cancelText="Hủy"
        confirmText="Xác nhận">
        {renderBottomSheetContent()}
      </CustomBottomSheet>

      <Controller
        control={control}
        name={startDateName}
        rules={{required}}
        render={({field: startField}) => (
          <Controller
            control={control}
            name={endDateName}
            rules={{required}}
            render={({field: endField}) => {
              // Convert timestamp to Date for display if using timestamp format
              const startDate = useTimestamp
                ? timestampToDate(startField.value)
                : startField.value;
              const endDate = useTimestamp
                ? timestampToDate(endField.value)
                : endField.value;

              const displayValue = formatDateRange(startDate, endDate);

              return (
                <View style={[datePickerStyles.container, style]}>
                  <TouchableOpacity
                    style={[datePickerStyles.inputWrapper]}
                    onPress={() => {
                      if (!disabled) {
                        showDateRangePicker(
                          startDate,
                          endDate,
                          (selectedStartDate, selectedEndDate) => {
                            if (useTimestamp) {
                              // Convert Date to timestamp before saving
                              startField.onChange(
                                dateToTimestamp(selectedStartDate),
                              );
                              endField.onChange(
                                dateToTimestamp(selectedEndDate),
                              );
                            } else {
                              // Save as Date object
                              startField.onChange(selectedStartDate);
                              endField.onChange(selectedEndDate);
                            }
                          },
                        );
                      }
                    }}
                    disabled={disabled}>
                    {(prefix || icon) && (
                      <View style={[datePickerStyles.iconContainer]}>
                        {prefix || icon}
                      </View>
                    )}

                    <Text
                      style={[
                        datePickerStyles.displayText,
                        !displayValue && datePickerStyles.placeholderText,
                        textStyle,
                      ]}>
                      {displayValue || placeholder}
                    </Text>
                  </TouchableOpacity>

                  {(errors[startDateName] || errors[endDateName]) && (
                    <Text style={datePickerStyles.errorText}>
                      {(errors[startDateName]?.message as string) ||
                        (errors[endDateName]?.message as string) ||
                        'Vui lòng chọn khoảng thời gian'}
                    </Text>
                  )}
                </View>
              );
            }}
          />
        )}
      />
    </>
  );
};
