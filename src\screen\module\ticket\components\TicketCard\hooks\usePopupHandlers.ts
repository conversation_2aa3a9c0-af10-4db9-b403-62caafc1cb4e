import {useRef} from 'react';
import {showPopup, closePopup} from 'wini-mobile-components';

export interface PopupHandlersHookReturn {
  popupRef: React.RefObject<any>;
  showViewPopup: (content: React.ReactNode) => void;
  showEditPopup: (content: React.ReactNode) => void;
  showFilePreviewPopup: (content: React.ReactNode) => void;
  closeCurrentPopup: () => void;
}

export const usePopupHandlers = (): PopupHandlersHookReturn => {
  const popupRef = useRef<any>();

  const showViewPopup = (content: React.ReactNode) => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: content,
    });
  };

  const showEditPopup = (content: React.ReactNode) => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: content,
    });
  };

  const showFilePreviewPopup = (content: React.ReactNode) => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: content,
    });
  };

  const closeCurrentPopup = () => {
    if (popupRef && popupRef.current) {
      closePopup(popupRef);
    }
  };

  return {
    popupRef,
    showViewPopup,
    showEditPopup,
    showFilePreviewPopup,
    closeCurrentPopup,
  };
};
