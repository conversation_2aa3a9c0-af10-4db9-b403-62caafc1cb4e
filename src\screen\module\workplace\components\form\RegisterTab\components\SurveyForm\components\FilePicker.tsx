import React from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import {ColorThemes} from '../../../../../../../../../assets/skin/colors';
import {Winicon} from '../../../../../../../../../component/export-component';
import ScreenHeader from '../../../../../../../../layout/header';
import ListTile from '../../../../../../../../../component/list-tile/list-tile';
import {closePopup} from '../../../../../../../../../component/popup/popup';

interface FilePickerProps {
  onPickImage: () => void;
  onPickFile: () => void;
  popupRef: any;
}

export const FilePicker: React.FC<FilePickerProps> = ({
  onPickImage,
  onPickFile,
  popupRef,
}) => {
  return (
    <View style={styles.filePickerContainer}>
      <ScreenHeader
        style={styles.filePickerHeader}
        title={`Thêm mới`}
        prefix={<View />}
        action={
          <View style={styles.closeButtonContainer}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(popupRef)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <ListTile onPress={onPickImage} title={'Thêm ảnh, video'} />
      <ListTile onPress={onPickFile} title={'Thêm tệp'} />
    </View>
  );
};

const styles = StyleSheet.create({
  filePickerContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height / 3,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  filePickerHeader: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButtonContainer: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
});
