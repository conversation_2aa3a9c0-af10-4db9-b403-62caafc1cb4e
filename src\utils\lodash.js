/**
 * Hàm get giống như lodash.get
 * Lấy giá trị tại path của object. Nếu giá trị không tồn tại, trả về defaultValue
 * 
 * @param {Object|null} object - Object cần lấy giá trị
 * @param {string|Array} path - Path đến giá trị cần lấy
 * @param {*} defaultValue - Giá trị mặc định nếu không tìm thấy
 * @returns {*} Giá trị tại path hoặc defaultValue
 */
function get(object, path, defaultValue = undefined) {
  // Kiểm tra nếu object là null hoặc undefined
  if (object == null) {
    return defaultValue;
  }

  // Chuyển path thành array nếu là string
  let pathArray;
  if (typeof path === 'string') {
    // Xử lý các trường hợp path như 'a.b.c', 'a[0].b', 'a["key"].b'
    pathArray = path
      .replace(/\[(\w+)\]/g, '.$1') // Chuyển [key] thành .key
      .replace(/^\./, '') // Loại bỏ dấu . đầu tiên
      .split('.');
  } else if (Array.isArray(path)) {
    pathArray = path;
  } else {
    return defaultValue;
  }

  // Duyệt qua từng key trong path
  let result = object;
  for (let i = 0; i < pathArray.length; i++) {
    const key = pathArray[i];
    
    // Kiểm tra nếu key tồn tại trong object hiện tại
    if (result == null || typeof result !== 'object' || !(key in result)) {
      return defaultValue;
    }
    
    result = result[key];
  }

  return result;
}

export { get };
