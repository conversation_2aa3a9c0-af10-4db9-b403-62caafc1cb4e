import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useState,
} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {Radio, RadioAction} from '../../../../../component/Field/Radio';
import FastImage from 'react-native-fast-image';
import {AppSvg, Winicon} from 'wini-mobile-components';
import iconSvg from '../../../../../svgs/iconSvg';
import {FTextField} from '../../../../../component/export-component';
import ProductDA from '../../productDA';
import {getImage} from '../../../../../redux/actions/rootAction';
const {height: screenHeight} = Dimensions.get('window');

interface BottomSheetProps {
  title?: string;
  onSelect?: (item: any) => void;
  renderItem?: (item: any, index: number) => React.ReactNode;
  children?: React.ReactNode;
  value?: any;
  setGetInputValue?: any;
  attributeData?: any[];
  setEditAttributr?: any;
}

export interface BottomSheetRef {
  show: () => void;
  hide: () => void;
}

const BottomSheetComponent = forwardRef<BottomSheetRef, BottomSheetProps>(
  (
    {
      title = 'Chọn mục',
      onSelect,
      renderItem,
      children,
      value,
      setGetInputValue,
      attributeData,
      setEditAttributr,
    },
    ref,
  ) => {
    const [visible, setVisible] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [dataParent, setDataParent] = useState<any[]>([]);
    const [dataChild, setDataChild] = useState<any[]>([]);
    const [selectParentId, setSelectParentId] = useState('');
    const [selectChildren, setselectChildren] = useState('');
    const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>(
      {},
    );
    const [arrayDataListAttribute, setArrayDataListAttribute] = useState<any[]>(
      [],
    );
    const [inputValues, setInputValues] = useState<{[key: string]: string}>({});
    const [allAttribute, setAllAttribute] = useState<any[]>([]);

    useImperativeHandle(ref, () => ({
      show: () => setVisible(true),
      hide: () => setVisible(false),
    }));

    const getAllAttribute = async (checkedItems: any) => {
      let res = await ProductDA.getAllAttributeInStore();
      if (res?.code === 200) {
        const filteredAttributes = res.data.filter(
          (item: any) => checkedItems[item.CateAttributeId],
        );
        setAllAttribute(filteredAttributes);
      }
    };

    useEffect(() => {
      if (title === 'nhóm thuộc tính' && allAttribute.length > 0) {
        const resultWithInputs = allAttribute.map(item => ({
          ...item,
          inputValue:
            item.CateAttributeId === inputValues[item.CateAttributeId]
              ? inputValues[item.CateAttributeId]
              : item.Value,
        }));
        if (resultWithInputs.length > 0) {
          console.log('Changed/New items:', resultWithInputs, allAttribute);
          setEditAttributr(resultWithInputs);
        }
      }
    }, [inputValues, allAttribute]);

    // Hàm trở về data cha
    const handleBackToParent = () => {
      if (selectParentId) {
        // Reset về trạng thái ban đầu - hiển thị data cha
        setSelectParentId('');
        setDataChild([]);
        setselectChildren('');
      }
    };

    const handleGetdata = async (title: string) => {
      let res;
      if (title == 'danh mục sản phẩm') res = await ProductDA.GetAllCate();
      else if (title == 'nhóm thuộc tính')
        res = await ProductDA.getAllCateAttribute();
      else if (title == 'thuộc tính') res = await ProductDA.getAllAttribute();
      else if (title == 'thương hiệu') res = await ProductDA.getAllBrands();
      else if (title == 'tiêu thụ') res = await ProductDA.getAllConsume();
      else if ((title = 'màu sắc')) res = await ProductDA.getAllColor();
      if (res?.code === 200) {
        // Lọc ra các danh mục không có parentId (danh mục gốc)
        const rootCategories = res.data.filter(
          (item: any) =>
            !item.ParentId || item.ParentId === null || item.ParentId === 0,
        );
        let data = await getImage({items: rootCategories});
        setDataParent(data);
      } else {
        setDataParent([]);
      }
    };
    const cancel = () => {
      setSelectParentId('');
      setselectChildren('');
      setDataChild([]);
      setCheckedItems({});
      setArrayDataListAttribute([]);
      setInputValues({});
      setVisible(false);
    };
    useEffect(() => {
      if (title == 'phân loại') {
        setDataParent([
          {Id: '1', Name: 'Chế phẩm sinh học'},
          {Id: '2', Name: 'Thiết bị'},
        ]);
      } else if (title == 'nguồn gốc') {
        setDataParent([
          {Id: '1', Name: 'Nhập khẩu'},
          {Id: '2', Name: 'Nội địa'},
        ]);
      } else {
        handleGetdata(title);
      }
    }, [title, visible]);

    // useEffect để xử lý giá trị value được truyền vào
    useEffect(() => {
      if (value && dataParent.length > 0) {
        if (title === 'nhóm thuộc tính') {
          // Đối với nhóm thuộc tính, value có thể là string các ID cách nhau bởi dấu phẩy
          const valueIds = value
            .toString()
            .split(',')
            .map((id: string) => id.trim());
          const newCheckedItems: {[key: string]: boolean} = {};
          const selectedItems: any[] = [];

          valueIds.forEach((id: string) => {
            if (id) {
              newCheckedItems[id] = true;
              // Tìm item tương ứng trong dataParent
              const foundItem = dataParent.find(item => item.Id === id);
              if (foundItem) {
                selectedItems.push(foundItem);
              }
            }
          });

          setCheckedItems(newCheckedItems);
          getAllAttribute(newCheckedItems);
          setArrayDataListAttribute(selectedItems);
        } else if (title === 'danh mục sản phẩm') {
          // Tìm kiếm item có tên trùng với value trong danh mục sản phẩm
          const searchItemByName = async (searchValue: string) => {
            try {
              // Lấy tất cả danh mục từ API
              const res = await ProductDA.GetAllCate();
              if (res?.code === 200) {
                const allCategories = res.data;

                // Tìm item có tên trùng với value (không phân biệt hoa thường)
                const foundItem = allCategories.find(
                  (item: any) =>
                    item.Name?.toLowerCase() === searchValue.toLowerCase(),
                );

                if (foundItem) {
                  // Kiểm tra xem item này có ParentId không
                  if (
                    foundItem.ParentId &&
                    foundItem.ParentId !== null &&
                    foundItem.ParentId !== 0
                  ) {
                    // Đây là item con, cần tìm parent và load dữ liệu child
                    const parentId = foundItem.ParentId.toString();

                    // Set parentId
                    setSelectParentId(parentId);

                    // Lấy dữ liệu child theo parentId
                    const childCategories = allCategories.filter(
                      (childItem: any) =>
                        childItem.ParentId?.toString() === parentId,
                    );

                    if (childCategories.length > 0) {
                      // Lấy ảnh cho các danh mục con
                      let dataWithImages = await getImage({
                        items: childCategories,
                      });
                      setDataChild(dataWithImages);
                      console.log('Found item with name:', inputValues);

                      // Set checkbox cho item có tên trùng với value
                      setTimeout(() => {
                        setselectChildren(foundItem.Id);
                      }, 100);
                    }
                  } else {
                    setSelectParentId(foundItem.Id);
                    setselectChildren(foundItem.Id);
                  }
                } else {
                  console.log('No item found with name:', searchValue);
                }
              }
            } catch (error) {
              console.error('Error searching for item by name:', error);
            }
          };

          // Gọi hàm tìm kiếm với value
          if (value) {
            searchItemByName(value.toString());
          }
        } else if (
          title === 'phân loại' ||
          title === 'nguồn gốc' ||
          title === 'thương hiệu' ||
          title === 'tiêu thụ' ||
          title === 'màu sắc' ||
          title === 'nhóm thuộc tính'
        ) {
          // Đối với các title khác, value là một ID duy nhất
          const valueId = value.toString();
          const foundItem = dataParent.find(item => item.Id === valueId);
          if (foundItem) {
            setSelectParentId(valueId);
            setselectChildren(valueId);
          }
        }
      }
    }, [value, dataParent, title]);

    // useEffect để load allAttribute khi có attributeData
    useEffect(() => {
      if (attributeData && attributeData.length > 0) {
        setAllAttribute(attributeData);
      }
    }, [attributeData]);

    // Không cần useEffect này vì nó tạo infinite loop
    // useEffect(() => {
    //   setInputValues(inputValues);
    // }, [inputValues]);
    // useEffect riêng để xử lý khi dataChild được load
    useEffect(() => {
      if (value && dataChild.length > 0 && title === 'danh mục sản phẩm') {
        const valueId = value.toString();

        // Kiểm tra xem valueId có trong dataChild không
        const foundInChild = dataChild.find(item => item.Id === valueId);
        if (foundInChild) {
          setselectChildren(valueId);
          // Reset selectParentId vì đã tìm thấy trong child
          setSelectParentId('');
        }
      }
    }, [dataChild, value, title]);

    const handleSelectParent = async (item: any) => {
      try {
        let res = await ProductDA.GetAllCate();
        if (res?.code === 200) {
          const childCategories = res.data.filter(
            (childItem: any) => childItem.ParentId === item.Id,
          );
          if (childCategories.length === 0) {
            // Nếu không có danh mục con, chọn luôn danh mục cha
            setselectChildren(item.Id);
            onSelect?.(item);
            return;
          } else {
            // Lấy ảnh cho các danh mục con
            let dataWithImages = await getImage({items: childCategories});
            setDataChild(dataWithImages);
          }
        } else {
          // Nếu API thất bại, chọn luôn danh mục cha
          setselectChildren(item.Id);
          onSelect?.(item);
          setDataChild([]);
        }
      } catch (error) {
        // Nếu có lỗi, chọn luôn danh mục cha
        setselectChildren(item.Id);
        onSelect?.(item);
        setDataChild([]);
      }
    };

    const handleSelectDataChild = (item: any) => {
      // Xử lý khi chọn danh mục con
      onSelect?.(item);
    };

    const handleItemSelect = (item: any) => {
      if (!selectParentId || selectParentId !== item.Id) {
        setSelectParentId(item.Id);
        handleSelectParent(item);
      } else {
        // Nếu đã có parent, gọi handleSelectDataChild
        handleSelectDataChild(item);
        setselectChildren(item.Id);
      }
    };

    const defaultRenderItem = (item: any, index: number) => (
      <View key={index}>
        <TouchableOpacity
          style={styles.item}
          onPress={() => {
            if (title === 'nhóm thuộc tính') {
              // Logic cho nhóm thuộc tính - cho phép chọn nhiều
              const newCheckedState = !checkedItems[item.Id];
              setCheckedItems({
                ...checkedItems,
                [item.Id]: newCheckedState,
              });

              if (newCheckedState) {
                // Thêm item vào mảng đã chọn
                const newArray = [...arrayDataListAttribute, item];
                setArrayDataListAttribute(newArray);
              } else {
                // Xóa item khỏi mảng đã chọn
                const newArray = arrayDataListAttribute.filter(
                  selectedItem => selectedItem.Id !== item.Id,
                );
                setArrayDataListAttribute(newArray);
              }
            } else {
              // Logic cho các title khác - giữ nguyên
              handleItemSelect(item);
            }
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
            {title === 'nhóm thuộc tính' ? (
              // Hiển thị checkbox ở đầu cho nhóm thuộc tính
              <View style={{marginRight: 10}}>
                {checkedItems[item.Id] ? <RadioAction /> : <Radio />}
              </View>
            ) : null}
            <FastImage
              source={
                item?.Img
                  ? {uri: item.Img}
                  : require('../../../../../assets/logo.png')
              }
              style={{
                height: 30,
                width: 30,
                borderRadius: 40,
              }}
              resizeMode={FastImage.resizeMode.cover}
              defaultSource={require('../../../../../assets/logo.png')}
              onError={() => {
                console.log('Failed to load image for item:', item?.Name);
              }}
            />
            <Text style={styles.itemText}>{item.Name}</Text>
          </View>
          {title !== 'nhóm thuộc tính'
            ? // Hiển thị radio ở cuối cho các title khác
              (() => {
                const isSelected =
                  item.Id === selectParentId || item.Id === selectChildren;

                return isSelected ? <RadioAction /> : <Radio />;
              })()
            : null}
        </TouchableOpacity>

        {/* Input text cho nhóm thuộc tính khi item được chọn */}
        {title === 'nhóm thuộc tính' && checkedItems[item.Id] && (
          <View
            style={{
              paddingHorizontal: 16,
              paddingTop: 8,
              paddingBottom: 12,
            }}>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 8,
                fontSize: 14,
                color: ColorThemes.light.neutral_text_title_color,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
              }}
              placeholder="Nhập thông tin..."
              placeholderTextColor={
                ColorThemes.light.neutral_text_subtitle_color
              }
              multiline={true}
              numberOfLines={2}
              value={(() => {
                // Tìm attribute có CateAttributeId bằng item.Id
                const matchingAttribute = allAttribute.find(
                  (e: any) => e.CateAttributeId === item.Id,
                );

                // Ưu tiên inputValues (giá trị user đã nhập), sau đó mới đến attribute value
                return inputValues[item.Id] || matchingAttribute?.Value || '';
              })()}
              onChangeText={text => {
                // Cập nhật local state khi user đang gõ
                const updatedInputValue = {
                  ...inputValues,
                  [item.Id]: text,
                };
                setInputValues(updatedInputValue);

                // Gọi setGetInputValue ngay lập tức để sync với parent
                if (setGetInputValue) {
                  setGetInputValue(updatedInputValue);
                }
              }}
            />
          </View>
        )}
      </View>
    );

    return (
      <Modal
        visible={visible}
        transparent
        animationType="slide"
        onRequestClose={cancel}>
        <View style={styles.overlay}>
          <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={1}
            onPress={cancel}
          />
          <View style={[styles.bottomSheet, {height: screenHeight * 0.85}]}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity style={styles.closeButton} onPress={cancel}>
                  <Text style={styles.closeText}>✕</Text>
                </TouchableOpacity>
                {selectParentId ? (
                  // Hiển thị nút back khi đang ở data con
                  <TouchableOpacity
                    onPress={handleBackToParent}
                    style={{
                      padding: 8,
                      borderRadius: 20,
                    }}>
                    <AppSvg SvgSrc={iconSvg.back} size={18} />
                  </TouchableOpacity>
                ) : (
                  // Hiển thị nút close khi đang ở data cha
                  <TouchableOpacity style={styles.closeButton} onPress={cancel}>
                    <AppSvg SvgSrc={iconSvg.back} size={18} />
                  </TouchableOpacity>
                )}
              </View>
              <Text style={styles.title}>{`Chọn ${title}`}</Text>
              <View style={styles.rightSection} />
            </View>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                paddingHorizontal: 16,
                gap: 8,
                paddingBottom: 16,
                paddingTop: 10,
                alignItems: 'center',
              }}>
              <FTextField
                style={{
                  paddingHorizontal: 16,
                  flex: 1,
                  height: 40,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}
                onChange={async (vl: string) => {
                  setSearchValue(vl.trim());
                }}
                value={searchValue}
                placeholder="Tìm kiếm..."
                prefix={
                  <Winicon
                    src="outline/development/zoom"
                    size={14}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                }
              />
              <Text style={{color: ColorThemes.light.primary_main_color}}>
                Xoá
              </Text>
            </View>

            {/* Content */}
            <ScrollView
              style={styles.content}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              keyboardDismissMode="on-drag"
              bounces={true}
              alwaysBounceVertical={false}
              contentContainerStyle={{
                flexGrow: 1,
                paddingBottom: 20,
              }}
              nestedScrollEnabled={true}
              scrollEventThrottle={16}>
              {children ? (
                <ScrollView
                  style={{flex: 1}}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  contentContainerStyle={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                  }}>
                  {children}
                </ScrollView>
              ) : (
                <ScrollView
                  style={{flex: 1}}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  contentContainerStyle={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                  }}
                  removeClippedSubviews={true}
                  scrollEventThrottle={16}
                  bounces={true}>
                  {(() => {
                    const dataToRender =
                      selectParentId && dataChild.length > 0
                        ? dataChild
                        : dataParent;

                    return dataToRender.map((item, index) =>
                      renderItem
                        ? renderItem(item, index)
                        : defaultRenderItem(item, index),
                    );
                  })()}
                </ScrollView>
              )}
            </ScrollView>
            {/* Bottom Buttons */}
            {title === 'nhóm thuộc tính' ? (
              <View style={styles.bottomButtons}>
                <TouchableOpacity style={styles.cancelButton} onPress={cancel}>
                  <Text style={styles.cancelButtonText}>Hủy</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={() => {
                    // Gửi kết quả đã chọn cho nhóm thuộc tính kèm input values
                    if (arrayDataListAttribute.length > 0) {
                      const resultWithInputs = arrayDataListAttribute.map(
                        item => ({
                          ...item,
                          inputValue: inputValues[item.Id] || '',
                        }),
                      );
                      onSelect?.(resultWithInputs);
                    }
                    cancel();
                  }}>
                  <Text style={styles.confirmButtonText}>Xác nhận</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.bottomButtons}>
                <TouchableOpacity style={styles.cancelButton} onPress={cancel}>
                  <Text style={styles.cancelButtonText}>Hủy</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.confirmButton} onPress={cancel}>
                  <Text style={styles.confirmButtonText}>Xác nhận</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    );
  },
);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: screenHeight * 0.9, // Fixed height at 85% of screen height
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 0, // Đã giảm xuống 0 vì đã thêm padding vào leftSection và rightSection
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    paddingLeft: 10,
    gap: 10, // Khoảng cách giữa các button
  },
  rightSection: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingRight: 10,
  },
  title: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginLeft: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  item: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  // Bottom buttons styles
  bottomButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20, // Cách bottom 20px
    paddingTop: 16,
    gap: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  cancelButton: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_main_color,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '500',
  },
});

export default BottomSheetComponent;
