import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Winicon} from '../../../../component/export-component';
import {RootScreen} from '../../../../router/router';

interface HeaderActionsProps {
  onRefresh: () => void;
}

export default function HeaderActions({onRefresh}: HeaderActionsProps) {
  const navigation = useNavigation<any>();

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={onRefresh} style={styles.actionButton}>
        <Winicon
          src="outline/arrows/refresh"
          color={ColorThemes.light.neutral_text_subtitle_color}
          size={20}
        />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigation.reset({
            index: 0,
            routes: [{name: RootScreen.navigateView}],
          });
        }}
        style={styles.actionButton}>
        <Winicon
          src="fill/user interface/home"
          color={ColorThemes.light.neutral_text_subtitle_color}
          size={20}
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 16,
    paddingRight: 16,
  },
  actionButton: {
    padding: 6,
    borderRadius: 32,
    backgroundColor: '#fff',
    position: 'relative',
  },
});
