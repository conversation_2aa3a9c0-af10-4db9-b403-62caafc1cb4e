import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {BottomActionBarProps} from '../types';

const BottomActionBar: React.FC<BottomActionBarProps> = ({
  onChatPress,
  onAddToCartPress,
  onBuyNowPress,
}) => {
  return (
    <View style={styles.bottomActions}>
      {/* <TouchableOpacity style={styles.chatButton} onPress={onChatPress}>
        <View style={styles.actionButtonContent}>
          <Winicon src="fill/user interface/f-chat" size={24} color="#00474F" />
          <Text style={styles.actionButtonText}>Chat ngay</Text>
        </View>
      </TouchableOpacity> */}
      <TouchableOpacity style={styles.cartAddButton} onPress={onAddToCartPress}>
        <View style={styles.actionButtonContent}>
          <Winicon src="fill/shopping/cart" size={24} color="#00474F" />
          <Text style={styles.actionButtonText}>Thêm vào giỏ hàng</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity style={styles.buyButton} onPress={onBuyNowPress}>
        <View style={styles.actionButtonContent}>
          <Winicon src="fill/shopping/box-ribbon" size={24} color="#fff" />
          <Text style={[styles.actionButtonText, {color: '#fff'}]}>
            Mua ngay
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    height: 64,
  },
  chatButton: {
    flex: 1,
    backgroundColor: '#9CE5CB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartAddButton: {
    flex: 1,
    backgroundColor: '#9CE5CB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buyButton: {
    flex: 1,
    backgroundColor: '#43A047',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonContent: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  actionButtonText: {
    color: '#00474F',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default BottomActionBar;
