import React, {forwardRef, useRef, useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  StyleSheet,
} from 'react-native';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {
  CustomerItem,
  CustomerType,
} from '../../../../../../../redux/reducers/user/da';
import {DataController} from '../../../../../../base-controller';
import ConfigAPI from '../../../../../../../config/configApi';
import {
  FDialog,
  FTextField,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../../component/component-status';
import ScreenHeader from '../../../../../../layout/header';
import ListTile from '../../../../../../../component/list-tile/list-tile';
import {SkeletonImage} from '../../../../../../../project-component/skeleton-img';
import AppButton from '../../../../../../../component/button';
import EmptyPage from '../../../../../../../project-component/empty-page';
import {Ultis} from '../../../../../../../utils/Utils';
import {closePopup} from 'component/popup/popup';
import {ToiletServiceItem} from 'types/toiletServiceType';
import {RoleDa} from '../../../../../role/roleDa';

interface AsignForConsultantProps {
  serviceData: ToiletServiceItem;
  lat: any;
  lng: any;
  cateServices: Array<any>;
  onDone: () => void;
}

export const AsignForConsultant = forwardRef<any, AsignForConsultantProps>(
  function AsignForConsultant({serviceData, lat, lng, onDone}, ref) {
    const [searchValue, setSearchValue] = useState('');
    const [partners, setPartners] = useState({
      data: Array<CustomerItem>(),
      totalCount: undefined,
    });
    const dialogRef = useRef<any>();

    const getData = async (page = 1, size = 1000) => {
      let search = '';
      if (searchValue.length) {
        if (!searchValue.startsWith('+84') && !searchValue.startsWith('0')) {
          search = ` @Name:(*${searchValue}*)`;
        } else {
          let mobile = searchValue.replace('+84', '0');
          if (mobile.startsWith('84')) mobile = mobile.replace('84', '0');
          search = ` @Mobile:(*${mobile}*)`;
        }
      }

      // Validate lat/lng values
      const validLat = lat && !isNaN(lat) ? lat : 0;
      const validLng = lng && !isNaN(lng) ? lng : 0;

      const customerController = new DataController('Customer');
      const res = await customerController.group({
        searchRaw: `(-@Id:{${ConfigAPI.adminKtxId}}) @Type:[${CustomerType.partner}] @Lat:[0 +inf] @Long:[0 +inf]${search}`,
        reducers: `LOAD * APPLY geodistance(@Lat,@Long,${validLat},${validLng}) AS __dist SORTBY 1 @__dist LIMIT ${(page - 1) * size} ${size}`,
      });
      if (res.code === 200) {
        setPartners({
          data: res.data,
          totalCount: res.totalCount,
        });
      }
    };

    const handleSelectPartner = async (item: any) => {
      showDialog({
        ref: dialogRef,
        status: ComponentStatus.INFOR,
        title: 'Bạn chắc chắn muốn chia đơn cho đối tác này xử lý?',
        onSubmit: async () => {
          const toiletServicesController = new DataController('ToiletServices');
          const roleDa = new RoleDa();

          // Prepare updated service data
          const updatedServiceData = {
            ...serviceData,
            CustomerId: item.Id,
          };

          // Check if partner has a company using RoleDa
          try {
            const customerRole = await roleDa.getCustomerRole(item.Id);
            if (customerRole.isCompany && customerRole.company) {
              updatedServiceData.CompanyProfileId = customerRole.company.Id;
            }
          } catch (error) {
            console.error('Error getting customer role:', error);
          }

          const res = await toiletServicesController.edit([updatedServiceData]);
          if (res.code === 200) {
            showSnackbar({
              message: 'Đơn hàng đã được chuyển tới cho đối tác xử lý',
              status: ComponentStatus.SUCCSESS,
            });
          }
          setTimeout(() => {
            if (ref && typeof ref !== 'function') {
              closePopup(ref as React.MutableRefObject<any>);
            }
          }, 100);
          setTimeout(() => {
            if (onDone) onDone();
          }, 200);
        },
      });
    };

    const renderPartnerItem = ({item}: {item: any; index: number}) => (
      <ListTile
        key={item.Id}
        leading={
          item?.Img ? (
            <SkeletonImage
              source={{
                uri: item.Img.startsWith('https')
                  ? item.Img
                  : ConfigAPI.imgUrlId + item?.Img,
              }}
              style={styles.partnerAvatar}
            />
          ) : (
            <View
              style={[
                styles.partnerAvatarPlaceholder,
                {backgroundColor: Ultis.generateRandomColor()},
              ]}>
              <Text style={styles.partnerAvatarText}>
                {item?.Name?.substring(0, 1)}
              </Text>
            </View>
          )
        }
        title={item?.Name ?? '-'}
        subtitle={
          <View style={styles.partnerInfo}>
            <Text style={styles.partnerSubText}>
              {`Sđt: ${item?.Mobile ?? '-'}`}
            </Text>
            <Text style={styles.partnerSubText} numberOfLines={4}>
              {`Địa chỉ: ${item?.Address ?? '-'}`}
            </Text>
          </View>
        }
        bottom={
          <View style={styles.partnerActions}>
            <AppButton
              title={'Chọn'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={styles.selectButton}
              onPress={() => handleSelectPartner(item)}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          </View>
        }
      />
    );

    useEffect(() => {
      getData();
    }, [searchValue]);

    return (
      <SafeAreaView style={styles.container}>
        <FDialog ref={dialogRef} />
        <ScreenHeader
          style={styles.header}
          title={`Tìm đối tác`}
          prefix={<View style={styles.headerPrefix} />}
          action={
            <TouchableOpacity
              onPress={() => {
                if (ref && typeof ref !== 'function') {
                  closePopup(ref as React.MutableRefObject<any>);
                }
              }}
              style={styles.headerAction}>
              <Winicon
                src="outline/layout/xmark"
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          }
          bottom={
            <View style={styles.searchContainer}>
              <FTextField
                style={styles.searchInput}
                onChange={vl => {
                  const trimmedValue = vl.trim();
                  setSearchValue(trimmedValue);

                  // Clear data when search input is empty
                  if (trimmedValue === '') {
                    setPartners({
                      data: [],
                      totalCount: undefined,
                    });
                  }
                }}
                onFocus={() => {
                  // Clear old data when user focuses on search input
                  setPartners({
                    data: [],
                    totalCount: undefined,
                  });
                }}
                value={searchValue}
                placeholder="Tìm kiếm"
                prefix={
                  <Winicon
                    src="outline/development/zoom"
                    size={14}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                }
              />
            </View>
          }
        />

        <KeyboardAvoidingView
          behavior={'padding'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 260 : 0}
          style={styles.content}>
          <FlatList
            data={partners.data}
            style={styles.list}
            renderItem={renderPartnerItem}
            ListEmptyComponent={<EmptyPage title="Không tìm thấy đối tác" />}
            keyExtractor={item => item.Id}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 75,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
  },
  headerPrefix: {
    width: 50,
  },
  headerAction: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 16,
    gap: 8,
    paddingBottom: 16,
  },
  searchInput: {
    paddingHorizontal: 16,
    flex: 1,
    height: 40,
  },
  content: {
    flex: 1,
    height: '100%',
    padding: 16,
  },
  list: {
    flex: 1,
  },
  partnerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 50,
    objectFit: 'cover',
  },
  partnerAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  partnerAvatarText: {
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  partnerInfo: {
    gap: 4,
  },
  partnerSubText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  partnerActions: {
    flexDirection: 'row',
    gap: 8,
    paddingTop: 16,
  },
  selectButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
