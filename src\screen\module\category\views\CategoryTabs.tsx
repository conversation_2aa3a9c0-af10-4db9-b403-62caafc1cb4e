import React, {useState} from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {TypoSkin} from '../../../../assets/skin/typography';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';

interface Category {
  Id: string;
  Name: string;
  Img: string;
}

const CategoryTabsSkeleton = () => (
  <SkeletonPlaceholder backgroundColor="#F0F0F0" highlightColor="#E0E0E0">
    <View style={{flexDirection: 'row', gap: 12}}>
      {Array(5)
        .fill(0)
        .map((_, index) => (
          <View key={index} style={{alignItems: 'center', gap: 4}}>
            <SkeletonPlaceholder.Item
              width={30}
              height={30}
              borderRadius={15}
            />
            <SkeletonPlaceholder.Item width={60} height={12} borderRadius={4} />
          </View>
        ))}
    </View>
  </SkeletonPlaceholder>
);

interface CategoryTabsProps {
  categories: Category[];
  selectedCategory: string;
  onSelectCategory: (id: string) => void;
  scrollViewRef: React.Ref<ScrollView>;
}

const CategoryTabs = ({
  categories,
  selectedCategory,
  onSelectCategory,
  scrollViewRef,
}: CategoryTabsProps) => {
  const [isChoose, setIsChoose] = useState<string>(categories[0].Id);
  // Safety check for categories
  if (!Array.isArray(categories)) {
    console.warn('CategoryTabs: categories is not an array');
    return <CategoryTabsSkeleton />;
  }

  const handleCategoryPress = (categoryId: string) => {
    try {
      if (isChoose === categoryId) return;
      if (onSelectCategory && typeof onSelectCategory === 'function') {
        onSelectCategory(categoryId);
        setIsChoose(categoryId);
      }
    } catch (error) {
      console.error('Error in CategoryTabs onPress:', error);
    }
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryTabsContainer}
      contentContainerStyle={styles.categoryTabsContent}>
      {categories.length === 0 ? (
        <CategoryTabsSkeleton />
      ) : (
        categories.map(category => {
          // Safety check for each category
          if (!category || !category.Id) {
            return null;
          }

          return (
            <TouchableOpacity
              key={category.Id}
              style={[
                styles.categoryTab,
                selectedCategory === category.Id && styles.activeCategoryTab,
              ]}
              onPress={() => handleCategoryPress(category.Id)}>
              {category.Id == 'all' ? (
                <Winicon
                  src="outline/layout/square-grid"
                  size={20}
                  color={
                    selectedCategory === category.Id
                      ? ColorThemes.light.primary_main_color
                      : '#666666'
                  }
                />
              ) : (
                <FastImage
                  source={{uri: category.Img}}
                  style={{width: 30, height: 30}}
                  resizeMode={FastImage.resizeMode.contain}
                />
              )}

              <Text
                style={[
                  styles.categoryTabText,
                  selectedCategory === category.Id &&
                    styles.activeCategoryTabText,
                ]}>
                {category.Name || ''}
              </Text>
            </TouchableOpacity>
          );
        })
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  categoryTabsContainer: {
    maxHeight: 73,
    marginTop: 16,
  },
  categoryTabsContent: {
    gap: 12,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    gap: 4,
    borderRadius: 8,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: ColorThemes.light.primary_main_color,
    overflow: 'hidden',
  },
  activeCategoryTab: {
    backgroundColor: '#E3F2FD',
  },
  categoryTabText: {
    ...TypoSkin.buttonText6,
  },
  activeCategoryTabText: {
    color: '#2962FF',
  },
});

export default React.memo(CategoryTabs);
