import React from 'react';
import {useRoute} from '@react-navigation/native';
import {OrderDetailRouteParams} from './index';
import OrderDetailShop from './OrderDetailShop';
import OrderDetailCustomer from './OrderDetailCustomer';

const OrderDetailPage: React.FC = () => {
  const route = useRoute();

  const {type, orderId} = route.params as OrderDetailRouteParams;

  if (type === 'customer') {
    return <OrderDetailCustomer orderId={orderId} />;
  } else {
    return <OrderDetailShop orderId={orderId} />;
  }
};

export default OrderDetailPage;
