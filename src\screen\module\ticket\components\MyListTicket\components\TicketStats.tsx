import React from 'react';
import {View, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../../../../../assets/skin/colors';
import {Winicon} from '../../../../../../../../component/export-component';
import ListTile from '../../../../../../../../component/list-tile/list-tile';

interface TicketStatsProps {
  processing: number;
  done: number;
  cancelled: number;
}

const TicketStats: React.FC<TicketStatsProps> = ({
  processing,
  done,
  cancelled,
}) => {
  return (
    <View style={styles.container}>
      <ListTile
        style={styles.statItem}
        leading={
          <Winicon
            src="outline/buildings/time-alarm"
            size={14}
            color={ColorThemes.light.infor_main_color}
            style={[
              styles.icon,
              {backgroundColor: ColorThemes.light.infor_background},
            ]}
          />
        }
        title={`${processing}`}
        subtitle="Đang <PERSON> lý"
        subTitleStyle={{
          color: ColorThemes.light.infor_main_color,
        }}
      />
      <ListTile
        style={[styles.statItem, styles.statItemRow]}
        leading={
          <Winicon
            src="outline/layout/check-double"
            size={14}
            color={ColorThemes.light.success_main_color}
            style={[
              styles.icon,
              {backgroundColor: ColorThemes.light.success_background},
            ]}
          />
        }
        title={`${done}`}
        subTitleStyle={{
          color: ColorThemes.light.success_main_color,
        }}
        subtitle="Hoàn thành"
      />
      <ListTile
        style={[styles.statItem, styles.statItemRow]}
        leading={
          <Winicon
            src="outline/layout/circle-xmark"
            size={14}
            color={ColorThemes.light.error_main_color}
            style={[
              styles.icon,
              {backgroundColor: ColorThemes.light.error_background},
            ]}
          />
        }
        title={`${cancelled}`}
        subtitle="Đã hủy"
        subTitleStyle={{
          color: ColorThemes.light.error_main_color,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 4,
    flexDirection: 'row',
  },
  statItem: {
    padding: 0,
    paddingTop: 16,
    flex: 1,
  },
  statItemRow: {
    flexDirection: 'row',
  },
  icon: {
    padding: 14,
    borderRadius: 50,
  },
});

export default TicketStats;
