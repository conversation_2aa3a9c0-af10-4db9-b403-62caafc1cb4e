import React, {useEffect, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {ProgressSteps, Step1, Step2, Step3} from './components';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {get} from '../../../../../utils/lodash';
import {FormData} from './components/Step2';
import {NetZeroDa} from 'screen/module/service/Da/netzeroDa';
import {handleToiletDistribute} from 'screen/module/service/components/da';
import {RoleDa} from 'screen/module/role/roleDa';

interface InfoRegister {
  Toilets: string[];
  CustomerId: string;
  Name: string;
  Phone: string;
  Address: string;
  Long: number;
  Lat: number;
  CompanyProfileId?: string; // Made optional with '?'
}

export default function RegisterToiletStep({
  onCompleted,
  levelCateCriterion,
  CateCriterionId,
  serviceId,
}: {
  onCompleted: (data: InfoRegister) => void;
  levelCateCriterion: number;
  CateCriterionId: string;
  serviceId: string;
}) {
  const customer = useSelectorCustomerState().data;
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [info, setInfo] = useState<InfoRegister>({
    Toilets: [],
    CustomerId: '',
    Name: '',
    Phone: '',
    Address: '',
    Long: 0,
    Lat: 0,
    CompanyProfileId: '',
  });
  const netZeroDa = new NetZeroDa();

  const next = () => setCurrentStep(prev => Math.min(prev + 1, 3));
  const back = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  const handleStep1Next = (toilets: string[]) => {
    setInfo({...info, Toilets: toilets});
    next();
  };

  const handleStep2Next = ({Name, Phone, Address, Long, Lat}: FormData) => {
    setInfo({...info, Name, Phone, Address, Long, Lat});
    next();
  };

  const handleStep3Next = async () => {
    const closestConsultant = await handleToiletDistribute({
      lat: info.Lat,
      long: info.Long,
      cateIds: [serviceId],
    });

    onCompleted({
      Toilets: info.Toilets,
      CustomerId: closestConsultant?.Id || '',
      CompanyProfileId: closestConsultant?.CompanyProfileId || '',
      Name: info.Name,
      Phone: info.Phone,
      Address: info.Address,
      Long: info.Long,
      Lat: info.Lat,
    });
  };

  useEffect(() => {
    if (customer) {
      setInfo({
        ...info,
        Name: get(customer, 'Name', ''),
        CustomerId: customer.Id,
        Phone: get(customer, 'Mobile', ''),
        Address: get(customer, 'Address', ''),
        Long: get(customer, 'Long', 0),
        Lat: get(customer, 'Lat', 0),
      });
    }
  }, [customer]);

  return (
    <View style={styles.container}>
      <ProgressSteps step={currentStep} />
      <View style={styles.content}>
        {currentStep === 1 && (
          <Step1
            onNext={handleStep1Next}
            levelCateCriterion={levelCateCriterion}
          />
        )}
        {currentStep === 2 && (
          <Step2
            onNext={handleStep2Next}
            defaultValues={{
              Long: info.Long,
              Lat: info.Lat,
              Name: info.Name,
              Phone: info.Phone,
              Address: info.Address,
            }}
          />
        )}
        {currentStep === 3 && (
          <Step3
            phone={info.Phone}
            onBack={back}
            onNext={handleStep3Next}
            Toilets={info.Toilets}
            CateCriterionId={CateCriterionId}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {flex: 1, padding: 16},
  content: {flex: 1, marginTop: 16},
});
