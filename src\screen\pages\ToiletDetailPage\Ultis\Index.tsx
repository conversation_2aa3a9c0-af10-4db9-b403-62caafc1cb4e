import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {RootScreen} from '../../../../router/router';

import {Winicon} from 'wini-mobile-components';
import {useEffect} from 'react';
import {CateServicesType} from '../../../module/service/components/da';
import {Ultis} from '../../../../utils/Utils';

export const generateDaysInYear = () => {
  const currentYear = new Date().getFullYear();
  const days = [];

  for (let month = 0; month < 12; month++) {
    const daysInMonth = new Date(currentYear, month + 1, 0).getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, month, day);
      const dayName = date.toLocaleDateString('vi-VN', {weekday: 'long'});
      const dateString = date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });

      days.push({
        id: days.length + 1,
        name: ` ${dateString}`,
        date: date,
        dateString: dateString,
      });
    }
  }

  return days;
};

export const registerKtx = (toiletId: string, navigation: any) => {
  return (
    <TouchableOpacity
      onPress={() => {
        navigation.navigate(RootScreen.CetificateAchievementPage, {
          toiletId: toiletId,
        });
      }}>
      <Image
        source={require('../../../../assets/RegisterKtxServices.png')}
        style={styles.backgroundImage}
      />
      <View style={styles.overlay}>
        <View style={styles.textContainer}>
          <Text style={styles.titleText}>
            Chuyển đổi NVS Sạch - Xanh - Tuần hoàn cùng KTX
          </Text>
          <Text style={styles.subtitleText}>
            Cùng nhau, chúng ta xây dựng hệ sinh thái tiêu chuẩn Sạch - Xanh -
            Tuần hoàn, tạo nên môi trường sống, môi trường làm việc lý tưởng
          </Text>
        </View>
      </View>
      <TouchableOpacity
        style={{
          position: 'absolute',
          right: 20,
          bottom: 10,
          backgroundColor: ColorThemes.light.primary_main_color,
          paddingVertical: 5,
          paddingHorizontal: 20,
          borderRadius: 20,
        }}
        onPress={() => {
          navigation.navigate(RootScreen.ServicesWorkFlow, {
            type: 'netzero',
            serviceId: CateServicesType.netzero,
          });
        }}>
        <Text style={{...TypoSkin.buttonText6, color: '#fff'}}>
          Đăng ký ngay
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export const renderDataRegisterKtx = (
  toiletId: string,
  navigation: any,
  infoService: any,
  dataToiletOperational: any,
  data12Month: number,
  dataLog: any,
) => {
  return (
    <TouchableOpacity
      onPress={() => {
        navigation.navigate(RootScreen.CetificateAchievementPage, {
          toiletId: toiletId,
        });
      }}>
      <Image
        source={require('../../../../assets/RegisterKtxServices.png')}
        style={styles.backgroundImage}
      />
      <View style={styles.overlay}>
        <View style={styles.contentContainer}>
          {/* Left side - Service Information */}
          <View style={styles.leftContent}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Dịch vụ đăng ký:</Text>
              <Text style={styles.statusValue}>
                {infoService?.CateCriterion?.Name}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Ngày đăng ký:</Text>
              <Text style={styles.statusValue}>
                {infoService?.DateCreated
                  ? Ultis.datetoString(new Date(infoService.DateCreated))
                  : '-'}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Đơn vị tư vấn:</Text>
              <Text style={styles.statusValue}>KTX Group</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Tiến độ:</Text>
              <Text style={styles.statusValue}>{data12Month}/12</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Trạng thái:</Text>
              <Text style={styles.statusValue}>
                {infoService.Status == 0 && 'Không tiếp nhận'}
                {infoService.Status == 1 && 'Chờ tiếp nhận'}
                {infoService.Status == 2 && 'Khảo sát'}
                {infoService.Status == 3 && 'Tư vấn'}
                {infoService.Status == 4 && 'Chốt báo giá'}
                {infoService.Status == 5 && 'Hợp đồng'}
                {infoService.Status == 6 && 'Ký hợp đồng'}
                {infoService.Status == 7 && 'Thiết kế'}
                {infoService.Status == 8 && 'Chốt thiết kế'}
                {infoService.Status == 9 && 'Thi công'}
                {infoService.Status == 10 && 'Hoàn công'}
                {infoService.Status == 11 && 'Thanh lý HĐ'}
                {infoService.Status == 12 && 'Hoàn thành'}
              </Text>
            </View>
          </View>
          <View style={styles.rightContent}>
            {dataLog &&
              dataLog?.length > 0 &&
              dataLog?.map((item: any) => (
                <View
                  style={[styles.timelineItem, styles.timelineItemWithLine]}>
                  <View style={styles.timelineItemLine} />
                  <View style={styles.timelineTime}>
                    {/* <Text style={styles.timeText}>08:42</Text> */}
                    <Text style={styles.dateText}>
                      {Ultis.datetoString(new Date(item.DateCreated))}
                    </Text>
                  </View>
                  <View style={[styles.timelineDot, styles.completedDot]}>
                    <Winicon
                      src="outline/user interface/check"
                      size={8}
                      color="white"
                    />
                  </View>
                  <Text
                    style={styles.timelineLabel}
                    numberOfLines={1}
                    ellipsizeMode="tail">
                    {item?.Name}
                  </Text>
                </View>
              ))}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export const renderCetificateA = (
  image: string,
  toiletId: string,
  navigation: any,
) => {
  return (
    <TouchableOpacity
      onPress={() => {
        navigation.navigate(RootScreen.CetificateAchievementPage, {
          toiletId: toiletId,
        });
      }}>
      <Image source={{uri: image}} style={styles.backgroundImage} />
    </TouchableOpacity>
  );
};

export const renderTwoCetificateA = (
  image: string,
  toiletId: string,
  navigation: any,
) => {
  return (
    <ScrollView
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      pagingEnabled={true}
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContent}>
      {/* First Slide */}
      <View style={styles.slideItem}>
        <TouchableOpacity
          onPress={() => {
            navigation.navigate(RootScreen.CetificateAchievementPage, {
              toiletId: toiletId,
            });
          }}>
          <Image
            source={require('../../../../assets/RegisterKtxServices.png')}
            style={styles.backgroundImage}
          />
          <View style={styles.overlay}>
            <View style={styles.textContainer}>
              <Text style={styles.titleText}>
                Chuyển đổi NVS Sạch - Xanh - Tuần hoàn cùng KTX
              </Text>
              <Text style={styles.subtitleText}>
                Cùng nhau, chúng ta xây dựng hệ sinh thái tiêu chuẩn Sạch - Xanh
                - Tuần hoàn, tạo nên môi trường sống, môi trường làm việc lý
                tưởng
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={{
              position: 'absolute',
              right: 20,
              bottom: 10,
              backgroundColor: ColorThemes.light.primary_main_color,
              paddingVertical: 5,
              paddingHorizontal: 20,
              borderRadius: 20,
            }}
            onPress={() => {
              navigation.navigate(RootScreen.ServicesWorkFlow, {
                type: 'netzero',
                serviceId: CateServicesType.netzero,
              });
            }}>
            <Text style={{...TypoSkin.buttonText6, color: '#fff'}}>
              Đăng ký thêm dịch vụ
            </Text>
          </TouchableOpacity>
        </TouchableOpacity>
      </View>
      {/* Third Slide */}
      <View style={styles.slideItem}>
        <TouchableOpacity
          onPress={() => {
            navigation.navigate(RootScreen.CetificateAchievementPage, {
              toiletId: toiletId,
            });
          }}>
          <Image source={{uri: image}} style={styles.backgroundImage} />
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  bannerContainer: {
    height: 200,
    position: 'relative',
    overflow: 'hidden',
  },
  backgroundImage: {
    height: 200,
    width: '100%',
    resizeMode: 'cover',
  },
  overlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  leftContent: {
    flex: 0.5,
    justifyContent: 'center',
  },
  rightContent: {
    flex: 0.5,
    position: 'relative',
    paddingLeft: 10,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 10,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '400',
    minWidth: 80,
    flex: 1,
  },
  infoValue: {
    ...TypoSkin.buttonText6,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: 'bold',
  },
  statusValue: {
    ...TypoSkin.buttonText6,
    color: '#9CE5CB',
    fontWeight: 'bold',
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    position: 'relative',
  },
  timelineItemWithLine: {
    position: 'relative',
  },
  timelineItemLine: {
    position: 'absolute',
    left: 77, // Position to align with dot center (60 + 12 + 5)
    top: 12, // Start from dot center
    bottom: -19, // Extend to next item
    width: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    opacity: 0.3,
    zIndex: 1,
  },
  timelineTime: {
    alignItems: 'center',
    marginRight: 12,
    minWidth: 60,
  },
  timeText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: 'bold',
    fontSize: 8,
  },
  dateText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    opacity: 0.8,
    fontSize: 8,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
    zIndex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedDot: {
    backgroundColor: '#4CAF50',
  },
  pendingDot: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: ColorThemes.light.neutral_absolute_background_color,
  },
  timelineLabel: {
    ...TypoSkin.buttonText6,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '500',
    fontSize: 10,
    flex: 1,
  },

  textContainer: {
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  titleText: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_absolute_background_color,
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
    paddingVertical: 5,
  },
  subtitleText: {
    ...TypoSkin.heading9,
    color: ColorThemes.light.neutral_absolute_background_color,
    textAlign: 'center',
    lineHeight: 20,
    opacity: 0.9,
    paddingVertical: 5,
  },
  slideContainer: {
    flex: 1,
    position: 'relative',
  },
  slidesWrapper: {
    flexDirection: 'row',
    height: '100%',
  },
  slide: {
    height: '100%',
  },
  buttonStyle: {
    position: 'absolute',
    right: 20,
    bottom: 10,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 5,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  buttonText: {
    ...TypoSkin.buttonText6,
    color: '#fff',
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeDot: {
    backgroundColor: ColorThemes.light.primary_main_color,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexDirection: 'row',
  },
  slideItem: {
    width: 370, // Fixed width for each slide
    marginRight: 10, // Space between slides
  },
});
