import React, {useEffect} from 'react';
import {FieldValues, useForm, UseFormReturn} from 'react-hook-form';
import {View, Text, TouchableOpacity} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {TextFieldForm} from '../../../../../project-component/component-form';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {RegisterPartnerFormStyles} from '../styles/RegisterPartnerFormStyles';
import {TypoSkin} from '../../../../../assets/skin/typography';
import iconSvg from '../../../../../svgs/iconSvg';
import {CustomDropdownNoBorder} from '../CustomDropdownNoBorder';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {
  CustomerRole,
  CustomerType,
} from '../../../../../redux/reducers/user/da';
import {store} from 'redux/store/store';

const BankInformationForm = ({
  methods,
  BankData,
  getDataCompany,
}: {
  methods: UseFormReturn<FieldValues, any, FieldValues>;
  BankData: any[];
  getDataCompany: any;
}) => {
  const userRole = useSelectorCustomerState().role;
  const cusInfo = store.getState().customer.data;

  const prefix = (icon: string) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          height: 32,
          width: 32,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <AppSvg SvgSrc={icon} size={18} />
      </View>
    );
  };

  useEffect(() => {
    if (getDataCompany) {
      methods.setValue('BankId', getDataCompany.BankId);
      methods.setValue('BankAccount', getDataCompany.BankAccount);
      methods.setValue('BankAccountName', getDataCompany.BankAccountName);
    }
  }, [getDataCompany]);

  return (
    <View style={{gap: 20, marginBottom: 24}}>
      <Text
        style={{
          color: ColorThemes.light.neutral_text_title_color,
          ...TypoSkin.title2,
          fontWeight: 'bold',
        }}>
        Thông tin tài khoản ngân hàng
      </Text>
      <View
        style={{
          position: 'relative',
          height: 48,
          backgroundColor: ColorThemes.light.transparent,
          borderWidth: 0,
          borderBottomWidth: 0.8,
          borderBlockColor: ColorThemes.light.neutral_text_title_reverse_color,
        }}>
        <View
          style={{
            position: 'relative',
            height: 48,
            backgroundColor: ColorThemes.light.neutral_disable_background_color,
            borderWidth: 0,
            borderRadius: 8,
            borderBottomWidth: 0.8,
            borderBlockColor:
              ColorThemes.light.neutral_text_title_reverse_color,
          }}>
          <View
            style={{
              position: 'absolute',
              left: 8,
              top: 0,
              bottom: 0,
              justifyContent: 'center',
              zIndex: 1,
            }}>
            {prefix(iconSvg.formBankName)}
          </View>
          {cusInfo &&
          cusInfo?.Type !== CustomerType.partner &&
          !userRole?.Role?.includes(CustomerRole.Owner) ? (
            // Nếu có role Owner: Hiển thị dropdown có thể chọn
            <CustomDropdownNoBorder
              control={methods.control}
              errors={methods.formState.errors}
              style={{marginLeft: 45}}
              required
              placeholder="Ngân hàng"
              name="BankId"
              options={
                BankData?.map((bank: any) => ({
                  id: bank.Id,
                  name: bank.Name,
                })) || []
              }
            />
          ) : (
            // Nếu không có role Owner: Chỉ hiển thị text, không cho chọn
            <View
              style={{
                marginLeft: 36,
                justifyContent: 'center',
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
              }}>
              <Text
                style={{
                  ...TypoSkin.regular2,
                  marginLeft: 16,
                }}>
                {BankData?.find(
                  (bank: any) => bank.Id === methods.watch('BankId'),
                )?.Name || 'Chưa chọn ngân hàng'}
              </Text>
            </View>
          )}
        </View>
        <TextFieldForm
          control={methods.control}
          name="BankAccount"
          placeholder="Số tài khoản"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          type="phone-pad"
          required
          prefix={prefix(iconSvg.formBankAccount)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
        <TextFieldForm
          control={methods.control}
          name="BankAccountName"
          placeholder="Chủ tài khoản"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formAccountOwner)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
      </View>
    </View>
  );
};
export default BankInformationForm;
