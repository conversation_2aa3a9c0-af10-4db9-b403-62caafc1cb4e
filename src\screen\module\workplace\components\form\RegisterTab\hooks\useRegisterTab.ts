import {useEffect, useMemo, useState} from 'react';
import {useForm} from 'react-hook-form';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../../../redux/hooks/hooks';
import {
  CateServicesType,
  SurveyStatus,
  TaskType,
  ToiletServiceStatus,
} from '../../../../../service/components/da';
import {CustomerRole} from '../../../../../../../redux/reducers/user/da';
import {DataController} from '../../../../../../base-controller';
import {BaseDA} from '../../../../../../baseDA';
import {randomGID, Ultis} from '../../../../../../../utils/Utils';
import ConfigAPI from '../../../../../../../config/configApi';
import {showSnackbar} from '../../../../../../../component/snackbar/snackbar';
import {ComponentStatus} from 'wini-mobile-components';

interface UseRegisterTab1Props {
  data: any;
  serviceData: any;
  methodParent: any;
}

export const useRegisterTab = ({
  data,
  serviceData,
  methodParent,
}: UseRegisterTab1Props) => {
  // Redux selectors
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const company = useSelectorCustomerCompanyState().data;
  const userRole = useSelectorCustomerState().role;

  // Local state
  const [surveyData, setSurveyData] = useState<any>();
  const [consultantTask, setConsultantTask] = useState<any>();
  const [faq, setFaq] = useState<Array<any>>([]);
  const [assignees, setAssignees] = useState([]);
  const [isLoading, setLoading] = useState(false);

  // Form methods
  const methods = useForm<any>({shouldFocusError: false});

  // Computed values
  const cateServices = useMemo(
    () =>
      methodParent
        .watch('cateServices')
        ?.filter((e: any) => e.Id !== CateServicesType.edu),
    [methodParent?.watch('cateServices')],
  );

  const isEditable = useMemo(() => {
    if (
      !serviceData?.Status ||
      serviceData?.Status > ToiletServiceStatus.research ||
      userRole?.CompanyProfileId === ConfigAPI.ktxCompanyId
    )
      return false;
    else if (user?.Id === serviceData?.CustomerId) return true;
    else if (
      serviceData?.CustomerId === owner?.Id &&
      (consultantTask?.CustomerId === user?.Id ||
        userRole?.Role?.includes(CustomerRole.Coordinator))
    )
      return true;
    return false;
  }, [serviceData, user, consultantTask, userRole]);

  const rejectReasons = useMemo(
    () => (surveyData?.RejectReason ? JSON.parse(surveyData.RejectReason) : []),
    [surveyData],
  );

  const initSurvey = useMemo(() => {
    return surveyData
      ? {...surveyData, Status: SurveyStatus.sendSurvey}
      : {
          Id: randomGID(),
          Name: 'Khảo sát nhà vệ sinh',
          ToiletServicesId: serviceData?.Id,
          Position: `${data?.Place}`.length ? data?.Place : undefined,
          ToiletType: `${data?.Type}`.length ? data?.Type : undefined,
          Type: 1,
          PassDesign: serviceData?.CateServicesId?.includes(
            CateServicesType.clean,
          ),
          Status: SurveyStatus.sendSurvey,
        };
  }, [surveyData, serviceData, data]);

  // API functions
  const getAssignees = async () => {
    const customerCompanyController = new DataController('CustomerCompany');
    const res = await customerCompanyController.getListSimple({
      page: 1,
      size: 1000,
      query: `@Status:[1 1] @CompanyProfileId:{${company?.Id}} @Role:(${[
        CustomerRole.Owner,
        CustomerRole.Coordinator,
        CustomerRole.Consultant,
      ]
        .map(e => `*${e}*`)
        .join(' | ')})`,
    });
    if (res.code === 200) {
      const customerController = new DataController('Customer');
      const resCustomer = await customerController.getByListId(
        res.data.map((e: any) => e.CustomerId),
      );

      if (resCustomer.code === 200) {
        setAssignees(
          resCustomer.data.map((e: any) => ({
            ...e,
            bgColor: Ultis.generateRandomColor(),
          })),
        );
      }
    }
  };

  const getSurveyData = async () => {
    try {
      const surveyController = new DataController('Survey');
      const res = await surveyController.aggregateList({
        page: 1,
        size: 1,
        searchRaw: `@ToiletServicesId:{${serviceData?.Id}}`,
      });
      if (res.code === 200) {
        if (res.data[0]) {
          let tmp = res.data[0];
          if (tmp.Design?.length) {
            const fileInfor = await BaseDA.getFilesInfor(tmp.Design.split(','));
            if (fileInfor?.code === 200) tmp.Design = fileInfor.data;
          } else {
            tmp.Design = [];
          }
          setSurveyData(tmp);
        }
      }
    } catch (error) {
      showSnackbar({
        message: 'Lỗi khi lấy dữ liệu khảo sát',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const uploadFiles = async (files: any[]) => {
    if (files.length) {
      const res = await BaseDA.uploadFiles(files);

      res.map((item: any) => {
        methods.setValue('Design', [
          ...(methods.getValues('Design') ?? []).filter(
            (e: any) => item.Name !== e?.name && item.Url !== e?.uri,
          ),
          {
            Id: item.Id,
            Url: item.Url,
            Name: item.Name ?? 'new file',
            Type: item.Type,
            Size: item.Size,
          },
        ]);
      });
    }
  };

  // Effects
  useEffect(() => {
    if (company) getAssignees();
  }, [company]);

  useEffect(() => {
    if (serviceData && data && user) {
      setLoading(true);
      if (serviceData?.Status === ToiletServiceStatus.research) {
        const taskController = new DataController('Task');
        taskController
          .aggregateList({
            page: 1,
            size: 1,
            searchRaw: `@ToiletServicesId:{${serviceData.Id}} @Type:[${TaskType.consultant} ${TaskType.consultant}]`,
          })
          .then(res => {
            if (res.code === 200 && res.data.length)
              setConsultantTask(res.data[0]);
          });
      }
      if (serviceData?.FAQId?.length) {
        const faqController = new DataController('FAQ');
        faqController.getByListId(serviceData?.FAQId?.split(',')).then(res => {
          if (res.code === 200) setFaq(res.data);
        });
      }
      getSurveyData();
    }
  }, []);

  return {
    // State
    surveyData,
    setSurveyData,
    consultantTask,
    faq,
    assignees,
    isLoading,
    setLoading,

    // Form methods
    methods,

    // Computed values
    cateServices,
    isEditable,
    rejectReasons,
    initSurvey,

    // Functions
    getSurveyData,
    uploadFiles,
  };
};
