import React, {useState, useEffect, useMemo} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  TextInput,
  Modal,
  Pressable,
  StyleProp,
  ViewStyle,
} from 'react-native';
import {Winicon} from '../export-component';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../svgs/iconSvg';
import {CustomerDa} from '../../screen/module/customer/customerDa';
import {
  Controller,
  Control,
  FieldErrors,
  FieldValues,
  Path,
} from 'react-hook-form';

// Types
interface Customer {
  Id: string;
  Name: string;
  Mobile?: string;
  Email?: string;
  AvatarUrl?: string;
}

interface CustomerDropdownProps {
  style?: StyleProp<ViewStyle>;
  onSelect?: (customer: Customer) => void;
  placeholder?: string;
  selectedCustomer?: Customer;
  disabled?: boolean;
  typeCustomer?: 1 | 2;
}

interface CustomerDropdownFormProps<T extends FieldValues = FieldValues> {
  control: Control<T>;
  name: Path<T>;
  errors: FieldErrors<T>;
  style?: StyleProp<ViewStyle>;
  placeholder?: string;
  disabled?: boolean;
  typeCustomer?: 1 | 2;
  required?: boolean;
}

// Helper Components
const CustomerAvatar = ({name}: {name: string}) => (
  <View style={styles.customerAvatar}>
    <Text style={styles.customerAvatarText}>
      {name.charAt(0).toUpperCase()}
    </Text>
  </View>
);

const CustomerItem = ({
  customer,
  onPress,
}: {
  customer: Customer;
  onPress: () => void;
}) => (
  <TouchableOpacity style={styles.customerItem} onPress={onPress}>
    <CustomerAvatar name={customer.Name} />
    <View style={styles.customerInfo}>
      <Text style={styles.customerName}>{customer.Name}</Text>
      {customer.Mobile && (
        <Text style={styles.customerDetail}>{customer.Mobile}</Text>
      )}
    </View>
  </TouchableOpacity>
);

const SearchInput = ({
  value,
  onChangeText,
}: {
  value: string;
  onChangeText: (text: string) => void;
}) => (
  <View style={styles.searchContainer}>
    <Winicon
      src="outline/development/zoom"
      size={16}
      color={ColorThemes.light.neutral_text_subtitle_color}
    />
    <TextInput
      style={styles.searchInput}
      placeholder="Tìm kiếm khách hàng..."
      value={value}
      onChangeText={onChangeText}
      placeholderTextColor={ColorThemes.light.neutral_text_subtitle_color}
      autoFocus={false}
      returnKeyType="search"
      clearButtonMode="while-editing"
    />
  </View>
);

// Custom hook for customer data
const useCustomers = (typeCustomer?: 1 | 2) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const customerDa = new CustomerDa();

  const fetchCustomers = async () => {
    try {
      const config: any = {
        page: 1,
        size: 100,
        query: typeCustomer ? `@Type: [${typeCustomer}]` : '*',
        returns: [
          'Id',
          'Name',
          'Type',
          'Mobile',
          'Email',
          'Address',
          'AvatarUrl',
          'CompanyProfileId',
        ],
        sortby: {BY: 'Name', DIRECTION: 'ASC'},
      };
      const response = await customerDa.fetchByQuery(config);
      setCustomers(response);
    } catch (error) {
      console.error('Error fetching customers:', error);
    }
  };

  useEffect(() => {
    fetchCustomers();
  }, [typeCustomer]);

  return customers;
};

// Main component
const CustomerDropdown: React.FC<CustomerDropdownProps> = ({
  style,
  onSelect,
  typeCustomer,
  placeholder = 'Chọn khách hàng',
  selectedCustomer,
  disabled = false,
}) => {
  const customers = useCustomers(typeCustomer);
  const [searchText, setSearchText] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  const filteredCustomers = useMemo(() => {
    if (!searchText.trim()) return customers;
    return customers.filter(
      customer =>
        customer.Name.toLowerCase().includes(searchText.toLowerCase()) ||
        customer.Mobile?.includes(searchText) ||
        customer.Email?.toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [customers, searchText]);

  const handleSelect = (customer: Customer) => {
    onSelect?.(customer);
    setIsVisible(false);
    setSearchText('');
  };

  const openModal = () => {
    if (!disabled) {
      setSearchText('');
      setIsVisible(true);
    }
  };

  return (
    <View style={style}>
      {/* Dropdown Button */}
      <TouchableOpacity
        style={[styles.dropdownButton]}
        onPress={openModal}
        disabled={disabled}>
        <View style={styles.dropdownContent}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <AppSvg
              style={{marginLeft: 8}}
              SvgSrc={iconSvg.formNameGroup}
              size={22}
              color="#4CAF50"
            />
            <Text
              style={[
                styles.dropdownText,
                !selectedCustomer && styles.placeholderText,
              ]}>
              {selectedCustomer ? selectedCustomer.Name : placeholder}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      {/* Modal */}
      <Modal
        visible={isVisible}
        transparent
        statusBarTranslucent
        animationType="slide"
        onRequestClose={() => setIsVisible(false)}>
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setIsVisible(false)}>
          <View style={styles.bottomSheetContainer}>
            {/* Header */}
            <View style={styles.bottomSheetHeader}>
              <View style={styles.dragIndicator} />
              <View style={styles.headerContent}>
                <Text style={styles.bottomSheetTitle}>
                  {selectedCustomer ? selectedCustomer.Name : 'Chọn khách hàng'}
                </Text>
                <TouchableOpacity
                  onPress={() => setIsVisible(false)}
                  style={styles.closeButton}>
                  <Winicon
                    src="outline/layout/xmark"
                    size={20}
                    color={ColorThemes.light.neutral_text_body_color}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Search */}
            <SearchInput value={searchText} onChangeText={setSearchText} />

            {/* Customer List */}
            <FlatList
              data={filteredCustomers}
              keyExtractor={item => item.Id}
              renderItem={({item}) => (
                <CustomerItem
                  customer={item}
                  onPress={() => handleSelect(item)}
                />
              )}
              style={styles.customerList}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            />
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

// Form-integrated version
export const CustomerDropdownForm = <T extends FieldValues = FieldValues>({
  control,
  name,
  errors,
  style,
  placeholder = 'Chọn khách hàng',
  disabled = false,
  typeCustomer,
  required = false,
}: CustomerDropdownFormProps<T>) => {
  const fieldError = errors[name];
  const errorMessage =
    fieldError && typeof fieldError === 'object' && 'message' in fieldError
      ? (fieldError.message as string)
      : required && fieldError
        ? 'Trường này là bắt buộc'
        : null;
  const hasError = !!errorMessage;

  return (
    <View style={style}>
      <Controller
        control={control}
        name={name}
        rules={{required: required ? 'Trường này là bắt buộc' : false}}
        render={({field}) => (
          <View>
            <CustomerDropdown
              selectedCustomer={field.value ? field.value : undefined}
              onSelect={customer => field.onChange(customer)}
              placeholder={placeholder}
              disabled={disabled}
              typeCustomer={typeCustomer}
            />
            {hasError && <Text style={styles.errorText}>{errorMessage}</Text>}
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    minHeight: 48,
    justifyContent: 'center',
  },
  dropdownContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_body_color,
    flex: 1,
    marginLeft: 20,
  },
  placeholderText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheetContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    minHeight: 500,
  },
  bottomSheetHeader: {
    paddingTop: 8,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: ColorThemes.light.neutral_text_subtitle_color,
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  bottomSheetTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    ...TypoSkin.body3,
    flex: 1,
    color: ColorThemes.light.neutral_text_body_color,
    padding: 0,
  },
  customerList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  customerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    gap: 12,
    borderRadius: 8,
  },
  customerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorThemes.light.primary_main_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customerAvatarText: {
    ...TypoSkin.semibold3,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
    fontWeight: '500',
  },
  customerDetail: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 2,
  },
  errorText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.error_main_color,
    marginLeft: 8,
  },
});

export default CustomerDropdown;
