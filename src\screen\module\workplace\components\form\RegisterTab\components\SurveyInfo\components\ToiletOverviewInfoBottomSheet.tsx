import {
  View,
  Text,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {closePopup, showPopup, Winicon} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import EmptyPage from 'project-component/empty-page';
import {
  ToiletPlace,
  ToiletSurveyTypeStrings,
  TypeStringData,
} from 'screen/module/service/components/da';
import ScreenHeader from 'screen/layout/header';
import WebView from 'react-native-webview';
import {TypoSkin} from 'assets/skin/typography';
import ConfigAPI from 'config/configApi';
import {useRef} from 'react';
import {CustomBottomSheet} from 'project-component/form/DateRangePicker/CustomBottomSheet';

const ToiletOverviewInfoBottomSheet = ({
  visible,
  onClose,
  surveyData,
}: {
  visible: boolean;
  onClose: () => void;
  surveyData: any;
}) => {
  const popupRef = useRef<any>();
  const positionText = () => {
    return surveyData?.Position === ToiletPlace.outDoor
      ? 'Ngoài trời'
      : 'Gắn liền tòa nhà';
  };
  const maintainText = () => {
    switch (surveyData?.Maintain) {
      case 1:
        return 'Định kỳ';
      case 2:
        return 'Không bảo trì/bảo dưỡng';
      case 3:
        return 'Theo tình trạng hư hỏng';
      default:
        return '';
    }
  };

  const openFileViewer = (item: any) => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <View style={styles.fileViewerContainer}>
          <ScreenHeader
            style={styles.fileViewerHeader}
            title={`Xem nhanh`}
            prefix={<View />}
            action={
              <TouchableOpacity
                onPress={() => closePopup(popupRef)}
                style={styles.closeButton}>
                <Winicon
                  src="outline/layout/xmark"
                  size={20}
                  color={ColorThemes.light.neutral_text_body_color}
                />
              </TouchableOpacity>
            }
          />
          <WebView
            style={styles.webView}
            source={{
              uri: ConfigAPI.url.replace('api/', '') + item.Url,
            }}
          />
        </View>
      ),
    });
  };

  const frequencyText = () => {
    const frequencyOptions = [
      {id: 1, name: 'Thường xuyên'},
      {id: 2, name: 'Theo khung giờ'},
      {id: 3, name: 'Không thường xuyên'},
    ];
    return (
      frequencyOptions.find((item: any) => item.id === surveyData?.Frequency)
        ?.name ?? surveyData?.Frequency
    );
  };
  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title="Thông tin khảo sát"
      cancelText="Đóng"
      isShowConfirm={false}>
      {surveyData ? (
        <View style={styles.surveyDetailContainer}>
          <Text style={styles.sectionTitle}>Thông tin khảo sát</Text>
          <Text style={styles.detailText}>
            Tiêu chí nhà vệ sinh:{' '}
            {ToiletSurveyTypeStrings.find(e => e.key === surveyData?.Type)
              ?.title ?? '-'}
          </Text>
          <Text style={styles.detailText}>
            Loại hình nhà vệ sinh:{' '}
            {TypeStringData.find(e => e.key === surveyData?.ToiletType)
              ?.title ?? '-'}
          </Text>
          <View style={styles.checkboxRow}>
            <Text style={styles.detailText}>Bỏ qua thiết kế: </Text>
            {surveyData.PassDesign && (
              <Winicon
                src="fill/layout/circle-check"
                size={18}
                color={ColorThemes.light.primary_main_color}
              />
            )}
          </View>
          <Text style={styles.detailText}>
            Tình trạng: {surveyData.Condition}
          </Text>
          <Text style={styles.detailText}>Độ tuổi: {surveyData.Age}</Text>
          <Text style={styles.detailText}>Thiết kế:</Text>
          <View style={styles.designContainer}>
            {surveyData?.Design?.filter((e: any) => e).map((item: any) => (
              <Text
                key={item.Id}
                onPress={() => openFileViewer(item)}
                style={styles.designLink}>
                - {item?.Name ?? '-'}
              </Text>
            ))}
          </View>
          <Text style={styles.detailText}>Tần suất: {frequencyText()}</Text>
          <Text style={styles.detailText}>Công năng: {surveyData.Feature}</Text>
          <Text style={styles.detailText}>
            Địa điểm xây dựng: {positionText()}
          </Text>
          <Text style={styles.detailText}>Quy mô: {surveyData.Size}</Text>
          <Text style={styles.detailText}>
            Tình trạng: {surveyData.Condition}
          </Text>
          <Text style={styles.detailText}>
            Tình trạng thiết bị: {surveyData.DeviceCondition}
          </Text>
          <Text style={styles.detailText}>
            Tần suất dọn dẹp: {surveyData.Clean}
          </Text>
          <Text style={styles.detailText}>
            Bảo trì, bảo dưỡng: {maintainText()}
          </Text>
          {surveyData.Description && (
            <Text style={styles.detailText}>Mô tả: {surveyData.Clean}</Text>
          )}
          {surveyData.Description && (
            <RenderHTML
              contentWidth={Dimensions.get('window').width}
              source={{html: surveyData.Description ?? ''}}
              baseStyle={styles.htmlContent}
            />
          )}
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <EmptyPage title="Chúng tôi sẽ liên hệ với Anh/Chị để thực hiện khảo sát." />
        </View>
      )}
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
    gap: 16,
  },
  title: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  tabContainer: {
    borderTopColor: ColorThemes.light.neutral_main_background_color,
    borderTopWidth: 1,
  },
  tabScrollView: {
    flexGrow: 0,
  },
  tabScrollContent: {
    alignItems: 'center',
    paddingHorizontal: 0,
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    alignItems: 'center',
    borderBottomColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.title3,
  },
  surveyDetailContainer: {
    gap: 16,
    paddingBottom: 100,
    paddingTop: 16,
  },
  sectionTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  detailText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  designContainer: {
    gap: 6,
  },
  designLink: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.primary_main_color,
    alignSelf: 'baseline',
  },
  emptyContainer: {
    paddingBottom: 100,
  },
  fileViewerContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  fileViewerHeader: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  webView: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  htmlContent: {
    fontSize: 14,
  },
});

export default ToiletOverviewInfoBottomSheet;
