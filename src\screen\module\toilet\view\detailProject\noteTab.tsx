import React, {useRef, useState, useMemo, useEffect} from 'react';
import {
  FlatList,
  Pressable,
  RefreshControl,
  Text,
  useWindowDimensions,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import ListTile from '../../../../../component/list-tile/list-tile';
import {FPopup} from '../../../../../component/popup/popup';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import {
  ToiletServiceStatus,
  ContractType,
  ContractStatus,
} from '../../../service/components/da';
import {TabBar, TabView} from 'react-native-tab-view';
import NewOrderWorkList from '../../../workplace/NewOrderWorkList';
import MyWorkList from '../../../workplace/MyWorkList';
import ConfigAPI from '../../../../../config/configApi';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {FRating} from '../../../../../component/export-component';
import {CardToiletHoriSkeleton} from '../../../../../project-component/skeletonCard';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

export default function NoteTab({
  data,
  serviceData,
  refreshing,
  onRefresh,
}: any) {
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const userRole = useSelectorCustomerState().role;
  const popupRef = useRef<any>();
  const [contracts, setContracts] = useState({data: [], totalCount: undefined});
  const [pageDetails, setPageDetails] = useState({page: 1, size: 10});
  const [services, setServices] = useState<Array<any>>([]);
  const isEditable = useMemo(() => user?.Id === data?.CustomerId, [user, data]);
  const layout = useWindowDimensions();

  const [index, setIndex] = useState(0);

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      activeColor={ColorThemes.light.primary_main_color}
      indicatorStyle={{
        backgroundColor: ColorThemes.light.primary_main_color,
        height: 1.5,
      }}
      onTabPress={() => {}}
      tabStyle={{paddingHorizontal: 4, paddingTop: 0}}
      inactiveColor={ColorThemes.light.neutral_text_subtitle_color}
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        height: 45,
      }}
    />
  );

  const renderScene = ({route}: any) => {
    switch (route.key) {
      case 'nvsRating':
        return (
          <ViewRating
            index={index}
            toiletId={data?.Id}
            toiletService={serviceData}
          />
        );
      // case 'orderRating':
      //     return <ViewRating index={index} toiletId={data?.Id} toiletService={serviceData} />
      default:
        return null;
    }
  };

  const routes = [
    {key: 'nvsRating', title: 'Đánh giá NVS'},
    // { key: 'orderRating', title: 'Đánh giá Đơn hàng' },
  ];

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <ViewRating
        index={index}
        toiletId={data?.Id}
        toiletService={serviceData}
      />
      {/* <TabView
            navigationState={{ index, routes }}
            renderScene={renderScene}
            renderTabBar={renderTabBar}
            swipeEnabled={false}
            onIndexChange={setIndex}
            initialLayout={{ width: layout.width }}
        /> */}
    </View>
  );
}

const ViewRating = ({index, toiletId, toiletService}: any) => {
  const [refreshing, setRefreshing] = useState(false);
  const [data, setData] = useState<Array<any>>([]);
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);

  const getData = async () => {
    setData([]);
    setRefreshing(true);
    const _controller = new DataController('Rating');
    const res = await _controller.aggregateList({
      page: 1,
      size: 1000,
      searchRaw: `@ToiletId:{${toiletId}}`,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code == 200) {
      const customerController = new DataController('Customer');
      const customerIds = res.data
        .map((e: any) => e.CustomerId)
        .filter(
          (v: any, i: any, a: any | any[]) => v?.length && a.indexOf(v) === i,
        );
      const resCustomer = await customerController.getByListId(customerIds);
      if (resCustomer.code === 200)
        setCustomers(
          resCustomer.data.map((e: any) => ({
            ...e,
            bgColor: Ultis.generateDarkColorRgb(),
          })),
        );
      setData(
        index === 0
          ? res.data
          : res.data.filter((e: any) => e.ToiletServicesId),
      );
      setRefreshing(false);
    }
    if (toiletId && index === 1) {
      const toiletServicesController = new DataController('ToiletServices');
      toiletServicesController
        .aggregateList({
          page: 1,
          size: 1000,
          searchRaw: `@ToiletId:{${toiletId}} @Status:[${ToiletServiceStatus.liquid} ${ToiletServiceStatus.liquid}]`,
        })
        .then(async res => {
          if (res.code === 200 && res.data.length) {
            setToiletServices(res.data);
          }
        });
    }
  };

  useEffect(() => {
    getData();
  }, [index]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
      }}>
      <FlatList
        data={data}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              getData();
            }}
          />
        }
        ListEmptyComponent={() =>
          refreshing ? (
            Array.from(Array(10)).map((_, index) => (
              <Pressable
                key={index}
                style={{width: '100%', flex: 1, marginTop: 8}}>
                <View
                  style={{
                    backgroundColor:
                      ColorThemes.light.neutral_absolute_background_color,
                    padding: 16,
                    borderRadius: 8,
                  }}>
                  <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item
                      height={65}
                      width={'100%'}
                      gap={16}>
                      <SkeletonPlaceholder.Item
                        width={'100%'}
                        flexDirection="row"
                        gap={16}
                        alignItems="center">
                        <SkeletonPlaceholder.Item
                          height={32}
                          width={32}
                          borderRadius={50}
                        />
                        <SkeletonPlaceholder.Item
                          height={30}
                          width={'100%'}
                          gap={8}>
                          <SkeletonPlaceholder.Item height={15} width={200} />
                          <SkeletonPlaceholder.Item height={15} width={100} />
                        </SkeletonPlaceholder.Item>
                      </SkeletonPlaceholder.Item>
                      <SkeletonPlaceholder.Item height={15} width={200} />
                    </SkeletonPlaceholder.Item>
                  </SkeletonPlaceholder>
                </View>
              </Pressable>
            ))
          ) : (
            <EmptyPage title="Nhà vệ sinh chưa có đánh giá" />
          )
        }
        ItemSeparatorComponent={() => <View style={{height: 8}} />}
        style={{paddingTop: 16, paddingHorizontal: 16}}
        renderItem={({item, index}) => {
          const customer = customers.find(
            (e: any) => e.Id === item?.CustomerId,
          );
          const toietletService = toiletServices?.find(
            (e: any) => e.Id === item?.ToiletServicesId,
          );
          return (
            <ListTile
              key={item.Id}
              style={{
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
              }}
              leading={
                customer?.Img ? (
                  <SkeletonImage
                    source={{
                      uri: customer.Img.startsWith('https')
                        ? customer.Img
                        : ConfigAPI.imgUrlId + customer?.Img,
                    }}
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: 50,
                      objectFit: 'cover',
                    }}
                  />
                ) : (
                  <View
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: Ultis.generateDarkColorRgb(),
                    }}>
                    <Text
                      style={{
                        color: '#fff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      {customer?.Name?.substring(0, 1)}
                    </Text>
                  </View>
                )
              }
              title={
                <Text style={{...TypoSkin.label3}}>{`${customer?.Name}`}</Text>
              }
              subtitle={
                <View style={{flex: 1}}>
                  <FRating
                    style={{gap: 2}}
                    value={item?.Value ?? 0}
                    fillColor={ColorThemes.light.primary_main_color}
                    onChange={e => {}}
                    size={14}
                  />
                </View>
              }
              bottom={
                <View
                  style={{
                    flex: 1,
                    alignItems: 'flex-start',
                    width: '100%',
                    paddingTop: 8,
                    gap: 8,
                  }}>
                  {toietletService ? (
                    <Text style={{...TypoSkin.subtitle3}}>
                      Đơn hàng: {`${toietletService?.Name ?? ''}`}
                    </Text>
                  ) : null}
                  <Text
                    style={{...TypoSkin.subtitle3}}>{`${item?.Content}`}</Text>
                  <Text
                    style={{
                      ...TypoSkin.subtitle4,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`${Ultis.datetoString(new Date(item?.DateCreated), 'dd/MM/yyyy hh:mm')}`}</Text>
                </View>
              }
            />
          );
        }}
        ListFooterComponent={() => <View style={{height: 16}} />}
      />
    </View>
  );
};
