import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rollView, Text, View } from 'react-native';
import ListTile from '../../../../../component/list-tile/list-tile';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { TypoSkin } from '../../../../../assets/skin/typography';
import { Winicon } from '../../../../../component/export-component';
import AppButton from '../../../../../component/button';
import { SkeletonImage } from '../../../../../project-component/skeleton-img';
import { useEffect, useRef, useState } from 'react';
import {
    useSelectorCustomerState,
    useSelectorCustomerCompanyState,
    useSelectorToiletState,
} from '../../../../../redux/hooks/hooks';
import { CustomerType } from '../../../../../redux/reducers/user/da';
import { DataController } from '../../../../base-controller';
import {
    CateServicesType,
    TaskType,
    ToiletServiceStatus,
    TypeStringData,
} from '../../../service/components/da';
import { Ultis } from '../../../../../utils/Utils';
import { RootScreen } from '../../../../../router/router';
import { useNavigation } from '@react-navigation/native';
import ConfigAPI from '../../../../../config/configApi';
import { FPopup } from '../../../../../component/popup/popup';
import { saveDataToAsyncStorage } from '../../../../../utils/AsyncStorage';

export default function Overview({
    data,
    serviceData,
    refreshing,
    setTab,
    onRefresh,
    serviceId,
}: {
    data: any;
    serviceData: any;
    setTab: any;
    refreshing?: boolean;
    onRefresh?: () => Promise<void>;
    serviceId: any;
}) {
    const user = useSelectorCustomerState().data;
    const userRole = useSelectorCustomerState().role;
    const owner = useSelectorCustomerCompanyState().owner;
    const [toiletServices, setToiletServices] = useState<Array<any>>([]);
    const [toiletItem, setToiletItem] = useState<any>();
    const [consultantUser, setConsultantUser] = useState<any>();
    const [cateService, setCateService] = useState([]);
    const navigation = useNavigation<any>();

    useEffect(() => {
        if (data) setToiletItem(data);
        switch (user?.Type) {
            case CustomerType.partner:
                if (data?.ToiletId) {
                    const toilet = new DataController('Toilet');
                    toilet.getById(data.ToiletId).then(res => {
                        if (res.code === 200) setToiletItem(res.data);
                    });
                } else setToiletItem(data);
                break;
            default:
                setToiletItem(data);
                break;
        }
    }, [data]);

    useEffect(() => {
        if (toiletItem) {
            const toiletServicesController = new DataController('ToiletServices');
            toiletServicesController
                .aggregateList({
                    page: 1,
                    size: 10,
                    searchRaw: `@ToiletId:{${toiletItem?.Id}} @Status:[${ToiletServiceStatus.register} ${ToiletServiceStatus.liquid}]`,
                })
                .then(async res => {
                    if (res.code === 200 && res.data.length) {
                        setToiletServices(res.data);
                    }
                });
        }
    }, [toiletItem]);

    useEffect(() => {
        if (toiletServices.length) {
            const customerController = new DataController('Customer');
            customerController
                .getByListId(
                    toiletServices
                        .map(e => e.CustomerId)
                        .filter((id, i, a) => id && a.indexOf(id) === i),
                )
                .then(res => {
                    if (res.code === 200)
                        setConsultantUser(
                            res.data
                                .filter((e: any) => e !== undefined && e !== null)
                                .map((e: any) => ({
                                    ...e,
                                    bgColor: Ultis.generateDarkColorRgb(),
                                })),
                        );
                });
        }
    }, [toiletServices.length]);

    return (
        <ScrollView
            refreshControl={
                <RefreshControl
                    refreshing={refreshing ?? false}
                    onRefresh={() => {
                        if (onRefresh) onRefresh();
                    }}
                />
            }
            style={{ flex: 1, paddingHorizontal: 16 }}>
            <ListTile
                title={toiletItem?.Name ?? '-'}
                titleStyle={[
                    TypoSkin.heading6,
                    { color: ColorThemes.light.neutral_text_title_color },
                ]}
                style={{
                    backgroundColor: ColorThemes.light.transparent,
                    paddingVertical: 16,
                    paddingHorizontal: 0,
                }}
                // subtitle={cateService.map((e: any) => e?.Name).join(", ")}
                subTitleStyle={[
                    TypoSkin.subtitle4,
                    { color: ColorThemes.light.neutral_text_subtitle_color },
                ]}
                bottom={
                    <View style={{ gap: 16, alignItems: 'flex-start' }}>
                        {toiletItem?.Description ? (
                            <Text
                                style={[
                                    TypoSkin.body3,
                                    {
                                        color: ColorThemes.light.neutral_text_body_color,
                                        textAlign: 'left',
                                    },
                                ]}
                                numberOfLines={3}>
                                {toiletItem?.Description ?? '-'}
                            </Text>
                        ) : null}
                        <ListTile
                            style={{ padding: 0, paddingTop: toiletItem?.Description ? 0 : 16 }}
                            leading={
                                <Winicon
                                    src="outline/user interface/filter"
                                    size={16}
                                    color={ColorThemes.light.neutral_text_subtitle_color}
                                />
                            }
                            title={`Phân loại: ${TypeStringData.find((e: any) => e.key === toiletItem?.Type)?.title ?? '-'}`}
                            titleStyle={[
                                TypoSkin.label4,
                                { color: ColorThemes.light.neutral_text_label_color },
                            ]}
                        />
                        <ListTile
                            style={{ padding: 0 }}
                            leading={
                                <Winicon
                                    src="outline/user interface/phone-call"
                                    size={16}
                                    color={ColorThemes.light.neutral_text_subtitle_color}
                                />
                            }
                            title={`Số điện thoại liên hệ: ${toiletItem?.Mobile ?? '-'}`}
                            titleStyle={[
                                TypoSkin.label4,
                                { color: ColorThemes.light.neutral_text_label_color },
                            ]}
                        />
                        <ListTile
                            style={{ padding: 0 }}
                            leading={
                                <Winicon
                                    src="outline/location/compass-3"
                                    size={16}
                                    color={ColorThemes.light.neutral_text_subtitle_color}
                                />
                            }
                            title={`Địa chỉ: ${toiletItem?.Address ?? '-'}`}
                            titleStyle={[
                                TypoSkin.label4,
                                { color: ColorThemes.light.neutral_text_label_color },
                            ]}
                        />
                        <ListTile
                            style={{ padding: 0 }}
                            leading={
                                <Winicon
                                    src="outline/user interface/c-check"
                                    size={16}
                                    color={ColorThemes.light.neutral_text_subtitle_color}
                                />
                            }
                            title={`Ngày tạo: ${toiletItem?.DateCreated ? Ultis.datetoString(new Date(toiletItem.DateCreated)) : ''}`}
                            titleStyle={[
                                TypoSkin.label4,
                                { color: ColorThemes.light.neutral_text_label_color },
                            ]}
                        />
                    </View>
                }
            />
            {toiletServices.length != 0 ? (
                <View style={{ gap: 16, paddingBottom: 0 }}>
                    <Text
                        style={[
                            TypoSkin.heading7,
                            {
                                color: ColorThemes.light.neutral_text_title_color,
                                paddingBottom: 8,
                            },
                        ]}>
                        Dịch vụ đang được cung cấp:
                    </Text>
                    {toiletServices.map((ser: any, i: number) => {
                        const partnerData = consultantUser?.find(
                            (e: any) => e?.Id === ser?.CustomerId,
                        );
                        var processLabel = '';
                        switch (ser?.Status) {
                            case ToiletServiceStatus.register:
                                processLabel = 'Chờ tiếp nhận';
                                break;
                            case ToiletServiceStatus.research:
                                processLabel = 'Đang khảo sát';
                                break;
                            case ToiletServiceStatus.consultant:
                                processLabel = 'Đang tư vấn';
                                break;
                            case ToiletServiceStatus.sendCompleteQuote:
                                processLabel = 'Đã gửi báo giá';
                                break;
                            case ToiletServiceStatus.contract:
                                processLabel = 'Đang làm hợp đồng';
                                break;
                            case ToiletServiceStatus.sendCompleteContract:
                                processLabel = 'Đã gửi hợp đồng';
                                break;
                            case ToiletServiceStatus.design:
                                processLabel = 'Đang thiết kế';
                                break;
                            case ToiletServiceStatus.sendCompleteDesign:
                                processLabel = 'Đã gửi thiết kế';
                                break;
                            case ToiletServiceStatus.build:
                                processLabel = "Đang thực hiện hợp đồng";
                                break;
                            case ToiletServiceStatus.sendCompleteBuild:
                                processLabel = 'Đã gửi biên bản nghiệm thu';
                                break;
                            case ToiletServiceStatus.liquid:
                                processLabel = 'Thanh lý hợp đồng';
                                break;
                            default:
                                break;
                        }
                        return (
                            <View
                                key={`ser ${i}`}
                                style={{
                                    width: '100%',
                                    padding: 16,
                                    borderColor: ColorThemes.light.neutral_main_border_color,
                                    borderWidth: 1,
                                    borderRadius: 8,
                                }}>
                                <ListTile
                                    style={{ padding: 0, flex: 1 }}
                                    title={`${i + 1}. Đơn hàng: ${ser.Name}`}
                                    titleStyle={[
                                        TypoSkin.heading8,
                                        {
                                            color: ColorThemes.light.neutral_text_title_color,
                                            paddingBottom: 4,
                                        },
                                    ]}
                                    subtitle={`Dịch vụ: ${ser.CateServicesId?.split(',')
                                        .map((id: any) => {
                                            switch (id) {
                                                case CateServicesType.create:
                                                    return 'Xây mới';
                                                case CateServicesType.repair:
                                                    return 'Sửa chữa';
                                                case CateServicesType.upgrade:
                                                    return 'Cải tạo';
                                                case CateServicesType.clean:
                                                    return 'Vệ sinh lau dọn';
                                                case CateServicesType.contact:
                                                    return 'Tư vấn';
                                                case CateServicesType.edu:
                                                    return 'Đào tạo';
                                                default:
                                                    return '';
                                            }
                                        })
                                        .join(', ')}`}
                                    subTitleStyle={[
                                        TypoSkin.subtitle4,
                                        { color: ColorThemes.light.neutral_text_subtitle_color },
                                    ]}
                                    bottom={
                                        <ListTile
                                            style={{ paddingHorizontal: 0, flex: 1 }}
                                            listtileStyle={{ gap: 16 }}
                                            leading={
                                                <SkeletonImage
                                                    source={{
                                                        uri: partnerData?.Img
                                                            ? partnerData?.Img?.startsWith('https')
                                                                ? partnerData?.Img
                                                                : ConfigAPI.imgUrlId + partnerData?.Img
                                                            : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTU3FcmHH1HtWFPQqC9Z-IK4JdvSWkvswcDfA&s',
                                                    }} // Remote image
                                                    style={{
                                                        width: 40,
                                                        height: 40,
                                                        objectFit: 'cover',
                                                        borderRadius: 100,
                                                    }}
                                                />
                                            }
                                            title={`Đối tác: ${partnerData?.Name ?? '-'}`}
                                            titleStyle={[
                                                TypoSkin.heading8,
                                                {
                                                    color: ColorThemes.light.neutral_text_title_color,
                                                    paddingBottom: 4,
                                                },
                                            ]}
                                            subtitle={`Số điện thoại: ${partnerData?.Mobile ?? '-'}`}
                                            subTitleStyle={[
                                                TypoSkin.subtitle4,
                                                { color: ColorThemes.light.neutral_text_subtitle_color },
                                            ]}
                                        />
                                    }
                                />
                                {serviceId === ser.Id ? null : (
                                    <AppButton
                                        title={processLabel}
                                        onPress={() => {
                                            navigation.navigate(RootScreen.DetailWorkView, {
                                                Id: toiletServices[0]?.Id,
                                                Status: toiletServices[0]?.Status,
                                                Name: toiletServices[0]?.Name,
                                            });
                                        }}
                                        suffixIcon={'outline/arrows/circle-arrow-right'}
                                        suffixIconSize={16}
                                        backgroundColor={ColorThemes.light.secondary2_main_color}
                                        borderColor="transparent"
                                        textStyle={TypoSkin.buttonText3}
                                        textColor={ColorThemes.light.neutral_text_stable_color}
                                        containerStyle={{
                                            alignSelf: 'flex-start',
                                            borderRadius: 8,
                                            paddingHorizontal: 8,
                                        }}
                                    />
                                )}
                            </View>
                        );
                    })}
                </View>
            ) : data?.Certificate ? null : (
                <AppButton
                    title={'Đăng ký tư vấn số hóa nhà vệ sinh'}
                    onPress={() => {
                        saveDataToAsyncStorage('contact-toiletid', data?.Id);
                        navigation.reset({
                            index: 0,
                            routes: [{ name: RootScreen.navigateView }],
                        });

                        setTimeout(() => {
                            navigation.push(RootScreen.ServicesWorkFlow, {
                                type: RootScreen.contact,
                            });
                        }, 100);
                    }}
                    backgroundColor={ColorThemes.light.transparent}
                    borderColor={ColorThemes.light.success_main_color}
                    textStyle={TypoSkin.buttonText5}
                    textColor={ColorThemes.light.success_main_color}
                    containerStyle={{
                        alignSelf: 'flex-start',
                        borderRadius: 8,
                        height: 26,
                        paddingHorizontal: 8,
                        marginLeft: 24,
                        marginTop: 8,
                    }}
                />
            )}
            {/* Mile stone */}
            <OverviewMileStones
                data={data}
                serviceData={serviceData}
                setTab={setTab}
            />
        </ScrollView>
    );
}

const OverviewMileStones = ({ data, serviceData, setTab }: any) => {
    const [newestTasks, setNewestTasks] = useState<Array<any>>([].flat(Infinity));
    const [processTasks, setProcessTasks] = useState<Array<any>>([]);
    const user = useSelectorCustomerState().data;
    const userRole = useSelectorCustomerState().role;
    const owner = useSelectorCustomerCompanyState().owner;
    const [toiletServices, setToiletServices] = useState<Array<any>>([]);
    const popupRef = useRef<any>();

    const getNewestTask = async () => {
        const taskController = new DataController('Task');
        const res = await taskController.aggregateList({
            page: 1,
            size: 5,
            searchRaw: `@Type:[${TaskType.consultant} ${TaskType.other}] @ToiletId:{${data.Id}}`,
            sortby: [{ prop: 'DateCreated', direction: 'DESC' }],
        });
        if (res.code === 200) {
            const servicesIds = res.data
                .filter((e: any) => e?.ToiletServicesId)
                .map((e: any) => e?.ToiletServicesId)
                .filter(
                    (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
                );
            if (servicesIds.length) {
                const servicesController = new DataController('ToiletServices');
                servicesController.getByListId(servicesIds).then(serRes => {
                    if (serRes.code === 200) setToiletServices(serRes.data);
                });
            }
            setNewestTasks(res.data);
        }
    };

    const getProcessTask = async () => {
        const toiletServicesController = new DataController('ToiletServices');
        const lastService = await toiletServicesController.aggregateList({
            page: 1,
            size: 1,
            searchRaw: `@ToiletId:{${data.Id}} (-@Status:[${ToiletServiceStatus.reject} ${ToiletServiceStatus.reject}])`,
        });
        if (lastService.code === 200 && lastService?.data[0]) {
            const taskController = new DataController('Task');
            const res = await taskController.aggregateList({
                page: 1,
                size: 10,
                searchRaw: `@Type:[${TaskType.consultant} ${TaskType.liquid}] @ToiletServicesId:{${lastService.data[0].Id}}`,
                sortby: [{ prop: 'DateCreated', direction: 'ASC' }],
            });
            if (res.code === 200) setProcessTasks(res.data);
        }
    };

    useEffect(() => {
        if (data) getNewestTask();
        getProcessTask();
    }, [data]);

    return (
        <View style={{ alignItems: 'flex-start', flex: 1, height: '100%' }}>
            <FPopup ref={popupRef} />
            <View
                style={{
                    alignItems: 'flex-start',
                    flex: 1,
                    marginTop: !processTasks?.length ? 0 : 16,
                }}>
                {processTasks.length ? (
                    <Text
                        style={[
                            TypoSkin.heading7,
                            { color: ColorThemes.light.neutral_text_title_color },
                        ]}>
                        Dịch vụ đăng ký gần nhất
                    </Text>
                ) : null}
                {processTasks.length
                    ? processTasks.map((item: any, index) => {
                        return (
                            <ListTile
                                key={`minestone${index}`}
                                style={{
                                    paddingVertical: 13,
                                    paddingHorizontal: 0,
                                    flex: 1,
                                    borderBottomColor:
                                        ColorThemes.light.neutral_main_border_color,
                                    borderBottomWidth: 1,
                                }}
                                title={item?.Name ?? '-'}
                                titleStyle={[
                                    TypoSkin.label4,
                                    { color: ColorThemes.light.neutral_text_label_color },
                                ]}
                                trailing={
                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                        <Text
                                            numberOfLines={2}
                                            style={[
                                                TypoSkin.subtitle4,
                                                {
                                                    color:
                                                        ColorThemes.light.neutral_text_subtitle_color,
                                                },
                                            ]}>
                                            {Ultis.datetoString(new Date(item?.DateStart))} -
                                            {Ultis.datetoString(new Date(item?.DateEnd))}
                                        </Text>
                                        <View
                                            style={{
                                                paddingLeft: 16,
                                                position: 'relative',
                                            }}>
                                            <Winicon
                                                src="outline/user interface/calendar-date"
                                                size={12}
                                            />
                                        </View>
                                    </View>
                                }
                            />
                        );
                    })
                    : null}
            </View>
            {/* new task */}
            {newestTasks.length && processTasks.length ? (
                <View
                    style={{
                        flex: 1,
                        width: '100%',
                        borderRadius: 8,
                        marginTop: 16,
                        paddingVertical: 16,
                        borderColor: ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                    }}>
                    <ListTile
                        title="Danh sách công việc mới"
                        style={{
                            borderRadius: 0,
                            paddingBottom: 4,
                            backgroundColor: ColorThemes.light.transparent,
                            padding: 0,
                            paddingHorizontal: 24,
                        }}
                        titleStyle={[
                            TypoSkin.heading8,
                            { color: ColorThemes.light.neutral_text_placeholder_color },
                        ]}
                    />
                    {newestTasks.map((task, index) => {
                        return (
                            <ListTile
                                key={`k${index}`}
                                onPress={() => setTab(2)}
                                style={{
                                    paddingVertical: 10,
                                    paddingHorizontal: 24,
                                    borderRadius: 0,
                                    alignContent: 'center',
                                    padding: 0,
                                    backgroundColor: ColorThemes.light.transparent,
                                }}
                                title={`${index + 1}. ${task?.Name ?? '-'}`}
                                subtitle={
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            marginTop: 4,
                                        }}>
                                        {(() => {
                                            var startValue = undefined;
                                            var endValue = undefined;
                                            if (!task || (!task.DateStart && !task.DateEnd))
                                                return null;
                                            if (task.DateStart) startValue = new Date(task.DateStart);
                                            if (task.DateEnd) endValue = new Date(task.DateEnd);
                                            return (
                                                <View
                                                    style={{
                                                        gap: 8,
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                    }}>
                                                    <Winicon
                                                        src="outline/user interface/calendar-date"
                                                        size={12}
                                                        color={
                                                            ColorThemes.light.neutral_text_subtitle_color
                                                        }
                                                    />
                                                    <Text
                                                        style={{
                                                            ...TypoSkin.body3,
                                                            color: ColorThemes.light.neutral_text_title_color,
                                                        }}>
                                                        {startValue
                                                            ? `${Ultis.datetoString(startValue, startValue.getSeconds() === 1 ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy')} - `
                                                            : ''}
                                                        {endValue
                                                            ? Ultis.datetoString(
                                                                endValue,
                                                                endValue.getSeconds() === 59
                                                                    ? 'dd/mm/yyyy hh:mm'
                                                                    : 'dd/mm/yyyy',
                                                            )
                                                            : ''}
                                                    </Text>
                                                    {task.RepeatValue ? (
                                                        <Winicon src="outline/arrows/loop-2" size={12} />
                                                    ) : null}
                                                </View>
                                            );
                                        })()}
                                    </View>
                                }
                                titleStyle={[
                                    TypoSkin.heading8,
                                    {
                                        color: ColorThemes.light.neutral_text_title_color,
                                        paddingBottom: 4,
                                    },
                                ]}
                            />
                        );
                    })}
                    {/* add */}
                    {/* <AppButton
                title={'Thêm công việc'}
                onPress={() => { }}
                prefixIcon={'outline/user interface/e-add'}
                prefixIconSize={12}
                backgroundColor={ColorThemes.light.transparent}
                borderColor={ColorThemes.light.success_main_color}
                textStyle={TypoSkin.buttonText5}
                textColor={ColorThemes.light.success_main_color}
                containerStyle={{
                    alignSelf: 'flex-start',
                    borderRadius: 8,
                    height: 26,
                    paddingHorizontal: 8,
                    marginLeft: 24,
                    marginTop: 8,
                    borderStyle: 'dotted',
                }}
            /> */}
                </View>
            ) : null}
        </View>
    );
};
