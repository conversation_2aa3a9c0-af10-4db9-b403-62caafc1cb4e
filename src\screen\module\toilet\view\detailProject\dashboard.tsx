import { FlatList, Pressable, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { ColorThemes } from "../../../../../assets/skin/colors";
import { TaskType } from "../../../service/components/da";
import ConfigAPI from "../../../../../config/configApi";
import ChartById from "../../../report/ChartById";

export default function Dashboard({ data, serviceData, refreshing, onRefresh }: { data: any, serviceData: any, refreshing?: boolean, onRefresh?: () => Promise<void> }) {
    const featuresData = [
        { id: "1", name: "New tasks", },
        { id: "2", name: "Warning tasks", },
        { id: "3", name: "Incomplete tasks", },
        { id: "4", name: "Overdue tasks", },
        { id: "5", name: "Completed tasks", },
        { id: "6", name: "Total tasks", },
    ]

    const viewFeatureItems = (item: any, index: number) => {
        return <View key={`feature${index}`} style={[{ width: '50%', height: 130, alignItems: "center", justifyContent: "center", }, { backgroundColor: ColorThemes.light.neutral_absolute_background_color, borderRadius: 8, borderColor: ColorThemes.light.neutral_main_border_color, borderWidth: 1 }]}>
            <View style={{ alignItems: "center", justifyContent: "center", gap: 8 }}>
                <Text>{index}</Text>
                <Text>{item.name}</Text>
            </View>
        </View>
    }

    return <ScrollView style={{ flex: 1, backgroundColor: ColorThemes.light.neutral_absolute_background_color }}>
        {/* <FlatList
            scrollEnabled={false}
            keyExtractor={item => "_" + item.id}
            data={featuresData}
            numColumns={2}
            columnWrapperStyle={{ gap: 16 }}
            ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
            renderItem={({ item, index }) => viewFeatureItems(item, index)}
        /> */}
        <Pressable style={styles.container}>
            <ChartById
                id="04aa037443724acfa026caf11b002db0"
                searchRaw={`@Type:[${TaskType.consultant} ${TaskType.other}] @ToiletId:{${data.Id}}`}
                onPress={false}
            />
        </Pressable>
    </ScrollView>
}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 10,
        backgroundColor: ColorThemes.light.white,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 20,
        color: '#333',
    },
});
