import {ComponentStatus, showDialog} from 'wini-mobile-components';
import {randomGID, Ultis} from 'utils/Utils';
import productDA from 'screen/module/product/productDA';
import {useCartActions} from 'redux/hooks/cartHook';
import {StatusOrder} from 'config/Contanst';
import {CartItem} from 'types/cartTypes';
import {
  StoreGroup,
  StockCheckResult,
  PaymentMethod,
  UseOrderProcessingReturn,
} from '../types';
import {showSnackbar} from 'component/export-component';
import {OrderProductDA} from 'screen/module/orderProduct/orderProductDA';
import {dialogCheckAcc} from 'screen/layout/main-layout';

interface UseOrderProcessingProps {
  items: CartItem[];
  storeGroups: StoreGroup[];
  customer: any;
  customerAddress: any;
  address: any;
  payment: PaymentMethod;
  dialogRef: React.RefObject<any>;
  setIsProcessing: (processing: boolean) => void;
  setDone: (done: boolean) => void;
}

export const useOrderProcessing = ({
  items,
  storeGroups,
  customer,
  customerAddress,
  address,
  payment,
  dialogRef,
  setIsProcessing,
  setDone,
}: UseOrderProcessingProps): UseOrderProcessingReturn => {
  const cartActions = useCartActions();
  const orderProductDa = new OrderProductDA();

  // Check stock availability for all products
  const checkStock = async (): Promise<StockCheckResult> => {
    const outOfStockItems: string[] = [];

    try {
      await Promise.all(
        items.map(async item => {
          const productDetail = await productDA.getProductDetail(
            item.ProductId,
          );
          if (
            productDetail &&
            productDetail.code === 200 &&
            productDetail.data.length > 0
          ) {
            const product = productDetail.data[0];
            const inStock = product.InStock || 0;

            if (inStock < 1 || inStock < item.Quantity) {
              outOfStockItems.push(item.Name);
            }
          } else {
            outOfStockItems.push(item.Name);
          }
        }),
      );

      return {
        isValid: outOfStockItems.length === 0,
        outOfStockItems,
      };
    } catch (error) {
      console.error('Error checking stock:', error);
      return {
        isValid: false,
        outOfStockItems: items.map(item => item.Name),
      };
    }
  };

  const submitOrder = async () => {
    // Check stock before placing order
    setIsProcessing(true);
    const stockCheck = await checkStock();
    setIsProcessing(false);

    if (!stockCheck.isValid) {
      const outOfStockMessage =
        stockCheck.outOfStockItems.length === 1
          ? `Sản phẩm "${stockCheck.outOfStockItems[0]}" đã hết hàng hoặc không đủ số lượng.`
          : `Các sản phẩm sau đã hết hàng hoặc không đủ số lượng:\n${stockCheck.outOfStockItems
              .map(name => `• ${name}`)
              .join('\n')}`;

      showSnackbar({
        message: outOfStockMessage,
        status: ComponentStatus.ERROR,
      });
      return;
    }

    // Show confirmation dialog
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.SUCCSESS,
      title: 'Bạn chắc chắn muốn đặt hàng?',
      onSubmit: async () => {
        await handlePlaceOrder();
      },
    });
  };

  const handlePlaceOrder = async () => {
    if (!customer) {
      dialogCheckAcc({ref: dialogRef});
      showSnackbar({
        message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    if (customerAddress?.length === 0) {
      showSnackbar({
        message: 'Vui lòng cập nhật điểm giao hàng để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    setIsProcessing(true);
    if (storeGroups.length > 0) {
      const successfullyOrderedItemIds: any[] = [];
      const listOrder: any[] = [];
      const listOrderDetail: any[] = [];
      // Create orders for each store group
      for (const group of storeGroups) {
        const order = {
          Id: randomGID(),
          CustomerId: customer?.Id,
          Name: customer?.Name,
          ShopId: group.ShopId,
          Code: Ultis.randomString(10).toLocaleUpperCase(),
          DateCreated: new Date().getTime(),
          DateUpdated: new Date().getTime(),
          Status: StatusOrder.new,
          Value: group.totalPrice,
          AddressId:
            address?.Id ||
            customerAddress?.find((item: any) => item.IsDefault)?.Id,
          PaymentType: payment.id,
          Description: 'Giao hàng nhanh',
        };
        listOrder.push(order);
        // Create order details for each item
        for (const item of group.items) {
          const price = parseFloat(item.Price?.toString() ?? '0');
          const discount = parseFloat(item.Discount?.toString() ?? '0');
          const orderDetail = {
            Id: randomGID(),
            Name: item.Name,
            OrderProductId: order.Id,
            ProductId: item.ProductId,
            DateCreated: new Date().getTime(),
            Quantity: item.Quantity ?? 1,
            Price: price,
            Discount: discount,
            Status: StatusOrder.new,
            Total: price * item.Quantity * (1 - (discount ?? 0) / 100),
          };
          successfullyOrderedItemIds.push(item.id);
          listOrderDetail.push(orderDetail);
        }
      }
      // Submit orders
      const detailRes = await orderProductDa.createOrderDetail(listOrderDetail);
      const res = await orderProductDa.createOrder(listOrder);
      if (detailRes?.code === 200 && res?.code === 200) {
        if (successfullyOrderedItemIds.length > 0) {
          cartActions.removeItemsById(successfullyOrderedItemIds);

          showSnackbar({
            message: 'Đặt hàng thành công!',
            status: ComponentStatus.SUCCSESS,
          });
          setDone(true);
        }
      } else {
        showSnackbar({
          message: 'Đặt hàng thất bại. Vui lòng thử lại sau.',
          status: ComponentStatus.ERROR,
        });
      }
    }
    setIsProcessing(false);
  };

  return {
    isProcessing: false, // This will be managed by the parent component
    checkStock,
    handlePlaceOrder: submitOrder,
  };
};
