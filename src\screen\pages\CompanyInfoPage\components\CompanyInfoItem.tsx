import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';

interface CompanyInfoItemProps {
  label: string;
  value: string | undefined;
}

export const CompanyInfoItem: React.FC<CompanyInfoItemProps> = ({
  label,
  value,
}) => {
  if (!value) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  label: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_body_color,
    fontWeight: '500',
  },
});
