import React, {useCallback, useRef} from 'react';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';
import TitleHeader from '../../../layout/headers/TitleHeader';
const CategoryHeader: React.FC = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get category data from Redux store
  const {parentCategory, filter} = useSelector(
    (state: RootState) => state.productByCategory,
  );

  const handleSearch = useCallback(
    (text?: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        productByCategoryHook.setData('filter', {
          ...filter,
          textSearch: text || null,
        });
      }, 1000);
    },
    [filter, productByCategoryHook],
  );

  return (
    <TitleHeader
      title={parentCategory?.Name || ''}
      showSearch={true}
      onSearch={handleSearch}
      showActions={true}
    />
  );
};

export default CategoryHeader;
