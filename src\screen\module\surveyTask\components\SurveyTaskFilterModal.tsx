import React, {useState, useEffect, forwardRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  FlatList,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {FCheckbox, Winicon} from '../../../../component/export-component';
import {
  SurveyTaskFilter,
  SURVEY_TASK_STATUS_OPTIONS,
  SORT_OPTIONS,
  DEFAULT_FILTER,
  SortOption,
} from '../constants/filterConstants';
import {AppButton} from 'wini-mobile-components';
import {useForm} from 'react-hook-form';
import {DateRangePicker} from '../../../../project-component/form/DateRangePicker/DateRangePicker';

interface SurveyTaskFilterModalProps {
  filter: SurveyTaskFilter;
  onApply: (filter: SurveyTaskFilter) => void;
  onClose: () => void;
}

const SurveyTaskFilterModal = forwardRef<any, SurveyTaskFilterModalProps>(
  ({filter, onApply, onClose}, ref) => {
    const [localFilter, setLocalFilter] = useState<SurveyTaskFilter>(filter);

    const {control, setValue} = useForm({
      defaultValues: {
        startDate: filter.dateRange?.startDate,
        endDate: filter.dateRange?.endDate,
      },
    });

    useEffect(() => {
      setLocalFilter(filter);
      setValue('startDate', filter.dateRange?.startDate);
      setValue('endDate', filter.dateRange?.endDate);
    }, [filter, setValue]);

    const handleStatusToggle = (statusKey: number) => {
      const currentStatus = localFilter.Status || [];
      const newStatus = currentStatus.includes(statusKey)
        ? currentStatus.filter(s => s !== statusKey)
        : [...currentStatus, statusKey];

      setLocalFilter({
        ...localFilter,
        Status: newStatus,
      });
    };

    const handleSortChange = (sortOption: SortOption) => {
      setLocalFilter({
        ...localFilter,
        sortBy: sortOption,
      });
    };

    const handleDateRangeChange = (startDate?: Date, endDate?: Date) => {
      setLocalFilter({
        ...localFilter,
        dateRange: {
          startDate,
          endDate,
        },
      });
      // Update form values
      setValue('startDate', startDate);
      setValue('endDate', endDate);
    };

    const handleReset = () => {
      const resetFilter = {...DEFAULT_FILTER};
      setLocalFilter(resetFilter);
      // Reset form values to default
      setValue('startDate', undefined);
      setValue('endDate', undefined);
      // Apply the reset filter immediately
      onApply(resetFilter);
      onClose();
    };

    const handleApply = () => {
      onApply(localFilter);
      onClose();
    };

    const renderStatusItem = ({item}: {item: any}) => (
      <TouchableOpacity
        key={item.key}
        onPress={() => handleStatusToggle(item.key)}
        style={styles.checkboxItem}>
        <FCheckbox
          value={localFilter.Status?.includes(item.key) || false}
          onChange={() => handleStatusToggle(item.key)}
        />
        <View style={styles.statusLabelContainer}>
          <View
            style={[styles.statusIndicator, {backgroundColor: item.color}]}
          />
          <Text style={styles.checkboxText}>{item.title}</Text>
        </View>
      </TouchableOpacity>
    );

    const renderSortItem = ({item}: {item: SortOption}) => (
      <TouchableOpacity
        key={`${item.field}-${item.direction}`}
        onPress={() => handleSortChange(item)}
        style={styles.radioItem}>
        <View style={styles.radioButton}>
          {localFilter.sortBy?.field === item.field &&
            localFilter.sortBy?.direction === item.direction && (
              <View style={styles.radioButtonSelected} />
            )}
        </View>
        <Text style={styles.checkboxText}>{item.label}</Text>
      </TouchableOpacity>
    );

    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Bộ lọc & Sắp xếp</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* Status Filter Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Trạng thái</Text>
            <FlatList
              data={SURVEY_TASK_STATUS_OPTIONS}
              renderItem={renderStatusItem}
              scrollEnabled={false}
              keyExtractor={item => item.key.toString()}
            />
          </View>

          {/* Sort Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Sắp xếp theo</Text>
            <FlatList
              data={SORT_OPTIONS}
              renderItem={renderSortItem}
              scrollEnabled={false}
              keyExtractor={item => `${item.field}-${item.direction}`}
            />
          </View>

          {/* Date Range Filter Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Lọc theo thời gian</Text>
            <View style={styles.dateRangeContainer}>
              <DateRangePicker
                control={control}
                startDateName="startDate"
                endDateName="endDate"
                errors={{}}
                onDateRangeChange={handleDateRangeChange}
                placeholder="Chọn khoảng thời gian"
                style={styles.dateRangePicker}
              />
            </View>
          </View>
        </ScrollView>

        {/* Footer Buttons */}
        <View style={styles.footer}>
          <AppButton
            title="Làm mới"
            onPress={handleReset}
            backgroundColor={ColorThemes.light.neutral_main_background_color}
            borderColor={ColorThemes.light.neutral_bolder_border_color}
            textColor={ColorThemes.light.neutral_text_body_color}
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 8,
            }}
          />
          <AppButton
            title="Áp dụng"
            onPress={handleApply}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor={ColorThemes.light.primary_main_color}
            containerStyle={{
              flex: 1,
              height: 40,
              borderRadius: 8,
              paddingHorizontal: 8,
            }}
          />
        </View>
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_bolder_border_color,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  statusLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  checkboxText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  dateRangeContainer: {
    marginTop: 8,
  },
  dateRangePicker: {
    backgroundColor: ColorThemes.light.white,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
  },

  footer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_bolder_border_color,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
  },
  applyButton: {
    flex: 1,
  },
});

export default SurveyTaskFilterModal;
