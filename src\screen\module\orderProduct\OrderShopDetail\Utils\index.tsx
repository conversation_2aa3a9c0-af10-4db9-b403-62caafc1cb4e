import {ComponentStatus} from 'wini-mobile-components';
import {DataController} from 'screen/base-controller';
import {OrderProductDA} from '../../orderProductDA';
import productDA from 'screen/module/product/productDA';
import {StatusOrder, Title} from 'config/Contanst';
import {navigate, RootScreen} from 'router/router';
import {store} from 'redux/store/store';
import {showSnackbar} from 'component/export-component';

const orderDetailController = new DataController('OrderDetailProduct');
const productController = new DataController('Product');
const shopController = new DataController('OrderProduct');

export const handleUpdateStatusOrder = async (
  item: any,
  type?: string,
  dispatch?: any,
  navigation?: any,
  shopInfo?: any,
  orderActions?: any,
) => {
  try {
    // setLoading(true);
    let order = item;
    const orderProductDA = new OrderProductDA();
    const shopId = store.getState().partner.data?.[0]?.Id;

    if (type == 'processing') {
      // Lấy chi tiết đơn hàng
      const orderDetailResponse = await orderProductDA.getOrderDetail(order.Id);
      if (
        orderDetailResponse?.code === 200 &&
        orderDetailResponse?.data?.length > 0
      ) {
        const orderDetails = orderDetailResponse.data;

        for (const detail of orderDetails) {
          const productId = detail?.ProductId;
          const orderQuantity = detail?.Quantity || 1;

          if (productId) {
            const productResponse = await productDA.getProductById(productId);

            if (productResponse?.code === 200 && productResponse?.data) {
              const product = productResponse.data;
              const currentStock = product?.InStock || 0;

              if (currentStock <= 0) {
                showSnackbar({
                  message: `Sản phẩm "${product?.Name}" đã hết hàng. Không thể xử lý đơn hàng này.`,
                  status: ComponentStatus.ERROR,
                });
                // setLoading(false); // Stop loading here to allow user to retry or cancel
                return; // Stop processing this order
              }

              if (orderQuantity > currentStock) {
                showSnackbar({
                  message: `Sản phẩm "${product?.Name}" chỉ còn ${currentStock} sản phẩm trong kho. Đơn hàng yêu cầu ${orderQuantity} sản phẩm.`,
                  status: ComponentStatus.ERROR,
                });
                // setLoading(false); // Stop loading here to allow user to retry or cancel
                return; // Stop processing this order
              }

              // Cập nhật số lượng tồn kho của sản phẩm
              const newStock = currentStock - orderQuantity;
              const updatedProduct = {
                ...product,
                InStock: newStock,
                Status: newStock === 0 ? 2 : 1,
              };
              const productUpdateResponse =
                await productDA.updateProduct(updatedProduct);
              console.log('check-productUpdateResponse', productUpdateResponse);
              if (productUpdateResponse?.code !== 200) {
                showSnackbar({
                  message:
                    'Có lỗi xảy ra khi cập nhật số lượng tồn kho sản phẩm',
                  status: ComponentStatus.ERROR,
                });
                // setLoading(false);
                return;
              }
            } else {
              return;
            }
          }
        }
        // Cập nhật trạng thái đơn hàng (chỉ khi tất cả sản phẩm được xử lý thành công)
        const res = await orderProductDA.updateOrder([
          {
            Id: order.Id,
            CustomerId: order.CustomerId,
            ShopId: order.ShopId,
            Code: order.Code,
            Status: StatusOrder.proccess,
            DateProcess: new Date().getTime(),
          },
        ]);
        if (res.code == 200) {
          console.log('✅ Order status updated successfully');
          showSnackbar({
            message:
              'Cập nhật trạng thái đơn hàng sang trạng thái đang xử lý thành công',
            status: ComponentStatus.SUCCSESS,
          });
          orderActions.fetchAllOrdersByShopId(shopId);
        } else {
          console.log('❌ Order update failed:', res);
        }
        // Chuyển trang sau khi cập nhật thành công - luôn chuyển về trạng thái 3 (hoàn thành)
        navigate(RootScreen.OrderShopDetail, {
          type: Title.Processing,
          status: 2,
        });
        return res;
      } else {
        return;
      }
    }
    if (type == 'completed') {
      console.log('✅ Completing order status update...');
      // Cập nhật trạng thái đơn hàng
      const res = await orderProductDA.updateOrder([
        {
          Id: order.Id,
          CustomerId: order.CustomerId,
          ShopId: order.ShopId,
          Code: order.Code,
          Status: StatusOrder.success,
          DateCompleted: new Date().getTime(),
        },
      ]);
      if (res.code == 200) {
        //#region cập nhật order detail khi đơn hàng hoàn thành
        const orderDetailResponse = await orderDetailController.getListSimple({
          query: `@OrderId: {${order?.Id}}`,
        });

        if (
          orderDetailResponse?.code === 200 &&
          orderDetailResponse?.data?.length > 0
        ) {
          const orderDetails = orderDetailResponse.data;
          // Cập nhật trạng thái của các order detail
          const updatedOrderDetails = orderDetails.map((detail: any) => ({
            Id: detail.Id,
            Status: 3,
          }));

          const orderDetailUpdateResponse =
            await orderProductDA.updateOrderDetail(updatedOrderDetails);

          if (orderDetailUpdateResponse?.code !== 200) {
            console.error(
              'Error updating order detail status:',
              orderDetailUpdateResponse,
            );
            showSnackbar({
              message:
                'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật trạng thái chi tiết đơn hàng',
              status: ComponentStatus.WARNING,
            });
          }

          // Cập nhật số lượng sản phẩm đã bán cho từng sản phẩm trong đơn hàng
          for (const detail of orderDetails) {
            const productId = detail?.ProductId;
            const orderQuantity = detail?.Quantity || 1;

            if (productId) {
              try {
                // Lấy thông tin sản phẩm hiện tại
                const productResponse = await productController.getListSimple({
                  query: `@Id:{${productId}}`,
                });

                if (
                  productResponse?.code === 200 &&
                  productResponse?.data?.length > 0
                ) {
                  const product = productResponse.data[0];
                  const currentSold = product?.Sold || 0;

                  // Cập nhật số lượng đã bán
                  const newSold = currentSold + orderQuantity;
                  const updatedProduct = {
                    ...product,
                    Sold: newSold,
                  };

                  const productUpdateResponse = await productController.edit([
                    updatedProduct,
                  ]);

                  if (productUpdateResponse?.code !== 200) {
                    console.error(
                      'Error updating product sold count:',
                      productUpdateResponse,
                    );
                    showSnackbar({
                      message:
                        'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật số lượng đã bán',
                      status: ComponentStatus.WARNING,
                    });
                  } else {
                    if (
                      dispatch &&
                      shopInfo &&
                      shopInfo.length > 0 &&
                      shopInfo[0]?.Id
                    ) {
                      orderActions.fetchAllOrdersByShopId(shopId);
                    }

                    // Chuyển trang sau khi cập nhật thành công - luôn chuyển về trạng thái 3 (hoàn thành)
                    if (navigation) {
                      navigation.navigate(RootScreen.OrderShopDetail, {
                        type: Title.Done,
                        status: 3,
                      });
                    }
                  }
                }
              } catch (error) {
                console.error('Error updating product sold count:', error);
                showSnackbar({
                  message:
                    'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật số lượng đã bán',
                  status: ComponentStatus.WARNING,
                });
              }
            }
          }
          return res;
        }

        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng sang trạng thái đã hoàn thành',
          status: ComponentStatus.SUCCSESS,
        });
      }
    }

    if (type == 'cancelled') {
      // Cập nhật trạng thái đơn hàng
      const res = await shopController.edit([
        {
          Id: order.Id,
          CustomerId: order.CustomerId,
          ShopId: order.ShopId,
          Code: order.Code,
          Status: StatusOrder.cancel,
          DateUpdate: new Date().getTime(),
          CancelReason: order.cancelReason || '',
        },
      ]);
      if (res.code == 200) {
        // Chỉ khôi phục số lượng tồn kho nếu đơn hàng trước đó có status = 2 (đang xử lý)
        // Nếu status = 1 (mới) thì không cần khôi phục vì chưa trừ tồn kho
        if (order.Status === 2) {
          const orderDetailResponse = await orderProductDA.getOrderDetail(
            order.Id,
          );
          if (
            orderDetailResponse?.code === 200 &&
            orderDetailResponse?.data?.length > 0
          ) {
            const orderDetails = orderDetailResponse.data;

            for (const detail of orderDetails) {
              const productId = detail?.ProductId;
              const orderQuantity = detail?.Quantity || 1;

              if (productId) {
                try {
                  // Lấy thông tin sản phẩm hiện tại
                  const productResponse =
                    await productDA.getProductById(productId);
                  if (
                    productResponse?.code === 200 &&
                    productResponse?.data?.length > 0
                  ) {
                    const product = productResponse.data[0];
                    const currentStock = product?.InStock || 0;

                    // Khôi phục số lượng tồn kho bằng cách cộng thêm số lượng đã đặt hàng
                    const newStock = currentStock + orderQuantity;
                    const updatedProduct = {
                      ...product,
                      InStock: newStock,
                      Status: newStock === 0 ? 2 : 1,
                    };

                    const productUpdateResponse = await productDA.updateProduct(
                      [updatedProduct],
                    );

                    if (productUpdateResponse?.code !== 200) {
                      console.error(
                        'Error updating product stock when cancelling order:',
                        productUpdateResponse,
                      );
                      showSnackbar({
                        message:
                          'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi khôi phục số lượng tồn kho sản phẩm',
                        status: ComponentStatus.WARNING,
                      });
                    } else {
                      console.log(
                        `Đã khôi phục ${orderQuantity} sản phẩm cho sản phẩm ${product?.Name}`,
                      );
                    }
                  } else {
                    showSnackbar({
                      message: `Không tìm thấy thông tin sản phẩm với ID: ${productId} khi hủy đơn hàng.`,
                      status: ComponentStatus.WARNING,
                    });
                  }
                } catch (error) {
                  console.error(
                    'Error updating product stock when cancelling order:',
                    error,
                  );
                  showSnackbar({
                    message:
                      'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi khôi phục số lượng tồn kho sản phẩm',
                    status: ComponentStatus.WARNING,
                  });
                }
              }
            }
          } else {
          }
        } else {
          console.log(
            `Đơn hàng có status = ${order.Status}, không cần khôi phục tồn kho`,
          );
        }

        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng sang trạng thái hủy',
          status: ComponentStatus.SUCCSESS,
        });
        if (dispatch && shopInfo && shopInfo.length > 0 && shopInfo[0]?.Id) {
          orderActions.fetchAllOrdersByShopId(shopId);
        }

        // Chuyển trang sau khi cập nhật thành công - luôn chuyển về trạng thái 3 (hoàn thành)
        if (navigation) {
          navigation.navigate(RootScreen.OrderShopDetail, {
            type: Title.Cancel,
            status: 4,
          });
        }
        return res;
      }
    }
  } catch (error) {
    console.error('❌ Error updating order status:', error);
    console.error('❌ Error details:', {
      item: item?.Id,
      type,
      shopId: store.getState().partner.data?.[0]?.Id,
      error: error,
    });
    showSnackbar({
      message: 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng',
      status: ComponentStatus.ERROR,
    });
  }
};
