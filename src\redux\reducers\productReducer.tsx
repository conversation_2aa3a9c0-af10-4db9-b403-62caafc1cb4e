import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {ProductItem} from '../../types/ProductType';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../store/store';

interface ProductSimpleResponse {
  data: ProductItem[];
  totalCount: number;
  onLoading?: boolean;
  type?: string;
}

const initState: ProductSimpleResponse = {
  data: [],
  totalCount: 0,
  onLoading: false,
};

export const productSlice = createSlice({
  name: 'Product',
  initialState: initState,
  reducers: {
    setData: <K extends keyof ProductSimpleResponse>(
      state: ProductSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: ProductSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
});

export const {setData} = productSlice.actions;

export default productSlice.reducer;

export const useProductHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (
      stateName: keyof ProductSimpleResponse,
      data: ProductSimpleResponse[keyof ProductSimpleResponse],
    ) => {
      dispatch(setData({stateName, data}));
    },
  };

  return action;
};
