import {getImage} from '../../../redux/actions/rootAction';
import {DataController} from '../../base-controller';

/**
 * Interface for Brand data structure
 */
export interface BrandItem {
  Id: string;
  Name: string;
  Sort: number;
  Img: string;
  Description: string;
  DateCreated: number;
  Status: number;
  IsHot?: boolean;
}

/**
 * Data Access class for Brand module
 */
export class BrandDa {
  private brandController: DataController;

  constructor() {
    this.brandController = new DataController('Brands');
  }

  /**
   * Fetch brands with pagination
   * @param page - Page number (default: 1)
   * @param size - Number of items per page (default: 10)
   * @param searchRaw - Search query (default: '*')
   * @param sortby - Sort configuration (default: DateCreated DESC)
   * @returns Promise with API response
   */
  async fetch({
    page = 1,
    size = 10,
    searchRaw = '*',
    sortby = [{prop: 'DateCreated', direction: 'DESC' as const}],
  }: {
    page?: number;
    size?: number;
    searchRaw?: string;
    sortby?: Array<{prop: string; direction?: 'ASC' | 'DESC'}>;
  } = {}): Promise<BrandItem[]> {
    try {
      const response = await this.brandController.aggregateList({
        page,
        size,
        searchRaw,
        sortby,
      });

      if (response.code === 200) {
        const data = await getImage({items: response.data});
        return data;
      }
      throw new Error(response.message);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Fetch a single brand by ID
   * @param id - Brand ID
   * @returns Promise with brand data or null if not found
   */
  async fetchById(id: string): Promise<BrandItem | null> {
    try {
      // Validate input
      if (!id || typeof id !== 'string') {
        console.error('Invalid ID provided to fetchById:', id);
        return null;
      }

      const response = await this.brandController.getById(id);

      if (response.code === 200 && response.data) {
        const data = await getImage({items: [response.data]});
        return data[0] || null;
      }

      return null;
    } catch (error) {
      console.error('Error in fetchById:', error);
      return null;
    }
  }
}

export default new BrandDa();
