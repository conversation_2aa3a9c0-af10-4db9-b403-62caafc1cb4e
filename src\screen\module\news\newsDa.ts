import {getImage} from '../../../redux/actions/rootAction';
import {DataController} from '../../base-controller';

class NewsDa {
  private newsController;
  constructor() {
    this.newsController = new DataController('News');
  }
  async fetch(config: any) {
    const response = await this.newsController.aggregateList(config);
    if (response.code === 200) {
      const data = await getImage({items: response.data});
      return data;
    }
    return [];
  }
  async fetchById(id: string) {
    const response = await this.newsController.getById(id);
    if (response.code === 200) {
      const data = await getImage({items: [response.data]});
      return data[0] || null;
    }
    return null;
  }
}

export default new NewsDa();
