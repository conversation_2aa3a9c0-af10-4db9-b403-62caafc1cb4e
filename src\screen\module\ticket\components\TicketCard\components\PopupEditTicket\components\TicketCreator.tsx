import React from 'react';
import {Text, View, StyleSheet} from 'react-native';
import ListTile from '../../../../../../../../component/list-tile/list-tile';
import {SkeletonImage} from '../../../../../../../../project-component/skeleton-img';
import {TypoSkin} from '../../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../../assets/skin/colors';
import {Ultis} from '../../../../../../../../utils/Utils';
import ConfigAPI from '../../../../../../../../config/configApi';

interface TicketCreatorProps {
  customer: any;
}

export const TicketCreator: React.FC<TicketCreatorProps> = ({customer}) => {
  if (!customer) return null;

  return (
    <>
      <Text style={styles.sectionLabel}><PERSON><PERSON><PERSON><PERSON> tạo</Text>
      <ListTile
        style={styles.listTile}
        leading={
          customer?.Img ? (
            <SkeletonImage
              source={{
                uri: customer.Img.startsWith('https')
                  ? customer.Img
                  : ConfigAPI.imgUrlId + customer?.Img,
              }}
              style={styles.customerAvatar}
            />
          ) : (
            <View
              style={[
                styles.customerAvatarPlaceholder,
                {backgroundColor: Ultis.generateDarkColorRgb()},
              ]}>
              <Text style={styles.customerAvatarText}>
                {customer?.Name?.substring(0, 1)}
              </Text>
            </View>
          )
        }
        title={customer?.Name ?? '-'}
        subtitle={customer?.Mobile ?? '-'}
      />
    </>
  );
};

const styles = StyleSheet.create({
  sectionLabel: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  listTile: {
    padding: 0,
  },
  customerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 50,
    objectFit: 'cover',
  },
  customerAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customerAvatarText: {
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
