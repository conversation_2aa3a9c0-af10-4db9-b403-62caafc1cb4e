import React from 'react';
import ActivityIndicator from 'react-native-paper/src/components/ActivityIndicator';
import {Pulse, Bounce} from 'react-native-animated-spinkit';
import DialogContent from 'react-native-paper/src/components/Dialog/DialogContent';
import {Image, View, ViewStyle} from 'react-native';
import {ColorSkin} from '../../assets/skin/colors';
import {Dialog, Portal} from 'react-native-paper';

interface FLoadingProps {
  style?: ViewStyle;
  avt?: string;
  visible: boolean;
  loadFullScreen?: boolean;
}

const FLoading: React.FC<FLoadingProps> = ({
  style,
  visible,
  loadFullScreen = true,
}) =>
  loadFullScreen ? (
    <Portal>
      <Dialog
        visible={visible}
        dismissable={false}
        // dismissableBackButton={false}
        style={{
          height: 0,
          alignItems: 'center',
          justifyContent: 'center',
          shadowColor: 'transparent',
          ...style,
        }}>
        <DialogContent
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <LoadingUI />
        </DialogContent>
      </Dialog>
    </Portal>
  ) : (
    <LoadingUI />
  );

export const LoadingUI = ({style}: {style?: ViewStyle}) => {
  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        ...style,
      }}>
      <Pulse
        size={150}
        color={ColorSkin.primary}
        style={{position: 'absolute'}}></Pulse>
      <Bounce
        size={80}
        color={ColorSkin.primary}
        style={{position: 'absolute'}}></Bounce>
      <ActivityIndicator
        size={60}
        color={ColorSkin.primary}
        style={{position: 'absolute'}}
      />
      <View
        style={{
          width: 50,
          height: 50,
          borderRadius: 100,
          position: 'absolute',
          backgroundColor: 'white',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Image
          style={{
            width: 35,
            height: 35,
            objectFit: 'contain',
          }}
          source={require('../../assets/logo.png')}
        />
      </View>
    </View>
  );
};

export default FLoading;
