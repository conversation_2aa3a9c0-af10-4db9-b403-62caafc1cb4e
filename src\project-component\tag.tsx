import { Text } from "react-native-paper"
import { TypoSkin } from "../assets/skin/typography"
import { ColorSkin } from "../assets/skin/colors"
import { StyleSheet, TextStyle, TouchableOpacity } from "react-native"
import { ComponentStatus } from "../component/component-status"
import { GestureResponderEvent } from "react-native-modal"

/** 1: infor, 2: error, 3: warning, 4: success, default: primary, 5: disabled, 6: selected */
export const DefaultTag = ({ title, status, style = {}, onPress }: { title: string, status?: number, style?: TextStyle, onPress?: (((event: GestureResponderEvent) => void) & (() => void)) }) => {
    const getStatusStyles = (num?: number) => {
        switch (num) {
            case ComponentStatus.INFOR:
                return {
                    color: ColorSkin.inforColor,
                    backgroundColor: ColorSkin.inforBackground,
                }
            case ComponentStatus.ERROR:
                return {
                    color: ColorSkin.errorColor,
                    backgroundColor: ColorSkin.errorBackground,
                }
            case ComponentStatus.WARNING:
                return {
                    color: ColorSkin.warningColor,
                    backgroundColor: ColorSkin.warningBackground,
                }
            case ComponentStatus.SUCCSESS:
                return {
                    color: ColorSkin.successColor,
                    backgroundColor: ColorSkin.successBackground,
                }
            case 5:
                return {
                    color: ColorSkin.textColorGrey1,
                    backgroundColor: ColorSkin.disabledBackground,
                }
            case 6:
                return {
                    color: '#fff',
                    backgroundColor: ColorSkin.primary,
                }
            default:
                return {
                    color: ColorSkin.primary,
                    backgroundColor: ColorSkin.backgroundPrimary,
                }
        }
    }
    return onPress ? <TouchableOpacity style={{ borderRadius: style.borderRadius ?? 100 }} onPress={onPress}><Text style={[styles.primary, getStatusStyles(status), style]}>{title}</Text></TouchableOpacity> :
        <Text style={[styles.primary, getStatusStyles(status), style]}>{title}</Text>
}

const styles = StyleSheet.create({
    primary: {
        ...TypoSkin.buttonText5,
        textAlign: 'center',
        borderRadius: 100,
        paddingVertical: 4,
        alignItems: 'center',
        width: 80
    }
})