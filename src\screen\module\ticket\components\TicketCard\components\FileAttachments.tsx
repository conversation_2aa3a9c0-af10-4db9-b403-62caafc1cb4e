import React from 'react';
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {SkeletonImage} from '../../../../../../project-component/skeleton-img';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import ConfigAPI from '../../../../../../config/configApi';

interface FileAttachmentsProps {
  fileInfor?: Array<any>;
  onFilePress: (file: any) => void;
}

export const FileAttachments: React.FC<FileAttachmentsProps> = ({
  fileInfor,
  onFilePress,
}) => {
  if (!fileInfor?.length) return null;

  return (
    <>
      <Text style={styles.attachmentLabel} numberOfLines={3}>
        {`Tệp đính kèm: `}
      </Text>
      <ScrollView style={styles.scrollContainer} horizontal={true}>
        {fileInfor.map((f: any, fIndex: number) => (
          <TouchableOpacity
            onPress={() => onFilePress(f)}
            style={styles.fileContainer}
            key={f.Id}>
            {f.Type?.includes('image') ? (
              <SkeletonImage
                src={ConfigAPI.imgUrlId + f.Id}
                style={styles.imageFile}
              />
            ) : (
              <Text numberOfLines={1} style={styles.fileText}>
                Tệp đính kèm: {f.Name}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  attachmentLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  scrollContainer: {
    flex: 1,
    width: '100%',
  },
  fileContainer: {
    height: 100,
    width: 100,
    marginLeft: 8,
  },
  imageFile: {
    height: 100,
    width: 100,
    objectFit: 'contain',
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    borderWidth: 1,
  },
  fileText: {
    color: ColorThemes.light.neutral_text_body_color,
  },
});
