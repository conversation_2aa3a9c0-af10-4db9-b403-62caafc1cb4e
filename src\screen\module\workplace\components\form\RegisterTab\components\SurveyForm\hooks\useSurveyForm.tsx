import React, {useEffect, useRef, useState} from 'react';

interface UseSurveyFormParams {
  methods: any;
  uploadFiles: (files: any[]) => Promise<void>;
  initSurvey: any;
}

interface UseSurveyFormResult {
  popupRef: React.MutableRefObject<any>;
  tab: number;
  setTab: (index: number) => void;
}

export const useSurveyForm = (
  params: UseSurveyFormParams,
): UseSurveyFormResult => {
  const {methods, uploadFiles, initSurvey} = params;

  const popupRef = useRef<any>();
  const [tab, setTab] = useState(0);

  useEffect(() => {
    if (initSurvey) {
      Object.keys(initSurvey).forEach((key: any) => {
        methods.setValue(key, initSurvey[key]);
      });
    }
  }, [initSurvey, methods]);

  return {
    popupRef,
    tab,
    setTab,
  };
};
