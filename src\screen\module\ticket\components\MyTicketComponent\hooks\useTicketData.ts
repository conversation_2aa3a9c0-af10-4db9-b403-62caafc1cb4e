import {useState, useCallback, useEffect} from 'react';
import {DataController} from 'screen/base-controller';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {BaseDA} from 'screen/baseDA';
import {ComponentStatus} from 'component/component-status';
import {showSnackbar} from 'component/export-component';

interface TicketData {
  data: any[];
  totalCount?: number;
}

interface UseTicketDataProps {
  searchValue: string;
  setResult: (result: any[]) => void;
  methods: any;
}

export const useTicketData = ({
  searchValue,
  setResult,
  methods,
}: UseTicketDataProps) => {
  const user = useSelectorCustomerState().data;
  const [isLoading, setLoading] = useState<boolean>(true);
  const [data, setData] = useState<TicketData>({
    data: [],
    totalCount: undefined,
  });
  const [relatives, setRelatives] = useState<Array<any>>([]);

  const ticketController = new DataController('Ticket');

  const fetchTickets = useCallback(async () => {
    try {
      if (!user) return;

      setLoading(true);
      let query = `@CustomerId:{${user?.Id}}`;
      if (searchValue?.length) {
        query += ` (@Name:(*${searchValue}*)) | (@Description:(*${searchValue}*)) | (@Mobile:(*${searchValue}*)))`;
      }

      const res = await ticketController.aggregateList({
        page: 1,
        size: 1000,
        searchRaw: query.length ? query : '*',
        sortby: [{prop: 'DateCreated', direction: 'DESC'}],
      });

      if (res.code === 200) {
        const existingFiles = methods.getValues('_files') ?? [];
        const newFileIds = res.data
          .flatMap((e: any) => e?.File?.split(','))
          .filter(
            (id: any) =>
              id?.length &&
              !existingFiles.some((e: any) => e.Id === id) &&
              !id.startsWith('http'),
          );

        if (newFileIds.length > 0) {
          const resFile = await BaseDA.getFilesInfor(newFileIds);
          if (resFile.code === 200) {
            methods.setValue('_files', [
              ...existingFiles,
              ...resFile.data.filter((e: any) => e !== undefined && e !== null),
            ]);
          }
        }

        setData({data: res.data, totalCount: res.totalCount});
        setResult(res.data.map((e: any) => e.Status));
      }
    } catch (error) {
      showSnackbar({
        message: 'Lỗi khi lấy dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  }, [user, searchValue, methods, ticketController, setResult]);

  const fetchRelatives = useCallback(async () => {
    const toiletIds = [
      ...new Set(data.data.map(e => e.ToiletId).filter(id => id)),
    ];
    const toiletServicesIds = [
      ...new Set(data.data.map(e => e.ToiletServicesId).filter(id => id)),
    ];

    const results = [];
    if (toiletIds.length) {
      const toiletController = new DataController('Toilet');
      const res = await toiletController.getByListId(toiletIds);
      if (res.code === 200) results.push(...res.data);
    }

    if (toiletServicesIds.length) {
      const toiletServicesController = new DataController('ToiletServices');
      const res = await toiletServicesController.getByListId(toiletServicesIds);
      if (res.code === 200) results.push(...res.data);
    }

    setRelatives(results);
  }, [data.data]);

  const onUpdateTicket = useCallback(
    (updatedTicket: any) => {
      // Update the local state with the updated ticket
      setData(prevData => ({
        ...prevData,
        data: prevData.data.map((ticket: any) =>
          ticket.Id === updatedTicket.Id ? updatedTicket : ticket,
        ),
      }));

      // Update the result array for parent component
      const updatedResults = data.data.map((ticket: any) =>
        ticket.Id === updatedTicket.Id ? updatedTicket.Status : ticket.Status,
      );
      setResult(updatedResults);
    },
    [data.data, setResult],
  );

  useEffect(() => {
    if (user) {
      fetchTickets();
    }
  }, [user, searchValue]);

  useEffect(() => {
    if (data.data.length) {
      fetchRelatives();
    }
  }, [data.data.length]);

  return {
    isLoading,
    data,
    relatives,
    fetchTickets,
    onUpdateTicket,
  };
};
