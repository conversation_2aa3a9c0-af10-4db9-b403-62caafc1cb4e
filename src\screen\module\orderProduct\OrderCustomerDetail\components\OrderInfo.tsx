import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {OrderInfoProps} from '../types';

const OrderInfo: React.FC<OrderInfoProps> = ({
  isLoading,
  isSearching,
  dataLength,
  searchTerm,
}) => {
  const getOrderCountText = () => {
    if (isLoading || isSearching) {
      return 'Đang tải dữ liệu...';
    }

    if (dataLength > 0) {
      return `${dataLength} đơn hàng${searchTerm.trim() ? ' (đã tìm thấy)' : ''}`;
    }

    if (searchTerm.trim()) {
      return 'Không tìm thấy đơn hàng nào';
    }

    return 'Chưa có đơn hàng nào';
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}><PERSON>h sách đơn hàng</Text>
      <Text style={styles.numberOrder}>{getOrderCountText()}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    marginLeft: 12,
    marginTop: 8,
  },
  title: {
    ...TypoSkin.title3,
    fontWeight: '500',
    fontFamily: 'roboto',
  },
  numberOrder: {
    ...TypoSkin.title4,
    marginTop: 4,
    color: '#999',
    marginBottom: 8,
  },
});

export default OrderInfo;
