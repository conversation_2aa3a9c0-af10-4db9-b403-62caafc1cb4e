/**
 * Example usage of ToiletDa class
 * This file demonstrates how to use the create, update, and other methods
 */

import { ToiletDa } from './toiletDa';
import { ToiletItem } from '../../../types/toiletType';

// Create an instance of ToiletDa
const toiletDa = new ToiletDa();

/**
 * Example: Create a new toilet
 */
export const createToiletExample = async () => {
  const newToiletData: Partial<ToiletItem> = {
    Name: 'Nhà vệ sinh tầng 1',
    CustomerId: 'customer-id-123',
    Description: 'Nhà vệ sinh dành cho khách hàng tầng 1',
    Address: '123 Đường ABC, Quận 1, TP.HCM',
    Lat: 10.762622,
    Long: 106.660172,
    Mobile: '0901234567',
    Status: 1,
    Certificate: 1
  };

  try {
    const result = await toiletDa.create(newToiletData);
    
    if (result.code === 200) {
      console.log('✅ Toilet created successfully:', result.data);
      return result.data;
    } else {
      console.error('❌ Failed to create toilet:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Error creating toilet:', error);
    return null;
  }
};

/**
 * Example: Update an existing toilet
 */
export const updateToiletExample = async (toiletId: string) => {
  const updateData: Partial<ToiletItem> & { Id: string } = {
    Id: toiletId,
    Name: 'Nhà vệ sinh tầng 1 - Đã cập nhật',
    Description: 'Nhà vệ sinh đã được nâng cấp và cải thiện',
    Status: 2
  };

  try {
    const result = await toiletDa.update(updateData);
    
    if (result.code === 200) {
      console.log('✅ Toilet updated successfully:', result.data);
      return result.data;
    } else {
      console.error('❌ Failed to update toilet:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Error updating toilet:', error);
    return null;
  }
};

/**
 * Example: Get toilet by ID
 */
export const getToiletByIdExample = async (toiletId: string) => {
  try {
    const result = await toiletDa.getById(toiletId);
    
    if (result.code === 200) {
      console.log('✅ Toilet found:', result.data);
      return result.data;
    } else {
      console.error('❌ Toilet not found:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Error getting toilet:', error);
    return null;
  }
};

/**
 * Example: Get toilets by customer
 */
export const getToiletsByCustomerExample = async (customerId: string) => {
  try {
    const toilets = await toiletDa.fetchByCustomer(customerId);
    console.log('✅ Customer toilets:', toilets);
    return toilets;
  } catch (error) {
    console.error('❌ Error getting customer toilets:', error);
    return [];
  }
};

/**
 * Example: Get all toilets with pagination
 */
export const getAllToiletsExample = async () => {
  try {
    const result = await toiletDa.getAll({
      page: 1,
      size: 10,
      query: '*',
      sortby: { BY: 'DateCreated', DIRECTION: 'DESC' }
    });
    
    if (result.code === 200) {
      console.log('✅ All toilets:', result.data);
      return result.data;
    } else {
      console.error('❌ Failed to get toilets:', result.message);
      return [];
    }
  } catch (error) {
    console.error('❌ Error getting all toilets:', error);
    return [];
  }
};

/**
 * Example: Delete a toilet
 */
export const deleteToiletExample = async (toiletId: string) => {
  try {
    const result = await toiletDa.delete(toiletId);
    
    if (result.code === 200) {
      console.log('✅ Toilet deleted successfully');
      return true;
    } else {
      console.error('❌ Failed to delete toilet:', result.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error deleting toilet:', error);
    return false;
  }
};

/**
 * Complete workflow example
 */
export const completeWorkflowExample = async () => {
  console.log('🚀 Starting complete toilet workflow example...');
  
  // 1. Create a new toilet
  const newToilet = await createToiletExample();
  if (!newToilet) return;
  
  // 2. Get the created toilet by ID
  const retrievedToilet = await getToiletByIdExample(newToilet.Id);
  if (!retrievedToilet) return;
  
  // 3. Update the toilet
  const updatedToilet = await updateToiletExample(newToilet.Id);
  if (!updatedToilet) return;
  
  // 4. Get all toilets
  await getAllToiletsExample();
  
  // 5. Get toilets by customer
  await getToiletsByCustomerExample(newToilet.CustomerId);
  
  // 6. Delete the toilet (optional - uncomment if needed)
  // await deleteToiletExample(newToilet.Id);
  
  console.log('✅ Complete workflow finished successfully!');
};
