import React from 'react';
import {StyleSheet} from 'react-native';
import AppButton from '../../../../../../../component/button';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import WScreenFooter from '../../../../../../layout/footer';

interface Props {
  onAdd: () => void;
}

export default function AddDeviceFooter({onAdd}: Props) {
  return (
    <WScreenFooter style={styles.footer}>
      <AppButton
        title={'Thêm thiết bị'}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={styles.buttonContainer}
        onPress={onAdd}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
    </WScreenFooter>
  );
}

const styles = StyleSheet.create({
  footer: {paddingHorizontal: 16},
  buttonContainer: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
