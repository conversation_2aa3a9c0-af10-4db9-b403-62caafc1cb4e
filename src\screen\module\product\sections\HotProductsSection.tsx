import React, {useEffect, useState, useCallback} from 'react';
import {View, ActivityIndicator, Text, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {productAction} from '../../../../redux/actions/productAction';
import {RootScreen} from '../../../../router/router';
import {ColorThemes} from '../../../../assets/skin/colors';
import HotProductsRow from '../view/HotProductsRow';

interface HotProductsSectionProps {
  title?: string;
  pageSize?: number;
  onSeeAll?: () => void;
  onRefresh?: boolean;
}

const HotProductsSection: React.FC<HotProductsSectionProps> = ({
  title = 'Sản phẩm HOT',
  pageSize = 10,
  onSeeAll,
  onRefresh = false,
}) => {
  const navigation = useNavigation<any>();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Hàm lấy dữ liệu sản phẩm HOT
  const fetchHotProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await productAction.find({
        page: 1,
        size: pageSize * 10,
      });
      if (data.length) {
        // Chuyển đổi dữ liệu từ API sang định dạng SquareProductItem
        const formattedProducts: any[] = data.map((item: any) => ({
          Id: item.Id || '',
          Name: item.Name || 'Tên sản phẩm',
          Price: item.Price || 0,
          Img: item.Img || '',
          rating: item.Rating || item.rating || 0, // Sử dụng tên thuộc tính đúng
          soldCount: item.Sold || item.soldCount || 0, // Sử dụng tên thuộc tính đúng
          Discount: item.Discount || 0,
          Description: item.Description || '',
        }));
        setProducts(formattedProducts);
      } else {
        setError('Không thể tải dữ liệu sản phẩm');
      }
    } catch (err) {
      console.error('Error fetching hot products:', err);
      setError('Đã xảy ra lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  // useEffect để xử lý onRefresh
  useEffect(() => {
    if (onRefresh) {
      fetchHotProducts();
    }
  }, [onRefresh]);

  useEffect(() => {
    fetchHotProducts();
  }, []);

  // Xử lý khi nhấn vào sản phẩm
  const handleProductPress = (product: any) => {
    navigation.navigate(RootScreen.DetailProductPage, {id: product.Id});
  };

  // Xử lý khi nhấn vào nút "Xem thêm"
  const handleSeeAll = () => {
    if (onSeeAll) {
      onSeeAll();
    }
  };

  // Hiển thị loading indicator khi đang tải dữ liệu
  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  // Hiển thị thông báo nếu không có sản phẩm
  if (products.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.noProductText}>Không có sản phẩm HOT</Text>
      </View>
    );
  }

  // Hiển thị danh sách sản phẩm HOT
  return (
    <HotProductsRow
      title={title}
      products={products}
      onSeeAll={handleSeeAll}
      onProductPress={handleProductPress}
      showRating={true}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  errorText: {
    color: ColorThemes.light.error_main_color,
  },
  noProductText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default HotProductsSection;
