import { Pressable, ScrollView, StyleSheet, View } from 'react-native';
import ScreenHeader from '../layout/header';
import { ColorThemes } from '../../assets/skin/colors';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../redux/hooks/hooks';
import { useMemo } from 'react';
import ChartById from './report/ChartById';
import ConfigAPI from '../../config/configApi';
import { CustomerRole, CustomerType } from '../../redux/reducers/user/da';

export default function Dashboard() {
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const company = useSelectorCustomerCompanyState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const onLoading = useSelectorCustomerCompanyState().onLoading

  const filterByCustomerId = useMemo(() => {
    const list = []
    if (user) {
      if (user.Type === CustomerType.partner) list.push(user.Id)
      if (owner?.Type === CustomerType.partner && userRole?.Role.includes(CustomerRole.Coordinator)) list.push(owner.Id)
    }
    return list
  }, [user, owner, userRole])

  return (
    <View style={{ flex: 1, backgroundColor: ColorThemes.light.neutral_absolute_background_color }}>
      <ScreenHeader title="Báo cáo" />
      <ScrollView style={{ height: "100%", flex: 1 }}>
        <Pressable style={{ flex: 1, gap: 16 }}>
          {(user && !onLoading) ? (
            <Pressable style={styles.container}>
              <ChartById
                id="7fc6a1f2adff4117a4dd457fa31b471f"
                searchRaw={
                  company?.Id !== ConfigAPI.ktxCompanyId
                    ? `@CustomerId:{${user?.Id}}`
                    : ''
                }
              />
            </Pressable>
          ) : null}
          {(filterByCustomerId.length && !onLoading) ? (
            <Pressable style={styles.container}>
              <ChartById
                id="a8e388ec693f4799b388aab8cdc9ae59"
                searchRaw={company?.Id !== ConfigAPI.ktxCompanyId ? `@CustomerId:{${filterByCustomerId.join(" | ")}}` : ""}
              />
            </Pressable>
          ) : null}
          {(filterByCustomerId.length && !onLoading) ? (
            <Pressable style={styles.container}>
              <ChartById
                id="dc12b02387144350a9199defbefdfe14"
                searchRaw={company?.Id !== ConfigAPI.ktxCompanyId ? `@CustomerId:{${filterByCustomerId.join(" | ")}}` : ""}
              />
            </Pressable>
          ) : null}
          {(filterByCustomerId.length && !onLoading) ? (
            <Pressable style={styles.container}>
              <ChartById
                id="4011ce1e9e1c4630a68ab0bc7b8b542a"
                searchRaw={company?.Id !== ConfigAPI.ktxCompanyId ? `@CustomerId:{${filterByCustomerId.join(" | ")}}` : ""}
              />
            </Pressable>
          ) : null}

          {(filterByCustomerId.length && !onLoading) ? (<Pressable style={styles.container}>
            <ChartById
              id="74c3e943da5e49a591baf84bf20fee49"
              searchRaw={company?.Id !== ConfigAPI.ktxCompanyId ? `@CustomerId:{${filterByCustomerId.join(" | ")}}` : ""}
            />
          </Pressable>) : null}

          {(company?.Id === ConfigAPI.ktxCompanyId && (user?.Id === ConfigAPI.adminKtxId || userRole?.Role.includes(CustomerRole.KTX))) ? <Pressable style={styles.container}><ChartById
            filterAll
            id="558002a1f7a2439496e1b7a4e226a6ad"
          /> </Pressable> : null}
          {(company?.Id !== ConfigAPI.ktxCompanyId) ? null : (
            <Pressable style={styles.container}>
              <ChartById
                filterAll={true}
                id="e1f5879c0efa404bb3e4a16dc07a4366"
              />
            </Pressable>
          )}
          {(filterByCustomerId.length && !onLoading) ? (
            <Pressable style={styles.container}>
              <ChartById
                id="6029e42771f24132b784293d64785a47"
                searchRaw={company?.Id !== ConfigAPI.ktxCompanyId ? `@CustomerId:{${filterByCustomerId.join(" | ")}}` : ""}
              />
            </Pressable>
          ) : null}
        </Pressable>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.white,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  chartContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  centerText: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  totalText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
  },
  legend: {
    marginTop: 20,
    width: '80%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  legendColor: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    marginRight: 10,
  },
  legendText: {
    fontSize: 14,
    color: '#7F7F7F',
  },
});
