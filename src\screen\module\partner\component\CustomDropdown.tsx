import {useState} from 'react';
import {Controller} from 'react-hook-form';
import {Modal, TouchableOpacity, View} from 'react-native';
import {Text} from 'react-native-paper';
import {AppSvg, Winicon} from 'wini-mobile-components';
import {FlatList} from 'react-native-gesture-handler';
import {ColorThemes} from '../../../../assets/skin/colors';
import iconSvg from '../../../../svgs/iconSvg';

export const CustomDropdown = ({
  control,
  name,
  placeholder,
  options,
  required = false,
  errors,
  style = {},
}: {
  control?: any;
  name: string;
  placeholder: string;
  options: Array<{id: number; name: string}>;
  required?: boolean;
  errors?: any;
  style?: any;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => {
        // Tìm item tương ứng với giá trị hiện tại
        const currentItem = options.find(item => item.id === field.value);

        return (
          <View style={[{flex: 1}, style]}>
            <TouchableOpacity
              onPress={() => setIsVisible(true)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: 12,
                height: 48,
                borderWidth: 1,
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderRadius: 8,
                backgroundColor: ColorThemes.light.white,
              }}>
              <Text
                style={{
                  flex: 1,
                  fontFamily: 'Inter',
                  fontSize: 14,
                  fontWeight: '400',
                  lineHeight: 22,
                  color: currentItem
                    ? ColorThemes.light.neutral_text_title_color
                    : ColorThemes.light.neutral_text_subtitle_color,
                }}>
                {currentItem ? currentItem.name : placeholder}
              </Text>
              <AppSvg
                SvgSrc={iconSvg.dropdownMenu}
                size={18}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
            </TouchableOpacity>
            <Modal
              visible={isVisible}
              transparent={true}
              animationType="fade"
              onRequestClose={() => setIsVisible(false)}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                activeOpacity={1}
                onPress={() => setIsVisible(false)}>
                <View
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 8,
                    width: '80%',
                    maxHeight: '60%',
                  }}>
                  <FlatList
                    data={options}
                    keyExtractor={item => item.id.toString()}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={{
                          paddingVertical: 15,
                          paddingHorizontal: 20,
                          borderBottomWidth: 0.5,
                          borderBottomColor: '#eee',
                        }}
                        onPress={() => {
                          field.onChange(item.id);
                          setIsVisible(false);
                        }}>
                        <Text
                          style={{
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: '400',
                            lineHeight: 22,
                            color:
                              field.value === item.id
                                ? ColorThemes.light.primary_main_color
                                : '#00474F', // Same as TypoSkin.body3.color
                          }}>
                          {item.name}
                        </Text>
                      </TouchableOpacity>
                    )}
                  />
                </View>
              </TouchableOpacity>
            </Modal>

            {errors[name] && (
              <Text
                style={{
                  color: 'red',
                  fontSize: 12,
                  marginTop: 4,
                  fontFamily: 'Roboto',
                }}>
                {errors[name].message ||
                  `Vui lòng chọn ${placeholder.toLowerCase()}`}
              </Text>
            )}
          </View>
        );
      }}
    />
  );
};

export const CustomDropdownWithAvata = ({
  control,
  name,
  placeholder,
  options,
  required = false,
  errors,
  style = {},
}: {
  control: any;
  name: string;
  placeholder: string;
  options: Array<{id: number; name: string; phone?: string; avatar?: string}>;
  required?: boolean;
  errors: any;
  style?: any;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => {
        // Tìm item tương ứng với giá trị hiện tại
        const currentItem = options.find(item => item.id === field.value);

        return (
          <View style={[{flex: 1}, style]}>
            <TouchableOpacity
              onPress={() => setIsVisible(true)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 12,
                paddingVertical: 12,
                borderWidth: 1,
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderRadius: 8,
                backgroundColor: ColorThemes.light.white,
              }}>
              {currentItem ? (
                <>
                  <View
                    style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: '#E6F7FF',
                      borderWidth: 2,
                      borderColor: '#1890FF',
                      marginRight: 12,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: '600',
                        color: '#1890FF',
                      }}>
                      {currentItem.avatar || currentItem.name.charAt(0)}
                    </Text>
                  </View>
                  <View style={{flex: 1}}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: ColorThemes.light.neutral_text_title_color,
                        marginBottom: 2,
                      }}>
                      {currentItem.name}
                    </Text>
                    {currentItem.phone && (
                      <Text
                        style={{
                          fontSize: 14,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>
                        {currentItem.phone}
                      </Text>
                    )}
                  </View>
                </>
              ) : (
                <Text
                  style={{
                    flex: 1,
                    fontFamily: 'Inter',
                    fontSize: 14,
                    fontWeight: '400',
                    lineHeight: 22,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  {placeholder}
                </Text>
              )}
              <AppSvg
                SvgSrc={iconSvg.dropdownMenu}
                size={18}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
            </TouchableOpacity>
            <Modal
              visible={isVisible}
              transparent={true}
              animationType="fade"
              onRequestClose={() => setIsVisible(false)}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                activeOpacity={1}
                onPress={() => setIsVisible(false)}>
                <View
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 8,
                    width: '80%',
                    maxHeight: '60%',
                  }}>
                  <FlatList
                    data={options}
                    keyExtractor={item => item.id.toString()}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          paddingVertical: 12,
                          paddingHorizontal: 16,
                          borderBottomWidth: 0.5,
                          borderBottomColor: '#eee',
                        }}
                        onPress={() => {
                          field.onChange(item.id);
                          setIsVisible(false);
                        }}>
                        <View
                          style={{
                            width: 32,
                            height: 32,
                            borderRadius: 16,
                            backgroundColor: '#E6F7FF',
                            borderWidth: 2,
                            borderColor: '#1890FF',
                            marginRight: 12,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              fontSize: 12,
                              fontWeight: '600',
                              color: '#1890FF',
                            }}>
                            {item.avatar || item.name.charAt(0)}
                          </Text>
                        </View>
                        <View style={{flex: 1}}>
                          <Text
                            style={{
                              fontSize: 14,
                              fontWeight: '500',
                              color:
                                field.value === item.id
                                  ? ColorThemes.light.primary_main_color
                                  : ColorThemes.light.neutral_text_title_color,
                              marginBottom: item.phone ? 2 : 0,
                            }}>
                            {item.name}
                          </Text>
                          {item.phone && (
                            <Text
                              style={{
                                fontSize: 12,
                                color:
                                  ColorThemes.light.neutral_text_subtitle_color,
                              }}>
                              {item.phone}
                            </Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    )}
                  />
                </View>
              </TouchableOpacity>
            </Modal>

            {errors[name] && (
              <Text
                style={{
                  color: 'red',
                  fontSize: 12,
                  marginTop: 4,
                  fontFamily: 'Roboto',
                }}>
                {errors[name].message ||
                  `Vui lòng chọn ${placeholder.toLowerCase()}`}
              </Text>
            )}
          </View>
        );
      }}
    />
  );
};

export const CustomDropdownWithDate = ({
  control,
  name,
  placeholder,
  options,
  required = false,
  errors,
  style = {},
}: {
  control: any;
  name: string;
  placeholder: string;
  options: Array<{id: number; name: string}>;
  required?: boolean;
  errors: any;
  style?: any;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => {
        // Tìm item tương ứng với giá trị hiện tại
        const currentItem = options.find(item => item.id === field.value);

        return (
          <View style={[{flex: 1}, style]}>
            <TouchableOpacity
              onPress={() => setIsVisible(true)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 12,
                paddingVertical: 12,
                borderWidth: 1,
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderRadius: 8,
                backgroundColor: ColorThemes.light.white,
              }}>
              <AppSvg
                SvgSrc={iconSvg.calendar}
                size={20}
                color={ColorThemes.light.neutral_text_subtitle_color}
                style={{marginRight: 12}}
              />
              <Text
                style={{
                  flex: 1,
                  fontFamily: 'Inter',
                  fontSize: 14,
                  fontWeight: '400',
                  lineHeight: 22,
                  color: currentItem
                    ? ColorThemes.light.neutral_text_title_color
                    : ColorThemes.light.neutral_text_subtitle_color,
                }}>
                {currentItem ? currentItem.name : placeholder}
              </Text>
              <AppSvg
                SvgSrc={iconSvg.dropdownMenu}
                size={20}
                color={ColorThemes.light.neutral_text_subtitle_color}
                style={{marginRight: 12}}
              />
            </TouchableOpacity>
            <Modal
              visible={isVisible}
              transparent={true}
              animationType="fade"
              onRequestClose={() => setIsVisible(false)}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                activeOpacity={1}
                onPress={() => setIsVisible(false)}>
                <View
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 8,
                    width: '80%',
                    maxHeight: '60%',
                  }}>
                  <FlatList
                    data={options}
                    keyExtractor={item => item.id.toString()}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          paddingVertical: 12,
                          paddingHorizontal: 16,
                          borderBottomWidth: 0.5,
                          borderBottomColor: '#eee',
                        }}
                        onPress={() => {
                          field.onChange(item.id);
                          setIsVisible(false);
                        }}>
                        <AppSvg
                          SvgSrc={iconSvg.calendar}
                          size={16}
                          color={ColorThemes.light.neutral_text_subtitle_color}
                          style={{marginRight: 12}}
                        />
                        <Text
                          style={{
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: '400',
                            lineHeight: 22,
                            color:
                              field.value === item.id
                                ? ColorThemes.light.primary_main_color
                                : ColorThemes.light.neutral_text_title_color,
                          }}>
                          {item.name}
                        </Text>
                      </TouchableOpacity>
                    )}
                  />
                </View>
              </TouchableOpacity>
            </Modal>

            {errors[name] && (
              <Text
                style={{
                  color: 'red',
                  fontSize: 12,
                  marginTop: 4,
                  fontFamily: 'Roboto',
                }}>
                {errors[name].message ||
                  `Vui lòng chọn ${placeholder.toLowerCase()}`}
              </Text>
            )}
          </View>
        );
      }}
    />
  );
};
