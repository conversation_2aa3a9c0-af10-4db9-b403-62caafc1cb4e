import React from 'react';
import {View, ScrollView, StyleSheet} from 'react-native';
import {FBottomSheet, FDialog} from 'wini-mobile-components';
import {SafeAreaView} from 'react-native-safe-area-context';
import TitleHeader from 'screen/layout/headers/TitleHeader';

// Import new components
import StoreGroup from './components/StoreGroup';
import CheckoutBottomBar from './components/CheckoutBottomBar';
import OrderSuccessMessage from './components/OrderSuccessMessage';
import LoadingIndicator from './components/LoadingIndicator';

// Import hooks
import {useCheckout} from './hooks/useCheckout';
import {useStoreGroups} from './hooks/useStoreGroups';
import {useOrderProcessing} from './hooks/useOrderProcessing';
import RecipientInfo from '../CartPage/components/RecipientInfo';
import {navigateReset, RootScreen} from 'router/router';
import {useNavigation} from '@react-navigation/native';
import {StatusOrder} from 'config/Contanst';

const CheckoutPage: React.FC = () => {
  const navigation = useNavigation<any>();
  // Use custom hooks
  const checkoutHook = useCheckout();
  const {
    items,
    address,
    customer,
    customerAddress,
    isProcessing,
    isDone,
    payment,
    setPayment,
    dialogRef,
    btsRef,
    setIsProcessing,
    setDone,
  } = checkoutHook as any;

  const {storeGroups, loadingStoreGroups, totalPrice} = useStoreGroups(items);

  const orderProcessing = useOrderProcessing({
    items,
    storeGroups,
    customer,
    customerAddress,
    address,
    payment,
    dialogRef,
    setIsProcessing,
    setDone,
  });

  // Navigation handlers
  const handleNavigateHome = () => {
    navigateReset(RootScreen.navigateView);
  };

  const handleNavigateOrders = () => {
    navigation.replace(RootScreen.OrderCustomerDetail, {
      status: StatusOrder.new,
    });
  };

  const handleContinueShopping = () => {
    navigateReset(RootScreen.navigateView, {
      screen: 'products',
    });
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <FBottomSheet ref={btsRef} />
      <FDialog ref={dialogRef} />
      <TitleHeader title="Thanh toán và đặt hàng" />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        <OrderSuccessMessage isDone={isDone} />
        <RecipientInfo dialogRef={dialogRef} isDone={isDone} />

        <LoadingIndicator isLoading={loadingStoreGroups} />

        {!loadingStoreGroups &&
          storeGroups.map((group, index) => (
            <StoreGroup
              key={group.ShopId + index}
              storeGroup={group}
              index={index}
              payment={payment}
              isDone={isDone}
              onPaymentChange={setPayment}
              btsRef={btsRef}
            />
          ))}

        <View style={styles.bottomSpacer} />
      </ScrollView>

      <CheckoutBottomBar
        totalPrice={totalPrice}
        isProcessing={isProcessing}
        isDone={isDone}
        onSubmitOrder={orderProcessing.handlePlaceOrder}
        onNavigateHome={handleNavigateHome}
        onNavigateOrders={handleNavigateOrders}
        onContinueShopping={handleContinueShopping}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 16,
  },
  bottomSpacer: {
    height: 100,
  },
});

export default CheckoutPage;
