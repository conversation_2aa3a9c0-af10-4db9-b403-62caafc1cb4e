import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {StatusOrder} from '../../../../../../../config/Contanst';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../../assets/skin/typography';

interface OrderStatusTimelineProps {
  currentStatus: number;
  order: any;
}

const OrderStatusTimeline: React.FC<OrderStatusTimelineProps> = ({
  currentStatus,
  order,
}) => {
  if (currentStatus == StatusOrder.cancel) {
    return (
      <View style={{...styles.timelineContainer, alignItems: 'center', gap: 8}}>
        <Winicon
          src="fill/location/route-close"
          size={36}
          color={ColorThemes.light.error_main_color}
        />
        <Text style={styles.timelineLabel}>Đơn hàng đã hủy</Text>
      </View>
    );
  }

  return (
    <View style={styles.timelineContainer}>
      {/* <PERSON><PERSON><PERSON> điểm trạng thái */}
      <View style={styles.timelinePoints}>
        {/* Đặt hàng */}
        <View style={styles.timelinePointWrapper}>
          <View
            style={[
              styles.timelinePoint,
              currentStatus >= StatusOrder.new
                ? styles.timelinePointActive
                : {},
            ]}>
            {currentStatus >= StatusOrder.new && (
              <Winicon
                src="fill/user interface/check"
                size={12}
                color="#FFFFFF"
              />
            )}
          </View>
          <Text style={styles.timelineTime}>
            {order?.DateCreated || order?.DateUpdated
              ? new Date(
                  order?.DateUpdated || order?.DateCreated,
                ).toLocaleTimeString('vi-VN', {
                  hour: '2-digit',
                  minute: '2-digit',
                })
              : ''}
          </Text>
          <Text style={styles.timelineLabel}>Đặt hàng</Text>
        </View>

        {/* Đường nối */}
        <View
          style={[
            styles.timelineConnector,
            currentStatus >= StatusOrder.proccess
              ? styles.timelineConnectorActive
              : {},
          ]}
        />

        {/* Chờ lấy hàng */}
        <View style={styles.timelinePointWrapper}>
          <View
            style={[
              styles.timelinePoint,
              currentStatus >= StatusOrder.proccess
                ? styles.timelinePointActive
                : {},
            ]}>
            {currentStatus >= StatusOrder.proccess && (
              <Winicon
                src="fill/user interface/check"
                size={12}
                color="#FFFFFF"
              />
            )}
          </View>
          <Text style={styles.timelineTime}>
            {order?.DateProcess && currentStatus >= StatusOrder.proccess
              ? new Date(order?.DateProcess).toLocaleTimeString('vi-VN', {
                  hour: '2-digit',
                  minute: '2-digit',
                })
              : '-'}
          </Text>
          <Text style={styles.timelineLabel}>Chờ lấy hàng</Text>
        </View>

        {/* Đường nối */}
        <View
          style={[
            styles.timelineConnector,
            currentStatus >= StatusOrder.proccess
              ? styles.timelineConnectorActive
              : {},
          ]}
        />

        {/* Đang vận chuyển */}
        <View style={styles.timelinePointWrapper}>
          <View
            style={[
              styles.timelinePoint,
              currentStatus == StatusOrder.success
                ? styles.timelinePointActive
                : {},
            ]}>
            {currentStatus == StatusOrder.success && (
              <Winicon
                src="fill/user interface/check"
                size={12}
                color="#FFFFFF"
              />
            )}
          </View>
          <Text style={styles.timelineTime}>
            {order?.DateProcess && currentStatus == StatusOrder.success
              ? new Date(order?.DateProcess).toLocaleTimeString('vi-VN', {
                  hour: '2-digit',
                  minute: '2-digit',
                })
              : '-'}
          </Text>
          <Text style={styles.timelineLabel}>Hoàn thành</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  timelineContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  timelinePoints: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  timelinePointWrapper: {
    alignItems: 'center',
    width: 70,
  },
  timelinePoint: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  timelinePointActive: {
    backgroundColor: '#4CAF50',
  },
  timelineConnector: {
    marginTop: 12,
    height: 1.5,
    backgroundColor: '#E0E0E0',
    flex: 1,
  },
  timelineConnectorActive: {
    backgroundColor: '#4CAF50',
  },
  timelineTime: {
    ...TypoSkin.body3,
    color: '#757575',
    marginBottom: 2,
  },
  timelineLabel: {
    ...TypoSkin.body3,
    color: '#212121',
    textAlign: 'center',
  },
});

export default OrderStatusTimeline;
