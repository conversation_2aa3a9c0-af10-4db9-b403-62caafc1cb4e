import {useState, useRef} from 'react';
import {useForm} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import {showDialog, ComponentStatus} from 'wini-mobile-components';
import {randomGID} from 'utils/Utils';
import {showSnackbar} from 'component/export-component';
import {CustomerActions} from 'redux/reducers/user/reducer';
import {CustomerItem, CustomerRole} from 'redux/reducers/user/da';
import {DataController} from 'screen/base-controller';

export const useCompanyRegistration = (
  user: any,
  companyProfile: any,
  refreshCompanyData: () => void,
) => {
  const dispatch = useDispatch<any>();
  const [isRegistering, setIsRegistering] = useState(false);
  const dialogRef = useRef<any>();

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID()},
  });

  const handleRegisterCompany = () => {
    methods.reset({Id: randomGID()});
    setIsRegistering(true);
  };

  const handleEditCompany = () => {
    if (companyProfile) {
      methods.reset(companyProfile);
    }
    setIsRegistering(true);
  };

  const handleCancelRegistration = () => {
    setIsRegistering(false);
    methods.reset({Id: randomGID()});
  };

  const onSubmit = (formData: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.INFOR,
      title:
        'Bạn chắc chắn muốn lưu hồ sơ doanh nghiệp theo các thông tin đã nhập?',
      onSubmit: async () => {
        const controller = new DataController('CompanyProfile');
        const isEditing = !!companyProfile?.Id;

        let res;
        if (isEditing) {
          res = await controller.edit([{...formData, DateUpdated: Date.now()}]);
        } else {
          res = await controller.add([{...formData, DateCreated: Date.now()}]);
        }

        if (res.code === 200) {
          if (isEditing) {
            setIsRegistering(false);
            showSnackbar({
              message: 'Đã cập nhật hồ sơ doanh nghiệp',
              status: ComponentStatus.SUCCSESS,
            });
            refreshCompanyData();
          } else {
            dispatch(
              CustomerActions.edit(dispatch, {
                ...user,
                CompanyProfileId: res.data[0].Id ?? randomGID(),
              } as CustomerItem).then(res => {
                if (res.code === 200) {
                  CustomerActions.addRole(dispatch, {
                    Id: randomGID(),
                    Name: user?.Name ?? '',
                    DateCreated: Date.now(),
                    Status: 1,
                    Role: CustomerRole.Owner,
                    Sort: 1,
                    Description: 'Chủ doanh nghiệp',
                    CustomerId: user?.Id ?? '',
                    CompanyProfileId:
                      res.data[0].CompanyProfileId ?? randomGID(),
                  });
                  setIsRegistering(false);
                  showSnackbar({
                    message: 'Đã lưu hồ sơ doanh nghiệp',
                    status: ComponentStatus.SUCCSESS,
                  });
                  refreshCompanyData();
                }
              }),
            );
          }
        } else {
          showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        }
      },
    });
  };

  const onError = (errors: any) => {
    console.log('Form validation errors:', errors);
  };

  return {
    isRegistering,
    dialogRef,
    methods,
    handleRegisterCompany,
    handleEditCompany,
    handleCancelRegistration,
    onSubmit,
    onError,
  };
};
