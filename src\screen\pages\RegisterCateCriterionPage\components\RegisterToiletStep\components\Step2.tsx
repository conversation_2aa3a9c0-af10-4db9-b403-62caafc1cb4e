import React from 'react';
import {View, Text, StyleSheet, ScrollView, TextInput} from 'react-native';
import {useTranslation} from 'react-i18next';

import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {Winicon} from '../../../../../../component/wini-icon/wini_icon';
import {validatePhoneNumber} from '../../../../../../utils/validate';
import {
  FAddressPickerForm,
  TextFieldForm,
} from '../../../../../../project-component/component-form';
import {useForm, Controller} from 'react-hook-form';
import {AppButton} from 'wini-mobile-components';

export interface FormData {
  Name: string;
  Phone: string;
  Address: string;
  Long: number;
  Lat: number;
}

interface Step2Props {
  onNext: (data: FormData) => void;
  defaultValues?: FormData;
}

export default function Step2({onNext, defaultValues}: Step2Props) {
  const {t} = useTranslation();
  const methods = useForm<FormData>({
    shouldFocusError: false,
    defaultValues: {
      Name: defaultValues?.Name || '',
      Phone: defaultValues?.Phone || '',
      Address: defaultValues?.Address || '',
      Long: defaultValues?.Long || 0,
      Lat: defaultValues?.Lat || 0,
    },
  });

  const handleSubmit = methods.handleSubmit(data => {
    onNext({
      Name: data.Name,
      Phone: data.Phone,
      Address: data.Address,
      Long: data.Long,
      Lat: data.Lat,
    });
  });

  const validatePhone = (value: string) => {
    if (!value || value.length === 0) {
      return 'Số điện thoại không được để trống';
    }

    let mobile = value.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const isValid = validatePhoneNumber(mobile);
    return isValid || 'Số điện thoại không hợp lệ';
  };

  return (
    <ScrollView contentContainerStyle={styles.stepContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Thông tin liên hệ</Text>
        <Text style={styles.subtitle}>
          Chúng tôi sẽ dùng thông tin này để liên hệ
        </Text>
      </View>

      <View style={styles.formContainer}>
        <TextFieldForm
          control={methods.control as any}
          name="Name"
          required
          label="Họ và tên"
          placeholder="Nhập họ và tên"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={styles.textFieldStyle}
          register={methods.register}
        />

        <Controller
          control={methods.control}
          name="Phone"
          rules={{
            required: 'Số điện thoại không được để trống',
            validate: validatePhone,
          }}
          render={({field}) => (
            <View style={{gap: 8}}>
              <Text style={styles.label}>Số điện thoại</Text>
              <View style={styles.phoneInputContainer}>
                <View style={styles.phonePrefix}>
                  <Text style={styles.phonePrefixText}>+84</Text>
                  <Winicon src="outline/arrows/down-arrow" size={16} />
                </View>
                <TextInput
                  style={{
                    flex: 1,
                    height: '100%',
                    paddingHorizontal: 12,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}
                  placeholder="Nhập số điện thoại của bạn"
                  value={field.value}
                  onChangeText={field.onChange}
                  keyboardType="number-pad"
                  returnKeyType="done"
                  placeholderTextColor={
                    ColorThemes.light.neutral_text_subtitle_reverse_color
                  }
                />
              </View>
              {methods.formState.errors.Phone && (
                <Text style={styles.errorText}>
                  {methods.formState.errors.Phone.message as string}
                </Text>
              )}
            </View>
          )}
        />

        <FAddressPickerForm
          control={methods.control as any}
          errors={methods.formState.errors}
          name="Address"
          label={t('Địa chỉ')}
          placeholder={t('Địa chỉ')}
          onChange={value => {
            methods.setValue('Long', value.geometry.location.lng);
            methods.setValue('Lat', value.geometry.location.lat);
            methods.setValue('Address', value.formatted_address || '');
            return value.formatted_address || '';
          }}
        />
      </View>

      <View style={styles.buttonContainer}>
        {/* Continue Button */}
        <AppButton
          title="Gửi thông tin"
          textStyle={styles.continueButtonText}
          containerStyle={styles.continueButtonContainer}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor={ColorThemes.light.primary_main_color}
          onPress={handleSubmit}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  stepContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
  },
  subtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  formContainer: {
    flex: 1,
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 12,
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  countryCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    height: 48,
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 12,
  },
  countryCodeText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  phoneInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 12,
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  addressInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingRight: 48,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  locationIcon: {
    position: 'absolute',
    right: 16,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  continueButtonText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
  },
  continueButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
  },
  textFieldStyle: {
    paddingLeft: 16,
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
  },
  phonePrefix: {
    flexDirection: 'row',
    height: 46,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
  },
  phonePrefixText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  phoneInputContainer: {
    height: 48,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 12,
    marginBottom: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  errorText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.error_main_color,
    marginTop: 4,
  },
});
