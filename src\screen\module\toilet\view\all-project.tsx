import { FlatList, View, Text } from "react-native";
import { ColorThemes } from "../../../../assets/skin/colors";
import ListTile from "../../../../component/list-tile/list-tile";
import AppButton from "../../../../component/button";
import { <PERSON><PERSON><PERSON><PERSON> } from "../../../../assets/skin/typography";
import { Winicon } from "../../../../component/export-component";
import { useNavigation } from "@react-navigation/native";
import { RootScreen } from "../../../../router/router";
import { DataController } from "../../../base-controller";
import { useEffect, useState } from "react";
import { CardToiletHoriSkeleton } from "../../../../project-component/skeletonCard";
import EmptyPage from "../../../../project-component/empty-page";
import { useSelectorCustomerState, useSelectorCustomerCompanyState } from "../../../../redux/hooks/hooks";

function sortData(data: any[]) {
    return data.sort((a, b) => {
        if (a.Favor === b.Favor) {
            return b.DateCreated - a.DateCreated; // Sort by DateCreated descending
        }
        return b.Favor - a.Favor; // Prioritize Favor items
    });
}

export default function AllProjects({ isRefresh }: any) {
    const navigation = useNavigation<any>()
    const pjController = new DataController("Toilet");
    const [projects, setProjects] = useState<Array<any>>([]);
    const [loading, setLoading] = useState(false);
    const user = useSelectorCustomerState().data;
    const company = useSelectorCustomerCompanyState().data;
    const owner = useSelectorCustomerCompanyState().owner;

    useEffect(() => {
        getData()
    }, [])

    useEffect(() => {
        if (isRefresh) {
            setProjects([])
            getData()
        }
    }, [isRefresh])

    const getData = async () => {
        setLoading(true)
        if (user || owner) {
            pjController.aggregateList({ page: 1, size: 5, searchRaw: `@CustomerId:{${user?.Id}${owner ? ` | ${owner.Id}` : ""}}` }).then(res => {
                if (res.code == 200) {
                    setLoading(false)
                    setProjects(sortData(res.data))
                } else {
                    setLoading(false)
                }
            })
        }
    }

    return <View style={{ flex: 1, backgroundColor: ColorThemes.light.neutral_main_background_color, marginTop: 32, borderRadius: 8 }}>
        <ListTile
            title="Nhà vệ sinh"
            style={{ borderRadius: 0, backgroundColor: ColorThemes.light.transparent }}
            titleStyle={[TypoSkin.heading7, { color: ColorThemes.light.neutral_text_title_color, }]}
            trailing={user || owner ? <AppButton onPress={() => {
                navigation.navigate(RootScreen.ToiletList)
            }} title={"Tất cả"} backgroundColor={ColorThemes.light.transparent} borderColor="transparent" textStyle={TypoSkin.buttonText3} textColor={ColorThemes.light.neutral_text_subtitle_color} suffixIcon={"outline/arrows/circle-arrow-right"} suffixIconSize={16} /> : null}
        />
        {projects.length == 0 && !loading ? <View style={{ flex: 1 }}>
            <EmptyPage hideImg /></View> : null}
        <FlatList
            scrollEnabled={false}
            // keyExtractor={item => "_" + item.id}
            data={projects}
            style={{ flex: 1, gap: 8, marginHorizontal: 16, marginBottom: 16 }}
            ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
            renderItem={({ item, index }) => <ListTile
                key={`pj-${index}`}
                onPress={() => {
                    navigation.push(RootScreen.detailProject, { item: item })
                }}
                style={{ padding: 16, gap: 8, backgroundColor: ColorThemes.light.white }}
                leading={<Winicon src={item?.Favor ? "fill/user interface/star" : "outline/user interface/star"} size={16} color={ColorThemes.light.secondary1_main_color} />}
                title={item?.Name ?? '-'}
                titleStyle={[TypoSkin.heading8, { color: ColorThemes.light.neutral_text_title_color }]}
            />}
            ListEmptyComponent={() => loading ? Array.from({ length: 3 }).map((_, index) => <CardToiletHoriSkeleton key={index} />) : <View />}
        />
    </View>
}