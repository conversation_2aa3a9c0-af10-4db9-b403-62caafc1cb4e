import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {AppSvg, Winicon} from 'wini-mobile-components';

interface MenuNotActionProps {
  title: string;
  svgIcon: string;
  setSelect: (value: string) => void;
  selected: boolean;
  type: string;
  numberOrder: number;
}

export const MenuNotAction = (props: MenuNotActionProps) => {
  let {title, svgIcon, setSelect, type, selected, numberOrder} = props;
  return (
    <TouchableOpacity
      style={styles.tabContainer}
      onPress={() => (setSelect ? setSelect(type) : null)}>
      {selected ? (
        <LinearGradient
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
          style={styles.tabContent}>
          <AppSvg SvgSrc={svgIcon} size={20} />
          <Text style={[styles.tabText, styles.activeTabText]}>{title}</Text>
          {title == 'Shop' && (
            <View
              style={{
                position: 'absolute',
                left: 10,
                top: 1,
                width: 16,
                height: 16,
                borderRadius: 8,
                backgroundColor: 'red',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{color: 'white', fontSize: 10}}>
                {numberOrder ? numberOrder : 0}
              </Text>
            </View>
          )}
        </LinearGradient>
      ) : (
        <View style={styles.tabContent}>
          <AppSvg SvgSrc={svgIcon} size={20} />
          <Text style={[styles.tabText, styles.inactiveTabText]}>{title}</Text>
          {title == 'Shop' && (
            <View
              style={{
                position: 'absolute',

                left: 10,
                top: 1,
                width: 16,
                height: 16,
                borderRadius: 8,
                backgroundColor: 'red',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{color: 'white', fontSize: 10}}>
                {numberOrder ? numberOrder : 0}
              </Text>
            </View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    borderRadius: 10,
    justifyContent: 'center',
    width: Dimensions.get('window').width / 4 - 8,
  },
  tabContent: {
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignContent: 'center',
    gap: 8,
    position: 'relative',
  },
  tabText: {
    ...TypoSkin.title3,
    paddingVertical: 4,
  },
  activeTabText: {
    color: ColorThemes.light.primary_main_color,
  },
  inactiveTabText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});
