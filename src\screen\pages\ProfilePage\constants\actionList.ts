import {ColorThemes} from '../../../../assets/skin/colors';
import {RootScreen} from '../../../../router/router';
import iconSvg from '../../../../svgs/iconSvg';

export interface ActionItem {
  id: number;
  name: string;
  icon: string;
  background: string;
  colorIcon: string;
  svg?: string;
  route?: string;
  action?: string;
  show?: boolean;
  type?: string;
}

export const actionList: ActionItem[] = [
  {
    id: 999,
    name: 'Giỏ hàng',
    icon: 'outline/shopping/shopping-cart',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.CartPage,
  },
  {
    id: 0,
    name: 'Thông tin cá nhân',
    icon: 'fill/users/profile',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.SettingProfile,
  },
  {
    id: 10,
    name: '<PERSON><PERSON> s<PERSON> doanh nghiệp',
    icon: 'fill/users/company',
    svg: iconSvg.updateBusiness,
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.SettingProfile,
    type: 'company',
  },
  // {
  //   id: 1,
  //   name: 'Xác thực tài khoản',
  //   icon: 'fill/users/profile',
  //   svg: iconSvg.otp,
  //   background: ColorThemes.light.neutral_main_background_color,
  //   colorIcon: '#00474f',
  //   // route: RootScreen.SettingProfile,
  // },
  {
    id: 2,
    name: 'Sản phẩm yêu thích',
    icon: 'outline/user interface/favorite',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.FavoriteProductPage,
  },
  {
    id: 3,
    name: 'Thông tin nhận hàng',
    icon: 'fill/location/pin-3',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.MyAddress,
  },
  {
    id: 4,
    name: 'Hỗ trợ',
    show: true,
    icon: 'fill/layout/circle-question',
    svg: iconSvg.faq,
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.FAQView,
  },
  {
    id: 5,
    name: 'Thiết lập sinh trắc học',
    action: 'biometric',
    icon: 'fill/technology/face-recognition',
    svg: iconSvg.Biometrics,
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
  },
  // {
  //   id: 6,
  //   name: 'Thay đổi ngôn ngữ',
  //   icon: 'fill/technology/lock-portrait',
  //   svg: iconSvg.changLanguage,
  //   background: ColorThemes.light.neutral_main_background_color,
  //   colorIcon: '#00474f',
  //   // route: RootScreen.TwoFactorAuth,
  // },
  {
    id: 7,
    name: 'Điều khoản ứng dụng',
    icon: 'fill/technology/lock-portrait',
    background: ColorThemes.light.neutral_main_background_color,
    svg: iconSvg.terms,
    colorIcon: '#00474f',
    route: RootScreen.PolicyView,
  },
  {
    id: 8,
    name: 'Đăng xuất',
    show: true,
    action: 'logout',
    icon: 'outline/arrows/logout',
    svg: iconSvg.logout,
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: '#00474f',
    route: RootScreen.login,
  },
];
