import {useCallback, useEffect, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useProductHook} from '../../../../redux/reducers/productReducer';
import {ComponentStatus} from 'wini-mobile-components';
import {RootState} from '../../../../redux/store/store';
import {navigate, RootScreen} from '../../../../router/router';
import {ProductItem} from '../../../../types/ProductType';
import productDA from '../../../module/product/productDA';
import {CartActions} from '../../../../redux/reducers/cart/CartReducer';
import {showSnackbar} from '../../../../component/export-component';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {updateArrayWithObjects} from 'utils/arrayUtils';

export const useListProductHook = () => {
  const dispatch = useDispatch();
  const productHook = useProductHook();
  const customer = useSelectorCustomerState().data;
  const {data, onLoading} = useSelector((state: RootState) => state.product);
  const {filter} = useSelector((state: RootState) => state.productByCategory);
  const abortControllerRef = useRef<AbortController | null>(null);

  // States cho load more
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchProduct = useCallback(
    async (pageNumber: number = 1, isLoadMore: boolean = false) => {
      try {
        // Cancel previous request if exists
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        // Create new abort controller
        abortControllerRef.current = new AbortController();

        if (isLoadMore) {
          setIsLoadingMore(true);
        }

        const result = await productDA.filterProductByCategories(
          {
            filter,
            pageNumber,
            size: 10,
          },
          customer?.Id,
        );

        // Handle data update based on load type
        if (result.length > 0) {
          if (isLoadMore) {
            // Append new data for load more
            productHook.setData('data', [...data, ...result]);
          } else {
            // Replace data for first load or refresh
            productHook.setData('data', result);
          }
          // Check if there's more data to load
          if (result.length < 10) {
            setHasMoreData(false);
          }
        } else if (!isLoadMore) {
          // No data for first load - set empty array
          productHook.setData('data', []);
          setHasMoreData(false);
        } else {
          // No more data for load more
          setHasMoreData(false);
        }
      } finally {
        setIsLoadingMore(false);
        setIsRefreshing(false);
        abortControllerRef.current = null;
      }
    },
    [filter, data, productHook],
  );

  const onFilter = useCallback(async () => {
    try {
      productHook.setData('onLoading', true);
      setPage(1);
      setHasMoreData(true);
      await fetchProduct(1, false);
    } catch (error) {
      console.log(error);
    } finally {
      productHook.setData('onLoading', false);
    }
  }, [fetchProduct, productHook]);

  // Effect to fetch products when filter changes
  useEffect(() => {
    onFilter();
  }, [filter]);

  // Cleanup effect to cancel requests on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Handle product press - navigate to product detail
  const handleProductPress = useCallback((_product: ProductItem) => {
    navigate(RootScreen.DetailProductPage, {id: _product.Id});
  }, []);

  const handleFavoritePress = useCallback(
    async (_item: ProductItem) => {
      if (!customer?.Id) {
        showSnackbar({
          message: 'Vui lòng đăng nhập để sử dụng tính năng này',
          status: ComponentStatus.ERROR,
        });
        return;
      }

      try {
        let result;
        const updatedItem = {..._item, IsFavorite: !_item.IsFavorite};

        if (_item.IsFavorite) {
          // Remove from favorites
          result = await productDA.unFavoriteProduct({
            productId: _item.Id,
            customerId: customer.Id,
          });

          if (result) {
            const updatedData: any = updateArrayWithObjects(data, [
              updatedItem,
            ]);
            productHook.setData('data', updatedData);

            showSnackbar({
              message: 'Đã xóa khỏi danh sách yêu thích',
              status: ComponentStatus.SUCCSESS,
            });
          }
        } else {
          // Add to favorites
          result = await productDA.favoriteProduct({
            product: _item,
            customerId: customer.Id,
          });

          if (result?.code === 200) {
            const updatedData: any = updateArrayWithObjects(data, [
              updatedItem,
            ]);
            productHook.setData('data', updatedData);

            showSnackbar({
              message: 'Đã thêm vào danh sách yêu thích',
              status: ComponentStatus.SUCCSESS,
            });
          }
        }
      } catch (error) {
        console.error('Error handling favorite:', error);
        showSnackbar({
          message: 'Có lỗi xảy ra, vui lòng thử lại',
          status: ComponentStatus.ERROR,
        });
      }
    },
    [customer?.Id, data, productHook],
  );

  const onAddToCart = useCallback((_item: ProductItem) => {
    // TODO: Implement add to cart functionality
    CartActions.addItemToCart(_item, 1)(dispatch);
    showSnackbar({
      message: 'Đã thêm sản phẩm vào giỏ hàng',
      status: ComponentStatus.SUCCSESS,
    });
  }, []);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMoreData && !onLoading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchProduct(nextPage, true);
    }
  }, [isLoadingMore, hasMoreData, onLoading, page, fetchProduct]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    setPage(1);
    setHasMoreData(true);
    fetchProduct(1, false);
  }, [fetchProduct]);

  // Generate unique key for FlatList items
  const keyExtractor = useCallback((item: ProductItem, index: number) => {
    return item.Id ? `product-${item.Id}` : `product-index-${index}`;
  }, []);

  return {
    // Data
    data,
    onLoading,

    // States
    page,
    isLoadingMore,
    hasMoreData,
    isRefreshing,

    // Functions
    handleProductPress,
    handleFavoritePress,
    onAddToCart,
    handleLoadMore,
    handleRefresh,
    keyExtractor,
  };
};
