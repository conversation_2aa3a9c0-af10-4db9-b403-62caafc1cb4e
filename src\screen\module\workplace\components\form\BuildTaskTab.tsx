import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
  ContractType,
  ContractStatus,
  signTickImg,
} from '../../../service/components/da';
import React, {forwardRef, useEffect, useMemo, useRef, useState} from 'react';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {
  CustomerType,
  CustomerRole,
} from '../../../../../redux/reducers/user/da';
import {DataController} from '../../../../base-controller';
import {
  <PERSON><PERSON><PERSON>og,
  FTextField,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import ListTile from '../../../../../component/list-tile/list-tile';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {randomGID, regexGetVariables, Ultis} from '../../../../../utils/Utils';
import {differenceInDays} from 'date-fns';
import {useForm} from 'react-hook-form';
import {PopupCheckOtp} from '../../../../../project-component/popup-otp';
import WebView from 'react-native-webview';
import ConfigAPI from '../../../../../config/configApi';
import {PopupEditTask} from '../../../toilet/view/detailProject/task';
import AppButton from '../../../../../component/button';
import {popupReject} from './QuoteTable';
import {ButtonViewRejectReason} from '../popup/DialogCustomize';
import {mapCheckBuildContractData} from './PrintForm';
import {
  TextFieldForm,
  Fselect1Form,
} from '../../../../../project-component/component-form';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import OperationalStatistics from '../../../../pages/CetificateAchievementPage/Component/OperationalStatistics';
import {useRoute} from '@react-navigation/native';
import {NetZeroDa} from '../../../service/Da/netzeroDa';
import ListToiletCriterionDA from './RegisterTab/components/SurveyInfo/components/ListToiletCriterion/da';
import CetificateAchievemenDa from 'screen/pages/CetificateAchievementPage/CetificateAchievemenDa';
import {store} from 'redux/store/store';
import Acceptance from '../Ulits/acceptance';
import {SafeAreaView} from 'react-native-safe-area-context';

export default function BuildTaskTab({
  data,
  serviceData,
  onSubmit,
  setServiceData,
  isRefreshing,
  onRefreshing,
  customer,
  methodParent,
}: any) {
  const user = useSelectorCustomerState().data;
  const bgColor = useSelectorCustomerState().bgColor;
  const owner = useSelectorCustomerCompanyState().owner;
  const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
  const company = useSelectorCustomerCompanyState().data;
  const userRole = useSelectorCustomerState().role;
  const cusId = store.getState().customer.data?.Id;

  const [consultantTask, setConsultantTask] = useState<any>();
  const [toiletCriterionsAcceptiance, setToiletCriterionsAcceptiance] =
    useState<any[]>([]);
  const route = useRoute<any>();
  const netZeroDa = new NetZeroDa();

  const [tasks, setTasks] = useState({
    data: Array<any>(),
    totalCount: undefined,
  });
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [report, setReport] = useState<any>();
  const [acceptanceTask, setAcceptanceTask] = useState<any>();
  const [searchValue, setSearchValue] = useState('');
  const [checkNestZero, setCheckNestZero] = useState(false);
  const [countTask, setCountTask] = useState(0);
  const [countStatistics, setCountStatistics] = useState(0);
  const [countAcceptance, setCountAcceptance] = useState(0);
  const [CheckToilet, setSelectedToilet] = useState<boolean>(false);

  const cateServices = useMemo(
    () => methodParent?.watch('cateServices'),
    [methodParent?.watch('cateServices')],
  );
  const methodsSendContract = useForm({shouldFocusError: false});
  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();
  const webRef = useRef<any>();
  const listToiletCriterionData = useMemo(
    () => new ListToiletCriterionDA(),
    [],
  );
  const isEditable = useMemo(() => {
    return (
      serviceData?.Status === ToiletServiceStatus.build &&
      (user?.Type === CustomerType.partner ||
        userRole?.Role?.includes(CustomerRole.Coordinator))
    );
  }, [user, data, serviceData, userRole, consultantTask]);

  const rejectReasons = useMemo(
    () => (report?.RejectReason ? JSON.parse(report.RejectReason) : []),
    [report],
  );
  const methods = useForm({shouldFocusError: false});

  const getData = async () => {
    let query = `@ToiletServicesId:{${serviceData?.Id}} @Status:[${TaskStatus.open} ${TaskStatus.closed}] @Type:[${TaskType.other} ${TaskType.other}]`;
    if (searchValue?.length) query += ` @Name:(*${searchValue}*)`;

    const controller = new DataController('Task');
    const res = await controller.aggregateList({
      page: 1,
      size: 1000,
      searchRaw: query,
      sortby: [{prop: 'DateEnd', direction: 'ASC'}],
    });
    if (res.code === 200) {
      const customerController = new DataController('Customer');
      const customerIds = res.data
        .map((e: any) => e.CustomerId)
        .filter(
          (id: any, i: any, arr: string | any[]) =>
            id && customers.every(e => e.Id !== id) && arr.indexOf(id) === i,
        );
      customerController.getByListId(customerIds).then(cusRes => {
        if (cusRes.code === 200) console.log('check-cusRes', cusRes);
        setCustomers([
          ...customers,
          ...cusRes.data.map((e: any) => ({
            ...e,
            bgColor: user?.Id === e.Id ? bgColor : Ultis.generateDarkColorRgb(),
          })),
        ]);
      });
      setCountTask(res.data.filter((e: any) => e.Status === 1)?.length),
        setTasks({
          data: res.data.sort(
            (a: any, b: any) => b?.DateCreated - a?.DateCreated,
          ),
          totalCount: res.totalCount,
        });
    }
  };

  const CheckToiletCriterions = async (ToiletId: string) => {
    const getCriterionsBytoiletId =
      await listToiletCriterionData.getCriterionsBytoiletId(ToiletId);
    let countAcceptance = getCriterionsBytoiletId.filter(
      (item: any) => item.Status !== 3,
    );
    if (countAcceptance?.length > 0) {
      let checkAcceptance = countAcceptance.find(
        (item: any) => item.Status !== 3,
      );
      if (checkAcceptance) {
        setSelectedToilet(true);
      }
      setToiletCriterionsAcceptiance(getCriterionsBytoiletId);
    }
  };

  const getOperationalStatistics = async (
    toiletId: string,
    toiletServicesId: string,
  ) => {
    const currentTime = Date.now();
    const response = await CetificateAchievemenDa.getOperationalData(
      toiletId,
      toiletServicesId,
    );
    if (response?.code === 200) {
      setCountStatistics(
        response.data.filter((item: any) => {
          return item.Status === 1 && item.DateStart < currentTime;
        }).length,
      );
      return response;
    }
    return [];
  };
  useEffect(() => {
    // Chỉ chạy khi có đủ dữ liệu cần thiết
    if (cusId && data && data.length > 0) {
      getOperationalStatistics(serviceData?.ToiletId, serviceData.Id);
    } else {
    }
  }, [cusId, data, serviceData]);

  useEffect(() => {
    CheckToiletCriterions(serviceData?.ToiletId);
  }, [serviceData]);

  const returnButtonChangeStatus = (item: any) => {
    if (!item) return <View />;

    switch (item?.Status) {
      case TaskStatus.open:
        return (
          <Text
            style={{
              color: ColorThemes.light.infor_main_color,
              backgroundColor: ColorThemes.light.infor_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Đang mở
          </Text>
        );
      case TaskStatus.done:
        return (
          <Text
            style={{
              color: ColorThemes.light.success_main_color,
              backgroundColor: ColorThemes.light.success_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Hoàn thành
          </Text>
        );
      case TaskStatus.overdue:
        return (
          <Text
            style={{
              color: ColorThemes.light.error_main_color,
              backgroundColor: ColorThemes.light.error_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Quá hạn
          </Text>
        );
      case TaskStatus.closed:
        return (
          <Text
            style={{
              color: ColorThemes.light.neutral_text_title_color,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Đã đóng
          </Text>
        );
      default:
        return (
          <Text
            style={{
              color: ColorThemes.light.neutral_text_title_color,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            -
          </Text>
        );
    }
  };
  const getAcceptanceTask = async () => {
    const taskController = new DataController('ToiletServiceCriterion');
    const res = await taskController.getListSimple({
      page: 1,
      size: 100000,
      query: `@ToiletId:{${serviceData?.ToiletId}} @Type:[2 2]`,
    });
    if (res.code === 200) setAcceptanceTask(res.data);
    else {
      showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    }
  };

  const onChangeTask = async (t: any) => {
    const controller = new DataController('Task');
    const res = await controller.edit([t]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    setTasks((ts: any) => ({
      data: ts.data.map((e: any) => (e.Id === t.Id ? t : e)),
      totalCount: ts.totalCount,
    }));
    getData();
  };

  useEffect(() => {
    if (serviceData) {
      getData();
      getAcceptanceTask();
    }
  }, [serviceData]);

  useEffect(() => {
    if (serviceData) {
      const contractController = new DataController('Contract');
      contractController
        .getListSimple({
          page: 1,
          size: 1,
          query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${ContractType.report} ${ContractType.report}]`,
        })
        .then(res => {
          if (res.code === 200 && res.data.length) setReport(res.data[0]);
        });
    }
  }, [serviceData]);

  const [isLoading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<
    'statistics' | 'myTasks' | 'acceptance'
  >('myTasks');

  // useEffect(() => {
  //   console.log('route.params?.type', route.params?.type);
  // }, [route.params?.type]);

  const submitOtp = async (onSuccess: any) => {
    if (!user?.Mobile) return;
    showPopup({
      ref: popupRef,
      children: (
        <PopupCheckOtp
          ref={popupRef}
          phone={user?.Mobile}
          onSuccess={onSuccess}
          isLoading={isLoading}
          setLoading={setLoading}
        />
      ),
    });
  };

  async function createBBNT({
    bankAccount,
    bankAccountName,
    bankName,
    templateContractId,
  }: any) {
    if (!bankName) return;
    const bankController = new DataController('Bank');
    const bankRes = await bankController.getById(bankName);
    if (bankRes.code !== 200)
      return showSnackbar({
        message: bankRes.message,
        status: ComponentStatus.ERROR,
      });
    bankName = bankRes?.data?.Name;
    const controller = new DataController('Contract');
    const contractContent = await mapCheckBuildContractData({
      cateServices: cateServices,
      customer: customer,
      data: data,
      servicesData: serviceData,
      partnerData: {data: user, company: company},
      ktxgroup: ktxgroup,
      methods: methodsSendContract,
      bankAccount: bankAccount,
      bankAccountName: bankAccountName,
      bankName: bankName,
    });
    const newContract = {
      Id: report?.Id ?? randomGID(),
      ...(report ?? {}),
      Name: 'BIÊN BẢN NGHIỆM THU',
      DateCreated: Date.now(),
      Type: ContractType.report,
      Status: ContractStatus.init,
      ToiletServicesId: serviceData.Id,
      DocumentsId: templateContractId,
      Content: contractContent,
    };
    await controller.add([newContract]);
    setReport(newContract);
  }

  const {width, height} = Dimensions.get('window');

  const addNewTask = async () => {
    const taskController = new DataController('Task');
    const res = await taskController.add([
      {
        Id: randomGID(),
        Name: 'Công việc mới',
        DateCreated: Date.now(),
        Description: 'Công việc mới',
        Type: TaskType.other,
        ToiletId: data.Id,
        ToiletServicesId: serviceData.Id,
        Status: TaskStatus.open,
        CustomerId: user?.Id,
      },
    ]);
    if (res.code === 200) getData();
  };
  const checkNestZeroService = async () => {
    const checkNestZeroService = await netZeroDa.getNestZeroService();
    if (checkNestZeroService?.code === 200) {
      if (serviceData?.CateServicesId === checkNestZeroService?.data[0]?.Id) {
        setCheckNestZero(true);
      } else {
        setCheckNestZero(false);
      }
    } else {
      setCheckNestZero(false);
    }
  };

  useEffect(() => {
    checkNestZeroService();
  }, []);

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      {rejectReasons.length > 0 ? (
        <ButtonViewRejectReason
          customers={[customer.data]}
          rejectReasons={rejectReasons}
        />
      ) : null}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'myTasks' && styles.activeTab]}
          onPress={() => setActiveTab('myTasks')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'myTasks' && styles.activeTabText,
            ]}>
            Quản lý công việc
          </Text>
          {countTask > 0 ? (
            <Text
              style={{
                backgroundColor: ColorThemes.light.secondary1_darker_color,
                color: ColorThemes.light.neutral_absolute_background_color,
                marginLeft: 12,
                position: 'absolute',
                right: 0,
                top: -7,
                borderRadius: 50,
                paddingHorizontal: 8,
                paddingVertical: 4,
                minWidth: 20,
                minHeight: 20,
                textAlign: 'center',
                fontSize: 12,
                fontWeight: 'bold',
                lineHeight: 12,
              }}>
              {countTask > 0 ? countTask : 0}
            </Text>
          ) : null}
        </TouchableOpacity>
        {checkNestZero ? (
          <>
            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'statistics' && styles.activeTab,
              ]}
              onPress={() => setActiveTab('statistics')}>
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'statistics' && styles.activeTabText,
                  {paddingHorizontal: 5},
                ]}>
                Thống kê vận hành
              </Text>
              {countStatistics > 0 ? (
                <Text
                  style={{
                    backgroundColor: ColorThemes.light.secondary1_darker_color,
                    color: ColorThemes.light.neutral_absolute_background_color,
                    marginLeft: 12,
                    position: 'absolute',
                    right: 0,
                    top: -7,
                    borderRadius: 50,
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    minWidth: 20,
                    minHeight: 20,
                    textAlign: 'center',
                    fontSize: 12,
                    fontWeight: 'bold',
                    lineHeight: 12,
                  }}>
                  {countStatistics > 0 ? countStatistics : 0}
                </Text>
              ) : null}
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'acceptance' && styles.activeTab,
              ]}
              onPress={() => setActiveTab('acceptance')}>
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'acceptance' && styles.activeTabText,
                ]}>
                Nghiệm thu
              </Text>
              {countAcceptance > 0 ? (
                <Text
                  style={{
                    backgroundColor: ColorThemes.light.secondary1_darker_color,
                    color: ColorThemes.light.neutral_absolute_background_color,
                    marginLeft: 12,
                    position: 'absolute',
                    right: 0,
                    top: -7,
                    borderRadius: 50,
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    minWidth: 20,
                    minHeight: 20,
                    textAlign: 'center',
                    fontSize: 12,
                    fontWeight: 'bold',
                    lineHeight: 12,
                  }}>
                  {countAcceptance ? countAcceptance : 0}
                </Text>
              ) : null}
            </TouchableOpacity>
          </>
        ) : null}
      </View>

      {serviceData?.Status === ToiletServiceStatus.build &&
      activeTab === 'myTasks' &&
      report?.Status === undefined ? (
        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            paddingHorizontal: 16,
            gap: 8,
            paddingTop: 16,
          }}>
          <FTextField
            style={{paddingHorizontal: 16, height: 40}}
            onChange={vl => {
              setSearchValue(vl.trim());
            }}
            onSubmit={() => getData()}
            prefix={
              <Winicon
                src="outline/development/zoom"
                size={14}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
            }
            value={searchValue}
            placeholder="Tìm kiếm"
          />
        </View>
      ) : null}
      {activeTab === 'myTasks' && (
        <View style={{paddingHorizontal: 16, gap: 16, paddingTop: 16}}>
          {((owner?.Id === serviceData?.CustomerId &&
            userRole?.Role.includes(CustomerRole.Coordinator)) ||
            user?.Id === serviceData?.CustomerId) &&
          serviceData?.Status === ToiletServiceStatus.build &&
          report?.Status == undefined ? (
            <AppButton
              title={'Thêm công việc'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={{
                height: 40,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={addNewTask}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          ) : null}
        </View>
      )}
      {activeTab === 'myTasks' && (
        <View style={{flex: 1}}>
          {serviceData?.Status === ToiletServiceStatus.build &&
          report?.Status == undefined ? (
            <FlatList
              data={tasks.data}
              style={{flex: 1, paddingHorizontal: 16}}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing ?? false}
                  onRefresh={() => {
                    onRefreshing();
                    getData();
                  }}
                />
              }
              keyExtractor={(item, i) => item + '-' + i}
              ItemSeparatorComponent={() => <View style={{height: 16}} />}
              renderItem={({item, index}: any) => {
                const _customer = customers.find(
                  (e: any) => e?.Id === item?.CustomerId,
                ) as any;

                let checkEditable =
                  isEditable && item?.Status !== TaskStatus.closed;
                if (
                  item?.Type >= TaskType.consultant &&
                  item?.Type <= TaskType.build
                ) {
                  checkEditable =
                    user?.Id === serviceData?.CustomerId ||
                    (userRole?.Role?.includes(CustomerRole.Coordinator) &&
                      owner?.Id === serviceData?.CustomerId);
                }

                return (
                  <ListTile
                    key={`${item.Id}`}
                    onPress={
                      checkEditable
                        ? () => {
                            if (item.DateStart) {
                              methods.setValue(
                                'dateStart',
                                new Date(item.DateStart),
                              );
                            } else {
                              methods.setValue('dateStart', undefined);
                            }
                            if (item.DateEnd) {
                              methods.setValue(
                                'dateEnd',
                                new Date(item.DateEnd),
                              );
                            } else {
                              methods.setValue('dateEnd', undefined);
                            }

                            methods.setValue(
                              'Description',
                              `${item?.Description ?? ''}`,
                            );
                            methods.setValue(
                              'CateServicesId',
                              `${item?.CateServicesId ?? 'undefined'}`,
                            );

                            if (checkEditable && item.Type === TaskType.other) {
                              methods.setValue('Name', `${item?.Name}`);
                            }
                            {
                              /*{id: TaskStatus.doing, name: "Đang làm"},
                                     { id: TaskStatus.overdue, name: "Quá hạn" },
                                    */
                            }
                            var options = [
                              {id: TaskStatus.open, name: 'Mở'},
                              {id: TaskStatus.done, name: 'Hoàn thành'},
                              {id: TaskStatus.closed, name: 'Đóng'},
                            ];
                            if (checkEditable || item.CustomerId === user?.Id) {
                              options = options.filter(
                                e => e.id !== TaskStatus.closed,
                              );
                            }
                            methods.setValue(
                              'status',
                              options.find(e => e.id === item?.Status)?.id ??
                                options[0].id,
                            );

                            showPopup({
                              ref: popupRef,
                              enableDismiss: true,
                              children: (
                                <PopupEditTask
                                  methods={methods}
                                  options={options}
                                  toiletId={data.Id}
                                  ref={popupRef}
                                  item={item}
                                  onChange={onChangeTask}
                                  checkEditable={checkEditable}
                                />
                              ),
                            });
                          }
                        : undefined
                    }
                    title={`${index + 1}. ${item?.Name ?? ''}`}
                    titleStyle={[
                      TypoSkin.heading7,
                      {color: ColorThemes.light.neutral_text_title_color},
                    ]}
                    subtitle={
                      <View style={{gap: 4, paddingTop: 8}}>
                        <Text
                          style={{
                            ...TypoSkin.buttonText4,
                            color:
                              ColorThemes.light.neutral_text_subtitle_color,
                          }}>{`Người thực hiện: ${_customer?.Name ?? ''}`}</Text>
                        <Text
                          style={{
                            ...TypoSkin.buttonText4,
                            color:
                              ColorThemes.light.neutral_text_subtitle_color,
                          }}>{`Số điện thoại: ${_customer?.Mobile ?? ''}`}</Text>
                        {(() => {
                          var startValue = undefined;
                          var endValue = undefined;
                          if (!item || (!item.DateStart && !item.DateEnd))
                            return null;
                          if (item.DateStart)
                            startValue = new Date(item.DateStart);
                          if (item.DateEnd) endValue = new Date(item.DateEnd);
                          return (
                            <View
                              style={{
                                gap: 6,
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Text
                                style={{
                                  ...TypoSkin.buttonText4,
                                  color:
                                    ColorThemes.light
                                      .neutral_text_subtitle_color,
                                }}>
                                {startValue
                                  ? `${Ultis.datetoString(startValue, startValue.getSeconds() === 1 ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy')} - `
                                  : ''}
                                {endValue
                                  ? Ultis.datetoString(
                                      endValue,
                                      endValue.getSeconds() === 59
                                        ? 'dd/mm/yyyy hh:mm'
                                        : 'dd/mm/yyyy',
                                    )
                                  : ''}
                              </Text>
                              {item.RepeatValue ? (
                                <Winicon
                                  src="outline/arrows/loop-2"
                                  size={12}
                                />
                              ) : null}
                            </View>
                          );
                        })()}
                        <Text
                          style={{
                            ...TypoSkin.buttonText4,
                            color:
                              ColorThemes.light.neutral_text_subtitle_color,
                          }}>{`Số ngày: ${item.DateStart && item.DateEnd ? differenceInDays(new Date(item.DateEnd), new Date(item.DateStart)) : ''}`}</Text>
                        {item.Description ? (
                          <Text
                            style={{
                              ...TypoSkin.buttonText4,
                              color:
                                ColorThemes.light.neutral_text_subtitle_color,
                            }}
                            numberOfLines={3}>
                            Mô tả: {item?.Description?.trim()}
                          </Text>
                        ) : null}
                      </View>
                    }
                    listtileStyle={{gap: 16}}
                    style={{
                      borderColor: ColorThemes.light.neutral_main_border_color,
                      borderWidth: 1,
                      marginTop: 16,
                      padding: 16,
                    }}
                    trailing={
                      checkEditable ? (
                        <Winicon
                          src="outline/user interface/d-edit"
                          size={16}
                        />
                      ) : null
                    }
                    bottom={
                      <View
                        style={{
                          flexDirection: 'row',
                          alignContent: 'flex-start',
                          width: '100%',
                          paddingTop: 12,
                        }}>
                        {returnButtonChangeStatus(item)}
                      </View>
                    }
                  />
                );
              }}
              ListFooterComponent={() => <View style={{height: 100}} />}
            />
          ) : (
            <Pressable
              style={{
                flex: 1,
                height: Dimensions.get('screen').height * 3,
              }}>
              <WebView
                key={`${report?.Id} ${report?.Status}`}
                renderError={() => <EmptyPage />}
                startInLoadingState
                textZoom={100}
                bounces={false}
                nestedScrollEnabled
                onLoadEnd={() => {
                  console.log('WebView finished loading');
                }}
                limitsNavigationsToAppBoundDomains
                renderLoading={() => (
                  <ActivityIndicator
                    style={{
                      backgroundColor: 'transparent',
                      position: 'absolute',
                      left: 0,
                      right: 0,
                      top: height / 3,
                      zIndex: 9,
                    }}
                    color={ColorThemes.light.primary_main_color}
                    size="large"
                  />
                )}
                style={{
                  height: Dimensions.get('screen').height * 3,
                  width: Dimensions.get('screen').width,
                  flex: 1,
                }}
                originWhitelist={['*']}
                javaScriptEnabled={true}
                injectedJavaScript='document.body.querySelector(".innerhtml-view").style.paddingBottom = "100px";'
                source={{
                  uri: `${ConfigAPI.urlWeb}contract-web-view?type=contract&id=${report?.Id}`,
                }}
              />
            </Pressable>
          )}
          {user?.Id === data[0]?.CustomerId &&
          serviceData?.Status === ToiletServiceStatus.sendCompleteBuild ? (
            <SafeAreaView
              edges={['bottom']}
              style={{
                ...styles.bottomButtons,
                paddingTop: 16,
              }}>
              <TouchableOpacity
                style={{
                  ...styles.button,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}
                onPress={() => {
                  popupReject({
                    ref: dialogRef,
                    title:
                      'Bạn chắc chắn muốn từ chối biên bản nghiệm thu này?',
                    methods: methods,
                    onSubmit: async (ev: any) => {
                      const newRejectReason = {
                        DateCreated: Date.now(),
                        Content: ev,
                      };
                      const controller = new DataController('Contract');
                      const res = await controller.edit([
                        {
                          ...report,
                          Status: ContractStatus.reject,
                          RejectReason: JSON.stringify([
                            ...rejectReasons,
                            newRejectReason,
                          ]),
                        },
                      ]);
                      if (res.code === 200) {
                        const controller = new DataController('ToiletServices');
                        controller.edit([
                          {...serviceData, Status: ToiletServiceStatus.build},
                        ]);
                        setServiceData({
                          ...serviceData,
                          Status: ToiletServiceStatus.build,
                        });
                        webRef.current?.reload();
                      }
                    },
                  });
                }}>
                <Text
                  style={{
                    ...styles.buttonText,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  Từ chối
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.button}
                onPress={() =>
                  submitOtp(async () => {
                    const controller = new DataController('Contract');
                    if (user) {
                      const res = await controller.edit([
                        {
                          ...report,
                          Status: ContractStatus.guestSigned,
                          DateSign: Date.now(),
                          Content: report.Content.replace(
                            regexGetVariables,
                            (m: string, key: string) => {
                              switch (key) {
                                case 'GuestSign':
                                  return signTickImg;
                                case 'GuestSignName':
                                  return user.Name;
                                case 'GuestDateSign':
                                  return Ultis.datetoString(new Date());
                                default:
                                  return '';
                              }
                            },
                          ),
                          ToiletId: data.Id,
                        },
                      ]);

                      if (res.code === 200) onSubmit();
                    }
                  })
                }>
                <Text style={styles.buttonText}>{'Xác nhận ký BBNT'}</Text>
              </TouchableOpacity>
            </SafeAreaView>
          ) : null}
          {serviceData?.CustomerId === user?.Id &&
          serviceData?.Status === ToiletServiceStatus.build &&
          report?.Status === ContractStatus.reject ? (
            <View style={styles.bottomButtons}>
              <TouchableOpacity
                style={{...styles.button, marginVertical: 16}}
                onPress={async () => {
                  const controller = new DataController('Contract');
                  let updateReport = {...report};
                  delete updateReport.Status;
                  await controller.add([updateReport]);
                  setReport(updateReport);
                }}>
                <Text style={styles.buttonText}>Chỉnh sửa công việc</Text>
              </TouchableOpacity>
            </View>
          ) : null}
          {serviceData?.CustomerId === user?.Id &&
          !CheckToilet &&
          serviceData?.Status === ToiletServiceStatus.build &&
          report?.Status !== ContractStatus.reject &&
          tasks.data.every(
            e => e.Status === TaskStatus.done || e.Status === TaskStatus.closed,
          ) ? (
            <View style={styles.bottomButtons}>
              {report?.Status === undefined ? (
                <TouchableOpacity
                  style={{...styles.button, marginBottom: 16}}
                  onPress={async () => {
                    const templateContractId =
                      '6bc2c41f5e834560afdaaaa1e74af07d';
                    let bankAccount = company
                      ? company?.BankAccount
                      : user?.BankAccount;
                    let bankAccountName = company
                      ? company.BankAccountName
                      : user?.BankAccountName;
                    let bankName = company ? company.BankId : user?.BankId;

                    if (
                      !bankAccount?.length ||
                      !bankAccountName?.length ||
                      !bankName?.length
                    ) {
                      showPopup({
                        ref: popupRef,
                        children: (
                          <PopupSetBankAccount
                            ref={popupRef}
                            onSubmit={async (ev: any) => {
                              bankAccount = ev.BankAccount;
                              bankAccountName = ev.BankAccountName;
                              bankName = ev.BankId;
                              await createBBNT({
                                bankAccount,
                                bankAccountName,
                                bankName,
                                templateContractId,
                              });
                            }}
                          />
                        ),
                      });
                    } else {
                      await createBBNT({
                        bankAccount,
                        bankAccountName,
                        bankName,
                        templateContractId,
                      });
                    }
                    webRef.current?.reload();
                  }}>
                  <Text style={styles.buttonText}>Tạo biên bản nghiệm thu</Text>
                </TouchableOpacity>
              ) : (
                <View style={{flex: 1, gap: 8, marginBottom: 16}}>
                  <TouchableOpacity
                    style={{
                      ...styles.button,
                      backgroundColor:
                        ColorThemes.light.neutral_main_background_color,
                    }}
                    onPress={async () => {
                      const controller = new DataController('Contract');
                      let updateReport = {...report};
                      delete updateReport.Status;
                      await controller.add([updateReport]);
                      setReport(updateReport);
                      webRef.current?.reload();
                    }}>
                    <Text
                      style={{
                        ...styles.buttonText,
                        color: ColorThemes.light.neutral_text_title_color,
                      }}>
                      Chỉnh sửa công việc
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.button}
                    onPress={async () => {
                      submitOtp(async () => {
                        const controller = new DataController('Contract');
                        const updateReport = {
                          ...report,
                          Status: ContractStatus.partnerSigned,
                          Content: report.Content.replace(
                            regexGetVariables,
                            (m: any, key: any) => {
                              switch (key) {
                                case 'KTXSign':
                                  return signTickImg;
                                case 'PartnerSign':
                                  return signTickImg;
                                case 'PartnerSignName':
                                  return user?.Name;
                                case 'PartnerDateSign':
                                  return Ultis.datetoString(new Date());
                                default:
                                  return m;
                              }
                            },
                          ),
                        };
                        await controller.edit([updateReport]);
                        setReport(updateReport);
                        const toiletServicesController = new DataController(
                          'ToiletServices',
                        );
                        toiletServicesController.edit([
                          {
                            ...serviceData,
                            Status: ToiletServiceStatus.sendCompleteBuild,
                          },
                        ]);
                        setServiceData({
                          ...serviceData,
                          Status: ToiletServiceStatus.sendCompleteBuild,
                        });
                        webRef.current?.reload();
                      });
                    }}>
                    <Text style={styles.buttonText}>
                      Gửi biên bản nghiệm thu
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          ) : null}
        </View>
      )}
      {activeTab === 'statistics' && (
        <View style={{flex: 1}}>
          <OperationalStatistics
            type="statistics"
            toiletId={data.map((item: any) => item.Id).join(',')}
            setCountStatistics={setCountStatistics}
            getOperationalStatistics={getOperationalStatistics}
            toiletServicesId={serviceData?.Id}
          />
        </View>
      )}
      {activeTab === 'acceptance' && (
        <View
          style={{
            flex: 1,
          }}>
          <Acceptance
            toiletServiceId={serviceData?.Id}
            toiletServiceStatus={serviceData?.Status}
            ToiletId={serviceData?.ToiletId}
            setCountAcceptance={setCountAcceptance}
            settoiletCriterionsAcceptance={setToiletCriterionsAcceptiance}
            toiletCriterionsAcceptance={toiletCriterionsAcceptiance}
            serviceData={serviceData}
          />
          {serviceData?.CustomerId === user?.Id &&
          !CheckToilet &&
          serviceData?.Status === ToiletServiceStatus.build &&
          report?.Status !== ContractStatus.reject &&
          tasks.data.every(
            e => e.Status === TaskStatus.done || e.Status === TaskStatus.closed,
          ) ? (
            <View style={styles.bottomButtons}>
              {report?.Status === undefined ? (
                <TouchableOpacity
                  style={{...styles.button, marginBottom: 16}}
                  onPress={async () => {
                    const templateContractId =
                      '6bc2c41f5e834560afdaaaa1e74af07d';
                    let bankAccount = company
                      ? company?.BankAccount
                      : user?.BankAccount;
                    let bankAccountName = company
                      ? company.BankAccountName
                      : user?.BankAccountName;
                    let bankName = company ? company.BankId : user?.BankId;

                    if (
                      !bankAccount?.length ||
                      !bankAccountName?.length ||
                      !bankName?.length
                    ) {
                      showPopup({
                        ref: popupRef,
                        children: (
                          <PopupSetBankAccount
                            ref={popupRef}
                            onSubmit={async (ev: any) => {
                              bankAccount = ev.BankAccount;
                              bankAccountName = ev.BankAccountName;
                              bankName = ev.BankId;
                              await createBBNT({
                                bankAccount,
                                bankAccountName,
                                bankName,
                                templateContractId,
                              });
                            }}
                          />
                        ),
                      });
                    } else {
                      await createBBNT({
                        bankAccount,
                        bankAccountName,
                        bankName,
                        templateContractId,
                      });
                    }
                    webRef.current?.reload();
                  }}>
                  <Text style={styles.buttonText}>Tạo biên bản nghiệm thu</Text>
                </TouchableOpacity>
              ) : (
                <View style={{flex: 1, gap: 8, marginBottom: 16}}>
                  <TouchableOpacity
                    style={{
                      ...styles.button,
                      backgroundColor:
                        ColorThemes.light.neutral_main_background_color,
                    }}
                    onPress={async () => {
                      const controller = new DataController('Contract');
                      let updateReport = {...report};
                      delete updateReport.Status;
                      await controller.add([updateReport]);
                      setReport(updateReport);
                      webRef.current?.reload();
                    }}>
                    <Text
                      style={{
                        ...styles.buttonText,
                        color: ColorThemes.light.neutral_text_title_color,
                      }}>
                      Chỉnh sửa công việc
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.button}
                    onPress={async () => {
                      submitOtp(async () => {
                        const controller = new DataController('Contract');
                        const updateReport = {
                          ...report,
                          Status: ContractStatus.partnerSigned,
                          Content: report.Content.replace(
                            regexGetVariables,
                            (m: any, key: any) => {
                              switch (key) {
                                case 'KTXSign':
                                  return signTickImg;
                                case 'PartnerSign':
                                  return signTickImg;
                                case 'PartnerSignName':
                                  return user?.Name;
                                case 'PartnerDateSign':
                                  return Ultis.datetoString(new Date());
                                default:
                                  return m;
                              }
                            },
                          ),
                        };
                        await controller.edit([updateReport]);
                        setReport(updateReport);
                        const toiletServicesController = new DataController(
                          'ToiletServices',
                        );
                        toiletServicesController.edit([
                          {
                            ...serviceData,
                            Status: ToiletServiceStatus.sendCompleteBuild,
                          },
                        ]);
                        setServiceData({
                          ...serviceData,
                          Status: ToiletServiceStatus.sendCompleteBuild,
                        });
                        webRef.current?.reload();
                      });
                    }}>
                    <Text style={styles.buttonText}>
                      Gửi biên bản nghiệm thu
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          ) : null}
        </View>
      )}
    </View>
  );
}

const PopupSetBankAccount = forwardRef(function PopupSetBankAccount(
  data: {onSubmit: any},
  ref: any,
) {
  const {onSubmit} = data;
  const [bankList, setBankList] = useState([]);
  const methods = useForm({
    shouldFocusError: false,
  });

  const _onSubmit = (ev: any) => {
    if (
      !methods.watch('BankAccount') ||
      !methods.watch('BankAccountName') ||
      !methods.watch('BankId')
    )
      return;
    closePopup(ref);
    onSubmit(ev);
  };

  useEffect(() => {
    const bankController = new DataController('Bank');
    bankController.getAll().then(res => {
      if (res.code === 200) setBankList(res.data);
    });
  }, []);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 146,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={`Nhập thông tin ngân hàng`}
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <KeyboardAvoidingView behavior={'height'} style={{flex: 1}}>
        <ScrollView
          style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
          <View style={{flex: 1, gap: 16}}>
            <TextFieldForm
              required
              style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
              label="Số tài khoản ngân hàng"
              placeholder="Số tài khoản ngân hàng"
              control={methods.control}
              type="number-pad"
              maxLength={13}
              prefix={
                <Winicon src="outline/business/contactless-card" size={14} />
              }
              register={methods.register}
              errors={methods.formState.errors}
              name="BankAccount"
              textFieldStyle={{padding: 16}}
            />
            <TextFieldForm
              required
              style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
              label="Tên chủ tài khoản"
              placeholder="Tên chủ tài khoản"
              control={methods.control}
              prefix={
                <Winicon src="outline/business/contactless-card" size={14} />
              }
              register={methods.register}
              errors={methods.formState.errors}
              name="BankAccountName"
              textFieldStyle={{padding: 16}}
            />
            <Fselect1Form
              required
              placeholder="Chọn ngân hàng"
              label="Ngân hàng"
              control={methods.control}
              errors={methods.formState.errors}
              style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
              name="BankId"
              options={bankList.map((e: any) => ({id: e.Id, name: e.Name}))}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Hoàn tất'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={methods.handleSubmit(_onSubmit)}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
    width: '100%',
    marginTop: 10,
  },
  bottomButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    gap: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // TabView Styles
  tabContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    padding: 4,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  activeTab: {
    backgroundColor: ColorThemes.light.primary_main_color,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
    marginTop: 8,
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
});
