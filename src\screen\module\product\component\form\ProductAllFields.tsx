import React from 'react';
import {View, Text, TouchableOpacity, TextInput} from 'react-native';
import {Controller, Control, FieldErrors} from 'react-hook-form';
import {AppSvg} from 'wini-mobile-components';
import {InputForm} from './InputForm';
import {CreatePartnerProductFormStyles} from '../styles/CreatePartnerProductFormStyles';
import {TextFieldForm} from '../../../../../project-component/component-form';
import iconSvg from '../../../../../svgs/iconSvg';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {Ultis} from '../../../../../utils/Utils';
import {FCheckbox} from '../../../../../component/export-component';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {ProductDatePicker} from './CustomDatePicker';

interface ProductAllFieldsProps {
  control: Control<any>;
  errors: FieldErrors<any>;
  watch: (name?: string) => any;
  register: any;
  selectType: string;
  selectCate: string;
  selectCateattribute: string;
  selectAttribute: string;
  selectSource: string;
  selectColor: string;
  selectBrand: string;
  selectConsume: string;
  isFreeShip: boolean;
  setIsFreeShip: (value: boolean) => void;
  showDateInput: boolean;
  setShowDateInput: (value: boolean) => void;
  openBottomSheet: (title: string, currentValue?: any) => void;
  styles: any;
}

const ProductAllFields: React.FC<ProductAllFieldsProps> = ({
  control,
  errors,
  watch,
  register,
  selectType,
  selectCate,
  selectCateattribute,
  selectAttribute,
  selectSource,
  selectColor,
  selectBrand,
  selectConsume,
  isFreeShip,
  setIsFreeShip,
  showDateInput,
  setShowDateInput,
  openBottomSheet,
  styles,
}) => {
  const prefix = (icon: string) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          height: 32,
          width: 32,
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 100,
        }}>
        <AppSvg SvgSrc={icon} size={20} />
      </View>
    );
  };

  return (
    <>
      {/* Detail Field */}
      <InputForm
        control={control}
        checkLengthText={watch().Detail?.length || 0}
        nameFeild="Chi tiết sản phẩm"
        name="Detail"
        rule="Vui lòng nhập mô tả sản phẩm"
        placeholder="Nhập mô tả sản phẩm"
        maxlength={3000}
        mutiline={true}
        large={true}
      />
      {errors.Name &&
        (!watch('Detail') ||
          (Array.isArray(watch('Detail')) && watch('Detail').length === 0)) && (
          <Text style={CreatePartnerProductFormStyles.error}>
            {(errors.Detail?.message as string) ||
              'Vui lòng nhập mô tả sản phẩm'}
          </Text>
        )}

      {/* Price Field */}
      <View style={styles.option}>
        <Controller
          control={control}
          name="Price"
          rules={{
            required: 'Vui lòng nhập giá bán',
            validate: value => {
              if (!value || value <= 0) {
                return 'Giá bán phải lớn hơn 0';
              }
              return true;
            },
          }}
          render={({field: {value, onChange}}) => (
            <View style={{position: 'relative'}}>
              <View
                style={{
                  position: 'absolute',
                  left: 5,
                  top: 14,
                  zIndex: 1,
                }}>
                <AppSvg SvgSrc={iconSvg.Price} size={20} />
              </View>

              <TextInput
                placeholder="Giá bán"
                returnKeyType="done"
                style={{
                  height: 48,
                  borderWidth: 0,
                  borderRadius: 8,
                  paddingLeft: 50,
                  paddingRight: 60,
                  fontSize: 14,
                  color: ColorThemes.light.neutral_text_title_color,
                  fontWeight: '400',
                }}
                value={value ? Ultis.money(value) : ''}
                onChangeText={(text: string) => {
                  const numericValue = text.replace(/[^0-9]/g, '');
                  onChange(numericValue ? parseInt(numericValue) : '');
                }}
                keyboardType="numeric"
              />

              <View
                style={{
                  position: 'absolute',
                  right: 16,
                  top: 14,
                  zIndex: 1,
                }}>
                <Text
                  style={{
                    color: ColorThemes.light.neutral_text_subtitle_color,
                    ...TypoSkin.regular2,
                  }}>
                  VNĐ
                </Text>
              </View>
            </View>
          )}
        />
      </View>
      {errors.Price && !watch('Price') && (
        <Text style={CreatePartnerProductFormStyles.error}>
          {(errors.Price?.message as string) || 'Vui lòng nhập giá bán'}
        </Text>
      )}

      {/* Description Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <TextFieldForm
          control={control}
          name="Description"
          placeholder="Mô tả"
          returnKeyType="done"
          errors={errors}
          textFieldStyle={{
            height: 48,
            borderWidth: 0,
          }}
          // style={{
          //   color: '#000000', // Màu chữ đen giống InputForm
          //   fontSize: 14,
          // }}
          register={register}
          required
          prefix={prefix(iconSvg.description)}
        />
      </View>

      {/* Source Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="Source"
          rules={{
            required: 'Vui lòng chọn nguồn gốc , xuất xứ',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
              }}
              onPress={() => {
                openBottomSheet('nguồn gốc', value);
              }}>
              {prefix(iconSvg.Source)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  fontWeight: '400',
                  color: value ? '#000' : '#999',
                }}>
                {selectSource || 'Nguồn gốc , xuất xứ'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.Source &&
        (!watch('Source') ||
          (Array.isArray(watch('Source')) && watch('Source').length === 0)) && (
          <Text style={CreatePartnerProductFormStyles.error}>
            {(errors.Source?.message as string) ||
              'Vui lòng chọn Nguồn gốc xuất xứ'}
          </Text>
        )}

      {/* Preserve Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <ProductDatePicker
          control={control}
          name="Preserve"
          errors={errors}
          placeholder="Chọn thời gian bảo dưỡng"
          required={true}
          style={{
            flex: 1,
            height: 48,
          }}
          prefix={prefix(iconSvg.Preserve)}
          onDateChange={(selectedDate: Date | undefined) => {
            // Optional: Handle date change if needed
            console.log('Date selected:', selectedDate);
          }}
        />
      </View>

      {/* Guarantee Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <ProductDatePicker
          control={control}
          name="Guarantee"
          errors={errors}
          placeholder="Chọn thời gian bảo hành"
          required={true}
          style={{
            flex: 1,
            height: 48,
          }}
          prefix={prefix(iconSvg.Guarantee)}
          onDateChange={(selectedDate: Date | undefined) => {
            // Optional: Handle date change if needed
            console.log('Guarantee date selected:', selectedDate);
          }}
        />
      </View>

      <View style={[{marginBottom: 12}]}>
        <InputForm
          control={control}
          checkLengthText={watch('Specifications')?.length || 0}
          nameFeild="Thông số kỹ thuật"
          name="Specifications"
          rule="Vui lòng nhập thông số kỹ thuật"
          placeholder="Nhập thông số kỹ thuật"
          maxlength={3000}
          mutiline={true}
        />
        {errors.Specifications &&
          (!watch('Specifications') ||
            (Array.isArray(watch('Specifications')) &&
              watch('Specifications').length === 0)) && (
            <Text style={CreatePartnerProductFormStyles.error}>
              {(errors.Specifications?.message as string) ||
                'Vui lòng nhập thông số kỹ thuật'}
            </Text>
          )}
      </View>

      {/* InStock Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <TextFieldForm
          control={control}
          name="InStock"
          placeholder="Số lượng tồn kho"
          returnKeyType="done"
          type="numeric"
          errors={errors}
          textFieldStyle={{
            height: 48,
            borderWidth: 0,
          }}
          register={register}
          required
          prefix={prefix(iconSvg.instock)}
        />
      </View>

      {/* Unit Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <TextFieldForm
          control={control}
          name="Unit"
          placeholder="Đơn vị tính"
          returnKeyType="done"
          errors={errors}
          textFieldStyle={{
            height: 48,
            borderWidth: 0,
          }}
          register={register}
          required
          prefix={prefix(iconSvg.instock)}
        />
      </View>

      {/* Color Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="ColorId"
          rules={{
            required: 'Vui lòng chọn màu sắc',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
              }}
              onPress={() => {
                openBottomSheet('màu sắc', value);
              }}>
              {prefix(iconSvg.color)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  color: value ? '#000' : '#999',
                  fontWeight: '400',
                }}>
                {selectColor || 'Màu sắc'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.ColorId &&
        (!watch('ColorId') ||
          (Array.isArray(watch('ColorId')) &&
            watch('ColorId').length === 0)) && (
          <Text style={CreatePartnerProductFormStyles.error}>
            {(errors.ColorId?.message as string) ||
              'Vui lòng nhập thông số kỹ thuật'}
          </Text>
        )}

      {/* Brand Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="BrandsId"
          rules={{
            required: 'Vui lòng chọn thương hiệu',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
              }}
              onPress={() => {
                openBottomSheet('thương hiệu', value);
              }}>
              {prefix(iconSvg.label)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  color: value ? '#000' : '#999',
                  fontWeight: '400',
                }}>
                {selectBrand || 'Thương hiệu *'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.BrandsId &&
        (!watch('BrandsId') ||
          (Array.isArray(watch('BrandsId')) &&
            watch('BrandsId').length === 0)) && (
          <Text style={CreatePartnerProductFormStyles.error}>
            {(errors.BrandsId?.message as string) ||
              'Vui lòng nhập thông số kỹ thuật'}
          </Text>
        )}

      {/* Consume Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="ConsumeId"
          rules={{
            required: 'Vui lòng chọn tiêu thụ',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
              }}
              onPress={() => {
                openBottomSheet('tiêu thụ', value);
              }}>
              {prefix(iconSvg.consume)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  color: value ? '#000' : '#999',
                  fontWeight: '400',
                }}>
                {selectConsume || 'Tiêu thụ *'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.ConsumeId &&
        (!watch('ConsumeId') ||
          (Array.isArray(watch('ConsumeId')) &&
            watch('ConsumeId').length === 0)) && (
          <Text style={CreatePartnerProductFormStyles.error}>
            {(errors.ConsumeId?.message as string) ||
              'Vui lòng nhập thông số kỹ thuật'}
          </Text>
        )}

      {/* FreeShip Checkbox */}
      <View style={styles.freeShip}>
        <FCheckbox
          size={25}
          value={isFreeShip}
          onChange={v => {
            setIsFreeShip(v);
          }}
        />
        <Text style={{marginLeft: 10, ...TypoSkin.title4}}>FreeShip</Text>
      </View>
    </>
  );
};

export default ProductAllFields;
