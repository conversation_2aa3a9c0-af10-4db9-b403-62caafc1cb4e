import {Text, TouchableOpacity, View, ActivityIndicator} from 'react-native';
import ListTile from '../../../../../component/list-tile/list-tile';
import {Ultis} from '../../../../../utils/Utils';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {
  FDialog,
  showDialog,
  Winicon,
} from '../../../../../component/export-component';
import {useMemo, useRef, useState} from 'react';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../../router/router';
import {
  CertificateStringData,
  PlaceStringData,
  ToiletStatus,
  TypeStringData,
} from '../../../service/components/da';
import {ComponentStatus} from '../../../../../component/component-status';
import {CustomerRole} from '../../../../../redux/reducers/user/da';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import {useDispatch} from 'react-redux';
import {FPopup, showPopup} from '../../../../../component/popup/popup';
import {AddEditToiletPopup} from '../../../toilet/components/form/AddEditToilet';

export default function ToiletCard({
  currentItem,
  user,
  onDelete,
  getData,
}: any) {
  const dialogDelAccRef = useRef<any>();
  const popupRef = useRef<any>();
  const navigation = useNavigation<any>();
  const owner = useSelectorCustomerCompanyState().owner;
  const userRole = useSelectorCustomerState().role;
  const [isNavigating, setIsNavigating] = useState(false);
  const dispatch = useDispatch<any>();
  const checkEditable = useMemo(() => {
    return (
      currentItem?.CustomerId === user.Id ||
      (currentItem.CustomerId === owner?.Id &&
        userRole?.Role?.includes(CustomerRole.Coordinator))
    );
  }, [currentItem, user, owner, userRole]);
  const [item, setItem] = useState<any>(currentItem);
  return (
    <View
      style={{
        borderWidth: 1,
        borderColor: ColorThemes.light.neutral_main_border_color,
        borderRadius: 10,
      }}>
      <FDialog ref={dialogDelAccRef} />
      <FPopup ref={popupRef} />
      <ListTile
        onPress={() => {
          // navigation.push(RootScreen.detailProject, {item: item});
          navigation.push(RootScreen.ToiletDetailPage, {item: item});
        }}
        title={`${item?.Name ?? '-'}`}
        titleStyle={{...TypoSkin.title3, paddingBottom: 8}}
        subtitle={
          <View style={{alignItems: 'flex-start', flex: 1}}>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Ngày tạo: ${item?.DateCreated ? Ultis.datetoString(new Date(item.DateCreated)) : '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Phân loại: ${TypeStringData.find((e: any) => e.key === item?.Type)?.title ?? '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Vi trí: ${PlaceStringData.find((e: any) => e.key === item?.Place)?.title ?? '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Người sở hữu: ${item?.CustomerName ?? '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Số điện thoại: ${item?.Mobile ?? '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Địa chỉ: ${item?.Address ?? '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Mô tả: ${item?.Description ?? '-'}`}</Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Chứng chỉ: ${CertificateStringData.find((e: any) => e.key === item?.Certificate)?.title ?? '-'}`}</Text>
          </View>
        }
        bottom={
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
              paddingTop: 16,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            {/* actions */}
            <View
              style={{
                flexDirection: 'row',
                gap: 16,
                flex: 1,
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Status item={item} status={item?.Status} />
              <View
                style={{flexDirection: 'row', gap: 16, alignItems: 'center'}}>
                {ToiletStatus.run == item?.Status && (
                  <TouchableOpacity
                    style={{padding: 4}}
                    onPress={() => {
                      navigation.push(RootScreen.detailProject, {
                        item: item,
                        step: 'calendar',
                      });
                    }}>
                    <Winicon
                      src="outline/user interface/calendar-date-2"
                      size={16}
                    />
                  </TouchableOpacity>
                )}
                {checkEditable ? (
                  <TouchableOpacity
                    style={{padding: 4}}
                    onPress={() => {
                      showPopup({
                        ref: popupRef,
                        enableDismiss: true,
                        children: (
                          <AddEditToiletPopup
                            ref={popupRef}
                            toiletId={item.Id}
                            onDone={getData}
                          />
                        ),
                      });
                    }}>
                    <Winicon src="outline/user interface/d-edit" size={16} />
                  </TouchableOpacity>
                ) : null}

                {checkEditable ? (
                  <TouchableOpacity
                    style={{padding: 4}}
                    onPress={() => {
                      showDialog({
                        ref: dialogDelAccRef,
                        status: ComponentStatus.WARNING,
                        title: 'Bạn chắc chắn muốn xóa',
                        onSubmit: async () => {
                          if (onDelete) onDelete();
                        },
                      });
                    }}>
                    <Winicon src="outline/user interface/trash-can" size={16} />
                  </TouchableOpacity>
                ) : null}
              </View>
            </View>
          </View>
        }
      />
    </View>
  );
}

export const StatusToiletData = [
  {
    key: ToiletStatus.register,
    title: 'Đăng ký',
    backgrColor: ColorThemes.light.neutral_text_subtitle_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.consultant,
    title: 'Tư vấn',
    backgrColor: ColorThemes.light.primary_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.contract,
    title: 'Hợp đồng',
    backgrColor: ColorThemes.light.warning_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.design,
    title: 'Thiết kế',
    backgrColor: ColorThemes.light.success_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.build,
    title: 'Thi công',
    backgrColor: ColorThemes.light.secondary1_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.liquid,
    title: 'Thanh lý hợp đồng',
    backgrColor: ColorThemes.light.secondary2_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.run,
    title: 'Vận hành',
    backgrColor: ColorThemes.light.secondary3_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
];

export function Status({item, status}: any) {
  if (!status) return <View></View>;
  var st = StatusToiletData.find(e => e.key === status);

  if (!st)
    return (
      <View
        style={{
          backgroundColor: ColorThemes.light.neutral_text_subtitle_color,
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 20,
        }}>
        <Text
          style={{color: ColorThemes.light.neutral_absolute_background_color}}>
          {status}
        </Text>
      </View>
    );
  if (st)
    return (
      <View
        style={{
          backgroundColor: '#B8ECE6',
          paddingHorizontal: 15,
          paddingVertical: 4,
          borderRadius: 20,
        }}>
        <Text
          style={{
            color: '#178E84',
          }}>
          Trạng thái: {st.title ?? status}
        </Text>
      </View>
    );
}
