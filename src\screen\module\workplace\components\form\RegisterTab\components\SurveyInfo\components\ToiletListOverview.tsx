import {FlatList, View} from 'react-native';
import {useEffect, useState} from 'react';
import ToiletOverviewInfoBottomSheet from './ToiletOverviewInfoBottomSheet';
import ToiletSelectionCard from 'screen/module/toilet/components/card/ToiletSelectionCard';
import {BaseDA} from 'screen/baseDA';
import {DataController} from 'screen/base-controller';

const ToiletListOverview = ({toiletIds}: {toiletIds: string[]}) => {
  const [listToilet, setListToilet] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  const [surveyList, setSurveyList] = useState<any[]>([]);
  const [selectedSurveyData, setSelectedSurveyData] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch toilet data
        const toiletController = new DataController('Toilet');
        const toiletRes = await toiletController.getByListId(toiletIds);
        if (toiletRes.code === 200) {
          setListToilet(toiletRes.data);
        }

        // Fetch survey data for all toilets
        const surveyController = new DataController('Survey');
        const surveyRes = await surveyController.aggregateList({
          page: 1,
          size: 1000,
          searchRaw: `@ToiletId:{${toiletIds.join(' | ')}}`,
        });

        if (surveyRes.code === 200 && surveyRes.data?.length > 0) {
          // Process survey data to include file information
          const processedSurveys = await Promise.all(
            surveyRes.data.map(async (survey: any) => {
              if (survey.Design?.length) {
                const fileInfo = await BaseDA.getFilesInfor(
                  survey.Design.split(','),
                );
                if (fileInfo?.code === 200) {
                  survey.Design = fileInfo.data;
                } else {
                  survey.Design = [];
                }
              } else {
                survey.Design = [];
              }
              return survey;
            }),
          );
          setSurveyList(processedSurveys);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    if (toiletIds?.length > 0) {
      fetchData();
    }
  }, [toiletIds]);

  const handleToiletCardPress = (item: any) => {
    // Find survey data for the selected toilet
    const surveyData = surveyList.find(survey => survey.ToiletId === item.Id);

    setSelectedSurveyData(surveyData || null);
    setVisible(true);
  };

  return (
    <View>
      {listToilet.length > 0 && (
        <FlatList
          data={listToilet}
          scrollEnabled={false}
          renderItem={({item}) => (
            <ToiletSelectionCard
              item={item}
              showSelect={false}
              onPress={() => handleToiletCardPress(item)}
            />
          )}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          style={{marginBottom: 16}}
        />
      )}
      <ToiletOverviewInfoBottomSheet
        visible={visible}
        onClose={() => {
          setVisible(false);
          setSelectedSurveyData(null);
        }}
        surveyData={selectedSurveyData}
      />
    </View>
  );
};

export default ToiletListOverview;
