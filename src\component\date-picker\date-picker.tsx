import React, {createRef, useEffect, useRef, useState} from 'react';
import {
  FBottomSheet,
  FTextField,
  hideBottomSheet,
  showBottomSheet,
} from '../export-component';
import {Pressable, TextStyle, TouchableOpacity, View} from 'react-native';
import {differenceInCalendarDays} from 'date-fns';
import {inRangeTime} from '../calendar/calendar';
import {Ultis} from '../../utils/Utils';
import {SvgXml} from 'react-native-svg';
import DatePicker from 'react-native-date-picker';
import {Text} from 'react-native-paper';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorSkin} from '../../assets/skin/colors';

interface DatePickerProps {
  defaultValue?: string | undefined;
  onChange: (e?: Date) => void;
  placeholder?: string;
  disabled?: boolean;
  helperText?: string;
  helperTextColor?: string;
  style?: TextStyle;
  min?: Date;
  max?: Date;
  editable?: boolean;
  type?: 'date' | 'time' | 'datetime';
  hideSuffixIcon?: boolean;
}

export default function WDatePicker(props: DatePickerProps) {
  const {
    defaultValue,
    onChange,
    placeholder,
    disabled,
    helperText,
    helperTextColor,
    style,
    min,
    max,
    editable,
    type,
    hideSuffixIcon,
  } = props;
  const botSheetRef = useRef<any>();
  const today = new Date();
  const startDate = new Date(
    today.getFullYear() - 100,
    today.getMonth(),
    today.getDate(),
  );
  const endDate = new Date(
    today.getFullYear() + 100,
    today.getMonth(),
    today.getDate(),
  );

  const [tmpValue, setTmpValue] = useState(today);

  useEffect(() => {
    if (defaultValue) {
      console.log('====================================');
      console.log(
        'defaultValue',
        defaultValue,
        Ultis.stringToDate(defaultValue),
      );
      console.log('====================================');
      setTmpValue(Ultis.stringToDate(defaultValue));
    } else {
      setTmpValue(today);
    }
  }, [defaultValue]);

  const showPicker = () => {
    setTmpValue(Ultis.stringToDate(defaultValue));
    showBottomSheet({
      ref: botSheetRef,
      titleText: ' ',
      enableDismiss: true,
      prefixAction: (
        <TouchableOpacity
          style={{paddingVertical: 6, paddingHorizontal: 4}}
          onPress={() => {
            hideBottomSheet(botSheetRef);
          }}>
          <Text
            style={[TypoSkin.buttonText3, {color: ColorSkin.textColorGrey2}]}>
            Hủy
          </Text>
        </TouchableOpacity>
      ),
      suffixAction: (
        <TouchableOpacity
          style={{paddingVertical: 6, paddingHorizontal: 4}}
          onPress={() => {
            // setValue({ selectvalue: tmpValue, selectDate: tmpValue.getDate(), selectMonth: tmpValue.getMonth(), selectYear: tmpValue.getFullYear(), selectHour: tmpValue.getHours(), selectMin: tmpValue.getMinutes(), selectSec: tmpValue.getSeconds() })
            onChange(tmpValue);
            hideBottomSheet(botSheetRef);
          }}>
          <Text style={[TypoSkin.buttonText3, {color: ColorSkin.primary}]}>
            Xác nhận
          </Text>
        </TouchableOpacity>
      ),
      children: (
        <View
          style={{
            overflow: 'hidden',
            flexDirection: 'row',
            paddingHorizontal: 16,
          }}>
          <DatePicker
            date={tmpValue}
            mode={type ?? 'date'}
            minimumDate={min ?? startDate}
            maximumDate={max ?? endDate}
            dividerColor={'#f2f5f8'}
            theme="auto"
            onDateChange={vl => {
              setTmpValue(vl);
            }}
          />
        </View>
      ),
    });
  };

  return (
    <View
      style={{
        flex: style?.flex ?? 1,
        width: style?.width,
        overflow: 'visible',
      }}>
      <FBottomSheet ref={botSheetRef} />
      <View>
        <FTextField
          placeholder={placeholder}
          disabled={editable === false || disabled}
          disabledBg={disabled ? '#f2f5f8' : '#FFFFFFFF'}
          onPress={disabled ? undefined : showPicker}
          helperText={helperText}
          helperTextColor={helperTextColor}
          style={{paddingRight: 8, height: '100%', ...(style ?? {})}}
          type="phone-pad"
          value={Ultis.datetoString(tmpValue)}
          onBlur={() => {
            onChange(tmpValue);
          }}
          suffix={
            hideSuffixIcon ? undefined : (
              <TouchableOpacity
                disabled={disabled}
                onPress={showPicker}
                style={{padding: 8, paddingRight: 2}}>
                <CalendarIcon />
              </TouchableOpacity>
            )
          }
        />
      </View>
    </View>
  );
}

const CalendarIcon = () => (
  <SvgXml
    xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.16667 11.1667C5.70643 11.1667 5.33333 11.5398 5.33333 12V12.8333C5.33333 13.2936 5.70643 13.6667 6.16667 13.6667H7.83333C8.29357 13.6667 8.66667 13.2936 8.66667 12.8333V12C8.66667 11.5398 8.29357 11.1667 7.83333 11.1667H6.16667Z" fill="#667994"/>
    <path d="M11.1667 11.1667C10.7064 11.1667 10.3333 11.5398 10.3333 12V12.8333C10.3333 13.2936 10.7064 13.6667 11.1667 13.6667H12.8333C13.2936 13.6667 13.6667 13.2936 13.6667 12.8333V12C13.6667 11.5398 13.2936 11.1667 12.8333 11.1667H11.1667Z" fill="#667994"/>
    <path d="M5.33333 16.1667C5.33333 15.7064 5.70643 15.3333 6.16667 15.3333H7.83333C8.29357 15.3333 8.66667 15.7064 8.66667 16.1667V17C8.66667 17.4602 8.29357 17.8333 7.83333 17.8333H6.16667C5.70643 17.8333 5.33333 17.4602 5.33333 17V16.1667Z" fill="#667994"/>
    <path d="M11.1667 15.3333C10.7064 15.3333 10.3333 15.7064 10.3333 16.1667V17C10.3333 17.4602 10.7064 17.8333 11.1667 17.8333H12.8333C13.2936 17.8333 13.6667 17.4602 13.6667 17V16.1667C13.6667 15.7064 13.2936 15.3333 12.8333 15.3333H11.1667Z" fill="#667994"/>
    <path d="M15.3333 12C15.3333 11.5398 15.7064 11.1667 16.1667 11.1667H17.8333C18.2936 11.1667 18.6667 11.5398 18.6667 12V12.8333C18.6667 13.2936 18.2936 13.6667 17.8333 13.6667H16.1667C15.7064 13.6667 15.3333 13.2936 15.3333 12.8333V12Z" fill="#667994"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.83333 2.83333C7.83333 2.3731 7.46024 2 7 2C6.53976 2 6.16667 2.3731 6.16667 2.83333V3.66667H4.5C3.11929 3.66667 2 4.78595 2 6.16667V18.6667C2 20.0474 3.11929 21.1667 4.5 21.1667H19.5C20.8807 21.1667 22 20.0474 22 18.6667V6.16667C22 4.78595 20.8807 3.66667 19.5 3.66667H17.8333V2.83333C17.8333 2.3731 17.4602 2 17 2C16.5398 2 16.1667 2.3731 16.1667 2.83333V3.66667H7.83333V2.83333ZM4.5 5.33333C4.03976 5.33333 3.66667 5.70643 3.66667 6.16667V7.83333H20.3333V6.16667C20.3333 5.70643 19.9602 5.33333 19.5 5.33333H17.8333C17.8333 5.79357 17.4602 6.16667 17 6.16667C16.5398 6.16667 16.1667 5.79357 16.1667 5.33333H7.83333C7.83333 5.79357 7.46024 6.16667 7 6.16667C6.53976 6.16667 6.16667 5.79357 6.16667 5.33333H4.5ZM4.5 19.5C4.03976 19.5 3.66667 19.1269 3.66667 18.6667V9.5H20.3333V18.6667C20.3333 19.1269 19.9602 19.5 19.5 19.5H4.5Z" fill="#667994"/>
    </svg>`}
    width={16}
    height={16}
  />
);
