import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootScreen, svdata} from '../../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {useState, useEffect} from 'react';
import {TouchableNativeFeedback, Keyboard, View} from 'react-native';
import ReactNativeBiometrics, {BiometryTypes} from 'react-native-biometrics';
import {useDispatch} from 'react-redux';
import {FSnackbar} from '../../../../component/export-component';
import MapViewIndex from '../../../../features/map/views/map-view';
import {
  useSelectorCateServiceState,
  useSelectorCustomerState,
} from '../../../../redux/hooks/hooks';
import {ServiceActions} from '../../../../redux/reducers/cateServices/reducer';
import {saveDataToAsyncStorage} from '../../../../utils/AsyncStorage';
import CompanyView from '../../../module/customer/CompanyView';
import PolicyView from '../../../module/customer/policy';
import ResultScanQrcode from '../../../module/customer/ResultScanQrcode';
import SettingProfile from '../../../module/customer/SettingProfile';
import ForgotPass from '../../../module/login/components/forgot-pass';
import Login from '../../../module/login/login';
import NotificationIndex from '../../../module/notification/view';
import DetailProduct from '../../../pages/DetailProduct';
import MaterialList from '../../../module/product/view/MaterialList';
import ProductList from '../../../module/product/view/ProductList';
import TrustProductView from '../../../module/product/view/TrustProductView';
import ContactFlow from '../../../module/service/components/contacts/ContactsFlow';
import CreateFlow from '../../../module/service/components/create/CreateFlow';
import CleanFlow from '../../../module/service/components/edit/CleanFlow';
import RepairFlow from '../../../module/service/components/edit/RepairFlow';
import WebViewServiceFlow from '../../../module/service/components/edu/EduFlow';
import ServicesWorkFlow from '../../../module/service/components/ServicesWorkFlow';
import DetailProject from '../../../module/toilet/view/detailProject';
import ToiletList from '../../../module/toilet/view/ToiletsList';
import MainLayout from '../../main-layout';
import Splash from '../../splash';
import ProductIndex from '../../../module/product/ProductIndex';
import CartPage from '../../../pages/CartPage/CartPage';
import CheckoutPage from '../../../pages/CheckoutPage/CheckoutPage';
import TabRegisterPartner from '../../../module/partner/component/TabRegisterPartner';
import ManageProductPage from '../../../pages/ProductPage/ManageProductPage';
import FavoriteProductPage from '../../../pages/FavoriteProductPage/FavoriteProductPage';
import ProductListByCategory from '../../../pages/ProductListByCategory/ProductListByCategory';
import DetailProductPage from '../../../pages/DetailProductPage/DetailProductPage';
import CreateProductPartnerPage from '../../../pages/ProductPage/CreateProductPartnerPage';
import EditAddress from '../../../module/customer/form/EditAddress';
import CreateReviewOrder from '../../../module/orderProduct/CreateReviewOrder';
import OrderCustomerDetail from '../../../module/orderProduct/OrderCustomerDetail/OrderCustomerDetail';
import Review from '../../../module/partner/Review';
import PromorionProductPage from '../../../pages/PromorionProductPage/PromorionProductPage';
import MyAddress from '../../../module/customer/myAddress/myAddress';
import OrderDetailPage from '../../../pages/OrderDetailPage/OrderDetailPage';
import OrderShopDetail from '../../../module/orderProduct/OrderShopDetail/OrderShopDetail';
import RegisterCateCriterionPage from '../../../pages/RegisterCateCriterionPage/RegisterCateCriterionPage';
import ToiletDetailPage from '../../../pages/ToiletDetailPage/ToiletDetailPage';
import ListSvgPage from '../../../pages/ListSvgPage';
import RatingPage from '../../../pages/RatingPage/RatingPage';
import InforShopView from '../../../module/partner/inforShopView/inforShopView';
import ProductShopPage from '../../../pages/ProductShopPage/ProductShopPage';
import CetificateAchievementPage from '../../../pages/CetificateAchievementPage/CetificateAchievementPage';
import OperationalStaticDetailPage from '../../../pages/CetificateAchievementPage/OperationalStaticDetailPage';
import CreateSurveyStepPage from '../../../pages/CreateSurveyStepPage';
import OperationalStatisticsActionForKtx from '../../../pages/CetificateAchievementPage/Component/OperationalStatisticsActionForKtx';
import DetailToiletServicePage from '../../../pages/DetailToiletServicePage';
import ShopInfo from '../../../module/partner/component/ShopInfo';
import ManagePayment from '../../../module/partner/component/ManagePayment';
import DetailNews from '../../../pages/DetailNews';
import DetailBrand from '../../../pages/DetailBrand';
import MyTicketList from 'screen/module/ticket/components/MyListTicket';
import DetailTypicalProject from 'screen/pages/DetailTypicalProject';
import OrderDetailPageForShop from 'screen/module/orderProduct/OrderShopDetail/component/OrderDetailPageForShop';
import FAQView from 'screen/module/customer/FAQView';
import CompanyInfoPage from 'screen/pages/CompanyInfoPage';

const Stack = createNativeStackNavigator();

export function KtxStackNavigator() {
  const [isReady, setIsReady] = useState(true);
  const dispatch = useDispatch<any>();
  const navigation = useNavigation<any>();
  const serviceData = useSelectorCateServiceState().data;
  const [services, setServices] = useState<Array<any>>([]);
  const {data} = useSelectorCustomerState();

  useEffect(() => {
    if (serviceData?.length > 0) {
      serviceData.forEach(item => {
        for (let index = 0; index < svdata.length; index++) {
          const element = svdata[index];
          if (
            element.RouteName.toLowerCase() === item.RouteName.toLowerCase()
          ) {
            element.isShow = true;
            if (
              services.findIndex(x => x.RouteName === item.RouteName) === -1
            ) {
              setServices([...services, element]);
            }
          }
        }
      });
    }
  }, [serviceData]);

  const stackServices = () => {
    return services.map((item, index) => {
      return (
        <Stack.Screen
          key={item.RouteName}
          name={item.name}
          component={item.view}
        />
      );
    });
  };

  const rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });
  useEffect(() => {
    rnBiometrics.isSensorAvailable().then(resultObject => {
      const {available, biometryType} = resultObject;
      if (available && biometryType === BiometryTypes.TouchID) {
        console.log('TouchID is supported');
        saveDataToAsyncStorage('spBiometrics', 'true');
        saveDataToAsyncStorage('biometryType', 'TouchID');
      } else if (available && biometryType === BiometryTypes.FaceID) {
        console.log('FaceID is supported');
        saveDataToAsyncStorage('spBiometrics', 'true');
        saveDataToAsyncStorage('biometryType', 'FaceID');
      } else if (available && biometryType === BiometryTypes.Biometrics) {
        console.log('Biometrics is supported');
        saveDataToAsyncStorage('spBiometrics', 'true');
        saveDataToAsyncStorage('biometryType', 'true');
      } else {
        console.log('Biometrics not supported');
        saveDataToAsyncStorage('spBiometrics', 'false');
        saveDataToAsyncStorage('biometryType', 'false');
      }
    });
  }, []);

  useEffect(() => {
    ServiceActions.getCateServices(dispatch);
  }, []);

  return (
    <Splash isAppReady={isReady}>
      <TouchableNativeFeedback
        onPress={Keyboard.dismiss}
        style={{flex: 1, width: '100%'}}>
        <View style={{flex: 1, width: '100%'}}>
          <FSnackbar />
          <Stack.Navigator
            initialRouteName={data ? RootScreen.navigateView : RootScreen.login}
            screenOptions={{headerShown: false, orientation: 'portrait'}}>
            <Stack.Screen
              name={RootScreen.navigateView}
              component={MainLayout}
            />
            <Stack.Screen name={RootScreen.login} component={Login} />
            <Stack.Screen name={RootScreen.ForgotPass} component={ForgotPass} />
            {/* notification */}
            <Stack.Screen
              name={RootScreen.NotificationIndex}
              component={NotificationIndex}
            />
            {/* services call */}
            {stackServices()}
            {/* services flow */}
            <Stack.Screen
              name={RootScreen.ServicesWorkFlow}
              component={ServicesWorkFlow}
            />
            <Stack.Screen
              name={RootScreen.WebViewServiceFlow}
              component={WebViewServiceFlow}
            />
            <Stack.Screen
              name={RootScreen.ContactFlow}
              component={ContactFlow}
            />
            <Stack.Screen name={RootScreen.CreateFlow} component={CreateFlow} />
            <Stack.Screen name={RootScreen.RepairFlow} component={RepairFlow} />
            <Stack.Screen name={RootScreen.CleanFlow} component={CleanFlow} />
            {/* screens */}
            <Stack.Screen
              name={RootScreen.detailProject}
              component={DetailProject}
            />
            {/* manages */}
            <Stack.Screen name={RootScreen.ToiletList} component={ToiletList} />
            <Stack.Screen
              name={RootScreen.ProductList}
              component={ProductList}
            />
            <Stack.Screen
              name={RootScreen.MaterialList}
              component={MaterialList}
            />
            <Stack.Screen
              name={RootScreen.MyTicketList}
              component={MyTicketList}
            />
            {/*  */}
            <Stack.Screen
              name={RootScreen.mapViewIndex}
              component={MapViewIndex}
            />
            {/* workplace */}
            <Stack.Screen
              name={RootScreen.DetailWorkView}
              component={DetailToiletServicePage}
            />
            {/* profile setting */}
            <Stack.Screen
              name={RootScreen.SettingProfile}
              component={SettingProfile}
            />
            <Stack.Screen name={RootScreen.PolicyView} component={PolicyView} />
            <Stack.Screen name={RootScreen.FAQView} component={FAQView} />
            <Stack.Screen
              name={RootScreen.ProductView}
              component={TrustProductView}
            />
            <Stack.Screen
              name={RootScreen.DetailProduct}
              component={DetailProduct}
            />
            <Stack.Screen
              name={RootScreen.CompanyView}
              component={CompanyView}
            />
            <Stack.Screen
              name={RootScreen.ResultScanQrcode}
              component={ResultScanQrcode}
            />
            <Stack.Screen
              name={RootScreen.ProductIndex}
              component={ProductIndex}
            />
            <Stack.Screen
              name={RootScreen.ProductListByCategory}
              component={ProductListByCategory}
            />
            <Stack.Screen name={RootScreen.CartPage} component={CartPage} />
            <Stack.Screen
              name={RootScreen.CheckoutPage}
              component={CheckoutPage}
            />
            <Stack.Screen
              name={RootScreen.TabRegisterPartner}
              component={TabRegisterPartner}
            />
            <Stack.Screen
              name={RootScreen.FavoriteProductPage}
              component={FavoriteProductPage}
            />
            <Stack.Screen
              name={RootScreen.DetailProductPage}
              component={DetailProductPage}
            />
            <Stack.Screen
              name={RootScreen.ManageProduct}
              component={ManageProductPage}
            />
            <Stack.Screen
              name={RootScreen.CreateProductPartnerPage}
              component={CreateProductPartnerPage}
            />
            <Stack.Screen
              name={RootScreen.EditAddress}
              component={EditAddress}
            />
            <Stack.Screen name={RootScreen.MyAddress} component={MyAddress} />
            <Stack.Screen
              name={RootScreen.PromorionProductPage}
              component={PromorionProductPage}
            />
            <Stack.Screen
              name={RootScreen.OrderDetailPage}
              component={OrderDetailPage}
            />
            <Stack.Screen
              name={RootScreen.CreateReviewOrder}
              component={CreateReviewOrder}
            />
            <Stack.Screen
              name={RootScreen.OrderCustomerDetail}
              component={OrderCustomerDetail}
            />
            <Stack.Screen name={RootScreen.Review} component={Review} />
            <Stack.Screen
              name={RootScreen.OrderShopDetail}
              component={OrderShopDetail}
            />
            <Stack.Screen
              name={RootScreen.RegisterCateCriterionPage}
              component={RegisterCateCriterionPage}
            />
            <Stack.Screen
              name={RootScreen.OrderDetailPageForShop}
              component={OrderDetailPageForShop}
            />
            <Stack.Screen
              name={RootScreen.ToiletDetailPage}
              component={ToiletDetailPage}
            />
            <Stack.Screen
              name={RootScreen.ListSvgPage}
              component={ListSvgPage}
            />
            <Stack.Screen name={RootScreen.RatingPage} component={RatingPage} />
            <Stack.Screen
              name={RootScreen.InforShopView}
              component={InforShopView}
            />
            <Stack.Screen
              name={RootScreen.ProductShopPage}
              component={ProductShopPage}
            />
            <Stack.Screen
              name={RootScreen.CreateSurveyStepPage}
              component={CreateSurveyStepPage}
            />
            {/*  */}
            <Stack.Screen
              name={RootScreen.CetificateAchievementPage}
              component={CetificateAchievementPage}
            />
            <Stack.Screen
              name={RootScreen.OperationalStaticDetailPage}
              component={OperationalStaticDetailPage}
            />
            <Stack.Screen
              name={RootScreen.OperationalStatisticsActionForKtx}
              component={OperationalStatisticsActionForKtx}
            />

            <Stack.Screen name={RootScreen.ShopInfo} component={ShopInfo} />
            <Stack.Screen
              name={RootScreen.ManagePayment}
              component={ManagePayment}
            />
            <Stack.Screen name={RootScreen.DetailNews} component={DetailNews} />
            <Stack.Screen
              name={RootScreen.DetailBrand}
              component={DetailBrand}
            />
            <Stack.Screen
              name={RootScreen.DetailTypicalProject}
              component={DetailTypicalProject}
            />
            <Stack.Screen
              name={RootScreen.CompanyInfoPage}
              component={CompanyInfoPage}
            />
          </Stack.Navigator>
        </View>
      </TouchableNativeFeedback>
    </Splash>
  );
}
