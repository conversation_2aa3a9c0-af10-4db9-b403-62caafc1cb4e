import {DataController} from '../../../../screen/base-controller';
import {CartItem} from '../../../../types/cartTypes';
import {randomGID} from '../../../../utils/Utils';
import {CartActions} from '../CartReducer';
import {store} from '../../../store/store';
import {showSnackbar} from '../../../../component/export-component';
import {ComponentStatus} from '../../../../component/component-status';

/**
 * Kiểm tra thông tin sản phẩm và số lượng tồn kho
 * @param productId - ID của sản phẩm cần kiểm tra
 * @returns Promise<{isValid: boolean, stockQuantity: number}>
 */
async function validateProductStock(productId: string) {
  const productController = new DataController('Product');
  const productData = await productController.getById(productId);

  // Kiểm tra sản phẩm có tồn tại không
  if (!productData || !productData?.data) {
    showSnackbar({
      message: 'Không tìm thấy thông tin sản phẩm',
      status: ComponentStatus.ERROR,
    });
    return {isValid: false, stockQuantity: 0};
  }

  // const stockQuantity =  productData?.data?.InStock || 0;
  const stockQuantity = 10;

  // Kiểm tra tồn kho có đủ hay không
  if (stockQuantity < 1) {
    showSnackbar({
      message: 'Sản phẩm hiện đã hết hàng',
      status: ComponentStatus.ERROR,
    });
    return {isValid: false, stockQuantity: 0};
  }

  return {isValid: true, stockQuantity};
}

/**
 * Kiểm tra số lượng có thể thêm vào giỏ hàng
 * @param product - Thông tin sản phẩm
 * @param quantity - Số lượng muốn thêm
 * @param stockQuantity - Số lượng tồn kho
 * @param cartItems - Danh sách sản phẩm trong giỏ hàng
 * @returns boolean - true nếu có thể thêm, false nếu không thể
 */
async function validateCartQuantity(
  product: any,
  quantity: number,
  stockQuantity: number,
  cartItems: any[],
) {
  // Tìm sản phẩm hiện có trong giỏ hàng
  const existingCartItem = cartItems.find(
    item => item.ProductId === product.Id,
  );

  const currentCartQuantity = existingCartItem ? existingCartItem.Quantity : 0;
  const totalQuantityAfterAdd = currentCartQuantity + quantity;

  // Kiểm tra xem tổng số lượng có vượt quá tồn kho không
  if (totalQuantityAfterAdd > stockQuantity) {
    const availableQuantity = stockQuantity - currentCartQuantity;

    if (availableQuantity <= 0) {
      showSnackbar({
        message: 'Sản phẩm đã đạt giới hạn tồn kho trong giỏ hàng',
        status: ComponentStatus.ERROR,
      });
    } else {
      showSnackbar({
        message: `Chỉ có thể thêm tối đa ${availableQuantity} sản phẩm nữa`,
        status: ComponentStatus.ERROR,
      });
    }
    return false;
  }

  return true;
}

/**
 * Lấy Thông tin đối tác và customer cho các sản phẩm trong giỏ hàng
 * @param cartItems - Danh sách sản phẩm trong giỏ hàng
 * @param productShopId - ID shop của sản phẩm mới
 * @returns Promise<any> - Dữ liệu shop và customer
 */
async function fetchShopData(cartItems: any[], productShopId: string) {
  // Lấy danh sách ID shop từ giỏ hàng và sản phẩm mới
  const shopIds = [...cartItems.map(item => item.ShopId), productShopId];

  const uniqueShopIds = Array.from(new Set(shopIds));
  const shopController = new DataController('Shop');

  return await shopController.getPatternList({
    page: 1,
    size: 100,
    query: `@Id: {${uniqueShopIds.join(' | ')}}`,
    pattern: {
      CustomerId: ['Id', 'Name', 'AvatarUrl'],
    },
  });
}

/**
 * Cập nhật Thông tin đối tác cho các sản phẩm trong giỏ hàng
 * @param cartItems - Danh sách sản phẩm trong giỏ hàng
 * @param shopsData - Dữ liệu shop và customer
 * @returns any[] - Danh sách sản phẩm đã được cập nhật Thông tin đối tác
 */
async function enrichCartItemsWithShopInfo(cartItems: any[], shopsData: any) {
  return cartItems.map(item => {
    const shop = shopsData.data.find((shop: any) => shop.Id === item.ShopId);

    return {
      ...item,
      ShopName: shop?.Name,
      ShopAvatar: shopsData.Customer?.find(
        (customer: any) => customer.Id === shop?.CustomerId,
      )?.AvatarUrl,
    };
  });
}

/**
 * Tạo đối tượng CartItem mới từ thông tin sản phẩm
 * @param product - Thông tin sản phẩm
 * @param quantity - Số lượng
 * @param shopsData - Dữ liệu shop và customer
 * @returns CartItem - Đối tượng sản phẩm trong giỏ hàng
 */
async function createCartItem(product: any, quantity: number, shopsData: any) {
  const shop = shopsData.data.find((shop: any) => shop.Id === product.ShopId);

  return {
    id: randomGID(),
    ProductId: product.Id,
    Name: product.Name,
    Price: product.Price,
    Quantity: quantity,
    Img: product.Img,
    ShopId: product.ShopId,
    ShopName: shop?.Name,
    ShopAvatar: shopsData.Customer?.find(
      (customer: any) => customer.Id === shop?.CustomerId,
    )?.AvatarUrl,
    Discount: product.Discount,
    selected: true,
    dateAdded: Date.now(),
    CategoryId: product.CategoryId,
    IsFreeShip: product.IsFreeShip,
  };
}

export {
  validateProductStock,
  validateCartQuantity,
  fetchShopData,
  enrichCartItemsWithShopInfo,
  createCartItem,
};
