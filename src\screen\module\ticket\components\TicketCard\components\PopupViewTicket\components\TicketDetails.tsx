import React from 'react';
import {Text, View, StyleSheet, FlatList} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import {FileAttachments} from 'screen/module/ticket/components/TicketCard/components/FileAttachments';
import {CustomerInfo} from './CustomerInfo';
import {TicketType} from 'types/ticketType';
import {
  parseTicketDetails,
  TicketDetailItem,
} from 'screen/module/ticket/utils/ticketDetailParser';

interface TicketDetailsProps {
  item: TicketType;
  typeLabel: any;
  relativeData: any;
  handleRelativeDataPress: () => void;
  fileInfor: Array<any>;
  onFilePress: (file: any) => void;
  customers: Array<any>;
}

export const TicketDetails: React.FC<TicketDetailsProps> = ({
  item,
  typeLabel,
  relativeData,
  handleRelativeDataPress,
  fileInfor,
  onFilePress,
  customers,
}) => {
  const details = item?.Detail ? parseTicketDetails(item?.Detail) : [];
  if (!customers.length) return <View></View>;

  const renderDetail = (dt: TicketDetailItem, i: number) => {
    const customer = customers.find(e => e.Id === dt.CustomerId);
    if (!customer) return <View key={i}></View>;
    return <CustomerInfo customer={customer} />;
  };

  const renderCustomer = () => {
    if (!details || !details.length) return <View></View>;
    return (
      <FlatList
        data={details}
        scrollEnabled={false}
        renderItem={({item, index}) => renderDetail(item, index)}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  };

  return (
    <View style={styles.content}>
      <CustomerInfo
        customer={customers?.find(e => e.Id === item?.CustomerId)}
      />
      <Text style={styles.infoText}>
        Loại yêu cầu: {typeof typeLabel === 'string' ? typeLabel : '-'}
      </Text>
      <Text
        onPress={relativeData ? handleRelativeDataPress : undefined}
        style={[styles.infoText, relativeData && styles.linkText]}>
        Đối tượng liên quan:{' '}
        {relativeData ? relativeData?.Name || '-' : 'Phản hồi về KTX'}
      </Text>
      <Text style={styles.infoText}>
        Mô tả: {item?.Description?.trim() || '-'}
      </Text>
      <FileAttachments fileInfor={fileInfor} onFilePress={onFilePress} />
      {details?.length > 0 ? (
        <Text style={styles.sectionLabel}>Chi tiết xử lý:</Text>
      ) : null}
      {renderCustomer()}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 16,
    gap: 16,
  },
  infoText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  linkText: {
    textDecorationLine: 'underline',
  },
  sectionLabel: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  detailContent: {
    flex: 1,
    alignItems: 'flex-start',
    width: '100%',
    paddingTop: 8,
  },
  detailText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});
