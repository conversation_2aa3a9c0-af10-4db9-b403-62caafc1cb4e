# DetailToiletServicePage Documentation

## Mục lục
1. [Tổng quan](#tổng-quan)
2. [Cấu trúc Component](#cấu-trúc-component)
3. [Báo cáo Refactoring](#báo-cáo-refactoring)
4. [Chi tiết các lỗi đã sửa](#chi-tiết-các-lỗi-đã-sửa)
5. [Hướng dẫn sử dụng](#hướng-dẫn-sử-dụng)

## Tổng quan

DetailToiletServicePage là một component phức tạp quản lý quy trình xử lý dịch vụ toilet từ đăng ký đến hoàn thành. Component này đã được refactor toàn diện để cải thiện type safety, performance và maintainability.

## Cấu trúc Component

```
DetailToiletServicePage/
├── index.tsx                 # Component chính
├── hooks/
│   └── useToiletService.tsx  # Custom hook quản lý logic
├── components/
│   ├── TabBarComponent.tsx   # Tab navigation
│   ├── TabContent.tsx        # Nội dung các tab
│   └── HeaderActions.tsx     # Actions trong header
├── constants/
│   └── statusData.tsx        # Constants và data
└── docs/
    ├── README.md             # File này
    ├── REFACTORING_REPORT.md # Báo cáo refactoring
    └── BUGS_FIXED.md         # Chi tiết các lỗi đã sửa
```

## Báo cáo Refactoring

Xem file [REFACTORING_REPORT.md](./REFACTORING_REPORT.md) để biết chi tiết về:
- Các vấn đề đã phát hiện
- Giải pháp đã áp dụng
- Cải tiến về performance
- Khuyến nghị cho tương lai

## Chi tiết các lỗi đã sửa

Xem file [BUGS_FIXED.md](./BUGS_FIXED.md) để biết chi tiết về:
- 11 bugs đã được sửa
- Code before/after
- Mức độ nghiêm trọng của từng bug
- Impact của việc sửa bugs

## Hướng dẫn sử dụng

### Props của DetailToiletServicePage

Component này nhận props từ React Navigation:

```typescript
interface RouteParams {
  Id: string;        // Required: ID của toilet service
  Name?: string;     // Optional: Tên hiển thị
}
```

### Cách sử dụng

```typescript
// Navigation đến DetailToiletServicePage
navigation.navigate('DetailToiletServicePage', {
  Id: 'toilet-service-id',
  Name: 'Tên dịch vụ'
});
```

### States và Logic Flow

Component quản lý các states chính:
- `workData`: Thông tin toilet
- `serviceData`: Thông tin dịch vụ
- `index`: Tab hiện tại
- `isLoading`: Trạng thái loading
- `isRefreshing`: Trạng thái refresh
- `guest`: Thông tin khách hàng

### Workflow

1. **Register**: Đăng ký dịch vụ mới
2. **Consultant**: Tư vấn và báo giá
3. **Contract**: Ký hợp đồng
4. **Design**: Thiết kế
5. **Build**: Thực hiện xây dựng
6. **Liquid**: Thanh lý hợp đồng

## API Dependencies

Component sử dụng các DataController:
- `ToiletServices`: Quản lý dịch vụ toilet
- `Toilet`: Quản lý thông tin toilet
- `Customer`: Quản lý khách hàng
- `Task`: Quản lý công việc
- `Device`: Quản lý thiết bị
- `BioProduct`: Quản lý sản phẩm sinh học
- `MaterialToilet`: Quản lý vật liệu
- `Addendum`: Quản lý phụ lục hợp đồng

## Error Handling

Component đã được cải thiện error handling:
- Try-catch blocks cho tất cả async operations
- Null checks toàn diện
- Logging errors để debug
- Graceful fallbacks khi data không tồn tại

## Performance Optimizations

- Sử dụng `useMemo` cho render functions
- Sử dụng `useCallback` cho event handlers
- Tối ưu dependency arrays trong useEffect
- Giảm unnecessary re-renders

## Testing

Hiện tại component chưa có tests. Khuyến nghị thêm:
- Unit tests cho custom hook
- Integration tests cho component flow
- Error scenario tests

## Troubleshooting

### Lỗi thường gặp

1. **Missing route parameter**: Đảm bảo truyền `Id` khi navigate
2. **Network errors**: Check API endpoints và network connection
3. **Permission errors**: Đảm bảo user có quyền truy cập

### Debug

Sử dụng console logs đã được thêm vào:
```typescript
console.error('Error in getData:', error);
console.warn('getServicesData called without serviceData');
```

## Contributing

Khi modify component này:
1. Maintain type safety
2. Add proper error handling
3. Update tests (khi có)
4. Update documentation
5. Follow existing patterns

## Changelog

### v2.0.0 (Current)
- ✅ Added comprehensive type safety
- ✅ Fixed 11 critical bugs
- ✅ Improved performance with memoization
- ✅ Added proper error handling
- ✅ Enhanced null safety

### v1.0.0 (Previous)
- Basic functionality
- Multiple type safety issues
- Performance problems
- Limited error handling

---

**Lưu ý**: Documentation này được tạo sau quá trình refactoring toàn diện. Mọi thay đổi trong tương lai nên update documentation tương ứng.
