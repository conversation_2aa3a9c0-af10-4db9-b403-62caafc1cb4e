import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  FlatList,
  useWindowDimensions,
  Pressable,
} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {useRoute, useNavigation} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import AppSvg from '../../../component/AppSvg';
import iconSvg from '../../../svgs/iconSvg';
import {ProductItem} from '../../../types/ProductType';
import {BrandItem} from '../../module/brand/brandDa';
import ProductBestSellerCard from '../../module/product/component/card/ProductBestSellerCard';
import {navigate, RootScreen} from '../../../router/router';
import {showSnackbar} from '../../../component/snackbar/snackbar';
import {ComponentStatus} from '../../../component/component-status';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../../redux/reducers/cart/CartReducer';
import brandDA from '../../module/brand/brandDa';
import productDA from '../../module/product/productDA';
import Content from '../DetailNews/components/Content';

const DetailBrandPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();

  // Lấy brandId từ route params
  const {id: brandId} = route.params as {id: string};

  const [brand, setBrand] = useState<BrandItem | null>(null);
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadBrandDataCallback = React.useCallback(async () => {
    try {
      setLoading(true);

      // Fetch brand detail by ID
      const brandResult = await brandDA.fetchById(brandId);

      if (brandResult) {
        setBrand(brandResult);
      }

      // Fetch products by brand ID
      const productsResult = await productDA.getProductsByBrandId(
        brandId,
        1,
        50,
      );
      setProducts(productsResult.data || []);
    } catch (error) {
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [brandId]);

  useEffect(() => {
    if (brandId) {
      loadBrandDataCallback();
    } else {
      setLoading(false); // Tắt loading nếu không có brandId
    }
  }, [brandId]);

  // Fallback timeout để đảm bảo loading không bị stuck
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.log('Timeout: Force setting loading to false');
        setLoading(false);
        showSnackbar({
          message: 'Timeout khi tải dữ liệu',
          status: ComponentStatus.ERROR,
        });
      }
    }, 30000); // 30 seconds timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  const handleRefresh = () => {
    setRefreshing(true);
    loadBrandDataCallback();
  };

  const handleProductPress = (item: ProductItem) => {
    navigate(RootScreen.DetailProductPage, {id: item.Id});
  };

  const handleAddToCart = (item: ProductItem) => {
    CartActions.addItemToCart(item, 1)(dispatch);
    showSnackbar({
      message: 'Đã thêm sản phẩm vào giỏ hàng',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleFavoritePress = (item: ProductItem) => {
    // Cập nhật trạng thái favorite
    setProducts(prevProducts =>
      prevProducts.map(product =>
        product.Id === item.Id
          ? {...product, IsFavorite: !product.IsFavorite}
          : product,
      ),
    );

    showSnackbar({
      message: item.IsFavorite
        ? 'Đã xóa khỏi danh sách yêu thích'
        : 'Đã thêm vào danh sách yêu thích',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const renderProductItem = ({item}: {item: ProductItem}) => (
    <View>
      <ProductBestSellerCard
        item={item}
        onPress={handleProductPress}
        onAddToCart={handleAddToCart}
        onFavoritePress={handleFavoritePress}
        width={(width - 48) / 2}
        height={((width - 48) / 2) * 1.8}
      />
    </View>
  );

  if (loading) {
    console.log('Showing loading screen');
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Đang tải...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header với nút back */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}>
        <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
      </TouchableOpacity>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }>
        <Pressable>
          {/* Ảnh brand */}
          <FastImage
            source={{uri: brand?.Img}}
            style={styles.brandImage}
            resizeMode={FastImage.resizeMode.contain}
          />

          <Text style={styles.title}>{brand?.Name}</Text>

          {/* Thông tin brand */}
          <Content data={brand?.Description} />
          {products.length > 0 && (
            <View style={styles.productsSection}>
              <Text style={styles.sectionTitle}>Sản phẩm của thương hiệu</Text>
              <FlatList
                data={products}
                renderItem={renderProductItem}
                keyExtractor={item => item.Id}
                numColumns={2}
                scrollEnabled={false}
                contentContainerStyle={styles.productsList}
                columnWrapperStyle={styles.productRow}
              />
            </View>
          )}
        </Pressable>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  title: {
    ...TypoSkin.title1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
    marginHorizontal: 12,
    marginTop: 12,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  brandImage: {
    width: '100%',
    height: 300,
  },
  brandInfoContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  brandName: {
    ...TypoSkin.title1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
  },
  productsSection: {
    padding: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 16,
  },
  productsList: {
    paddingBottom: 20,
  },
  productRow: {
    justifyContent: 'space-between',
  },
});

export default DetailBrandPage;
