import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import {FDialog} from '../../../../../../../component/export-component';
import {TextFieldForm} from '../../../../../../../project-component/component-form';
import ScreenHeader from '../../../../../../layout/header';
import {closePopup, FPopup} from '../../../../../../../component/popup/popup';
import {Winicon} from '../../../../../../../component/export-component';
import {SkeletonImage} from '../../../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../../../config/configApi';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import AppButton from '../../../../../../../component/button';
import {useForm} from 'react-hook-form';
import {randomGID, Ultis} from '../../../../../../../utils/Utils';
import {DeviceBioStatus} from '../../../../../service/components/da';
import {DataController} from '../../../../../../base-controller';
import {BaseDA} from '../../../../../../baseDA';
import ImagePicker from 'react-native-image-crop-picker';

type Props = {
  toiletId: any;
  id: any;
  onSubmit: () => void;
  formId: any;
};

export const PopupAddEditDevice = forwardRef(function PopupAddEditDevice(
  data: Props,
  ref: any,
) {
  const {toiletId, id, onSubmit, formId} = data;
  const dialogRef = useRef<any>();
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      ToiletId: toiletId,
      Status: formId ? DeviceBioStatus.inactive : DeviceBioStatus.active,
      Price: undefined,
      Discount: undefined,
      Vat: undefined,
    },
  });

  const controller = new DataController('BioProduct');
  const popupRef = useRef<any>();
  const [img, setImg] = useState<Array<any>>([]);

  useEffect(() => {
    let isMounted = true;
    if (id) {
      controller.getById(id).then(async res => {
        if (!isMounted) return;
        if (res.code === 200) {
          if (res.data.ProductId) {
            const productController = new DataController('Product');
            const product = await productController.getById(res.data.ProductId);
            if (product.code === 200) {
              res.data.Img = product.data.Img;
              res.data.Description = product.data.Specifications;
              res.data.Unit = product.data.Unit;
            }
          }
          Object.keys(res.data).forEach(key => {
            if (key === 'Price') {
              methods.setValue(key, Ultis.money(res.data[key]));
            } else {
              methods.setValue(key, `${res.data[key] ?? ''}`);
            }
          });
          const _tmpFileIds = res.data.Img;
          const ids = Array.isArray(_tmpFileIds)
            ? _tmpFileIds
            : `${_tmpFileIds ?? ''}`.split(',').filter(Boolean);
          if (ids.length) {
            const resFile = await BaseDA.getFilesInfor(ids);
            if (resFile.code === 200)
              setImg([
                ...resFile.data.filter(
                  (e: any) => e !== undefined && e !== null,
                ),
              ]);
          }
        }
      });
    }
    return () => {
      isMounted = false;
    };
  }, [id, controller, methods]);

  const pickerImg = async () => {
    const image = await ImagePicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 1,
    });
    if (image) {
      _uploadFiles({
        name: (image as any).filename ?? 'new file img',
        type: (image as any).mime,
        uri: (image as any).path,
      });
    }
  };

  const _uploadFiles = async (files: {
    uri: string;
    type: string;
    name: string;
  }) => {
    const res = await BaseDA.uploadFiles([files]);
    if (res?.length) {
      setImg([res[0]]);
      methods.setValue('Img', res.map((e: any) => e.Id).join(','));
    }
  };

  const _onSubmit = async () => {
    const rawPrice = methods.getValues('Price');
    const parsedPrice =
      typeof rawPrice === 'string'
        ? parseInt(rawPrice.replace(/,/g, ''))
        : rawPrice;
    const item = {
      ...methods.getValues(),
      Img: methods.watch('Img') ? methods.watch('Img') : undefined,
      Quantity: methods.getValues('Quantity')
        ? parseInt(methods.getValues('Quantity'))
        : undefined,
      Vat: methods.getValues('Vat')
        ? parseInt(methods.getValues('Vat'))
        : undefined,
      Discount: methods.getValues('Discount')
        ? parseInt(methods.getValues('Discount'))
        : undefined,
      Price: parsedPrice ?? 0,
      DateCreated: Date.now(),
    };

    if (id) {
      await controller.edit([item]);
      onSubmit();
    } else {
      await controller.add([item]);
      onSubmit();
    }
    closePopup(ref);
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  return (
    <SafeAreaView style={styles.container}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={styles.header}
        title={
          id ? `Chỉnh sửa chế phẩm sinh học` : 'Thêm mới chế phẩm sinh học'
        }
        prefix={<View />}
        action={
          <View style={styles.headerAction}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 75 : 0}
        style={styles.formWrap}>
        <ScrollView>
          <View style={styles.formContainer}>
            <TextFieldForm
              required
              label="Tên chế phẩm"
              textFieldStyle={styles.textFieldInset}
              style={styles.fullWidth}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Name"
            />
            <View style={styles.imgWrap}>
              <Text numberOfLines={1} style={TypoSkin.label3}>
                Ảnh
              </Text>
              {methods.watch('Img') && img?.length > 0 ? null : (
                <TouchableOpacity onPress={pickerImg} style={styles.addImgBox}>
                  <SkeletonImage
                    source={{
                      uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                    }}
                    style={styles.addImgIcon}
                  />
                  <Text
                    numberOfLines={1}
                    style={[TypoSkin.buttonText4, styles.addImgText]}>
                    Thêm ảnh
                  </Text>
                </TouchableOpacity>
              )}
              {methods.watch('Img') &&
                img?.map((item: any, index: number) => {
                  return (
                    <ListTileItem
                      key={`${index}`}
                      item={item}
                      onRemove={() => {
                        methods.setValue('Img', undefined);
                        setImg([]);
                      }}
                    />
                  );
                })}
            </View>
            <TextFieldForm
              required
              label="Số lượng"
              textFieldStyle={styles.textFieldInset}
              style={styles.fullWidth}
              type="number-pad"
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Quantity"
            />
            <TextFieldForm
              label="Đơn vị"
              textFieldStyle={styles.textFieldInset}
              style={styles.fullWidth}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Unit"
            />
            <TextFieldForm
              label="Giá"
              textFieldStyle={styles.textFieldInset}
              style={styles.fullWidth}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Price"
              type="money"
              returnKeyType="done"
              onBlur={async (value: any) => {
                if (!value) return;
                const newPrice = parseInt(String(value).replaceAll(',', ''));
                if (!isNaN(newPrice)) {
                  methods.setValue('Price', Ultis.money(newPrice));
                }
              }}
            />
            <TextFieldForm
              control={methods.control}
              name="Description"
              label="Mô tả"
              errors={methods.formState.errors}
              placeholder={'Mô tả khác'}
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={styles.textFieldInset}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              register={methods.register}
            />
            {formId ? (
              <View style={styles.twoFieldGap}>
                <TextFieldForm
                  label="Vat"
                  textFieldStyle={styles.textFieldInset}
                  style={styles.fullWidth}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Vat"
                />
                <TextFieldForm
                  label="Discount"
                  textFieldStyle={styles.textFieldInset}
                  style={styles.fullWidth}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Discount"
                />
              </View>
            ) : null}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles.footer}>
        <AppButton
          title={id ? 'Sửa' : 'Thêm mới'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={styles.submitBtn}
          onPress={() => {
            methods.handleSubmit(_onSubmit, _onError)();
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </View>
    </SafeAreaView>
  );
});

function ListTileItem({item, onRemove}: {item: any; onRemove: () => void}) {
  return (
    <View style={styles.tileWrap}>
      <SkeletonImage
        source={{uri: ConfigAPI.imgUrlId + item.Id}}
        style={styles.tileImg}
      />
      <View style={styles.tileInfo}>
        <Text style={[TypoSkin.heading7, styles.tileTitle]} numberOfLines={1}>
          {item?.Name ?? 'Ảnh'}
        </Text>
        <Text
          style={
            TypoSkin.body3
          }>{`${Math.round(item.Size / (1024 * 1024))}MB`}</Text>
      </View>
      <TouchableOpacity onPress={onRemove} style={styles.removeBtnHitbox}>
        <FontAwesomeIcon
          icon={faMinusCircle}
          size={20}
          color="#D72525FF"
          style={styles.removeBtn}
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  headerAction: {flexDirection: 'row', padding: 12, alignItems: 'center'},
  formWrap: {height: '100%', width: '100%', paddingHorizontal: 16},
  formContainer: {gap: 18, paddingBottom: 156},
  textFieldInset: {padding: 16},
  fullWidth: {width: '100%'},
  imgWrap: {gap: 8},
  addImgBox: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 0.4,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 8,
  },
  addImgIcon: {width: 35, height: 35},
  addImgText: {color: ColorThemes.light.neutral_text_subtitle_color},
  twoFieldGap: {flex: 1, gap: 18},
  footer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  submitBtn: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
  tileWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
    padding: 8,
    borderRadius: 8,
  },
  tileImg: {width: 65, height: 65},
  tileInfo: {flex: 1, gap: 2},
  tileTitle: {color: ColorThemes.light.neutral_text_title_color},
  removeBtnHitbox: {padding: 4},
  removeBtn: {backgroundColor: '#fff', borderRadius: 20},
});
