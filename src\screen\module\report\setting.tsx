import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Circle, G } from 'react-native-svg';
export default function ReportAllToilet({ data }: any) {
  const total = data?.total ?? 0;
  // const data = [
  //   { name: '<PERSON><PERSON> tích xanh', value: 34, color: '#4285F4' },
  //   { name: 'Đang đánh giá', value: 15, color: '#76A9F9' },
  //   { name: 'Chưa đăng ký', value: 20, color: '#E0E0E0' },
  // ];

  const radius = 50;
  const strokeWidth = 30;
  const circumference = 2 * Math.PI * radius;

  let cumulativeValue = 0;

  const renderSegments = () => {
    return data?.data?.map((item: any, index: number) => {
      const segmentAngle = (item.value / total) * circumference;
      const dashOffset = (cumulativeValue / total) * circumference;
      cumulativeValue += item.value;
      return (
        <Circle
          key={index}
          cx="50%"
          cy="50%"
          r={radius}
          stroke={item.color}
          strokeWidth={strokeWidth}
          strokeDasharray={`${segmentAngle} ${circumference - segmentAngle}`}
          strokeDashoffset={-dashOffset}
          fill="transparent"
        // strokeLinecap="round"
        />
      );
    })
  }



  return (

    <View style={styles.container}>
      <Text style={styles.title}>Tổng số nhà vệ sinh</Text>
      <View style={styles.chartContainer}>
        <Svg width="150" height="150" viewBox="0 0 150 150">
          <G transform="rotate(-90 75 75)">{renderSegments()}</G>
        </Svg>
        <View style={styles.centerText}>
          <Text style={styles.totalText}>{total}</Text>
        </View>
      </View>
      <View style={styles.legend}>
        {data && data?.data?.map((item: any, index: number) => (
          <View style={styles.legendItem} key={index}>
            <View style={[styles.legendColor, { backgroundColor: item.color }]} />
            <Text style={styles.legendText}>
              {item.name}: {item.value}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  chartContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  centerText: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  totalText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
  },
  legend: {
    marginTop: 20,
    width: '80%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  legendColor: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    marginRight: 10,
  },
  legendText: {
    fontSize: 14,
    color: '#7F7F7F',
  },
});

