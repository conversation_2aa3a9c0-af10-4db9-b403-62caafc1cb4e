import {useNavigation} from '@react-navigation/native';
import {Text, View} from 'react-native';
import ScreenHeader from './header';
import React from 'react';

import {Winicon} from '../../component/export-component';
import AppButton from '../../component/button';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';
import ListTile from '../../component/list-tile/list-tile';

interface Props {
  title?: string;
  subTitle?: string;
  children?: React.ReactNode;
  action?: React.ReactNode;
  iconAction?: string;
  iconActionPress?: () => void;
  prefix?: React.ReactNode;
}

export default function TitleWithImage(props: Props) {
  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <ScreenHeader
        style={{paddingLeft: 16}}
        prefix={
          <View style={{flex: 1}}>
            <ListTile
              style={{padding: 0, alignItems: 'center'}}
              isClickLeading
              leading={
                props.prefix ? (
                  props.prefix
                ) : (
                  <View style={{width: 48, height: 48, borderRadius: 100}}>
                    <View
                      style={{
                        width: '100%',
                        height: '100%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 100,
                      }}>
                      <Winicon src="fill/users/profile" size={24} />
                    </View>
                    {/* <SkeletonImage
                    source={{uri: ConfigAPI.imgUrlId + customer?.data?.Img}}
                    style={{width: 45, height: 45, borderRadius: 100}}
                  /> */}
                    <View
                      style={{
                        position: 'absolute',
                        padding: 5,
                        borderRadius: 24,
                        backgroundColor: '#fff',
                        right: -2,
                        bottom: -2,
                      }}>
                      <Winicon
                        src="fill/user interface/star"
                        size={12}
                        color={'#000'}
                      />
                    </View>
                  </View>
                )
              }
              title={props.title ?? 'title'}
              titleStyle={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.neutral_text_title_color,
              }}
              subtitle={props.subTitle ?? 'subTitle'}
              subTitleStyle={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}
            />
          </View>
        }
        action={
          <View style={{flexDirection: 'row', gap: 8, paddingRight: 16}}>
            {props.action ? props.action : null}
            <AppButton
              backgroundColor={'transparent'}
              borderColor="transparent"
              onPress={props.iconActionPress}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 32,
                width: 32,
                backgroundColor:
                  ColorThemes.light.neutral_main_background_color,
              }}
              title={
                <Winicon
                  src={props.iconAction ?? 'fill/user interface/bell'}
                  size={18}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              }
            />
          </View>
        }
      />
      {props.children ? props.children : null}
    </SafeAreaView>
  );
}
