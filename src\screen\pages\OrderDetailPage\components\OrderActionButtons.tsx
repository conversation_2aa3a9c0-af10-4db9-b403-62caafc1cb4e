import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {AppSvg, Winicon} from 'wini-mobile-components';
import {TypoSkin} from '../../../../assets/skin/typography';
import {StatusOrder} from '../../../../config/Contanst';
import {OrderActionButtonsProps} from '../types';
import iconSvg from '../../../../svgs/iconSvg';
import {ColorThemes} from '../../../../assets/skin/colors';

const OrderActionButtons: React.FC<OrderActionButtonsProps> = ({
  currentStatus,
  isCustomer,
  onChatWithShop,
  onContactShop,
  onConfirmOrder,
  onRejectOrder,
  onUpdateOrderStatus,
}) => {
  if (!isCustomer && currentStatus === StatusOrder.proccess) {
    // Status update view - Cập nhật trạng thái + <PERSON>t với người mua
    return (
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onUpdateOrderStatus}>
          <AppSvg
            SvgSrc={iconSvg.pinmap}
            size={25}
            color={ColorThemes.light.primary_main_color}
          />
          <Text style={styles.actionButtonText}>
            Cập nhật trạng thái đơn hàng
          </Text>
        </TouchableOpacity>

        <View style={styles.separator} />

        <TouchableOpacity style={styles.actionButton} onPress={onChatWithShop}>
          <AppSvg
            SvgSrc={iconSvg.weChat}
            size={25}
            color={ColorThemes.light.primary_main_color}
          />
          <Text style={styles.actionButtonText}>Chat với người mua</Text>
        </TouchableOpacity>

        <View style={styles.bottomSeparator} />
      </View>
    );
  }

  // if (!isCustomer && currentStatus === StatusOrder.new) {
  //   // Seller view - Chat với người mua + action buttons
  //   return (
  //     <View style={styles.actionButtonsContainer}>
  //       <TouchableOpacity style={styles.actionButton} onPress={onChatWithShop}>
  //         <AppSvg
  //           SvgSrc={iconSvg.weChat}
  //           size={25}
  //           color={ColorThemes.light.primary_main_color}
  //         />
  //         <Text style={styles.actionButtonText}>Chat với người mua</Text>
  //       </TouchableOpacity>

  //       <View style={styles.separator} />

  //       <View style={styles.actionButtonsRow}>
  //         <TouchableOpacity
  //           style={styles.confirmButton}
  //           onPress={onConfirmOrder}>
  //           <Text style={styles.confirmButtonText}>Xác nhận đơn hàng</Text>
  //         </TouchableOpacity>

  //         <TouchableOpacity style={styles.rejectButton} onPress={onRejectOrder}>
  //           <Text style={styles.rejectButtonText}>Từ chối đơn hàng</Text>
  //         </TouchableOpacity>
  //       </View>

  //       <View style={styles.bottomSeparator} />
  //     </View>
  //   );
  // }

  // if (isCustomer) {
  //   return (
  //     <View style={styles.actionButtonsContainer}>
  //       <TouchableOpacity style={styles.actionButton} onPress={onChatWithShop}>
  //         <AppSvg
  //           SvgSrc={iconSvg.weChat}
  //           size={25}
  //           color={ColorThemes.light.primary_main_color}
  //         />
  //         <Text style={styles.actionButtonText}>Chat với cửa hàng</Text>
  //       </TouchableOpacity>

  //       <View style={styles.separator} />

  //       <TouchableOpacity style={styles.actionButton} onPress={onContactShop}>
  //         <AppSvg
  //           SvgSrc={iconSvg.phoneCall}
  //           size={25}
  //           color={ColorThemes.light.primary_main_color}
  //         />
  //         <Text style={styles.actionButtonText}>Liên hệ cửa hàng</Text>
  //       </TouchableOpacity>

  //       <View style={styles.bottomSeparator} />
  //     </View>
  //   );
  // }

  return null;
};

const styles = StyleSheet.create({
  actionButtonsContainer: {
    backgroundColor: '#FFFFFF',
    marginLeft: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
  },
  actionButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.primary_main_color,
    marginLeft: 10,
    fontWeight: 'bold',
  },
  separator: {
    height: 1,
    backgroundColor: ColorThemes.light.primary_border_color,
    marginLeft: 12,
    marginRight: 12,
  },
  bottomSeparator: {
    height: 8,
    backgroundColor: '#F5F5F5',
    marginTop: 8,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#FF9500',
    borderRadius: 20,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    ...TypoSkin.body2,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#FF3B30',
    borderRadius: 20,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rejectButtonText: {
    ...TypoSkin.body2,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default OrderActionButtons;
