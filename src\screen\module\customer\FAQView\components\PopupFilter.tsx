import React, {forwardRef, useState, useEffect} from 'react';
import {
  SafeAreaView,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Dimensions,
  StyleSheet,
} from 'react-native';
import ScreenHeader from '../../../../layout/header';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {FCheckbox, Winicon} from '../../../../../component/export-component';
import {closePopup} from '../../../../../component/popup/popup';
import AppButton from '../../../../../component/button';
import WScreenFooter from '../../../../layout/footer';

interface PopupFilterProps {
  filterMethods: any;
  onApply: (values: Array<any>) => void;
  selectedAttributes: any[];
  listStatus: any[];
}

const PopupFilter = forwardRef<any, PopupFilterProps>(function PopupFilter(
  {onApply, selectedAttributes, listStatus, filterMethods},
  ref,
) {
  const [selected, setSelected] = useState<Array<any>>([]);

  useEffect(() => {
    if (selectedAttributes.length) setSelected(selectedAttributes);
  }, [selectedAttributes.length]);

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader
        style={styles.header}
        title={`Loại câu hỏi`}
        prefix={<View style={styles.prefixContainer} />}
        action={
          <View style={styles.actionContainer}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref as any)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <View style={styles.content}>
        <Text style={styles.label}>Phân loại câu hỏi: </Text>
        <FlatList
          data={listStatus}
          renderItem={({item, index}) => (
            <TouchableOpacity
              key={item.Id}
              onPress={() => {
                if (!selected.includes(item.Id))
                  setSelected(ids => [...ids, item.Id]);
                else
                  setSelected(ids => ids.filter((id: any) => id !== item.Id));
              }}
              style={styles.itemContainer}>
              <FCheckbox
                value={selected.includes(item.Id)}
                onChange={v => {
                  if (v) setSelected(ids => [...ids, item.Id]);
                  else
                    setSelected(ids => ids.filter((id: any) => id !== item.Id));
                }}
              />
              <Text style={styles.itemText}>
                {`${item?.Name ?? '-'} (${item?._count ?? 0})`}
              </Text>
            </TouchableOpacity>
          )}
          numColumns={2}
          style={styles.flatListContainer}
          contentContainerStyle={styles.flatListContent}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <WScreenFooter style={styles.footerContainer}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={styles.resetButton}
          onPress={() => {
            filterMethods.setValue('AttributeId', []);
            setSelected([]);
          }}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={styles.applyButton}
          onPress={() => {
            closePopup(ref as any);
            onApply(selected);
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  container: {
    width: '100%' as const,
    height: Dimensions.get('window').height - 146,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    borderBottomColor: ColorThemes.light.neutral_main_background_color,
    borderBottomWidth: 0.5,
    shadowColor: 'rgba(0, 0, 0, 0.03)',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowRadius: 20,
    elevation: 20,
    shadowOpacity: 1,
  },
  prefixContainer: {
    width: 50,
  },
  actionContainer: {
    flexDirection: 'row' as const,
    padding: 12,
    alignItems: 'center' as const,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  label: {
    ...TypoSkin.label3,
    marginBottom: 8,
  },
  flatListContainer: {
    width: '100%' as const,
  },
  flatListContent: {
    gap: 16,
  },
  itemContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    width: '50%' as const,
  },
  itemText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  footerContainer: {
    flexDirection: 'row' as const,
    gap: 8,
    paddingHorizontal: 16,
  },
  resetButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
  applyButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});

export default PopupFilter;
