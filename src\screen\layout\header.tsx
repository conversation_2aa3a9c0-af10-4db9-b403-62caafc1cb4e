import { faAngleLeft } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { GestureResponderEvent, StyleSheet, TextStyle, TouchableOpacity, View } from "react-native"
import { Text } from "react-native-paper"
import { TypoSkin } from "../../assets/skin/typography"
import { ColorThemes } from "../../assets/skin/colors"
import { Winicon } from "../../component/export-component"

export default function ScreenHeader({ onBack, title, action, height, bottom, children, backIcon, style = {}, prefix }: { onBack?: ((event: GestureResponderEvent) => void) & (() => void), title?: React.ReactNode | string, action?: React.ReactNode, bottom?: React.ReactNode, height?: number, children?: React.ReactNode, backIcon?: React.ReactNode, prefix?: React.ReactNode, style?: TextStyle }) {
    return <View style={[styles.container, style]}>
        {children ?? <View style={{ ...styles.header, height: height ?? style.height ?? 56 }}>
            {prefix}
            {onBack ? <TouchableOpacity style={{}} onPress={onBack}>
                {backIcon ?? <View style={{ paddingLeft: 16, paddingVertical: 8, paddingRight: 16, borderRadius: 8 }}><Winicon src="outline/arrows/left-arrow" size={24} /></View>}
            </TouchableOpacity> : undefined}
            {typeof title === 'string' ? <Text numberOfLines={2} style={[TypoSkin.title3, styles.title]}>{title ?? '-'}</Text> : title ?? <View />}
            {action}
        </View>}
        {bottom}
    </View>
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
        backgroundColor: ColorThemes.light.white,
        // shadowColor: "rgba(0, 0, 0, 0.03)",
        // shadowOffset: {
        //     width: 0,
        //     height: 4
        // },
        // shadowRadius: 20,
        // elevation: 20,
        // shadowOpacity: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        position: 'relative',
        height: "auto"
    },
    title: {
        position: 'absolute',
        left: '12%',
        right: '12%',
        textAlign: 'center',
        color: ColorThemes.light.neutral_text_title_color
        // top: 12
    }
})

