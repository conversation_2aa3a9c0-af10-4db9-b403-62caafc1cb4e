import {groupRole} from '../../../../config/Contanst';
import {CustomerDA} from '../../../../redux/reducers/user/da';
import {store} from '../../../../redux/store/store';
import {getDataToAsyncStorage} from '../../../../utils/AsyncStorage';
import {randomGID} from '../../../../utils/Utils';
import {DataController} from '../../../base-controller';

export class GroupDA {
  private groupController: DataController;
  private groupMemberController: DataController;
  private postController: DataController;

  constructor() {
    this.groupController = new DataController('Group');
    this.groupMemberController = new DataController('Role');
    this.postController = new DataController('Posts');
  }

  async getList(page?: number, size?: number, query?: string) {
    const response = await this.groupController.getListSimple({
      page: page ?? 1,
      size: size ?? 10,
      query: query ?? '*',
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getById(id: string) {
    const response = await this.groupController.getById(id);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async add(group: any) {
    const cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const newGroup = {
        ...group,
        CustomerId: cusId,
        DateCreated: new Date().getTime(),
      };

      const response = await this.groupController.add([newGroup]);
      if (response?.code === 200) {
        // Tự động thêm người tạo vào nhóm như admin
        await this.groupMemberController.add([
          {
            Id: randomGID(),
            GroupId: newGroup.Id,
            CustomerId: cusId,
            Type: groupRole.admin,
            DateCreated: new Date().getTime(),
            Status: 1,
          },
        ]);
        return newGroup;
      }
    }
    return null;
  }

  async edit(group: any) {
    if (!group.Id) {
      return null;
    }
    const cusId = store.getState().customer.data?.Id;

    if (cusId) {
      const currentGroup = await this.getById(group.Id);
      if (currentGroup?.data?.CustomerId === cusId) {
        const response = await this.groupController.edit([group]);

        if (response?.code === 200) {
          return response;
        }
      }
    }
    return null;
  }

  async delete(id: string) {
    const cusId = store.getState().customer.data?.Id;

    if (cusId) {
      const currentGroup = await this.getById(id);
      if (currentGroup?.data?.CustomerId === cusId) {
        const response = await this.groupController.delete([id]);
        if (response?.code === 200) {
          // Xóa tất cả thành viên của nhóm
          const members = await this.groupMemberController.getListSimple({
            query: `@GroupId:{${id}}`,
          });
          if (members?.code === 200 && members.data?.length > 0) {
            await this.groupMemberController.delete(
              members.data.map((m: any) => m.Id),
            );
          }
          return true;
        }
      }
    }
    return false;
  }

  async getMyGroups(page?: number, size?: number) {
    const cusId = store.getState().customer.data?.Id;

    if (cusId) {
      const response = await this.groupController.getListSimple({
        page: page ?? 1,
        size: size ?? 10,
        query: `@CustomerId:{${cusId}}`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });
      if (response?.code === 200) {
        return response;
      }
    }
    return null;
  }
  //lấy danh sách group đã joined
  async getJoinedGroups(page?: number, size?: number) {
    const cusId = store.getState().customer.data?.Id;

    if (cusId) {
      // Lấy danh sách GroupId mà user đã tham gia
      const memberResponse = await this.groupMemberController.getListSimple({
        query: `@CustomerId:{${cusId}}`,
      });
      if (memberResponse?.code === 200 && memberResponse.data?.length > 0) {
        const groupIds = memberResponse.data.map((m: any) => m.GroupId);
        const response = await this.groupController.getListSimple({
          page: page ?? 1,
          size: size ?? 10,
          query: `@Id:{${groupIds.join(' | ')}}`,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });
        if (response?.code === 200) {
          return response;
        }
      }
    }
    return null;
  }

  async getMemberRole(
    groupId: string,
    memberId: string,
  ): Promise<number | null> {
    const member = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${memberId}}`,
    });
    if (member?.code === 200 && member.data?.length > 0) {
      return member.data[0].Type;
    }
    return null;
  }

  async updateMemberRole(groupId: string, memberId: string, newRole: number) {
    const cusId = store.getState().customer.data?.Id;

    if (!cusId) {
      return null;
    }

    // Kiểm tra quyền của người thực hiện thao tác (phải là admin)
    const currentUserRole = await this.getMemberRole(groupId, cusId);
    if (currentUserRole !== groupRole.admin) {
      return null;
    }

    // Kiểm tra member cần update có tồn tại không
    const memberResponse = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${memberId}}`,
    });

    if (memberResponse?.code === 200 && memberResponse.data?.length > 0) {
      const memberData = memberResponse.data[0];

      // Không cho phép thay đổi role của chính mình
      if (memberId === cusId) {
        return null;
      }

      // Cập nhật role mới
      const updateData = {
        ...memberData,
        Type: newRole,
      };

      const response = await this.groupMemberController.edit([updateData]);
      if (response?.code === 200) {
        if (newRole === groupRole.admin) {
          var group = await this.groupController.getById(groupId);
          await this.groupController.edit([
            {
              ...group.data,
              CustomerId: memberId,
            },
          ]);
          const memberAdmin = await this.groupMemberController.getListSimple({
            query: `@GroupId:{${groupId}} @CustomerId:{${cusId}}`,
          });
          await this.groupMemberController.edit([
            {
              ...memberAdmin.data[0],
              Type: groupRole.subadmin,
            },
          ]);
        }
        return updateData;
      }
    }
    return null;
  }

  async getGroupMembers(
    groupId: string,
    page?: number,
    size?: number,
    role?: number,
  ) {
    let query = `@GroupId:{${groupId}}`;
    if (role) {
      query += `| @Type:{${role}}`;
    }
    const response = await this.groupMemberController.getListSimple({
      page: page ?? 1,
      size: size ?? 10,
      query: query,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (response?.code === 200) {
      const customerDA = new CustomerDA();
      const customerIds = response.data.map((member: any) => member.CustomerId);
      const customersResult = await customerDA.getCustomersByIds(customerIds);
      if (customersResult?.code === 200) {
        const customers = customersResult.data.map((item: any) => {
          return {
            ...item,
            DateCreated: response.data.find(
              (m: any) => m.CustomerId === item.Id,
            )?.DateCreated,
            Role: response.data.find((m: any) => m.CustomerId === item.Id)
              ?.Type,
          };
        });
        return customers;
      }
    }
    return null;
  }
  async getRoles(groupId: string, customerId: string) {
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${customerId}}`,
    });
    if (response?.code === 200 && response.data?.length > 0) {
      return response.data[0].Type;
    }
    return null;
  }
  async deleteRoles(groupId: string, customerId: string) {
    const cusId = store.getState().customer.data?.Id;

    if (!cusId) {
      return false;
    }

    // Kiểm tra xem user có phải là thành viên của group không
    const memberRole = await this.getMemberRole(groupId, cusId);
    if (!memberRole) {
      return false;
    }
    // Kiểm tra xem user có quyền xóa thành viên không (admin hoặc subadmin)
    if (memberRole !== groupRole.admin && memberRole !== groupRole.subadmin) {
      return false;
    }
    // Xóa thành viên
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${customerId}}`,
    });
    if (response?.code === 200 && response.data?.length > 0) {
      const deleteResult = await this.groupMemberController.delete([
        response.data[0].Id,
      ]);

      if (deleteResult.code === 200) {
        return true;
      }
    }
    return false;
  }
  async addPost(post: any) {
    const cusId = store.getState().customer.data?.Id;

    if (!cusId) {
      return null;
    }

    // Kiểm tra xem user có phải là thành viên của group không
    const memberRole = await this.getMemberRole(post.GroupId!, cusId);
    if (!memberRole) {
      return null;
    }

    const newPost = {
      ...post,
      Id: randomGID(),
      CustomerId: cusId,
      DateCreated: new Date().getTime(),
    };

    const response = await this.postController.add([newPost]);
    if (response?.code === 200) {
      return newPost;
    }
    return null;
  }

  async editPost(post: any) {
    if (!post.Id) {
      return null;
    }

    const cusId = store.getState().customer.data?.Id;

    if (!cusId) {
      return null;
    }

    // Kiểm tra xem user có phải là người tạo post không
    const currentPost = await this.postController.getById(post.Id);
    if (currentPost?.data?.CustomerId !== cusId) {
      return null;
    }
    const response = await this.postController.edit([post]);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async deletePost(postId: string) {
    const cusId = store.getState().customer.data?.Id;

    if (!cusId) {
      return false;
    }

    // Lấy thông tin post
    const post = await this.postController.getById(postId);
    if (!post?.data) {
      return false;
    }

    // Kiểm tra quyền xóa (người tạo post hoặc admin của group)
    const isCreator = post.data.CustomerId === cusId;
    const memberRole = await this.getMemberRole(post.data.GroupId, cusId);
    const canDelete =
      isCreator ||
      memberRole === groupRole.admin ||
      memberRole === groupRole.subadmin;

    if (canDelete) {
      const response = await this.postController.delete([postId]);
      if (response?.code === 200) {
        return true;
      }
    }
    return false;
  }

  async isFollowingGroup(groupId: string): Promise<boolean> {
    const cusId = store.getState().customer.data?.Id;

    if (!cusId) return false;

    const memberResult = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${cusId}}`,
      size: 1,
      returns: ['Id'],
    });

    return memberResult?.code === 200 && memberResult.data?.length > 0;
  }
}
