import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import ListTile from '../../../../../../../component/list-tile/list-tile';
import {SkeletonImage} from '../../../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../../../config/configApi';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import {Ultis} from '../../../../../../../utils/Utils';
import {Winicon} from '../../../../../../../component/export-component';
import {DeviceBioStatus} from '../../../../../service/components/da';

type DeviceItem = any;

interface Props {
  item: DeviceItem;
  files: Array<any>;
  products: Array<any>;
  userId: string | number | undefined;
  toiletCustomerId?: string | number | undefined;
  disabled?: boolean;
  formId?: any;
  onEdit: (item: DeviceItem) => void;
  onDelete: (item: DeviceItem) => void;
}

export default function DeviceListItem(props: Props) {
  const {
    item,
    files,
    products,
    userId,
    toiletCustomerId,
    disabled,
    formId,
    onEdit,
    onDelete,
  } = props;

  const discount = (item.Price * item.Quantity * (item.Discount ?? 0)) / 100;
  let deviceTotalPrice = item.Price * item.Quantity - discount;
  const vat = deviceTotalPrice * ((item.Vat ?? 0) / 100);

  let product = undefined as any;
  if (item.ProductId) {
    product = products.find(p => p?.Id === item?.ProductId);
  }

  const fileInfos = files
    .filter(f => (product ?? item).Img?.includes(f.Id))
    .filter((f, i, arr) => arr.findIndex(ff => ff.Id === f.Id) === i);

  return (
    <ListTile
      key={item.Id}
      leading={
        fileInfos.length ? (
          <SkeletonImage
            source={{uri: ConfigAPI.imgUrlId + fileInfos[0].Id}}
            style={styles.leadingImage}
          />
        ) : (
          <View />
        )
      }
      isClickLeading={false}
      style={styles.itemContainer}
      listtileStyle={styles.itemListTile}
      title={`${item?.Name ?? '-'}`}
      bottom={
        <View style={styles.itemBottomContainer}>
          <Text
            style={
              styles.subtitleText
            }>{`Mã thiết bị: ${item?.Code ?? '-'}`}</Text>
          <Text style={styles.subtitleText}>{`Trạng thái: ${
            item.Status === DeviceBioStatus.active
              ? 'Đang hoạt động'
              : 'Đang số hóa'
          }`}</Text>
          <Text style={styles.subtitleText}>{`Ngày tạo: ${
            item.DateCreated
              ? Ultis.datetoString(new Date(item.DateCreated))
              : ''
          }`}</Text>
          <Text style={styles.subtitleText}>{`Đơn vị: ${
            item.Unit ?? product?.Unit ?? '-'
          }`}</Text>
          {!formId ? (
            <View style={styles.priceContainer}>
              <Text
                style={
                  styles.subtitleText
                }>{`Giá tiền (VNĐ): ${Ultis.money(item.Price) ?? '-'}`}</Text>
              <Text style={styles.subtitleText}>{`Giảm giá (VNĐ): ${
                Ultis.money(discount) ?? '-'
              } - Thuế VAT (VNĐ): ${Ultis.money(vat) ?? '-'}`}</Text>
              <Text style={styles.totalPriceText}>{`Thành tiền: ${Ultis.money(
                deviceTotalPrice + vat,
              )}`}</Text>
            </View>
          ) : null}
          {!disabled ? (
            <View style={styles.actions}>
              <Winicon
                src="outline/user interface/s-edit"
                size={16}
                onClick={() => onEdit(item)}
              />
              {toiletCustomerId === userId ||
              item.Status !== DeviceBioStatus.active ? (
                <Winicon
                  src="outline/user interface/trash-can"
                  size={16}
                  onClick={() => onDelete(item)}
                />
              ) : null}
            </View>
          ) : null}
        </View>
      }
      titleStyle={styles.itemTitle}
      subtitle={`${Ultis.money(item.Price)} VNĐ`}
    />
  );
}

const styles = StyleSheet.create({
  leadingImage: {
    width: 55,
    height: 60,
    borderRadius: 4,
    overflow: 'hidden',
    paddingTop: 6,
  },
  itemContainer: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
    borderRadius: 8,
  },
  itemListTile: {
    gap: 16,
    alignItems: 'flex-start',
  },
  itemBottomContainer: {
    flex: 1,
    alignItems: 'flex-start',
    width: '100%',
    paddingTop: 4,
    gap: 4,
  },
  subtitleText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  priceContainer: {
    flex: 1,
    alignItems: 'flex-start',
    width: '100%',
    gap: 4,
  },
  totalPriceText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  actions: {
    alignSelf: 'flex-end',
    flex: 1,
    gap: 16,
    paddingTop: 16,
    flexDirection: 'row',
  },
  itemTitle: {
    ...TypoSkin.title3,
    paddingBottom: 8,
  },
});
