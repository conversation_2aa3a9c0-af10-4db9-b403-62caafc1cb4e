import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  SafeAreaView,
  Pressable,
} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import {Ultis} from 'utils/Utils';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from 'router/router';

const ManagerRequestServiceSection = ({
  serviceRequests,
  allServiceRequests,
}: {
  serviceRequests: any[];
  allServiceRequests: any[];
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const navigation = useNavigation<any>();
  const handleViewAll = () => {
    // Mở Modal để hiển thị tất cả service requests
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const renderServiceRequestCard = (request: any) => (
    <TouchableOpacity
      key={request.Id}
      style={styles.cardContainer}
      onPress={() => {
        closeModal();
        navigation.navigate(RootScreen.DetailWorkView, {
          Id: request.Id || request.ToiletServicesId,
          Status: request.Status,
          Name: request.Name || request?.Toilet?.Name || '-',
        });
      }}>
      <View style={styles.cardHeader}>
        <Text style={[TypoSkin.semibold3, styles.codeText]}>
          {request.Name}-{request?.Toilet?.Name}
        </Text>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.timeContainer}>
          <Text style={[TypoSkin.regular2, styles.timeText]}>
            {Ultis.datetoString(new Date(request.DateCreated))}
          </Text>
          <Text style={[styles.statusText]}>
            {request.Status == 0 && 'Không tiếp nhận'}
            {request.Status == 1 && 'Chờ tiếp nhận'}
            {request.Status == 2 && 'Khảo sát'}
            {request.Status == 3 && 'Tư vấn'}
            {request.Status == 4 && 'Chốt báo giá'}
            {request.Status == 5 && 'Hợp đồng'}
            {request.Status == 6 && 'Ký hợp đồng'}
            {request.Status == 7 && 'Thiết kế'}
            {request.Status == 8 && 'Chốt thiết kế'}
            {request.Status == 9 && 'Thi công'}
            {request.Status == 10 && 'Hoàn công'}
            {request.Status == 11 && 'Thanh lý HĐ'}
            {request.Status == 12 && 'Hoàn thành'}
          </Text>
        </View>

        <View style={styles.serviceContainer}>
          <Text style={[TypoSkin.regular2, styles.serviceLabel]}>Dịch vụ:</Text>
          <View style={styles.chipContainer}>
            <View style={styles.chip}>
              <Text style={[TypoSkin.medium1, styles.chipText]}>
                {request?.CateServices?.Name}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
  return (
    <View style={{}}>
      {/* Header Section */}
      <View style={styles.headerContainer}>
        <Text style={[TypoSkin.semibold4, styles.headerTitle]}>
          Quản lý yêu cầu dịch vụ
        </Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAllText}>Xem thêm</Text>
        </TouchableOpacity>
      </View>

      {/* Service Requests List */}
      <Pressable style={styles.listContainer}>
        {serviceRequests && serviceRequests?.length > 0 ? (
          serviceRequests.map(renderServiceRequestCard)
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Không có dữ liệu</Text>
          </View>
        )}
      </Pressable>

      {/* Modal Bottom Sheet */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={closeModal}>
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Tất cả yêu cầu dịch vụ</Text>
            <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>Đóng</Text>
            </TouchableOpacity>
          </View>

          {/* Modal Content */}
          {allServiceRequests && allServiceRequests.length > 0 ? (
            <FlatList
              data={allServiceRequests}
              renderItem={({item}) => renderServiceRequestCard(item)}
              keyExtractor={(item, index) =>
                item.Id?.toString() || index.toString()
              }
              contentContainerStyle={styles.modalListContainer}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Không có dữ liệu</Text>
            </View>
          )}
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  viewAllText: {
    ...TypoSkin.title5,
    fontWeight: 500,
    color: ColorThemes.light.primary_main_color,
  },
  listContainer: {
    gap: 12,
  },
  cardContainer: {
    backgroundColor: ColorThemes.light.secondary2_border_color,
    borderRadius: 12,
    padding: 10,
    paddingTop: 8,
  },
  cardHeader: {
    marginBottom: 8,
  },
  codeText: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  cardContent: {
    gap: 12,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    color: ColorThemes.light.neutral_text_subtitle_reverse_color,
  },
  statusText: {
    ...TypoSkin.title5,
    marginLeft: 8,
    fontWeight: 'bold',
    color: ColorThemes.light.primary_main_color,
  },
  serviceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  serviceLabel: {
    color: ColorThemes.light.neutral_text_subtitle_reverse_color,
  },
  chipContainer: {
    flexDirection: 'row',
    gap: 8,
    flex: 1,
  },
  chip: {
    backgroundColor: ColorThemes.light.secondary2_main_color,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  chipText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.secondary2_border_color,
  },
  modalTitle: {
    ...TypoSkin.semibold4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 8,
  },
  closeButtonText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.white,
    fontWeight: '500',
  },
  modalListContainer: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
});

export default ManagerRequestServiceSection;
