import ConfigAPI from '../../../../config/configApi';
import {BaseDA} from '../../../baseDA';
import productDA from '../productDA';

interface LoadNameFromIdParams {
  type: string;
  id: string;
  setSelectType: (value: string) => void;
  setSelectCate: (value: string) => void;
  setSelectCateattribute: (value: string) => void;
  setSelectAttribute: (value: string) => void;
  setselectSource: (value: string) => void;
  setSelectColor: (value: string) => void;
  setSelectBrand: (value: string) => void;
  setSelectConsume: (value: string) => void;
}

export const loadNameFromId = async ({
  type,
  id,
  setSelectType,
  setSelectCate,
  setSelectCateattribute,
  setSelectAttribute,
  setselectSource,
  setSelectColor,
  setSelectBrand,
  setSelectConsume,
}: LoadNameFromIdParams) => {
  try {
    console.log(`Loading ${type} name for ID:`, id);

    switch (type) {
      case 'type':
        // Data cứng cho Type
        const typeData = [
          {Id: '1', Name: 'Chế phẩm sinh học'},
          {Id: '2', Name: 'Thiết bị'},
        ];
        const foundType = typeData.find(
          item => item.Id === id || item.Id === String(id),
        );
        if (foundType) {
          setSelectType(foundType.Name);
        } else {
          setSelectType('');
        }
        break;

      case 'category':
        const categoryResponse = await productDA.GetAllCate();
        if (categoryResponse && categoryResponse.code === 200) {
          // Kiểm tra xem id có phải là chuỗi nhiều ID cách nhau bằng dấu phẩy không
          if (id.includes(',')) {
            // Trường hợp nhiều ID
            const idArray = id
              .split(',')
              .map(singleId => singleId.trim())
              .filter(singleId => singleId);
            const foundNames: string[] = [];

            idArray.forEach(singleId => {
              const foundCategory = categoryResponse.data.find(
                (item: any) =>
                  item.Id === singleId || item.Id === String(singleId),
              );
              if (foundCategory && foundCategory.Name) {
                foundNames.push(foundCategory.Name);
              }
            });

            // Set tên hiển thị là các tên cách nhau bằng dấu phẩy
            setSelectCate(foundNames.length > 0 ? foundNames.join(', ') : '');
          } else {
            // Trường hợp ID đơn lẻ (logic cũ)
            const foundCategory = categoryResponse.data.find(
              (item: any) => item.Id === id || item.Id === String(id),
            );
            if (foundCategory) {
              setSelectCate(foundCategory.Name);
            } else {
              setSelectCate('');
            }
          }
        } else {
          setSelectCate('');
        }
        break;

      case 'cateAttribute':
        const cateAttributeResponse = await productDA.getAllCateAttribute();
        if (
          cateAttributeResponse &&
          cateAttributeResponse.code === 200 &&
          cateAttributeResponse.data
        ) {
          // Kiểm tra xem id có phải là chuỗi nhiều ID cách nhau bằng dấu phẩy không
          if (id.includes(',')) {
            // Trường hợp nhiều ID
            const idArray = id
              .split(',')
              .map(singleId => singleId.trim())
              .filter(singleId => singleId);
            const foundNames: string[] = [];

            idArray.forEach(singleId => {
              const foundCateAttribute = cateAttributeResponse.data.find(
                (item: any) =>
                  item.Id === singleId || item.Id === String(singleId),
              );
              if (foundCateAttribute && foundCateAttribute.Name) {
                foundNames.push(foundCateAttribute.Name);
              }
            });

            // Set tên hiển thị là các tên cách nhau bằng dấu phẩy
            setSelectCateattribute(
              foundNames.length > 0 ? foundNames.join(', ') : '',
            );
          } else {
            // Trường hợp ID đơn lẻ (logic cũ)
            const foundCateAttribute = cateAttributeResponse.data.find(
              (item: any) => item.Id === id || item.Id === String(id),
            );
            if (foundCateAttribute) {
              setSelectCateattribute(foundCateAttribute.Name || '');
            } else {
              setSelectCateattribute('');
            }
          }
        } else {
          setSelectCateattribute('');
        }
        break;

      case 'attribute':
        const attributeResponse = await productDA.getAllAttribute();
        if (
          attributeResponse &&
          attributeResponse.code === 200 &&
          attributeResponse.data
        ) {
          const foundAttribute = attributeResponse.data.find(
            (item: any) => item.Id === id || item.Id === String(id),
          );
          if (foundAttribute) {
            setSelectAttribute(foundAttribute.Name || '');
          } else {
            setSelectAttribute('');
          }
        } else {
          setSelectAttribute('');
        }
        break;

      case 'source':
        // Data cứng cho Source
        const sourceData = [
          {Id: '1', Name: 'Nhập khẩu'},
          {Id: '2', Name: 'Nội địa'},
        ];
        const foundSource = sourceData.find(
          item => item.Id === id || item.Id === String(id),
        );
        if (foundSource) {
          setselectSource(foundSource.Name || '');
        } else {
          setselectSource('');
        }
        break;

      case 'color':
        const colorResponse = await productDA.getAllColor();
        if (colorResponse && colorResponse.code === 200) {
          const foundColor = colorResponse.data.find(
            (item: any) => item.Id === id || item.Id === String(id),
          );
          if (foundColor) {
            setSelectColor(foundColor.Name);
          } else {
            setSelectColor('');
          }
        } else {
          setSelectColor('');
        }
        break;

      case 'brand':
        const brandResponse = await productDA.getAllBrands();
        if (brandResponse && brandResponse.code === 200 && brandResponse.data) {
          const foundBrand = brandResponse.data.find(
            (item: any) => item.Id === id || item.Id === String(id),
          );
          if (foundBrand) {
            setSelectBrand(foundBrand.Name || '');
          } else {
            setSelectBrand('');
          }
        } else {
          setSelectBrand('');
        }
        break;

      case 'consume':
        const consumeResponse = await productDA.getAllConsume();
        if (
          consumeResponse &&
          consumeResponse.code === 200 &&
          consumeResponse.data
        ) {
          const foundConsume = consumeResponse.data.find(
            (item: any) => item.Id === id || item.Id === String(id),
          );
          if (foundConsume) {
            setSelectConsume(foundConsume.Name || '');
          } else {
            setSelectConsume('');
          }
        } else {
          setSelectConsume('');
        }
        break;

      default:
        console.log('Unknown type:', type);
        break;
    }
  } catch (error) {
    console.error(`Error loading ${type} name:`, error);
    // Set empty value based on type
    switch (type) {
      case 'type':
        setSelectType('');
        break;
      case 'category':
        setSelectCate('');
        break;
      case 'cateAttribute':
        setSelectCateattribute('');
        break;
      case 'attribute':
        setSelectAttribute('');
        break;
      case 'source':
        setselectSource('');
        break;
      case 'color':
        setSelectColor('');
        break;
      case 'brand':
        setSelectBrand('');
        break;
      case 'consume':
        setSelectConsume('');
        break;
    }
  }
};

// Helper function để tạo wrapper cho việc gọi loadNameFromId
export const createLoadNameFromIdWrapper = (setters: {
  setSelectType: (value: string) => void;
  setSelectCate: (value: string) => void;
  setSelectCateattribute: (value: string) => void;
  setSelectAttribute: (value: string) => void;
  setselectSource: (value: string) => void;
  setSelectColor: (value: string) => void;
  setSelectBrand: (value: string) => void;
  setSelectConsume: (value: string) => void;
}) => {
  return (type: string, id: string) => {
    return loadNameFromId({
      type,
      id,
      ...setters,
    });
  };
};

// Interface cho populateFormWithEditData
interface PopulateFormParams {
  dataEdit: any;
  setValue: (name: string, value: any) => void;
  setIsFreeShip: (value: boolean) => void;
  loadNameFromId: (type: string, id: string) => void;
  setProductState: (value: any) => void;
  setters: {
    setSelectType: (value: string) => void;
    setSelectCate: (value: string) => void;
    setSelectCateattribute: (value: string) => void;
    setSelectAttribute: (value: string) => void;
    setselectSource: (value: string) => void;
    setSelectColor: (value: string) => void;
    setSelectBrand: (value: string) => void;
    setSelectConsume: (value: string) => void;
  };
}

// Hàm chuyển đổi timestamp thành chuỗi ngày dd/mm/yyyy
const CoverMiliSecondToDate = (miliSecond: number): string => {
  // Kiểm tra nếu không có dữ liệu hoặc timestamp không hợp lệ
  if (!miliSecond || miliSecond === 0 || isNaN(miliSecond)) {
    return '';
  }

  try {
    const date = new Date(miliSecond);

    // Kiểm tra nếu Date object không hợp lệ
    if (isNaN(date.getTime())) {
      return '';
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.warn('Error converting timestamp to date:', error);
    return '';
  }
};

// Hàm format date cho DateSinglePicker để đảm bảo consistency
export const formatDateForDisplay = (timestamp: number): string => {
  if (!timestamp || timestamp === 0) return '';

  try {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';

    // Sử dụng cùng format với CoverMiliSecondToDate
    return CoverMiliSecondToDate(timestamp);
  } catch (error) {
    console.warn('Error formatting date for display:', error);
    return '';
  }
};

// Export CoverMiliSecondToDate để sử dụng ở nơi khác
export {CoverMiliSecondToDate};

// Hàm populate form với edit data
export const populateFormWithEditData = async ({
  dataEdit,
  setValue,
  setIsFreeShip,
  loadNameFromId,
  setProductState,
  setters,
}: PopulateFormParams) => {
  if (!dataEdit) return;

  console.log('Populating form with edit data:', dataEdit);

  // Set form values
  setValue('Name', dataEdit.Name || '');
  setValue('Code', dataEdit.Code || '');
  setValue('Description', dataEdit.Description || '');
  setValue('Price', dataEdit.Price || '');
  setValue('Detail', dataEdit.Detail || '');
  setValue('Source', dataEdit.Source || '');
  // Set timestamp cho DateSinglePicker (vì useTimestamp={true})
  setValue('Guarantee', dataEdit.Guarantee || 0);
  setValue('Preserve', dataEdit.Preserve || 0);
  setValue('Specifications', dataEdit.Specifications || '');
  setValue('Unit', dataEdit.Unit || '');
  setValue('image', dataEdit.Img || '');
  setValue('listImage', dataEdit.Imgs || '');
  setValue('Discount', dataEdit.Discount || 0);
  setValue('Type', dataEdit.Type || '');
  setValue('CategoryId', dataEdit.CategoryId || '');
  setValue('AttributeId', dataEdit.AttributeId || '');
  setValue('CateAttributeId', dataEdit.CateAttributeId || '');
  setValue('InStock', dataEdit.InStock ? dataEdit.InStock.toString() : '');
  setValue('ColorId', dataEdit.ColorId || '');
  setValue('BrandsId', dataEdit.BrandsId || '');
  setValue('ConsumeId', dataEdit.ConsumeId || '');
  setValue('ShopId', dataEdit.ShopId || '');
  setValue('Status', dataEdit.Status || '');
  setValue('IsPublic', dataEdit.IsPublic || false);
  setValue('IsHot', dataEdit.IsHot || false);
  setValue('IsFreeShip', dataEdit.IsFreeShip || false);

  // Set display states để hiển thị text trong dropdown
  // Lấy tên từ ID cho các trường
  if (dataEdit.Type) {
    loadNameFromId('type', dataEdit.Type);
  } else {
    setters.setSelectType(dataEdit.TypeName || '');
  }

  if (dataEdit.CategoryId) {
    loadNameFromId('category', dataEdit.CategoryId);
  } else {
    setters.setSelectCate(dataEdit.CategoryName || '');
  }

  if (dataEdit.CateAttributeId) {
    loadNameFromId('cateAttribute', dataEdit.CateAttributeId);
  } else {
    setters.setSelectCateattribute(dataEdit.CateAttributeName || '');
  }

  if (dataEdit.AttributeId) {
    loadNameFromId('attribute', dataEdit.AttributeId);
  } else {
    setters.setSelectAttribute(dataEdit.AttributeName || '');
  }

  if (dataEdit.Source) {
    loadNameFromId('source', dataEdit.Source);
  } else {
    setters.setselectSource(dataEdit.SourceName || '');
  }

  if (dataEdit.ColorId) {
    loadNameFromId('color', dataEdit.ColorId);
  } else {
    setters.setSelectColor(dataEdit.ColorName || '');
  }

  if (dataEdit.BrandsId) {
    loadNameFromId('brand', dataEdit.BrandsId);
  } else {
    setters.setSelectBrand(dataEdit.BrandName || '');
  }

  if (dataEdit.ConsumeId) {
    loadNameFromId('consume', dataEdit.ConsumeId);
  } else {
    setters.setSelectConsume(dataEdit.ConsumeName || '');
  }

  setIsFreeShip(dataEdit.IsFreeShip || false);

  // Xử lý hiển thị ảnh
  if (dataEdit.Img || dataEdit.Imgs) {
    const url = ConfigAPI.url.split('/api/')[0];
    let renderImage: any[] = [];

    // Thêm ảnh chính nếu có
    if (dataEdit.Img) {
      try {
        const imageAvata = await BaseDA.getFilesInfor([dataEdit.Img]);
        if (
          imageAvata?.data &&
          Array.isArray(imageAvata.data) &&
          imageAvata.data.length > 0 &&
          imageAvata.data[0]?.Url
        ) {
          renderImage.push({
            path: `${url}${imageAvata.data[0].Url}`,
            mime: imageAvata.data[0].MimeType || 'image/jpeg',
            filename: imageAvata.data[0].FileName || 'image.jpg',
            id: imageAvata.data[0].Id, // Store the ID
          });
        }
      } catch (error) {
        console.error('Error fetching avatar image:', error);
      }
    }

    // Thêm danh sách ảnh phụ nếu có
    if (dataEdit.Imgs) {
      try {
        const imageIds = dataEdit.Imgs.split(',').filter((id: string) =>
          id.trim(),
        );
        if (imageIds.length > 0) {
          const imageList = await BaseDA.getFilesInfor(imageIds);
          if (imageList?.data && Array.isArray(imageList.data)) {
            const listImagesData = imageList.data
              .filter(
                (item: any) => item && item.Url && typeof item.Url === 'string',
              )
              .map((item: any) => ({
                path: `${url}${item.Url}`,
                mime: item.MimeType || 'image/jpeg',
                filename: item.FileName || 'image.jpg',
                id: item.Id, // Store the ID
              }));
            // Kiểm tra nếu phần tử đầu tiên của renderImage trùng với phần tử đầu của listImagesData
            if (renderImage.length > 0 && listImagesData.length > 0) {
              const firstRenderImage = renderImage[0];
              const firstListImage = listImagesData[0];

              console.log('Checking for duplicates:');
              console.log('First render image ID:', firstRenderImage.id);
              console.log('First list image ID:', firstListImage.id);

              // So sánh id hoặc path để kiểm tra trùng lặp
              if (
                firstRenderImage.id === firstListImage.id ||
                firstRenderImage.path === firstListImage.path
              ) {
                // Nếu trùng, bỏ phần tử đầu tiên của listImagesData
                console.log(
                  'Duplicate found, removing first element from listImagesData',
                );
                const filteredListImagesData = listImagesData.slice(1);
                renderImage = renderImage.concat(filteredListImagesData);
              } else {
                // Nếu khác, giữ lại tất cả
                console.log('No duplicate found, keeping all elements');
                renderImage = renderImage.concat(listImagesData);
              }
            } else {
              // Nếu một trong hai mảng rỗng, concat bình thường
              console.log('One of the arrays is empty, concatenating normally');
              renderImage = renderImage.concat(listImagesData);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching image list:', error);
      }
    }

    console.log('Setting images:', renderImage);

    setProductState((prev: any) => ({
      ...prev,
      image: renderImage,
      avataProduct: renderImage.length > 0 ? renderImage[0].path : '',
    }));
  }
};

// Helper function để tạo wrapper cho populateFormWithEditData
export const createPopulateFormWrapper = (
  setValue: (name: string, value: any) => void,
  setIsFreeShip: (value: boolean) => void,
  loadNameFromId: (type: string, id: string) => void,
  setProductState: (value: any) => void,
  setters: {
    setSelectType: (value: string) => void;
    setSelectCate: (value: string) => void;
    setSelectCateattribute: (value: string) => void;
    setSelectAttribute: (value: string) => void;
    setselectSource: (value: string) => void;
    setSelectColor: (value: string) => void;
    setSelectBrand: (value: string) => void;
    setSelectConsume: (value: string) => void;
  },
) => {
  return async (dataEdit: any) => {
    return await populateFormWithEditData({
      dataEdit,
      setValue,
      setIsFreeShip,
      loadNameFromId,
      setProductState,
      setters,
    });
  };
};
