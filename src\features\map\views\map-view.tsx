import React, { useEffect, useRef, useState } from 'react';
import Map<PERSON>ie<PERSON>, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { Dimensions, FlatList, Linking, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ScreenHeader from '../../../screen/layout/header';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ColorSkin } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import { FilledDirection, FilledGoldCoin, FilledPhone, FilledPitch, OutlineLocation } from '../../../assets/icon';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faAngleLeft, faMapLocation, faMapPin, faSearch } from '@fortawesome/free-solid-svg-icons';
import Geolocation from '@react-native-community/geolocation';
import { DialogSearchMap, showDialogMap } from '../local-component/search-map-dialog';

export default function MapViewIndex({ props }: any) {

    const navigation = useNavigation<any>()
    const route = useRoute<any>()
    const dialogMapRef = useRef<any>()

    const widthScr: number = Dimensions.get("screen").width

    const mapRef = useRef<any>(null);
    const [currentRegion, setcurrentRegion] = useState({
        latitude: 21.040531,
        longitude: 105.774083,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
    });

    useEffect(() => {
        if (route.params != null) {
            setcurrentRegion({
                latitude: route.params?.lat,
                longitude: route.params?.lon,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
            })
        } else {
            animatedCurrentRegion()
        }
    }, [])

    const animatedCurrentRegion = () => {
        Geolocation.getCurrentPosition((position) => {
            setcurrentRegion({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
            });
            onChangeMapPicker({ lat: position.coords.latitude, lng: position.coords.longitude });
        })
    }

    const onChangeMapPicker = ({ lat, lng }: { lat: number, lng: number }) => {
        mapRef.current.animateToRegion({
            latitude: lat,
            longitude: lng,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
        });
    }

    return (
        <View style={styles.container}>
            <DialogSearchMap ref={dialogMapRef} />
            <ScreenHeader
                backIcon={<FontAwesomeIcon icon={faAngleLeft} size={20} color="#00204D99" />}
                onBack={() => { navigation.goBack() }}
                action={
                    <TouchableOpacity style={{ padding: 8, borderRadius: 100 }} onPress={() => {
                        showDialogMap({
                            ref: dialogMapRef,
                            title: 'Tìm kiếm địa chỉ',
                            onSubmit: (value) => {
                                // console.log('====================================');
                                // console.log(value);
                                setcurrentRegion({
                                    latitude: value.geometry.location.lat,
                                    longitude: value.geometry.location.lng,
                                    latitudeDelta: 0.01,
                                    longitudeDelta: 0.01,
                                })
                                onChangeMapPicker(value.geometry.location)
                                // console.log('====================================');
                            },
                            suggestList: []
                        })
                    }}>
                        <FontAwesomeIcon icon={faSearch} size={20} color="#00204D99" />
                    </TouchableOpacity>
                }
                style={{ zIndex: 1, backgroundColor: ColorSkin.transparent, position: 'absolute', top: 0, left: 0, right: 0, alignItems: 'flex-start' }}
            />

            <MapView
                ref={mapRef}
                style={styles.map}
                provider={PROVIDER_GOOGLE}
                initialRegion={currentRegion}
                zoomEnabled={true}
                zoomControlEnabled={true}
            >
                <Marker
                    title='Yor are here'
                    description='This is a description'
                    coordinate={currentRegion} onPress={() => {
                        mapRef.current.animateToRegion(currentRegion);
                        setcurrentRegion(currentRegion)
                    }} />

            </MapView>
            <View style={{ flex: 1 }} />
            <TouchableOpacity
                onPress={() => Linking.openURL(Platform.OS === 'ios' ? `maps://app?saddr=${currentRegion.latitude}+${currentRegion.longitude}&daddr=21.040531+105.774083` : 'google.navigation:q=100+101')}
                style={{
                    padding: 12,
                    marginRight: 16,
                    backgroundColor: ColorSkin.primary,
                    borderRadius: 40,
                }}>
                <FilledDirection color={ColorSkin.white} size={28} />
            </TouchableOpacity>
            {/* <TouchableOpacity
                onPress={() => animatedCurrentRegion()}
                style={{
                    padding: 12,
                    marginRight: 16,
                    backgroundColor: ColorSkin.primary,
                    borderRadius: 40,
                }}>
                <FontAwesomeIcon icon={faMapPin} size={28} color={ColorSkin.white} />
            </TouchableOpacity> */}
            <View
                style={[styles.ViewContainer]}>
                <FlatList
                    style={{
                        paddingHorizontal: 16
                    }}
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    keyExtractor={(_, index) => index.toString()}
                    ItemSeparatorComponent={() => <View style={{ width: 16 }} />}
                    data={[{}, {}, {}, {}]}
                    renderItem={({ item, index }) => (
                        <TouchableOpacity
                            key={`${index}`}
                            style={[styles.viewCard, {
                                width: (widthScr - 40) * 0.68,
                            }]}
                        />
                    )}

                />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "flex-end",
        justifyContent: "space-between",
        gap: 16,
    },
    map: {
        ...StyleSheet.absoluteFillObject,
    },
    ViewContainer: {
        height: 255,
        paddingBottom: 16,
        backgroundColor: ColorSkin.transparent,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        shadowOpacity: 1,
        elevation: 20,
        shadowRadius: 20,
        shadowOffset: {
            width: 0,
            height: -4
        },
        shadowColor: "rgba(0, 0, 0, 0.03)"
    },
    viewCard: {
        backgroundColor: "grey",
        borderRadius: 8,
    },

    searchContainer: {
        flexDirection: 'row',
        gap: 8,
        paddingHorizontal: 16,
        paddingVertical: 8
    },

});


