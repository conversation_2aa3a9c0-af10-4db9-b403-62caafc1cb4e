import React, {useEffect, useState} from 'react';
import {FlatList} from 'react-native-gesture-handler';
import {View} from 'react-native';
import {ReviewDataDto, ReviewProductProps} from '../../../../types/dto';
import {DataController} from '../../../base-controller';
import {OrderProductDA} from '../../orderProduct/orderProductDA';
import {TypeMenuReview} from '../../../../config/Contanst';
import EmptyPage from '../../../../project-component/empty-page';
import {useSelectorShopState} from '../../../../redux/hooks/shopHook ';
import ReviewProductIteCard from '../card/CardReviewItem';
import {store} from '../../../../redux/store/store';

const ReviewProduct = (props: ReviewProductProps) => {
  const {type} = props;
  const [data, setData] = useState<ReviewDataDto[] | any[]>([]);
  const RatingController = new DataController('Rating');
  const shopInfo = store.getState().partner.data;
  const shopId = shopInfo && shopInfo.length > 0 ? shopInfo[0].Id : null;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const orderProductDA = new OrderProductDA();
  useEffect(() => {
    setIsLoading(true);
    if (type == TypeMenuReview.Product) {
      callApiGetReview(2);
    } else {
      callApiGetReview(1);
    }
  }, [type]);

  let callApiGetReview = async (Type: number) => {
    if (Type == 2) {
      let resRating = await RatingController.getPatternList({
        query: `@ShopId:  {${shopInfo?.[0].Id}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
          ProductId: ['Id', 'Name', 'Description', 'Img', 'Content'],
        },
      });
      if (resRating && resRating.code === 200) {
        let arrayOrderId = resRating?.data?.map((item: any) => item.OrderId);
        let arrayData = resRating?.data?.map((item: any) => {
          return {
            ...item,
            Customer: resRating?.Customer?.find(
              (customer: any) => customer.Id == item.CustomerId,
            ),
            Product: resRating?.Product?.find(
              (product: any) => product.Id == item.ProductId,
            ),
          };
        });
        console.log('check-arrayData1', arrayData);
        setData(arrayData);
        setIsLoading(false);
      }
    } else {
      let resRating = await RatingController.getPatternList({
        query: `@ShopId:  {${shopInfo?.[0].Id}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
          ProductId: ['Id', 'Name', 'Description', 'Img', 'Content'],
        },
      });
      if (resRating && resRating.code === 200) {
        let arrayOrderId = resRating?.data?.map((item: any) => item.OrderId);
        let arrayData = resRating?.data?.map((item: any) => {
          return {
            ...item,
            Customer: resRating?.Customer?.find(
              (customer: any) => customer.Id == item.CustomerId,
            ),
            Product: resRating?.Product?.find(
              (product: any) => product.Id == item.ProductId,
            ),
          };
        });
        console.log('check-arrayData2', arrayData);
        setData(arrayData);
        setIsLoading(false);
      }
    }
  };

  if (data && data.length > 0) {
    return (
      <FlatList
        data={data}
        style={{flex: 1}}
        keyExtractor={(item, i) => `${i} ${item.Id}`}
        renderItem={({item, index}) =>
          ReviewProductIteCard({item, index}, type as string)
        }
      />
    );
  } else {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <EmptyPage />
      </View>
    );
  }
};

export default ReviewProduct;
