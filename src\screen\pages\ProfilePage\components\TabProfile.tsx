import {View} from 'react-native';
import {useProfileActions} from '../hooks';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from 'redux/hooks/hooks';
import ActionList from './ActionList';
import {ActionItem, actionList} from '../constants';
import {FDialog} from 'wini-mobile-components';
import OrderMenu from './menu/OrderMenu';
import {CompanyProfileActions} from 'redux/reducers/company/reducer';
import {useDispatch} from 'react-redux';
import {useRef} from 'react';
import PopupCompanyProfile from 'screen/module/customer/popup/PopupCompanyProfile';
import {FPopup, showPopup} from 'component/popup/popup';
import {navigate, RootScreen} from 'router/router';

const TabProfile = () => {
  const customer = useSelectorCustomerState().data;
  const {handleActionPress, dialogRef, orderDetail} = useProfileActions();
  const company = useSelectorCustomerCompanyState().data;
  const dispatch = useDispatch();
  const popupRef = useRef<any>(null);

  const showDrawerCompanyForm = () => {
    if (company) CompanyProfileActions.getInfor(dispatch, company.Id);
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: <PopupCompanyProfile ref={popupRef} />,
    });
  };

  const pressAction = (action: ActionItem) => {
    if (action.type === 'company') {
      navigate(RootScreen.CompanyInfoPage);
      return;
    }
    handleActionPress(action);
  };

  return (
    <View>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <OrderMenu
        orderDetail={orderDetail}
        customer={customer}
        style={{
          marginTop: 8,
        }}
      />
      <ActionList
        actions={actionList}
        customer={customer}
        onActionPress={pressAction}
      />
    </View>
  );
};

export default TabProfile;
