import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import {ProductItem} from '../../../../types/ProductType';
import ProductCard from '../component/card/ProductCard';
import {Winicon} from '../../../../component/wini-icon/wini_icon';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import ProductBestSeller from '../sections/productBestSeller';
import ProductBestSellerCard from '../component/card/ProductBestSellerCard';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../../../redux/reducers/cart/CartReducer';

const {width} = Dimensions.get('window');

// Kích thước của mỗi item sản phẩm
const ITEM_WIDTH = width * 0.45;
const ITEM_HEIGHT = ITEM_WIDTH * 2; // Tăng chiều cao một chút
const ITEM_SPACING = 12;

// Tạo component ItemSeparator bên ngoài component chính
const ItemSeparator = React.memo(() => <View style={{width: ITEM_SPACING}} />);

interface ProductCarouselProps {
  title: string;
  products: ProductItem[];
  onSeeAll?: () => void;
  onProductPress: (product: ProductItem) => void;
  onFavoritePress?: (product: ProductItem) => void;
}

const ProductCarousel: React.FC<ProductCarouselProps> = ({
  title,
  products,
  onSeeAll,
  onProductPress,
  onFavoritePress,
}) => {
  const dispatch = useDispatch<any>();
  const onAddToCart = (item: ProductItem) => {
    CartActions.addItemToCart(item, 1)(dispatch);
  };

  const renderItem = ({item}: {item: ProductItem}) => {
    return (
      <ProductBestSellerCard
        key={item.Id}
        item={item}
        onPress={() => onProductPress(item)}
        onAddToCart={onAddToCart}
        onFavoritePress={onFavoritePress}
        width={ITEM_WIDTH}
        height={ITEM_HEIGHT}
      />
    );
  };

  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity onPress={onSeeAll} activeOpacity={0.7}>
          <View style={styles.seeAllButton}>
            <Text style={styles.seeAllText}>Xem thêm</Text>
            {/* <Winicon
              src="outline/arrows/circle-arrow-right"
              size={16}
              color="#1FB5A8"
            /> */}
          </View>
        </TouchableOpacity>
      </View>

      <FlatList
        data={products}
        renderItem={renderItem}
        keyExtractor={item => item.Id?.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={ITEM_WIDTH + ITEM_SPACING}
        snapToAlignment="start"
        decelerationRate="fast"
        pagingEnabled={false}
        disableIntervalMomentum={true}
        ItemSeparatorComponent={ItemSeparator}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    ...TypoSkin.title2,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    ...TypoSkin.title5,
    fontWeight: 500,
    color: ColorThemes.light.primary_main_color,
  },
});

export default ProductCarousel;
