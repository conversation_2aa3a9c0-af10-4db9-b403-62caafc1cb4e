import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {FCheckbox} from '../../../../../component/export-component';
import iconSvg from '../../../../../svgs/iconSvg';

interface ToiletItem {
  Id: string;
  Name: string;
  Address: string;
  CustomerId: string;
  Status: number;
}

interface ToiletSelectionCardProps {
  item: ToiletItem | any;
  isSelected?: boolean | undefined;
  onPress: (id: string) => void;
  showSelect?: boolean;
  disabled?: boolean;
  warningText?: string;
}

const ToiletSelectionCard: React.FC<ToiletSelectionCardProps> = ({
  item,
  isSelected,
  onPress,
  showSelect = true,
  disabled = false,
  warningText = '',
}) => {
  return (
    <Pressable style={[styles.container, disabled && styles.disabledContainer]}>
      <TouchableOpacity
        style={styles.itemRow}
        onPress={() => onPress(item.Id)}
        disabled={disabled}>
        {showSelect && isSelected !== undefined && (
          <View style={{marginRight: 12}}>
            <FCheckbox
              value={isSelected}
              onChange={() => onPress(item.Id)}
              size={20}
              borderRadius={4}
              disable={disabled}
            />
          </View>
        )}
        <View style={[styles.itemContent, disabled && styles.disabledContent]}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.toilet} size={24} />
          </View>
          <View style={styles.itemInfo}>
            <Text style={[styles.itemId, disabled && styles.disabledText]}>
              {item.Name}
            </Text>
            <Text style={[styles.itemLabel, disabled && styles.disabledText]}>
              {item.Address}
            </Text>
            {warningText && (
              <Text style={styles.warningText}>{warningText}</Text>
            )}
          </View>
        </View>
        {item.isUpdated && <Text style={styles.updatedText}>Đã chỉnh sửa</Text>}
      </TouchableOpacity>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  disabledContainer: {
    opacity: 0.5,
  },
  updatedText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.success_main_color,
    fontStyle: 'italic',
    position: 'absolute',
    top: 8,
    right: 8,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
  },
  disabledContent: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  iconContainer: {
    marginLeft: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemInfo: {
    flex: 1,
    marginLeft: 12,
    marginVertical: 8,
  },
  itemId: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  itemLabel: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 20,
  },
  disabledText: {
    color: ColorThemes.light.neutral_text_disabled_color,
  },
  warningText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.warning_main_color,
    marginTop: 4,
    fontStyle: 'italic',
  },
});

export default ToiletSelectionCard;
