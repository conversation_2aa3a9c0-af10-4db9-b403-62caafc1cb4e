import React from 'react';
import {View} from 'react-native';
import ListTile from '../../../../../component/list-tile/list-tile';
import {Winicon} from '../../../../../component/export-component';
import {ColorThemes} from '../../../../../assets/skin/colors';
import Content from 'screen/pages/DetailNews/components/Content';

interface FAQItemProps {
  item: any;
  index: number;
  isExpanded: boolean;
  isContentExpanded: boolean;
  onToggle: () => void;
  onToggleContent: () => void;
}

const FAQItem: React.FC<FAQItemProps> = ({
  item,
  index,
  isExpanded,
  onToggle,
}) => {
  const styles = {
    container: {
      borderBottomColor: ColorThemes.light.neutral_main_border_color,
      borderBottomWidth: 1,
    },
    bottomContainer: {
      paddingTop: 8,
      alignItems: 'flex-start' as const,
      width: '100%' as const,
    },
    contentContainer: {
      flex: 1,
    },
    contentContainerCollapsed: {
      flex: 1,
      maxHeight: 100,
      overflow: 'hidden' as const,
    },
    toggleButton: {
      alignSelf: 'flex-end' as const,
      marginTop: 8,
      paddingVertical: 4,
      paddingHorizontal: 8,
    },
    toggleText: {
      color: ColorThemes.light.primary_main_color,
      fontWeight: '500' as const,
      textDecorationLine: 'underline' as const,
      fontSize: 14,
    },
  };

  return (
    <ListTile
      key={index}
      style={styles.container}
      title={`${item?.Name}`}
      onPress={onToggle}
      trailing={
        <Winicon
          src={
            isExpanded
              ? 'outline/arrows/arrow-sm-down'
              : 'fill/arrows/arrow-sm-right'
          }
          size={24}
          color={ColorThemes.light.neutral_text_body_color}
        />
      }
      bottom={
        isExpanded ? (
          <View style={styles.bottomContainer}>
            {item?.Content ? <Content data={item?.Content} /> : null}
          </View>
        ) : null
      }
    />
  );
};

export default FAQItem;
