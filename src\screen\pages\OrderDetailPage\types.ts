// TypeScript interfaces for OrderDetailPage components

export interface OrderDetailRouteParams {
  orderId: string;
  type?: string;
  CancelReason?: string;
  refundInfo?: any;
}

export interface OrderData {
  Id: string;
  Code: string;
  CustomerId: string;
  ShopId: string;
  DateCreated: number;
  DateUpdated?: number;
  DateProcess?: number;
  Status: number;
  Value: number;
  AddressId: string;
  PaymentType: number;
  CancelReason?: string;
  Address?: AddressData;
}

export interface AddressData {
  Id: string;
  Name: string;
  Mobile: string;
  Email: string;
  Address: string;
}

export interface OrderDetailItem {
  Id: string;
  ProductId: string;
  OrderId: string;
  Name: string;
  Img: string;
  Price: number;
  Discount?: number;
  Quantity: number;
  CategoryId: string;
  Reward?: number;
}

export interface RefundInfo {
  all: number;
  allRefund: number;
}

// Component Props Interfaces

export interface OrderStatusTimelineProps {
  currentStatus: number;
  order: OrderData | null;
}

export interface OrderInfoProps {
  order: OrderData | null;
  currentStatus: number;
  CancelReason?: string;
  onCopyOrderCode: () => void;
}

export interface OrderItemsProps {
  style?: any;
  orderDetails: OrderDetailItem[];
  order: OrderData | null;
  isCustomer?: boolean;
  type?: string;
  refundInfo?: RefundInfo;
  onProductPress: (productId: string) => void;
  onCancelOrder: () => void;
  onRequestRefund: () => void;
  onReviewProduct: () => void;
}

export interface OrderActionButtonsProps {
  currentStatus: number;
  isCustomer: boolean;
  onChatWithShop: () => void;
  onContactShop: () => void;
  onConfirmOrder?: () => void;
  onRejectOrder?: () => void;
  onUpdateOrderStatus?: () => void;
  showSellerActions?: boolean; // true for seller view, false for buyer view
  showStatusUpdate?: boolean; // true for status update view
}

// Hook Interfaces

export interface UseOrderDataReturn {
  loading: boolean;
  order: OrderData | null;
  orderDetails: OrderDetailItem[];
  refreshing: boolean;
  fetchOrderData: () => Promise<void>;
  handleRefresh: () => Promise<void>;
}

export interface UseOrderActionsReturn {
  isCancelPopupVisible: boolean;
  isSubmittingCancel: boolean;
  isUpdateStatusPopupVisible: boolean;
  isSubmittingUpdateStatus: boolean;
  setCancelPopupVisible: (visible: boolean) => void;
  setUpdateStatusPopupVisible: (visible: boolean) => void;
  handleCancelOrder: () => void;
  handleSubmitRejectOrder: (reason: string) => Promise<void>;
  handleSubmitUpdateStatus: (item: any, status_str?: string) => Promise<void>;
  dialogRef: React.RefObject<any>;
}

// Payment Method Interface
export interface PaymentMethod {
  id: number;
  name: string;
}
