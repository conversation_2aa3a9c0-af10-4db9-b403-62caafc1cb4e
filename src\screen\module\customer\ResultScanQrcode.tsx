import {useNavigation, useRoute} from '@react-navigation/native';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../redux/hooks/hooks';
import ScreenHeader from '../../layout/header';
import {useEffect, useRef, useState} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import AppButton from '../../../component/button';
import {showPopup, closePopup, FPopup} from '../../../component/popup/popup';
import {Text} from 'react-native';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {useForm} from 'react-hook-form';
import ImageCropPicker from 'react-native-image-crop-picker';
import {TypoSkin} from '../../../assets/skin/typography';
import {ComponentStatus} from '../../../component/component-status';
import {
  FRating,
  showSnackbar,
  Winicon,
} from '../../../component/export-component';
import ListTile from '../../../component/list-tile/list-tile';
import ConfigAPI from '../../../config/configApi';
import {TextFieldForm} from '../../../project-component/component-form';
import {SkeletonImage} from '../../../project-component/skeleton-img';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../base-controller';
import {BaseDA} from '../../baseDA';
import WScreenFooter from '../../layout/footer';
import {TaskType, TicketStatus, TicketType} from '../service/components/da';
import DocumentPicker from 'react-native-document-picker';
import {PopupCheckinout} from './view/CheckinCheckout';
import {validatePhoneNumber} from '../../../utils/validate';
import FLoading from '../../../component/Loading/FLoading';
import {CustomerRoleList} from '../../../redux/reducers/user/da';
import TitleHeader from '../../layout/headers/TitleHeader';

export default function ResultScanQrcode() {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  let {toiletItem} = route.params;
  const owner = useSelectorCustomerCompanyState().owner;
  const company = useSelectorCustomerCompanyState().data;
  const user = useSelectorCustomerState().data;
  const role = useSelectorCustomerState().role;
  const [step, setStep] = useState(0);
  const popRef = useRef<any>();
  const [customer, setCustomer] = useState<any>();
  const [tasks, setTasks] = useState({
    data: Array<any>(),
    totalCount: undefined,
  });

  const [isLoading, setLoading] = useState(false);

  const getData = async () => {
    if (toiletItem) {
      setLoading(true);
      const customerController = new DataController('Customer');
      customerController.getById(toiletItem.CustomerId).then(res => {
        if (res.code === 200) setCustomer(res.data);
      });

      const taskController = new DataController('Task');
      const now = new Date();
      let toiletId = toiletItem?.Id;
      let query = `@ToiletId:{${toiletId}} @CustomerId:{${user?.Id}} @Type:[${TaskType.other} ${TaskType.other}] @RequireCheckin:{true} @DateEnd:[${now.getTime()} +inf]`; //
      const res = await taskController.aggregateList({
        page: 1,
        size: 20,
        searchRaw: query,
      });
      if (res.code === 200) {
        setTasks({
          data: res.data,
          totalCount: res.totalCount,
        });
      }
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, [toiletItem]);

  const [checkinTitle, setCheckinTitle] = useState('');

  if (!toiletItem) return null;

  if (isLoading || !toiletItem) {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <TitleHeader
          title={'Thông tin nhà vệ sinh'}
          onBack={() => {
            navigation.pop();
          }}
        />
        <FLoading visible={isLoading} />
      </View>
    );
  }

  const returnView = () => {
    switch (step) {
      case 4:
        return (
          <PopupCheckinout
            setCheckinTitle={setCheckinTitle}
            checkinTitle={checkinTitle}
            tasksData={tasks.data}
            toiletItem={toiletItem}
            setStepParent={setStep}
            onDone={(vl: any) => {
              if (vl) {
                setStep(0);
              }
            }}
          />
        );
      case 2:
        return (
          <AddProblemTicket
            type={TicketType.accident}
            toiletId={toiletItem.Id}
            setStep={setStep}
          />
        );
      case 3:
        return (
          <RatingToilet
            toiletItem={toiletItem}
            onDone={() => {
              setStep(0);
            }}
          />
        );
      default:
        return <View></View>;
    }
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'center',
      }}>
      <FPopup ref={popRef} />
      <TitleHeader
        title={
          isLoading || !toiletItem
            ? 'Thông tin nhà vệ sinh'
            : step === 2
              ? 'Phiếu phản ánh/sự cố'
              : step === 3
                ? 'Đánh giá nhà vệ sinh'
                : step == 4
                  ? checkinTitle
                  : 'Thông tin nhà vệ sinh'
        }
        onBack={() => {
          if (step == 0) {
            navigation.pop();
          } else {
            setStep(0);
            setCheckinTitle('');
          }
        }}
        actionView={
          step == 4 ? (
            <TouchableOpacity
              onPress={() => setCheckinTitle('')}
              style={{
                padding: 8,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
                borderRadius: 100,
              }}>
              <Winicon src="outline/user interface/calendar-event" size={16} />
            </TouchableOpacity>
          ) : (
            <View></View>
          )
        }
      />

      {step == 0 || step == 1 ? (
        <ScrollView
          style={{
            paddingTop: 16,
            paddingHorizontal: 16,
            flex: 1,
            width: Dimensions.get('screen').width,
          }}>
          <View
            style={{
              flex: 1,
              height: '100%',
              gap: 16,
              alignItems: 'center',
              alignContent: 'center',
              justifyContent: 'center',
            }}>
            <ListTile
              title={toiletItem?.Name ?? '-'}
              titleStyle={[
                TypoSkin.heading6,
                {color: ColorThemes.light.neutral_text_title_color},
              ]}
              style={{
                backgroundColor: ColorThemes.light.transparent,
                paddingVertical: 16,
                paddingHorizontal: 0,
              }}
              subtitle={`Chủ NVS: ${customer?.Name ?? '-'}`}
              subTitleStyle={[
                TypoSkin.subtitle3,
                {color: ColorThemes.light.neutral_text_subtitle_color},
              ]}
              bottom={
                <View style={{gap: 16, alignItems: 'flex-start'}}>
                  <ListTile
                    style={{padding: 0, paddingTop: 16}}
                    leading={
                      <Winicon
                        src="outline/user interface/phone-call"
                        size={16}
                        color={ColorThemes.light.neutral_text_subtitle_color}
                      />
                    }
                    title={`Số điện thoại liên hệ: ${toiletItem?.Mobile ?? '-'}`}
                    titleStyle={[
                      TypoSkin.label4,
                      {color: ColorThemes.light.neutral_text_label_color},
                    ]}
                  />
                  <ListTile
                    style={{padding: 0}}
                    leading={
                      <Winicon
                        src="outline/location/compass-3"
                        size={16}
                        color={ColorThemes.light.neutral_text_subtitle_color}
                      />
                    }
                    title={`Địa chỉ: ${toiletItem?.Address ?? '-'}`}
                    titleStyle={[
                      TypoSkin.label4,
                      {color: ColorThemes.light.neutral_text_label_color},
                    ]}
                  />
                </View>
              }
            />
            <View
              style={{
                width: '100%',
                flex: 1,
                flexDirection: 'row',
                alignContent: 'flex-start',
              }}>
              <Text
                style={[
                  TypoSkin.buttonText2,
                  {color: ColorThemes.light.neutral_text_label_color},
                ]}>
                Chọn chức năng bạn muốn sử dụng:
              </Text>
            </View>
            <View style={{flex: 1, gap: 16, width: '100%'}}>
              {(() => {
                if (!user) return null;
                const hasAnyRole = CustomerRoleList.filter(r =>
                  role?.Role?.split(',').includes(r),
                );
                if (
                  toiletItem.CustomerId !== user.Id ||
                  hasAnyRole?.length == 0 ||
                  tasks.data.length == 0 ||
                  company?.Id == ConfigAPI.ktxCompanyId
                )
                  return null;

                return (
                  <AppButton
                    title={'Chấm công'}
                    disabled={hasAnyRole?.length == 0 || tasks.data.length == 0}
                    backgroundColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{
                      height: 40,
                      flex: 1,
                      borderRadius: 8,
                      paddingHorizontal: 12,
                      paddingVertical: 5,
                    }}
                    onPress={async () => {
                      setCheckinTitle('');
                      setStep(4);
                      // showPopup({
                      //     ref: popRef, enableDismiss: false, children: <PopupCheckinout tasksData={tasks.data} toiletItem={toiletItem} ref={popRef} onDone={(vl: any) => { if (vl) { navigation.pop() } }} />
                      // })
                    }}
                    textColor={
                      ColorThemes.light.neutral_absolute_background_color
                    }
                  />
                );
              })()}
              <AppButton
                title={'Phản ánh sự cố'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{
                  height: 40,
                  flex: 1,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                onPress={() => {
                  setStep(2);
                }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
              />
              <AppButton
                title={'Đánh giá nhà vệ sinh'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{
                  height: 40,
                  flex: 1,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                onPress={() => {
                  setStep(3);
                }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
              />
            </View>
          </View>
        </ScrollView>
      ) : (
        <View
          style={{
            flex: 1,
            width: '100%',
            paddingTop: 16,
          }}>
          {returnView()}
        </View>
      )}
    </View>
  );
}

const AddProblemTicket = (data: {type: any; toiletId: any; setStep: any}) => {
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID()},
  });
  const user = useSelectorCustomerState().data;
  const popupRef = useRef<any>();
  const navigation = useNavigation<any>();
  const [isLoading, setLoading] = useState(false);

  const _onSubmit = async (ev: any) => {
    setLoading(true);
    if (ev.Files?.length) {
      const res = await BaseDA.uploadFiles(ev.Files);
      if (res?.length) var File = res.map((e: any) => e.Id).join(',');
    }
    delete ev.Files;
    const newTicket = {
      ...methods.getValues(),
      DateCreated: Date.now(),
      Sort: 1,
      File: File,
      ToiletId: data?.toiletId ?? undefined,
      CustomerId: user?.Id,
      Type: data.type ?? TicketType.feedback,
      Status: TicketStatus.init,
    };
    const ticketController = new DataController('Ticket');

    const res = await ticketController.add([newTicket]);
    if (res.code !== 200) {
      setLoading(false);
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    } else {
      setLoading(false);
      showSnackbar({
        message: 'Gửi thông tin thành công',
        status: ComponentStatus.SUCCSESS,
      });
      data.setStep(0);
    }
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  useEffect(() => {
    if (user) {
      methods.setValue('Mobile', `${user?.Mobile}`);
    }
  }, [user]);

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: true,
      cropping: false,
      maxFiles: 5,
    });
    if (image) {
      methods.setValue('Files', [
        ...(methods.getValues('Files') ?? []),
        ...image.map((e: any) => ({
          name: e.filename ?? 'new img',
          type: e.mime,
          uri: e.path,
          size: e.size,
        })),
      ]);
      closePopup(popupRef);
    }
  };

  const pickerFile = async () => {
    try {
      const result = await DocumentPicker.pick({
        allowMultiSelection: true,
      });
      if (result) {
        methods.setValue('Files', [
          ...(methods.getValues('Files') ?? []),
          ...result.map((e: any) => ({
            name: e.name ?? 'new file img',
            type: e.type,
            uri: e.uri,
            size: e.size,
          })),
        ]);
        closePopup(popupRef);
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('Document picker cancelled by user');
      } else {
        console.log('Error picking document:', err);
      }
      return null;
    }
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FPopup ref={popupRef} />
      <FLoading visible={isLoading} />
      <ScrollView style={{flex: 1, paddingTop: 16}}>
        <KeyboardAvoidingView
          behavior={'padding'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 160 : 0}
          style={{height: '100%', width: '100%', paddingHorizontal: 16}}>
          <Pressable style={{flex: 1, gap: 16}}>
            <TextFieldForm
              label="Tên yêu cầu"
              required
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Name"
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              required
              disabled={user ? true : false}
              label="Số điện thoại"
              returnKeyType="done"
              style={{flex: 1}}
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 46,
                    paddingHorizontal: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                    borderRadius: 8,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.buttonText3,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    +84
                  </Text>
                </View>
              }
              type="number-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
                  return;
                }
                var mobile = ev.trim();
                // Check if the number doesn't already start with 0 or +84
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile; // Add 0 at the beginning
                }
                const val = validatePhoneNumber(mobile);
                if (val) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
              }}
            />
            <View style={{gap: 8}}>
              <Text numberOfLines={1} style={[TypoSkin.label3]}>
                Tệp đính kèm
              </Text>
              <TouchableOpacity
                onPress={() => {
                  showPopup({
                    ref: popupRef,
                    enableDismiss: true,
                    children: (
                      <View
                        style={{
                          backgroundColor:
                            ColorThemes.light.neutral_absolute_background_color,
                          height: Dimensions.get('window').height / 3,
                          borderTopLeftRadius: 12,
                          borderTopRightRadius: 12,
                        }}>
                        <ScreenHeader
                          style={{
                            backgroundColor: ColorThemes.light.transparent,
                            flexDirection: 'row',
                            paddingVertical: 4,
                          }}
                          title={`Thêm File`}
                          prefix={<View />}
                          action={
                            <View
                              style={{
                                flexDirection: 'row',
                                padding: 12,
                                alignItems: 'center',
                              }}>
                              <Winicon
                                src="outline/layout/xmark"
                                onClick={() => closePopup(popupRef)}
                                size={20}
                                color={
                                  ColorThemes.light.neutral_text_body_color
                                }
                              />
                            </View>
                          }
                        />
                        <ListTile
                          onPress={() => {
                            pickerImg();
                          }}
                          title={'Thêm ảnh, video'}
                        />
                        <ListTile
                          onPress={() => {
                            pickerFile();
                          }}
                          title={'Thêm Files'}
                        />
                      </View>
                    ),
                  });
                }}
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 8,
                  borderWidth: 0.4,
                  borderColor: ColorThemes.light.neutral_main_border,
                  borderStyle: 'dashed',
                  borderRadius: 8,
                  padding: 8,
                }}>
                <SkeletonImage
                  source={{
                    uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                  }}
                  style={{width: 35, height: 35, objectFit: 'cover'}}
                />
                <Text
                  numberOfLines={1}
                  style={{
                    ...TypoSkin.buttonText4,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  Thêm ảnh, tệp
                </Text>
              </TouchableOpacity>
            </View>
            {methods.watch('Files')?.length > 0
              ? methods
                  .watch('Files')
                  ?.slice(0, 3)
                  ?.map((item: any, index: number) => {
                    return (
                      <ListTile
                        key={`${item.uri}-${index}`}
                        leading={
                          <SkeletonImage
                            source={{uri: item.uri}}
                            style={{
                              width: 35,
                              height: 35,
                              objectFit: 'cover',
                              borderRadius: 4,
                            }}
                          />
                        }
                        title={item.name ?? `File ${index + 1}`}
                        titleStyle={[
                          TypoSkin.heading7,
                          {color: ColorThemes.light.neutral_text_title_color},
                        ]}
                        subtitle={`${Math.round(item.size / (1024 * 1024))}MB`}
                        listtileStyle={{gap: 16}}
                        style={{
                          borderColor:
                            ColorThemes.light.neutral_main_border_color,
                          borderWidth: 1,
                          padding: 8,
                        }}
                        trailing={
                          <TouchableOpacity
                            onPress={async () => {
                              methods.setValue('Files', [
                                ...(methods.getValues('Files') ?? []).filter(
                                  (e: any) => e.uri !== item.uri,
                                ),
                              ]);
                            }}
                            style={{padding: 4}}>
                            <FontAwesomeIcon
                              icon={faMinusCircle}
                              size={20}
                              color="#D72525FF"
                              style={{
                                backgroundColor: '#fff',
                                borderRadius: 20,
                              }}
                            />
                          </TouchableOpacity>
                        }
                      />
                    );
                  })
              : null}
            {methods.watch('Files') && methods.watch('Files')?.length > 3 ? (
              <Text
                style={{
                  color: ColorThemes.light.neutral_text_body_color,
                  textAlign: 'center',
                }}>
                +{methods.watch('Files').length - 3} Tệp
              </Text>
            ) : null}
            <TextFieldForm
              control={methods.control}
              name="Description"
              errors={methods.formState.errors}
              label="Mô tả"
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 100,
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              register={methods.register}
            />
          </Pressable>
        </KeyboardAvoidingView>
      </ScrollView>

      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 20,
        }}>
        <AppButton
          title={'Gửi'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={methods.handleSubmit(_onSubmit, _onError)}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
};

const RatingToilet = ({toiletItem, onDone}: any) => {
  const methods = useForm({shouldFocusError: false});
  const [rate, setState] = useState(0);
  const user = useSelectorCustomerState().data;
  const navigation = useNavigation<any>();

  return (
    <KeyboardAvoidingView
      behavior={'padding'}
      style={{flex: 1, alignContent: 'center'}}>
      <ScrollView style={{flex: 1, alignContent: 'center'}}>
        <View
          style={{
            paddingBottom: 100,
            flex: 1,
            gap: 16,
            alignItems: 'center',
            width: '100%',
            height: '100%',
            alignContent: 'center',
            justifyContent: 'center',
            paddingHorizontal: 16,
          }}>
          <Winicon
            src="https://redis.ktxgroup.com.vn/api/file/img/fc4b703aee2e40ada3f4c48a855ef55b"
            size={200}
          />
          <Text
            style={[
              TypoSkin.buttonText2,
              {
                color: ColorThemes.light.neutral_text_label_color,
                textAlign: 'center',
              },
            ]}>
            Cảm ơn bạn đã sử dụng nhà vệ sinh của chúng tôi
          </Text>
          <Text
            style={{
              ...TypoSkin.subtitle2,
              color: ColorThemes.light.neutral_text_title_color,
              textAlign: 'center',
            }}>
            Hãy để lại đánh giá/góp ý của bạn về nhà vệ sinh để giúp tiếp tục
            phát triển dịch vụ tốt hơn.
          </Text>
          <FRating
            value={rate}
            fillColor={ColorThemes.light.primary_main_color}
            onChange={e => setState(e)}
            size={32}
          />
          <TextFieldForm
            control={methods.control}
            name="Content"
            errors={methods.formState.errors}
            placeholder={'Nhập phản hồi/góp ý...'}
            style={{backgroundColor: ColorThemes.light.transparent}}
            textFieldStyle={{
              height: 100,
              width: '100%',
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 16,
              justifyContent: 'flex-start',
              backgroundColor: ColorThemes.light.transparent,
            }}
            textStyle={{textAlignVertical: 'top'}}
            numberOfLines={10}
            multiline={true}
            register={methods.register}
          />
        </View>
      </ScrollView>
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 20,
        }}>
        <TouchableOpacity
          style={styles.button}
          onPress={async () => {
            const newRatingData = {
              Id: randomGID(),
              Name: 'Đánh giá nhà vệ sinh ' + toiletItem.Name,
              DateCreated: Date.now(),
              CustomerId: user?.Id,
              Sort: 1,
              ToiletId: toiletItem.Id,
              Content: methods.getValues('Content'),
              Value: rate,
            };
            const controller = new DataController('Rating');
            const res = await controller.add([newRatingData]);
            if (res.code !== 200)
              return showSnackbar({
                message: res.message,
                status: ComponentStatus.ERROR,
              });
            showSnackbar({
              message: 'Gửi đánh giá thành công. Cảm ơn bạn!',
              status: ComponentStatus.SUCCSESS,
            });
            if (onDone) onDone();
          }}>
          <Text style={styles.buttonText}>Gửi đánh giá</Text>
        </TouchableOpacity>
      </WScreenFooter>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  bottomButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
    gap: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
