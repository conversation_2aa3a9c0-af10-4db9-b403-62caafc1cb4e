import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, ScrollView, TouchableOpacity} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {useForm} from 'react-hook-form';
import {DateSinglePicker} from '../../../../../project-component/form';
import {Ultis} from '../../../../../utils/Utils';

const EditFormDemo = () => {
  const [demoResults, setDemoResults] = useState<any>(null);
  const [editData, setEditData] = useState<any>(null);

  const {control, setValue, watch, getValues} = useForm({
    defaultValues: {
      Guarantee: 0,
      Preserve: 0,
    },
  });

  // Hàm CoverMiliSecondToDate được cải thiện
  const CoverMiliSecondToDate = (miliSecond: number): string => {
    if (!miliSecond || miliSecond === 0 || isNaN(miliSecond)) {
      return '';
    }

    try {
      const date = new Date(miliSecond);
      
      if (isNaN(date.getTime())) {
        return '';
      }

      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      
      return `${day}/${month}/${year}`;
    } catch (error) {
      console.warn('Error converting timestamp to date:', error);
      return '';
    }
  };

  // Mô phỏng dữ liệu edit từ database
  const simulateEditData = () => {
    const mockEditData = {
      Id: 'test-id-123',
      Name: 'Sản phẩm test',
      Guarantee: 1735689600000, // 01/01/2025
      Preserve: 1767225600000,  // 01/01/2026
      // Các field khác...
    };

    setEditData(mockEditData);

    // Mô phỏng populateFormWithEditData
    console.log('=== DEMO EDIT FORM ===');
    console.log('1. Dữ liệu edit từ database:', mockEditData);

    // Set timestamp trực tiếp cho DateSinglePicker (useTimestamp={true})
    setValue('Guarantee', mockEditData.Guarantee || 0);
    setValue('Preserve', mockEditData.Preserve || 0);

    console.log('2. Đã set value cho form:', {
      Guarantee: mockEditData.Guarantee,
      Preserve: mockEditData.Preserve,
    });

    // Kiểm tra hiển thị
    const displayResults = {
      guaranteeTimestamp: mockEditData.Guarantee,
      guaranteeFormatted: CoverMiliSecondToDate(mockEditData.Guarantee),
      guaranteeUtilsFormat: Ultis.datetoString(new Date(mockEditData.Guarantee), 'dd/MM/yyyy'),
      preserveTimestamp: mockEditData.Preserve,
      preserveFormatted: CoverMiliSecondToDate(mockEditData.Preserve),
      preserveUtilsFormat: Ultis.datetoString(new Date(mockEditData.Preserve), 'dd/MM/yyyy'),
    };

    console.log('3. Kết quả hiển thị:', displayResults);
    setDemoResults(displayResults);
  };

  const testFormValues = () => {
    const currentValues = getValues();
    console.log('=== CURRENT FORM VALUES ===');
    console.log('Form values:', currentValues);
    
    const testResults = {
      formValues: currentValues,
      guaranteeDisplay: currentValues.Guarantee ? CoverMiliSecondToDate(currentValues.Guarantee) : 'Chưa có',
      preserveDisplay: currentValues.Preserve ? CoverMiliSecondToDate(currentValues.Preserve) : 'Chưa có',
    };

    setDemoResults(prev => ({
      ...prev,
      currentTest: testResults,
    }));
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Demo Edit Form - Hiển thị Thời gian</Text>
      <Text style={styles.subtitle}>
        Test việc hiển thị thời gian khi edit sản phẩm
      </Text>

      <TouchableOpacity style={styles.button} onPress={simulateEditData}>
        <Text style={styles.buttonText}>Mô phỏng Load Edit Data</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testFormValues}>
        <Text style={styles.buttonText}>Kiểm tra Form Values</Text>
      </TouchableOpacity>

      {/* Demo DateSinglePicker */}
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>📅 DateSinglePicker Demo:</Text>
        
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Thời gian bảo hành:</Text>
          <DateSinglePicker
            control={control}
            name="Guarantee"
            placeholder="Chọn thời gian bảo hành"
            useTimestamp={true}
            style={styles.datePicker}
            onDateChange={selectedDate => {
              console.log('Guarantee date selected:', selectedDate);
            }}
          />
        </View>

        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Thời gian bảo dưỡng:</Text>
          <DateSinglePicker
            control={control}
            name="Preserve"
            placeholder="Chọn thời gian bảo dưỡng"
            useTimestamp={true}
            style={styles.datePicker}
            onDateChange={selectedDate => {
              console.log('Preserve date selected:', selectedDate);
            }}
          />
        </View>
      </View>

      {demoResults && (
        <View style={styles.resultsContainer}>
          <Text style={styles.sectionTitle}>📊 Kết quả Demo:</Text>
          
          {editData && (
            <View style={styles.dataBox}>
              <Text style={styles.dataTitle}>Dữ liệu Edit từ Database:</Text>
              <Text style={styles.dataText}>
                Guarantee: {editData.Guarantee} (timestamp)
              </Text>
              <Text style={styles.dataText}>
                Preserve: {editData.Preserve} (timestamp)
              </Text>
            </View>
          )}

          <View style={styles.dataBox}>
            <Text style={styles.dataTitle}>Hiển thị được format:</Text>
            <Text style={styles.dataText}>
              Guarantee: {demoResults.guaranteeFormatted}
            </Text>
            <Text style={styles.dataText}>
              Preserve: {demoResults.preserveFormatted}
            </Text>
          </View>

          <View style={styles.dataBox}>
            <Text style={styles.dataTitle}>Ultis.datetoString format:</Text>
            <Text style={styles.dataText}>
              Guarantee: {demoResults.guaranteeUtilsFormat}
            </Text>
            <Text style={styles.dataText}>
              Preserve: {demoResults.preserveUtilsFormat}
            </Text>
          </View>

          {demoResults.currentTest && (
            <View style={styles.dataBox}>
              <Text style={styles.dataTitle}>Current Form Values:</Text>
              <Text style={styles.dataText}>
                Guarantee: {demoResults.currentTest.formValues.Guarantee}
              </Text>
              <Text style={styles.dataText}>
                Preserve: {demoResults.currentTest.formValues.Preserve}
              </Text>
              <Text style={styles.dataText}>
                Display Guarantee: {demoResults.currentTest.guaranteeDisplay}
              </Text>
              <Text style={styles.dataText}>
                Display Preserve: {demoResults.currentTest.preserveDisplay}
              </Text>
            </View>
          )}

          <View style={styles.summaryBox}>
            <Text style={styles.summaryTitle}>✅ Tóm tắt cải thiện:</Text>
            <Text style={styles.summaryText}>
              • CoverMiliSecondToDate đã được cải thiện với padding 0
            </Text>
            <Text style={styles.summaryText}>
              • populateFormWithEditData set timestamp trực tiếp
            </Text>
            <Text style={styles.summaryText}>
              • DateSinglePicker xử lý timestamp đúng cách
            </Text>
            <Text style={styles.summaryText}>
              • Hiển thị format dd/mm/yyyy nhất quán
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_darker_color,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  formSection: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: ColorThemes.light.neutral_text_title_color,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  datePicker: {
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    padding: 12,
  },
  resultsContainer: {
    marginTop: 10,
  },
  dataBox: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  dataTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  dataText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
    color: '#666',
  },
  summaryBox: {
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#2d5a2d',
  },
  summaryText: {
    fontSize: 12,
    marginBottom: 4,
    color: '#2d5a2d',
  },
});

export default EditFormDemo;
