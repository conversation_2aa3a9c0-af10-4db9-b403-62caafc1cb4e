import {Control, FieldErrors, FieldValues} from 'react-hook-form';
import {TextStyle, ViewStyle} from 'react-native';

// Common base interface for date picker props
interface BaseDatePickerProps<T extends FieldValues = FieldValues> {
  // Required props
  control: Control<T>;
  errors: FieldErrors<T>;

  // Optional props
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;

  // Styling props
  style?: ViewStyle;
  textStyle?: TextStyle;

  // Icon and decorations
  icon?: React.ReactNode;
  prefix?: React.ReactNode;

  // Date constraints
  minDate?: Date;
  maxDate?: Date;
}

// Date Range Picker specific props
export interface DateRangePickerProps<T extends FieldValues = FieldValues>
  extends BaseDatePickerProps<T> {
  startDateName: string;
  endDateName: string;
  onDateRangeChange?: (startDate?: Date, endDate?: Date) => void;
  // Support for timestamp format
  useTimestamp?: boolean;
  onTimestampRangeChange?: (
    startTimestamp?: number,
    endTimestamp?: number,
  ) => void;
}

// Single Date Picker specific props
export interface SingleDatePickerProps<T extends FieldValues = FieldValues>
  extends BaseDatePickerProps<T> {
  name: string;
  onDateChange?: (date?: Date) => void;
  // Support for timestamp format
  useTimestamp?: boolean;
  onTimestampChange?: (timestamp?: number) => void;
}
