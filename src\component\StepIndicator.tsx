import React from 'react';
import {View, StyleSheet} from 'react-native';
import {ColorThemes} from '../assets/skin/colors';

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  currentStep,
  totalSteps,
}) => {
  return (
    <View style={styles.container}>
      {Array.from({length: totalSteps}, (_, index) => (
        <View
          key={index}
          style={[
            styles.step,
            {
              backgroundColor:
                index < currentStep
                  ? ColorThemes.light.primary_main_color
                  : ColorThemes.light.neutral_text_disabled_reverse_color,
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 8,
    width: '90%',
    alignSelf: 'center',
  },
  step: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
});

export default StepIndicator;
