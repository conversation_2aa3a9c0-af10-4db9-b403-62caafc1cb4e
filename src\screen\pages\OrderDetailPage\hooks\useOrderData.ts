import {useState, useEffect, useCallback} from 'react';
import {OrderProductDA} from '../../../module/orderProduct/orderProductDA';
import {UseOrderDataReturn, OrderData, OrderDetailItem} from '../types';

export const useOrderData = (orderId: string): UseOrderDataReturn => {
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<OrderData | null>(null);
  const [orderDetails, setOrderDetails] = useState<Array<OrderDetailItem>>([]);
  const [refreshing, setRefreshing] = useState(false);
  const orderDA = new OrderProductDA();

  const fetchOrderData = useCallback(async () => {
    if (!orderId) {
      setLoading(false);
      return;
    }
    try {
      // Lấy thông tin đơn hàng
      const orderResponse = await orderDA.getOrderByOrderId(orderId);
      if (orderResponse?.code === 200 && orderResponse.data?.length > 0) {
        orderResponse.data[0].Address = orderResponse.Address.find(
          (item: any) => item.Id === orderResponse.data[0].AddressId,
        );

        console.log('Fetched order data:', orderResponse.data[0]);
        setOrder(orderResponse.data[0]);

        // Lấy chi tiết đơn hàng
        const detailsResponse = await orderDA.getOrderDetailsByOrderId(orderId);
        if (detailsResponse?.code === 200) {
          const listOrderDetailId = detailsResponse.data.map(
            (item: any) => item.Id,
          );
          let historyRewardResponse =
            await orderDA.getMoneyDetailsByListOrderDetailId(listOrderDetailId);
          detailsResponse.datahistoryRewardResponse = detailsResponse.data.map(
            (item: any) => {
              var product = detailsResponse.Product.find(
                (product: any) => product.Id === item.ProductId,
              );
              item.Img = product?.Img;
              item.Name = product?.Name;
              item.CategoryId = product?.CategoryId;
              item.Reward = historyRewardResponse?.data.find(
                (history: any) =>
                  history.OrderDetailId === item.Id && history.Filial === 0,
              )?.Value;
              return item;
            },
          );
          setOrderDetails(detailsResponse.data);
        }
      }
    } catch (error) {
      console.error('Error fetching order data:', error);
    } finally {
      setLoading(false);
    }
  }, [orderId, orderDA]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchOrderData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    fetchOrderData();
  }, []);

  return {
    loading,
    order,
    orderDetails,
    refreshing,
    fetchOrderData,
    handleRefresh,
  };
};
