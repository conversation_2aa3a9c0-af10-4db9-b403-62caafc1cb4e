import {useRef} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useSelectorCustomerState} from '../../../../../../redux/hooks/hooks';
import {useCriterionData} from './useCriterionData';
import {useToiletSelection} from './useToiletSelection';
import type {RouteParams, UseServicesWorkFlowReturn} from '../types';

export const useServicesWorkFlow = (): UseServicesWorkFlowReturn => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const user = useSelectorCustomerState().data;
  const popupRef = useRef<any>();

  const routeParams: RouteParams = route.params;
  const {type: serviceType} = routeParams;

  const {dataCriterion} = useCriterionData();

  const {tab, setTab, selected, setSelected, runningToilets, loadingToilet} =
    useToiletSelection(serviceType);

  return {
    tab,
    setTab,
    selected,
    setSelected,
    dataCriterion,
    popupRef,
    myToilet: runningToilets,
    loadingToilet,
    user,
    routeParams,
    navigation,
  };
};
