import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import {DataController} from '../../../screen/base-controller';
import {showSnackbar} from '../../../component/export-component';
import {ComponentStatus} from '../../../component/component-status';
import {getImage} from '../../actions/rootAction';
import {ServiceItem} from '../../../types/serviceType';

interface CateServiceSimpleResponse {
  data: Array<ServiceItem>;
  totalCount?: number;
  onLoading?: boolean;
}

const initState: CateServiceSimpleResponse = {
  data: [],
  totalCount: 0,
  onLoading: undefined,
};

export const CateServiceSlice = createSlice({
  name: 'services',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETALL':
          state.data = action.payload.data ?? [];
          state.totalCount = action.payload.totalCount;
          break;
        case 'GETLISTSIMPLE':
          state.data = action.payload.data;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
    onResetCategory: state => {
      state.data = [];
      state.onLoading = undefined;
    },
  },
});

const {handleActions, onFetching, onResetCategory} = CateServiceSlice.actions;

export default CateServiceSlice.reducer;
export {onResetCategory};

export class ServiceActions {
  static getCateServices = (dispatch: Dispatch<UnknownAction>) => {
    dispatch(onFetching());
    const controller = new DataController('CateServices');
    controller
      .aggregateList({
        page: 1,
        size: 1000,
        sortby: [{prop: 'Sort', direction: 'DESC'}],
      })
      .then(async res => {
        if (res.code === 200) {
          const data = await getImage({items: res.data});
          dispatch(
            handleActions({
              type: 'GETALL',
              data: data.sort(
                (a: ServiceItem, b: ServiceItem) => a.Sort - b.Sort,
              ),
              totalCount: res.data.length,
            }),
          );
        } else {
          showSnackbar({
            message: res?.message ?? 'Có lỗi xảy ra',
            status: ComponentStatus.ERROR,
          });
        }
      });
  };

  // static getListSimple = (dispatch: Dispatch<UnknownAction>, filter?: FilterListSimpleBody) => {
  //     dispatch(onFetching())
  //     NotificationController.getListSimple(filter).then(res => {
  //         if (res) {
  //             dispatch(handleActions({
  //                 type: 'GETLISTSIMPLE',
  //                 data: res.data,
  //                 totalCount: res.totalCount,
  //             }))
  //         }
  //     })
  // }

  // static add = async (dispatch: Dispatch<UnknownAction>, notificationItem: NotificationItem) => {
  //     dispatch(onFetching())
  //     const newId = await NotificationController.add(notificationItem)
  //     if (newId) {
  //         let newnotificationItem: NotificationItem = {
  //             ...notificationItem,
  //             id: newId
  //         }
  //         dispatch(handleActions({
  //             type: 'ADD',
  //             data: newnotificationItem,
  //         }))
  //         return newId
  //     }
  // }

  // static edit = async (dispatch: Dispatch<UnknownAction>, notificationItem: NotificationItem) => {
  //     dispatch(onFetching())
  //     const res = await NotificationController.edit([notificationItem])
  //     if (res) {
  //         dispatch(handleActions({
  //             type: 'EDIT',
  //             data: notificationItem,
  //         }))
  //         return res
  //     }
  // }

  // static delete = async (dispatch: Dispatch<UnknownAction>, ids: Array<string>) => {
  //     dispatch(onFetching())
  //     const res = await NotificationController.delete(ids)
  //     if (res) {
  //         dispatch(handleActions({
  //             type: 'DELETE',
  //             data: ids,
  //         }))
  //         return res
  //     }
  // }
}
