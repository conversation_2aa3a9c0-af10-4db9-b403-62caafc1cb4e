import {forwardRef, useEffect, useMemo, useRef, useState} from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import AppButton from '../../../component/button';
import {
  FCheckbox,
  showSnackbar,
  Winicon,
} from '../../../component/export-component';
import {closePopup, FPopup, showPopup} from '../../../component/popup/popup';
import WScreenFooter from '../../layout/footer';
import ScreenHeader from '../../layout/header';
import {TypoSkin} from '../../../assets/skin/typography';
import AllServices from '../service/services';
import {useDispatch} from 'react-redux';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../redux/hooks/hooks';
import {CustomerType} from '../../../redux/reducers/user/da';
import {CustomerActions} from '../../../redux/reducers/user/reducer';
import {ComponentStatus} from '../../../component/component-status';
import WebView from 'react-native-webview';
import {regexGetVariables} from '../../../utils/Utils';
import {DataController} from '../../base-controller';
import ListTile from '../../../component/list-tile/list-tile';
import {PopupCheckOtp} from '../../../project-component/popup-otp';
import FLoading from '../../../component/Loading/FLoading';

export const RegisterPartnerView = forwardRef(function RegisterPartnerView(
  data: {},
  ref: any,
) {
  const [selectedCate, setSelectedCate] = useState([]);
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
  const dispatch = useDispatch<any>();
  const [step, setStep] = useState(0);
  const [policyData, setPolicyData] = useState<any>();
  const now = new Date();
  const popupRef = useRef<any>();

  const [checked, setChecked] = useState(false);
  const [isLoading, setLoading] = useState(false);

  const htmlContent = useMemo(() => {
    if (!policyData || !user) return '';
    return policyData?.Content.replace(
      regexGetVariables,
      (m: any, key: any) => {
        switch (key) {
          case 'CateServicesTitle':
            return selectedCate
              .map((e: any) => e.Name)
              .join(', ')
              .toLowerCase();
          case 'CustomerName':
            return user.Name;
          case 'CustomerAddress':
            return user.Address;
          case 'CustomerTaxCode':
            return company?.TaxCode;
          case 'CustomerRepresentative':
            return company?.Representative ?? user.Name;
          case 'CustomerPosition':
            return company?.Position;
          case 'CustomerEmail':
            return company?.Email;
          case 'CustomerMobile':
            return company?.Mobile;
          case 'KTXRepresentative':
            return ktxgroup?.Representative ?? '';
          case 'KTXPosition':
            return ktxgroup?.Position ?? '';
          case 'day':
            return now.getDate();
          case 'month':
            return now.getMonth() + 1;
          case 'year':
            return now.getFullYear();
          default:
            return m;
        }
      },
    );
  }, [policyData, user, step]);

  useEffect(() => {
    if (step === 1 && user?.Type !== CustomerType.partner) {
      const policyController = new DataController('Policy');
      const url = '/partner';
      policyController
        .getListSimple({
          page: 1,
          size: 1,
          query: `@Url:(${url.length ? url : '\/'})`,
        })
        .then(res => {
          if (res.code === 200 && res.data.length) setPolicyData(res.data[0]);
        });
    }
  }, [step]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 40,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={step ? 'Kiểm tra hợp đồng' : `Lựa chọn dịch vụ`}
        prefix={<View />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      {step ? (
        <WebView
          originWhitelist={['*']}
          source={{html: htmlContent}}
          style={{
            flex: 1,
            width: '100%',
            paddingBottom: 100,
            paddingHorizontal: 4,
          }}
          nestedScrollEnabled={true}
          onLoad={() => console.log('WebView loaded')}
          renderLoading={() => <FLoading visible={true} />}
          onError={syntheticEvent => {
            const {nativeEvent} = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
          }}
        />
      ) : (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
          style={{
            height: '100%',
            width: '100%',
            paddingHorizontal: 16,
            flex: 1,
          }}>
          <ScrollView
            style={{backgroundColor: ColorThemes.light.transparent, flex: 1}}>
            <Pressable style={{}}>
              <Text
                style={[
                  TypoSkin.heading5,
                  {
                    color: ColorThemes.light.neutral_text_title_color,
                    paddingBottom: 4,
                  },
                ]}>
                Lựa chọn dịch vụ bạn cung cấp
              </Text>
              <Text
                style={[
                  TypoSkin.body3,
                  {
                    color: ColorThemes.light.neutral_text_body_color,
                    paddingBottom: 8,
                  },
                ]}>
                Với hàng nghìn chuyên gia trong lĩnh vực thiết kế, xây dựng nhà
                vệ sinh. Chúng tôi tự hào cung cấp các dịch vụ tốt nhất đến
                khách hàng. Hãy lựa chọn dịch vụ bạn có thể cung cấp, và trở
                thành đối tác của hệ thống KTX.
              </Text>
            </Pressable>
            <AllServices
              selectedCate={selectedCate}
              setSelectedCate={setSelectedCate}
            />
          </ScrollView>
        </KeyboardAvoidingView>
      )}
      <WScreenFooter style={{gap: 8, paddingHorizontal: 16}}>
        {step ? (
          <ListTile
            style={{padding: 0}}
            onPress={() => setChecked(!checked)}
            leading={
              <FCheckbox onChange={(v: any) => {}} size={20} value={checked} />
            }
            title="Tôi đã đọc và đồng ý với các điều khoản trên"
            titleStyle={[
              TypoSkin.body2,
              {color: ColorThemes.light.neutral_text_body_color},
            ]}
          />
        ) : null}
        <View style={{flexDirection: 'row', gap: 8}}>
          {checked ? null : step && user?.Type !== CustomerType.partner ? (
            <AppButton
              title={'Quay lại'}
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                height: 40,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={async () => {
                setStep(0);
              }}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
            />
          ) : null}
          <AppButton
            title={
              user?.Type === CustomerType.partner || (step && checked) || step
                ? 'Xác nhận'
                : 'Tiếp tục'
            }
            backgroundColor={ColorThemes.light.primary_main_color}
            disabled={
              user?.Type !== CustomerType.partner && step && !checked
                ? true
                : false
            }
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={async () => {
              if (user?.Type === CustomerType.partner) {
                await CustomerActions.edit(dispatch, {
                  ...user,
                  CateServicesId: selectedCate.map((e: any) => e.Id).join(','),
                });
                showSnackbar({
                  message: 'Cập nhật danh sách dịch vụ cung cấp thành công!',
                  status: ComponentStatus.SUCCSESS,
                });
                closePopup(ref);
              } else if (step && checked) {
                // check OTP
                if (!user?.Mobile) return;
                showPopup({
                  ref: popupRef,
                  children: (
                    <PopupCheckOtp
                      ref={popupRef}
                      phone={user?.Mobile}
                      onSuccess={async () => {
                        await CustomerActions.edit(dispatch, {
                          ...user,
                          Type: CustomerType.partner,
                          CateServicesId: selectedCate
                            .map((e: any) => e.Id)
                            .join(','),
                        });
                        setTimeout(() => {
                          closePopup(ref);
                        }, 300);
                      }}
                      isLoading={isLoading}
                      setLoading={setLoading}
                    />
                  ),
                });
              } else {
                setStep(1);
              }
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </View>
      </WScreenFooter>
    </SafeAreaView>
  );
});
