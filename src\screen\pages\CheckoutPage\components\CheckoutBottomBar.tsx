import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Ultis} from '../../../../utils/Utils';
import WScreenFooter from '../../../layout/footer';
import {CheckoutBottomBarProps} from '../types';

const CheckoutBottomBar: React.FC<CheckoutBottomBarProps> = ({
  totalPrice,
  isProcessing,
  isDone,
  onSubmitOrder,
  onNavigateHome,
  onNavigateOrders,
  onContinueShopping,
}) => {
  if (isDone) {
    return (
      <WScreenFooter style={styles.doneFooter}>
        <View style={styles.doneButtonsContainer}>
          <TouchableOpacity
            style={[styles.doneButton, styles.homeButton]}
            onPress={onNavigateHome}>
            <Text style={[styles.doneButtonText, styles.homeButtonText]}>
              Về trang chủ
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.doneButton, styles.ordersButton]}
            onPress={onNavigateOrders}>
            <Text style={[styles.doneButtonText, styles.ordersButtonText]}>
              Danh sách đơn
            </Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={[styles.doneButton, styles.continueShoppingButton]}
          onPress={onContinueShopping}>
          <Text
            style={[styles.doneButtonText, styles.continueShoppingButtonText]}>
            Tiếp tục mua hàng
          </Text>
        </TouchableOpacity>
      </WScreenFooter>
    );
  }

  return (
    <View style={styles.bottomBar}>
      <View style={styles.totalContainer}>
        <Text style={styles.totalLabel}>Tổng tiền:</Text>
        <Text style={styles.totalPrice}>{Ultis.money(totalPrice ?? 0)} đ</Text>
      </View>

      <TouchableOpacity
        style={[
          styles.placeOrderButton,
          isProcessing ? styles.placeOrderButtonDisabled : {},
        ]}
        onPress={onSubmitOrder}
        disabled={isProcessing}>
        <Text style={styles.placeOrderButtonText}>Đặt hàng</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.heading7,
    color: '#000000',
    marginRight: 8,
  },
  totalPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
  },
  placeOrderButton: {
    backgroundColor: '#FFC043',
    paddingHorizontal: 20,
    paddingVertical: 4,
    borderRadius: 40,
  },
  placeOrderButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  placeOrderButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
  doneFooter: {
    width: '100%',
    gap: 8,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  doneButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  doneButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 40,
    justifyContent: 'center',
  },
  homeButton: {
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    flex: 1,
  },
  ordersButton: {
    backgroundColor: '#FFC043',
    flex: 1,
  },
  continueShoppingButton: {
    flex: 1,
    width: '100%',
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  doneButtonText: {
    ...TypoSkin.heading7,
    textAlign: 'center',
  },
  homeButtonText: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  ordersButtonText: {
    color: '#FFFFFF',
  },
  continueShoppingButtonText: {
    color: '#FFFFFF',
    flex: 1,
  },
});

export default CheckoutBottomBar;
