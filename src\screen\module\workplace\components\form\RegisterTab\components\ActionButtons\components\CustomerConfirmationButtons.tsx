import React from 'react';
import {View, StyleSheet} from 'react-native';
import {popupReject} from '../../../../QuoteTable';
import {
  FDialog,
  showDialog,
} from '../../../../../../../../../component/export-component';
import {AppButton, ComponentStatus} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../../../../assets/skin/typography';

interface CustomerConfirmationButtonsProps {
  dialogRef: any;
  methods: any;
  onRejectSurvey: (rejectionReason: any) => void;
  onConfirmSurvey: () => void;
}

export const CustomerConfirmationButtons: React.FC<
  CustomerConfirmationButtonsProps
> = ({dialogRef, methods, onRejectSurvey, onConfirmSurvey}) => {
  return (
    <View style={styles.buttonRow}>
      <AppButton
        title={'Từ chối khảo sát'}
        backgroundColor={ColorThemes.light.neutral_main_background_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={() => {
          popupReject({
            ref: dialogRef,
            title: 'Bạn chắc chắn muốn từ chối xác nhận khảo sát này?',
            methods: methods,
            onSubmit: onRejectSurvey,
          });
        }}
        textColor={ColorThemes.light.neutral_text_subtitle_color}
      />
      <AppButton
        title={'Xác nhận khảo sát'}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={() => {
          showDialog({
            ref: dialogRef,
            title: 'Xác nhận hoàn thành khảo sát.',
            content:
              'Sau khi xác nhận đơn hàng sẽ tự động chuyển sang bước tư vấn báo giá.',
            onSubmit: onConfirmSurvey,
          });
        }}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
      <FDialog ref={dialogRef} />
    </View>
  );
};

const styles = StyleSheet.create({
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
    flex: 1,
  },
  button: {
    ...TypoSkin.subtitle4,
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingVertical: 5,
  },
});
