import React from 'react';
import {StyleSheet} from 'react-native';
import {TabBar} from 'react-native-tab-view';
import {ColorThemes} from '../../../../assets/skin/colors';
import {
  ToiletStatus,
  CateServicesType,
} from '../../../module/service/components/da';

interface ServiceData {
  CateServicesId?: string;
  [key: string]: any;
}

interface TabBarComponentProps {
  serviceData: ServiceData | null;
  getServicesData: () => void;
}

export default function TabBarComponent({
  serviceData,
  getServicesData,
}: TabBarComponentProps) {
  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      scrollEnabled
      activeColor={ColorThemes.light.primary_main_color}
      indicatorStyle={styles.indicator}
      onTabPress={ev => {
        if (
          parseInt(ev.route.key) === ToiletStatus.consultant &&
          serviceData?.CateServicesId !== CateServicesType.contact
        ) {
          getServicesData();
        }
      }}
      tabStyle={styles.tab}
      inactiveColor={ColorThemes.light.neutral_text_subtitle_color}
      style={styles.tabBar}
    />
  );

  return renderTabBar;
}

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: ColorThemes.light.primary_main_color,
    height: 1.5,
  },
  tab: {
    width: 'auto',
    paddingHorizontal: 4,
    paddingTop: 0,
  },
  tabBar: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: 45,
    elevation: 0.5,
  },
});
