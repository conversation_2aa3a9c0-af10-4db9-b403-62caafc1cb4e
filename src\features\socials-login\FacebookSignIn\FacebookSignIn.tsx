// import {
//   View,
//   Text,
//   Button,
//   Image,
//   Alert,
//   Platform,
//   ActivityIndicator,
// } from 'react-native';

// import { TouchableOpacity, StyleSheet } from 'react-native';
// import auth from '@react-native-firebase/auth';


// import {
//   Settings,
//   AccessToken,
//   AuthenticationToken,
//   LoginManager,
//   GraphRequest,
//   GraphRequestManager,
// } from 'react-native-fbsdk-next';
// import { ColorSkin } from '../../../assets/skin/colors';
// import { TypoSkin } from '../../../assets/skin/typography';
// import AppSvg from '../../../component/AppSvg';
// import { AppIcons } from '../../../component/AppSvg/AppIcons';


// // Setting the facebook app id using setAppID
// // Remember to set CFBundleURLSchemes in Info.plist on iOS if needed
// Settings.setAppID('1144257703431795');
// Settings.initializeSDK();

// interface Props {
//   onAuthSuccess: (value: any) => void;
//   onLoading: (value: boolean) => void;
//   isLoading: boolean;
// }

// const FacebookLogin = (props: Props) => {
//   const { onAuthSuccess, onLoading, isLoading } = props;

//   const getAccessToken = async () => {
//     //first run checking logout
//     AccessToken.getCurrentAccessToken().then(data => {
//       // console.log(data?.accessToken);
//       if (data?.accessToken) {
//         FBLogout(data?.accessToken?.toString());
//       }
//     });
//   };

//   const FBLogout = (accessToken: string) => {
//     let logout = new GraphRequest(
//       'me/permissions/',
//       {
//         accessToken: accessToken,
//         httpMethod: 'DELETE',
//       },
//       (error, result) => {
//         if (error) {
//         } else {
//           LoginManager.logOut();
//         }
//       },
//     );
//     new GraphRequestManager().addRequest(logout).start();
//   };

//   const signIn = async () => {
//     if (isLoading) {
//       return;
//     }
//     try {
//       onLoading(true);

//       getAccessToken();
//       LoginManager.logInWithPermissions(['public_profile', 'email']).then(
//         async function (result) {
//           if (result.isCancelled) {
//             console.log('Login cancelled');

//             onLoading(false);
//           } else {
//             await AccessToken.getCurrentAccessToken().then(data => {
//               onAuthSuccess(data?.accessToken?.toString());

//               // Create a Firebase credential with the AccessToken
//               const facebookCredential = auth.FacebookAuthProvider.credential(data!.accessToken);

//               // Sign-in the user with the credential
//               auth().signInWithCredential(facebookCredential);
//               onLoading(false);
//             });

//           }
//         },
//         function (error) {
//           console.error('Login fail with error: ' + error);
//           onLoading(false);
//         },
//       );
//     } catch (error) {
//       onLoading(false);

//       Alert.alert('Thông báo', `${error}`, [{ text: 'OK', onPress: () => { } }], {
//         cancelable: false,
//       });
//     }
//   };

//   return (
//     <TouchableOpacity onPress={signIn} style={styles.TouchStyle}>
//       {isLoading ? (
//         <View style={styles.TouchStyle}>
//           <View style={styles.loading}>
//             <ActivityIndicator size="large" />
//           </View>
//         </View>
//       ) : (
//         <View style={styles.TouchStyle}>
//           <AppSvg SvgSrc={AppIcons.logoFacebook} size={24} />
//           <Text
//             style={[
//               TypoSkin.buttonText1,
//               { color: ColorSkin.body, paddingLeft: 8 },
//             ]}>
//             Đăng nhập bằng Facebook
//           </Text>
//         </View>
//       )}
//     </TouchableOpacity>
//   );
// };
// const styles = StyleSheet.create({
//   loading: {
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   TouchStyle: {
//     flexDirection: 'row',
//     width: '100%',
//     height: 48,
//     borderRadius: 25,
//     borderWidth: 1,
//     borderColor: ColorSkin.border1,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
// });
// export default FacebookLogin;
