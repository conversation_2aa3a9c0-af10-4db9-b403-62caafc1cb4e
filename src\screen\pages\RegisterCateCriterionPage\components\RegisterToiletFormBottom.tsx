import {View, StyleSheet} from 'react-native';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

export default function RegisterToiletFormBottom({
  onConfirm,
  onCancel,
}: {
  onConfirm: () => void;
  onCancel: () => void;
}) {
  return (
    <View style={styles.container}>
      {/* Skip Button */}
      <AppButton
        title="Bỏ qua"
        textStyle={styles.skipButtonText}
        containerStyle={styles.skipButtonContainer}
        onPress={() => {
          onCancel();
        }}
      />

      {/* Continue Button */}
      <AppButton
        title="Tiếp tục"
        textStyle={styles.continueButtonText}
        containerStyle={styles.continueButtonContainer}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor={ColorThemes.light.primary_main_color}
        onPress={() => {
          onConfirm();
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  skipButtonText: {
    ...TypoSkin.buttonText1,
  },
  skipButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  continueButtonText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
  },
  continueButtonContainer: {
    height: 36,
    flex: 1,
    borderRadius: 40,
  },
});
