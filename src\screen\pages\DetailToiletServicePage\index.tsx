import React, {useState, useMemo} from 'react';
import {View, StyleSheet, useWindowDimensions} from 'react-native';
import {useRoute, RouteProp} from '@react-navigation/native';
import {TabView} from 'react-native-tab-view';
import {ColorThemes} from '../../../assets/skin/colors';
import FLoading from '../../../component/Loading/FLoading';
import RegisterTab from '../../module/workplace/components/form/RegisterTab';
import ConfigAPI from '../../../config/configApi';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../redux/hooks/hooks';
import {
  CateServicesType,
  ToiletStatus,
} from '../../module/service/components/da';
import EmptyPage from '../../../project-component/empty-page';
import {
  RegisterTabContent,
  ConsultantTabContent,
  ContractTabContent,
  DesignTabContent,
  BuildTabContent,
  LiquidTabContent,
} from './components/TabContent/components';

// Import refactored components and hooks
import {StatusWorkSpaceData} from './constants/statusData';
import useToiletService from './hooks/useToiletService';
import TabBarComponent from './components/TabBarComponent';
import TitleHeader from '../../layout/headers/TitleHeader';

// Type definitions
interface RouteParams {
  Id: string;
  Name?: string;
  Status?: string;
}

type DetailToiletServicePageRouteProp = RouteProp<
  {
    DetailToiletServicePage: RouteParams;
  },
  'DetailToiletServicePage'
>;

interface TabRoute {
  key: string;
  title: string;
}

export default function DetailToiletServicePage() {
  const route = useRoute<DetailToiletServicePageRouteProp>();
  const layout = useWindowDimensions();

  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;

  const [routes] = useState<TabRoute[]>(
    StatusWorkSpaceData.filter(item => item.key !== ToiletStatus.run).map(
      item => ({
        key: item.key.toString(),
        title: item.title,
      }),
    ),
  );

  const {
    workData,
    serviceData,
    setServiceData,
    index,
    setIndex,
    isRefreshing,
    isLoading,
    guest,
    methods,
    onRefresh,
    onChangeStatus,
    getServicesData,
    handleReject,
    handleSubmitRegister,
  } = useToiletService({
    toiletServiceId: route.params.Id,
    user,
  });

  // Create render functions with proper null checks and memoization
  const renderTabBar = useMemo(
    () =>
      TabBarComponent({
        serviceData,
        getServicesData,
      }),
    [serviceData, getServicesData],
  );

  const renderScene = useMemo(() => {
    return ({route}: any) => {
      if (!workData || !workData.length) return <View />;

      switch (route.key) {
        case `${ToiletStatus.register}`:
          return (
            <RegisterTabContent
              workData={workData}
              serviceData={serviceData}
              setServiceData={setServiceData}
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              methods={methods}
              guest={guest}
              handleReject={handleReject}
              handleSubmitRegister={handleSubmitRegister}
            />
          );
        case `${ToiletStatus.consultant}`:
          return (
            <ConsultantTabContent
              workData={workData}
              serviceData={serviceData}
              setServiceData={setServiceData}
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              methods={methods}
              guest={guest}
              onChangeStatus={onChangeStatus}
            />
          );
        case `${ToiletStatus.contract}`:
          return (
            <ContractTabContent
              workData={workData}
              serviceData={serviceData}
              setServiceData={setServiceData}
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              methods={methods}
              guest={guest}
              onChangeStatus={onChangeStatus}
            />
          );
        case `${ToiletStatus.design}`:
          return (
            <DesignTabContent
              workData={workData}
              serviceData={serviceData}
              setServiceData={setServiceData}
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              methods={methods}
              guest={guest}
              onChangeStatus={onChangeStatus}
            />
          );
        case `${ToiletStatus.build}`:
          return (
            <BuildTabContent
              workData={workData}
              serviceData={serviceData}
              setServiceData={setServiceData}
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              methods={methods}
              guest={guest}
              onChangeStatus={onChangeStatus}
            />
          );
        case `${ToiletStatus.liquid}`:
          return (
            <LiquidTabContent
              workData={workData}
              serviceData={serviceData}
              setServiceData={setServiceData}
              onRefresh={onRefresh}
              isRefreshing={isRefreshing}
              methods={methods}
              guest={guest}
              onChangeStatus={onChangeStatus}
            />
          );
        default:
          return <EmptyPage />;
      }
    };
  }, [
    workData,
    serviceData,
    setServiceData,
    onRefresh,
    isRefreshing,
    methods,
    guest,
    onChangeStatus,
    handleReject,
    handleSubmitRegister,
  ]);

  return (
    <View style={styles.container}>
      <FLoading visible={isLoading} />
      <TitleHeader title={route.params.Name ?? 'Chi tiết công việc'} />
      {company?.Id === ConfigAPI.ktxCompanyId ||
      serviceData?.CateServicesId === CateServicesType.contact ? (
        <RegisterTab
          data={workData}
          serviceData={serviceData}
          setServiceData={setServiceData}
          onRefreshing={onRefresh}
          isRefreshing={isRefreshing}
          methodParent={methods}
          customer={guest}
          onReject={handleReject}
          onSubmit={handleSubmitRegister}
        />
      ) : (
        <TabView
          navigationState={{index, routes}}
          renderScene={renderScene}
          swipeEnabled={false}
          renderTabBar={renderTabBar}
          onIndexChange={setIndex}
          initialLayout={{width: layout.width}}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});
