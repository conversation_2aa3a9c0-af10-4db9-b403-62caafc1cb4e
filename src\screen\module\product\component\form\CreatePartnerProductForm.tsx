/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {FPopup} from 'wini-mobile-components';
import {CreatePartnerProductFormStyles} from '../styles/CreatePartnerProductFormStyles';
import {Controller, useForm} from 'react-hook-form';
import DescriptionImage from './DescriptionImageForm';
import {ProductState} from '../types';
import ImageCropPicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import {showSnackbar} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import {BaseDA} from '../../../../baseDA';
import BottomSheetComponent, {BottomSheetRef} from './BottomSheetComponent';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import ProductFormFields from './ProductFormFields';
import ProductAllFields from './ProductAllFields';
import {
  createLoadNameFromIdWrapper,
  createPopulateFormWrapper,
} from '../../utils/ProductFormUtils';
import {store} from '../../../../../redux/store/store';
import productDA from '../../productDA';
import {useNavigation, useRoute} from '@react-navigation/native';
import {Snackbar} from 'react-native-paper';

const CreatePartnerProductForm = ({
  title,
  routeParams,
  dataEdit,
  dataCopy,
}: any) => {
  const popupbtsRef = useRef<any>(null);
  const bottomSheetRef = useRef<BottomSheetRef>(null);
  const [isFreeShip, setIsFreeShip] = useState(false);
  const [selectOption, setSelectOption] = useState('');
  const [selectType, setSelectType] = useState('');
  const [selectCate, setSelectCate] = useState('');
  const [selectCateattribute, setSelectCateattribute] = useState('');
  const [selectAttribute, setSelectAttribute] = useState('');
  const [selectSource, setselectSource] = useState('');
  const [selectBrand, setSelectBrand] = useState('');
  const [selectConsume, setSelectConsume] = useState('');
  const [selectColor, setSelectColor] = useState('');
  const [getInputValue, setGetInputValue] = useState<any>({});
  const [editAttributr, setEditAttributr] = useState<any>([]);
  const [ShopId, setShopId] = useState<any>('');
  const [currentBottomSheetValue, setCurrentBottomSheetValue] =
    useState<any>(null);
  const cusId = store.getState().customer.data?.Id;
  const partner = store.getState().partner.data;
  const navigation = useNavigation<any>();
  const route = useRoute();
  const [productState, setProductState] = useState<ProductState>({
    image: undefined,
    avataProduct: '',
    lengText: {
      ProductName: 0,
      DesProduct: 0,
      SpecificationsProduct: 0,
    },
  });
  const [showDateInput, setShowDateInput] = useState(false);
  const {
    control,
    handleSubmit,
    setValue,
    register,
    formState: {errors},
    watch,
    setError,
  } = useForm<any>({
    defaultValues: {
      Name: '',
      Description: '',
      Price: '',
      Detail: '',
      Source: '',
      Guarantee: '',
      Preserve: '',
      Specifications: '',
      Unit: '',
      IsPublic: false,
      Type: '',
      Sort: '',
      Code: '',
      IsHot: false,
      IsFreeShip: false,
      Discount: 0,
      CategoryId: '',
      AttributeId: '',
      CateAttributeId: '',
      InStock: '',
      ColorId: '',
      BrandsId: '',
      ConsumeId: '',
      ShopId: '',
      Status: 2,
      Img: '',
      Imgs: '',
    },
  });

  const openBottomSheet = (
    title: string,
    currentValue?: any,
    attributeData?: any,
  ) => {
    setSelectOption(title);

    // Lấy giá trị hiện tại từ form nếu không có currentValue
    let value = currentValue;
    if (!value) {
      if (title === 'phân loại') {
        value = watch('Type');
      } else if (title === 'danh mục sản phẩm') {
        value = watch('CategoryId');
      } else if (title === 'nhóm thuộc tính') {
        value = watch('CateAttributeId');
      }
    }

    setCurrentBottomSheetValue(value);
    bottomSheetRef.current?.show();
  };

  const handleSelectProductType = (item: any) => {
    console.log('check-selectOption', selectOption);
    // Update form value here
    if (selectOption == 'phân loại') {
      setValue('Type', Number(item.Id));
      setSelectType(item.Name);
    } else if (selectOption == 'danh mục sản phẩm') {
      setValue('CategoryId', item.Id);
      setSelectCate(item.Name);
    } else if (selectOption == 'nhóm thuộc tính') {
      // Map phần input, lọc lấy id thành mảng, chuyển sang string
      if (Array.isArray(item)) {
        // Trường hợp item là mảng (multiple selection)
        const idArray = item.map(selectedItem => selectedItem.Id);
        const idString = idArray.join(',');
        setValue('CateAttributeId', idString);

        // Set tên hiển thị (lấy tên của các item đã chọn)
        const nameArray = item.map(selectedItem => selectedItem.Name);
        setSelectCateattribute(nameArray.join(', '));
      } else {
        // Trường hợp item là object đơn lẻ
        setValue('CateAttributeId', item.Id);
        setSelectCateattribute(item.Name);
      }
    } else if (selectOption == 'thuộc tính') {
      setValue('AttributeId', item.Id);
      setSelectAttribute(item.Name);
    } else if (selectOption == 'nguồn gốc') {
      setValue('Source', Number(item.Id));
      setselectSource(item.Name);
    } else if (selectOption == 'thương hiệu') {
      setValue('BrandsId', item.Id);
      setSelectBrand(item.Name);
    } else if (selectOption == 'tiêu thụ') {
      setValue('ConsumeId', item.Id);
      setSelectConsume(item.Name);
    } else if (selectOption == 'màu sắc') {
      setValue('ColorId', item.Id);
      setSelectColor(item.Name);
    }
  };
  const getpartnerByCustomerId = async () => {
    if (!partner?.length) {
      const res = await productDA.getShopById(cusId as string);
      if (res.code === 200) {
        setShopId(res.data[0].Id);
      } else {
        showSnackbar({
          message: 'Không tìm thấy thông tin của shop',
          status: ComponentStatus.ERROR,
        });
      }
    }
  };

  useEffect(() => {
    getpartnerByCustomerId();
  }, [partner, cusId]);

  // Tạo wrapper function để sử dụng loadNameFromId từ utils
  const loadNameFromId = createLoadNameFromIdWrapper({
    setSelectType,
    setSelectCate,
    setSelectCateattribute,
    setSelectAttribute,
    setselectSource,
    setSelectColor,
    setSelectBrand,
    setSelectConsume,
  });

  // Tạo wrapper function để sử dụng populateFormWithEditData từ utils
  const populateFormWithEditData = createPopulateFormWrapper(
    setValue,
    setIsFreeShip,
    loadNameFromId,
    setProductState,
    {
      setSelectType,
      setSelectCate,
      setSelectCateattribute,
      setSelectAttribute,
      setselectSource,
      setSelectColor,
      setSelectBrand,
      setSelectConsume,
    },
  );

  const pickerImg = async () => {
    try {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: true,
        maxFiles: 5,
      });
      if (img.length > 5) {
        showSnackbar({
          message: 'Bạn chỉ được chọn tối đa 5 ảnh',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      // Check image size
      for (let i = 0; i < img.length; i++) {
        if (img[i].size > 100 * 1024 * 1024) {
          // 100 MB in bytes
          showSnackbar({
            message: 'Kích thước ảnh không được vượt quá 100MB',
            status: ComponentStatus.ERROR,
          });
          return;
        }
      }
      if (img) {
        // Store all image info, including path for display and potential future ID
        setProductState(prev => ({
          ...prev,
          image: img.map((image: ImageOrVideo) => ({...image, id: null})), // Temporarily set ID to null
          avataProduct: img[0].path,
        }));

        let arrayImg = [];
        for (let i = 0; i < img.length; i++) {
          arrayImg.push({
            uri: img[i].path,
            type: img[i].mime,
            name: img[i].filename ?? 'file',
          });
        }
        const resImgs = await BaseDA.uploadFiles(arrayImg);
        if (resImgs.length > 0) {
          // Update productState.image with actual IDs after upload
          setProductState(prev => ({
            ...prev,
            image: (prev.image || []).map((image, index) => {
              const uploadedImage = resImgs.find(
                (res: any) => res.FileName === image.filename,
              );
              return uploadedImage ? {...image, id: uploadedImage.Id} : image;
            }),
          }));
          setValue('image', resImgs[0].Id);
          setValue('listImage', resImgs.map((img: any) => img.Id).toString());
        }
      }
    } catch (error: any) {}
  };
  const deleteImage = (index: number) => {
    if (
      productState.image &&
      Array.isArray(productState.image) &&
      productState.image.length > index
    ) {
      const newImages = [...productState.image];
      newImages.splice(index, 1);
      const newImageIds = newImages.map((img: any) => img.id).filter(Boolean); // Extract IDs and filter out undefined/null
      setProductState(prev => ({
        ...prev,
        image: newImages,
        avataProduct: newImages.length > 0 ? newImages[0].path : '',
      }));
      // Update form values
      setValue('image', newImageIds.length > 0 ? newImageIds[0] : null); // Set to first image ID or null
      setValue('listImage', newImageIds.join(','));
    }
  };

  useEffect(() => {
    if (isFreeShip) {
      setValue('IsFreeShip', true);
    } else {
      setValue('IsFreeShip', false);
    }
  }, [isFreeShip]);

  useEffect(() => {
    populateFormWithEditData(dataEdit);
  }, [dataEdit]);

  useEffect(() => {
    populateFormWithEditData(dataCopy);
  }, [dataCopy]);

  const CovertDate = (date: any): number => {
    // Nếu không có dữ liệu, trả về 0
    if (!date) return 0;

    // Nếu đã là số (timestamp), trả về luôn
    if (typeof date === 'number') return date;

    // Nếu là chuỗi, xử lý chuyển đổi
    if (typeof date === 'string') {
      // Kiểm tra nếu là chuỗi số (timestamp dạng string)
      if (/^\d+$/.test(date)) {
        return parseInt(date);
      }

      // Xử lý định dạng dd/mm/yyyy
      if (date.includes('/')) {
        const [day, month, year] = date.split('/');
        if (day && month && year) {
          const getDate = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
          );
          return getDate.getTime();
        }
      }
    }

    // Nếu là Date object
    if (date instanceof Date) {
      return date.getTime();
    }

    // Trường hợp khác, thử parse trực tiếp
    try {
      const parsedDate = new Date(date);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.getTime();
      }
    } catch (error) {
      console.warn('Cannot convert date:', date, error);
    }

    // Trả về 0 nếu không thể chuyển đổi
    return 0;
  };

  const onSubmit = async (data: any) => {
    console.log('dsfsfs', data);
    let dataCreate = {
      Id: dataEdit?.Id || randomGID(), // Sử dụng ID từ dataEdit nếu có, nếu không tạo mới
      CustomerId: cusId,
      DateCreated: dataEdit?.DateCreated || new Date().getTime(), // Giữ nguyên DateCreated nếu edit, tạo mới nếu create
      Name: data.Name,
      Description: data.Description,
      Price: Number(data.Price),
      Detail: data.Detail,
      Source: data.Source,
      Guarantee: CovertDate(data.Guarantee),
      Preserve: CovertDate(data.Preserve),
      Specifications: data.Specifications,
      Unit: data.Unit,
      Img: data.image,
      Imgs: data.listImage,
      Discount: 0,
      Type: Number(data.Type),
      Code: data.Code,
      IsFreeShip: data.IsFreeShip,
      CategoryId: data.CategoryId,
      AttributeId: data.AttributeId,
      CateAttributeId: data.CateAttributeId,
      InStock: data.InStock ? Number(data.InStock) : 0,
      ColorId: data.ColorId,
      BrandsId: data.BrandsId,
      ConsumeId: data.ConsumeId,
      Status: dataEdit?.Status || 2, // Giữ nguyên Status nếu edit, mặc định 2 nếu create
      ShopId: partner && partner.length > 0 ? partner[0].Id : ShopId,
    };
    console.log('check-dsfsdf', dataCreate);
    let res;
    // Kiểm tra điều kiện: nếu là edit hoặc copy thì dùng hàm tương ứng
    if (dataEdit?.Id) {
      // Trường hợp edit sản phẩm - sử dụng EditProductPartner

      // Đảm bảo dataCreate có đủ thông tin để update
      const updateData = {
        ...dataCreate,
        Id: dataEdit.Id, // Đảm bảo ID đúng
      };
      res = await productDA.EditProductPartner(updateData);
    } else if (dataCopy?.Id) {
      // Trường hợp copy sản phẩm - tạo mới với ID mới và reset một số field
      const dataCopyCreate = {
        ...dataCreate,
        Id: randomGID(), // Tạo ID mới cho bản copy
        Code: data.Code || '', // Có thể để trống hoặc tự động generate
        Status: 2, // Set status mặc định cho bản copy
        DateCreated: new Date().getTime(), // Thời gian tạo mới
      };
      res = await productDA.CreateProductPartner(dataCopyCreate);
    } else {
      // Trường hợp tạo mới sản phẩm
      res = await productDA.CreateProductPartner(dataCreate);
    }
    if (res.code === 200) {
      // Hiển thị thông báo phù hợp với từng trường hợp
      let successMessage = 'Tạo sản phẩm thành công';
      if (title === 'Chỉnh sửa sản phẩm' && dataEdit) {
        let productId = res.data[0]?.Id;
        const result =
          getInputValue && typeof getInputValue === 'object'
            ? Object.entries(getInputValue)
                .filter(([, value]) => value && value.toString().trim() !== '')
                .map(([key, value]: [string, any]) => ({
                  ProductId: productId,
                  CateAttributeId: key,
                  Value: value.toString().trim(),
                  Id: randomGID(),
                  DateCreated: new Date().getTime(),
                }))
            : [];
        if (result.length > 0) {
          await productDA.CreateAttributeProduct(result);
        }
        successMessage = 'Cập nhật sản phẩm thành công';
      } else if (title === 'Sao chép sản phẩm' || dataCopy) {
        let productId = res.data[0]?.Id;
        const result =
          getInputValue && typeof getInputValue === 'object'
            ? Object.entries(getInputValue)
                .filter(([, value]) => value && value.toString().trim() !== '')
                .map(([key, value]: [string, any]) => ({
                  ProductId: productId,
                  CateAttributeId: key,
                  Value: value.toString().trim(),
                  Id: randomGID(),
                  DateCreated: new Date().getTime(),
                }))
            : [];
        if (result.length > 0) {
          await productDA.CreateAttributeProduct(result);
        }
        successMessage = 'Sao chép sản phẩm thành công';
      }

      showSnackbar({
        message: successMessage,
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    }
    navigation.goBack();
  };
  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <FPopup ref={popupbtsRef} />
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={{flexGrow: 1, paddingBottom: 100}}
          showsVerticalScrollIndicator={false}>
          <View>
            <Controller
              control={control}
              name="image"
              rules={{
                required: 'Vui lòng chọn ảnh',
              }}
              render={({field: {value, onChange}}) => (
                <DescriptionImage
                  deleteImage={deleteImage}
                  checkImage={productState.image}
                  image={productState.image || []}
                  pickerImg={pickerImg}
                  avataProduct={productState.avataProduct}
                  imageListEdit={productState.image as ImageOrVideo[]}
                />
              )}
            />
            {errors.image &&
              (!watch('image') ||
                (Array.isArray(watch('image')) &&
                  watch('image').length === 0)) && (
                <Text style={CreatePartnerProductFormStyles.error}>
                  {(errors.image?.message as string) ||
                    'Vui lòng chọn ảnh sản phẩm'}
                </Text>
              )}
          </View>
          <ProductFormFields
            control={control}
            errors={errors}
            watch={watch}
            register={register}
            selectType={selectType}
            selectCate={selectCate}
            selectCateattribute={selectCateattribute}
            selectAttribute={selectAttribute}
            openBottomSheet={openBottomSheet}
            styles={styles}
          />

          <ProductAllFields
            control={control}
            errors={errors}
            watch={watch}
            register={register}
            selectType={selectType}
            selectCate={selectCate}
            selectCateattribute={selectCateattribute}
            selectAttribute={selectAttribute}
            selectSource={selectSource}
            selectColor={selectColor}
            selectBrand={selectBrand}
            selectConsume={selectConsume}
            isFreeShip={isFreeShip}
            setIsFreeShip={setIsFreeShip}
            showDateInput={showDateInput}
            setShowDateInput={setShowDateInput}
            openBottomSheet={openBottomSheet}
            styles={styles}
          />
        </ScrollView>

        <TouchableOpacity
          style={styles.ButtonWrapper}
          onPress={() => {
            handleSubmit(onSubmit)();
          }}>
          <Text style={styles.ButtonText}>
            {title == 'Chỉnh sửa sản phẩm' ? 'Chỉnh sửa' : 'Tạo mới'}
          </Text>
        </TouchableOpacity>

        {/* BottomSheet Component */}
        <BottomSheetComponent
          ref={bottomSheetRef}
          title={selectOption}
          onSelect={handleSelectProductType}
          value={currentBottomSheetValue}
          setGetInputValue={setGetInputValue}
          setEditAttributr={setEditAttributr}
        />
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 10,
  },
  ButtonWrapper: {
    backgroundColor: ColorThemes.light.primary_darker_color,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 20,
    position: 'absolute',
    bottom: 0,
    left: 16,
    right: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  ButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  option: {
    marginTop: 12,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 5,
    // elevation: 2, // Tạo bóng cho Android
    // shadowColor: ColorThemes.light.neutral_text_subtitle_color, // Tạo bóng cho iOS
    // shadowOffset: {width: 0, height: 1},
    // shadowOpacity: 0.1,
    // shadowRadius: 2,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  freeShip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
});

export default CreatePartnerProductForm;
