import React from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {AppButton, Winicon} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';

interface NoCompanyViewProps {
  onRegister: () => void;
}

export const NoCompanyView: React.FC<NoCompanyViewProps> = ({onRegister}) => {
  return (
    <View style={styles.container}>
      <Pressable style={styles.content}>
        <Winicon
          src="outline/business/building-office"
          size={64}
          color={ColorThemes.light.neutral_text_subtitle_color}
        />
        <Text style={styles.title}>Chưa có doanh nghiệp</Text>
        <Text style={styles.subtitle}>
          Bạn chưa có thông tin doanh nghiệp. Vui lòng đăng ký để trở thành
          doanh nghiệp.
        </Text>
      </Pressable>
      <AppButton
        title="Đăng ký doanh nghiệp"
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={styles.registerButton}
        onPress={onRegister}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 32,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  registerButton: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
});
