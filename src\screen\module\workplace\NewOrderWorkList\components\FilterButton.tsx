import React from 'react';
import {StyleSheet} from 'react-native';
import AppButton from '../../../../../component/button';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';

interface FilterButtonProps {
  onPress: () => void;
  hasActiveFilters: boolean;
  title?: string;
}

export const FilterButton: React.FC<FilterButtonProps> = ({
  onPress,
  hasActiveFilters,
  title = 'Bộ lọc',
}) => {
  return (
    <AppButton
      onPress={onPress}
      backgroundColor={ColorThemes.light.transparent}
      borderColor={
        hasActiveFilters
          ? ColorThemes.light.primary_main_color
          : ColorThemes.light.neutral_main_border_color
      }
      containerStyle={styles.container}
      prefixIconSize={18}
      prefixIcon={'outline/user interface/setup-preferences'}
      textColor={
        hasActiveFilters
          ? ColorThemes.light.primary_main_color
          : ColorThemes.light.neutral_text_subtitle_color
      }
      title={title}
      textStyle={{
        ...TypoSkin.subtitle3,
        color: hasActiveFilters
          ? ColorThemes.light.primary_main_color
          : ColorThemes.light.neutral_text_subtitle_color,
      }}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 8,
  },
});
