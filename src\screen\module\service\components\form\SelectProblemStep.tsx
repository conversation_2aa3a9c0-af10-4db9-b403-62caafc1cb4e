import React, { forwardRef, useEffect, useRef, useState } from "react";
import { Di<PERSON>sions, FlatList, KeyboardAvoidingView, Platform, Pressable, SafeAreaView, Text, TouchableOpacity, View } from "react-native";
import { closePopup, FPopup, showPopup } from "../../../../../component/popup/popup";
import { DataController } from "../../../../base-controller";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { ColorThemes } from "../../../../../assets/skin/colors";
import { TypoSkin } from "../../../../../assets/skin/typography";
import { CardHoriSkeleton } from "../../../../../project-component/skeletonCard";
import AppButton from "../../../../../component/button";
import { Winicon, FCheckbox } from "../../../../../component/export-component";
import ListTile from "../../../../../component/list-tile/list-tile";
import ScreenHeader from "../../../../layout/header";
import { TextFieldForm } from "../../../../../project-component/component-form";
import WScreenFooter from "../../../../layout/footer";

interface Props {
    methods?: any
    forConsultant?: boolean;
    display?: string;
    category?: Array<any>;
    upgrade?: boolean
}

const height = Dimensions.get('screen').height;

export default function SelectedProblemStep(props: Props) {
    const { methods, forConsultant = false, display = "flex", upgrade = false, category = [] } = props;
    const popupRef = useRef<any>()
    const [selectedCate, setSelectedCate] = useState()

    useEffect(() => {
        if (category.length) setSelectedCate(category[0]?.Id)
    }, [category.length])

    useEffect(() => {
        if (selectedCate && !methods.getValues(selectedCate)?.length) {
            const cateController = new DataController("Category")
            cateController.aggregateList({ page: 1, size: 20, searchRaw: `@ParentId:{${selectedCate}}` }).then(res => {
                if (res.code === 200) methods.setValue(selectedCate, { data: res.data, totalCount: res.totalCount })
            })
        }
    }, [selectedCate])

    const showPopupSelectProblem = (item?: any) => {
        showPopup({
            ref: popupRef,
            enableDismiss: true,
            children: <PopupSelectProblem ref={popupRef} item={item} methods={methods} selectedCate={selectedCate} />
        })
    }

    const returnServiceList = () => {
        const countOtherDevice = methods.watch("FAQ")?.some((e: any) => !e.Id && e.CategoryId === selectedCate)
        return (
            <View style={{}}>
                <FlatList
                    horizontal={true}
                    data={category}
                    showsHorizontalScrollIndicator={false}
                    style={{ height: 35, marginVertical: 6 }}
                    keyExtractor={(item, index) => `${item.Id}`}
                    ItemSeparatorComponent={() => <View style={{ width: 8 }} />}
                    renderItem={({ item, index }: { item: any; index: number }) => {
                        const childrenId = (methods.watch(item.Id)?.data ?? []).map((e: any) => e.Id)
                        return (
                            <TouchableOpacity
                                key={`cate${index}`}
                                onPress={() => {
                                    setSelectedCate(item.Id);
                                }}
                                style={{
                                    borderRadius: 20,
                                    paddingVertical: 4,
                                    paddingHorizontal: 8,
                                    backgroundColor:
                                        selectedCate === item.Id
                                            ? ColorThemes.light
                                                .neutral_absolute_reverse_background_color
                                            : ColorThemes.light.neutral_absolute_background_color,
                                    borderColor: ColorThemes.light.neutral_main_border_color,
                                }}>
                                <Text
                                    style={[
                                        TypoSkin.regular2,
                                        {
                                            color:
                                                selectedCate === item.Id
                                                    ? ColorThemes.light.neutral_absolute_background_color
                                                    : ColorThemes.light
                                                        .neutral_absolute_reverse_background_color,
                                        },
                                    ]}>
                                    {item?.Name ?? ''}
                                </Text>
                            </TouchableOpacity>
                        );
                    }}
                    onEndReachedThreshold={0.1}
                    ListEmptyComponent={() => [0, 1, 2, 3].map((_, index) => <SkeletonPlaceholder key={index}>
                        <SkeletonPlaceholder.Item height={32} width={56} borderRadius={16} marginRight={8} />
                    </SkeletonPlaceholder>)}
                />
                <FlatList
                    data={methods
                        .watch(selectedCate)
                        ?.data}
                    style={{ height: "100%" }}
                    keyExtractor={(item, index) => `${item.Id}`}
                    renderItem={({ item, index }: { item: any; index: number }) => {
                        const count = methods.watch("FAQ")?.filter((e: any) => e.CategoryId?.includes(item.Id))?.length ?? 0
                        return <CateTile
                            key={item.Id}
                            item={item}
                            countDevice={count}
                            onPress={() => showPopupSelectProblem(item)}
                        />
                    }}
                    ListFooterComponent={() => {
                        return <CateTile
                            key={"countOtherDevice"}
                            countDevice={countOtherDevice ? 1 : undefined}
                            onPress={() => showPopupSelectProblem()}
                        />
                    }}
                    onEndReachedThreshold={0.1}
                    ListEmptyComponent={() => [0, 1, 2, 3].map((_, index) => <CardHoriSkeleton key={index} />)}
                />

            </View>
        );
    };

    return <View style={{ flex: 1, padding: 16, display: display === "flex" ? 'flex' : 'none', paddingBottom: 168 }}>
        <FPopup ref={popupRef} />
        {forConsultant ? returnServiceList() :
            <View style={{
                flex: 1,
                height: "100%", paddingBottom: 75
            }}>
                <Pressable
                    style={{
                        borderColor: 'transparent',
                        borderBottomColor: ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                    }}>
                    <Text
                        style={[
                            TypoSkin.heading5,
                            {
                                color: ColorThemes.light.neutral_text_title_color,
                                paddingBottom: 4,
                            },
                        ]}>
                        Nhà vệ sinh của bạn gặp vấn đề gì
                    </Text>
                    <Text
                        style={[
                            TypoSkin.body3,
                            {
                                color: ColorThemes.light.neutral_text_body_color,
                                paddingBottom: 8,
                            },
                        ]}>
                        Bạn có thể chọn một hoặc nhiều
                    </Text>
                </Pressable>
                {returnServiceList()}
            </View>
        }
    </View>
}


const CateTile = ({
    item, countDevice, onPress
}: {
    item?: any;
    countDevice?: number;
    onPress?: () => void
}) => {

    return (
        <ListTile
            key={item ? item.Id : "countOtherDevice"}
            onPress={onPress}
            leading={
                <Winicon
                    src={'outline/files/backup'}
                    style={{ width: 32, height: 32 }}
                    color={countDevice && countDevice > 0 ? ColorThemes.light.warning_main_color : undefined}
                />
            }
            title={item ? item.Name : "Thiết bị khác"}
            titleStyle={[
                TypoSkin.heading7,
                { color: ColorThemes.light.neutral_text_title_color },
            ]}
            subtitle={
                countDevice
                    ? `${countDevice} vấn đề`
                    : 'Chọn vấn đề'
            }
            subTitleStyle={[
                TypoSkin.subtitle4,
                { color: ColorThemes.light.neutral_text_subtitle_color },
            ]}
            listtileStyle={{ gap: 16 }}
            style={{
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderBottomWidth: 1,
            }}
        />
    );
};

const PopupSelectProblem = forwardRef(function PopupSelectProduct(data: { item: any, methods: any, selectedCate: any }, ref: any) {
    const { item, methods, selectedCate } = data

    const [faq, setFAQ] = useState([])
    const [showDescription, setShowDescription] = useState(false)
    const [selected, setSelected] = useState(false)

    useEffect(() => {
        if (item) {
            const faqController = new DataController("FAQ")
            faqController.getListSimple({ page: 1, size: 1000, query: `@CategoryId:{*${item.Id}*} @Type:[5 5]` }).then(async (res) => {
                if (res.code === 200 && res.data.length) setFAQ(res.data)
            })
            methods.setValue("TextAreaDes", methods.watch("FAQ")?.find((e: any) => !e.Id && e.CategoryId === item.Id)?.Content ?? "")
        }
        if (!item || methods.watch("FAQ")?.some((e: any) => !e.Id && e.CategoryId === item.Id)) setShowDescription(true)

    }, [item])

    return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height - 65, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: '#fff' }}>
        <ScreenHeader
            style={{
                backgroundColor: ColorThemes.light.transparent,
                flexDirection: 'row',
                paddingTop: 8
            }}
            title={`Chọn vấn đề bạn gặp phải`}
            prefix={<View />}
            action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
            </View>}
        />
        <KeyboardAvoidingView behavior={'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 35 : 0} style={{ flex: 1, height: "100%" }}>
            <FlatList
                data={faq}
                style={{ height: "100%", marginVertical: 6 }}
                keyExtractor={(item, index) => `${item.Id}`}
                renderItem={({ item, index }: { item: any; index: number }) => {
                    return <ListTile
                        key={`${item.Id}`}
                        leading={
                            <Winicon
                                src={'outline/layout/circle-warning'}
                                style={{ width: 32, height: 32 }}
                            />
                        }
                        title={item.Name ?? ''}
                        titleStyle={[
                            TypoSkin.heading7,
                            { color: ColorThemes.light.neutral_text_title_color, paddingBottom: 8 },
                        ]}
                        subtitle={item.Content ?? ''}
                        listtileStyle={{ gap: 16, }}
                        style={{
                            borderBottomColor: ColorThemes.light.neutral_main_border_color,
                            borderBottomWidth: 1,
                        }}
                        onPress={() => {
                            setSelected(!selected)
                            if (methods.watch("FAQ")?.some((e: any) => e.Id === item.Id)) {
                                methods.setValue("FAQ", methods.getValues("FAQ").filter((e: any) => e.Id !== item.Id))
                            }
                            else {
                                methods.setValue("FAQ", [...(methods.getValues("FAQ") ?? []), item])
                            }
                        }}
                        trailing={
                            <FCheckbox
                                onChange={(v) => {
                                    if (v) {
                                        methods.setValue("FAQ", [...(methods.getValues("FAQ") ?? []), item])
                                    }
                                    else {
                                        methods.setValue("FAQ", methods.getValues("FAQ").filter((e: any) => e.Id !== item.Id))
                                    }
                                }}
                                size={20}
                                value={methods.watch("FAQ")?.some((e: any) => e.Id === item.Id && e.Name === item.Name) ?? false}
                            />
                        }
                    />
                }}
                ListFooterComponent={() => {
                    return <View>
                        {item ? <ListTile
                            title={"Vấn đề khác"}
                            leading={
                                <Winicon
                                    src={'outline/layout/circle-warning'}
                                    style={{ width: 32, height: 32 }}
                                />
                            }
                            titleStyle={[
                                TypoSkin.heading7,
                                { color: ColorThemes.light.neutral_text_title_color },
                            ]}
                            listtileStyle={{ gap: 16, }}
                            style={{
                                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                                borderBottomWidth: 1,
                            }}
                            onPress={() => {
                                setShowDescription(!showDescription)
                                if (methods.watch("FAQ")?.some((e: any) => !e.Id && e.CategoryId === item.Id)) {
                                    methods.setValue("FAQ", methods.getValues("FAQ").filter((e: any) => e.Id || e.CategoryId !== item.Id))

                                } else {
                                    methods.setValue("FAQ", [...(methods.getValues("FAQ") ?? []), { Name: `Vấn đề khác`, Content: "", CategoryId: item.Id }])
                                    methods.setValue("TextAreaDes", methods.watch("FAQ")?.find((e: any) => !e.Id && e.CategoryId === item.Id)?.Content ?? "")
                                }

                            }}
                            trailing={
                                <FCheckbox
                                    onChange={(v) => {
                                        if (v) {
                                            setShowDescription(true)
                                            methods.setValue("FAQ", [...(methods.getValues("FAQ") ?? []), { Name: `Vấn đề khác`, Content: "", CategoryId: item.Id }])
                                            methods.setValue("TextAreaDes", methods.watch("FAQ")?.find((e: any) => !e.Id && e.CategoryId === item.Id)?.Content ?? "")
                                        } else {
                                            methods.setValue("FAQ", methods.getValues("FAQ").filter((e: any) => e.Id || e.CategoryId !== item.Id))
                                            setShowDescription(false)
                                        }
                                    }}
                                    size={20}
                                    value={methods.watch("FAQ")?.some((e: any) => !e.Id && e.CategoryId === item.Id) ?? false}
                                />
                            }
                        /> : <View />}
                        {/* description */}
                        {showDescription ? <View style={{ marginHorizontal: 16, marginTop: 16 }}>
                            <TextFieldForm
                                required
                                control={methods.control}
                                name="TextAreaDes"
                                errors={methods.formState.errors}
                                placeholder={'Giúp chúng tôi hiểu rõ hơn vấn đề của bạn'}
                                style={{ backgroundColor: ColorThemes.light.transparent }}
                                textFieldStyle={{
                                    height: 100,
                                    width: "100%",
                                    paddingHorizontal: 16,
                                    paddingTop: 16,
                                    paddingBottom: 16,
                                    justifyContent: "flex-start",
                                    backgroundColor: ColorThemes.light.transparent,
                                }}
                                textStyle={{ textAlignVertical: 'top' }}
                                numberOfLines={10}
                                multiline={true}
                                register={methods.register}
                                onBlur={(ev) => {
                                    if (methods.getValues("FAQ")?.some((e: any) => !e.Id && e.CategoryId === (item?.Id ?? selectedCate))) {
                                        methods.setValue("FAQ", methods.getValues("FAQ").map((e: any) => (!e.Id && e.CategoryId === (item?.Id ?? selectedCate)) ? { ...e, Content: ev.trim() } : e))
                                    } else {
                                        methods.setValue("FAQ", [
                                            ...(methods.getValues("FAQ") ?? []),
                                            { Name: `Vấn đề thiết bị khác`, Content: ev.trim(), CategoryId: item?.Id ?? selectedCate }
                                        ])
                                    }
                                }}
                            />
                        </View> : <View />}
                    </View>
                }}
            />

            <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16, paddingBottom: 16 }}>
                <AppButton
                    title={'Xong'}
                    backgroundColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                    onPress={() => {
                        closePopup(ref)
                    }}
                    textColor={ColorThemes.light.neutral_absolute_background_color}
                />
            </WScreenFooter>
        </KeyboardAvoidingView>
    </SafeAreaView>
});