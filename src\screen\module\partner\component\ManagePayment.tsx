import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {hideBottomSheet, ListTile} from 'wini-mobile-components';
import {Radio, RadioAction} from '../../../../component/Field/Radio';
import TitleHeader from '../../../layout/headers/TitleHeader';

const ManagePayment: React.FC = () => {
  const paymentData = [
    {id: 1, name: 'Ship COD'},
    {id: 2, name: 'VNPay'},
  ];
  return (
    <View
      style={{
        height: 200,
        width: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <TitleHeader title={'QL phương thức thanh toán'} />
      {paymentData.map((item, index) => {
        return (
          <ListTile
            key={index}
            title={item.name}
            trailing={
              <TouchableOpacity>
                <Radio />
              </TouchableOpacity>
            }
          />
        );
      })}
    </View>
  );
};

export default ManagePayment;
