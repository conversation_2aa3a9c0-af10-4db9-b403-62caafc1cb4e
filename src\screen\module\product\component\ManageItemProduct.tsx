import {useFocusEffect, useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import MenuProduct from './MenuProduct';
import {RootScreen} from '../../../../router/router';
import {ColorThemes} from '../../../../assets/skin/colors';
import ManageProductDetail from './list/ListProductDetail';
import productDa from '../productDA';
import {store} from '../../../../redux/store/store';
import productDA from '../productDA';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'wini-mobile-components';
import {CustomerRole} from 'redux/reducers/user/da';
import {useSelectorCustomerState} from 'redux/hooks/hooks';

const ManageItemProduct = () => {
  const navigation = useNavigation<any>();
  const [menu, setMenu] = useState<string>('Còn hàng');
  const [productInfo, setProductInfo] = useState<any[]>([]);
  const [shopId, setShopId] = useState('');
  const partner = store.getState().partner.data;
  const cusId = store.getState().customer.data?.Id;
  const userRole = useSelectorCustomerState().role;

  const partnerId = partner && partner.length > 0 ? partner[0].Id : shopId;
  useEffect(() => {
    if (partnerId) {
      getAllProductByShopId(partnerId);
    }
  }, [partnerId]);
  const getpartnerByCustomerId = async () => {
    if (!partner?.length) {
      const res = await productDA.getShopById(cusId as string);
      if (res.code === 200) {
        setShopId(res.data[0].Id);
      } else {
        showSnackbar({
          message: 'Không tìm thấy thông tin của shop',
          status: ComponentStatus.ERROR,
        });
      }
    }
  };

  const getAllProductByShopId = async (partnerId: string) => {
    if (partnerId && partnerId.length > 0) {
      const res = await productDa.getProductsByShopId(partnerId);
      if (res && res.length > 0) {
        // Lọc sản phẩm theo trạng thái từ 0 đến 4
        const filteredProducts = [
          {
            name: 'Hoạt động',
            number: res.filter((product: any) => product.Status === 2).length,
            data: res.filter((product: any) => product.Status === 2),
          },
          {
            name: 'Hết hàng',
            number: res.filter((product: any) => product.Status === 0).length,
            data: res.filter((product: any) => product.Status === 0),
          },
          {
            name: 'Chờ duyệt',
            number: res.filter((product: any) => product.Status === 3).length,
            data: res.filter((product: any) => product.Status === 3),
          },
          {
            name: 'Vi phạm',
            number: res.filter((product: any) => product.Status === 4).length,
            data: res.filter((product: any) => product.Status === 4),
          },
          {
            name: 'Ẩn',
            number: res.filter((product: any) => product.Status === 1).length,
            data: res.filter((product: any) => product.Status === 1),
          },
        ];
        setProductInfo(filteredProducts);
      }
    }
  };

  useEffect(() => {
    getpartnerByCustomerId();
  }, [cusId]);

  useFocusEffect(
    useCallback(() => {
      getAllProductByShopId((partnerId as string) ?? shopId);
    }, [partnerId]),
  );
  return (
    <View
      style={{flex: 1, margin: 10, position: 'relative', paddingBottom: 100}}>
      <MenuProduct setMenu={setMenu} menu={menu} data={productInfo} />
      <ManageProductDetail
        menu={menu}
        dataShop={productInfo}
        partnerId={(partnerId as string) ?? shopId}
        getAllProductByShopId={getAllProductByShopId}
      />
      {userRole?.Role?.includes(
        CustomerRole.Owner || CustomerRole.Coordinator,
      ) ? (
        <TouchableOpacity
          style={{
            backgroundColor: ColorThemes.light.primary_darker_color,
            width: '80%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 50,
            borderRadius: 30,
            margin: 'auto',
            marginBottom: 20,
            position: 'absolute',
            bottom: 0,
            left: '10%',
          }}
          onPress={() =>
            navigation.push(RootScreen.CreateProductPartnerPage, {
              title: 'Tạo sản phẩm mới',
            })
          }>
          <Text
            style={{
              color: ColorThemes.light.neutral_text_stable_color,
              fontSize: 16,
              fontWeight: '500',
              textAlign: 'center',
            }}>
            Thêm sản phẩm mới
          </Text>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};
export default ManageItemProduct;
