import React from 'react';
import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
} from '../../../../../module/service/components/da';
import EmptyPage from '../../../../../../project-component/empty-page';
import LiquidContractTab from '../../../../../module/workplace/components/form/LiquidContractTab';
import {DataController} from '../../../../../base-controller';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface LiquidTabContentProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  methods: any;
  guest: any;
  onChangeStatus: (status: any, setServicesValue?: any) => void;
}

export default function LiquidTabContent({
  workData,
  serviceData,
  setServiceData,
  onRefresh,
  isRefreshing,
  methods,
  guest,
  onChangeStatus,
}: LiquidTabContentProps) {
  const handleLiquidSubmit = async () => {
    onChangeStatus(ToiletStatus.run);
    const taskController = new DataController('Task');

    // Complete liquid tasks
    const liquidRes = await taskController.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData?.Id}} @Type:[${TaskType.liquid} ${TaskType.liquid}] @Status:[${TaskStatus.open} ${TaskStatus.overdue}]`,
    });

    if (liquidRes.code === 200 && liquidRes.data.length) {
      taskController.edit(
        liquidRes.data.map((e: any) => ({
          ...e,
          Status: TaskStatus.done,
        })),
      );
    }

    // Close all remaining tasks
    const remainingRes = await taskController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@ToiletServicesId:{${serviceData?.Id}} @Type:[${TaskType.consultant} ${TaskType.liquid}] -@Status:[${TaskStatus.done} ${TaskStatus.done}]`,
    });

    if (remainingRes.code === 200 && remainingRes.data.length)
      await taskController.edit(
        remainingRes.data.map((e: any) => ({...e, Status: TaskStatus.closed})),
      );
  };

  if (
    serviceData?.Status != null &&
    serviceData.Status < ToiletServiceStatus.liquid
  ) {
    return <EmptyPage title={'Đơn hàng đang trong quá trình thiết kế'} />;
  }

  return (
    <LiquidContractTab
      methods={methods}
      data={workData}
      serviceData={serviceData}
      setServiceData={setServiceData}
      onRefreshing={onRefresh}
      customer={guest}
      isRefreshing={isRefreshing}
      onSubmit={handleLiquidSubmit}
    />
  );
}
