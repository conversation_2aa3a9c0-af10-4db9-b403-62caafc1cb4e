import React, {forwardRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  SafeAreaView,
  Platform,
  Dimensions,
  StyleSheet,
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';
import {Winicon} from 'component/export-component';
import {SkeletonImage} from 'project-component/skeleton-img';
import {TextFieldForm} from 'project-component/component-form';
import AppButton from 'component/button';
import ListTile from 'component/list-tile/list-tile';
import FLoading from 'component/Loading/FLoading';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {useTicketForm} from '../hooks/useTicketForm';
import {useFileUpload} from '../hooks/useFileUpload';
import ScreenHeader from 'screen/layout/header';
import WScreenFooter from 'screen/layout/footer';
import {closePopup, FPopup, showPopup} from 'wini-mobile-components';
import {TicketType} from 'types/ticketType';

interface AddTicketPopupProps {
  type?: number;
  toiletServicesId?: string;
  onDone: (value: TicketType) => void;
  parentPopupRef?: any;
}

const AddTicketPopup = forwardRef<any, AddTicketPopupProps>(
  function AddTicketPopup({type, onDone, parentPopupRef}, ref) {
    const user = useSelectorCustomerState().data;

    const {methods, isLoading, onSubmit, onError, popupRef} = useTicketForm(
      onDone,
      parentPopupRef,
    );
    const {pickImages, pickFiles, removeFile, getFileSize} =
      useFileUpload(methods);

    useEffect(() => {
      if (type) methods.setValue('Type', `${type}`);
    }, [type]);

    useEffect(() => {
      if (user) {
        methods.setValue('FullName', `${user?.Name}`);
        methods.setValue('Mobile', `${user?.Mobile}`);
      }
    }, [user]);

    const handleSubmit = () => {
      methods.handleSubmit(onSubmit, onError)();
    };

    const showFilePicker = () => {
      showPopup({
        ref: popupRef,
        enableDismiss: true,
        children: (
          <View style={styles.filePickerContainer}>
            <ScreenHeader
              style={styles.filePickerHeader}
              title="Thêm tệp"
              prefix={<View />}
              action={
                <View style={styles.filePickerAction}>
                  <Winicon
                    src="outline/layout/xmark"
                    onClick={() => closePopup(popupRef)}
                    size={20}
                    color={ColorThemes.light.neutral_text_body_color}
                  />
                </View>
              }
            />
            <ListTile onPress={pickImages} title="Thêm ảnh, video" />
            <ListTile onPress={pickFiles} title="Thêm tệp" />
          </View>
        ),
      });
    };

    const renderFileItem = (item: any, index: number) => (
      <ListTile
        key={`${item.uri}-${index}`}
        leading={
          <SkeletonImage source={{uri: item.uri}} style={styles.fileImage} />
        }
        title={item.name ?? `File ${index + 1}`}
        titleStyle={[
          TypoSkin.heading7,
          {color: ColorThemes.light.neutral_text_title_color},
        ]}
        subtitle={getFileSize(item.size)}
        listtileStyle={styles.fileListTile}
        style={styles.fileItem}
        trailing={
          <TouchableOpacity
            onPress={() => removeFile(item.uri)}
            style={styles.removeButton}>
            <FontAwesomeIcon
              icon={faMinusCircle}
              size={20}
              color="#D72525FF"
              style={styles.removeIcon}
            />
          </TouchableOpacity>
        }
      />
    );

    return (
      <SafeAreaView style={styles.container}>
        <FPopup ref={popupRef} />
        <FLoading visible={isLoading} />

        <ScreenHeader
          style={styles.header}
          title="Tạo yêu cầu hỗ trợ"
          prefix={<View />}
          action={
            <TouchableOpacity
              onPress={() => closePopup(parentPopupRef || popupRef)}
              style={styles.closeButton}>
              <Winicon
                src="outline/layout/xmark"
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          }
        />

        <KeyboardAvoidingView
          behavior="height"
          keyboardVerticalOffset={Platform.OS === 'ios' ? 160 : 0}
          style={styles.keyboardView}>
          <ScrollView style={styles.scrollView}>
            <View style={styles.formContainer}>
              <TextFieldForm
                label="Tên yêu cầu"
                required
                textFieldStyle={styles.textField}
                style={styles.textFieldContainer}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Name"
              />

              <View style={styles.fileSection}>
                <Text numberOfLines={1} style={[TypoSkin.label3]}>
                  Tệp đính kèm
                </Text>
                <TouchableOpacity
                  onPress={showFilePicker}
                  style={styles.addFileButton}>
                  <SkeletonImage
                    source={{
                      uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                    }}
                    style={styles.addFileIcon}
                  />
                  <Text style={styles.addFileText}>Thêm ảnh, files</Text>
                </TouchableOpacity>
              </View>

              {methods.watch('Files')?.length > 0 &&
                methods.watch('Files')?.slice(0, 3)?.map(renderFileItem)}

              {methods.watch('Files') && methods.watch('Files')?.length > 3 ? (
                <Text style={styles.moreFilesText}>
                  +{methods.watch('Files').length - 3} Files
                </Text>
              ) : null}

              <TextFieldForm
                required
                control={methods.control}
                name="Description"
                errors={methods.formState.errors}
                label="Mô tả"
                style={styles.descriptionContainer}
                textFieldStyle={styles.descriptionField}
                textStyle={styles.descriptionText}
                numberOfLines={10}
                multiline={true}
                onBlur={value => {
                  if (value == undefined || value.length == 0) {
                    methods.setError('TextArea', {
                      message: 'Vui lòng nhập thông tin mô tả',
                    });
                  } else {
                    methods.clearErrors('TextArea');
                  }
                }}
                register={methods.register}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        <WScreenFooter style={styles.footer}>
          <AppButton
            title="Gửi"
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={styles.submitButton}
            onPress={handleSubmit}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 100,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  keyboardView: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 16,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    flex: 1,
    gap: 16,
  },
  textFieldContainer: {
    width: '100%',
  },
  textField: {
    padding: 16,
  },
  fileSection: {
    gap: 8,
  },
  addFileButton: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 0.4,
    borderColor: ColorThemes.light.neutral_main_border,
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 8,
  },
  addFileIcon: {
    width: 35,
    height: 35,
    objectFit: 'cover',
  },
  addFileText: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  fileImage: {
    width: 35,
    height: 35,
    objectFit: 'cover',
    borderRadius: 4,
  },
  fileListTile: {
    gap: 16,
  },
  fileItem: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
    padding: 8,
  },
  removeButton: {
    padding: 4,
  },
  removeIcon: {
    backgroundColor: '#fff',
    borderRadius: 20,
  },
  moreFilesText: {
    color: ColorThemes.light.neutral_text_body_color,
    textAlign: 'center',
  },
  descriptionContainer: {
    backgroundColor: ColorThemes.light.transparent,
  },
  descriptionField: {
    height: 100,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    justifyContent: 'flex-start',
    backgroundColor: ColorThemes.light.transparent,
  },
  descriptionText: {
    textAlignVertical: 'top',
  },
  footer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  submitButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
  filePickerContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height / 3,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  filePickerHeader: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  filePickerAction: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
});

export default AddTicketPopup;
