import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ShopDetailsProps} from '../types';
import {styles} from '../styles';

const ShopDetails: React.FC<ShopDetailsProps> = ({shop, onCall}) => {
  const formatPhoneNumber = (phone: string) => {
    return phone.replace(/(\d{2})(\d{5})(\d{3})/, '$1*****$3');
  };

  return (
    <View style={styles.detailsContainer}>
      {/* Owner Name */}
      {shop?.OwnerName && (
        <View style={styles.detailRow}>
          <Winicon
            src="fill/users/profile"
            size={20}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
          <Text
            style={[
              TypoSkin.regular2,
              {color: ColorThemes.light.neutral_text_title_color},
              styles.detailText,
            ]}>
            {shop.OwnerName}
          </Text>
        </View>
      )}

      {/* Phone Number */}
      {shop?.Mobile && (
        <TouchableOpacity
          style={styles.detailRow}
          onPress={() => onCall(shop.Mobile)}>
          <Winicon
            src="fill/user interface/phone-call"
            size={20}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
          <Text
            style={[
              TypoSkin.regular2,
              {color: ColorThemes.light.neutral_text_title_color},
              styles.detailText,
            ]}>
            {formatPhoneNumber(shop.Mobile)}
          </Text>
        </TouchableOpacity>
      )}

      {/* Email */}
      {shop?.Email && (
        <View style={styles.detailRow}>
          <Winicon
            src="fill/user interface/mail"
            size={20}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
          <Text
            style={[
              TypoSkin.regular2,
              {color: ColorThemes.light.neutral_text_title_color},
              styles.detailText,
            ]}>
            {shop.Email}
          </Text>
        </View>
      )}

      {/* Shop Info Header */}
      <View style={styles.detailRowNoBorder}>
        <Winicon
          src="fill/shopping/store"
          size={20}
          color={ColorThemes.light.neutral_text_subtitle_color}
        />
        <Text
          style={[
            TypoSkin.regular2,
            {color: ColorThemes.light.neutral_text_title_color},
            styles.detailText,
          ]}>
          Thông tin đối tác
        </Text>
      </View>
    </View>
  );
};

export default ShopDetails;
