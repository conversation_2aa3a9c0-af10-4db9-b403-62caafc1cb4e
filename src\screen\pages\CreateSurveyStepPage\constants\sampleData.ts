// Sample data for CreateSurveyStepData interface
// This file contains comprehensive sample data for testing and development purposes

import {CreateSurveyStepData} from '../components/SurveyForm/types';
import {CustomerItem} from '../../../../redux/reducers/user/da';

/**
 * Sample executor (người thực hiện khảo sát)
 */
export const sampleExecutor: CustomerItem = {
  Id: 'executor-001',
  Name: '<PERSON>uy<PERSON><PERSON>ăn <PERSON>',
  Email: '<EMAIL>',
  Mobile: '0901234567',
  Address: '123 Đường Lê Lợi, Quận 1, TP.HCM',
  Gender: 1, // 1: Nam, 2: Nữ
  Username: 'nguyenvanan',
  Type: 2, // 1: KH, 2: đối tác
  Status: 1,
  Description: 'Chuyên viên khảo sát môi trường',
  Lat: 10.7769,
  Long: 106.7009,
  PeoplesIdentity: 123456789,
  DateCreated: Date.now(),
};

/**
 * Sample customer (khách hàng/đơn vị liên hệ)
 */
export const sampleCustomer: CustomerItem = {
  Id: 'customer-001',
  Name: 'Trần Thị Bình',
  Email: '<EMAIL>',
  Mobile: '0987654321',
  Address: 'Sở Xây dựng TP.HCM, 23-25 Lê Duẩn, Quận 1',
  Gender: 2,
  Username: 'tranthibinh',
  Type: 1,
  Status: 1,
  Description: 'Phụ trách quản lý cơ sở hạ tầng công cộng',
  Lat: 10.7829,
  Long: 106.6953,
  PeoplesIdentity: 987654321,
  DateCreated: Date.now(),
};

/**
 * Complete sample data for CreateSurveyStepData interface
 * Organized by logical sections matching the interface structure
 */
export const sampleCreateSurveyStepData: CreateSurveyStepData = {
  // Personal Information Section
  Executor: sampleExecutor,
  DateStart: new Date('2025-01-15').getTime(), // 1737763200000
  DateEnd: new Date('2025-01-20').getTime(), // 1738195200000
  Description:
    'Khảo sát định kỳ hệ thống nhà vệ sinh công cộng khu vực trung tâm thành phố',

  // Contact Information Section
  Customer: sampleCustomer,

  // Toilet Infrastructure Section
  ToiletCount: '6',
  UserCount: '6',
  SanitaryToiletCount: '4',
  CleaningProcess: 1, // Hàng ngày
  SepticTankLocation: 'Phía sau tòa nhà, cách 15m',
  Capacity: '50m³',
  TreatmentTechnology: 1, // Xử lý sinh học
  Kitchen: 4, // Bếp củi (closest match for "Không có khu vực bếp")
  SeparateDrainage: 1, // Có
  PumpingFrequency: 6,

  // Energy and Sustainability Section
  RenewableEnergy: 1, // Năng lượng mặt trời
  EnergyDetails:
    'Sử dụng đèn LED tiết kiệm năng lượng, quạt thông gió năng lượng mặt trời',
  WaterReuse: 'Tái sử dụng nước rửa tay cho tưới cây',
  WasteClassification:
    'Phân loại đầy đủ: rác hữu cơ, vô cơ, và chất thải nguy hại',
};

/**
 * Alternative sample data with different values for testing
 */
export const alternativeSampleData: CreateSurveyStepData = {
  // Personal Information Section
  Executor: {
    Id: 'executor-002',
    Name: 'Lê Thị Cẩm',
    Email: '<EMAIL>',
    Mobile: '0912345678',
    Address: '456 Nguyễn Huệ, Quận 1, TP.HCM',
    Gender: 2,
    Username: 'lethicam',
    Type: 2,
    Status: 1,
    Description: 'Kỹ sư môi trường',
  },
  DateStart: new Date('2025-02-01').getTime(), // 1738368000000
  DateEnd: new Date('2025-02-05').getTime(), // 1738713600000
  Description:
    'Khảo sát theo khiếu nại về tình trạng vệ sinh nhà vệ sinh công cộng',

  // Contact Information Section
  Customer: {
    Id: 'customer-002',
    Name: 'Phạm Văn Đức',
    Email: '<EMAIL>',
    Mobile: '0923456789',
    Address: 'UBND Quận 3, 123 Võ Văn Tần, Quận 3',
    Gender: 1,
    Username: 'phamvanduc',
    Type: 1,
    Status: 1,
    Description: 'Trưởng phòng Quản lý đô thị',
  },

  // Toilet Infrastructure Section
  ToiletCount: '3',
  UserCount: '6',
  SanitaryToiletCount: '2',
  CleaningProcess: 2, // Hàng tuần
  SepticTankLocation: 'Bên cạnh khu vực chính, cách 8m',
  Capacity: '25m³',
  TreatmentTechnology: 3, // Xử lý vật lý
  Kitchen: 1, // Bếp gas
  SeparateDrainage: 3, // Một phần
  PumpingFrequency: 3,

  // Energy and Sustainability Section
  RenewableEnergy: 4, // Không sử dụng
  EnergyDetails:
    'Sử dụng điện lưới thông thường, chưa có biện pháp tiết kiệm năng lượng',
  WaterReuse: 'Không tái sử dụng nước',
  WasteClassification: 'Phân loại cơ bản: rác thải rắn và nước thải',
};

/**
 * Minimal sample data for testing edge cases
 */
export const minimalSampleData: Partial<CreateSurveyStepData> = {
  Executor: null,
  DateStart: 0,
  DateEnd: 0,
  Description: '',
  Customer: null,
  ToiletCount: '',
  UserCount: '',
  SanitaryToiletCount: '',
  CleaningProcess: 0,
  SepticTankLocation: '',
  Capacity: '',
  TreatmentTechnology: 0,
  Kitchen: 0,
  SeparateDrainage: 0,
  PumpingFrequency: 0,
  RenewableEnergy: 0,
  EnergyDetails: '',
  WaterReuse: '',
  WasteClassification: '',
};
