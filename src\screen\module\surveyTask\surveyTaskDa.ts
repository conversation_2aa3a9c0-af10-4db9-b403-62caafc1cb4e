import {SurveyTask} from '../../../types/surveyTaskType';
import {DataController} from '../../base-controller';
import {
  SurveyTaskFilter,
  buildSearchQuery,
  getSortConfig,
} from './constants/filterConstants';

export class SurveyTaskDa {
  controller: DataController;
  customerController: DataController;
  toiletController: DataController;
  surveyTaskCriterionController: DataController;
  companyProfileController: DataController;
  constructor() {
    this.controller = new DataController('SurveyTask');
    this.customerController = new DataController('Customer');
    this.toiletController = new DataController('Toilet');
    this.companyProfileController = new DataController('CompanyProfile');
    this.surveyTaskCriterionController = new DataController(
      'SurveyTaskCriterion',
    );
  }

  async fetch(config: any): Promise<SurveyTask[]> {
    const res = await this.controller.getListSimple(config);
    if (res.code !== 200) throw new Error(res.message);

    const surveyTasks = res.data;

    // If no survey tasks, return empty array
    if (!surveyTasks || surveyTasks.length === 0) {
      return surveyTasks;
    }

    // Collect all unique customer IDs and executor IDs
    const customerIds = new Set<string>();
    surveyTasks.forEach((task: any) => {
      if (task.CustomerId && typeof task.CustomerId === 'string') {
        customerIds.add(task.CustomerId);
      }
      if (task.Executor && typeof task.Executor === 'string') {
        customerIds.add(task.Executor);
      }
    });

    // If no customer IDs found, return tasks without customer info
    if (customerIds.size === 0) {
      return surveyTasks.map((task: any) => ({
        ...task,
        Customer: null,
        Executor: null,
      }));
    }

    try {
      // Fetch customer information for all unique IDs
      const customers = await this.customerController.getListSimple({
        query: `@Id:{${Array.from(customerIds).join(' | ')}}`,
      });

      // Create a map for quick lookup
      const customersMap = new Map();
      if (customers?.code === 200 && customers?.data) {
        customers.data.forEach((customer: any) => {
          customersMap.set(customer.Id, customer);
        });
      }

      // Enrich survey tasks with customer and executor data
      return surveyTasks.map((task: any) => ({
        ...task,
        Customer: task.CustomerId
          ? customersMap.get(task.CustomerId) || null
          : null,
        Executor: task.Executor
          ? customersMap.get(task.Executor) || null
          : null,
      }));
    } catch (error) {
      console.warn('Error fetching customer data in fetch method:', error);
      // Return tasks without customer info if fetch fails
      return [];
    }
  }

  async fetchWithFilter(
    filter: SurveyTaskFilter,
    page: number = 1,
    size: number = 10,
  ): Promise<SurveyTask[]> {
    const searchQuery = buildSearchQuery(filter);
    const sortConfig = getSortConfig(filter.sortBy);

    const config = {
      page,
      size,
      query: searchQuery,
      sortby: sortConfig,
    };

    return this.fetch(config);
  }

  async fetchToiletCriterion(surveyTaskId: string) {
    const surveyTaskRes = await this.controller.getById(surveyTaskId);
    if (surveyTaskRes.code !== 200) throw new Error(surveyTaskRes.message);
    const toiletRes = await this.toiletController.getListSimple({
      query: `@CustomerId: {${surveyTaskRes.data.CustomerId}}`,
    });
    if (toiletRes.code !== 200) throw new Error(toiletRes.message);
    let toiletCriterions = [];
    if (toiletRes.data.length > 0) {
      const toiletCriterionsRes =
        await this.surveyTaskCriterionController.getListSimple({
          query: `@SurveyTaskId: {${surveyTaskId}} @ToiletId: {${toiletRes.data.map((e: any) => e.Id).join(' | ')}}`,
        });
      if (toiletCriterionsRes.code !== 200)
        throw new Error(toiletCriterionsRes.message);
      toiletCriterions = toiletCriterionsRes.data;
    }
    return {
      surveyTask: surveyTaskRes.data,
      toilets: toiletRes.data,
      toiletCriterions,
    };
  }

  async fetchById(id: string) {
    try {
      // Validate input
      if (!id || typeof id !== 'string') {
        console.error('Invalid ID provided to fetchById:', id);
        return null;
      }

      // Fetch main survey data
      const res = await this.controller.getById(id);
      if (res?.code !== 200 || !res?.data) {
        console.error('Failed to fetch survey data:', res);
        return null;
      }

      const data = res.data;

      // Filter out null/undefined customer IDs
      const customerIds = [data.CustomerId, data.Executor].filter(
        id => id && typeof id === 'string',
      );

      // If no valid customer IDs, return data without customer info
      if (customerIds.length === 0) {
        data.Customer = null;
        data.Executor = null;
        return data;
      }

      // Fetch customer information
      try {
        const customers = await this.customerController.getPatternList({
          query: `@Id:{${customerIds.join(' | ')}}`,
          pattern: {
            CompanyProfileId: ['Id', 'Name'],
          },
        });

        // Check if customer fetch was successful
        if (customers?.code === 200 && customers?.data) {
          const companyProfiles = customers.CompanyProfile;

          const customer =
            customers.data.find((e: any) => e.Id === data.CustomerId) || null;

          const executor =
            customers.data.find((e: any) => e.Id === data.Executor) || null;
          executor.CompanyProfileId =
            companyProfiles.find(
              (e: any) => e.Id === executor.CompanyProfileId,
            ) || null;

          data.Customer = customer;
          data.Executor = executor;
        } else {
          console.warn('Failed to fetch customer data, setting to null');
          data.Customer = null;
          data.Executor = null;
        }
      } catch (customerError) {
        console.warn(
          'Error fetching customer data, setting to null:',
          customerError,
        );
        data.Customer = null;
        data.Executor = null;
      }

      return data;
    } catch (error) {
      console.error('Error in fetchById:', error);
      return null;
    }
  }

  async createSurveyTask(data: any) {
    const res = await this.controller.add([data]);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }

  async updateSurveyTask(data: any) {
    const res = await this.controller.edit([data]);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }

  async addToiletCriterion(data: any) {
    const res = await this.surveyTaskCriterionController.add([data]);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }

  async updateToiletCriterion(data: any) {
    const res = await this.surveyTaskCriterionController.edit([data]);
    if (res.code !== 200) throw new Error(res.message);
    return res;
  }
}
