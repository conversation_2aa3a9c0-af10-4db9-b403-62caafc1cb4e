import { useNavigation, useRoute } from "@react-navigation/native";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Dimensions, KeyboardAvoidingView, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from "react-native";
import SelectProductStep from "../form/SelectProductStep";
import SelectFeatureStep from "../form/SelectFeatureStep";
import InputContactStep from "../form/InputContactStep";
import CheckOptStep from "../form/CheckOptStep";
import WScreenFooter from "../../../../layout/footer";
import AppButton from "../../../../../component/button";
import { ColorThemes } from "../../../../../assets/skin/colors";
import ScreenHeader from "../../../../layout/header";
import { showS<PERSON>kbar, Winicon } from "../../../../../component/export-component";
import { TypoSkin } from "../../../../../assets/skin/typography";
import { DataController } from "../../../../base-controller";
import { randomGID } from "../../../../../utils/Utils";
import { CustomerStatus, CustomerType } from "../../../../../redux/reducers/user/da";
import { CateServicesType, handleToiletDistribute, ProductType, randomToiletServiceName, ToiletCertificateStatus, ToiletFileType, ToiletServiceStatus, ToiletStatus, ToiletType } from "../da";
import { ComponentStatus } from "../../../../../component/component-status";
import { initPhoneOtp, signInWithPhoneFB } from "../../../../../features/otp-loginwFirebase/PhoneSignIn";
import { useSelectorCustomerCompanyState, useSelectorCustomerState } from "../../../../../redux/hooks/hooks";
import FLoading from "../../../../../component/Loading/FLoading";
import { BaseDA } from "../../../../baseDA";
import SelectedProblemStep from "../form/SelectProblemStep";
import FindRepairDevices from "../form/FindingRepairDevices";
import UploadDeviceImage from "../form/UploadDeviceImage";
import { RoleDa } from "screen/module/role/roleDa";

export default function RepairFlow() {
    const navigation = useNavigation()
    const route = useRoute()
    const methods = useForm({ shouldFocusError: false, defaultValues: { devices: [] } })
    const [step, setStep] = useState(0)
    const [cate, setCate] = useState()
    const user = useSelectorCustomerState().data
    const [loading, setLoading] = useState(false)
    const [upgrade, setUpgrade] = useState(false)
    const [done, setDone] = useState(false)
    const owner = useSelectorCustomerCompanyState().owner

    useEffect(() => {
        if (route.params) {
            setStep(route.params.step);
            methods.setValue("description", route.params.description)
            methods.setValue("serviceData", route.params.serviceData)
            methods.setValue("guest", route.params?.customer)
            setUpgrade(route.params.upgrade)
        }
        const cateController = new DataController("Category")
        cateController.filterByEmptyKey({ page: 1, size: 100, key: "ParentId", searchRaw: "*", sortby: [{ prop: "Sort", direction: "DESC" }] }).then(res => {
            if (res.code === 200) {
                setCate(res.data)
            }
        })
    }, [route.params])

    useEffect(() => {
        if (route.params?.toiletItem) {
            var toiletItem = route.params?.toiletItem
            methods.setValue("Name", toiletItem.Name)
            methods.setValue("Mobile", toiletItem.Mobile)
            methods.setValue("Address", toiletItem.Address)
            methods.setValue("Lat", toiletItem.Lat)
            methods.setValue("Long", toiletItem.Long)
            methods.setValue("Type", toiletItem.Type)
            methods.setValue("Place", toiletItem.Place)
            // setStep(1)
        }
    }, [route.params?.toiletItem])

    const onSubmitInformation = async () => {
        setLoading(true)
        const customerController = new DataController("Customer")
        var findCustomerByPhone = undefined
        let _mobile = methods.watch("Mobile").replace("+84", "0")
        if (_mobile.startsWith("84")) _mobile = _mobile.replace("84", "0")
        if (user?.CateServicesId?.includes(upgrade ? CateServicesType.upgrade : CateServicesType.repair)) {
            var findCustomerByPhone = { ...user }
        } else if (owner?.CateServicesId?.includes(upgrade ? CateServicesType.upgrade : CateServicesType.repair)) {
            findCustomerByPhone = { ...owner }
        } else if (!user) {
            var findCustomerByPhone = await customerController.getListSimple({ page: 1, size: 1, query: `@Type:[${CustomerType.partner} ${CustomerType.partner}] @CateServicesId:{${upgrade ? CateServicesType.upgrade : CateServicesType.repair}} @Mobile:(${_mobile})` })
            if (findCustomerByPhone.code === 200 && findCustomerByPhone.data.length)
                findCustomerByPhone = findCustomerByPhone.data[0]
        }

        const newCustomer = methods.watch("guest") ?? user ?? findCustomerByPhone ?? {
            Id: randomGID(),
            Name: methods.watch("Name"),
            DateCreated: Date.now(),
            Mobile: _mobile,
            Address: methods.watch("Address"),
            Lat: methods.watch("Lat"),
            Long: methods.watch("Long"),
            Status: CustomerStatus.active,
            Type: CustomerType.guest,
        }
        const newToilet = route.params?.toiletItem ?? {
            Id: randomGID(),
            Name: "Nhà vệ sinh ở " + methods.watch("Address"),
            DateCreated: Date.now(),
            CustomerId: newCustomer.Id,
            Status: ToiletStatus.register,
            Type: methods.getValues("Type") ? parseInt(methods.getValues("Type")) : null,
            Place: methods.getValues("Place") ? parseInt(methods.getValues("Place")) : null,
            Address: methods.watch("Address"),
            Lat: methods.watch("Lat"),
            Long: methods.watch("Long"),
            Mobile: _mobile,
            Description: methods.watch("description"),
            Certificate: ToiletCertificateStatus.clean,
        }
        const otherProblem = methods.watch("FAQ")?.filter(e => !e.Id)

        const randomName = await randomToiletServiceName(upgrade ? "CT" : "SCBD")
        let toiletServices = {
            Id: randomGID(),
            Name: randomName,
            DateCreated: Date.now(),
            Description: (methods.watch("description") ?? "") + (otherProblem?.length ? ` - ${otherProblem.map(e => e.Content).join(". ")}` : ""),
            Sort: 1,
            Status: ToiletServiceStatus.register,
            CateServicesId: upgrade ? CateServicesType.upgrade : CateServicesType.repair,
            ToiletId: newToilet.Id,
            CustomerId: findCustomerByPhone?.Id,
            CustomerMobile: _mobile,
            FAQId: methods.watch("FAQ")?.filter(e => e.Id)?.map(e => e.Id).join(","),
        }
        methods.setValue("toiletServiceId", toiletServices.Id)
        const devices = methods.getValues("devices").filter(e => e.Type !== ProductType.bio).map(e => {
            return {
                Id: randomGID(),
                Name: e.Name,
                DateCreated: Date.now(),
                ToiletServicesId: toiletServices.Id,
                ProductId: e.Id,
                Quantity: e["_Quantity"],
                Price: e.Price,
                CateServicesId: upgrade ? CateServicesType.upgrade : CateServicesType.repair,
                Description: e.Description,
                Specifications: e.Specifications
            }
        })
        const bioProducts = methods.getValues("devices").filter(e => e.Type === ProductType.bio).map(e => {
            return {
                Id: randomGID(),
                Name: e.Name,
                DateCreated: Date.now(),
                ToiletServicesId: toiletServices.Id,
                ProductId: e.Id,
                Quantity: e["_Quantity"],
                Price: e.Price,
                CateServicesId: CateServicesType.create,
                Description: e.Description,
                Specifications: e.Specifications
            }
        })
        if (!toiletServices.CustomerId) {
            const closestConsultant = await handleToiletDistribute({ lat: newToilet.Lat, long: newToilet.Long, cateIds: toiletServices?.CateServicesId?.split(",") })
            toiletServices.CustomerId = closestConsultant?.Id
            // get company
            const roleDa = new RoleDa()
            const customerRole = await roleDa.getCustomerRole(closestConsultant?.Id)
            if (customerRole?.company) {
                toiletServices.CompanyProfileId = customerRole?.company?.Id
            }
        }
        const toiletController = new DataController("Toilet")
        const toiletServicesController = new DataController("ToiletServices")
        const deviceController = new DataController("Device")
        const bioProductController = new DataController("BioProduct")

        if (newCustomer.Id !== user?.Id && !findCustomerByPhone && !methods.watch("guest")) {
            const customerRes = await customerController.add([newCustomer])
            if (customerRes.code !== 200) {
                setLoading(false)
                setDone(false)
                return showSnackbar({ message: customerRes.message, type: ComponentStatus.ERROR })
            }
        }
        const _files = methods.watch("files")
        if (_files?.length) {
            const filesRes = await BaseDA.uploadFiles(methods.watch("files"))
            const toiletFileController = new DataController("ToiletFile")
            toiletFileController.add(filesRes.map(e => {
                return {
                    Id: randomGID(),
                    DateCreated: Date.now(),
                    Name: e.Name,
                    File: e.Id,
                    Size: e.Size,
                    Type: ToiletFileType.other,
                    ToiletId: newToilet.Id,
                    Description: `${e.Type} - ${e.Url}`,
                    Sort: 1
                }
            }))
        }
        if (!route.params?.toiletItem) {
            const toiletRes = await toiletController.add([newToilet])
            if (toiletRes.code !== 200) {
                setLoading(false)
                setDone(false)
                return showSnackbar({ message: toiletRes.message, type: ComponentStatus.ERROR })
            }
        }

        const toiletServicesRes = await toiletServicesController.add([toiletServices])
        if (toiletServicesRes.code !== 200) {
            setLoading(false)
            setDone(false)
            return showSnackbar({ message: toiletServicesRes.message, type: ComponentStatus.ERROR })
        }
        if (devices.length) await deviceController.add(devices)
        if (bioProducts.length) bioProductController.add(bioProducts)
        setLoading(false)
        setDone(true)
    }

    const checkValidNextStep = () => {
        if (step === 3 && !methods.watch("extend-step") && methods.watch("Name")?.length > 0 && methods.watch("Mobile")?.length > 0 && methods.watch("Address")?.length > 0) {
            return true
        }
        switch (step) {
            case 0:
                return true
            case 1:
                return true
            case 2:
                return true
            case 3:
                return methods.watch("devices")?.length > 0
            case 4:
                return methods.watch("Type") && methods.watch("Place")
            case 5:
                return methods.watch("Name")?.length > 0 && methods.watch("Mobile")?.length > 0 && methods.watch("Address")?.length > 0
            default:
                return false
        }
    }

    const onSendOtp = async () => {
        // console.log('Next w value: ', methods.getValues());
        if ((methods.watch("extend-step") && step === 5) || step === 3) setStep(step + 1)
        return
        // setLoading(true)
        // var rs = await signInWithPhoneFB(methods.watch("Mobile"))
        // if (rs) {
        //     // done
        //     methods.setValue("confirmationResult", rs)
        //     //done
        //     if ((methods.watch("extend-step") && step === 5) || step === 3) setStep(step + 1)
        //     showSnackbar({
        //         message: 'Đã gửi mã xác thực đến số diện thoại',
        //         status: ComponentStatus.SUCCSESS,
        //     });
        // } else {
        //     // fail
        //     showSnackbar({ message: "Đã có lỗi xảy ra", status: ComponentStatus.ERROR })
        // }
        // setLoading(false)
    }



    return <SafeAreaView style={{ flex: 1, backgroundColor: ColorThemes.light.neutral_absolute_background_color }}>
        <FLoading visible={loading} avt="" />
        <ScreenHeader
            style={{
                shadowColor: "rgba(0, 0, 0, 0.03)",
                shadowOffset: {
                    width: 0,
                    height: 4
                },
                shadowRadius: 20,
                elevation: 20,
                shadowOpacity: 1,
            }}
            children={
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <TouchableOpacity
                            style={{ padding: 12, gap: 8, flexDirection: 'row', alignItems: 'center' }}
                            onPress={() => {
                                navigation.pop();
                            }}>
                            <Winicon
                                src="outline/arrows/left-arrow"
                                color={ColorThemes.light.neutral_text_subtitle_color}
                                size={20}
                            />
                            <Text
                                style={[
                                    TypoSkin.heading8,
                                    { color: ColorThemes.light.neutral_text_title_color },
                                ]}>
                                Thoát
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            }
        />
        <View style={{ flex: 1 }}>
            <SelectedProblemStep category={cate} upgrade={upgrade} methods={methods} display={step === 0 ? 'flex' : 'none'} />
            {step === 1 ? <UploadDeviceImage category={cate} upgrade={upgrade} methods={methods} display={step === 1 ? 'flex' : 'none'} /> : <View />}
            {step === 2 ? <FindRepairDevices category={cate} upgrade={upgrade} setStep={(value) => { setStep(value) }} methods={methods} display={step === 2 ? 'flex' : 'none'} /> : <View />}
            {step === 3 && methods.watch("extend-step") ? <SelectProductStep methods={methods} display={step === 3 && methods.watch("extend-step") ? 'flex' : 'none'} /> : <View />}
            {step === 4 && methods.watch("extend-step") ? <SelectFeatureStep methods={methods} display={step === 4 && methods.watch("extend-step") ? 'flex' : 'none'} /> : <View />}
            {step === 3 && !methods.watch("extend-step") || step === 5 ?
                //step 3 | 5
                <InputContactStep methods={methods} display={step === 3 && !methods.watch("extend-step") || step === 5 ? 'flex' : 'none'} editable={route.params?.toiletItem ? false : true} />
                : <View />}
            {step === 4 && !methods.watch("extend-step") || step === 6 ?
                //step 4 | 6
                <CheckOptStep methods={methods} setLoading={setLoading} display={step === 4 && !methods.watch("extend-step") || step === 6 ? 'flex' : 'none'} onSendOtp={onSendOtp} onSubmitResult={onSubmitInformation} /> : <View />}
        </View>
        {done ? null : ((!methods.watch("extend-step") && step <= 3) || step < 6) ? <WScreenFooter style={{ justifyContent: "space-between", paddingVertical: 0 }}>
            <View style={{ flexDirection: "row", height: 6, width: "100%", gap: 4 }}>
                {Array.from({ length: methods.watch("extend-step") ? 6 : 4 }).map((_, i) => <View key={`step${i}`} style={{ flex: 1, height: "100%", width: "100%", backgroundColor: i === step ? ColorThemes.light.neutral_absolute_reverse_background_color : ColorThemes.light.neutral_main_background_color }} />)}
            </View>
            <View style={{
                flexDirection: 'row', justifyContent: "space-between", paddingHorizontal: 16, paddingTop: 12
            }}>
                {step !== 0 ? <AppButton
                    title={'Quay lại'}
                    backgroundColor={ColorThemes.light.neutral_main_background_color}
                    borderColor="transparent"
                    containerStyle={{ height: 40, borderRadius: 8, alignSelf: "baseline", paddingHorizontal: 12, paddingVertical: 5 }}
                    onPress={() => {
                        if (step) setStep(step - 1)
                    }}
                    textColor={ColorThemes.light.neutral_text_subtitle_color}
                /> : <View />}
                <AppButton
                    title={step === 5 || step === 3 && !methods.watch("extend-step") ? 'Gửi thông tin' : 'Tiếp'}
                    backgroundColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{ height: 40, borderRadius: 8, alignSelf: "baseline", paddingHorizontal: 12, paddingVertical: 5 }}
                    disabled={!checkValidNextStep()}
                    onPress={() => {
                        if ((!methods.watch("extend-step") && step === 3) || step === 5) onSendOtp()
                        else setStep(step + 1)

                    }}
                    textColor={ColorThemes.light.neutral_absolute_background_color}
                />
            </View>
        </WScreenFooter > : null}
    </SafeAreaView>
}