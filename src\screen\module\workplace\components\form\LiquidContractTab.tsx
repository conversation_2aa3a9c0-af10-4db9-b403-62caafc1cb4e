import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  FDialog,
  FRating,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {FPopup, showPopup} from '../../../../../component/popup/popup';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {
  ContractStatus,
  ContractType,
  signTickImg,
  ToiletServiceStatus,
} from '../../../service/components/da';
import EmptyPage from '../../../../../project-component/empty-page';
import {useNavigation} from '@react-navigation/native';
import {CustomerRole} from '../../../../../redux/reducers/user/da';
import {randomGID, regexGetVariables, Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import WebView from 'react-native-webview';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {PopupCheckOtp} from '../../../../../project-component/popup-otp';
import {ComponentStatus} from '../../../../../component/component-status';
import {RootScreen} from '../../../../../router/router';
import {useForm} from 'react-hook-form';
import ConfigAPI from '../../../../../config/configApi';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {TextFieldForm} from '../../../../../project-component/component-form';
import {popupReject} from './QuoteTable';
import {ButtonViewRejectReason} from '../popup/DialogCustomize';
import {mapLiquidContractData} from './PrintForm';
import {SafeAreaView} from 'react-native-safe-area-context';

export default function LiquidContractTab({
  data,
  serviceData,
  onSubmit,
  isRefreshing,
  onRefreshing,
  methods,
  customer,
}: any) {
  const user = useSelectorCustomerState().data;
  const {owner, ktxgroup} = useSelectorCustomerCompanyState();
  const userRole = useSelectorCustomerState().role;
  const company = useSelectorCustomerCompanyState().data;
  const navigation = useNavigation<any>();

  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();
  const templateContractId = 'e048a66efcae49139ab577f26a46561c';

  const [contract, setContract] = useState<any>();

  const cateServices = useMemo(
    () => methods.watch('cateServices'),
    [methods.watch('cateServices')],
  );

  const [isLoading, setLoading] = useState(false);
  const [tab, setTab] = useState(0);

  const isEditable = useMemo(() => {
    if (!serviceData?.Status) return false;
    else if (user?.Id === serviceData?.CustomerId) return true;
    else if (
      serviceData?.CustomerId === owner?.Id &&
      userRole?.Role?.includes(CustomerRole.Coordinator)
    )
      return true;
    return false;
  }, [data, serviceData, user, userRole]);

  const getData = async () => {
    const controller = new DataController('Contract');
    const res = await controller.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${ContractType.liquid} ${ContractType.liquid}]`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    let tmpContract = res?.data?.[0] ?? {Content: ''};

    if (res?.data?.[0]) {
      console.log('==============tmpContract=================', Platform.OS);
      console.log(tmpContract.Id, tmpContract.Status);
      if (
        tmpContract.Status !== ContractStatus.guestSigned &&
        tmpContract.Status !== ContractStatus.partnerSigned
      ) {
        tmpContract.Content = await mapLiquidContractData({
          cateServices: cateServices,
          customer: customer,
          data: data,
          partnerData: {data: user, company: company},
          servicesData: serviceData,
          ktxgroup: ktxgroup,
        });
      }
      setContract(tmpContract);
      return false;
    } else {
      return true;
    }
  };

  const createViewLiquidContract = async () => {
    const controller = new DataController('Contract');
    let Content = await mapLiquidContractData({
      cateServices: cateServices,
      customer: customer,
      data: data,
      partnerData: {data: user, company: company},
      servicesData: serviceData,
      ktxgroup: ktxgroup,
    });
    const newContract = {
      Id: randomGID(),
      Name: 'BIÊN BẢN THANH LÝ HỢP ĐỒNG',
      DateCreated: Date.now(),
      Type: ContractType.liquid,
      Status: ContractStatus.init,
      ToiletServicesId: serviceData.Id,
      DocumentsId: templateContractId,
      Content: Content,
    };
    controller.add([newContract]).then(reshd => {
      if (reshd.code === 200) {
        console.log('======create Content done===');
        setContract(newContract);
      }
    });
  };

  useEffect(() => {
    if (serviceData?.Status === ToiletServiceStatus.run) return setTab(1);
  }, [serviceData]);

  useEffect(() => {
    if (serviceData && customer && data && user) {
      getData().then(value => {
        if (value == false) {
          return;
        } else {
          if (value == true && user?.Id === serviceData?.CustomerId) {
            console.log(
              '==============content created=================',
              Platform.OS,
            );
            console.log(user?.Id === serviceData?.CustomerId);
            createViewLiquidContract();
          }
        }
      });
    }
  }, [serviceData, data, user, customer]);

  const rejectReasons = useMemo(
    () => (contract?.RejectReason ? JSON.parse(contract.RejectReason) : []),
    [contract],
  );

  const submitOtp = async (onSuccess: any) => {
    // check OTP
    if (!user?.Mobile) return;
    showPopup({
      ref: popupRef,
      children: (
        <PopupCheckOtp
          ref={popupRef}
          phone={user?.Mobile}
          onSuccess={onSuccess}
          isLoading={isLoading}
          setLoading={setLoading}
        />
      ),
    });
  };
  const {width, height} = Dimensions.get('window');

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      {tab == 1 ? (
        <RatingService serviceData={serviceData} data={data} />
      ) : (
        <View style={{flex: 1, height: '100%'}}>
          {!isEditable &&
          (!contract?.Id ||
            contract?.Status === ContractStatus.init ||
            contract?.Status === ContractStatus.reject) ? (
            <View
              style={{
                height: Dimensions.get('screen').height / 2,
                justifyContent: 'center',
                paddingHorizontal: 16,
              }}>
              <EmptyPage
                title={
                  'Đơn hàng đang chờ đối tác gửi biên bản thanh lý hợp đồng'
                }
              />
            </View>
          ) : (
            <View style={{flex: 1}}>
              {rejectReasons.length > 0 ? (
                <View style={{width: '100%'}}>
                  <ButtonViewRejectReason
                    customers={[customer.data]}
                    rejectReasons={rejectReasons}
                  />
                </View>
              ) : null}

              {contract ? (
                <Pressable
                  style={{
                    gap: 16,
                    flex: 1,
                    height:
                      Platform.OS === 'ios'
                        ? Dimensions.get('screen').height * 2
                        : Dimensions.get('screen').height + 500,
                  }}>
                  <WebView
                    key={`${contract?.Id}-${contract?.Status}`}
                    renderError={() => <EmptyPage />}
                    startInLoadingState
                    textZoom={100}
                    bounces={false}
                    nestedScrollEnabled
                    onLoadEnd={() => {
                      console.log('WebView finished loading');
                    }}
                    limitsNavigationsToAppBoundDomains
                    renderLoading={() => (
                      <ActivityIndicator
                        style={{
                          backgroundColor: 'transparent',
                          position: 'absolute',
                          left: 0,
                          right: 0,
                          top: height / 3,
                          zIndex: 9,
                        }}
                        color={ColorThemes.light.primary_main_color}
                        size="large"
                      />
                    )}
                    style={{
                      height: Dimensions.get('screen').height * 3,
                      width: Dimensions.get('screen').width,
                      flex: 1,
                    }}
                    originWhitelist={['*']}
                    javaScriptEnabled={true}
                    injectedJavaScript='document.body.querySelector(".innerhtml-view").style.paddingBottom = "100px";'
                    source={{
                      uri: `${ConfigAPI.urlWeb}contract-web-view?type=contract&id=${contract?.Id}`,
                    }}
                  />
                </Pressable>
              ) : null}
              {user?.Id === data[0]?.CustomerId &&
                contract?.Status === ContractStatus.partnerSigned && (
                  <SafeAreaView edges={['bottom']} style={styles.bottomButtons}>
                    <TouchableOpacity
                      style={{
                        ...styles.button,
                        backgroundColor:
                          ColorThemes.light.neutral_main_background_color,
                      }}
                      onPress={() => {
                        popupReject({
                          ref: dialogRef,
                          methods: methods,
                          title:
                            'Bạn chắc chắn muốn từ chối biên bản thanh lý hợp đồng này?',
                          onSubmit: async (ev: any) => {
                            const newRejectReason = {
                              DateCreated: Date.now(),
                              Content: ev,
                            };
                            const updateContact = {
                              ...contract,
                              Status: ContractStatus.reject,
                              RejectReason: JSON.stringify([
                                ...rejectReasons,
                                newRejectReason,
                              ]),
                            };
                            const controller = new DataController('Contract');
                            const res = await controller.edit([updateContact]);
                            if (res.code === 200) {
                              setContract(updateContact);
                            }
                          },
                        });
                      }}>
                      <Text
                        style={{
                          ...styles.buttonText,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>
                        Từ chối
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.button}
                      onPress={() =>
                        submitOtp(async () => {
                          const controller = new DataController('Contract');
                          const res = await controller.edit([
                            {
                              ...contract,
                              Status: ContractStatus.guestSigned,
                              DateSign: Date.now(),
                              ToiletId: data.Id,
                              Content: contract?.Content.replace(
                                regexGetVariables,
                                '',
                              ).replace(
                                regexGetVariables,
                                (m: any, key: any) => {
                                  switch (key) {
                                    case 'GuestSign':
                                      return signTickImg;
                                    case 'GuestSignName':
                                      return user?.Name;
                                    case 'GuestDateSign':
                                      return Ultis.datetoString(new Date());
                                    default:
                                      return '';
                                  }
                                },
                              ),
                            },
                          ]);
                          if (res.code === 200) {
                            onSubmit().then(() => {
                              setTab(1);
                            });
                          }
                        })
                      }>
                      <Text style={styles.buttonText}>Ký biên bản TLHĐ</Text>
                    </TouchableOpacity>
                  </SafeAreaView>
                )}
              {user?.Id === serviceData?.CustomerId &&
              contract?.Status !== ContractStatus.partnerSigned &&
              contract?.Status !== ContractStatus.guestSigned ? (
                <SafeAreaView edges={['bottom']} style={styles.bottomButtons}>
                  <TouchableOpacity
                    style={styles.button}
                    onPress={() => {
                      submitOtp(async () => {
                        const controller = new DataController('Contract');
                        const newContract = {
                          Id: contract?.Id ?? randomGID(),
                          ...(contract ?? {}),
                          Name:
                            'BIÊN BẢN THANH LÝ HỢP ĐỒNG ' +
                            cateServices
                              .map((e: any) => e.Name)
                              .join(', ')
                              .toUpperCase(),
                          DateCreated: Date.now(),
                          Type: ContractType.liquid,
                          Status: ContractStatus.partnerSigned,
                          DateSign: Date.now(),
                          Content: contract?.Content.replace(
                            regexGetVariables,
                            (m: any, key: any) => {
                              switch (key) {
                                case 'KTXSign':
                                  return signTickImg;
                                case 'PartnerSign':
                                  return signTickImg;
                                case 'PartnerSignName':
                                  return user?.Name;
                                case 'PartnerDateSign':
                                  return Ultis.datetoString(new Date());
                                default:
                                  return m;
                              }
                            },
                          ),
                          ToiletServicesId: serviceData.Id,
                          DocumentsId: templateContractId,
                        };

                        const res = await controller.add([newContract]);
                        if (res.code !== 200)
                          showSnackbar({
                            message: res.message,
                            status: ComponentStatus.ERROR,
                          });
                        showSnackbar({
                          message: 'Ký và gửi biên bản TLHĐ thành công',
                          status: ComponentStatus.SUCCSESS,
                        });
                        setContract(newContract);
                      });
                    }}>
                    <Text style={styles.buttonText}>
                      Ký và gửi biên bản TLHĐ
                    </Text>
                  </TouchableOpacity>
                </SafeAreaView>
              ) : null}
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const RatingService = ({serviceData, data}: any) => {
  const methods = useForm({shouldFocusError: false});
  const [rate, setState] = useState(0);
  const user = useSelectorCustomerState().data;
  const navigation = useNavigation<any>();

  return (
    <SafeAreaView edges={['bottom']} style={{flex: 1}}>
      <KeyboardAvoidingView
        behavior={'padding'}
        style={{flex: 1, width: '100%', height: '100%'}}>
        <ScrollView style={{flex: 1, width: '100%', height: '100%'}}>
          <Pressable style={{flex: 1}}>
            <View
              style={{
                flex: 1,
                gap: 16,
                alignItems: 'center',
                width: '100%',
                height: '100%',
                alignContent: 'center',
                justifyContent: 'center',
                paddingHorizontal: 16,
                paddingBottom: 165,
              }}>
              <Winicon
                src="https://redis.ktxgroup.com.vn/api/file/img/fc4b703aee2e40ada3f4c48a855ef55b"
                size={200}
              />
              <Text
                style={[
                  TypoSkin.buttonText2,
                  {
                    color: ColorThemes.light.neutral_text_label_color,
                    textAlign: 'center',
                  },
                ]}>
                Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi
              </Text>
              <Text
                style={{
                  ...TypoSkin.subtitle2,
                  color: ColorThemes.light.neutral_text_title_color,
                  textAlign: 'center',
                }}>
                Hãy để lại đánh giá/góp ý của bạn về chúng tôi tại đây để giúp
                KTX và đối tác tiếp tục phát triển dịch vụ tốt hơn.
              </Text>
              <FRating
                value={rate}
                fillColor={ColorThemes.light.secondary1_main_color}
                onChange={e => setState(e)}
                size={32}
              />
              <TextFieldForm
                control={methods.control}
                name="Content"
                errors={methods.formState.errors}
                placeholder={'Nhập phản hồi/góp ý...'}
                style={{backgroundColor: ColorThemes.light.transparent}}
                textFieldStyle={{
                  height: 100,
                  width: '100%',
                  paddingHorizontal: 16,
                  paddingTop: 16,
                  paddingBottom: 16,
                  justifyContent: 'flex-start',
                  backgroundColor: ColorThemes.light.transparent,
                }}
                textStyle={{textAlignVertical: 'top'}}
                numberOfLines={10}
                multiline={true}
                register={methods.register}
              />
            </View>
          </Pressable>
        </ScrollView>
        <View style={styles.bottomButtons}>
          <TouchableOpacity
            style={{
              ...styles.button,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
            }}
            onPress={() => {
              navigation.goBack();
              setTimeout(() => {
                navigation.navigate(RootScreen.detailProject, {
                  item: {ToiletId: data.Id},
                });
              }, 50);
            }}>
            <Text
              style={{
                ...styles.buttonText,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              Xem chi tiết
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.button}
            onPress={async () => {
              const newRatingData = {
                Id: randomGID(),
                Name: 'Đánh giá dịch vụ ' + serviceData.Name,
                DateCreated: Date.now(),
                CustomerId: user?.Id,
                Sort: 1,
                ToiletServicesId: serviceData.Id,
                Content: methods.getValues('Content'),
                Value: rate,
              };
              const controller = new DataController('Rating');
              const res = await controller.add([newRatingData]);
              if (res.code !== 200)
                return showSnackbar({
                  message: res.message,
                  status: ComponentStatus.ERROR,
                });
              showSnackbar({
                message: 'Gửi đánh giá thành công!',
                status: ComponentStatus.SUCCSESS,
              });
              navigation.goBack();
            }}>
            <Text style={styles.buttonText}>Gửi đánh giá</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
    width: '100%',
    marginTop: 10,
  },
  bottomButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    gap: 8,
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
