import {useCallback, useEffect, useMemo, useState} from 'react';
import ListToiletCriterionDA from '../da';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {ToiletCriterion} from 'types/toiletCriterionType';
import {ToiletItem} from 'types/toiletType';
import {CateCriterion} from 'types/cateCriterionType';

type UseListToiletCriterionParams = {
  toiletServiceId: string;
};

export function useListToiletCriterion({
  toiletServiceId,
}: UseListToiletCriterionParams) {
  // Data Access instance
  const listToiletCriterionData = useMemo(
    () => new ListToiletCriterionDA(),
    [],
  );

  // UI states
  const [isBsSelectCriterion, setIsBsSelectCriterion] = useState(false);
  const [selectedToilet, setSelectedToilet] = useState<ToiletItem | null>(null);
  const [toiletSelected, setToiletSelected] = useState<ToiletItem[]>([]);
  const [toiletCriterions, setToiletCriterions] = useState<ToiletCriterion[]>(
    [],
  );

  const [criterions, setCriterions] = useState<any[]>([]);
  const [cateCriterions, setCateCriterions] = useState<CateCriterion[] | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);

  const findToiletCriterionByToiletId = useCallback(
    (toiletId: string) => {
      return toiletCriterions.find(
        (criterion: any) => criterion.ToiletId === toiletId,
      );
    },
    [toiletCriterions],
  );

  const refreshToiletCriterions = useCallback(async () => {
    try {
      setIsLoading(true);
      const result =
        await listToiletCriterionData.getToiletCriterion(toiletServiceId);
      if (result) {
        setToiletCriterions(result.toiletCriterions);
      }
    } catch (error) {
      console.error('Error refreshing toilet criterions:', error);
      showSnackbar({
        message: 'Lỗi khi làm mới dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  }, [listToiletCriterionData, toiletServiceId]);

  const handleToiletCardPress = useCallback((item: ToiletItem) => {
    setSelectedToilet(item);
    setIsBsSelectCriterion(true);
  }, []);

  const markToiletAsUpdated = useCallback((toiletIds: string[]) => {
    setToiletSelected(previousToilets =>
      previousToilets.map(toilet =>
        toiletIds.includes(toilet.Id) ? {...toilet, isUpdated: true} : toilet,
      ),
    );
  }, []);

  const initData = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsError(false);

      const result =
        await listToiletCriterionData.getToiletCriterion(toiletServiceId);

      if (result) {
        const {toilets, cateCriterions, criterions, toiletCriterions} = result;
        setCateCriterions(cateCriterions);
        setCriterions(criterions || []);
        setToiletCriterions(toiletCriterions || []);
        setToiletSelected(toilets || []);
      } else {
        setIsError(true);
        showSnackbar({
          message: 'Không thể lấy dữ liệu toilet criterion',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error in initData:', error);
      setIsError(true);
      showSnackbar({
        message: 'Lỗi khi lấy dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  }, [listToiletCriterionData, toiletServiceId]);

  useEffect(() => {
    if (toiletServiceId) {
      initData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toiletServiceId]);

  return {
    // data states
    isLoading,
    isError,
    toiletSelected,
    toiletCriterions,
    criterions,
    cateCriterions,

    // selection modal state
    isBsSelectCriterion,
    setIsBsSelectCriterion,
    selectedToilet,

    // actions
    handleToiletCardPress,
    findToiletCriterionByToiletId,
    refreshToiletCriterions,
    markToiletAsUpdated,
  };
}

export default useListToiletCriterion;
