import {Text, View, StyleSheet} from 'react-native';
import MenuCards from '../../../pages/ProfilePage/components/menu/ChildMenu';
import iconSvg from '../../../../svgs/iconSvg';
import {RootScreen} from '../../../../router/router';

const TabPartnerManagerContent: React.FC<{}> = () => {
  // Custom hooks
  return (
    <View style={styles.container}>
      <MenuCards
        svgIcon={iconSvg.afiliate}
        title="Cập nhật Thông tin đối tác"
        order={RootScreen.ShopInfo}
        SvgIconSub={iconSvg.rightarrow}
      />
      <MenuCards
        svgIcon={iconSvg.manageProsuct}
        title="QL sản phẩm"
        order={RootScreen.ManageProduct}
        SvgIconSub={iconSvg.rightarrow}
      />
      <MenuCards
        svgIcon={iconSvg.manageGoods}
        title="QL vật tư"
        order={RootScreen.MaterialList}
        SvgIconSub={iconSvg.rightarrow}
      />
      <MenuCards
        svgIcon={iconSvg.Promotion}
        title="Khuyến mại"
        order={RootScreen.PromorionProductPage}
        SvgIconSub={iconSvg.rightarrow}
      />
      <MenuCards
        svgIcon={iconSvg.ManagePayment}
        title="QL phương thức thanh toán"
        order={RootScreen.ManagePayment}
        SvgIconSub={iconSvg.rightarrow}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
});

export default TabPartnerManagerContent;
