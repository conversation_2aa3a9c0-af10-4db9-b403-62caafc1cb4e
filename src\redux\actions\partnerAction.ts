import { DataController } from "../../screen/base-controller";

export const partnerAction = {
  findOne: async (
    config: {
      page?: number;
      size?: number;
      sortby?: any;
      query?: string;
    },
  ) => {
    const controller = new DataController('Shop');
       const res = await controller.aggregateList(config);
    if (res.code === 200) {
      return res.data;
    }
    return [];
  
  },
};