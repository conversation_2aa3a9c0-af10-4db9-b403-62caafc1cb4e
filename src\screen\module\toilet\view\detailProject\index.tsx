import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  SafeAreaView,
  Platform,
  StatusBar,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {useNavigation, useRoute} from '@react-navigation/native';
import HeaderDetailProject from '../../components/header-detail-project';
import Overview from './overview';
import Dashboard from './dashboard';
import Task from './task';
import {useEffect, useRef, useState} from 'react';
import {FPopup} from '../../../../../component/popup/popup';
import {DataController} from '../../../../base-controller';
import {
  ToiletServiceStatus,
  ToiletStatus,
} from '../../../service/components/da';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
  useSelectorToiletState,
} from '../../../../../redux/hooks/hooks';
import FLoading from '../../../../../component/Loading/FLoading';
import FilesList from './filesList';
import EmptyPage from '../../../../../project-component/empty-page';
import {PageSkeleton} from '../../../../../project-component/skeletonPage';
import CalendarTab from './calendarTab';
import DevicesListTab from './ToiletDeviceBottomSheet';
import ContractToiletListTab from './ToiletListContractTab';
import NoteTab from './noteTab';
import ToiletBioList from './ToiletBioList';
import ToiletDeviceList from './ToiletDeviceList';

export default function DetailProject() {
  const navigation = useNavigation<any>();
  const [tab, setTab] = useState(0);
  const popupRef = useRef<any>();
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const {myToilet} = useSelectorToiletState();
  const [isRefreshing, setRefreshing] = useState(false);
  const [isLoading, setLoading] = useState(false);

  const route = useRoute<any>();

  const [workData, setWorkData] = useState<any>();
  const [serviceData, setServiceData] = useState<any>();

  const getData = async () => {
    if (!route.params) return;
    setLoading(true);
    var id = route.params.item.ToiletId ?? route.params.item.Id;
    const servicesController = new DataController('ToiletServices');
    const controller = new DataController('Toilet');
    const serviceRes = await servicesController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@ToiletId:{${id}} -@Status:[${ToiletServiceStatus.reject} ${ToiletServiceStatus.reject}]`,
    });
    if (serviceRes.code === 200 && serviceRes.data.length)
      setServiceData(serviceRes.data[0]);
    const res = await controller.getById(id);
    if (res.code === 200 && res.data) setWorkData(res.data);
  };

  useEffect(() => {
    getData().then(value => {
      setLoading(false);
      if (route.params?.step === 'calendar') {
        setTab(3);
      }
    });
  }, [route.params, user]);

  const onRefreshing = async () => {
    setRefreshing(true);
    await getData().then(value => {
      setRefreshing(false);
      setLoading(false);
    });
  };

  const renderContent = () => {
    switch (tab) {
      case 0:
        return (
          <Overview
            refreshing={isRefreshing}
            onRefresh={onRefreshing}
            setTab={setTab}
            data={workData}
            serviceData={serviceData}
            serviceId={route.params.item.ToiletId ?? route.params.item.Id}
          />
        );
      case 1:
        return (
          <Dashboard
            refreshing={isRefreshing}
            onRefresh={onRefreshing}
            data={workData}
            serviceData={serviceData}
          />
        );
      case 2:
        return (
          <Task
            refreshing={isRefreshing}
            onRefresh={onRefreshing}
            data={workData}
            serviceData={serviceData}
          />
        );
      case 3:
        return <CalendarTab data={workData} serviceData={serviceData} />;
      case 4:
        return <ToiletDeviceList toiletIds={serviceData.ToiletId.split(',')} />;
      case 5:
        return <ToiletBioList toiletIds={serviceData.ToiletId.split(',')} />;
      case 6:
        return (
          <FilesList
            refreshing={isRefreshing}
            onRefresh={onRefreshing}
            data={workData}
            serviceData={serviceData}
          />
        );
      case 7:
        return (
          <ContractToiletListTab
            refreshing={isRefreshing}
            onRefresh={onRefreshing}
            data={workData}
            serviceData={serviceData}
          />
        );
      case 8:
        return <NoteTab data={workData} serviceData={serviceData} />;
      default:
        return <EmptyPage />;
    }
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
      }}>
      <SafeAreaView style={{flex: 1}}>
        <FPopup ref={popupRef} />
        <HeaderDetailProject
          title={workData?.Name ?? '-'}
          popupRef={popupRef}
          tab={tab}
          setTab={setTab}
          workData={workData}
          setWorkData={setWorkData}
          setServiceData={setServiceData}
          serviceData={serviceData}
        />
        {isLoading ? (
          <PageSkeleton refreshing={isRefreshing} onRefresh={onRefreshing} />
        ) : (
          <View style={{flex: 1, height: '100%', paddingBottom: 16}}>
            {renderContent()}
          </View>
        )}
      </SafeAreaView>
    </View>
  );
}
