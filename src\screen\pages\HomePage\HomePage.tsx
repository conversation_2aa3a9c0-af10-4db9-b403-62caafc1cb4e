import {useNavigation} from '@react-navigation/native';
import {useState, useRef, useEffect} from 'react';
import {
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ConfigAPI from '../../../config/configApi';
import {
  useSelectorCustomerCompanyState,
  useSelectorCateServiceState,
} from '../../../redux/hooks/hooks';
import {RootScreen} from '../../../router/router';
import {DataController} from '../../base-controller';
import HeaderLogo from '../../layout/headers/HeaderLogo';
import SpecialProjects from '../../module/toilet/view/special-project';
import DefaultBanner from '../../module/banner/DefaultBanner';
import ServiceGrid from './components/ServiceGrid';
import HotProductsSection from '../../module/product/sections/HotProductsSection';

import PartnerSlider from './components/PartnerSlider';
import ListLatestNews from '../../module/news/ListLatestNews';
import {FPopup} from '../../../component/popup/popup';
import ManagerRequestServiceSection from '../../module/service/components/section/ManagerRequestServiceSection';
import ManageTicket from '../ToiletDetailPage/components/ManageTicket';
import {ToiletDa} from '../../module/toilet/toiletDa';
import {store} from '../../../redux/store/store';
import {NetZeroDa} from '../../module/service/Da/netzeroDa';
import {ServiceItem} from 'types/serviceType';

export default function HomePage() {
  const navigation = useNavigation<any>();
  const [isRefresh, setIsRefresh] = useState(false);
  const [projects, setProjects] = useState<Array<any>>([]);
  const [getAllTicket, setGetAllTicket] = useState<Array<any>>([]);
  const [getAnyTicket, setGetAnyTicket] = useState<Array<any>>([]);
  const cusId = store.getState().customer.data?.Id;
  const popupRef = useRef<any>();
  const company = useSelectorCustomerCompanyState().data;
  const serviceData = useSelectorCateServiceState().data;
  const toiletDa = new ToiletDa();
  const netZeroDa = new NetZeroDa();
  const cusType = store.getState().customer.data?.Type;
  const [serviceRequests, setServiceRequests] = useState<any[]>([]);
  const [allServiceRequests, setAllServiceRequests] = useState<any[]>([]);
  const getTicket = async () => {
    const res = await toiletDa.fetchALlTicketrByCustomer(cusId as string);
    if (res.code === 200) {
      setGetAnyTicket(res.data.slice(0, 5)); // Chỉ lấy 5 phần tử đầu tiên
      setGetAllTicket(res.data);
    }
  };
  const getAllDataServicesManage = async () => {
    try {
      if (!cusId || cusType === undefined) {
        return;
      }
      const res: any = await netZeroDa.getServiceManager(
        cusId as string,
        cusType as number,
      );
      if (res && res.code === 200 && res.data) {
        if (Array.isArray(res.data) && res.data.length > 0) {
          setAllServiceRequests(res.data); // Lưu tất cả data
          setServiceRequests(res.data.slice(0, 2)); // Chỉ hiển thị 2 item đầu
        } else {
          // Trường hợp data rỗng
          setAllServiceRequests([]);
          setServiceRequests([]);
        }
      } else {
        setAllServiceRequests([]);
        setServiceRequests([]);
      }
    } catch (error) {
      setAllServiceRequests([]);
      setServiceRequests([]);
    }
  };
  useEffect(() => {
    if (cusId) {
      getTicket();
      getAllDataServicesManage();
    }
  }, [cusId, cusType]);

  useEffect(() => {
    const newsController = new DataController(
      'Products',
      'dd757d35dbd245df843b1f54ada656ff',
    );
    newsController
      .aggregateList({
        page: 1,
        size: 4,
        searchRaw: '@Status:[2 2] @ParentId:{e486cddea9a546539b576b89f803340f}',
        sortby: [
          {prop: 'Sort', direction: 'DESC'},
          {prop: 'DateCreated', direction: 'DESC'},
        ],
      })
      .then(res => {
        if (res.code === 200) setProjects(res.data);
      });
  }, [isRefresh]);
  const handleSeeMore = () => {
    navigation.navigate(RootScreen.ProductListByCategory);
  };
  return (
    <View style={styles.container}>
      <FPopup ref={popupRef} />
      <HeaderLogo />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={async () => {
              setIsRefresh(true);
              setTimeout(() => {
                setIsRefresh(false);
              }, 2000);
            }}
          />
        }>
        {/* banner */}
        {company?.Id === ConfigAPI.ktxCompanyId ? null : <DefaultBanner />}
        {/* features */}
        {/* <AllServices /> */}
        {company?.Id === ConfigAPI.ktxCompanyId ? null : (
          <Pressable style={{marginVertical: 12}}>
            <ServiceGrid
              data={
                serviceData.filter((e: ServiceItem) => e.IsHome === true) || []
              }
            />
          </Pressable>
        )}
        {allServiceRequests && allServiceRequests.length > 0 && (
          <ManagerRequestServiceSection
            serviceRequests={serviceRequests || []}
            allServiceRequests={allServiceRequests || []}
          />
        )}
        {getAnyTicket && getAnyTicket.length > 0 && (
          <ManageTicket
            type={'all'}
            tickets={getAnyTicket || []}
            allTickets={getAllTicket || []}
          />
        )}

        <HotProductsSection
          key={`HotProductsSection`} // Force re-render on refresh
          pageSize={10}
          onSeeAll={handleSeeMore}
          onRefresh={isRefresh}
        />
        {/* Dự án tiêu biểu */}
        <SpecialProjects />
        {/* Đối tác uy tín */}
        <PartnerSlider />
        {/* Tin tức mới */}
        <ListLatestNews
          isRefresh={false}
          isLoadMore={false}
          onLoadMoreEnd={function (): void {
            throw new Error('Function not implemented.');
          }}
        />

        <View style={{height: 30}} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  contentContainer: {
    padding: 16,
    flex: 1,
  },
});
