import {useState, useCallback} from 'react';
import {DataController} from '../../../../base-controller';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {OrderItem, ApiResponse, PAGE_SIZE, UseOrderDataReturn} from '../types';

export const useOrderData = (): UseOrderDataReturn => {
  const [data, setData] = useState<OrderItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMoreData, setHasMoreData] = useState<boolean>(true);

  const orderController = new DataController('OrderProduct');
  const ratingController = new DataController('Rating');
  const CustomerInfo = useSelectorCustomerState().data;

  const getData = useCallback(
    async (status: number, page: number = 1, isRefresh: boolean = false) => {
      try {
        const response: ApiResponse = await orderController.getPatternList({
          page: page,
          size: PAGE_SIZE,
          query: `@CustomerId: {${CustomerInfo?.Id}} @Status: [${status}]`,
          pattern: {
            CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
            ShopId: ['Id', 'Name', 'Avatar'],
            AddressId: ['Id', 'Address'],
          },
        });
        if (response.code === 200) {
          // Add shop item by response.Shop
          const processedData: OrderItem[] = response.data.map((item: any) => {
            item.Shop = response.Shop?.find(
              (shop: any) => shop.Id === item.ShopId,
            );
            return item;
          });

          // Handle rating for completed orders
          if (status === 3) {
            const resRated = await ratingController.getListSimple({
              page: page,
              size: PAGE_SIZE,
              query: `@OrderId: {${processedData
                .map((item: any) => item.Id)
                .join(' | ')}}`,
            });

            if (resRated.code === 200) {
              resRated.data.forEach((ratingItem: any) => {
                const order = processedData.find(
                  (order: any) => order.Id === ratingItem.OrderId,
                );
                if (order) {
                  order.isRated = true;
                }
              });
            }
          }

          if (isRefresh || page === 1) {
            setData(processedData);
            setCurrentPage(1);
          } else {
            setData(prevData => [...prevData, ...processedData]);
          }

          // Check if there's more data to load
          setHasMoreData(processedData.length === PAGE_SIZE);
          setCurrentPage(page);
        }
      } catch (error) {
        console.error('Error fetching order data:', error);
      } finally {
        setIsLoading(false);
        setLoadingMore(false);
      }
    },
    [orderController, ratingController, CustomerInfo?.Id],
  );

  const loadMoreData = useCallback(async () => {
    if (!loadingMore && hasMoreData) {
      setLoadingMore(true);
      // Note: This requires the status to be passed from the component
      // We'll handle this in the main component
    }
  }, [loadingMore, hasMoreData]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      setHasMoreData(true);
      // Note: This requires the status to be passed from the component
      // We'll handle this in the main component
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  return {
    data,
    isLoading,
    refreshing,
    loadingMore,
    hasMoreData,
    currentPage,
    getData,
    loadMoreData,
    onRefresh,
  };
};
