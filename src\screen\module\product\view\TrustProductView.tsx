import { useNavigation } from '@react-navigation/native';
import { useState, useRef, useEffect, forwardRef } from 'react';
import { useForm } from 'react-hook-form';
import {
    View,
    ScrollView,
    Text,
    SafeAreaView,
    Dimensions,
    FlatList,
    TouchableOpacity,
    RefreshControl,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { ColorThemes } from '../../../../assets/skin/colors';
import { TypoSkin } from '../../../../assets/skin/typography';
import AppButton from '../../../../component/button';
import { ComponentStatus } from '../../../../component/component-status';
import {
    showSnackbar,
    FTextField,
    Winicon,
    FCheckbox,
} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import { FPopup, showPopup, closePopup } from '../../../../component/popup/popup';
import {
    useSelectorCustomerState,
    useSelectorCustomerCompanyState,
} from '../../../../redux/hooks/hooks';
import { DataController } from '../../../base-controller';
import WScreenFooter from '../../../layout/footer';
import ScreenHeader from '../../../layout/header';
import TrustProductCard from '../component/card/TrustProductCard';
import { CardToiletHoriSkeleton } from '../../../../project-component/skeletonCard';
import EmptyPage from '../../../../project-component/empty-page';

export default function TrustProductView() {
    const navigation = useNavigation<any>();
    const user = useSelectorCustomerState().data;
    const company = useSelectorCustomerCompanyState().data;
    const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
    const dispatch = useDispatch<any>();
    const [step, setStep] = useState(0);
    const [category, setCategory] = useState<any>([]);
    const [listBrand, setListBrand] = useState<any>([]);
    const [products, setProducts] = useState({ data: [], totalCount: undefined });
    const now = new Date();
    const popupRef = useRef<any>();

    const [isLoading, setLoading] = useState(false);
    const [isRefreshing, setRefreshing] = useState(false);
    const filterMethods = useForm<any>({
        shouldFocusError: false,
        defaultValues: { CategoryId: [] },
    });
    const [searchValue, setSearchValue] = useState('');

    const getData = async () => {
        setProducts({ data: [], totalCount: undefined });
        setLoading(true);
        const _controller = new DataController('Product');
        let searchQuery = [];
        if (filterMethods.getValues('CategoryId')?.length)
            searchQuery.push(
                `@CategoryId:{${filterMethods.getValues('CategoryId').join(' | ')}}`,
            );
        if (searchValue != '') searchQuery.push(`@Name:(*${searchValue}*)`);

        const res = await _controller.aggregateList({
            page: 1,
            size: 1000,
            searchRaw: searchQuery.length ? searchQuery.join(' ') : '*',
        });
        if (res.code !== 200) {
            setLoading(false);
            return showSnackbar({
                message: res.message,
                status: ComponentStatus.ERROR,
            });
        }
        setProducts({ data: res.data, totalCount: res.totalCount });
        if (res.data.length) {
            const brandController = new DataController('Brands');
            const brandIds = res.data
                .filter((e: any) => e.BrandsId?.length)
                .map((e: any) => e.BrandsId.split(','))
                .flat(Infinity)
                .filter(
                    (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
                );
            const brandRes = await brandController.getByListId(brandIds);
            if (brandRes.code === 200) setListBrand(brandRes.data);
        }
        setLoading(false);
    };

    useEffect(() => {
        const cateController = new DataController('Category');
        cateController.getAll().then(async res => {
            if (res.code === 200) {
                const productController = new DataController('Product');
                const count = await productController.group({
                    searchRaw: '*',
                    reducers: 'GROUPBY 1 @CategoryId REDUCE COUNT 0 AS _count',
                });
                if (count.code === 200)
                    setCategory(
                        res.data.map((e: any) => ({
                            ...e,
                            _count: count.data.find((f: any) => f.CategoryId === e.Id)
                                ?._count,
                        })),
                    );
                else setCategory(res.data);
            }
        });
    }, []);

    useEffect(() => {
        getData().then(v => setLoading(false));
    }, [filterMethods.getValues('CategoryId')?.length, searchValue]);

    const [isOpen, setIsOpen] = useState<Array<any>>([]);

    return (
        <View style={{ flex: 1 }}>
            <FPopup ref={popupRef} />
            <ScreenHeader
                style={{ paddingTop: 8 }}
                title={'Sản phẩm'}
                bottom={
                    <View
                        style={{
                            flexDirection: 'row',
                            width: '100%',
                            paddingHorizontal: 16,
                            gap: 8,
                            paddingBottom: 16,
                        }}>
                        <FTextField
                            style={{ paddingHorizontal: 16, flex: 1, height: 40 }}
                            onChange={vl => {
                                setSearchValue(vl.trim());
                            }}
                            value={searchValue}
                            placeholder="Tìm kiếm"
                            prefix={
                                <Winicon
                                    src="outline/development/zoom"
                                    size={14}
                                    color={ColorThemes.light.neutral_text_subtitle_color}
                                />
                            }
                        />
                        <AppButton
                            onPress={() => {
                                showPopup({
                                    ref: popupRef,
                                    enableDismiss: true,
                                    children: (
                                        <PopupFilter
                                            ref={popupRef}
                                            filterMethods={filterMethods}
                                            listStatus={category}
                                            selectedAttributes={
                                                filterMethods.watch('CategoryId') ?? []
                                            }
                                            onApply={CategoryIds => {
                                                filterMethods.setValue('CategoryId', CategoryIds);
                                            }}
                                        />
                                    ),
                                });
                            }}
                            backgroundColor={ColorThemes.light.transparent}
                            borderColor={
                                filterMethods.watch('CategoryId')?.length
                                    ? ColorThemes.light.primary_main_color
                                    : ColorThemes.light.neutral_main_border_color
                            }
                            containerStyle={{
                                height: 40,
                                borderRadius: 8,
                                paddingHorizontal: 8,
                            }}
                            prefixIconSize={18}
                            prefixIcon={'outline/user interface/setup-preferences'}
                            textColor={
                                filterMethods.watch('CategoryId')?.length
                                    ? ColorThemes.light.primary_main_color
                                    : ColorThemes.light.neutral_text_subtitle_color
                            }
                            title={'Phân loại'}
                            textStyle={TypoSkin.subtitle3}
                        />
                    </View>
                }
            />
            <FlatList
                data={category}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefreshing}
                        onRefresh={() => {
                            setRefreshing(true);
                            getData().then(() => {
                                setLoading(false);
                                setRefreshing(false);
                            });
                        }}
                    />
                }
                style={{ flex: 1, marginHorizontal: 16, marginVertical: 16 }}
                renderItem={
                    ({ item, index }: any) => {
                        return (
                            <ListTile
                                key={item.Id}
                                onPress={() => {
                                    if (isOpen.includes(item.Id)) {
                                        setIsOpen(isOpen.filter((e: any) => e !== item.Id));
                                    } else {
                                        setIsOpen([...isOpen, item.Id]);
                                    }
                                }}
                                trailing={
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            gap: 8,
                                            alignItems: 'center',
                                        }}>
                                        {isOpen.includes(item.Id) ? (
                                            <Winicon src={'outline/arrows/arrow-sm-down'} size={32} />
                                        ) : (
                                            <Winicon src={'fill/arrows/arrow-sm-right'} size={32} />
                                        )}
                                    </View>
                                }
                                title={
                                    <Text
                                        style={[
                                            TypoSkin.title3,
                                            { color: ColorThemes.light.neutral_text_title_color },
                                        ]}>
                                        {item?.Name ?? '-'} {`${products?.data?.filter((e: any) => e.CategoryId === item.Id)?.length ? `(${products.data.filter((e: any) => e.CategoryId === item.Id)?.length})` : ''}`}
                                    </Text>
                                }
                                listtileStyle={{ gap: 8 }}
                                style={{
                                    padding: 0,
                                    paddingVertical: 8,
                                    backgroundColor: ColorThemes.light.transparent,
                                    borderBottomColor:
                                        ColorThemes.light.neutral_main_border_color,
                                    borderBottomWidth: 1,
                                }}
                                bottom={
                                    <View
                                        style={{
                                            marginTop:
                                                isOpen.includes(item.Id) &&
                                                    products.data.filter(
                                                        (e: any) => e.CategoryId === item.Id,
                                                    )?.length > 0
                                                    ? 16
                                                    : 0,
                                            alignContent: 'flex-start',
                                            width: '100%',
                                            backgroundColor:
                                                ColorThemes.light.neutral_main_background_color,
                                        }}>
                                        {isOpen.includes(item.Id) &&
                                            products.data.filter((e: any) => e.CategoryId === item.Id)
                                                ?.length > 0
                                            ? products.data
                                                .filter((e: any) => e.CategoryId === item.Id)
                                                ?.map((item: any, index: number) => {
                                                    return (
                                                        <TrustProductCard
                                                            key={index}
                                                            index={index}
                                                            item={item}
                                                            listBrand={listBrand}
                                                        />
                                                    );
                                                })
                                            : null}
                                    </View>
                                }
                            />
                        );
                    }
                    // renderItem={({ item, index }: any) => <TrustProductCard key={index} index={index} item={item} listBrand={listBrand} />
                }
                ListEmptyComponent={() =>
                    isLoading ? (
                        Array.from(Array(10)).map((_, index) => (
                            <View key={index} style={{ gap: 16 }}>
                                <CardToiletHoriSkeleton />
                            </View>
                        ))
                    ) : (
                        <EmptyPage />
                    )
                }
                ListFooterComponent={() => <View style={{ height: 100 }} />}
            />
        </View>
    );
}

const PopupFilter = forwardRef(function PopupFilter(
    data: {
        filterMethods: any;
        onApply: (values: Array<any>) => void;
        selectedAttributes: [];
        listStatus: any;
    },
    ref: any,
) {
    const { onApply, selectedAttributes, listStatus, filterMethods } = data;
    const [selected, setSelected] = useState<Array<any>>([]);

    useEffect(() => {
        console.log(selectedAttributes);

        if (selectedAttributes?.length) setSelected(selectedAttributes);
    }, [selectedAttributes]);

    return (
        <SafeAreaView
            style={{
                width: '100%',
                height: Dimensions.get('window').height - 146,
                borderTopLeftRadius: 12,
                borderTopRightRadius: 12,
                backgroundColor: '#fff',
            }}>
            <ScreenHeader
                style={{
                    backgroundColor: ColorThemes.light.transparent,
                    borderBottomColor: ColorThemes.light.neutral_main_background_color,
                    borderBottomWidth: 0.5,
                    shadowColor: 'rgba(0, 0, 0, 0.03)',
                    shadowOffset: {
                        width: 0,
                        height: 4,
                    },
                    shadowRadius: 20,
                    elevation: 20,
                    shadowOpacity: 1,
                }}
                title={`Loại sản phẩm`}
                prefix={<View style={{ width: 50 }} />}
                action={
                    <View
                        style={{ flexDirection: 'row', padding: 12, alignItems: 'center' }}>
                        <Winicon
                            src="outline/layout/xmark"
                            onClick={() => closePopup(ref)}
                            size={20}
                            color={ColorThemes.light.neutral_text_body_color}
                        />
                    </View>
                }
            />
            <View style={{ flex: 1, paddingHorizontal: 16, paddingVertical: 16 }}>
                <Text
                    style={{
                        ...TypoSkin.label3,
                        marginBottom: 8,
                    }}>{`Phân loại sản phẩm:`}</Text>
                <FlatList
                    data={listStatus.filter((e: any) => !e.ParentId)}
                    renderItem={({ item, index }) => {
                        const children = listStatus.filter(
                            (e: any) => e.ParentId === item.Id,
                        );
                        return (
                            <CateTile
                                item={item}
                                children={children}
                                setSelected={setSelected}
                                selected={selected}
                            />
                        );
                    }}
                    style={{ width: '100%' }}
                    keyExtractor={(item, index) => index.toString()}
                    ListFooterComponent={() => <View style={{ height: 100 }} />}
                />
            </View>
            <WScreenFooter
                style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16 }}>
                <AppButton
                    title={'Làm mới'}
                    backgroundColor={ColorThemes.light.neutral_main_background_color}
                    borderColor="transparent"
                    containerStyle={{
                        height: 40,
                        flex: 1,
                        borderRadius: 8,
                        paddingHorizontal: 12,
                        paddingVertical: 5,
                    }}
                    onPress={() => {
                        setSelected([]);
                    }}
                    textColor={ColorThemes.light.neutral_text_subtitle_color}
                />
                <AppButton
                    title={'Áp dụng'}
                    backgroundColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{
                        height: 40,
                        flex: 1,
                        borderRadius: 8,
                        paddingHorizontal: 12,
                        paddingVertical: 5,
                    }}
                    onPress={() => {
                        closePopup(ref);
                        onApply(selected);
                    }}
                    textColor={ColorThemes.light.neutral_absolute_background_color}
                />
            </WScreenFooter>
        </SafeAreaView>
    );
});

const CateTile = ({
    item,
    children,
    selected,
    setSelected,
}: {
    item: any;
    children?: Array<any>;
    selected: Array<any>;
    setSelected: any;
}) => {
    const [isOpen, setIsOpen] = useState(true);

    return (
        <ListTile
            key={item.Id}
            onPress={
                item.ParentId
                    ? undefined
                    : () => {
                        setIsOpen(!isOpen);
                    }
            }
            leading={
                <View style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
                    <Winicon
                        src={
                            isOpen
                                ? 'fill/arrows/triangle-down'
                                : 'fill/arrows/triangle-right'
                        }
                        size={16}
                    />
                </View>
            }
            title={
                <TouchableOpacity
                    onPress={() => {
                        if (children) {
                            if (!selected.includes(item.Id)) {
                                setSelected([
                                    ...selected,
                                    item.Id,
                                    ...children
                                        .filter(e => !selected.includes(e.Id))
                                        .map(e => e.Id),
                                ]);
                            } else {
                                setSelected([
                                    selected.filter(
                                        id => id !== item.Id && !children.some(e => e.Id === id),
                                    ),
                                ]);
                            }
                        }
                    }}
                    style={{
                        gap: 8,
                        flexDirection: 'row',
                        alignSelf: 'baseline',
                        alignItems: 'center',
                    }}>
                    <FCheckbox
                        value={
                            selected.includes(item?.Id) ||
                                (children?.every(e => selected.includes(e.Id)) &&
                                    children?.length)
                                ? true
                                : false
                        }
                        onChange={v => {
                            if (children) {
                                if (v) {
                                    setSelected([
                                        ...selected,
                                        item.Id,
                                        ...children
                                            .filter(e => !selected.includes(e.Id))
                                            .map(e => e.Id),
                                    ]);
                                } else {
                                    setSelected([
                                        selected.filter(
                                            id => id !== item.Id && !children.some(e => e.Id === id),
                                        ),
                                    ]);
                                }
                            }
                        }}
                    />
                    <Text
                        style={[
                            TypoSkin.heading7,
                            { color: ColorThemes.light.neutral_text_title_color },
                        ]}>
                        {item?.Name ?? '-'}
                    </Text>
                </TouchableOpacity>
            }
            listtileStyle={{ gap: 8 }}
            style={{
                padding: 0,
                paddingVertical: 8,
                paddingLeft: item.ParentId ? 32 : 0,
            }}
            bottom={
                <View
                    style={{
                        marginTop: 8,
                        alignContent: 'flex-start',
                        width: '100%',
                    }}>
                    {isOpen
                        ? children?.map((e: any) => {
                            console.log(e);

                            return (
                                <ListTile
                                    key={e.Id}
                                    title={
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (!selected.includes(e.Id))
                                                    setSelected([...selected, e.Id]);
                                                else
                                                    setSelected(
                                                        selected.filter((id: any) => id !== e.Id),
                                                    );
                                            }}
                                            style={{
                                                gap: 8,
                                                flexDirection: 'row',
                                                alignSelf: 'baseline',
                                                alignItems: 'center',
                                            }}>
                                            <FCheckbox
                                                value={selected.includes(e.Id)}
                                                onChange={v => {
                                                    if (v) setSelected([...selected, e.Id]);
                                                    else
                                                        setSelected(
                                                            selected.filter((id: any) => id !== e.Id),
                                                        );
                                                }}
                                            />
                                            <Text
                                                style={[
                                                    TypoSkin.heading7,
                                                    { color: ColorThemes.light.neutral_text_title_color },
                                                ]}>{`${e?.Name ?? '-'} (${e?._count ?? 0})`}</Text>
                                        </TouchableOpacity>
                                    }
                                    listtileStyle={{ gap: 8 }}
                                    style={{
                                        padding: 0,
                                        paddingVertical: 8,
                                        paddingLeft: 32,
                                    }}
                                />
                            );
                        })
                        : null}
                </View>
            }
        />
    );
};
