import {forwardRef, useCallback, useEffect, useState} from 'react';
import OTPInput from '../screen/module/login/components/input-otp';
import {ComponentStatus} from '../component/component-status';
import {showSnackbar, Winicon} from '../component/export-component';
import {CustomerActions} from '../redux/reducers/user/reducer';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {closePopup} from '../component/popup/popup';
import {ColorThemes} from '../assets/skin/colors';
import {TypoSkin} from '../assets/skin/typography';
import React from 'react';
import LocalAuthen from '../features/local-authen/local-authen';
import {RootScreen} from '../router/router';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../utils/AsyncStorage';
import AppButton from '../component/button';
import FLoading from 'component/Loading/FLoading';

export const PopupCheckOtp = React.forwardRef(function PopupCheckOtp(
  data: {
    phone: string;
    isLoading: any;
    setLoading: any;
    onSuccess: any;
    usingBio?: boolean;
  },
  ref: any,
) {
  const [allowErrorOpt, setAllowErrorOpt] = useState(0);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [biometric, setBiometric] = useState(false);
  const [timer, setTimer] = useState(0);
  const timeOutCallback = useCallback(() => {
    setTimer(currTimer => currTimer - 1);
  }, []);

  useEffect(() => {
    if (timer > 0) setTimeout(timeOutCallback, 1000);
  }, [timer, timeOutCallback]);
  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<any>(null);

  const {phone, onSuccess, isLoading, usingBio = true, setLoading} = data;
  const [password, setPassword] = useState('');
  const [isVisiblePass, setVisiblePass] = useState(true);
  useEffect(() => {
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          setBiometric(result == 'true' ? true : false);
        });
      } else {
        setBiometric(false);
      }
    });
  }, []);

  // const _submitOtp = async (value: string) => {
  //   // if (__DEV__) {
  //   //     onSuccess()
  //   //     setLoading(false)
  //   //     closePopup(ref)
  //   //     return
  //   // }
  //   setLoading(true);

  //   if (confirm) {
  //     // done
  //     var rsotp = await confirmCode(confirm, value);
  //     if (rsotp === true) {
  //       console.log('===============otp done=====================');
  //       onSuccess();
  //       setLoading(false);
  //       setAllowErrorOpt(0);
  //       closePopup(ref);
  //     } else {
  //       if (allowErrorOpt < 5) {
  //         setAllowErrorOpt(allowErrorOpt + 1);
  //       } else {
  //         const r = await CustomerActions.lockAccount(phone);
  //         setTimeout(() => {
  //           CustomerActions.logout(dispatch, navigation);
  //         }, 2000);
  //         return showSnackbar({
  //           message: r.message,
  //           status: ComponentStatus.ERROR,
  //         });
  //       }
  //       setLoading(false);

  //       showSnackbar({
  //         message:
  //           5 - (allowErrorOpt + 1) == 0
  //             ? 'Mã xác thực không chính xác'
  //             : `Mã xác thực không chính xác, bạn còn ${5 - (allowErrorOpt + 1)} lần nhập lại`,
  //         status: ComponentStatus.ERROR,
  //       });
  //     }
  //   } else {
  //     // fail
  //     setLoading(false);
  //   }
  // };

  // const _resendOTP = async () => {
  //   var phoneNB = phone;
  //   if (phoneNB.startsWith('0')) phoneNB = phoneNB.replace('0', '+84');
  //   const confirm = await signInWithPhoneFB(phoneNB);
  //   if (confirm) {
  //     showSnackbar({
  //       message: 'Đã gửi mã xác thực đến số diện thoại đăng ký',
  //       status: ComponentStatus.SUCCSESS,
  //     });
  //   } else {
  //   }
  // };

  const handlePasswordConfirm = async () => {
    if (!password.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    if (!phone) {
      showSnackbar({
        message: 'Không tìm thấy số diện thoại',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    setLoading(true);
    try {
      const res = await CustomerActions.checkPassword(phone || '', password);

      if (res?.code === 200) {
        console.log('===============pass done=====================');
        onSuccess();
        setLoading(false);
        setAllowErrorOpt(0);
        closePopup(ref);
      } else {
        if (allowErrorOpt < 5) {
          setAllowErrorOpt(allowErrorOpt + 1);
        } else {
          const r = await CustomerActions.lockAccount(phone);
          setTimeout(() => {
            CustomerActions.logout(dispatch, navigation);
          }, 2000);
          return showSnackbar({
            message: r.message,
            status: ComponentStatus.ERROR,
          });
        }
        setLoading(false);

        showSnackbar({
          message:
            5 - (allowErrorOpt + 1) == 0
              ? 'Mã xác thực không chính xác'
              : `Mã xác thực không chính xác, bạn còn ${5 - (allowErrorOpt + 1)} lần nhập lại`,
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
    }
  };

  return (
    <View
      style={{
        position: 'absolute',
        bottom: 265,
      }}>
      <View
        style={{
          height: undefined,
          gap: 26,
          margin: 24,
          alignContent: 'center',
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          borderRadius: 20,
          paddingHorizontal: 24,
          paddingVertical: 24,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View style={{paddingHorizontal: 12}} />
          <Text
            style={{
              ...TypoSkin.title3,
              color: ColorThemes.light.neutral_absolute_background_color,
            }}>
            Xác nhận mật khẩu
          </Text>
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{
              flexDirection: 'row',
              paddingHorizontal: 12,
              alignItems: 'center',
            }}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        </View>
        {/* <OTPInput
          autoFocus={false}
          isResend={false}
          length={6}
          disabled={allowErrorOpt > 5}
          onReSendOtp={() => _resendOTP()}
          onSubmit={_submitOtp}
        /> */}
        {/* <AppButton
          title={
            loadingOTP
              ? 'Đang gửi mã...'
              : `Gửi mã xác thực ${timer > 0 ? timer : ''}${timer > 0 ? 's' : ''}`
          }
          disabled={allowErrorOpt > 5 || loadingOTP || timer > 0}
          textColor={ColorThemes.light.neutral_absolute_background_color}
          textStyle={{...TypoSkin.buttonText1}}
          containerStyle={{borderRadius: 8}}
          borderColor={ColorThemes.light.neutral_main_border_color}
          backgroundColor={ColorThemes.light.primary_main_color}
          onPress={async () => {
            if (loadingOTP) return;
            var phoneNB = phone;
            // Check if the number doesn't already start with 0 or +84
            if (!/^(\+84|0)/.test(phoneNB)) {
              phoneNB = '0' + phoneNB; // Add 0 at the beginning
            }

            if (phoneNB.startsWith('0')) phoneNB = phoneNB.replace('0', '+84');

            setLoadingOTP(true);
            const rs = await signInWithPhoneFB(phoneNB);
            if (rs) {
              setLoadingOTP(false);
              setTimer(60);
              showSnackbar({
                message: 'Đã gửi mã xác nhận đến số diện thoại đăng ký',
                status: ComponentStatus.SUCCSESS,
              });
              setConfirm(rs);
            } else {
              setLoadingOTP(false);
            }
          }}
        /> */}
        {/* add check password */}
        <View
          style={{
            borderWidth: 1,
            borderColor: ColorThemes.light.neutral_text_subtitle_color,
            borderRadius: 8,
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
          <TextInput
            style={{
              flex: 1,
              height: 48,
              paddingHorizontal: 16,
              ...TypoSkin.body2,
              color: ColorThemes.light.neutral_text_body_color,
            }}
            placeholder="Nhập mật khẩu của bạn"
            placeholderTextColor={ColorThemes.light.neutral_text_disabled_color}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={isVisiblePass}
            autoFocus={true}
          />
          <TouchableOpacity
            style={{padding: 12}}
            onPress={() => setVisiblePass(!isVisiblePass)}>
            <Winicon
              src={
                isVisiblePass
                  ? `outline/user interface/view`
                  : `outline/user interface/hide`
              }
              size={14}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
          </TouchableOpacity>
        </View>

        <View
          style={{
            flexDirection: 'row',
            gap: 12,
          }}>
          <TouchableOpacity
            style={{
              flex: 1,
              height: 48,
              backgroundColor: ColorThemes.light.primary_main_color,
              borderRadius: 8,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={handlePasswordConfirm}>
            <Text
              style={{
                ...TypoSkin.buttonText3,
                color: ColorThemes.light.neutral_absolute_background_color,
              }}>
              Xác nhận
            </Text>
          </TouchableOpacity>
        </View>
        {/*  */}
        {biometric && usingBio ? (
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <LocalAuthen
              sizeIcon={42}
              isFirstTime={false}
              onSuccess={async value => {
                if (value === true) {
                  setLoading(false);
                  onSuccess();
                  setAllowErrorOpt(0);
                  closePopup(ref);
                  return;
                }
              }}
            />
          </View>
        ) : null}
      </View>
    </View>
  );
});
