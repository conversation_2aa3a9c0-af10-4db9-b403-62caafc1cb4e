import {View} from 'react-native';
import WebView from 'react-native-webview';
import FLoading from '../../../component/Loading/FLoading';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../redux/hooks/hooks';
import {useState, useMemo, useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {regexGetVariables} from '../../../utils/Utils';
import ListTile from '../../../component/list-tile/list-tile';
import {DataController} from '../../base-controller';
import TitleHeader from '../../layout/headers/TitleHeader';

export default function PolicyView() {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
  const dispatch = useDispatch<any>();
  const [step, setStep] = useState(0);
  const [policyData, setPolicyData] = useState<any>();
  const [data, setData] = useState<Array<any>>([]);
  const now = new Date();

  const htmlContent = useMemo(() => {
    if (!policyData || !user) return '';
    return policyData?.Content.replace(
      regexGetVariables,
      (m: any, key: any) => {
        switch (key) {
          case 'CustomerName':
            return user.Name;
          case 'CustomerAddress':
            return user.Address;
          case 'CustomerTaxCode':
            return company?.TaxCode;
          case 'CustomerRepresentative':
            return company?.Representative ?? user.Name;
          case 'CustomerPosition':
            return company?.Position;
          case 'CustomerEmail':
            return company?.Email;
          case 'CustomerMobile':
            return company?.Mobile;
          case 'KTXRepresentative':
            return ktxgroup?.Representative ?? '';
          case 'KTXPosition':
            return ktxgroup?.Position ?? '';
          case 'day':
            return now.getDate();
          case 'month':
            return now.getMonth() + 1;
          case 'year':
            return now.getFullYear();
          default:
            return m;
        }
      },
    );
  }, [policyData, user]);

  useEffect(() => {
    const policyController = new DataController('Policy');
    policyController.getListSimple({page: 1, size: 10}).then(res => {
      if (res.code === 200 && res.data.length) setData(res.data);
    });
  }, []);

  return (
    <View style={{flex: 1}}>
      <TitleHeader title="Chính sách" />
      {step ? (
        <WebView
          originWhitelist={['*']}
          source={{html: policyData?.Content}}
          style={{flex: 1, width: '100%', height: '100%', paddingHorizontal: 4}}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          onLoad={() => console.log('WebView loaded')}
          renderLoading={() => <FLoading visible={true} />}
          onError={syntheticEvent => {
            const {nativeEvent} = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
          }}
        />
      ) : (
        <View style={{flex: 1}}>
          {data
            .filter((e: any) => e.Url !== '/partner')
            .map((item: any, index: number) => (
              <ListTile
                key={index}
                title={item?.Name}
                onPress={() => {
                  setStep(1);
                  setPolicyData(data.find((e: any) => e.Id === item.Id));
                }}
              />
            ))}
        </View>
      )}
    </View>
  );
}
