import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {DataController} from '../../../../../../base-controller';
import {BaseDA} from '../../../../../../baseDA';
import {showSnackbar} from '../../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../../component/component-status';
import {DeviceBioStatus} from '../../../../../service/components/da';
import {randomGID} from '../../../../../../../utils/Utils';

type UseListBioParams = {
  toiletId?: string;
  formId?: any;
};

type DeviceListState = {
  data: Array<any>;
  totalCount?: number;
};

function parseImgIds(raw?: string | string[]): string[] {
  if (!raw) return [];
  if (Array.isArray(raw)) return raw.filter(Boolean);
  return raw
    .split(',')
    .map(s => s?.trim())
    .filter(Boolean);
}

export function useListBioTabBottomSheet({toiletId, formId}: UseListBioParams) {
  const [devices, setDevices] = useState<DeviceListState>({data: []});
  const [isLoading, setLoading] = useState(false);
  const [products, setProducts] = useState<Array<any>>([]);
  const [files, setFiles] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');

  const fetchFilesInfo = useCallback(
    async (fileIds: string[]) => {
      if (!fileIds?.length) return;
      const uniqueIds = Array.from(
        new Set(
          fileIds.filter(
            id => id && !id.startsWith('http') && files.every(f => f.Id !== id),
          ),
        ),
      );
      if (!uniqueIds.length) return;
      const res = await BaseDA.getFilesInfor(uniqueIds);
      if (res.code === 200) {
        setFiles(prev => {
          const next = [...prev, ...res.data.filter((e: any) => e)]
            // de-duplicate by Id
            .filter((e, i, a) => a.findIndex(v => v.Id === e.Id) === i);
          return next;
        });
      }
    },
    [files],
  );

  const getData = useCallback(
    async ({page, size}: {page?: number; size?: number}) => {
      if (!toiletId) return;
      setLoading(true);
      const bioController = new DataController('BioProduct');
      let query = `@ToiletId:{${toiletId}}`;
      if (searchValue?.length) query += ` @Name:(*${searchValue}*)`;

      const res = await bioController.aggregateList({
        page: page ?? 1,
        size: size ?? 20,
        searchRaw: query,
      });
      if (res.code === 200) {
        setDevices({data: res.data, totalCount: res.totalCount});

        const productIds = res.data
          .filter((e: any) => e?.ProductId)
          .map((e: any) => e?.ProductId)
          .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);

        const imageIdsFromDevices = res.data
          .map((e: any) => parseImgIds(e?.Img))
          .flat();

        if (productIds?.length) {
          const productController = new DataController('Product');
          const productRes = await productController.getByListId(productIds);
          if (productRes.code === 200) {
            setProducts(productRes.data);
            const imageIdsFromProducts = productRes.data
              .map((e: any) => parseImgIds(e?.Img))
              .flat();
            await fetchFilesInfo([
              ...imageIdsFromDevices,
              ...imageIdsFromProducts,
            ]);
          } else {
            await fetchFilesInfo(imageIdsFromDevices);
          }
        } else {
          await fetchFilesInfo(imageIdsFromDevices);
        }
      }
      setLoading(false);
    },
    [toiletId, searchValue, fetchFilesInfo],
  );

  useEffect(() => {
    if (toiletId) {
      getData({});
    }
  }, [toiletId, getData]);

  const deleteItems = useCallback(
    async (ids: string[]) => {
      if (!ids?.length) return;
      const controller = new DataController('BioProduct');
      const res = await controller.delete(ids);
      if (res.code === 200) {
        setDevices(prev => ({
          data: prev.data.filter((e: any) => !ids.includes(e.Id)),
          totalCount: (prev.totalCount ?? 0) - ids.length,
        }));
        showSnackbar({
          message: 'Đã xóa thành công',
          status: ComponentStatus.SUCCSESS,
        });
        // refresh list to ensure consistency (e.g., pagination server-side changes)
        getData({});
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
      }
    },
    [getData],
  );

  const addDevicesFromProducts = useCallback(
    async (selectedProducts: any[]) => {
      if (!toiletId || !selectedProducts?.length) return;
      const controller = new DataController('BioProduct');
      const newListDevice = selectedProducts.map((p: any) => ({
        Id: randomGID(),
        Name: p.Name,
        DateCreated: Date.now(),
        ToiletId: toiletId,
        ProductId: p.Id,
        Quantity: p['_Quantity'],
        Price: p.Price,
        Unit: p.Unit,
        Img: p.Img,
        Description: p.Description,
        Specifications: p.Specifications,
        Status: formId ? DeviceBioStatus.inactive : DeviceBioStatus.active,
      }));
      const res = await controller.add(newListDevice);
      if (res.code === 200) await getData({});
    },
    [toiletId, formId, getData],
  );

  return {
    devices,
    isLoading,
    products,
    files,
    searchValue,
    setSearchValue,
    getData,
    deleteItems,
    addDevicesFromProducts,
  };
}
