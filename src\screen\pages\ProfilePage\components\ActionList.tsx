import React from 'react';
import {View, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import ListTile from '../../../../component/list-tile/list-tile';
import {Winicon} from 'wini-mobile-components';
import BiometricsView from './BiometricsView';
import AppSvg from '../../../../component/AppSvg';

interface ActionItem {
  id: number;
  name: string;
  icon: string;
  svg?: string;
  background: string;
  colorIcon: string;
  route?: string;
  action?: string;
  show?: boolean;
  type?: string;
}

interface ActionListProps {
  actions: ActionItem[];
  customer: any;
  onActionPress: (item: ActionItem) => void;
}

const ActionList: React.FC<ActionListProps> = ({
  actions,
  customer,
  onActionPress,
}) => {
  const filteredActions = !customer
    ? actions.filter(item => item.show)
    : actions;

  const renderItem = (item: ActionItem) => {
    if (item.action === 'biometric') {
      return (
        <View
          key={item.id}
          style={{
            borderBottomColor: ColorThemes.light.primary_background,
            borderBottomWidth: 1,
          }}>
          <BiometricsView />
        </View>
      );
    }
    return (
      <ListTile
        key={item.id}
        style={styles.listTileContainer}
        listtileStyle={styles.listTileStyle}
        onPress={() => onActionPress(item)}
        leading={
          <View style={[styles.iconContainer]}>
            {item.svg ? (
              <AppSvg SvgSrc={item.svg} size={20} color="#00474F" />
            ) : (
              <Winicon src={item.icon} color={item.colorIcon} size={20} />
            )}
          </View>
        }
        title={
          !customer && item.action === 'logout' ? 'Về đăng nhập' : item.name
        }
        titleStyle={[
          styles.titleStyle,
          {
            color: ColorThemes.light.neutral_text_title_color,
          },
        ]}
        trailing={
          <Winicon
            src="outline/arrows/right-arrow"
            color={ColorThemes.light.neutral_text_subtitle_color}
            size={16}
          />
        }
      />
    );
  };

  return (
    <View style={styles.container}>
      {filteredActions.map(item => renderItem(item))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listTileContainer: {
    padding: 0,
  },
  listTileStyle: {
    paddingRight: 16,
    paddingVertical: 13,
    gap: 8,
    borderBottomColor: ColorThemes.light.primary_background,
    borderBottomWidth: 1,
    marginLeft: 16,
  },
  iconContainer: {
    height: 32,
    width: 32,
    borderRadius: 4,
    padding: 6,
  },
  titleStyle: {
    ...TypoSkin.heading8,
  },
});

export default ActionList;
