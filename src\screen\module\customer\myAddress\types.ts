/**
 * TypeScript interfaces for Address module
 */

export interface AddressItem {
  Id: string;
  Name: string;
  Mobile: string;
  Email?: string;
  Address: string;
  Lat?: number;
  Long?: number;
  IsDefault: boolean;
  CustomerId: string;
  DateCreated: number;
}

export interface MyAddressProps {
  chooseAddress?: boolean;
}

export interface AddressItemProps {
  item: AddressItem;
  index: number;
  chooseAddress?: boolean;
  onEdit: (item: AddressItem) => void;
  onDelete: (item: AddressItem) => void;
  onSelect?: (item: AddressItem) => void;
}

export interface AddressListProps {
  addresses: AddressItem[];
  chooseAddress?: boolean;
  onEdit: (item: AddressItem) => void;
  onDelete: (item: AddressItem) => void;
  onSelect?: (item: AddressItem) => void;
  refreshing: boolean;
  onRefresh: () => void;
}

export interface AddressActionsProps {
  item: AddressItem;
  chooseAddress?: boolean;
  onEdit: (item: AddressItem) => void;
  onDelete: (item: AddressItem) => void;
}

export interface EmptyAddressStateProps {
  title?: string;
}

export interface UseMyAddressReturn {
  addresses: AddressItem[];
  loading: boolean;
  refreshing: boolean;
  user: any;
  fetchAddresses: () => Promise<void>;
  deleteAddress: (addressId: string) => Promise<void>;
  refresh: () => Promise<void>;
}
