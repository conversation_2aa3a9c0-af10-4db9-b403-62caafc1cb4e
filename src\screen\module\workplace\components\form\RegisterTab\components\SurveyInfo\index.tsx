import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import {
  CateServicesType,
  ToiletServiceStatus,
} from 'screen/module/service/components/da';
import {FPopup} from 'component/popup/popup';
import ToiletBioList from 'screen/module/toilet/view/detailProject/ToiletBioList';
import ToiletDeviceList from 'screen/module/toilet/view/detailProject/ToiletDeviceList';
import ToiletListOverview from './components/ToiletListOverview';
import ListToiletCriterion from './components/ListToiletCriterion';
import {ToiletServiceItem} from 'types/toiletServiceType';

interface SurveyInfoProps {
  serviceData: ToiletServiceItem;
}

export const SurveyInfo: React.FC<SurveyInfoProps> = ({serviceData}) => {
  const [listTab, setListTab] = useState([
    'Tổng quan',
    'Thiết bị',
    'C/P sinh học',
  ]);
  const popupRef = useRef<any>();
  const [tab, setTab] = useState(0);

  useEffect(() => {
    if (serviceData.CateServicesId === CateServicesType.netzero) {
      setListTab(prev => {
        // Avoid adding duplicate tab
        if (!prev.includes('Tiêu chí NVS')) {
          return [...prev, 'Tiêu chí NVS'];
        }
        return prev;
      });
    }
  }, [serviceData.CateServicesId]);

  const renderTabContent = () => {
    switch (tab) {
      case 0:
        return (
          <ToiletListOverview toiletIds={serviceData.ToiletId.split(',')} />
        );
      case 1:
        return (
          <ToiletDeviceList
            toiletIds={serviceData.ToiletId.split(',')}
            toiletServiceStatus={serviceData.Status}
          />
        );
      case 2:
        return (
          <ToiletBioList
            toiletIds={serviceData.ToiletId.split(',')}
            toiletServiceStatus={serviceData.Status}
          />
        );
      case 3:
        return (
          <ListToiletCriterion
            toiletServiceId={serviceData.Id}
            toiletServiceStatus={serviceData.Status}
          />
        );
      default:
        return <View />;
    }
  };

  return (
    <View style={styles.container}>
      <FPopup ref={popupRef} />
      <Text style={styles.title}>Thông tin khảo sát</Text>
      <View style={styles.tabContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.tabScrollView}
          contentContainerStyle={styles.tabScrollContent}>
          {listTab.map((item, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => setTab(index)}
              style={[
                styles.tabButton,
                {
                  borderBottomWidth: tab === index ? 1 : 0,
                },
              ]}>
              <Text
                style={[
                  styles.tabText,
                  {
                    color:
                      tab === index
                        ? ColorThemes.light.primary_main_color
                        : ColorThemes.light.neutral_text_disabled_color,
                  },
                ]}>
                {item}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      {renderTabContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
    gap: 16,
  },
  title: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  tabContainer: {
    borderTopColor: ColorThemes.light.neutral_main_background_color,
    borderTopWidth: 1,
  },
  tabScrollView: {
    flexGrow: 0,
  },
  tabScrollContent: {
    alignItems: 'center',
    paddingHorizontal: 0,
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    alignItems: 'center',
    borderBottomColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.title3,
  },
  surveyDetailContainer: {
    gap: 16,
    paddingBottom: 100,
    paddingTop: 16,
  },
  sectionTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  detailText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  designContainer: {
    gap: 6,
  },
  designLink: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.primary_main_color,
    alignSelf: 'baseline',
  },
  emptyContainer: {
    paddingBottom: 100,
  },
  fileViewerContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  fileViewerHeader: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  webView: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  htmlContent: {
    fontSize: 14,
  },
});
