import React, { ReactNode, useState } from 'react'
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons'
import { ComponentStatus, getStatusIcon } from '../component-status'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { StyleSheet, TextStyle, TouchableOpacity, View } from 'react-native'
import { Text } from 'react-native-paper'
import { TypoSkin } from '../../assets/skin/typography'

export function FProgressBar({ status = ComponentStatus.INFOR, percent = 100, titleText, title, hideTitle = false, progressBarOnly = true, fullColor = '#E8E8E8', percentColor = '#FC6B03', style = {}, progressBarStyle = {}, textValue }: {
    percent: number,
    titleText?: string,
    title?: ReactNode,
    hideTitle?: boolean,
    progressBarOnly?: boolean,
    fullColor?: string,
    percentColor?: string,
    style?: TextStyle,
    status?: number,
    progressBarStyle?: TextStyle,
    textValue?: string
}) {
    const [openDetails, setOpenDetails] = useState(true)

    return <View style={[progressBarOnly ? {} : { paddingVertical: 16, paddingHorizontal: 24 }, { gap: 12, width: 160 }, style]}>
        {(hideTitle || progressBarOnly) ? null : (title ?? <View style={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between' }}>
            <Text style={TypoSkin.semibold2}>{titleText}</Text>
            <TouchableOpacity onPress={() => { setOpenDetails(!openDetails) }}><FontAwesomeIcon icon={openDetails ? faChevronDown : faChevronUp} size={14} color='#00204D' /></TouchableOpacity>
        </View>)}
        {openDetails ? <View style={{ flexDirection: 'row', width: '100%', height: 22, gap: 12, alignItems: 'center' }} >
            <View style={[styles.progressContainer, { backgroundColor: fullColor }]}><View style={[styles.progressValue, { width: `${percent}%`, backgroundColor: percentColor }, progressBarStyle]} /></View>
            {progressBarOnly || status === ComponentStatus.INFOR ? null : <View>{getStatusIcon(status)}</View>}
            {progressBarOnly && !textValue ? null : <Text style={[TypoSkin.regular2, { color: '#8C8C8C' }]}>{textValue ?? `${percent}/100`}</Text>}
        </View> : null}
    </View>
}

const styles = StyleSheet.create({
    progressContainer: {
        borderRadius: 4,
        height:4,
        width: '100%',
        flex: 1,
        position: 'relative',
        overflow: 'hidden',
    },
    progressValue: {
        position: 'absolute',
        top: 0,
        left: 0,
        height: 4,
        borderRadius: 4,
    }
})