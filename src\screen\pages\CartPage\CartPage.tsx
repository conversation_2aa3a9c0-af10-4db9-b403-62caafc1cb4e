import React from 'react';
import {View, ScrollView} from 'react-native';
import {FDialog} from 'wini-mobile-components';
import {SafeAreaView} from 'react-native-safe-area-context';
import EmptyPage from '../../../project-component/empty-page';
import HotProductsSection from '../../module/product/sections/HotProductsSection';
import {useCartPage} from './hooks/useCartPage';
import {cartPageStyles} from './styles/CartPageStyles';
import RecipientInfo from './components/RecipientInfo';
import StoreGroup from './components/StoreGroup';
import CartBottomBar from './components/CartBottomBar';
import TitleHeader from '../../layout/headers/TitleHeader';

const CartPage: React.FC = () => {
  const {
    storeGroups,
    totalSelectedPrice,
    hasSelectedItems,
    dialogRef,
    handleCheckout,
    handleNavigateToProductList,
  } = useCartPage();

  return (
    <SafeAreaView edges={['bottom']} style={cartPageStyles.container}>
      <FDialog ref={dialogRef} />
      <TitleHeader title={'Giỏ hàng'} />
      <ScrollView
        style={cartPageStyles.scrollView}
        contentContainerStyle={cartPageStyles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        <RecipientInfo dialogRef={dialogRef} />

        {storeGroups.length > 0 ? (
          storeGroups.map((storeGroup: any) => (
            <StoreGroup
              key={storeGroup.ShopId}
              storeGroup={storeGroup}
              dialogRef={dialogRef}
            />
          ))
        ) : (
          <EmptyPage title="Giỏ hàng của bạn trống" />
        )}

        <View style={cartPageStyles.bottomSpacer} />
        <HotProductsSection
          title="Gợi ý cho bạn"
          pageSize={10}
          onSeeAll={handleNavigateToProductList}
        />
      </ScrollView>
      <CartBottomBar
        totalPrice={totalSelectedPrice}
        hasSelectedItems={hasSelectedItems}
        onCheckout={handleCheckout}
      />
    </SafeAreaView>
  );
};

export default CartPage;
