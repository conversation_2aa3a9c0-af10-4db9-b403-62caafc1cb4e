import React from 'react';
import {Text, ScrollView, View} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import ServiceSelection from './ServiceSelection';
import RegisterToiletFormBottom from './RegisterToiletFormBottom';
import {useNavigation} from '@react-navigation/native';
import {CateCriterionItem} from '../../../../types/cateCriteriaType';

const FormChooseCriteria = ({
  onNextStep,
}: {
  onNextStep: (cateCriterion: CateCriterionItem) => void;
}) => {
  const [cateCriterion, setCateCriterion] = React.useState<CateCriterionItem>();
  const navigation = useNavigation<any>();

  const handleCancel = () => {
    navigation.goBack();
  };

  const handleChoose = (cateCriterion: CateCriterionItem) => {
    setCateCriterion(cateCriterion);
  };

  const handleNext = () => {
    if (cateCriterion) {
      onNextStep(cateCriterion);
    }
  };

  return (
    <View style={{flex: 1}}>
      <ScrollView style={{padding: 12}}>
        <Text style={{...TypoSkin.title3}}>
          Hệ thống cung cấp 3 cấp độ dịch vụ vận hành nhà vệ sinh, từ tiêu chuẩn
          cơ bản đến mô hình tuần hoàn tiên tiến. Vui lòng lựa chọn gói dịch vụ
          phù hợp để tiến hành đăng ký.
        </Text>
        <ServiceSelection onChoose={handleChoose} />
      </ScrollView>
      <View style={{paddingHorizontal: 12, marginBottom: 30}}>
        <RegisterToiletFormBottom
          onConfirm={handleNext}
          onCancel={handleCancel}
        />
      </View>
    </View>
  );
};

export default FormChooseCriteria;
