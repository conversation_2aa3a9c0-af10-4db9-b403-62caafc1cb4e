import {
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {useForm} from 'react-hook-form';
import {useEffect, useMemo, useRef, useState} from 'react';
import {
  FDialog,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../component/export-component';
import {validatePhoneNumber} from '../../../utils/validate';
import AppButton from '../../../component/button';
import {TextFieldForm} from '../../../project-component/component-form';
import OTPInput from './components/input-otp';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';
import {
  confirmCode,
  signInWithPhoneFB,
} from '../../../features/otp-loginwFirebase/PhoneSignIn';
import {ComponentStatus} from '../../../component/component-status';
import {CustomerActions} from '../../../redux/reducers/user/reducer';
import {useDispatch} from 'react-redux';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import FLoading from '../../../component/Loading/FLoading';
import LocalAuthen from '../../../features/local-authen/local-authen';
import {DataController} from '../../base-controller';
import {randomGID, regexPassWord} from '../../../utils/Utils';
import {CustomerStatus, CustomerType} from '../../../redux/reducers/user/da';

interface RegisterViewProps {
  methods: any;
}

export const RegisterView = ({methods}: RegisterViewProps) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [isOtp, setIsOtp] = useState(false);
  const [loading, setLoading] = useState(false);
  const dialogRef = useRef<any>();
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const [allowErrorOpt, setAllowErrorOpt] = useState(0);
  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<any>(null);

  const validationForm = useMemo(() => {
    if (isOtp) return true;
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message &&
      methods.watch('ConfirmPassword')?.length > 0 &&
      !methods.formState.errors.ConfirmPassword?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    methods.watch('ConfirmPassword'),
    methods.formState.errors.ConfirmPassword?.message,
    isOtp,
  ]);

  useEffect(() => {
    methods.setValue('Mobile', undefined);
    setLoading(false);
  }, []);

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 20000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const customerController = new DataController('Customer');
  const scrollRef = useRef<any>();

  const onCheckPhoneForOtp = async () => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!validatePhoneNumber(mobile)) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return false;
    }

    setLoading(true);

    // check sdt bi khoa
    // check sdt da dang ky
    const resCustomers = await customerController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@Mobile:(${mobile})`,
    });
    if (resCustomers) {
      if (resCustomers?.data?.length > 0) {
        // check sdt bi khoa
        if (resCustomers?.data[0]?.Status == CustomerStatus.locked) {
          showSnackbar({
            message:
              'Số điện thoại của bạn đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
          return false;
        }
        showSnackbar({
          message: 'Số điện thoại đã đăng ký trước đó',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }
    } else {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
    // var rs = await signInWithPhoneFB(mobile);
    // try {
    //   if (rs) {
    //     // done
    //     setConfirm(rs);
    //     showSnackbar({
    //       message: 'Đã gửi mã xác thực đến số diện thoại',
    //       status: ComponentStatus.SUCCSESS,
    //     });
    //     setLoading(false);
    //     scrollRef.current?.scrollTo({
    //       y: 0,
    //       animated: true,
    //     });
    //     return true;
    //   } else {
    //     setLoading(false);
    //     return false;
    //   }
    // } catch (error) {
    //   console.error('error', error);
    //   setLoading(false);
    //   return false;
    // }
    return true;
  };

  const submitOtp = async (value: string) => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!confirm) return false;
    setLoading(true);

    var rsotp = await confirmCode(confirm, value);

    if (rsotp) {
      console.log('===============otp done=====================');
      setLoading(false);
      setAllowErrorOpt(0);
      return true;
    } else {
      if (allowErrorOpt < 5) {
        setAllowErrorOpt(allowErrorOpt + 1);
      } else {
        const r = await CustomerActions.lockAccount(mobile);
        return showSnackbar({
          message: r.message,
          status: ComponentStatus.ERROR,
        });
      }
      showSnackbar({
        message:
          5 - (allowErrorOpt + 1) == 0
            ? 'Mã xác thực không chính xác'
            : `Mã xác thực không chính xác, bạn còn ${5 - (allowErrorOpt + 1)} lần nhập lại`,
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return false;
    }
  };

  const _signUp = async (otpValue: string) => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return;
    }

    // if (isOtp && otpValue == '') {
    //   setIsOtp(false);
    //   return;
    // }

    // submitOtp
    // if (otpValue == '' || !confirm) return;
    setLoading(true);

    // const rsOtp = await submitOtp(otpValue);
    const deviceToken = (await getDataToAsyncStorage('fcmToken')) ?? '';
    const hashPass = await CustomerActions.hashPassword(password);

    if (hashPass.code != 200) {
      setLoading(false);
      showSnackbar({
        message: hashPass.message,
        status: ComponentStatus.ERROR,
      });
      return;
    }
    if (!hashPass?.data) {
      setLoading(false);
      showSnackbar({
        message: 'Đã có lỗi xảy ra khi tạo tài khoản.',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    const newCus = {
      Id: randomGID(),
      Name: methods.watch('Name')?.trim(),
      DateCreated: Date.now(),
      Mobile: mobile,
      Status: CustomerStatus.active,
      Type: CustomerType.guest,
      Password: hashPass.data,
      DeviceToken: deviceToken ?? '',
    };
    const customerRes = await customerController.add([newCus]);

    if (customerRes.code == 200) {
      const res = await CustomerActions.login(mobile);

      if (res.code === 200) {
        saveDataToAsyncStorage('timeRefresh', `${Date.now() / 1000 + 9 * 60}`);
        saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
        saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
        saveDataToAsyncStorage('Mobile', `${mobile}`);
        await CustomerActions.getInfor(dispatch, navigation).then(() => {
          setLoading(false);
          getDataToAsyncStorage('spBiometrics').then(bio => {
            if (bio == 'true') {
              showDialog({
                ref: dialogRef,
                status: ComponentStatus.INFOR,
                title: 'Sinh trắc học để đăng nhập nhanh hơn?',
                content: (
                  <View style={styles.dialogContent}>
                    <Text style={styles.dialogText}>
                      Bạn có muốn sử dụng bảo mật sinh trắc học để đăng nhập
                      nhanh hơn...
                    </Text>
                    <LocalAuthen
                      sizeIcon={52}
                      isFirstTime={false}
                      onSuccess={async value => {
                        if (value === true) {
                          saveDataToAsyncStorage('Biometrics', 'true');
                          showSnackbar({
                            message: 'Đăng nhập thành công',
                            status: ComponentStatus.SUCCSESS,
                          });
                          navigation.replace(RootScreen.navigateView);
                        }
                      }}
                    />
                  </View>
                ),
                disableCancel: true,
                onSubmit: async () => {
                  saveDataToAsyncStorage('Biometrics', 'false');
                  showSnackbar({
                    message: 'Đăng nhập thành công',
                    status: ComponentStatus.SUCCSESS,
                  });
                  navigation.replace(RootScreen.navigateView);
                },
                titleSubmit: 'Để sau',
              });
            } else {
              showSnackbar({
                message: 'Đăng nhập thành công',
                status: ComponentStatus.SUCCSESS,
              });
              navigation.replace(RootScreen.navigateView);
            }
            setLoading(false);
          });
        });
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    } else {
      showSnackbar({
        message: customerRes.message ?? 'Đã có lỗi xảy ra khi tạo tài khoản.',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
    setLoading(false);
  };

  useEffect(() => {
    if (__DEV__) {
      methods.setValue('Name', 'Nguyễn Văn A');
      methods.setValue('Mobile', '0921999999');
      methods.setValue('Password', 'Chien123');
      methods.setValue('ConfirmPassword', 'Chien123');
    }
  }, []);

  return (
    <ScrollView
      snapToStart={isOtp ? true : undefined}
      showsVerticalScrollIndicator={false}
      style={styles.scrollView}
      ref={scrollRef}>
      <FLoading visible={loading} />
      <FDialog ref={dialogRef} />
      <Pressable>
        <KeyboardAvoidingView
          behavior={'padding'}
          style={styles.keyboardAvoidingView}>
          {isOtp ? (
            <OTPInput
              autoFocus={false}
              length={6}
              disabled={allowErrorOpt > 5}
              onReSendOtp={onCheckPhoneForOtp}
              onSubmit={_signUp}
            />
          ) : (
            <View style={styles.formContainer}>
              <TextFieldForm
                control={methods.control}
                name="Name"
                placeholder="Họ và tên"
                label="Họ và tên"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={styles.nameFieldStyle}
                register={methods.register}
                type="name-phone-pad"
                onBlur={(ev: string) => {
                  if (ev?.length !== 0) methods.clearErrors('Name');
                  else
                    methods.setError('Name', {
                      message: 'Họ và tên không được để trống',
                    });
                }}
              />
              <TextFieldForm
                control={methods.control}
                name="Mobile"
                required
                label="Số điện thoại"
                placeholder="Nhập số điện thoại của bạn"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={styles.textFieldStyle}
                register={methods.register}
                prefix={
                  <View style={styles.prefixContainer}>
                    <Text style={styles.prefixText}>+84</Text>
                    <Winicon src="outline/arrows/down-arrow" size={16} />
                  </View>
                }
                type="number-pad"
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('Mobile', {
                      message: 'Số điện thoại không hợp lệ',
                    });
                    return;
                  }
                  var mobile = ev.trim();
                  // Check if the number doesn't already start with 0 or +84
                  if (!/^(\+84|0)/.test(mobile)) {
                    mobile = '0' + mobile; // Add 0 at the beginning
                  }
                  const val = validatePhoneNumber(mobile);
                  if (val) methods.clearErrors('Mobile');
                  else
                    methods.setError('Mobile', {
                      message: 'Số điện thoại không hợp lệ',
                    });
                }}
              />
              <View style={styles.passwordContainer}>
                <TextFieldForm
                  control={methods.control}
                  name="Password"
                  secureTextEntry={isVisiblePass}
                  label="Mật khẩu"
                  returnKeyType="done"
                  placeholder={'Tạo mật khẩu của bạn'}
                  suffix={
                    <TouchableOpacity
                      style={styles.suffixButton}
                      onPress={() => {
                        setVisiblePass(!isVisiblePass);
                      }}>
                      <Winicon
                        src={
                          isVisiblePass
                            ? `outline/user interface/view`
                            : `outline/user interface/hide`
                        }
                        size={14}
                      />
                    </TouchableOpacity>
                  }
                  errors={methods.formState.errors}
                  textFieldStyle={{
                    ...styles.passwordFieldStyle,
                    marginBottom: methods.formState.errors.Password?.message
                      ? 16
                      : 8,
                  }}
                  register={methods.register}
                  onBlur={async (ev: string) => {
                    if ((ev === undefined || ev.length == 0) && !isOtp) {
                      methods.setError('Password', {
                        message: 'Mật khẩu không được để trống',
                      });
                      return;
                    }
                    var pass = ev.trim();
                    if (!regexPassWord.test(pass))
                      return methods.setError('Password', {
                        message: 'Mật khẩu sai định dạng, hãy thử lại',
                      });
                    methods.clearErrors('Password');
                  }}
                />
                <Text
                  style={
                    styles.passwordHintText
                  }>{`- Tối thiểu 8 ký tự/ Tối đa 16 ký tự \n- Gồm chữ hoa, thường và số`}</Text>
                <TextFieldForm
                  control={methods.control}
                  name="ConfirmPassword"
                  label="Nhập lại mật khẩu"
                  returnKeyType="done"
                  secureTextEntry={isVisiblePass2}
                  suffix={
                    <TouchableOpacity
                      style={styles.suffixButton}
                      onPress={() => {
                        setVisiblePass2(!isVisiblePass2);
                      }}>
                      <Winicon
                        src={
                          isVisiblePass2
                            ? `outline/user interface/view`
                            : `outline/user interface/hide`
                        }
                        size={14}
                      />
                    </TouchableOpacity>
                  }
                  placeholder={'Nhập lại mật khẩu của bạn'}
                  errors={methods.formState.errors}
                  textFieldStyle={styles.confirmPasswordFieldStyle}
                  register={methods.register}
                  onBlur={async (ev: string) => {
                    if ((ev === undefined || ev.length == 0) && !isOtp) {
                      methods.setError('ConfirmPassword', {
                        message: 'Mật khẩu không được để trống',
                      });
                      return;
                    }
                    var rePass = ev.trim();
                    if (methods.watch('Password') !== rePass)
                      return methods.setError('ConfirmPassword', {
                        message: 'Mật khẩu nhập lại không đúng',
                      });
                    methods.clearErrors('ConfirmPassword');
                  }}
                />
              </View>
            </View>
          )}
          <View style={styles.buttonContainer}>
            <AppButton
              title={isOtp ? 'Thử số khác' : 'Đăng ký'}
              textColor={ColorThemes.light.neutral_absolute_background_color}
              textStyle={styles.buttonText}
              disabled={!validationForm || (loading && !isOtp)}
              containerStyle={styles.primaryButtonContainer}
              borderColor={ColorThemes.light.neutral_main_border_color}
              backgroundColor={ColorThemes.light.primary_main_color}
              onPress={async () => {
                if (isOtp) setIsOtp(false);
                else {
                  if (loading) return;
                  const rs = await onCheckPhoneForOtp();
                  if (rs) {
                    _signUp('123456');
                    //   setIsOtp(true);
                  }
                }
              }}
            />
          </View>
        </KeyboardAvoidingView>
      </Pressable>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  dialogContent: {
    width: '100%',
    gap: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  scrollView: {
    width: '100%',
  },
  keyboardAvoidingView: {
    width: '100%',
    marginTop: 8,
  },
  formContainer: {
    width: '100%',
    gap: 16,
  },
  nameFieldStyle: {
    height: 48,
    padding: 16,
    backgroundColor: ColorThemes.light.transparent,
  },
  textFieldStyle: {
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
  },
  prefixContainer: {
    flexDirection: 'row',
    height: 46,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
  },
  prefixText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  passwordContainer: {
    width: '100%',
    gap: 8,
    paddingBottom: 12,
  },
  passwordFieldStyle: {
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
    paddingLeft: 16,
    paddingVertical: 16,
  },
  suffixButton: {
    padding: 12,
  },
  passwordHintText: {
    ...TypoSkin.subtitle4,
    alignSelf: 'baseline',
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  confirmPasswordFieldStyle: {
    height: 48,
    backgroundColor: ColorThemes.light.transparent,
    paddingLeft: 16,
    paddingVertical: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingTop: 16,
    paddingBottom: 130,
  },
  buttonText: {
    ...TypoSkin.buttonText1,
  },
  primaryButtonContainer: {
    height: 48,
    flex: 1,
    borderRadius: 8,
    marginTop: 8,
  },
});
