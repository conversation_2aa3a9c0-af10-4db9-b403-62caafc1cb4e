import React, {forwardRef, useEffect, useMemo} from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import {
  FDialog,
  Winicon,
} from '../../../../../../../component/export-component';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from 'redux/hooks/hooks';
import ConfigAPI from 'config/configApi';
import {closePopup} from 'wini-mobile-components';
import AppButton from 'component/button';
import WScreenFooter from '../../../../../../layout/footer';
import ScreenHeader from '../../../../../../layout/header';
import {FileAttachments} from '../FileAttachments';
import {StatusTicketData} from '../TicketStatus';
import {useTicketData} from './hooks/useTicketData';
import {usePopupHandlers} from './hooks/usePopupHandlers';
import {
  TicketCreator,
  TicketStatusForm,
  TicketDetails,
  TicketForm,
} from './components';
import {TicketType} from 'types/ticketType';

interface PopupEditTicketProps {
  customer: any;
  methods: any;
  fileInfor: Array<any>;
  item: TicketType;
  onChange: any;
}

export const PopupEditTicket = forwardRef<any, PopupEditTicketProps>(
  function PopupEditTicket(data, parentPopupRef) {
    const {customer, methods, onChange, item, fileInfor} = data;
    const user = useSelectorCustomerState().data;
    const editable = useMemo(() => onChange !== undefined, [onChange]);

    const company = useSelectorCustomerCompanyState().data;
    const listStatus =
      company?.Id !== ConfigAPI.ktxCompanyId
        ? StatusTicketData.slice(0, StatusTicketData.length - 1)
        : StatusTicketData;

    // Use custom hooks
    const {customers, details} = useTicketData({item});
    const {dialogRef, handleFilePress, handleSubmit} = usePopupHandlers({
      methods,
      onChange,
      item,
      user,
      details,
      parentPopupRef, // Pass parent ref to hook
    });

    return (
      <SafeAreaView style={styles.container}>
        <FDialog ref={dialogRef} />
        <ScreenHeader
          style={styles.header}
          title={`Chỉnh sửa yêu cầu`}
          prefix={<View />}
          action={
            <TouchableOpacity
              onPress={() =>
                parentPopupRef && closePopup(parentPopupRef as any)
              }
              style={styles.closeButton}>
              <Winicon
                src="outline/layout/xmark"
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          }
        />
        <KeyboardAvoidingView
          behavior="padding"
          keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
          style={styles.keyboardView}>
          <ScrollView style={{}}>
            <View style={styles.content}>
              <TicketCreator customer={customer} />
              <TicketStatusForm
                methods={methods}
                editable={editable}
                listStatus={listStatus}
              />
              <FileAttachments
                fileInfor={fileInfor}
                onFilePress={handleFilePress}
              />
              <TicketDetails details={details} customers={customers} />
              <TicketForm methods={methods} editable={editable} />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
        <WScreenFooter style={styles.footer}>
          <AppButton
            title={'Xong'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={styles.submitButton}
            onPress={handleSubmit}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height / 1.2,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  keyboardView: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 16,
  },
  content: {
    flex: 1,
    gap: 16,
    paddingBottom: 160,
  },
  footer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    paddingBottom: 30,
  },
  submitButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
