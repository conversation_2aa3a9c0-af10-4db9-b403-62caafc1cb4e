import {useEffect, useState} from 'react';
import {TicketDa} from '../../../../../ticketDa';
import {DataController} from 'screen/base-controller';
import {BaseDA} from 'screen/baseDA';
import {TicketType} from 'types/ticketType';
import {Ultis} from 'utils/Utils';
import {
  parseTicketDetails,
  extractCustomerIds,
} from 'screen/module/ticket/utils/ticketDetailParser';

export const useTicketData = (item: TicketType) => {
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [files, setFiles] = useState<Array<any>>([]);
  const ticketDa = new TicketDa();

  useEffect(() => {
    if (!item) return;

    const fetchTicketData = async () => {
      try {
        const ticketData = await ticketDa.getById(item.Id);

        if (ticketData) {
          const details = ticketData.Detail
            ? parseTicketDetails(ticketData.Detail)
            : [];

          if (ticketData.CustomerId || details.length) {
            const customerIds = extractCustomerIds(details);
            if (ticketData.CustomerId) customerIds.push(ticketData.CustomerId);

            // Remove duplicates and filter out null/undefined
            const uniqueCustomerIds = [...new Set(customerIds)].filter(Boolean);

            if (uniqueCustomerIds.length > 0) {
              const customerController = new DataController('Customer');
              const cusRes =
                await customerController.getByListId(uniqueCustomerIds);

              if (cusRes.code === 200) {
                const data = cusRes.data;
                const filteredData = data.filter(Boolean);
                if (!filteredData.length) return setCustomers([]);
                setCustomers(
                  filteredData.map((e: any) => ({
                    ...e,
                    bgColor: Ultis.generateDarkColorRgb(),
                  })),
                );
              }
            }
          }
          // Fetch files data
          const ticketWithFiles = ticketData as any;
          if (!ticketWithFiles.File) return;
          if (ticketWithFiles.File?.length) {
            const fileRes = await BaseDA.getFilesInfor(
              ticketWithFiles.File.split(','),
            );
            if (fileRes.code === 200) {
              setFiles(
                fileRes.data.filter((e: any) => e !== undefined && e !== null),
              );
            }
          }
        }
      } catch (error) {
        console.error('Error fetching ticket data:', error);
      }
    };

    fetchTicketData();
  }, [item]);

  return {customers, files};
};
