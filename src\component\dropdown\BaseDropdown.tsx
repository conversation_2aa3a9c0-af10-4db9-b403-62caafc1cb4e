import React, {useState, useMemo} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  TextInput,
  Modal,
  Pressable,
  StyleProp,
  ViewStyle,
  ActivityIndicator,
} from 'react-native';
import {Winicon} from '../export-component';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';

export interface BaseDropdownItem {
  id: string | number;
  label: string;
  [key: string]: any;
}

interface BaseDropdownProps<T extends BaseDropdownItem> {
  // Data props
  data: T[];
  selectedItem?: T;
  onSelect?: (item: T) => void;

  // Display props
  placeholder?: string;
  title?: string;
  icon?: any;
  iconColor?: string;

  // Search props
  searchable?: boolean;
  searchPlaceholder?: string;
  searchFields?: (keyof T)[];

  // Behavior props
  disabled?: boolean;
  loading?: boolean;

  // Style props
  style?: StyleProp<ViewStyle>;

  // Custom render functions
  renderItem?: (item: T) => React.ReactElement;
  renderSelectedItem?: (item: T) => string;

  // Error handling
  emptyMessage?: string;
  errorMessage?: string;
}

const BaseDropdown = <T extends BaseDropdownItem>({
  data = [],
  selectedItem,
  onSelect,
  placeholder = 'Chọn một mục',
  title,
  icon,
  iconColor = '#4CAF50',
  searchable = true,
  searchPlaceholder = 'Tìm kiếm...',
  searchFields = ['label'],
  disabled = false,
  loading = false,
  style,
  renderItem,
  renderSelectedItem,
  emptyMessage = 'Không có dữ liệu',
  errorMessage,
}: BaseDropdownProps<T>) => {
  const [searchText, setSearchText] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  // Memoized filtered data for better performance
  const filteredData = useMemo(() => {
    if (!searchText.trim() || !searchable) {
      return data;
    }

    return data.filter(item => {
      return searchFields.some(field => {
        const value = item[field];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchText.toLowerCase());
        }
        if (typeof value === 'number') {
          return value.toString().includes(searchText);
        }
        return false;
      });
    });
  }, [data, searchText, searchFields, searchable]);

  // Show bottom sheet
  const showBottomSheet = () => {
    if (disabled || loading) return;
    setSearchText('');
    setIsVisible(true);
  };

  // Hide bottom sheet
  const hideBottomSheet = () => {
    setIsVisible(false);
    setSearchText('');
  };

  // Handle item selection
  const handleSelectItem = (item: T) => {
    onSelect?.(item);
    hideBottomSheet();
  };

  // Get display text for selected item
  const getSelectedText = () => {
    if (!selectedItem) return placeholder;
    if (renderSelectedItem) return renderSelectedItem(selectedItem);
    return selectedItem.label;
  };

  // Default item renderer
  const defaultRenderItem = ({item}: {item: T}) => (
    <TouchableOpacity
      style={styles.defaultItem}
      onPress={() => handleSelectItem(item)}>
      <View style={styles.defaultItemContent}>
        <Text style={styles.defaultItemText}>{item.label}</Text>
      </View>
    </TouchableOpacity>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>{errorMessage || emptyMessage}</Text>
    </View>
  );

  return (
    <View style={style}>
      {/* Dropdown Button */}
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          disabled && styles.dropdownButtonDisabled,
        ]}
        onPress={showBottomSheet}
        disabled={disabled || loading}>
        <View style={styles.dropdownContent}>
          <View style={styles.leftContent}>
            {icon && (
              <AppSvg
                style={styles.icon}
                SvgSrc={icon}
                size={22}
                color={iconColor}
              />
            )}
            <Text
              style={[
                styles.dropdownText,
                !selectedItem && styles.placeholderText,
              ]}>
              {getSelectedText()}
            </Text>
          </View>
          {loading ? (
            <ActivityIndicator
              size="small"
              color={ColorThemes.light.primary_main_color}
            />
          ) : (
            <Winicon
              src="fill/arrows/triangle-sm-down"
              size={20}
              style={styles.dropdownArrow}
            />
          )}
        </View>
      </TouchableOpacity>

      {/* Bottom Sheet Modal */}
      <Modal
        visible={isVisible}
        transparent
        statusBarTranslucent
        animationType="slide"
        onRequestClose={hideBottomSheet}>
        <Pressable style={styles.modalOverlay} onPress={hideBottomSheet}>
          <View style={styles.bottomSheetContainer}>
            {/* Header */}
            <View style={styles.bottomSheetHeader}>
              <View style={styles.dragIndicator} />
              <View style={styles.headerContent}>
                <Text style={styles.bottomSheetTitle}>
                  {title || (selectedItem ? getSelectedText() : placeholder)}
                </Text>
                <TouchableOpacity
                  onPress={hideBottomSheet}
                  style={styles.closeButton}>
                  <Winicon
                    src="outline/layout/xmark"
                    size={20}
                    color={ColorThemes.light.neutral_text_body_color}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Search Input */}
            {searchable && (
              <View style={styles.searchContainer}>
                <Winicon
                  src="outline/development/zoom"
                  size={16}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder={searchPlaceholder}
                  value={searchText}
                  onChangeText={setSearchText}
                  placeholderTextColor={
                    ColorThemes.light.neutral_text_subtitle_color
                  }
                  autoFocus={false}
                  returnKeyType="search"
                  clearButtonMode="while-editing"
                />
              </View>
            )}

            {/* Data List */}
            <FlatList
              data={filteredData}
              keyExtractor={item => item.id.toString()}
              renderItem={renderItem || (defaultRenderItem as any)}
              style={styles.dataList}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              ListEmptyComponent={renderEmptyState}
            />
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    minHeight: 48,
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.primary_border_color,
  },
  dropdownButtonDisabled: {
    opacity: 0.6,
  },
  dropdownContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginLeft: 8,
  },
  dropdownText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_body_color,
    flex: 1,
    marginLeft: 20,
  },
  placeholderText: {
    color: ColorThemes.light.neutral_text_disabled_color,
  },
  dropdownArrow: {
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheetContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    minHeight: 400,
  },
  bottomSheetHeader: {
    paddingTop: 8,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: ColorThemes.light.neutral_text_subtitle_color,
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  bottomSheetTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    ...TypoSkin.body3,
    flex: 1,
    color: ColorThemes.light.neutral_text_body_color,
    padding: 0,
  },
  dataList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  defaultItem: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  defaultItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultItemText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
});

export default BaseDropdown;
