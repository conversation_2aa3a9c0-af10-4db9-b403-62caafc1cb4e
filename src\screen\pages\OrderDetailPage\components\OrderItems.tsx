import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, FlatList} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import {ColorThemes} from '../../../../assets/skin/colors';
import {ORDER_STATUS} from '../../../module/orderProduct/OrderCustomerDetail/types';
import {OrderItemsProps} from '../types';
import OrderItem from './OrderItem';

const OrderItems: React.FC<OrderItemsProps> = ({
  style,
  orderDetails,
  order,
  onProductPress,
  onCancelOrder,
}) => {
  const [showAll, setShowAll] = useState(false);

  if (!orderDetails || orderDetails?.length === 0) {
    return null;
  }

  const displayedItems = showAll ? orderDetails : orderDetails.slice(0, 2);
  const hasMoreItems = orderDetails.length > 2;

  const renderItem = ({item, index}: {item: any; index: number}) => (
    <OrderItem key={index} item={item} onProductPress={onProductPress} />
  );

  const renderViewMoreButton = () => {
    if (!hasMoreItems) return null;

    return (
      <TouchableOpacity
        style={styles.viewMoreButton}
        onPress={() => setShowAll(!showAll)}>
        <Text style={styles.viewMoreText}>
          {showAll ? 'Ẩn bớt' : 'Xem thêm'} {showAll ? '▲' : '▼'}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.orderItemsContainer, style]}>
      <View style={styles.paddingContainer}>
        <FlatList
          data={displayedItems}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.Id || index}`}
          scrollEnabled={false}
          ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
        />
        {renderViewMoreButton()}

        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>
            Tổng tiền ({orderDetails.length} sản phẩm):
          </Text>
          <Text style={styles.totalPrice}>
            {Ultis.money(order?.Value ?? 0)} đ
          </Text>
        </View>
        {order?.Status === ORDER_STATUS.NEW && (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              onPress={onCancelOrder}
              style={styles.cancelButton}>
              <Text style={styles.buttonText}>Hủy đơn</Text>
            </TouchableOpacity>
          </View>
        )}
        {order?.Status === ORDER_STATUS.COMPLETED && (
          <View style={styles.actionButtonsRow}>
            <TouchableOpacity onPress={() => {}} style={styles.refundButton}>
              <Text style={styles.buttonText}>Yêu cầu hoàn hàng</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {}} style={styles.reviewButton}>
              <Text style={styles.buttonText}>Đánh giá</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  orderItemsContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },

  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.heading9,
    color: '#212121',
  },
  totalPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
    marginLeft: 8,
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 20,
    width: 100,
    paddingVertical: 2,
    alignItems: 'center',
  },
  buttonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
    textAlign: 'center',
  },
  viewMoreButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  viewMoreText: {
    ...TypoSkin.buttonText5,
    fontSize: 14,
  },
  // Additional styles for inline styles
  paddingContainer: {
    padding: 12,
  },
  itemSeparator: {
    height: 12,
  },
  buttonContainer: {
    alignItems: 'flex-end',
    paddingTop: 8,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingTop: 8,
  },
  refundButton: {
    width: 170,
    backgroundColor: '#FF3B30',
    borderRadius: 20,
    paddingVertical: 2,
    alignItems: 'center',
  },
  reviewButton: {
    backgroundColor: ColorThemes.light.secondary2_main_color,
    borderRadius: 20,
    width: 100,
    paddingVertical: 2,
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default OrderItems;
