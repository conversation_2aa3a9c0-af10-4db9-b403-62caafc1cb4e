import React from 'react';
import {Text, View, StyleSheet} from 'react-native';
import ListTile from 'component/list-tile/list-tile';
import {SkeletonImage} from 'project-component/skeleton-img';
import ConfigAPI from 'config/configApi';
import {Ultis} from 'utils/Utils';

interface CustomerInfoProps {
  customer: any;
}

export const CustomerInfo: React.FC<CustomerInfoProps> = ({customer}) => {
  if (!customer) return null;

  return (
    <ListTile
      style={styles.listTile}
      leading={
        customer?.Img ? (
          <View>
            <SkeletonImage
              source={{
                uri: customer.Img.startsWith('https')
                  ? customer.Img
                  : ConfigAPI.imgUrlId + customer?.Img,
              }}
              style={styles.customerAvatar}
            />
          </View>
        ) : (
          <View
            style={[
              styles.customerAvatarPlaceholder,
              {backgroundColor: Ultis.generateDarkColorRgb()},
            ]}>
            <Text style={styles.customerAvatarText}>
              {customer?.Name ? customer?.Name?.substring(0, 1) : ''}
            </Text>
          </View>
        )
      }
      title={`Người tạo: ${customer?.Name ?? '-'}`}
      subtitle={customer?.Mobile ?? '-'}
    />
  );
};

const styles = StyleSheet.create({
  listTile: {
    padding: 0,
  },
  customerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 50,
    objectFit: 'cover',
  },
  customerAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customerAvatarText: {
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
