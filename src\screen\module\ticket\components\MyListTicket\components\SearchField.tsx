import React from 'react';
import {View, StyleSheet} from 'react-native';
import {
  FTextField,
  Winicon,
} from '../../../../../../../../component/export-component';
import {ColorThemes} from '../../../../../../../../assets/skin/colors';

interface SearchFieldProps {
  searchValue: string;
  onSubmit: (value: string) => void;
}

const SearchField: React.FC<SearchFieldProps> = ({searchValue, onSubmit}) => {
  return (
    <View style={styles.container}>
      <FTextField
        style={styles.textField}
        onChange={() => {}}
        onSubmit={vl => {
          onSubmit(vl.trim());
        }}
        returnKeyType="search"
        prefix={
          <Winicon
            src="outline/development/zoom"
            size={14}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        }
        value={searchValue}
        placeholder="Tì<PERSON> kiếm"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
  },
  textField: {
    paddingHorizontal: 16,
    width: '100%',
    height: 40,
  },
});

export default SearchField;
