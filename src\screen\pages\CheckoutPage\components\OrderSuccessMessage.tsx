import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {OrderSuccessMessageProps} from '../types';

const OrderSuccessMessage: React.FC<OrderSuccessMessageProps> = ({isDone}) => {
  if (!isDone) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.successText}>
        Đơn hàng của bạn đã được đặt thành công!
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  successText: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.success_main_color,
    textAlign: 'center',
  },
});

export default OrderSuccessMessage;
