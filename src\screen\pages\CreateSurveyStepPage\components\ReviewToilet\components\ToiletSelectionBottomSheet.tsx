import React, {useEffect, useState} from 'react';
import {View, FlatList, StyleSheet, TouchableOpacity, Text} from 'react-native';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import ToiletSelectionCard from '../../../../../module/toilet/components/card/ToiletSelectionCard';
import {ToiletItem} from '../../../../../../types/toiletType';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {CustomBottomSheet} from '../../../../../../project-component/form/DateRangePicker/CustomBottomSheet';

interface ToiletSelectionBottomSheetProps {
  data: ToiletItem[];
  onConfirm: (toilets: ToiletItem[]) => void;
  itemsSelected: ToiletItem[];
  visible: boolean;
  onClose: () => void;
}

export default function ToiletSelectionBottomSheet({
  data,
  itemsSelected,
  onConfirm,
  visible,
  onClose,
}: ToiletSelectionBottomSheetProps) {
  const [selected, setSelected] = useState<string[]>([]);

  // Cập nhật selected state khi itemsSelected thay đổi
  useEffect(() => {
    const selectedIds = itemsSelected.map(item => item.Id);
    setSelected(selectedIds);
  }, [itemsSelected]);

  const toggle = (id: string) => {
    const copy = [...selected];
    const index = copy.indexOf(id);
    if (index > -1) {
      copy.splice(index, 1); // Remove if exists
    } else {
      copy.push(id);
    }
    setSelected(copy);
  };

  const handleConfirm = () => {
    const toilets = data.filter(item => selected.includes(item.Id));
    onConfirm(toilets);
  };

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title="Chọn nhà vệ sinh"
      onCancel={onClose}
      onConfirm={handleConfirm}
      cancelText="Hủy"
      confirmText="Xác nhận">
      <View style={styles.container}>
        {data.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              Khách hàng này chưa có nhà vệ sinh. Vui lòng tạo mới nhà vệ sinh
            </Text>
          </View>
        ) : (
          <FlatList
            data={data}
            keyExtractor={item => item.Id}
            renderItem={({item}) => (
              <ToiletSelectionCard
                item={item}
                isSelected={selected.includes(item.Id)}
                onPress={toggle}
              />
            )}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{flex: 1}}
          />
        )}
      </View>
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: 700,
    width: '100%',
  },
  btnConfirm: {
    backgroundColor: ColorThemes.light.primary_main_color,
    padding: 12,
    borderRadius: 8,
  },
  btnConfirmText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.white,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    ...TypoSkin.body2,
    textAlign: 'center',
    marginHorizontal: 16,
  },
});
