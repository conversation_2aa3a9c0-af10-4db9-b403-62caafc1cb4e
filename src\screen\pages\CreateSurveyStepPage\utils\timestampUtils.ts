// Utility functions for timestamp operations in CreateSurveyStepPage
// This file provides helper functions for working with timestamp format dates

/**
 * Convert Date object to timestamp
 */
export const dateToTimestamp = (date?: Date): number => {
  if (!date) return 0;
  return date.getTime();
};

/**
 * Convert timestamp to Date object
 */
export const timestampToDate = (timestamp?: number): Date | null => {
  if (!timestamp || timestamp === 0) return null;
  return new Date(timestamp);
};

/**
 * Convert string date to timestamp
 */
export const stringToTimestamp = (dateString?: string): number => {
  if (!dateString) return 0;
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? 0 : date.getTime();
};

/**
 * Convert timestamp to string date (YYYY-MM-DD format)
 */
export const timestampToString = (timestamp?: number): string => {
  if (!timestamp || timestamp === 0) return '';
  const date = new Date(timestamp);
  return date.toISOString().split('T')[0];
};

/**
 * Format timestamp for display (Vietnamese locale)
 */
export const formatTimestampForDisplay = (timestamp?: number): string => {
  if (!timestamp || timestamp === 0) return 'Chưa chọn';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

/**
 * Format timestamp for display with time (Vietnamese locale)
 */
export const formatTimestampWithTime = (timestamp?: number): string => {
  if (!timestamp || timestamp === 0) return 'Chưa chọn';
  
  const date = new Date(timestamp);
  return date.toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Get current timestamp
 */
export const getCurrentTimestamp = (): number => {
  return new Date().getTime();
};

/**
 * Generate timestamp for days from now
 */
export const getTimestampDaysFromNow = (days: number): number => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.getTime();
};

/**
 * Check if timestamp is valid (not 0 and not NaN)
 */
export const isValidTimestamp = (timestamp?: number): boolean => {
  return typeof timestamp === 'number' && timestamp > 0 && !isNaN(timestamp);
};

/**
 * Validate date range (end date should be >= start date)
 */
export const validateDateRange = (startTimestamp?: number, endTimestamp?: number): {
  isValid: boolean;
  error?: string;
} => {
  if (!isValidTimestamp(startTimestamp)) {
    return { isValid: false, error: 'Ngày bắt đầu không hợp lệ' };
  }
  
  if (!isValidTimestamp(endTimestamp)) {
    return { isValid: false, error: 'Ngày kết thúc không hợp lệ' };
  }
  
  if (startTimestamp! > endTimestamp!) {
    return { isValid: false, error: 'Ngày kết thúc phải sau ngày bắt đầu' };
  }
  
  return { isValid: true };
};

/**
 * Calculate duration between two timestamps in days
 */
export const calculateDurationInDays = (startTimestamp?: number, endTimestamp?: number): number => {
  if (!isValidTimestamp(startTimestamp) || !isValidTimestamp(endTimestamp)) {
    return 0;
  }
  
  const diffInMs = endTimestamp! - startTimestamp!;
  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
};

/**
 * Get start of day timestamp (00:00:00)
 */
export const getStartOfDayTimestamp = (timestamp?: number): number => {
  if (!isValidTimestamp(timestamp)) return 0;
  
  const date = new Date(timestamp!);
  date.setHours(0, 0, 0, 0);
  return date.getTime();
};

/**
 * Get end of day timestamp (23:59:59.999)
 */
export const getEndOfDayTimestamp = (timestamp?: number): number => {
  if (!isValidTimestamp(timestamp)) return 0;
  
  const date = new Date(timestamp!);
  date.setHours(23, 59, 59, 999);
  return date.getTime();
};

/**
 * Create timestamp from date components
 */
export const createTimestamp = (year: number, month: number, day: number): number => {
  const date = new Date(year, month - 1, day); // month is 0-indexed
  return date.getTime();
};

/**
 * Parse timestamp to date components
 */
export const parseTimestamp = (timestamp?: number): {
  year: number;
  month: number;
  day: number;
} | null => {
  if (!isValidTimestamp(timestamp)) return null;
  
  const date = new Date(timestamp!);
  return {
    year: date.getFullYear(),
    month: date.getMonth() + 1, // Convert to 1-indexed
    day: date.getDate(),
  };
};

/**
 * Compare two timestamps
 */
export const compareTimestamps = (timestamp1?: number, timestamp2?: number): number => {
  const ts1 = timestamp1 || 0;
  const ts2 = timestamp2 || 0;
  
  if (ts1 < ts2) return -1;
  if (ts1 > ts2) return 1;
  return 0;
};

/**
 * Check if timestamp is today
 */
export const isToday = (timestamp?: number): boolean => {
  if (!isValidTimestamp(timestamp)) return false;
  
  const today = new Date();
  const date = new Date(timestamp!);
  
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

/**
 * Check if timestamp is in the past
 */
export const isPast = (timestamp?: number): boolean => {
  if (!isValidTimestamp(timestamp)) return false;
  return timestamp! < getCurrentTimestamp();
};

/**
 * Check if timestamp is in the future
 */
export const isFuture = (timestamp?: number): boolean => {
  if (!isValidTimestamp(timestamp)) return false;
  return timestamp! > getCurrentTimestamp();
};

/**
 * Get relative time description (e.g., "2 days ago", "in 3 days")
 */
export const getRelativeTime = (timestamp?: number): string => {
  if (!isValidTimestamp(timestamp)) return 'Không xác định';
  
  const now = getCurrentTimestamp();
  const diffInMs = timestamp! - now;
  const diffInDays = Math.round(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'Hôm nay';
  if (diffInDays === 1) return 'Ngày mai';
  if (diffInDays === -1) return 'Hôm qua';
  if (diffInDays > 1) return `Sau ${diffInDays} ngày`;
  if (diffInDays < -1) return `${Math.abs(diffInDays)} ngày trước`;
  
  return 'Không xác định';
};

/**
 * Example timestamps for testing and development
 */
export const EXAMPLE_TIMESTAMPS = {
  // Current time
  NOW: getCurrentTimestamp(),
  
  // Common test dates
  JAN_15_2025: new Date('2025-01-15').getTime(), // 1737763200000
  JAN_20_2025: new Date('2025-01-20').getTime(), // 1738195200000
  FEB_01_2025: new Date('2025-02-01').getTime(), // 1738368000000
  FEB_05_2025: new Date('2025-02-05').getTime(), // 1738713600000
  
  // Edge cases
  EPOCH: 0,
  YEAR_2000: new Date('2000-01-01').getTime(),
  YEAR_2030: new Date('2030-01-01').getTime(),
  
  // Example from user requirement
  EXAMPLE_TIMESTAMP: 1753780190110, // Wed Jul 29 2025 14:23:10 GMT+0700
} as const;

/**
 * Type definitions for timestamp operations
 */
export interface TimestampRange {
  start: number;
  end: number;
}

export interface TimestampValidationResult {
  isValid: boolean;
  error?: string;
}

export interface DateComponents {
  year: number;
  month: number;
  day: number;
}
