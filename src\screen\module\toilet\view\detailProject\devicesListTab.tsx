import React, {useRef, useState, useMemo, useEffect, forwardRef} from 'react';
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import ListTile from '../../../../../component/list-tile/list-tile';
import EmptyPage from '../../../../../project-component/empty-page';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import {
  DeviceBioStatus,
  TaskStatus,
  ToiletServiceStatus,
} from '../../../service/components/da';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import {CardToiletHoriSkeleton} from '../../../../../project-component/skeletonCard';
import {BaseDA} from '../../../../baseDA';
import WScreenFooter from '../../../../layout/footer';
import AppButton from '../../../../../component/button';
import {
  FDialog,
  FTextField,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import {useForm} from 'react-hook-form';
import DatePicker from 'react-native-date-picker';
import {
  Fselect1Form,
  TextFieldForm,
} from '../../../../../project-component/component-form';
import ScreenHeader from '../../../../layout/header';
import ImageCropPicker from 'react-native-image-crop-picker';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import WebView from 'react-native-webview';
import {CustomerType} from '../../../../../redux/reducers/user/da';
import {PopupSelectDevices} from '../../../workplace/components/popup/PopupSelectDevices';

interface Props {
  data: any;
  serviceData: any;
  refreshing: any;
  onRefresh: any;
  disabled?: any;
  formId?: any;
}

export default function DevicesListTab(props: Props) {
  const {data, serviceData, refreshing, onRefresh, disabled, formId} = props;

  const user = useSelectorCustomerState().data;
  const dialogRef = useRef<any>();
  const popupRef = useRef<any>();
  const [devices, setDevices] = useState<any>({
    data: [],
    totalCount: undefined,
  });
  const [pageDetails, setPageDetails] = useState({page: 1, size: 10});
  const isEditable = useMemo(() => user?.Id === data?.CustomerId, [user, data]);
  const [isLoading, setLoading] = useState(false);
  const [files, setFiles] = useState<Array<any>>([]);
  const [services, setServices] = useState<Array<any>>([]);
  const [products, setProducts] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');

  const getData = async ({page, size}: any) => {
    setLoading(true);
    const devController = new DataController('Device');
    let query = `@ToiletId:{${data.Id}}`;
    if (searchValue?.length)
      query += ` (@Name:(*${searchValue}*)) | (@Code:(*${searchValue}*)) | (@Unit:(*${searchValue}*))`;
    const res = await devController.aggregateList({
      page: page ?? 1,
      size: size ?? 100,
      searchRaw: query,
    });
    if (res.code === 200) {
      const serviceIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      const toiletServicesController = new DataController('ToiletServices');
      toiletServicesController.getByListId(serviceIds).then(servicesRes => {
        if (servicesRes.code === 200) {
          setServices(servicesRes.data);
        }
      });
      const productIds = res.data
        ?.map((e: any) => e.ProductId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      var _tmpFileIds: Array<string> = [];
      _tmpFileIds = res.data
        .filter((e: {Img: any}) => e.Img)
        .map((e: {Img: string}) => e.Img.split(','))
        .flat(Infinity)
        .filter(
          (id: string) =>
            id?.length &&
            files.every(e => e.Id !== id) &&
            !id.startsWith('http'),
        );
      if (productIds?.length) {
        const productController = new DataController('Product');
        const productRes = await productController.getByListId(productIds);
        if (productRes.code === 200) {
          if (productRes.data?.length) {
            setProducts(productRes.data);
            _tmpFileIds.push(
              ...productRes.data
                .map((e: any) => e?.Img?.split(','))
                .flat(Infinity)
                .filter(
                  (id: any) =>
                    id?.length &&
                    files.every((e: any) => e.Id !== id) &&
                    !id.startsWith('http'),
                ),
            );
            console.log(
              productRes.data
                .map((e: any) => e?.Img?.split(','))
                .flat(Infinity)
                .filter(
                  (id: any) =>
                    id?.length &&
                    files.every((e: any) => e.Id !== id) &&
                    !id.startsWith('http'),
                ),
            );
          }
        }
      }

      BaseDA.getFilesInfor(_tmpFileIds).then((fileRes: any) => {
        if (fileRes.code === 200)
          setFiles((f: any) => [
            ...f,
            ...fileRes.data.filter((e: any) => e !== undefined && e !== null),
          ]);
      });
      setDevices({data: res.data, totalCount: res.totalCount});
      setLoading(false);
    }
  };

  useEffect(() => {
    if (data) getData({});
  }, [data]);

  const deleteItem = (ids = Array<any>()) => {
    const controller = new DataController('Device');
    controller.delete(ids).then(res => {
      if (res.code === 200) {
        getData({});
      } else
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
    });
  };

  const showPopupSelectProduct = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupSelectDevices
          ref={popupRef}
          selectOther={() => {
            closePopup(popupRef);
            showAddEdit(undefined, true);
          }}
          onSubmit={async (selectedProducts: any) => {
            const controller = new DataController('Device');
            const newListDevice = selectedProducts
              .map((p: any) => {
                const quantity = p['_Quantity'];
                const tmp = [];
                for (let i = 0; i < quantity; i++) {
                  tmp.push({
                    Id: randomGID(),
                    Name: p.Name,
                    DateCreated: Date.now(),
                    ToiletId: data.Id,
                    ProductId: p.Id,
                    Quantity: 1,
                    Price: p.Price,
                    Unit: p.Unit,
                    Img: p.Img,
                    Description: p.Description,
                    Specifications: p.Specifications,
                    Status: formId
                      ? DeviceBioStatus.inactive
                      : DeviceBioStatus.active,
                  });
                }
                return tmp;
              })
              .flat(Infinity);
            const res = await controller.add(newListDevice);
            if (res.code === 200) getData({});
          }}
        />
      ),
    });
  };

  const showAddEdit = (item: any, isAddNew = false) => {
    if (item || isAddNew) {
      showPopup({
        ref: popupRef,
        enableDismiss: true,
        children: (
          <PopupAddEditDevice
            ref={popupRef}
            id={item?.Id}
            formId={formId}
            toiletId={data.Id}
            onSubmit={() => {
              getData({});
            }}
          />
        ),
      });
    } else {
      showPopupSelectProduct();
    }
  };

  return (
    <View
      style={{
        flex: 1,
        height: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      {!formId ? (
        <View
          style={{flexDirection: 'row', width: '100%', paddingHorizontal: 16}}>
          <FTextField
            style={{paddingHorizontal: 16, width: '100%'}}
            onChange={(vl: any) => {
              setSearchValue(vl);
            }}
            value={searchValue}
            onBlur={() => getData({})}
            onSubmit={() => getData({})}
            placeholder="Tìm kiếm thiết bị"
          />
        </View>
      ) : null}
      <FlatList
        nestedScrollEnabled
        scrollEnabled={formId ? false : true}
        data={devices.data}
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={() => {
              if (onRefresh) onRefresh();
              setSearchValue('');
              getData({});
            }}
          />
        }
        // contentContainerStyle={{ alignItems: "center", flexGrow: 1 }}
        style={{flex: 1, marginHorizontal: 16, marginVertical: 16}}
        ItemSeparatorComponent={() => <View style={{height: 16}} />}
        renderItem={({item, index}: any) => {
          const discount =
            (item.Price * item.Quantity * (item.Discount ?? 0)) / 100;
          let devTotalPrice = item.Price * item.Quantity - discount;
          const vat = devTotalPrice * ((item.Vat ?? 0) / 100);
          if (item.ProductId) {
            var _product = products.find(e => e?.Id === item?.ProductId);
          }
          const _fileInfor = files
            .filter(e => (_product ?? item).Img?.includes(e.Id))
            .filter((e, i, a) => a.findIndex(eL => eL.Id === e.Id) === i);

          return (
            <ListTile
              key={item.Id}
              leading={
                _fileInfor.length ? (
                  <SkeletonImage
                    source={{uri: ConfigAPI.imgUrlId + _fileInfor[0].Id}}
                    style={{
                      width: 55,
                      height: 60,
                      borderRadius: 4,
                      overflow: 'hidden',
                      paddingTop: 6,
                    }}
                  />
                ) : (
                  <View />
                )
              }
              isClickLeading={false}
              style={{
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
                borderRadius: 8,
              }}
              listtileStyle={{gap: 16, alignItems: 'flex-start'}}
              title={`${item?.Name ?? '-'}`}
              bottom={
                <View
                  style={{
                    flex: 1,
                    alignItems: 'flex-start',
                    width: '100%',
                    paddingTop: 4,
                    gap: 4,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`Mã thiết bị: ${item?.Code ?? '-'}`}</Text>
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`Trạng thái: ${item.Status === 2 ? 'Đang hoạt động' : 'Đang số hóa'}`}</Text>
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`Ngày tạo: ${item.DateCreated ? Ultis.datetoString(new Date(item.DateCreated)) : ''}`}</Text>
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>{`Đơn vị: ${item.Unit ?? _product?.Unit ?? '-'}`}</Text>
                  {!formId ? (
                    <View
                      style={{
                        flex: 1,
                        alignItems: 'flex-start',
                        width: '100%',
                        gap: 4,
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Giá tiền (VNĐ): ${Ultis.money(item.Price) ?? '-'}`}</Text>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Giảm giá (VNĐ): ${Ultis.money(discount) ?? '-'} - Thuế VAT (VNĐ): ${Ultis.money(vat) ?? '-'}`}</Text>
                      <Text
                        style={{
                          ...TypoSkin.body2,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>{`Thành tiền: ${Ultis.money(devTotalPrice + vat)}`}</Text>
                    </View>
                  ) : null}
                  {!disabled ? (
                    <View
                      style={{
                        alignSelf: 'flex-end',
                        flex: 1,
                        gap: 16,
                        paddingTop: 16,
                        flexDirection: 'row',
                      }}>
                      <Winicon
                        src="outline/user interface/s-edit"
                        size={16}
                        onClick={() => {
                          showAddEdit(item);
                        }}
                      />
                      {data?.CustomerId === user?.Id ||
                      item.Status !== DeviceBioStatus.active ? (
                        <Winicon
                          src="outline/user interface/trash-can"
                          size={16}
                          onClick={() => {
                            showDialog({
                              ref: dialogRef,
                              status: ComponentStatus.WARNING,
                              title: 'Bạn chắc chắn muốn xóa',
                              onSubmit: () => {
                                deleteItem([item?.Id]);
                              },
                            });
                          }}
                        />
                      ) : null}
                    </View>
                  ) : null}
                </View>
              }
              titleStyle={{...TypoSkin.title3, paddingBottom: 8}}
              subtitle={`${Ultis.money(item.Price)} VNĐ`}
            />
          );
        }}
        ListEmptyComponent={() =>
          isLoading && searchValue != '' ? (
            Array.from(Array(10)).map((_, index) => (
              <View key={index} style={{marginBottom: 16}}>
                <CardToiletHoriSkeleton />
              </View>
            ))
          ) : (
            <EmptyPage title="Nhà vệ sinh chưa có thiết bị nào" />
          )
        }
        ListFooterComponent={() => <View style={{height: 65}} />}
      />
      {!disabled ? (
        <WScreenFooter style={{paddingHorizontal: 16}}>
          <AppButton
            title={'Thêm thiết bị'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              showAddEdit(null);
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      ) : null}
    </View>
  );
}

const PopupAddEditDevice = forwardRef(function PopupAddEditDevice(
  data: {toiletId: any; id: any; onSubmit: any; formId: any},
  ref: any,
) {
  const {toiletId, id, onSubmit, formId} = data;
  const dialogRef = useRef<any>();
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      ToiletId: toiletId,
      Status: formId ? DeviceBioStatus.inactive : DeviceBioStatus.active,
      DateStart: undefined,
      DateEnd: undefined,
      Quantity: 1,
      Price: undefined,
      Discount: undefined,
      Vat: undefined,
    },
  });

  const [open, setOpen] = useState(false);
  const [open1, setOpen1] = useState(false);

  const controller = new DataController('Device');
  const popupRef = useRef<any>();
  const [img, setImg] = useState<Array<any>>([]);
  const user = useSelectorCustomerState().data;

  useEffect(() => {
    if (id) {
      controller.getById(id).then(async res => {
        if (res.code === 200) {
          if (res.data.ProductId) {
            const productController = new DataController('Product');
            const product = await productController.getById(res.data.ProductId);
            if (product.code === 200) {
              res.data.Img = product.data.Img;
              res.data.Description = product.data.Specifications;
              res.data.Unit = product.data.Unit;
            }
          }
          Object.keys(res.data).forEach(key => {
            if (key === 'DateStart' || key === 'DateEnd') {
              methods.setValue(
                key,
                `${Ultis.datetoString(new Date(res.data[key]))}`,
              );
            } else if (key === 'Price') {
              methods.setValue(key, Ultis.money(res.data[key]));
            } else {
              methods.setValue(key, `${res.data[key] ?? ''}`);
            }
          });
          const _tmpFileIds = res.data.Img;
          BaseDA.getFilesInfor([_tmpFileIds]).then(resFile => {
            if (resFile.code === 200)
              setImg([
                ...resFile.data.filter(
                  (e: any) => e !== undefined && e !== null,
                ),
              ]);
          });
        }
      });
    }
  }, []);

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 1,
    });
    if (image) {
      _uploadFiles({
        name: image.filename ?? 'new file img',
        type: image.mime,
        uri: image.path,
      });
    }
  };

  const _uploadFiles = async (files: {
    uri: string;
    type: string;
    name: string;
  }) => {
    const res = await BaseDA.uploadFiles([files]);
    if (res?.length) {
      setImg([res[0]]);
      methods.setValue('Img', res.map((e: any) => e.Id).join(','));
    }
  };

  const _onSubmit = async (ev: any) => {
    var item = {
      ...ev,
      Img: methods.watch('Img') ? methods.watch('Img') : undefined,
      Quantity: methods.getValues('Quantity')
        ? parseInt(methods.getValues('Quantity'))
        : undefined,
      Vat: methods.getValues('Vat')
        ? parseInt(methods.getValues('Vat'))
        : undefined,
      Discount: methods.getValues('Discount')
        ? parseInt(methods.getValues('Discount'))
        : undefined,
      Price: methods.getValues('Price')
        ? parseInt(methods.getValues('Price').replace(/,/g, ''))
        : undefined,
      DateCreated: Date.now(),
      DateStart: methods.getValues('DateStart')
        ? Ultis.stringToDate(methods.getValues('DateStart')).getTime()
        : undefined,
      DateEnd: methods.getValues('DateEnd')
        ? Ultis.stringToDate(methods.getValues('DateEnd')).getTime()
        : undefined,
    };

    if (id) {
      controller.edit([item]).then(() => {
        onSubmit();
      });
    } else {
      const quantity = ev.Quantity;
      const tmp = [];
      for (let i = 0; i < quantity; i++) {
        tmp.push({...item, Id: i ? randomGID() : ev.Id, Quantity: 1});
      }
      controller.add(tmp).then(() => {
        onSubmit();
      });
    }
    closePopup(ref);
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={id ? `Chỉnh sửa thiết bị` : 'Thêm mới thiết bị'}
        prefix={<View />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 75 : 0}
        style={{height: '100%', width: '100%', paddingHorizontal: 16}}>
        <ScrollView>
          <View style={{gap: 18, paddingBottom: 156}}>
            {/* thong tin chung */}
            <View style={{flex: 1, gap: 18}}>
              <TextFieldForm
                required
                label="Tên"
                textFieldStyle={{padding: 16}}
                style={{width: '100%'}}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Name"
              />
              <View style={{gap: 8}}>
                <Text numberOfLines={1} style={TypoSkin.label3}>
                  Ảnh
                </Text>
                {methods.watch('Img') && img?.length > 0 ? null : (
                  <TouchableOpacity
                    onPress={() => {
                      pickerImg();
                    }}
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 8,
                      borderWidth: 0.4,
                      borderColor: ColorThemes.light.neutral_main_border,
                      borderStyle: 'dashed',
                      borderRadius: 8,
                      padding: 8,
                    }}>
                    <SkeletonImage
                      source={{
                        uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                      }}
                      style={{width: 35, height: 35, objectFit: 'cover'}}
                    />
                    <Text
                      numberOfLines={1}
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      Thêm ảnh
                    </Text>
                  </TouchableOpacity>
                )}
                {methods.watch('Img') &&
                  img?.map((item: any, index: number) => {
                    return (
                      <ListTile
                        key={`${index}`}
                        leading={
                          <SkeletonImage
                            source={{uri: ConfigAPI.imgUrlId + item.Id}}
                            style={{width: 65, height: 65, objectFit: 'cover'}}
                          />
                        }
                        title={item?.Name ?? `Ảnh ${index + 1}`}
                        titleStyle={[
                          TypoSkin.heading7,
                          {color: ColorThemes.light.neutral_text_title_color},
                        ]}
                        subtitle={`${Math.round(item.Size / (1024 * 1024))}MB`}
                        listtileStyle={{gap: 16}}
                        style={{
                          borderColor:
                            ColorThemes.light.neutral_main_border_color,
                          borderWidth: 1,
                          padding: 8,
                        }}
                        trailing={
                          <TouchableOpacity
                            onPress={async () => {
                              methods.setValue('Img', undefined);
                              setImg([]);
                            }}
                            style={{padding: 4}}>
                            <FontAwesomeIcon
                              icon={faMinusCircle}
                              size={20}
                              color="#D72525FF"
                              style={{
                                backgroundColor: '#fff',
                                borderRadius: 20,
                              }}
                            />
                          </TouchableOpacity>
                        }
                      />
                    );
                  })}
              </View>
              <TextFieldForm
                required
                label="Đơn vị"
                textFieldStyle={{padding: 16}}
                style={{width: '100%'}}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Unit"
              />
              <TextFieldForm
                required
                label="Số lượng"
                textFieldStyle={{padding: 16}}
                style={{width: '100%'}}
                type="number-pad"
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Quantity"
              />
            </View>
            {/* thong tin bổ sung */}
            {user?.Type === CustomerType.partner ? (
              <View style={{gap: 18}}>
                <Text numberOfLines={1} style={TypoSkin.label2}>
                  Thông tin bổ sung
                </Text>
                <TextFieldForm
                  required
                  label="Giá"
                  textFieldStyle={{padding: 16}}
                  style={{width: '100%'}}
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Price"
                  type="money"
                  returnKeyType="done"
                  onBlur={async (value: any) => {
                    if (!value) return;
                    let newPrice = parseInt(value.replaceAll(',', ''));
                    if (!isNaN(newPrice)) {
                      value = Ultis.money(newPrice);
                    } else value = Ultis.money(methods.getValues('Price'));
                  }}
                />
                <TextFieldForm
                  label="Code"
                  textFieldStyle={{padding: 16}}
                  style={{width: '100%'}}
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Code"
                />
                <TextFieldForm
                  label="Vat"
                  textFieldStyle={{padding: 16}}
                  style={{width: '100%'}}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Vat"
                />
                <TextFieldForm
                  label="Discount"
                  textFieldStyle={{padding: 16}}
                  style={{width: '100%'}}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Discount"
                />

                <View style={{flexDirection: 'row', gap: 16}}>
                  <View style={{flex: 1, height: 65, gap: 8}}>
                    <Text
                      style={{
                        fontWeight: 'bold',
                        color: ColorThemes.light.neutral_text_title_color,
                      }}>
                      Ngày mua
                    </Text>
                    <TouchableOpacity
                      onPress={() => setOpen(true)}
                      style={{
                        padding: 8,
                        alignItems: 'flex-start',
                        justifyContent: 'center',
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        borderRadius: 8,
                        width: '100%',
                        flex: 1,
                        borderWidth: 1,
                      }}>
                      <Text
                        style={{
                          color: ColorThemes.light.neutral_text_title_color,
                        }}>
                        {methods.watch('DateStart')}
                      </Text>
                    </TouchableOpacity>
                    <DatePicker
                      modal
                      open={open}
                      date={Ultis.stringToDate(methods.getValues('DateStart'))}
                      title={'Ngày mua'}
                      mode="date"
                      locale="vi"
                      dividerColor={'#f2f5f8'}
                      maximumDate={
                        methods.watch('DateEnd')
                          ? Ultis.stringToDate(methods.watch('DateEnd'))
                          : undefined
                      }
                      confirmText="Xác nhận"
                      theme="light"
                      cancelText="Hủy"
                      onConfirm={date => {
                        setOpen(false);
                        methods.setValue(
                          'DateStart',
                          `${Ultis.datetoString(date)}`,
                        );
                      }}
                      onCancel={() => {
                        setOpen(false);
                      }}
                    />
                  </View>
                  <View style={{flex: 1, height: 65, gap: 8}}>
                    <Text
                      style={{
                        fontWeight: 'bold',
                        color: ColorThemes.light.neutral_text_title_color,
                      }}>
                      Ngày hết hạn bảo hành
                    </Text>
                    <TouchableOpacity
                      onPress={() => setOpen1(true)}
                      style={{
                        height: 35,
                        padding: 8,
                        alignItems: 'flex-start',
                        justifyContent: 'center',
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        borderRadius: 8,
                        width: '100%',
                        flex: 1,
                        borderWidth: 1,
                      }}>
                      <Text
                        style={{
                          color: ColorThemes.light.neutral_text_title_color,
                        }}>
                        {methods.watch('DateEnd')}
                      </Text>
                    </TouchableOpacity>
                    <DatePicker
                      modal
                      open={open1}
                      mode="date"
                      locale="vi"
                      date={Ultis.stringToDate(methods.getValues('DateEnd'))}
                      title={'Ngày hết hạn bảo hành'}
                      confirmText="Xác nhận"
                      cancelText="Hủy"
                      theme="light"
                      minimumDate={
                        methods.watch('DateStart')
                          ? Ultis.stringToDate(methods.watch('DateStart'))
                          : undefined
                      }
                      dividerColor={'#f2f5f8'}
                      onConfirm={date => {
                        setOpen1(false);
                        methods.setValue(
                          'DateEnd',
                          `${Ultis.datetoString(date)}`,
                        );
                      }}
                      onCancel={() => {
                        setOpen1(false);
                      }}
                    />
                  </View>
                </View>
                <TextFieldForm
                  control={methods.control}
                  name="Description"
                  label="Mô tả"
                  errors={methods.formState.errors}
                  placeholder={'Mô tả khác'}
                  style={{backgroundColor: ColorThemes.light.transparent}}
                  textFieldStyle={{
                    height: 100,
                    width: '100%',
                    paddingHorizontal: 16,
                    paddingTop: 16,
                    paddingBottom: 16,
                    justifyContent: 'flex-start',
                    backgroundColor: ColorThemes.light.transparent,
                  }}
                  textStyle={{textAlignVertical: 'top'}}
                  numberOfLines={10}
                  multiline={true}
                  register={methods.register}
                />
              </View>
            ) : null}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
        }}>
        <AppButton
          title={id ? 'Sửa' : 'Thêm mới'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            methods.handleSubmit(_onSubmit, _onError)();
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
