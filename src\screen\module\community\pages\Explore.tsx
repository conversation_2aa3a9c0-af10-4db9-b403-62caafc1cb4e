import {DrawerActions, useNavigation} from '@react-navigation/native';
import React, {useState, useRef, forwardRef, useEffect, useMemo} from 'react';
import {useForm} from 'react-hook-form';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Pressable,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import AppButton from '../../../../component/button';
import {ComponentStatus} from '../../../../component/component-status';
import {
  Winicon,
  showBottomSheet,
  hideBottomSheet,
  showSnackbar,
  FBottomSheet,
  FDialog,
  FTextField,
} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import {onShare} from '../../../../features/share';
import EmptyPage from '../../../../project-component/empty-page';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {AppDispatch} from '../../../../redux/store/store';
import {navigate, RootScreen} from '../../../../router/router';
import {Ultis} from '../../../../utils/Utils';
import {dialogCheckAcc} from '../../../layout/main-layout';
import {DefaultPost, SkeletonPlacePostCard} from '../card/defaultPost';
import {useNewsFeedData} from '../hook/newsFeedHook';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {ProfileView} from './Chat';

export default function Explore() {
  const [searchValue, setSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const filterMethods = useForm({shouldFocusError: false});

  const flatListRef = useRef<any>(null);

  const [isRefreshing, setIsRefreshing] = useState(false);

  const [isLoading, setLoading] = useState(false);
  const dispatch: AppDispatch = useDispatch();

  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const size = 10;
  const {data, loading, page} = useNewsFeedData(1, size);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        await dispatch(newsFeedActions.getNewFeed(1, size));
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };
  const handleLoadMore = async () => {
    // Kiểm tra các điều kiện để loadmore
    if (!loading && !isRefreshing && hasMore) {
      try {
        const result = newsFeedActions.getNewFeed(page + 1, size);

        // Kiểm tra kết quả trả về
        if ((result as any)?.payload?.length < size) {
          setHasMore(false); // Nếu số lượng data nhỏ hơn size, đánh dấu là hết data
        } else if ((result as any)?.payload?.length === 0) {
          setHasMore(false); // Nếu không có data trả về, đánh dấu là hết data
        }
      } catch (error) {
        console.error('Load more error:', error);
        setHasMore(false); // Nếu có lỗi, đánh dấu là hết data
      }
    }
  };

  const PostItem = ({
    item,
    user,
    dialogRef,
  }: {
    item: any;
    user: any;
    dialogRef: any;
  }) => {
    const actionView = (
      <View
        style={{
          flexDirection: 'row',
          paddingTop: 16,
          alignItems: 'center',
          gap: 8,
        }}>
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            padding: 4,
            height: 24,
            paddingVertical: 0,
            paddingHorizontal: 8,
          }}
          onPress={async () => {
            if (user) {
              await dispatch(
                newsFeedActions.updateLike(item.Id, item.IsLike === true),
              );
            } else {
              ///TODO: check chưa login thì confirm ra trang login
              dialogCheckAcc({ref: dialogRef});
            }
          }}
          title={
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              {item.Likes ?? 0}
            </Text>
          }
          textColor={
            item.IsLike === true
              ? ColorThemes.light.error_main_color
              : ColorThemes.light.neutral_text_subtitle_color
          }
          prefixIconSize={12}
          prefixIcon={
            item.IsLike === true
              ? 'fill/emoticons/heart'
              : 'outline/emoticons/heart'
          }
        />
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            padding: 4,
            height: 24,
            paddingVertical: 0,
            paddingHorizontal: 8,
          }}
          onPress={async () => {
            navigate(RootScreen.PostDetail, {item: item});
          }}
          prefixIcon={'outline/user interface/b-comment'}
          prefixIconSize={12}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
          title={
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              {item.Comment ?? 0}
            </Text>
          }
        />
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            padding: 4,
            height: 24,
            paddingVertical: 0,
            paddingHorizontal: 8,
          }}
          onPress={async () => {
            onShare({content: 'Hello world'});
          }}
          prefixIcon={'fill/arrows/social-sharing'}
          prefixIconSize={12}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
      </View>
    );

    const trailingView = (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 4,
        }}>
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          onPress={async () => {
            if (user) {
              await dispatch(
                newsFeedActions.addBookmark(item.Id, item.IsBookmark === true),
              );
            } else {
              ///TODO: check chưa login thì confirm ra trang login
              dialogCheckAcc({ref: dialogRef});
            }
          }}
          containerStyle={{
            borderRadius: 100,
            padding: 6,
            height: 24,
            width: 24,
          }}
          title={
            <Winicon
              src={
                item.IsBookmark === true
                  ? 'fill/user interface/bookmark'
                  : 'outline/user interface/bookmark'
              }
              size={14}
              color={
                item.IsBookmark === true
                  ? ColorThemes.light.warning_main_color
                  : ColorThemes.light.neutral_text_subtitle_color
              }
            />
          }
        />
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          onPress={() => {
            showBottomSheet({
              ref: bottomSheetRef,
              title: 'Actions',
              suffixAction: <View />,
              prefixAction: (
                <TouchableOpacity
                  onPress={() => hideBottomSheet(bottomSheetRef)}
                  style={{padding: 6, alignItems: 'center'}}>
                  <Winicon
                    src="outline/layout/xmark"
                    size={20}
                    color={ColorThemes.light.neutral_text_body_color}
                  />
                </TouchableOpacity>
              ),
              children: (
                <View
                  style={{
                    gap: 8,
                    height: Dimensions.get('window').height / 4,
                    width: '100%',
                    backgroundColor:
                      ColorThemes.light.neutral_absolute_background_color,
                  }}>
                  <ListTile
                    onPress={() => {
                      showSnackbar({
                        message: 'Chức năng đang được phát triển',
                        status: ComponentStatus.WARNING,
                      });
                    }}
                    title={'Report post'}
                    titleStyle={{...TypoSkin.body3}}
                  />
                  {item.CustomerId === user.Id && (
                    <ListTile
                      onPress={() => {
                        hideBottomSheet(bottomSheetRef);

                        navigation.push(RootScreen.createPost, {
                          editPost: item,
                          groupId: null,
                        });
                        // showSnackbar({
                        //   message: 'Chức năng đang được phát triển',
                        //   status: ComponentStatus.WARNING,
                        // });
                      }}
                      title={'Edit post'}
                      titleStyle={{...TypoSkin.body3}}
                    />
                  )}
                  {item.CustomerId === user.Id && (
                    <ListTile
                      onPress={() => {
                        hideBottomSheet(bottomSheetRef);
                        dispatch(newsFeedActions.deletePost(item));
                      }}
                      title={'Delete post'}
                      titleStyle={{...TypoSkin.body3}}
                    />
                  )}
                </View>
              ),
            });
          }}
          containerStyle={{
            borderRadius: 100,
            padding: 6,
            height: 24,
            width: 24,
          }}
          title={
            <Winicon
              src={'fill/user interface/menu-dots'}
              size={14}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
          }
        />
      </View>
    );

    return (
      <DefaultPost
        data={{
          ...item,
          relativeUser:
            item.CustomerId === user?.Id
              ? {
                  image: user?.AvatarUrl,
                  title: user?.Name,
                  subtitle: Ultis.getDiffrentTime(item.DateCreated),
                }
              : item.relativeUser,
        }}
        containerStyle={{paddingHorizontal: 0}}
        onPressDetail={() => navigate(RootScreen.PostDetail, {item: item})}
        actionView={actionView}
        onPressHeader={() =>
          navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId})
        }
        trailingView={trailingView}
        showContent={true}
      />
    );
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <Winicon src="fill/user interface/apps" size={20} />
          </TouchableOpacity>
        }
        title="Explore"
        trailing={<ProfileView />}
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              height: 56,
              gap: 8,
              paddingTop: 16,
              paddingBottom: 16,
            }}>
            <FTextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={async (vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder="Tìm kiếm..."
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              }
            />
          </View>
        }
      />

      <FlatList
        ref={flatListRef}
        data={
          searchValue
            ? data.filter(
                (item: any) =>
                  item?.Name?.toLowerCase().includes(
                    searchValue.toLowerCase(),
                  ) ||
                  item?.Content?.toLowerCase().includes(
                    searchValue.toLowerCase(),
                  ),
              )
            : data
        }
        renderItem={({item, index}) => (
          <PostItem item={item} user={user} dialogRef={dialogRef} />
        )}
        keyExtractor={item => item.Id.toString()}
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={{
          gap: 8,
          backgroundColor: ColorThemes.light.neutral_main_background_color,
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading) {
            return (
              <View style={{gap: 8}}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlacePostCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          }
          return <EmptyPage />;
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return <SkeletonPlacePostCard />;
          }
          if (!hasMore && data.length > 0) {
            return (
              <View
                style={{
                  padding: 16,
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: ColorThemes.light.neutral_text_subtitle_color,
                    ...TypoSkin.subtitle2,
                  }}>
                  Không còn dữ liệu
                </Text>
              </View>
            );
          }
          return null;
        }}
      />
    </View>
  );
}
