import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useCallback} from 'react';
import React from 'react';
import {useListProductHook} from '../hooks';
import {ColorThemes} from '../../../../assets/skin/colors';
import EmptyPage from '../../../../project-component/empty-page';
import {ProductItem} from '../../../../types/ProductType';
import DefaultBanner from '../../../module/banner/DefaultBanner';
import ProductBestSellerCard from '../../../module/product/component/card/ProductBestSellerCard';

const {width} = Dimensions.get('window');

const ListProduct = () => {
  const {
    data,
    onLoading,
    page,
    isLoadingMore,
    isRefreshing,
    handleProductPress,
    handleFavoritePress,
    onAddToCart,
    handleLoadMore,
    handleRefresh,
    keyExtractor,
  } = useListProductHook();

  // Render product item
  const renderProductItem = useCallback(
    ({item}: {item: ProductItem}) => (
      <View style={styles.productItemContainer}>
        <ProductBestSellerCard
          item={item}
          onPress={handleProductPress}
          onAddToCart={onAddToCart}
          onFavoritePress={handleFavoritePress}
          width={(width - 48) / 2}
          height={((width - 48) / 2) * 1.8}
        />
      </View>
    ),
    [handleProductPress, onAddToCart, handleFavoritePress],
  );

  // Render footer loading
  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.footerLoading}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }, [isLoadingMore]);

  // Show loading for first page
  if (onLoading && page === 1) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // Show empty page when no data
  if (data.length === 0 && !onLoading) {
    return <EmptyPage />;
  }

  return (
    <FlatList
      data={data}
      renderItem={renderProductItem}
      keyExtractor={keyExtractor}
      numColumns={2}
      contentContainerStyle={styles.flatListContent}
      ListHeaderComponent={() => (
        <>
          <View style={styles.bannerContainer}>
            <DefaultBanner />
          </View>
          <Text style={styles.sectionTitle}>Sản phẩm gợi ý</Text>
        </>
      )}
      ListFooterComponent={renderFooter}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          colors={[ColorThemes.light.primary_main_color]}
          tintColor={ColorThemes.light.primary_main_color}
        />
      }
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true} // Optimize performance
      maxToRenderPerBatch={10} // Limit items rendered per batch
      windowSize={10} // Optimize memory usage
    />
  );
};

const styles = StyleSheet.create({
  productItemContainer: {
    marginHorizontal: 8,
  },
  footerLoading: {
    paddingVertical: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flatListContent: {
    paddingHorizontal: 8,
    paddingBottom: 16,
  },
  bannerContainer: {
    marginHorizontal: 8,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
    marginLeft: 8,
    marginTop: 16,
    marginBottom: 12,
  },
});

export default ListProduct;
