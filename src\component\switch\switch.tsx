import React from 'react';
import {Switch, Text, TouchableOpacity, View} from 'react-native';
import {switchStyles} from './styles';

export function WSwitch({
  onChange,
  value = false,
  color,
  size = 1,
}: {
  onChange: (value: boolean) => void;
  value: boolean;
  color: string;
  size?: number;
}) {
  return (
    <Switch
      trackColor={{false: '#EFEFF0', true: color ?? '#287CF0'}}
      ios_backgroundColor="#EFEFF0"
      style={{transform: [{scaleX: size}, {scaleY: size}]}}
      onValueChange={onChange}
      value={value}
    />
  );
}

export const CustomSwitch: React.FC<{
  isOn?: boolean;
  onPress?: () => void;
  disabled?: boolean;
}> = ({isOn = true, onPress, disabled = false}) => {
  return (
    <TouchableOpacity
      style={[
        switchStyles.switchContainer,
        isOn && switchStyles.switchOn,
        disabled && switchStyles.switchDisabled,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={disabled}>
      <Text style={switchStyles.switchText}>{isOn ? 'ON' : 'OFF'}</Text>
      <View
        style={[switchStyles.switchCircle, isOn && switchStyles.switchCircleOn]}
      />
    </TouchableOpacity>
  );
};
export const ReverseCustomSwitch: React.FC<{
  isOn?: boolean;
  onPress?: () => void;
  disabled?: boolean;
}> = ({isOn = false, onPress, disabled = false}) => {
  return (
    <TouchableOpacity
      style={[
        switchStyles.switchContainer,
        disabled && switchStyles.switchDisabled,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={disabled}>
      <View style={[switchStyles.switchCircle]} />
      <Text style={switchStyles.switchText}>{'OFF'}</Text>
    </TouchableOpacity>
  );
};
