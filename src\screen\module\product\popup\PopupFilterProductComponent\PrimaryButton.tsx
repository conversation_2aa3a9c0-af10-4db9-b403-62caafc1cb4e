import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';

interface PrimaryButtonProps {
  title: string;
  onPress: () => void;
}

const PrimaryButton = ({title, onPress}: PrimaryButtonProps) => (
  <TouchableOpacity style={styles.primaryButton} onPress={onPress}>
    <Text style={styles.primaryButtonText}>{title}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  primaryButton: {
    backgroundColor: '#0052FF',
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: 'center',
    marginTop: 15,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PrimaryButton;
