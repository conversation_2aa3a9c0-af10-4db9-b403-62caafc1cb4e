import {StyleSheet, View} from 'react-native';
import {Text} from 'react-native-paper';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {useRoute} from '@react-navigation/native';
import {useRef} from 'react';
import Home from './Home';
import Explore from './Explore';
import GroupIndex from '../groups';
import Groups from './groupIndex';
import Chat from './Chat';
import Notif from './Notif';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {ColorThemes} from '../../../../assets/skin/colors';
import {FDialog, Winicon} from '../../../../component/export-component';
import {TypoSkin} from '../../../../assets/skin/typography';
// import {useTranslation} from 'react-i18next';

const Tab = createBottomTabNavigator();

const bottomNavigateData = [
  {
    id: 0,
    name: 'Home',
    component: Home,
    activeIcon: 'fill/user interface/home',
    inActiveIcon: 'outline/user interface/home',
  },
  {
    id: 1,
    name: 'Explore',
    component: Explore,
    activeIcon: 'outline/user interface/search',
    inActiveIcon: 'outline/user interface/search',
  },
  {
    id: 2,
    name: 'Group',
    component: Groups,
    activeIcon: 'fill/users/team',
    inActiveIcon: 'outline/users/team',
  },
  {
    id: 3,
    name: 'Chat',
    component: Chat,
    activeIcon: 'fill/user interface/f-chat',
    inActiveIcon: 'outline/user interface/f-chat',
  },
  {
    id: 4,
    name: 'Notify',
    component: Notif,
    activeIcon: 'fill/user interface/bell',
    inActiveIcon: 'outline/user interface/bell',
  },
];

export default function CommunityLayout() {
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;

  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          tabBarStyle: styles.bar,
          tabBarActiveTintColor: ColorThemes.light.primary_main_color,
          tabBarInactiveTintColor:
            ColorThemes.light.neutral_text_subtitle_color,
          headerShown: false,
        }}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (
                    !user &&
                    item.id != 0 &&
                    item.id != 4 &&
                    item.id != 3 &&
                    item.id != 2
                  ) {
                    // dialogCheckAcc(dialogCheckAccRef, undefined);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={() => {
                return {
                  tabBarLabel: ({color, focused}: any) => (
                    <Text
                      style={[
                        TypoSkin.buttonText6,
                        {color: color, fontWeight: focused ? 'bold' : '400'},
                      ]}>
                      {item.name}
                    </Text>
                  ),
                  tabBarIcon: ({color, focused}: any) => (
                    <Winicon
                      src={focused ? item.activeIcon : item.inActiveIcon}
                      size={20}
                      color={color}
                    />
                  ),
                };
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  bar: {
    backgroundColor: '#ffffff',
    borderStyle: 'solid',
    borderTopColor: '#F5F5F5',
  },
  tabBarButton: {
    // width: 64,
    height: '100%',
    // gap: 4,
    transform: [{translateY: -14}],
    alignItems: 'center',
  },
});
