import {DataController} from '../../screen/base-controller';
import {getImage} from './rootAction';
import {showSnackbar} from '../../component/snackbar/snackbar';
import {ComponentStatus} from '../../component/component-status';
import {ProductItem} from '../../types/ProductType';
import productDA from '../../screen/module/product/productDA';

export const productAction = {
  find: async (
    config: {
      page?: number;
      size?: number;
      sortby?: any;
      searchRaw?: string;
      returns?: string[];
    },
    customerId?: string | undefined,
  ) => {
    const controller = new DataController('Product');
    const res = await controller.aggregateList(config);
    if (res.code === 200) {
      let newData = await getImage({items: res.data});
      if (customerId) {
        newData = await productDA.handleCheckProductFavorite({
          products: newData,
          customerId,
        });
      }
      return newData;
    }
    return [];
  },
  update: async (products: ProductItem[]) => {
    const controller = new DataController('Product');
    try {
      const res = await controller.edit(products);
      if (res.code === 200) {
        return res.data;
      }
    } catch (error: any) {
      showSnackbar({message: error.message, status: ComponentStatus.ERROR});
    }
  },
};
