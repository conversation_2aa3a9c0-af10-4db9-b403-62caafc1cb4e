import React, {useEffect, useCallback, memo, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Text,
  ImageBackground,
} from 'react-native';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import {ColorThemes} from '../../../assets/skin/colors';
import iconSvg from '../../../svgs/iconSvg';
import AppSvg from '../../../component/AppSvg';
import {ComponentStatus} from '../../../component/component-status';
import {showDialog} from '../../../component/export-component';
import {useSelectorCustomerState} from '../../../redux/hooks/hooks';
import SearchInput from '../../../component/textfield/SearchInput';
import {TypoSkin} from '../../../assets/skin/typography';
import brandSvg from '../../../svgs/brandSvg';
import {get} from '../../../utils/lodash';
import {PopupQrcodeScan} from '../../../features/qrcode-scanner/qrcode-scan';
import {FPopup, showPopup} from '../../../component/popup/popup';
import {Ultis} from 'utils/Utils';
import {ListTile} from 'wini-mobile-components';

// Types
interface HeaderIconButtonProps {
  onPress?: () => void;
  children: React.ReactNode;
}

interface RightHeaderActionsProps {
  onQrPress: () => void;
  onNotificationPress: () => void;
  onMenuPress: () => void;
}

// Header Icon Button Component
const HeaderIconButton = memo<HeaderIconButtonProps>(({onPress, children}) => (
  <TouchableOpacity style={styles.iconButton} onPress={onPress}>
    <View style={styles.iconCircle}>{children}</View>
  </TouchableOpacity>
));

// Right Header Actions Component
const RightHeaderActions = memo<RightHeaderActionsProps>(
  ({onQrPress, onNotificationPress, onMenuPress}) => (
    <View style={styles.rightIcons}>
      <HeaderIconButton onPress={onQrPress}>
        <AppSvg SvgSrc={iconSvg.qr} size={16} />
      </HeaderIconButton>
      <HeaderIconButton onPress={onNotificationPress}>
        <AppSvg SvgSrc={iconSvg.notification} size={16} />
      </HeaderIconButton>
      <HeaderIconButton onPress={onMenuPress}>
        <AppSvg SvgSrc={iconSvg.menu} size={16} />
      </HeaderIconButton>
    </View>
  ),
);

const HeaderLogo: React.FC = ({
  showSearch = false,
  onSearch,
}: {
  showSearch?: boolean;
  onSearch?: (value?: string) => void;
}) => {
  const navigation = useNavigation();
  const customer = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  const popupRef = useRef<any>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Authentication check
  const handleProtectedAction = useCallback(
    (action: () => void) => {
      if (!customer?.Id) {
        showDialog({
          ref: dialogRef,
          status: ComponentStatus.WARNING,
          title: 'Vui lòng đăng nhập để sử dụng!',
          onSubmit: async () => {
            navigate(RootScreen.login);
          },
        });
        return;
      }
      action();
    },
    [customer?.Id],
  );

  // Action handlers
  const handleNotificationPress = useCallback(() => {
    handleProtectedAction(() => {
      navigate(RootScreen.NotificationIndex);
    });
  }, [handleProtectedAction]);

  const handleQrPress = useCallback(() => {
    handleProtectedAction(() => {
      showPopup({
        ref: popupRef,
        children: <PopupQrcodeScan popupRef={popupRef} />,
      });
    });
  }, [handleProtectedAction]);
  const handleMenuPress = useCallback(() => {}, [handleProtectedAction]);

  // Search handler
  const handlerSearch = useCallback((text: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      if (onSearch) onSearch(text);
    }, 1000);
  }, []);

  return (
    <View>
      <FPopup ref={popupRef} />
      <ImageBackground
        source={require('../../../assets/background-header.png')}>
        <ListTile
          style={{
            padding: 0,
            marginTop: 60,
            paddingHorizontal: 16,
            backgroundColor: 'transparent',
          }}
          listtileStyle={{gap: 8}}
          isClickLeading
          leading={
            <TouchableOpacity
              onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
              style={styles.logoButton}>
              <View style={styles.logoContainer}>
                <AppSvg SvgSrc={brandSvg.logo} size={36} />
              </View>
            </TouchableOpacity>
          }
          title={
            <View style={styles.userInfo}>
              <Text style={styles.greetingText} numberOfLines={1}>
                {customer?.Name ? `Xin chào, ${customer?.Name}` : 'Số hóa NVS'}
              </Text>
              <Text style={styles.locationText}>
                {
                  // show thứ ngày tháng năm
                  Ultis.datetoStringDefault()
                }
              </Text>
            </View>
          }
          trailing={
            <RightHeaderActions
              onQrPress={handleQrPress}
              onNotificationPress={handleNotificationPress}
              onMenuPress={handleMenuPress}
            />
          }
        />
        <View style={styles.searchSection}>
          {showSearch ? <SearchInput setDataSearch={handlerSearch} /> : null}
        </View>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContent: {
    marginTop: 50,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    paddingHorizontal: 12,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoButton: {
    flexDirection: 'row',
  },
  logoContainer: {
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 6,
  },
  userInfo: {},
  greetingText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  locationText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  searchSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingHorizontal: 12,
  },
});

export default HeaderLogo;
