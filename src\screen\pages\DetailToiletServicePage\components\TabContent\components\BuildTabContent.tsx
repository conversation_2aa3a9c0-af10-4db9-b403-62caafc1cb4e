import React, {useEffect} from 'react';
import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
} from '../../../../../module/service/components/da';
import EmptyPage from '../../../../../../project-component/empty-page';
import BuildTaskTab from '../../../../../module/workplace/components/form/BuildTaskTab';
import {DataController} from '../../../../../base-controller';
import {randomGID} from '../../../../../../utils/Utils';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface BuildTabContentProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  methods: any;
  guest: any;
  onChangeStatus: (status: any, setServicesValue?: any) => void;
}

export default function BuildTabContent({
  workData,
  serviceData,
  setServiceData,
  onRefresh,
  isRefreshing,
  methods,
  guest,
  onChangeStatus,
}: BuildTabContentProps) {
  const handleBuildSubmit = async () => {
    if (!serviceData || !workData) return;

    const taskController = new DataController('Task');
    const res = await taskController.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${TaskType.build} ${TaskType.build}] @Status:[${TaskStatus.open} ${TaskStatus.overdue}]`,
    });

    if (res.code === 200 && res.data.length) {
      taskController.edit(
        res.data.map((e: any) => ({
          ...e,
          Status: TaskStatus.done,
        })),
      );
    }

    if (serviceData.Status < ToiletServiceStatus.liquid) {
      await onChangeStatus(ToiletStatus.liquid);
      const now = new Date();
      const newTaskBuild = {
        Id: randomGID(),
        Name: 'Thanh lý hợp đồng',
        DateCreated: now.getTime(),
        Type: TaskType.liquid,
        Status: TaskStatus.open,
        DateStart: now.getTime(),
        Day: 3,
        DateEnd: new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() + 3,
          23,
          59,
        ).getTime(),
        CustomerId: serviceData.CustomerId,
        ToiletServicesId: serviceData.Id,
        ToiletId: workData[0].Id,
      };
      taskController.add([newTaskBuild]);
    }
  };

  if (
    serviceData?.Status != null &&
    serviceData.Status < ToiletServiceStatus.build
  ) {
    return <EmptyPage title={'Đơn hàng đang trong quá trình thiết kế'} />;
  }

  return (
    <BuildTaskTab
      data={workData}
      serviceData={serviceData}
      onRefreshing={onRefresh}
      isRefreshing={isRefreshing}
      setServiceData={setServiceData}
      methodParent={methods}
      customer={guest}
      onSubmit={handleBuildSubmit}
    />
  );
}
