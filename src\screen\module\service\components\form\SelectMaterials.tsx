import React, { useState, useRef, useEffect, useMemo, forwardRef } from 'react';
import { Dimensions, FlatList, Pressable, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { DataController } from '../../../../base-controller';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { TypoSkin } from '../../../../../assets/skin/typography';
import { Ultis } from '../../../../../utils/Utils';
import ListTile from '../../../../../component/list-tile/list-tile';
import { SkeletonImage } from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import { FN<PERSON>berPicker, Winicon } from '../../../../../component/export-component';
import AppButton from '../../../../../component/button';
import { closePopup, FPopup, showPopup } from '../../../../../component/popup/popup';
import { useForm } from 'react-hook-form';
import ScreenHeader from '../../../../layout/header';
import WScreenFooter from '../../../../layout/footer';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { CardMapSearchSkeleton } from '../../../../../features/map/local-component/map-item-shimmer';
import { CardHoriSkeleton } from '../../../../../project-component/skeletonCard';

interface Props {
    methods?: any;
}

const height = Dimensions.get('screen').height;

export default function SelectMaterials(props: Props) {
    const { methods } = props;

    const [selectedCate, setSelectedCate] = useState();
    const popupRef = useRef<any>()

    useEffect(() => {
        const cateController = new DataController("Category")
        cateController.getListSimple({
            page: 1, size: 1000,
            query: `(@Id:{782445755c884d14ba0eb40bde42f949}) | (@ParentId:{782445755c884d14ba0eb40bde42f949})`,
        }).then(async (res) => {
            if (res.code === 200) {
                const materialPartnerController = new DataController("Material")
                const count = await materialPartnerController.group({
                    searchRaw: "*",
                    reducers: "GROUPBY 1 @CategoryId REDUCE COUNT 0 AS _count"
                })
                const result = res.data.map((e: any) => ({ ...e, "_count": count.data.find((el: any) => el.CategoryId === e.Id)?.["_count"] ?? 0 }))
                const parent = result.find((e: any) => !e.ParentId)
                setSelectedCate(parent?.Id)
                const children = result.filter((el: any) => el.ParentId === parent?.Id).sort((a: any, b: any) => b.Sort - a.Sort)
                methods.setValue(parent.Id, { data: children, totalCount: children.length })
            }
        })
    }, []);

    const formDevices = () => {
        return (
            <View style={{}}>
                <FlatList
                    data={methods
                        .watch(selectedCate)
                        ?.data}
                    style={{ height: "100%", paddingBottom: 16 }}
                    keyExtractor={(item, index) => `${item.Id}`}
                    renderItem={({ item, index }: { item: any; index: number }) => {
                        return <View
                            key={item.Id}
                            style={{
                                marginBottom: index === methods
                                    .watch(selectedCate)
                                    ?.data.length - 1 ? 100 : 0
                            }}
                        >
                            <CateTile
                                key={item.Id}
                                item={item}
                                methods={methods}
                                showPopupSelectMaterial={showPopupSelectMaterial}
                            /></View>
                    }}
                    onEndReachedThreshold={0.1}
                    ListEmptyComponent={() => [0, 1, 2, 3].map((_, index) => <CardHoriSkeleton key={index} />)}
                />
            </View>
        );
    };

    const showPopupSelectMaterial = (item: any) => {
        showPopup({
            ref: popupRef,
            enableDismiss: true,
            children: <PopupSelectMaterial ref={popupRef} item={item}
                selecteds={methods.getValues("materials").filter((e: any) => e.CategoryId === item.Id && e.Id)}
                onDone={(items) => {
                    methods.setValue("materials", [...methods.getValues("materials").filter((e: any) => e.CategoryId !== item.Id), ...items.map(e => ({ ...e, "_Quantity": e["_Quantity"] ?? 1 }))])
                    closePopup(popupRef)
                }} />
        })
    }

    return (
        <View style={{ padding: 16, flex: 1 }}>
            <FPopup ref={popupRef} />
            {/* cate */}
            {formDevices()}
        </View>
    );
}

const CateTile = ({
    item,
    showPopupSelectMaterial,
    methods,
}: {
    item: any;
    showPopupSelectMaterial: any;
    methods: any;
}) => {
    const [onOpen, setOnOpen] = useState(true);
    const listMaterials = useMemo(() => {
        return methods
            .getValues('materials')
            .filter((e: any) => e.CategoryId === item.Id && e.Id);
    }, [methods.watch('materials')]);
    return (
        <ListTile
            key={item.Id}
            leading={
                <Winicon
                    src={ConfigAPI.imgUrlId + item.Img}
                    style={{ width: 32, height: 32 }}
                />
            }
            title={`${item.Name ?? ''} (${item._count ?? 0})`}
            titleStyle={[
                TypoSkin.heading7,
                { color: ColorThemes.light.neutral_text_title_color },
            ]}
            subtitle={
                listMaterials.length
                    ? `${listMaterials.length} sản phẩm đã chọn`
                    : 'Chọn sản phẩm'
            }
            subTitleStyle={[
                TypoSkin.subtitle4,
                { color: ColorThemes.light.neutral_text_subtitle_color },
            ]}
            listtileStyle={{ gap: 16 }}
            style={{
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderBottomWidth: 1,
            }}
            onPress={() => { if (listMaterials.length == 0) showPopupSelectMaterial(item) }}
            trailing={
                listMaterials.length == 0 ? (
                    <Winicon
                        onClick={() => showPopupSelectMaterial(item)}
                        src={'outline/layout/circle-plus'}
                        size={23}
                        color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                ) : (
                    <Winicon
                        onClick={() => setOnOpen(!onOpen)}
                        src={
                            onOpen
                                ? 'outline/arrows/arrow-sm-down'
                                : 'fill/arrows/arrow-sm-right'
                        }
                        size={28}
                        color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                )
            }
            bottom={
                <View
                    style={{
                        marginTop: 8,
                        alignContent: 'flex-start',
                        width: '100%',
                    }}>
                    {onOpen ?
                        listMaterials?.map((devItem: any) => {
                            return (
                                <ListTile
                                    key={devItem.Id}
                                    leading={
                                        <SkeletonImage
                                            source={{ uri: ConfigAPI.imgUrlId + devItem.Img }}
                                            style={{ width: 56, height: 56, borderRadius: 4 }}
                                        />
                                    }
                                    style={{ padding: 0, borderRadius: 0, paddingVertical: 8 }}
                                    title={devItem?.Name ?? ''}
                                    titleStyle={[
                                        TypoSkin.body3,
                                        { color: ColorThemes.light.neutral_text_body_color, fontWeight: "bold" },
                                    ]}
                                    subTitleStyle={[
                                        TypoSkin.subtitle4,
                                        { color: ColorThemes.light.neutral_text_body_color, fontWeight: "bold" },
                                    ]}
                                    subtitle={`${Ultis.money(devItem?.Price)}VNĐ`}
                                    trailing={
                                        <FNumberPicker buttonStyle={{ padding: 2 }} initValue={devItem["_Quantity"]} style={{}} onChange={(ev) => {
                                            if (ev) methods.setValue("devices", methods.getValues("devices").map((e: any) => e.Id === devItem.Id ? { ...e, "_Quantity": ev } : e))
                                            else methods.setValue("devices", methods.getValues("devices").filter((e: any) => e.Id !== devItem.Id))
                                        }}
                                        />
                                    }
                                />
                            );
                        }) : null}
                    {listMaterials.length && onOpen ? (
                        <AppButton
                            prefixIcon={'outline/layout/plus'}
                            prefixIconSize={16}
                            title={'Thêm'}
                            backgroundColor={ColorThemes.light.transparent}
                            borderColor={ColorThemes.light.primary_main_color}
                            containerStyle={{
                                borderStyle: 'dotted',
                                alignSelf: 'baseline',
                                paddingHorizontal: 8,
                            }}
                            height={32}
                            textColor={ColorThemes.light.primary_main_color}
                            onPress={() => showPopupSelectMaterial(item)}
                        />
                    ) : null}
                </View>
            }
        />
    );
};

const PopupSelectMaterial = forwardRef(function PopupSelectMaterial(data: { item: any, onDone: (values: Array<any>) => void, selecteds: [] }, ref: any) {
    const { item, onDone, selecteds } = data
    const filterMethods = useForm({ shouldFocusError: false })
    const MaterialController = new DataController("Material")
    const [Materials, setMaterials] = useState({ data: [], totalCount: undefined })
    const [selectedItems, setSelectedItems] = useState<Array<any>>([])

    const getData = async (page?: any) => {
        const res = await MaterialController.aggregateList({ page: page ?? 1, size: 10, searchRaw: `@CategoryId:{*${item.Id}*}${filterMethods.watch("Name")?.length ? ` @Name:(*${filterMethods.watch("Name")}*)` : ""}` })
        if (res.code === 200) setMaterials({ data: page ? [...Materials.data, ...res.data] : res.data, totalCount: res.totalCount })
    }

    useEffect(() => {
        if (item) {
            getData()
            if (selecteds.length) setSelectedItems(selecteds)
        }
    }, [item])

    return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height - 65, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: '#fff' }}>
        <ScreenHeader
            style={{
                backgroundColor: ColorThemes.light.transparent,
                flexDirection: 'row',
            }}
            title={`Chọn ${item.Name ?? 'vật liệu'}`}
            prefix={<View />}
            action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                <Winicon src="outline/layout/xmark" onClick={() => closePopup(ref)} size={20} color={ColorThemes.light.neutral_text_body_color} />
            </View>}
        />
        <View style={{ flex: 1 }}>
            {Materials.data.length == 0 ? <View style={{ flex: 1, height: "100%", alignItems: "center", justifyContent: "center" }}>
                <Text style={[TypoSkin.body1, { color: ColorThemes.light.neutral_text_body_color }]}>Không có dữ liệu</Text>
            </View> :
                <FlatList
                    data={Materials.data}
                    style={{ height: "100%", marginVertical: 6 }}
                    keyExtractor={(item, index) => `${item.Id}`}
                    // ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
                    renderItem={({ item, index }: { item: any; index: number }) => {
                        const isSelected = selectedItems.find((e: any) => e.Id === item.Id)
                        return <ListTile
                            key={`${item.Id}`}
                            onPress={() => {
                                if (isSelected) setSelectedItems(selectedItems.filter(e => e.Id !== item.Id))
                                else setSelectedItems([...selectedItems, item])
                            }}
                            leading={
                                <SkeletonImage
                                    source={{ uri: ConfigAPI.imgUrlId + item.Img }}
                                    style={{ width: 100, height: 100, borderRadius: 8 }}
                                />
                            }
                            title={item.Name ?? ''}
                            titleStyle={[
                                TypoSkin.heading6,
                                { color: ColorThemes.light.neutral_text_title_color, paddingBottom: 8 },
                            ]}
                            subtitle={<View style={{ flexDirection: "column", gap: 8, alignItems: "flex-start" }}>
                                <View style={{ flexDirection: "row", gap: 8, alignItems: "center" }}>
                                    <Winicon src="outline/shopping/label" size={12} color={ColorThemes.light.neutral_text_body_color} />
                                    <Text style={{ ...TypoSkin.subtitle3, color: ColorThemes.light.neutral_text_subtitle_color }}>{item.Description}</Text>
                                </View>
                                <View style={{ flexDirection: "row", gap: 8, alignItems: "center" }}>
                                    <Winicon src="outline/files/file-money" size={12} color={ColorThemes.light.neutral_text_body_color} />
                                    <Text style={[TypoSkin.body1, { color: ColorThemes.light.neutral_text_body_color, fontWeight: "bold" }]}>{Ultis.money(item.Price)}VNĐ</Text>
                                </View>
                            </View>}
                            listtileStyle={{ gap: 16, }}
                            style={{
                                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                                borderBottomWidth: 1,
                            }}
                            trailing={
                                <View>
                                    {isSelected ? <Winicon
                                        src={'fill/layout/circle-check'}
                                        size={20}
                                        color={ColorThemes.light.primary_main_color}
                                    /> : null}
                                </View>
                            }
                        />
                    }}
                    onEndReachedThreshold={0.1}
                />}
        </View>
        <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16 }}>
            <AppButton
                title={'Làm lại'}
                backgroundColor={ColorThemes.light.neutral_main_background_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={() => {
                    setSelectedItems([])
                }}
                textColor={ColorThemes.light.neutral_text_subtitle_color}
            />
            <AppButton
                title={selectedItems.length > 0 ? `Đã chọn ${selectedItems.length}` : 'Xong'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={() => {
                    onDone(selectedItems.map(e => {
                        const tmp = selecteds.find((el: any) => el.Id === e.Id)
                        if (tmp) return { ...e, "_Quantity": tmp["_Quantity"] }
                        else return e
                    }))
                }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
            />
        </WScreenFooter>
    </SafeAreaView>
});