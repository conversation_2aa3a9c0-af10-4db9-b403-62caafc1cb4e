# Tài liệu hàm getData - useNewOrderWorkList Hook

## Tổng quan
Hàm `getData` là một hàm bất đồng bộ (async) trong hook `useNewOrderWorkList` đư<PERSON>c sử dụng để lấy dữ liệu danh sách công việc đơn hàng mới từ server với khả năng phân trang và lọc dữ liệu.

## Chữ ký hàm
```typescript
const getData = async (page: number = 1, isLoadMore: boolean = false) => Promise<void>
```

## Tham số đầu vào
- `page` (number, mặc định = 1): Số trang cần lấy dữ liệu
- `isLoadMore` (boolean, mặc định = false): X<PERSON><PERSON> định có phải đang tải thêm dữ liệu hay không

## Chức năng chính

### 1. Quản lý trạng thái loading
- Nếu `isLoadMore = true`: Bật trạng thái `isLoadingMore`
- Nếu `isLoadMore = false`: Bật trạng thái `isLoading`, reset trang về 1, reset `hasMore = true`
- Nếu không đang refresh: Xóa dữ liệu cũ trong `works`

### 2. Xây dựng query tìm kiếm

#### Lọc theo trạng thái (Status)
- Nếu có `AttributeId` được chọn: Tạo query với các trạng thái đã chọn
- Nếu không có: Mặc định lọc theo trạng thái `register` và `liquid`

#### Lọc theo danh mục dịch vụ (CateServicesId)
- Nếu có `CateServicesId` được chọn: Tạo query lọc theo danh mục

#### Query tổng hợp
Kết hợp các điều kiện lọc với thông tin khách hàng:
- Lọc theo `CustomerId` của user hiện tại
- Nếu user là partner với role Coordinator/Consultant: Bao gồm cả owner ID
- Lọc theo số điện thoại của user

### 3. Lấy dữ liệu dịch vụ toilet
- Gọi API `serviceController.aggregateList()` với query đã xây dựng
- Sử dụng phân trang với `PAGE_SIZE = 10`

### 4. Xử lý dữ liệu toilet
Nếu có dữ liệu toilet:
- Lấy danh sách `toiletIds` duy nhất
- Gọi API để lấy thông tin khách hàng theo toilet
- Gọi API để lấy tên toilet
- Kết hợp dữ liệu toilet với tên toilet

### 5. Cập nhật state
- Nếu `isLoadMore = true`: Thêm dữ liệu mới vào cuối danh sách hiện tại
- Nếu `isLoadMore = false`: Thay thế toàn bộ danh sách bằng dữ liệu mới
- Cập nhật `totalCount` từ response
- Tính toán và cập nhật `hasMore` dựa trên tổng số item đã tải và tổng số item có sẵn

### 6. Xử lý lỗi
- Bắt và log lỗi nếu có
- Hiển thị thông báo lỗi bằng `showSnackbar` nếu API trả về lỗi

### 7. Cleanup
Trong khối `finally`:
- Tắt tất cả trạng thái loading (`isLoading`, `isRefreshing`, `isLoadingMore`)

## Luồng xử lý dữ liệu

```
1. Bật trạng thái loading phù hợp
2. Xây dựng query tìm kiếm dựa trên filter
3. Gọi API lấy danh sách dịch vụ toilet
4. Nếu có dữ liệu:
   a. Lấy danh sách toilet IDs
   b. Lấy thông tin khách hàng theo toilet
   c. Lấy tên toilet
   d. Kết hợp dữ liệu
5. Cập nhật state works
6. Tính toán hasMore cho phân trang
7. Cập nhật currentPage
8. Tắt trạng thái loading
```

## Các API được sử dụng
1. `serviceController.aggregateList()` - Lấy danh sách dịch vụ toilet
2. `toiletController.group()` - Nhóm toilet theo customer
3. `customerController.getByListId()` - Lấy thông tin khách hàng
4. `toiletController.getListSimple()` - Lấy thông tin cơ bản của toilet

## Phụ thuộc
- `DataController` - Controller để gọi API
- `showSnackbar` - Hiển thị thông báo
- `ComponentStatus` - Enum trạng thái component
- `ToiletServiceStatus` - Enum trạng thái dịch vụ toilet
- `CustomerRole`, `CustomerType` - Enum role và type của customer

## Sử dụng
Hàm này được gọi tự động khi:
- Component mount lần đầu
- `owner`, `user`, `userRole`, hoặc `onLoading` thay đổi
- User thực hiện refresh (`onRefresh`)
- User tải thêm dữ liệu (`loadMore`)
