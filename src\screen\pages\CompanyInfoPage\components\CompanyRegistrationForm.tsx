import React, {useState, useEffect} from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  Keyboard,
} from 'react-native';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import {validatePhoneNumber} from 'utils/validate';
import {DataController} from '../../../base-controller';
import {
  TextFieldForm,
  FAddressPickerForm,
  Fselect1Form,
} from 'project-component/component-form';

interface CompanyRegistrationFormProps {
  methods: any;
  bankList: Array<any>;
  onCancel: () => void;
  onSubmit: () => void;
}

export const CompanyRegistrationForm: React.FC<
  CompanyRegistrationFormProps
> = ({methods, bankList, onCancel, onSubmit}) => {
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidHideListener?.remove();
      keyboardDidShowListener?.remove();
    };
  }, []);

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}>
        <ScrollView>
          <Pressable style={styles.formContainer}>
            <TextFieldForm
              label="Tên doanh nghiệp"
              required
              textFieldStyle={{padding: 16}}
              style={styles.formField}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Name"
            />
            <TextFieldForm
              label="Ngành nghề"
              textFieldStyle={{padding: 16}}
              style={styles.formField}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Industry"
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              label="Số điện thoại doanh nghiệp"
              required
              returnKeyType="done"
              style={styles.formField}
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
                padding: 16,
              }}
              register={methods.register}
              type="number-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
                  return;
                }
                var mobile = ev.trim();
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile;
                }
                const val = validatePhoneNumber(mobile);
                if (!val) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
                  return;
                }

                const controller = new DataController('CompanyProfile');

                const res = await controller.getListSimple({
                  page: 1,
                  size: 1,
                  query: `@Mobile: (${mobile})`,
                });

                if (res.data && res.data.length > 0) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại đã được đăng ký',
                  });
                } else {
                  methods.clearErrors('Mobile');
                }
              }}
            />
            <TextFieldForm
              label="Email"
              type="email-address"
              textFieldStyle={{padding: 16}}
              style={styles.formField}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Email"
            />
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              label="Địa chỉ"
              placeholder="Nhập địa chỉ doanh nghiệp"
              placeName={''}
              style={{marginBottom: 12}}
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
            />
            <TextFieldForm
              control={methods.control}
              name="TaxCode"
              label="Mã số thuế"
              required
              returnKeyType="done"
              style={styles.formField}
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
                padding: 16,
              }}
              register={methods.register}
              type="name-phone-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('TaxCode', {
                    message: 'Mã số thuế không hợp lệ',
                  });
                  return;
                }
                const val = ev.trim().length == 10 || ev.trim().length == 13;
                if (val) methods.clearErrors('TaxCode');
                else
                  methods.setError('TaxCode', {
                    message: 'Mã số thuế không hợp lệ',
                  });
              }}
            />
            <TextFieldForm
              required
              style={styles.formField}
              label="Số tài khoản ngân hàng"
              placeholder="Số tài khoản ngân hàng"
              control={methods.control}
              type="number-pad"
              maxLength={13}
              register={methods.register}
              errors={methods.formState.errors}
              name="BankAccount"
              textFieldStyle={{padding: 16}}
            />
            <TextFieldForm
              required
              style={styles.formField}
              label="Tên chủ tài khoản"
              placeholder="Tên chủ tài khoản"
              control={methods.control}
              register={methods.register}
              errors={methods.formState.errors}
              name="BankAccountName"
              textFieldStyle={{padding: 16}}
            />
            <Fselect1Form
              required
              placeholder="Chọn ngân hàng"
              label="Ngân hàng"
              control={methods.control}
              errors={methods.formState.errors}
              style={styles.formField}
              name="BankId"
              options={bankList.map((e: any) => ({id: e.Id, name: e.Name}))}
            />
            <TextFieldForm
              label="Người đại diện"
              required
              textFieldStyle={{padding: 16}}
              style={styles.formField}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Representative"
            />
            {isKeyboardVisible && <View style={{height: 120}} />}
          </Pressable>
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles.footer}>
        <AppButton
          title="Hủy"
          backgroundColor={ColorThemes.light.transparent}
          borderColor={ColorThemes.light.neutral_main_border_color}
          containerStyle={{...styles.footerButton, flex: 1}}
          onPress={onCancel}
          textColor={ColorThemes.light.neutral_text_body_color}
        />
        <AppButton
          title="Lưu"
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{...styles.footerButton, flex: 1}}
          onPress={onSubmit}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  formField: {
    marginBottom: 20,
  },
  formContainer: {
    padding: 16,
  },
  footer: {
    flexDirection: 'row',
    gap: 8,
    padding: 16,
    marginBottom: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  footerButton: {
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
});
