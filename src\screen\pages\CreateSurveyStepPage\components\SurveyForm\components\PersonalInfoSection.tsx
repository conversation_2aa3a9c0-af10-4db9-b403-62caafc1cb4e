import React from 'react';
import {View, Text} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {PersonalInfoSectionProps} from '../types';
import {CreateSurveyStepData} from '../types';
import {styles} from '../styles';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import iconSvg from '../../../../../../svgs/iconSvg';
import {CustomerDropdownForm} from '../../../../../../component/dropdown/CustomerDropdown';
import {BaseTextField} from '../../../../../../project-component/form/BaseTextField';
import {DateRangePicker} from '../../../../../../project-component/form/DateRangePicker/DateRangePicker';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import InfoDisplay from './InfoDisplay';
import {get} from '../../../../../../utils/lodash';
import {Controller} from 'react-hook-form';

const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  isEdit,
  control,
  errors,
}) => {
  const renderIcon = (iconName: string) => {
    return (
      <View style={styles.iconContainer}>
        <AppSvg SvgSrc={iconName} size={22} color="#4CAF50" />
      </View>
    );
  };

  return (
    <View style={styles.section}>
      <Text style={{...TypoSkin.title2}}>Thông tin người khảo sát</Text>
      <CustomerDropdownForm
        style={[
          styles.inputField,
          {
            borderBottomWidth: 1,
            borderColor: ColorThemes.light.primary_border_color,
          },
        ]}
        control={control}
        name="Executor"
        errors={errors}
        placeholder="Người khảo sát"
        typeCustomer={2}
        disabled={!isEdit}
      />
      <Controller
        control={control}
        name="Executor"
        render={({field}) => {
          if (get(field, 'value.CompanyProfileId.Name')) {
            return (
              <InfoDisplay
                icon={iconSvg.userSetting}
                label="Đơn vị"
                value={get(get(field, 'value'), 'CompanyProfileId.Name', '')}
              />
            );
          }

          return <View />;
        }}
      />

      <DateRangePicker<CreateSurveyStepData>
        control={control}
        startDateName="DateStart"
        endDateName="DateEnd"
        errors={errors}
        required
        prefix={renderIcon(iconSvg.calendar)}
        disabled={!isEdit}
        useTimestamp={true}
      />
      <BaseTextField
        placeholder="Mô tả"
        name="Description"
        control={control}
        errors={errors}
        required
        prefix={renderIcon(iconSvg.draft)}
        style={styles.inputField}
        disabled={!isEdit}
      />
    </View>
  );
};

export default PersonalInfoSection;
