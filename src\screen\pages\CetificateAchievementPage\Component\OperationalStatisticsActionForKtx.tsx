import React, {useEffect, useRef, useState} from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {renderInfoSection} from '../Ultis/renderInfoSection';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {useNavigation, useRoute} from '@react-navigation/native';
import {TextFieldForm} from '../../../../project-component/component-form';
import {useForm} from 'react-hook-form';
import CetificateAchievemenDa from '../CetificateAchievemenDa';
import {store} from '../../../../redux/store/store';
import {showDialog, showSnackbar} from '../../../../component/export-component';
import {ComponentStatus} from '../../../../component/component-status';
import {RootScreen} from '../../../../router/router';
import {SafeAreaView} from 'react-native-safe-area-context';

const OperationalStatisticsActionForKtx: React.FC<{}> = () => {
  const route = useRoute<any>();
  const item = route.params?.item;
  const listToilet = route.params?.listToilet;
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [dataApprovel, setDataApprovel] = useState<any>();
  const [getDataToiletService, setGetDataToiletService] = useState<any>();
  const cusId = store.getState().customer.data?.Id;
  const cusName = store.getState().customer.data?.Name;
  const navigation = useNavigation<any>();

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Reason: '',
    },
  });

  const getToiletService = async () => {
    const res = await CetificateAchievemenDa.getToiletServiceByToiletId(
      item?.ToiletId,
    );
    if (res?.code === 200) {
      setGetDataToiletService(res.data);
    }
  };

  useEffect(() => {
    setDataApprovel(item);
    getToiletService();
  }, [item]);

  const handleUpdateCancelReason = async (data: any) => {
    if (item?.Status === 1 || !item?.Status) {
      showSnackbar({
        message:
          ' Thống kê vận hành của nhà vệ sinh này được chưa thực hiện nên không thể duyệt',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    if (data) {
      let dataCreate = {
        ...dataApprovel,
        Approver: cusId,
        ApproveStatus: 1,
        Reason: data.Reason,
      };

      const cancel = await CetificateAchievemenDa.editOperationalData([
        {
          ...dataCreate,
          ToiletServices: undefined,
        },
      ]);
      if (cancel?.code === 200) {
        setIsRejectModalVisible(false);
        UpdateStatusCriterion(cancel);
        showSnackbar({
          message: 'Đã Từ chối phê duyệt thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigation.goBack();
      }
    }
  };
  const UpdateStatusCriterion = async (check: any) => {
    const rows = Array.isArray(check.data) ? check.data : [];
    const hasTwelve = rows.length === 12;
    const allApproved =
      hasTwelve && rows.every((r: any) => r?.ApproveStatus === 2);
    if (allApproved) {
      const UpdateStatusCriterion =
        await CetificateAchievemenDa.updateStatusToiletSerivicesCriterion(
          item?.ToiletId,
        );
      if (UpdateStatusCriterion?.code === 200) {
        setIsRejectModalVisible(false);
        showSnackbar({
          message: `Phê duyệt thành công  nhà vệ sinh , nvs đã đủ điều kiện để nghiệm thu`,
          status: ComponentStatus.SUCCSESS,
        });
        await CetificateAchievemenDa.getLogByToiletId(
          item?.ToiletId,
          `Thực hiện phê duyệt thống kê vận hành thành công bởi ${cusName}`,
        );
        navigation.goBack();
      } else {
        setIsRejectModalVisible(false);
        showSnackbar({
          message: `Phê duyệt thành công nhà vệ sinh `,
          status: ComponentStatus.SUCCSESS,
        });
        navigation.goBack();
      }
    } else {
      setIsRejectModalVisible(false);
      showSnackbar({
        message: `Phê duyệt thành công nhà vệ sinh `,
        status: ComponentStatus.SUCCSESS,
      });
      navigation.goBack();
    }
  };
  const handleUpdateApprove = async () => {
    if (item?.Status === 1 || !item?.Status) {
      showSnackbar({
        message:
          ' Thống kê vận hành của nhà vệ sinh này được chưa thực hiện nên không thể duyệt',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    const cancel = await CetificateAchievemenDa.editOperationalData([
      {
        ...dataApprovel,
        Approver: cusId,
        ApproveStatus: 2,
        ToiletServices: undefined,
      },
    ]);
    if (cancel?.code === 200) {
      const check = await CetificateAchievemenDa.getAllstatisticsByToiletId(
        item?.ToiletId,
      );
      if (check?.code === 200) {
        UpdateStatusCriterion(check);
      } else {
        showSnackbar({
          message: 'Phê duyệt thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigation.goBack();
      }
    }
  };
  const buttonAction = () => {
    return (
      <View style={[styles.buttonActionContainer]}>
        <TouchableOpacity
          style={styles.rejectButton}
          onPress={() => setIsRejectModalVisible(true)}>
          <Text style={styles.rejectButtonText}>Từ chối</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.approveButton}
          onPress={() => handleUpdateApprove()}>
          <Text style={styles.approveButtonText}>Phê duyệt</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderNote = () => {
    return (
      <TouchableOpacity>
        <View style={{padding: 16}}>
          <Text style={styles.sectionTitle}>Ghi chú</Text>
          <View style={styles.notesContainer}>
            <ScrollView
              style={styles.noteScrollContainer}
              showsVerticalScrollIndicator={true}
              nestedScrollEnabled={true}>
              <Text style={styles.noteTitle}>{dataApprovel?.Note}</Text>
            </ScrollView>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.mainContainer}>
      {/* Fixed buttons at bottom */}
      <TitleHeader title="Thống kê vận hành" />
      <ScrollView
        style={styles.mainScrollContainer}
        showsVerticalScrollIndicator={true}
        nestedScrollEnabled={true}
        contentContainerStyle={styles.scrollContentContainer}>
        {renderInfoSection(dataApprovel, isDropdownOpen, setIsDropdownOpen)}
        {renderNote()}
      </ScrollView>
      {dataApprovel && dataApprovel.Status === 2 ? (
        <SafeAreaView>{buttonAction()}</SafeAreaView>
      ) : null}
      {/* Reject Reason Modal */}
      <Modal
        visible={isRejectModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsRejectModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Protected Icon */}
            <View style={styles.protectedIconContainer}>
              <View style={styles.protectedIcon}>
                <Text style={styles.protectedText}>REJECTED</Text>
              </View>
            </View>

            {/* Title */}
            <Text style={styles.modalTitle}>Vui lòng nhập lý do từ chối</Text>
            <TextFieldForm
              label="Nhập lý do từ chối"
              required
              placeholder="Nhập lý do từ chối"
              textFieldStyle={{padding: 5, height: 120}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Reason"
            />
            {/* Buttons */}
            <View style={styles.modalButtonContainer}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => {
                  setIsRejectModalVisible(false);
                }}>
                <Text style={styles.modalCancelButtonText}>Đóng</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalConfirmButton}
                onPress={() => {
                  // setIsRejectModalVisible(false);
                  methods.handleSubmit(handleUpdateCancelReason)();
                }}>
                <Text style={styles.modalConfirmButtonText}>Xác nhận</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContentContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 100, // Space for fixed buttons at bottom
  },
  headerContainer: {
    marginBottom: 24,
  },
  headerTitle: {
    ...TypoSkin.highlight6,
    fontWeight: '700',
    marginBottom: 8,
    lineHeight: 28,
  },
  headerSubtitle: {
    ...TypoSkin.buttonText5,
    fontWeight: '400',
    color: '#757575',
    lineHeight: 20,
  },
  infoSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoLabel: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dropdownWrapper: {
    flex: 1,
    marginLeft: 8,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 120,
  },
  dropdownText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  chevronIcon: {
    // Animation will be handled by React Native's default behavior
  },
  chevronRotated: {
    transform: [{rotate: '180deg'}],
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 200, // Changed from fixed height to maxHeight
    zIndex: 1001,
  },

  buttonActionContainer: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#E5E5E5',
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rejectButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '600',
  },
  approveButton: {
    flex: 1,
    backgroundColor: '#4CAF50',
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  approveButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  radioContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 24,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  radioSelected: {
    borderColor: '#4CAF50',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4CAF50',
  },
  radioText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  protectedIconContainer: {
    marginBottom: 20,
  },
  protectedIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#4CAF50',
  },
  protectedText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2E7D32',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  reasonInput: {
    width: '100%',
    height: 120,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 16,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#F9F9F9',
    marginBottom: 24,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
    marginTop: 40,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#E5E5E5',
    paddingVertical: 14,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalCancelButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '600',
  },
  modalConfirmButton: {
    flex: 1,
    backgroundColor: '#4CAF50',
    paddingVertical: 14,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalConfirmButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  sectionTitle: {
    ...TypoSkin.buttonText2,
  },
  notesContainer: {
    borderRadius: 10,
    marginBottom: 20,
    marginTop: 10,
    backgroundColor: '#f8f9fa',
    padding: 16,
    height: 150,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    lineHeight: 24,
    textAlign: 'left',
  },
  noteScrollContainer: {
    maxHeight: 150,
    minHeight: 80,
  },
  mainScrollContainer: {
    flex: 1,
  },
});

export default OperationalStatisticsActionForKtx;
