import React from 'react';
import {View} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {IconRendererProps} from '../types';
import {styles} from '../styles';

const IconRenderer: React.FC<IconRendererProps> = ({
  iconName,
  size = 22,
  color = '#4CAF50',
}) => {
  return (
    <View style={styles.iconContainer}>
      <AppSvg SvgSrc={iconName} size={size} color={color} />
    </View>
  );
};

export default IconRenderer;
