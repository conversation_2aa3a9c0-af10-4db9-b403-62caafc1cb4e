import {useEffect, useRef, useState} from 'react';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {navigate} from '../../../../router/router';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {ComponentStatus, showDialog} from 'wini-mobile-components';
import {OrderProductDA} from '../../../module/orderProduct/orderProductDA';

export const useProfileActions = () => {
  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const orderProductDA = new OrderProductDA();
  const [orderDetail, setOrderDetail] = useState<any>([]);

  useEffect(() => {
    fetchOrderUser();
  }, [customer]);

  const handleActionPress = (item: any) => {
    if (item.action === 'logout') {
      handleLogout();
      return;
    }

    if (item.route) {
      navigate(item.route);
    }
  };

  const handleLogout = () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn muốn đăng xuất?',
      onSubmit: async () => {
        dispatch(CustomerActions.logout(dispatch, navigation));
      },
    });
    console.log('showDialog called');
  };

  const fetchOrderUser = async () => {
    if (!customer) return;
    const res = await orderProductDA.getOrderByCustomerId(customer.Id);
    setOrderDetail(res);
  };

  return {
    dialogRef,
    handleActionPress,
    handleLogout,
    orderDetail,
  };
};
