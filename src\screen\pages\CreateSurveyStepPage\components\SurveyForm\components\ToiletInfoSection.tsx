import React from 'react';
import {View, Text} from 'react-native';
import {ToiletInfoSectionProps} from '../types';
import {
  accessibilityOptions,
  cleaningProcessOptions,
  kitchenOptions,
  renewableEnergyOptions,
  treatmentTechnologyOptions,
} from '../../../constants';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../../../../svgs/iconSvg';
import {styles} from '../styles';
import {BaseTextField} from '../../../../../../project-component/form/BaseTextField';
import BaseDropdown, {
  BaseDropdownItem,
} from '../../../../../../component/dropdown/BaseDropdown';
import {Controller} from 'react-hook-form';

const ToiletInfoSection: React.FC<ToiletInfoSectionProps> = ({
  isEdit,
  control,
  errors,
}) => {
  // Helper function to find selected item from options
  const findSelectedItem = (
    options: BaseDropdownItem[],
    value: string | number,
  ) => {
    if (!value) return undefined;
    return options.find(option => option.id.toString() === value.toString());
  };

  const renderIcon = (iconName: string) => {
    return (
      <View style={styles.iconContainer}>
        <AppSvg SvgSrc={iconName} size={22} color="#4CAF50" />
      </View>
    );
  };

  return (
    <View style={styles.section}>
      <Text style={{...TypoSkin.title3}}>
        Khảo sát thông tin chung về nhà vệ sinh
      </Text>

      {/* Số lượng NVS */}
      <BaseTextField
        placeholder="Số lượng NVS"
        name="ToiletCount"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.wcBoard)}
        style={styles.inputField}
        disabled={!isEdit}
      />

      {/* Số người sử dụng */}
      <BaseTextField
        placeholder="Số người sử dụng"
        name="UserCount"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.groupPeople)}
        style={styles.inputField}
        disabled={!isEdit}
      />

      {/* Số NVS vệ sinh */}
      <BaseTextField
        placeholder="Số NVS vệ sinh"
        name="SanitaryToiletCount"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.toilet2d)}
        style={styles.inputField}
        disabled={!isEdit}
      />

      {/* Quy trình dọn */}
      <Controller
        control={control}
        name="CleaningProcess"
        render={({field}) => {
          return (
            <BaseDropdown
              icon={iconSvg.clean}
              placeholder="Quy trình dọn"
              data={cleaningProcessOptions}
              selectedItem={findSelectedItem(
                cleaningProcessOptions,
                field.value,
              )}
              onSelect={item => field.onChange(item.id.toString())}
              style={styles.inputField}
              disabled={!isEdit}
            />
          );
        }}
      />
      {/* Vị trí bể phốt */}
      <BaseTextField
        placeholder="Vị trí bể phốt"
        name="SepticTankLocation"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.location)}
        style={styles.inputField}
        disabled={!isEdit}
      />

      {/* Dung tích */}
      <BaseTextField
        placeholder="Dung tích"
        name="Capacity"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.jug)}
        style={styles.inputField}
        disabled={!isEdit}
      />

      {/* Công nghệ xử lý */}
      <Controller
        control={control}
        name="TreatmentTechnology"
        render={({field}) => (
          <BaseDropdown
            icon={iconSvg.technology}
            placeholder="Công nghệ xử lý"
            data={treatmentTechnologyOptions}
            selectedItem={findSelectedItem(
              treatmentTechnologyOptions,
              field.value,
            )}
            onSelect={item => field.onChange(item.id.toString())}
            style={styles.inputField}
            disabled={!isEdit}
          />
        )}
      />

      {/* Bếp ăn */}
      <Controller
        control={control}
        name="Kitchen"
        render={({field}) => (
          <BaseDropdown
            icon={iconSvg.kitchenTable}
            placeholder="Bếp ăn"
            data={kitchenOptions}
            selectedItem={findSelectedItem(kitchenOptions, field.value)}
            onSelect={item => field.onChange(item.id.toString())}
            style={styles.inputField}
            disabled={!isEdit}
          />
        )}
      />

      {/* Có đường thu riêng nhà bếp và NVS */}
      <Controller
        control={control}
        name="SeparateDrainage"
        render={({field}) => (
          <BaseDropdown
            icon={iconSvg.pipe}
            placeholder="Có đường thu riêng nhà bếp và NVS"
            data={accessibilityOptions}
            selectedItem={findSelectedItem(accessibilityOptions, field.value)}
            onSelect={item => field.onChange(item.id.toString())}
            style={styles.inputField}
            disabled={!isEdit}
          />
        )}
      />

      {/* Thời gian hút bể phốt */}
      <BaseTextField
        placeholder="Thời gian hút bể phốt"
        name="PumpingFrequency"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.septic)}
        style={styles.inputField}
        disabled={!isEdit}
        suffix={
          <View style={{paddingHorizontal: 8}}>
            <Text style={{color: '#666', fontSize: 14}}>lần/năm</Text>
          </View>
        }
      />

      {/* Sử dụng năng lượng tự nhiên/tái tạo */}
      <Controller
        control={control}
        name="RenewableEnergy"
        render={({field}) => (
          <BaseDropdown
            icon={iconSvg.energyTower}
            placeholder="Sử dụng năng lượng tự nhiên/tái tạo"
            data={renewableEnergyOptions}
            selectedItem={findSelectedItem(renewableEnergyOptions, field.value)}
            onSelect={item => field.onChange(item.id.toString())}
            style={styles.inputField}
            disabled={!isEdit}
          />
        )}
      />

      {/* Năng lượng (ghi rõ nếu có) */}
      <BaseTextField
        placeholder="Năng lượng (ghi rõ nếu có)"
        name="EnergyDetails"
        control={control}
        errors={errors}
        prefix={renderIcon(iconSvg.energyCircle)}
        style={styles.inputField}
        disabled={!isEdit}
      />

      {/* Tái sử dụng nước */}
      <Controller
        control={control}
        name="WaterReuse"
        render={({field}) => (
          <BaseDropdown
            icon={iconSvg.warterCircle}
            placeholder="Tái sử dụng nước"
            data={accessibilityOptions}
            selectedItem={findSelectedItem(accessibilityOptions, field.value)}
            onSelect={item => field.onChange(item.id.toString())}
            style={styles.inputField}
            disabled={!isEdit}
          />
        )}
      />

      {/* Phân loại rác */}
      <Controller
        control={control}
        name="WasteClassification"
        render={({field}) => (
          <BaseDropdown
            icon={iconSvg.garbage}
            placeholder="Phân loại rác"
            data={accessibilityOptions}
            selectedItem={findSelectedItem(accessibilityOptions, field.value)}
            onSelect={item => field.onChange(item.id.toString())}
            style={styles.inputField}
            disabled={!isEdit}
          />
        )}
      />
    </View>
  );
};

export default ToiletInfoSection;
