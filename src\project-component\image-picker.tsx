import { FlatList, Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { ColorSkin } from "../assets/skin/colors";
import ImagePicker from 'react-native-image-crop-picker';
import React, { useState } from 'react';
import { FilledRemove, OutlinePLus } from "../assets/icon";
import { TypoSkin } from "../assets/skin/typography";

export default function ImagePickerView() {

    const [images, setImages] = useState<Array<string>>([]);

    const _pickerImgs = () => {
        ImagePicker.openPicker({
            multiple: true,
            cropping: false
        }).then(selectedImages => {
            const imageUris = selectedImages.map(image => image.path);
            var imgs = images.concat(imageUris);
            setImages(imgs);
        }).catch(error => {
            console.log(error);
        });
    }

    const _deleteImg = (index: number) => {
        var imgs = images.filter((e, i) => i != index);
        setImages(imgs);
    }

    const _pickerImgCardItem = () => {
        return <TouchableOpacity onPress={_pickerImgs} style={styles.imagePick}>
            <OutlinePLus size={24} />
            <Text numberOfLines={1}
                style={styles.textPick}>Thêm ảnh</Text>
        </TouchableOpacity>
    }
    const _imgCardItem = (item: string, index: number) => {
        return <View key={`${index}`} style={styles.containerItem}>
            <Pressable style={styles.image}>
                <Image source={{ uri: item }} style={styles.image} />
                <TouchableOpacity style={styles.removeIcon} onPress={() => _deleteImg(index)}>
                    <FilledRemove size={24} color={ColorSkin.errorColor} />
                </TouchableOpacity>
            </Pressable>
        </View>
    }


    return <View style={styles.imageContainer}>
        {images.length == 0 ? _pickerImgCardItem() : null}
        <FlatList style={{ flexDirection: 'row' }} showsHorizontalScrollIndicator={false} scrollEnabled horizontal data={images} renderItem={({ item, index }) =>
            <View style={styles.imageContainer}>
                {index == 0 ? _pickerImgCardItem() : null}
                {_imgCardItem(item, index)}
            </View>
        } />
    </View>
}

const styles = StyleSheet.create({
    imageContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',

    },
    containerItem: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        paddingTop: 10,
    },
    removeIcon: {
        position: 'absolute',
        top: -10,
        right: -10,
    },
    textPick: {
        ...TypoSkin.buttonText4,
        paddingTop: 8,
        color: ColorSkin.subtitle
    },
    imagePick: {
        marginTop: 10,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
        borderWidth: 0.3, borderColor: ColorSkin.subtitle, borderStyle: 'dashed', borderRadius: 8, height: 106, width: 106
    },
    image: {
        marginRight: 16,
        borderWidth: 0.3, borderColor: ColorSkin.subtitle, borderStyle: 'dashed', borderRadius: 8, height: 106, width: 106
    },
});