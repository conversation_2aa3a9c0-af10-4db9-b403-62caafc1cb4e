import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text, Image, FlatList} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import CetificateAchievemenDa from '../CetificateAchievemenDa';

const CetificateContent: React.FC<{
  getStatusForm: string;
  toiletId: string;
}> = ({getStatusForm, toiletId}) => {
  const [certificateData, setCertificateData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const getInfoALlToiletBy = async () => {
    setLoading(true);
    try {
      const respone =
        await CetificateAchievemenDa.getToiletInfoByToiletCertificate(
          getStatusForm,
          toiletId,
        );
      if (respone?.code === 200) {
        setCertificateData(respone.data || []);
      }
    } catch (error) {
      console.log('Error fetching certificate data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (getStatusForm) {
      getInfoALlToiletBy();
    }
  }, [getStatusForm]);

  const renderCertificateItem = ({item}: {item: any}) => (
    <View>
      <View style={styles.certificateCard}>
        <View style={styles.cardHeader}>
          <View style={styles.logoContainer}>
            <View style={styles.checkmarkCircle}>
              <Image
                source={require('../../../../assets/Cetificate.png')}
                style={{width: 30, height: 30, borderRadius: 20}}
              />
            </View>
          </View>
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.completionText}>
            Hoàn thành bởi {item.Company?.Name || 'N/A'}
          </Text>

          <Text style={styles.dateText}>
            Cấp ngày{' '}
            {item.DateCreated
              ? new Date(item.DateCreated).toLocaleDateString('vi-VN')
              : 'N/A'}
          </Text>

          <Text style={styles.certificateTitle}>
            Chứng nhận {item.Certificate == 1 && 'Sạch'}
            {item.Certificate == 2 && 'Đăng ký Xanh'}
            {item.Certificate == 3 && 'Xanh'}
            {item.Certificate == 4 && 'Đăng ký Tuần hoàn'}
            {item.Certificate == 5 && 'Tuần hoàn'}
          </Text>

          <Text style={styles.programDescription}>
            (Chương trình đánh giá tiêu chuẩn vận hành theo định hướng phát
            triển bền vững)
          </Text>

          <Text style={styles.programDetails}>
            Chương trình đánh giá và chứng nhận hoạt động được KTX Group ủy
            quyền và trao cho các tổ chức đáp ứng các tiêu chí chính về tính bền
            vững, kinh tế tuần hoàn và hoạt động xanh.
          </Text>
        </View>
      </View>
      <View style={styles.companyInfoContainer}>
        <View style={styles.logoSection}>
          <View style={styles.companyLogoContainer}>
            <Image
              source={require('../../../../assets/KtxcetifiIMg.png')}
              style={{width: 80, height: 80}}
            />
          </View>
          <Text style={styles.ktxText}></Text>
        </View>

        <View style={styles.companyTextSection}>
          <Text style={styles.companyName}>
            CÔNG TY CỔ PHẦN ĐẦU TƯ KIẾN TẠO XANH GROUP
          </Text>
          <Text style={styles.companyDescription}>
            Đơn vị đi đầu về Số hoá nhà vệ sinh Việt Nam theo tiêu chí Sạch -
            Xanh - Tuần Hoàn
          </Text>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text>Đang tải...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.listContainer}>
        {certificateData.map((item, index) => (
          <View key={item.Id?.toString()}>{renderCertificateItem({item})}</View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  certificateCard: {
    backgroundColor: ColorThemes.light.neutral_text_label_reverse_color,
    borderRadius: 12,
    padding: 10,
    marginBottom: 20,
    flexDirection: 'row',
  },
  cardHeader: {
    marginBottom: 20,
    paddingTop: 10,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkmarkCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  logoBox: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  logoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  logoSubtext: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  ktxLogo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ktxLogoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  cardContent: {
    paddingTop: 10,
    flex: 1,
  },
  completionText: {
    ...TypoSkin.buttonText2,
    fontWeight: '600',
    marginBottom: 8,
  },
  dateText: {
    fontWeight: '600',
    color: '#2E7D32',
    ...TypoSkin.buttonText2,
  },
  certificateTitle: {
    ...TypoSkin.buttonText2,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  programDescription: {
    ...TypoSkin.buttonText2,
    marginBottom: 16,
    fontStyle: 'italic',
  },
  programDetails: {
    ...TypoSkin.buttonText5,

    lineHeight: 20,
  },
  companyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 12,
  },
  companyDescription: {
    ...TypoSkin.buttonText5,
    lineHeight: 20,
    paddingBottom: 10,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {},
  companyInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  logoSection: {
    alignItems: 'center',
    marginRight: 20,
  },
  companyLogoContainer: {
    position: 'relative',
    width: 60,
    height: 60,
    marginBottom: 8,
  },
  logoM: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 40,
    height: 40,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },

  ktxText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    letterSpacing: 2,
  },
  companyTextSection: {
    flex: 1,
  },
  companyName: {
    ...TypoSkin.buttonText2,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 24,
  },
});

export default CetificateContent;
