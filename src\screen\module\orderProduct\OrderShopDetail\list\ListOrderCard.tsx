import React, {useEffect, useState} from 'react';
import {View, RefreshControl} from 'react-native';
import {FlatList} from 'react-native';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {store} from '../../../../../redux/store/store';
import EmptyPage from '../../../../../project-component/empty-page';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {RootScreen} from '../../../../../router/router';
import {Title} from '../../../../../config/Contanst';
import {OrderCardProps} from '../types';
import CardOrder from '../card/CardOrder';
import {handleUpdateStatusOrder} from '../Utils';
import {useOrderActions} from '../../../../../redux/reducers/order/OrderReducer';

const ListOrderCard = (props: OrderCardProps) => {
  const {type, dataSearchResult, dataSearch, orderInfo, HandleGetOrderData} =
    props;
  const navigation = useNavigation<any>();
  const [data, setData] = useState<any[]>();
  const [action, setAction] = useState<string>('');
  const dispatch = useDispatch<any>();
  const shopInfo = store.getState().partner.data;
  const orderActions = useOrderActions();
  //refresh
  const [refreshing, setRefreshing] = useState(false);
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (shopInfo && shopInfo[0]?.Id) {
        await HandleGetOrderData(shopInfo[0].Id);
      } else {
        showSnackbar({
          message: 'Không thể làm mới dữ liệu. Vui lòng thử lại sau.',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Có lỗi xảy ra khi làm mới dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setRefreshing(false);
    }
  };
  useEffect(() => {
    if (type === Title.New) {
      setAction('Xác nhận đơn');
      setData(orderInfo?.NewOrder?.data || []);
    } else if (type === Title.Processing) {
      setAction('Cập nhật trạng thái');
      setData(orderInfo?.ProcessOrder?.data || []);
    } else if (type === Title.Cancel) {
      setAction(type);
      setData(orderInfo?.CancelOrder?.data || []);
    } else if (type === Title.Done) {
      setData(orderInfo?.DoneOrder?.data || []);
    }
  }, [type, orderInfo]);

  const handleUpdateStatus = async (item: any, type?: string) => {
    await handleUpdateStatusOrder(
      item,
      type,
      dispatch,
      navigation,
      shopInfo,
      orderActions,
    );
  };

  const handleViewDetailOrder = (item: any, refundInfo: any) => {
    navigation.push(RootScreen.OrderDetailPageForShop, {
      orderId: item.Id,
      type: 'Shop',
      CancelReason: item.CancelReason,
      refundInfo: refundInfo,
    });
  };

  useEffect(() => {
    if (data) {
      props.setNumberCard(data.length);
    }
  }, [data]);
  if (
    (data && data?.length > 0) ||
    (dataSearchResult && dataSearchResult.length > 0)
  ) {
    return (
      <FlatList
        data={dataSearch ? dataSearchResult : data}
        style={{flex: 1}}
        keyExtractor={item => item.Id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        renderItem={({item, index}) => (
          <CardOrder
            item={item}
            index={index}
            action={action}
            handleUpdateStatusProcessOrder={handleUpdateStatus}
            handleViewDetailOrder={handleViewDetailOrder}
          />
        )}
      />
    );
  } else {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%',
        }}>
        <EmptyPage />
      </View>
    );
  }
};

export default ListOrderCard;
