import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ShopStatisticsProps} from '../types';
import {styles} from '../styles';

const ShopStatistics: React.FC<ShopStatisticsProps> = ({
  shop,
  onProductPress,
}) => {
  return (
    <View style={styles.statisticsContainer}>
      <View style={styles.statisticsRow}>
        {/* Rating */}
        <View style={styles.statisticsItem}>
          <Text
            style={[
              TypoSkin.heading5,
              {color: ColorThemes.light.neutral_text_title_color},
              styles.statisticsValue,
            ]}>
            {shop?.rating?.toFixed(1) ?? '4.5'}
          </Text>
          <Text style={[TypoSkin.regular3, styles.statisticsLabel]}>
            <PERSON><PERSON><PERSON> gi<PERSON>
          </Text>
        </View>

        <View style={styles.statisticsDivider} />

        {/* Products */}
        <TouchableOpacity
          onPress={onProductPress}
          style={styles.statisticsItem}>
          <Text style={[TypoSkin.heading5, styles.productStatistics]}>
            {shop?.totalProducts ?? '90'}
          </Text>
          <Text style={[TypoSkin.regular3, styles.statisticsLabel]}>
            Sản phẩm
          </Text>
        </TouchableOpacity>

        <View style={styles.statisticsDivider} />

        {/* Orders */}
        <View style={styles.statisticsItem}>
          <Text
            style={[
              TypoSkin.heading5,
              {color: ColorThemes.light.neutral_text_title_color},
              styles.statisticsValue,
            ]}>
            {shop?.totalOrder ?? '3000'}
          </Text>
          <Text style={[TypoSkin.regular3, styles.statisticsLabel]}>
            Đã bán
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ShopStatistics;
