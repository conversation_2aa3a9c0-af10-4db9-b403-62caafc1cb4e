import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  ViewStyle,
} from 'react-native';

import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import FastImage from 'react-native-fast-image';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import CategoryDA from '../categoryDa';
const {width} = Dimensions.get('window');

// Định nghĩa kiểu dữ liệu cho danh mục
interface Category {
  Id: string;
  Name: string;
  Img: string;
  route?: string; // Đường dẫn điều hướng khi nhấn vào danh mục
  color?: string; // Màu nền cho icon (tùy chọn)
  badge?: string; // Badge hiển thị trên danh mục (ví dụ: "Sale")
}

interface CategoryGridProps {
  title?: string;
  categories?: Category[];
  numColumns?: number;
  onCategoryPress?: (category: Category) => void;
  horizontal?: boolean;
  style?: ViewStyle;
  styleItem?: ViewStyle;
}

const CategoryGrid: React.FC<CategoryGridProps> = ({
  title,
  categories = [],
  onCategoryPress,
  style,
  styleItem,
}) => {
  // Tính toán kích thước của mỗi item cho layout 3x2 (6 items per page)
  const itemWidth = (width - 40 - 32) / 3; // 64 = page padding, 32 = gaps between items
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Category[][]>([]);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const autoScrollInterval = useRef<NodeJS.Timeout | null>(null);

  // Hàm chia mảng categories thành các nhóm nhỏ (mỗi nhóm có 'size' phần tử)
  const chunkArray = (array: Category[], size: number): Category[][] => {
    const chunkedArray: Category[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunkedArray.push(array.slice(i, i + size));
    }
    return chunkedArray;
  };

  useEffect(() => {
    initData();
  }, []);

  // Effect để tự động cuộn danh sách categories mỗi 3 giây
  useEffect(() => {
    // Hàm bắt đầu tự động cuộn
    const startAutoScroll = () => {
      if (autoScrollInterval.current) {
        clearInterval(autoScrollInterval.current);
      }

      autoScrollInterval.current = setInterval(() => {
        const totalData =
          data?.length > 0
            ? data
            : categories?.length > 0
              ? chunkArray(categories, 6)
              : [];
        if (totalData.length > 1 && flatListRef.current) {
          setCurrentIndex(prevIndex => {
            const nextIndex = (prevIndex + 1) % totalData.length;
            flatListRef.current?.scrollToIndex({
              index: nextIndex,
              animated: true,
            });
            return nextIndex;
          });
        }
      }, 3000); // 3 seconds
    };

    if (!loading && !error) {
      startAutoScroll();
    }

    return () => {
      if (autoScrollInterval.current) {
        clearInterval(autoScrollInterval.current);
      }
    };
  }, [data, categories, loading, error]);

  // Hàm dừng tự động cuộn khi người dùng bắt đầu vuốt
  const handleScrollBeginDrag = () => {
    if (autoScrollInterval.current) {
      clearInterval(autoScrollInterval.current);
    }
  };

  // Hàm tiếp tục tự động cuộn sau khi người dùng ngừng vuốt
  const handleScrollEndDrag = () => {
    setTimeout(() => {
      if (!loading && !error) {
        const totalData =
          data?.length > 0
            ? data
            : categories?.length > 0
              ? chunkArray(categories, 6)
              : [];
        if (totalData.length > 1) {
          if (autoScrollInterval.current) {
            clearInterval(autoScrollInterval.current);
          }

          autoScrollInterval.current = setInterval(() => {
            setCurrentIndex(prevIndex => {
              const nextIndex = (prevIndex + 1) % totalData.length;
              flatListRef.current?.scrollToIndex({
                index: nextIndex,
                animated: true,
              });
              return nextIndex;
            });
          }, 3000);
        }
      }
    }, 2000); // Resume after 2 seconds of inactivity
  };

  // Hàm khởi tạo và tải dữ liệu categories từ API
  const initData = async () => {
    try {
      setLoading(true);
      setError(null);
      const res = await CategoryDA.getParentCategories();
      if (res && res.length > 0) {
        setData(chunkArray(res, 6));
      } else {
        setData([]);
      }
    } catch (err) {
      setError('Không thể tải danh mục. Vui lòng thử lại.');
      console.error('Error loading categories:', err);
    } finally {
      setLoading(false);
    }
  };

  // Hàm xử lý khi người dùng nhấn vào một category
  const handleCategoryPress = (category: Category) => {
    if (onCategoryPress) {
      onCategoryPress(category);
    }
  };

  // Hàm render một trang chứa 6 categories (layout 3x2)
  const renderCategoryPage = ({item}: {item: Category[]}) => (
    <View style={styles.categoryPage}>
      <View style={styles.categoryGrid}>
        {item.map((category: Category) => (
          <TouchableOpacity
            key={category.Id}
            style={[{width: itemWidth, ...styleItem}]}
            onPress={() => handleCategoryPress(category)}
            activeOpacity={0.7}>
            <View style={styles.categoryContent}>
              <View style={[styles.iconContainer]}>
                <FastImage
                  key={category.Img}
                  source={{uri: category.Img}}
                  style={{width: 30, height: 30}}
                  resizeMode={FastImage.resizeMode.contain}
                />
              </View>
              <Text style={styles.categoryName} numberOfLines={2}>
                {category.Name}
              </Text>
              {category.badge && (
                <View style={styles.badgeContainer}>
                  <Text style={styles.badgeText}>{category.badge}</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Hàm render shimmer loading cho horizontal layout
  const renderShimmerPage = () => (
    <View style={styles.categoryPage}>
      <View style={styles.categoryGrid}>
        {Array(6)
          .fill(0)
          .map((_, index) => (
            <View
              key={`shimmer-${index}`}
              style={[{width: itemWidth}, styles.categoryItem]}>
              <View style={styles.categoryContent}>
                <SkeletonPlaceholder
                  backgroundColor="#F0F0F0"
                  highlightColor="#E0E0E0">
                  <View>
                    <SkeletonPlaceholder.Item
                      width={30}
                      height={30}
                      borderRadius={15}
                      marginBottom={8}
                    />
                    <SkeletonPlaceholder.Item
                      width="80%"
                      height={10}
                      borderRadius={4}
                    />
                  </View>
                </SkeletonPlaceholder>
              </View>
            </View>
          ))}
      </View>
    </View>
  );

  return (
    <View>
      {title && <Text style={styles.title}>{title}</Text>}

      {loading ? (
        renderShimmerPage()
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={initData}>
            <Text style={styles.retryText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          horizontal={true}
          style={style}
          data={
            data?.length > 0
              ? data
              : categories?.length > 0
                ? chunkArray(categories, 6)
                : []
          }
          renderItem={renderCategoryPage}
          keyExtractor={(_, index) => `page-${index}`}
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          contentContainerStyle={styles.gridContainer}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onScrollToIndexFailed={info => {
            console.warn('ScrollToIndex failed:', info);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
  gridContainer: {
    gap: 16,
  },
  categoryItem: {
    paddingHorizontal: 8,
  },
  categoryContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: ColorThemes.light.primary_main_color,
    height: 80,
    overflow: 'hidden',
    gap: 8,
  },
  iconContainer: {
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    lineHeight: 12,
    fontSize: 11,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#8E24AA',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },

  // Styles for error state
  errorContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF3F3',
    borderRadius: 8,
    marginVertical: 8,
  },
  errorText: {
    color: '#D32F2F',
    marginBottom: 12,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryText: {
    color: 'white',
    fontWeight: 'bold',
  },
  categoryPage: {
    width: width - 32, // Full width minus padding
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
});

export default CategoryGrid;
