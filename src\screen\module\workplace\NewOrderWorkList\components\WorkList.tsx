import React from 'react';
import {FlatList, View, StyleSheet} from 'react-native';
import {RefreshControl} from 'react-native-gesture-handler';
import NewWorkCard from '../../components/card/NewWorkCard';
import {CardOrderHoriSkeleton} from '../../../../../project-component/skeletonCard';
import EmptyPage from '../../../../../project-component/empty-page';
import {WorkData} from '../hooks/useNewOrderWorkList';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {LoadMoreIndicator} from './LoadMoreIndicator';

interface WorkListProps {
  works: WorkData;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  searchValue: string;
  guests: any[];
  user: any;
  setWorks: React.Dispatch<React.SetStateAction<WorkData>>;
  onRefresh: () => Promise<void>;
  loadMore: () => Promise<void>;
}

export const WorkList: React.FC<WorkListProps> = ({
  works,
  isLoading,
  isRefreshing,
  isLoadingMore,
  hasMore,
  guests,
  user,
  setWorks,
  onRefresh,
  loadMore,
}) => {
  const filteredData = works.data;

  const renderItem = ({item, index}: {item: any; index: number}) => {
    const _guest = guests.find(e => e.ids.includes(item.ToiletId));
    return (
      <View
        style={{
          borderWidth: 1,
          borderRadius: 12,
          borderColor: ColorThemes.light.neutral_main_background_color,
          padding: 1,
        }}>
        <NewWorkCard
          item={item}
          user={user}
          setWorks={setWorks}
          customer={_guest}
        />
      </View>
    );
  };

  const renderSeparator = () => <View style={styles.separator} />;

  const renderEmpty = () =>
    works.data.length == 0 && !isLoading ? (
      <EmptyPage
        title="Chưa có đơn hàng nào được tạo"
        subtitle="Hãy tạo các dịch vụ bạn muốn"
      />
    ) : (
      <>
        {[0, 1, 2, 3].map((_, index) => (
          <View key={index} style={styles.skeletonItem}>
            <CardOrderHoriSkeleton />
          </View>
        ))}
      </>
    );

  const renderFooter = () => (
    <View style={styles.footer}>
      <LoadMoreIndicator
        isLoading={isLoadingMore}
        hasMore={hasMore}
        data={filteredData}
      />
    </View>
  );

  const handleRefresh = async () => {
    await onRefresh();
  };

  const handleLoadMore = () => {
    if (hasMore && !isLoadingMore && !isLoading) {
      loadMore();
    }
  };

  return (
    <FlatList
      keyExtractor={(item, index) => '_' + item.Id + index}
      data={filteredData}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
      style={styles.container}
      ItemSeparatorComponent={renderSeparator}
      renderItem={renderItem}
      ListEmptyComponent={renderEmpty}
      ListFooterComponent={renderFooter}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
    height: '100%',
    flex: 1,
  },
  separator: {
    height: 16,
  },
  skeletonItem: {
    marginBottom: 16,
  },
  footer: {
    height: 100,
  },
});
