import React, {useState, useCallback} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {usePartner} from '../../../../redux/hooks/partnerHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';

const ExamplePartnerScreen: React.FC = () => {
  const navigation = useNavigation();
  const {partnerData, getPartnerByCustomerId} = usePartner();
  const customerId = useSelector((state: RootState) => state.customer.data?.Id);
  const [refreshKey, setRefreshKey] = useState(0);

  // Function để refresh data
  const refreshPartnerData = useCallback(() => {
    if (customerId) {
      console.log('Refreshing partner data...');
      getPartnerByCustomerId(customerId);
      setRefreshKey(prev => prev + 1); // Force re-render nếu cần
    }
  }, [customerId, getPartnerByCustomerId]);

  // Navigate đến trang đăng ký với callback
  const navigateToRegister = () => {
    navigation.navigate('TabRegisterPartner' as never, {
      onSuccess: refreshPartnerData, // Truyền callback function
    } as never);
  };

  return (
    <View style={{padding: 16}}>
      <Text style={{fontSize: 18, fontWeight: 'bold', marginBottom: 16}}>
        Thông tin đối tác (Refresh Key: {refreshKey})
      </Text>

      {partnerData ? (
        <View style={{marginBottom: 20}}>
          <Text>Tên: {partnerData.Name}</Text>
          <Text>Email: {partnerData.GroupEmail}</Text>
          <Text>Số điện thoại: {partnerData.GroupPhone}</Text>
          <Text>Trạng thái: {partnerData.Status === 1 ? 'Hoạt động' : 'Chưa kích hoạt'}</Text>
        </View>
      ) : (
        <Text style={{marginBottom: 20, color: 'gray'}}>
          Chưa có thông tin đối tác
        </Text>
      )}

      <TouchableOpacity
        style={{
          backgroundColor: '#3AAC6D',
          padding: 12,
          borderRadius: 8,
          alignItems: 'center',
          marginBottom: 10,
        }}
        onPress={navigateToRegister}>
        <Text style={{color: 'white', fontWeight: 'bold'}}>
          Đăng ký làm đối tác
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          backgroundColor: '#287CF0',
          padding: 12,
          borderRadius: 8,
          alignItems: 'center',
        }}
        onPress={refreshPartnerData}>
        <Text style={{color: 'white', fontWeight: 'bold'}}>
          Refresh thủ công
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ExamplePartnerScreen;
