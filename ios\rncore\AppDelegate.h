// #import <RCTAppDelegate.h>
// #import <UIKit/UIKit.h>
// #import "RNAppAuthAuthorizationFlowManager.h"

// @interface AppDelegate : RCTAppDelegate <RNAppAuthAuthorizationFlowManager>

// @property(nonatomic, weak) id<RNAppAuthAuthorizationFlowManagerDelegate> authorizationFlowManagerDelegate;

// @end
#import <RCTAppDelegate.h>
#import <UIKit/UIKit.h>

@interface AppDelegate : RCTAppDelegate

@end
