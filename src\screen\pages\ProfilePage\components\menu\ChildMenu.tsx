import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {AppSvg, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {useNavigation} from '@react-navigation/native';
import {Text} from 'react-native';

interface MenuCardsProps {
  svgIcon: string;
  title: string;
  order: string;
  SvgIconSub: string;
}

const MenuCards = (props: MenuCardsProps) => {
  let {svgIcon, title, order, SvgIconSub} = props;
  const navigation = useNavigation<any>();
  const handleNavigateOrders = (order: string) => {
    navigation.navigate(order);
  };
  return (
    <TouchableOpacity
      style={styles.actionCard}
      onPress={() => handleNavigateOrders(order)}>
      <View style={styles.leftContainer}>
        <AppSvg SvgSrc={svgIcon} size={20} />
        <Text style={styles.actionText} numberOfLines={2} ellipsizeMode="tail">
          {title}
        </Text>
      </View>
      <View style={styles.rightContainer}>
        <AppSvg SvgSrc={SvgIconSub} size={20} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 16,
    shadowColor: '#1890FF4D',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginBottom: 12,
    elevation: 2,
    minHeight: 70,
    maxHeight: 80,
    borderWidth: 0.3,
    borderColor: '#1890FF4D',
    width: '100%',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    minWidth: 0, // Đảm bảo text không overflow
  },
  rightContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 24,
    height: 24,
  },
  actionText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
    marginLeft: 12,
    flex: 1,
    flexWrap: 'wrap',
  },
});

export default MenuCards;
