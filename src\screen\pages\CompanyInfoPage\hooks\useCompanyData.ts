import {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from 'redux/hooks/hooks';
import {CompanyProfileActions} from 'redux/reducers/company/reducer';
import {DataController} from 'screen/base-controller';

export const useCompanyData = () => {
  const dispatch = useDispatch<any>();
  const companyProfile = useSelectorCustomerCompanyState().data;
  const {data: user, role: userRole} = useSelectorCustomerState();
  const isLoading = useSelectorCustomerCompanyState().onLoading;
  const [bankList, setBankList] = useState<Array<any>>([]);

  useEffect(() => {
    // Fetch company profile when component mounts
    if (user?.CompanyProfileId) {
      CompanyProfileActions.getInfor(dispatch, user.CompanyProfileId);
    }

    // Fetch bank list for registration form
    const bankController = new DataController('Bank');
    bankController.getAll().then(res => {
      if (res.code === 200) setBankList(res.data);
    });
  }, [dispatch, user?.CompanyProfileId]);

  const refreshCompanyData = () => {
    if (user?.CompanyProfileId) {
      CompanyProfileActions.getInfor(dispatch, user.CompanyProfileId);
    }
  };

  return {
    companyProfile,
    user,
    userRole,
    isLoading,
    bankList,
    refreshCompanyData,
  };
};
