import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {faHeart} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {useDispatch} from 'react-redux';
import {ColorThemes} from '../../../../assets/skin/colors';
import AppSvg from '../../../../component/AppSvg';
import iconSvg from '../../../../svgs/iconSvg';
import type {ProductItem} from '../../../../types/ProductType';
import {CartActions} from '../../../../redux/reducers/cart/CartReducer';

interface ProductItemProps {
  item: ProductItem;
  showFavorite?: boolean;
  showCart?: boolean;
  onPressFavorite: (item: ProductItem) => void;
}

const ProductFavoriteCard = ({
  item,
  showFavorite = false,
  onPressFavorite,
}: ProductItemProps) => {
  const dispatch = useDispatch();
  const onUnFavorite = (_id: string) => {};

  const addToCart = () => {
    CartActions.addItemToCart(item, 1)(dispatch);
  };

  const handlePress = () => {
    onPressFavorite(item);
  };

  const hasDiscount = item.Discount && item.Discount > 0;

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={handlePress}
      style={styles.productItemContainer}>
      <View style={styles.imageContainer}>
        <Image
          source={{uri: item.Img}}
          resizeMode="cover"
          style={styles.productImage}
        />
        {/* Discount Badge */}
        {hasDiscount && (
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>-{item.Discount}%</Text>
          </View>
        )}
      </View>

      <View style={styles.infoActionContainer}>
        <View style={styles.infoContainer}>
          <Text style={styles.productName} numberOfLines={2}>
            {item.Name}
          </Text>
          <Text style={styles.productPrice}>
            {item.Price ? item.Price.toLocaleString() : 0} đ
          </Text>
        </View>

        <View style={styles.actionsContainer}>
          {showFavorite && (
            <TouchableOpacity
              style={[styles.actionButton, styles.favoriteButton]}
              onPress={() => onUnFavorite(item.Id)}>
              <FontAwesomeIcon
                icon={faHeart}
                size={16}
                color={
                  item.IsFavorite
                    ? ColorThemes.light.error_main_color
                    : ColorThemes.light.primary_main_color
                }
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[styles.actionButton]}
            onPress={() => addToCart()}>
            <AppSvg SvgSrc={iconSvg.carShopping} size={25} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  productItemContainer: {
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    marginVertical: 8,
  },
  imageContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  discountBadge: {
    position: 'absolute',
    top: 4,
    left: 4,
    backgroundColor: ColorThemes.light.warning_main_color,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    zIndex: 1,
  },
  discountText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 10,
    fontWeight: 'bold',
  },
  infoActionContainer: {
    flex: 1,
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
  },
  infoContainer: {
    width: '60%',
    height: '100%',
    justifyContent: 'space-between',
  },
  productName: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  productPrice: {
    fontSize: 16,
    color: ColorThemes.light.error_main_color,
    fontWeight: '600',
  },
  actionsContainer: {
    width: 90,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  favoriteButton: {
    backgroundColor: ColorThemes.light.warning_background,
  },
});

export default ProductFavoriteCard;
