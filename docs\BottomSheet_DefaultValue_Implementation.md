# Triển khai truyền giá trị mặc định vào BottomSheet

## Mục tiêu
Tìm kiếm tất cả các trường gọi đến hàm `openBottomSheet` trong ProductFormFields.tsx và sửa để truyền giá trị mặc định (Id) sang BottomSheetComponent, hiển thị checkbox tương ứng với giá trị value.

## Phân tích các trường tìm thấy

### Trong ProductFormFields.tsx:
1. **"phân loại"** (dòng 102) - field `Type`
2. **"danh mục sản phẩm"** (dòng 143) - field `CategoryId`
3. **"nhóm thuộc tính"** (dòng 188) - field `CateAttributeId`

## Các thay đổi đã thực hiện

### 1. ProductFormFields.tsx

#### a) Cập nhật interface:
```typescript
openBottomSheet: (title: string, currentValue?: any) => void;
```

#### b) Cập nhật các lời gọi openBottomSheet:
```typescript
// Phân loại
onPress={() => {
  openBottomSheet('phân loại', value);
}}

// Danh mục sản phẩm  
onPress={() => {
  openBottomSheet('danh mục sản phẩm', value);
}}

// Nhóm thuộc tính
onPress={() => {
  openBottomSheet('nhóm thuộc tính', value);
}}
```

### 2. BottomSheetComponent.tsx

#### a) Cập nhật interface:
```typescript
interface BottomSheetProps {
  title?: string;
  onSelect?: (item: any) => void;
  renderItem?: (item: any, index: number) => React.ReactNode;
  children?: React.ReactNode;
  value?: any; // Thêm prop value
}
```

#### b) Cập nhật component signature:
```typescript
({title = 'Chọn mục', onSelect, renderItem, children, value}, ref) => {
```

#### c) Thêm useEffect xử lý giá trị mặc định:
```typescript
useEffect(() => {
  if (value && dataParent.length > 0) {
    console.log('Setting default value:', value, 'for title:', title);
    
    if (title === 'nhóm thuộc tính') {
      // Xử lý multi-select cho nhóm thuộc tính
      const valueIds = value.toString().split(',').map((id: string) => id.trim());
      const newCheckedItems: {[key: string]: boolean} = {};
      const selectedItems: any[] = [];

      valueIds.forEach((id: string) => {
        if (id) {
          newCheckedItems[id] = true;
          const foundItem = dataParent.find(item => item.Id === id);
          if (foundItem) {
            selectedItems.push(foundItem);
          }
        }
      });

      setCheckedItems(newCheckedItems);
      setArrayDataListAttribute(selectedItems);
    } else if (title === 'phân loại' || title === 'nguồn gốc' || title === 'danh mục sản phẩm') {
      // Xử lý single-select cho các title khác
      const valueId = value.toString();
      const foundItem = dataParent.find(item => item.Id === valueId);
      if (foundItem) {
        setSelectParentId(valueId);
        setselectChildren(valueId);
      }
    }
  }
}, [value, dataParent, title]);
```

### 3. CreatePartnerProductForm.tsx

#### a) Thêm state:
```typescript
const [currentBottomSheetValue, setCurrentBottomSheetValue] = useState<any>(null);
```

#### b) Cập nhật function openBottomSheet:
```typescript
const openBottomSheet = (title: string, currentValue?: any) => {
  setSelectOption(title);
  
  // Lấy giá trị hiện tại từ form nếu không có currentValue
  let value = currentValue;
  if (!value) {
    if (title === 'phân loại') {
      value = watch('Type');
    } else if (title === 'danh mục sản phẩm') {
      value = watch('CategoryId');
    } else if (title === 'nhóm thuộc tính') {
      value = watch('CateAttributeId');
    }
  }
  
  console.log('Opening bottom sheet for', title, 'with value:', value);
  setCurrentBottomSheetValue(value);
  bottomSheetRef.current?.show();
};
```

#### c) Truyền prop value vào BottomSheetComponent:
```typescript
<BottomSheetComponent
  ref={bottomSheetRef}
  title={selectOption}
  onSelect={handleSelectProductType}
  value={currentBottomSheetValue}
/>
```

## Kết quả đạt được

### ✅ Tính năng hoạt động:

#### Đối với "phân loại" và "danh mục sản phẩm":
- Khi mở BottomSheet, item có Id trùng với giá trị hiện tại sẽ hiển thị radio button được chọn
- Single-select: Chỉ chọn được 1 item
- Đóng modal ngay sau khi chọn

#### Đối với "nhóm thuộc tính":
- Khi mở BottomSheet, các item có Id trong chuỗi giá trị sẽ hiển thị checkbox được tích
- Multi-select: Có thể chọn nhiều item
- Hiển thị input text khi item được chọn
- Cần nhấn "Xác nhận" để hoàn tất

### 🔄 Luồng hoạt động:

1. **User click vào field** → `openBottomSheet(title, value)` được gọi
2. **Lấy giá trị hiện tại** → Từ `value` param hoặc `watch(fieldName)`
3. **Set state** → `setCurrentBottomSheetValue(value)`
4. **Mở BottomSheet** → Truyền `value` prop vào component
5. **useEffect trigger** → Khi `value` và `dataParent` có dữ liệu
6. **Set default selection** → Checkbox/radio tương ứng với value
7. **User thấy selection** → Giá trị hiện tại được highlight

### 📊 Mapping giá trị:

| Field | Title | Value Type | Selection Type |
|-------|-------|------------|----------------|
| Type | "phân loại" | Single ID | Radio (single) |
| CategoryId | "danh mục sản phẩm" | Single ID | Radio (single) |
| CateAttributeId | "nhóm thuộc tính" | Comma-separated IDs | Checkbox (multi) |

## Lưu ý kỹ thuật

### Performance:
- useEffect chỉ chạy khi `value`, `dataParent`, hoặc `title` thay đổi
- Console.log để debug quá trình set giá trị
- State được reset khi đóng modal

### Data Handling:
- **Single value**: Convert thành string và tìm item có `Id` tương ứng
- **Multi value**: Split bằng dấu phẩy, tìm multiple items
- **Fallback**: Nếu không tìm thấy item, không set selection

### UI/UX:
- User thấy ngay giá trị đã chọn trước đó
- Không cần nhớ đã chọn gì
- Consistent experience across tất cả fields

Tính năng đã hoàn thiện và sẵn sàng sử dụng! 🚀
