import React, {useRef, useState, useMemo, useEffect} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Platform,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import ListTile from '../../../../../component/list-tile/list-tile';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import {
  ToiletServiceStatus,
  ContractType,
  ContractStatus,
} from '../../../service/components/da';
import WebView from 'react-native-webview';
import {Winicon} from '../../../../../component/export-component';
import ConfigAPI from '../../../../../config/configApi';
import ScreenHeader from '../../../../layout/header';
import FLoading, {LoadingUI} from '../../../../../component/Loading/FLoading';
import AppButton from '../../../../../component/button';
import WScreenFooter from '../../../../layout/footer';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../../router/router';
import {saveDataToAsyncStorage} from '../../../../../utils/AsyncStorage';

export default function ContractToiletListTab({
  data,
  serviceData,
  refreshing,
  onRefresh,
}: any) {
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const userRole = useSelectorCustomerState().role;
  const popupRef = useRef<any>();
  const [services, setServices] = useState({data: [], totalCount: undefined});
  const isEditable = useMemo(() => user?.Id === data?.CustomerId, [user, data]);
  const [customers, setCustomers] = useState<Array<any>>([]);
  const [contracts, setContracts] = useState<Array<any>>([]);
  const [rating, setRating] = useState<Array<any>>([]);
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);
  const [cateServices, setCateServices] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');
  const [isLoadingContract, setLoadingContract] = useState(false);
  const navigation = useNavigation<any>();

  const {width, height} = Dimensions.get('window');

  const getData = async ({page, size}: any) => {
    const toiletServiceController = new DataController('ToiletServices');
    let query = `@ToiletId:{${data.Id}} @Status:[${ToiletServiceStatus.contract} +inf]`;
    if (searchValue?.length) query += ` @Name:(*${searchValue}*)`;

    const res = await toiletServiceController.aggregateList({
      page: page ?? 1,
      size: size ?? 100,
      searchRaw: query,
    });
    if (res.code === 200) {
      const customerController = new DataController('Customer');
      customerController
        .getByListId(
          res.data
            .map((e: any) => e.CustomerId)
            .filter(
              (id: string, i: any, arr: string | any[]) =>
                id?.length && arr.indexOf(id) === i,
            ),
        )
        .then(resCustomer =>
          setCustomers(
            (resCustomer.data ?? []).map((e: any) => ({
              ...e,
              bgColor: Ultis.generateDarkColorRgb(),
            })),
          ),
        );
      const contractController = new DataController('Contract');
      contractController
        .getListSimple({
          page: 1,
          size: 1000,
          query: `@ToiletServicesId:{${res.data.map((e: any) => e.Id).join(' | ')}} @Type:[${ContractType.contract} ${ContractType.contract}]`,
        })
        .then(resContract => {
          if (resContract.code === 200) {
            setContracts(resContract.data ?? []);
          }
        });
      const ratingController = new DataController('Rating');
      ratingController
        .getListSimple({
          page: 1,
          size: 1000,
          query: `@ToiletServicesId:{${res.data.map((e: any) => e.Id).join(' | ')}}`,
        })
        .then(resContract => setRating(resContract.data ?? []));
      setServices({data: res.data, totalCount: res.totalCount});
    }
  };

  useEffect(() => {
    const controller = new DataController('CateServices');
    controller.getAll().then(res => setCateServices(res.data ?? []));
  }, []);

  const returnButtonChangeStatus = (item: any) => {
    if (!item) return <View />;

    switch (item?.Status) {
      case ToiletServiceStatus.register:
        var processLabel = 'Chờ tiếp nhận';
        break;
      case ToiletServiceStatus.research:
        processLabel = 'Đang khảo sát';
        break;
      case ToiletServiceStatus.consultant:
        processLabel = 'Đang tư vấn';
        break;
      case ToiletServiceStatus.sendCompleteQuote:
        processLabel = 'Đã gửi báo giá';
        break;
      case ToiletServiceStatus.contract:
        processLabel = 'Đang làm hợp đồng';
        break;
      case ToiletServiceStatus.sendCompleteContract:
        processLabel = 'Đã gửi hợp đồng';
        break;
      case ToiletServiceStatus.design:
        processLabel = 'Đang thiết kế';
        break;
      case ToiletServiceStatus.sendCompleteDesign:
        processLabel = 'Đã gửi thiết kế';
        break;
      case ToiletServiceStatus.build:
        processLabel = 'Đang thực hiện hợp đồng';
        break;
      case ToiletServiceStatus.sendCompleteBuild:
        processLabel = 'Đã gửi biên bản nghiệm thu';
        break;
      case ToiletServiceStatus.liquid:
        processLabel = 'Thanh lý hợp đồng';
        break;
      default:
        processLabel = 'Hoàn thành';
        break;
    }
    return (
      <Text
        style={{
          ...TypoSkin.title3,
          alignSelf: 'baseline',
          borderColor: ColorThemes.light.primary_border_color,
          borderWidth: 1,
          color: ColorThemes.light.neutral_absolute_background_color,
          backgroundColor: ColorThemes.light.primary_main_color,
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 8,
        }}>
        {processLabel}
      </Text>
    );
  };

  useEffect(() => {
    if (data) getData({});
  }, [data]);

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <View style={{paddingHorizontal: 16, flex: 1}}>
        <FlatList
          contentContainerStyle={{
            paddingBottom: isEditable ? 56 : 16,
            paddingTop: 16,
          }}
          data={services.data}
          style={{flex: 1}}
          refreshControl={
            <RefreshControl
              refreshing={refreshing ?? false}
              onRefresh={() => {
                if (onRefresh) onRefresh();
                getData({});
              }}
            />
          }
          keyExtractor={(item, i) => item + '-' + i}
          ItemSeparatorComponent={() => <View style={{height: 12}} />}
          renderItem={({item, index}: any) => {
            const _customer = customers.find(e => e.Id === item.CustomerId);
            const _contract = contracts.find(
              e => e.ToiletServicesId === item.Id,
            );
            const _rate = rating.find(e => e.ToiletServicesId === item.Id);
            const checkWorking = item.Status !== ToiletServiceStatus.run;

            return (
              <ListTile
                key={`HD${index} ${item.Id}`}
                onPress={() => {
                  if (checkWorking) {
                    navigation.navigate(RootScreen.DetailWorkView, {
                      Id: item.Id,
                      Status: item.Status,
                      Name: item.Name,
                    });
                  } else {
                    console.log('====================================');
                    console.log(
                      _contract?.Id,
                      `${ConfigAPI.urlWeb}contract-web-view?type=contract&id=${_contract?.Id}`,
                    );
                    console.log('====================================');
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            height: Dimensions.get('window').height - 65,
                            borderTopLeftRadius: 12,
                            borderTopRightRadius: 12,
                          }}>
                          <ScreenHeader
                            style={{
                              backgroundColor: ColorThemes.light.transparent,
                              flexDirection: 'row',
                              paddingVertical: 4,
                            }}
                            title={`${item?.Name}`}
                            prefix={<View />}
                            action={
                              <TouchableOpacity
                                onPress={() => closePopup(popupRef)}
                                style={{padding: 12, alignItems: 'center'}}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  size={20}
                                  color={
                                    ColorThemes.light.neutral_text_body_color
                                  }
                                />
                              </TouchableOpacity>
                            }
                          />
                          <WebView
                            key={`${_contract?.Id}`}
                            renderError={() => <EmptyPage />}
                            startInLoadingState
                            onLoadEnd={() => {
                              console.log('WebView finished loading');
                            }}
                            textZoom={100}
                            bounces={false}
                            nestedScrollEnabled
                            limitsNavigationsToAppBoundDomains
                            renderLoading={() => {
                              return (
                                <View
                                  style={{
                                    backgroundColor: 'transparent',
                                    position: 'absolute',
                                    left: 0,
                                    right: 0,
                                    top: height / 3,
                                    zIndex: 9,
                                  }}>
                                  <LoadingUI />
                                </View>
                              );
                            }}
                            style={{
                              flex: 1,
                              height: '100%',
                              width: Dimensions.get('screen').width,
                            }}
                            originWhitelist={['*']}
                            injectedJavaScript='document.body.querySelector(".innerhtml-view").style.paddingBottom = "100px";'
                            source={{
                              uri: `${ConfigAPI.urlWeb}contract-web-view?type=contract&id=${_contract?.Id}`,
                            }}
                          />
                        </View>
                      ),
                    });
                  }
                }}
                title={`${item?.Name}`}
                titleStyle={[
                  TypoSkin.heading7,
                  {
                    color: ColorThemes.light.neutral_text_title_color,
                    textDecorationLine: 'underline',
                  },
                ]}
                subtitle={
                  <View style={{gap: 4, paddingTop: 8}}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      Đối tác: {_customer?.Name ?? '-'}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      SĐT: {_customer?.Mobile ?? '-'}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      Ngày đăng ký dịch vụ:{' '}
                      {Ultis.datetoString(new Date(item.DateCreated))}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      Ngày ký:{' '}
                      {_contract?.DateSign
                        ? Ultis.datetoString(
                            new Date(_contract.DateSign),
                            'dd/mm/yyyy',
                          )
                        : '-'}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      Dịch vụ:{' '}
                      {cateServices
                        .filter(e => item?.CateServicesId.includes(e.Id))
                        .map(e => e.Name)
                        .join(', ') ?? ''}
                    </Text>
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>
                      Giá trị hợp đồng:{' '}
                      {item.Value ? Ultis.money(item.Value) + ' VNĐ' : ''}
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 4,
                        paddingBottom: 8,
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.buttonText4,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                        }}>
                        Đánh giá: {_rate?.Value ?? 0}
                      </Text>
                      <Winicon
                        src={'fill/user interface/star'}
                        size={16}
                        color={ColorThemes.light.secondary1_main_color}
                      />
                    </View>
                    {returnButtonChangeStatus(item)}
                  </View>
                }
                listtileStyle={{gap: 16}}
                style={{
                  borderColor: ColorThemes.light.neutral_main_border_color,
                  borderWidth: 1,
                  marginTop: 16,
                  padding: 16,
                }}
              />
            );
          }}
          ListEmptyComponent={() => (
            <EmptyPage title={'Nhà vệ sinh chưa đăng ký sử dụng dịch vụ nào'} />
          )}
          ListFooterComponent={() => <View style={{height: 56}} />}
        />
      </View>
      {user?.Id === data?.CustomerId && services.data.length == 0 ? (
        <WScreenFooter style={{paddingHorizontal: 16}}>
          <AppButton
            title={'Đăng ký tư vấn số hóa nhà vệ sinh'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              saveDataToAsyncStorage('contact-toiletid', data?.Id);
              navigation.reset({
                index: 0,
                routes: [{name: RootScreen.navigateView}],
              });

              setTimeout(() => {
                navigation.push(RootScreen.ServicesWorkFlow, {
                  type: RootScreen.contact,
                });
              }, 100);
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      ) : null}
    </View>
  );
}
