import {Platform, PermissionsAndroid, <PERSON><PERSON>, Linking} from 'react-native';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';

/**
 * Helper class để xử lý việc lưu <PERSON>nh vào Camera Roll/Photo Library
 * Sử dụng @react-native-camera-roll/camera-roll package
 */
export class CameraRollHelper {
  /**
   * Kiểm tra và xin quyền truy cập thư viện ảnh
   * @returns Promise<boolean> - true nếu có quyền, false nếu không
   */
  static async requestPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const androidVersion = Platform.Version;
        console.log('Android version:', androidVersion);

        const getCheckPermissionPromise = () => {
          if (androidVersion >= 33) {
            // Android 13+ cần READ_MEDIA_IMAGES và READ_MEDIA_VIDEO
            return Promise.all([
              PermissionsAndroid.check(
                PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
              ),
              PermissionsAndroid.check(
                PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
              ),
            ]).then(
              ([hasReadMediaImagesPermission, hasReadMediaVideoPermission]) =>
                hasReadMediaImagesPermission && hasReadMediaVideoPermission,
            );
          } else {
            // Android cũ hơn cần READ_EXTERNAL_STORAGE và WRITE_EXTERNAL_STORAGE
            return Promise.all([
              PermissionsAndroid.check(
                PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
              ),
              PermissionsAndroid.check(
                PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
              ),
            ]).then(
              ([hasReadPermission, hasWritePermission]) =>
                hasReadPermission && hasWritePermission,
            );
          }
        };

        const hasPermission = await getCheckPermissionPromise();
        console.log('Current permissions status:', hasPermission);
        if (hasPermission) {
          return true;
        }

        const getRequestPermissionPromise = () => {
          if (androidVersion >= 33) {
            return PermissionsAndroid.requestMultiple([
              PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
              PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
            ]).then(
              statuses =>
                statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] ===
                  PermissionsAndroid.RESULTS.GRANTED &&
                statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] ===
                  PermissionsAndroid.RESULTS.GRANTED,
            );
          } else {
            return PermissionsAndroid.requestMultiple([
              PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
              PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
            ]).then(
              statuses =>
                statuses[
                  PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
                ] === PermissionsAndroid.RESULTS.GRANTED &&
                statuses[
                  PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
                ] === PermissionsAndroid.RESULTS.GRANTED,
            );
          }
        };

        const permissionResult = await getRequestPermissionPromise();
        console.log('Permission request result:', permissionResult);
        return permissionResult;
      } catch (err) {
        console.warn('Permission error:', err);
        return false;
      }
    }
    // iOS không cần xin quyền runtime cho việc lưu ảnh
    return true;
  }

  /**
   * Lưu ảnh vào thư viện ảnh
   * @param uri - URI của ảnh cần lưu (local file URI)
   * @param albumName - Tên album (chỉ hoạt động trên iOS)
   * @returns Promise với thông tin ảnh đã lưu
   */
  static async saveImage(uri: string, albumName?: string) {
    try {
      const hasPermission = await this.requestPermission();

      if (!hasPermission) {
        throw new Error('Không có quyền truy cập thư viện ảnh');
      }

      const result = await CameraRoll.saveAsset(uri, {
        type: 'photo',
        album: albumName,
      });

      return result;
    } catch (error) {
      console.error('Error saving image to camera roll:', error);
      throw error;
    }
  }

  /**
   * Hiển thị dialog yêu cầu quyền
   */
  static showPermissionDialog() {
    Alert.alert(
      'Cần quyền truy cập',
      'Ứng dụng cần quyền truy cập thư viện ảnh để lưu hình ảnh. Vui lòng vào Cài đặt > Ứng dụng > MobileKTX > Quyền để cấp quyền.',
      [
        {text: 'Hủy', style: 'cancel'},
        {
          text: 'Mở Cài đặt',
          onPress: () => {
            Linking.openSettings();
          },
        },
      ],
    );
  }
}
