import {useEffect, useMemo, useRef, useState} from 'react';
import {useSelectorCustomerState} from '../../../../../../../redux/hooks/hooks';
import {DataController} from '../../../../../../base-controller';
import {BaseDA} from '../../../../../../baseDA';
import {showSnackbar} from '../../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../../component/component-status';
import {DeviceBioStatus} from '../../../../../service/components/da';
import {
  closePopup,
  showPopup,
} from '../../../../../../../component/popup/popup';
import {PopupSelectDevices} from '../../../../../workplace/components/popup/PopupSelectDevices';
import {randomGID} from '../../../../../../../utils/Utils';
import {PopupAddEditDevice} from '../components/PopupAddEditDevice';

interface UseDevicesListTabParams {
  data: any;
  formId?: any;
  onRefresh?: any;
}

export function useDevicesListTab({
  data,
  formId,
  onRefresh,
}: UseDevicesListTabParams) {
  const user = useSelectorCustomerState().data;
  const dialogRef = useRef<any>();
  const popupRef = useRef<any>();
  const [devices, setDevices] = useState<any>({
    data: [],
    totalCount: undefined,
  });
  const isEditable = useMemo(() => user?.Id === data?.CustomerId, [user, data]);
  const [isLoading, setLoading] = useState(false);
  const [files, setFiles] = useState<Array<any>>([]);
  const [services, setServices] = useState<Array<any>>([]);
  const [products, setProducts] = useState<Array<any>>([]);
  const [searchValue, setSearchValue] = useState('');

  const getData = async ({page, size}: any) => {
    if (!data?.Id) return;
    setLoading(true);
    const devController = new DataController('Device');
    let query = `@ToiletId:{${data.Id}}`;
    if (searchValue?.length)
      query += ` (@Name:(*${searchValue}*)) | (@Code:(*${searchValue}*)) | (@Unit:(*${searchValue}*))`;
    const res = await devController.aggregateList({
      page: page ?? 1,
      size: size ?? 100,
      searchRaw: query,
    });
    if (res.code === 200) {
      const serviceIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      const toiletServicesController = new DataController('ToiletServices');
      toiletServicesController.getByListId(serviceIds).then(servicesRes => {
        if (servicesRes.code === 200) {
          setServices(servicesRes.data);
        }
      });
      const productIds = res.data
        ?.map((e: any) => e.ProductId)
        .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
      var _tmpFileIds: Array<string> = [];
      _tmpFileIds = res.data
        .filter((e: {Img: any}) => e.Img)
        .map((e: {Img: string}) => e.Img.split(','))
        .flat(Infinity)
        .filter(
          (id: string) =>
            id?.length &&
            files.every(e => e.Id !== id) &&
            !id.startsWith('http'),
        );
      if (productIds?.length) {
        const productController = new DataController('Product');
        const productRes = await productController.getByListId(productIds);
        if (productRes.code === 200) {
          if (productRes.data?.length) {
            setProducts(productRes.data);
            _tmpFileIds.push(
              ...productRes.data
                .map((e: any) => e?.Img?.split(','))
                .flat(Infinity)
                .filter(
                  (id: any) =>
                    id?.length &&
                    files.every((e: any) => e.Id !== id) &&
                    !id.startsWith('http'),
                ),
            );
          }
        }
      }

      BaseDA.getFilesInfor(_tmpFileIds).then((fileRes: any) => {
        if (fileRes.code === 200)
          setFiles((f: any) => [
            ...f,
            ...fileRes.data.filter((e: any) => e !== undefined && e !== null),
          ]);
      });
      setDevices({data: res.data, totalCount: res.totalCount});
      setLoading(false);
    }
  };

  useEffect(() => {
    if (data) getData({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const deleteItem = (ids = Array<any>()) => {
    const controller = new DataController('Device');
    controller.delete(ids).then(res => {
      if (res.code === 200) {
        getData({});
      } else
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
    });
  };

  const showPopupSelectProduct = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupSelectDevices
          ref={popupRef}
          selectOther={() => {
            closePopup(popupRef);
            showAddEdit(undefined, true);
          }}
          onSubmit={async (selectedProducts: any) => {
            const controller = new DataController('Device');
            const newListDevice = selectedProducts
              .map((p: any) => {
                const quantity = p['_Quantity'];
                const tmp: any[] = [];
                for (let i = 0; i < quantity; i++) {
                  tmp.push({
                    Id: randomGID(),
                    Name: p.Name,
                    DateCreated: Date.now(),
                    ToiletId: data.Id,
                    ProductId: p.Id,
                    Quantity: 1,
                    Price: p.Price,
                    Unit: p.Unit,
                    Img: p.Img,
                    Description: p.Description,
                    Specifications: p.Specifications,
                    Status: formId
                      ? DeviceBioStatus.inactive
                      : DeviceBioStatus.active,
                  });
                }
                return tmp;
              })
              .flat(Infinity);
            const res = await controller.add(newListDevice);
            if (res.code === 200) getData({});
          }}
        />
      ),
    });
  };

  const showAddEdit = (item: any, isAddNew = false) => {
    if (item || isAddNew) {
      showPopup({
        ref: popupRef,
        enableDismiss: true,
        children: (
          <PopupAddEditDevice
            ref={popupRef}
            id={item?.Id}
            formId={formId}
            toiletId={data.Id}
            onSubmit={() => {
              getData({});
            }}
          />
        ),
      });
    } else {
      showPopupSelectProduct();
    }
  };

  const onRefreshList = () => {
    if (onRefresh) onRefresh();
    setSearchValue('');
    getData({});
  };

  return {
    dialogRef,
    popupRef,
    devices,
    isLoading,
    files,
    products,
    services,
    searchValue,
    setSearchValue,
    getData,
    deleteItem,
    showAddEdit,
    onRefreshList,
    userId: user?.Id,
    isEditable,
  };
}
