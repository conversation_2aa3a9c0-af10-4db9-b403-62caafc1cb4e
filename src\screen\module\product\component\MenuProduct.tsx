import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {MenuProductProps} from './types';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
const MenuProduct = (props: MenuProductProps) => {
  const {menu, setMenu, data} = props;
  const TypeMenuPorduct = [
    'Còn hàng',
    'Hết hàng',
    'Chờ duyệt',
    'Vi phạm',
    'Ẩn',
  ];
  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 10,
      }}>
      {data &&
        data.length > 0 &&
        data.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={{padding: 5}}
            onPress={() => setMenu(TypeMenuPorduct[index])}>
            <Text
              style={
                menu === TypeMenuPorduct[index]
                  ? {
                      ...TypoSkin.title5,
                      margin: 'auto',
                      color:
                        ColorThemes.light.neutral_text_disabled_reverse_color,
                    }
                  : {
                      ...TypoSkin.title5,
                      margin: 'auto',
                      color: ColorThemes.light.neutral_bolder_border_color,
                    }
              }>
              {item.name}
            </Text>
            <Text
              style={
                menu === TypeMenuPorduct[index]
                  ? {
                      ...TypoSkin.title5,
                      margin: 'auto',
                      color:
                        ColorThemes.light.neutral_text_disabled_reverse_color,
                    }
                  : {
                      ...TypoSkin.title5,
                      margin: 'auto',
                      color: ColorThemes.light.neutral_bolder_border_color,
                    }
              }>
              ({item.number})
            </Text>
          </TouchableOpacity>
        ))}
    </View>
  );
};
export default MenuProduct;
