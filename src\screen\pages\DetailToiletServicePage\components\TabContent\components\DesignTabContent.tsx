import React from 'react';
import {View, StyleSheet} from 'react-native';
import {
  ToiletStatus,
  ToiletServiceStatus,
  TaskType,
  TaskStatus,
} from '../../../../../module/service/components/da';
import EmptyPage from '../../../../../../project-component/empty-page';
import DesignTab from '../../../../../module/workplace/components/form/DesignTab';
import {DataController} from '../../../../../base-controller';
import {useTabHandlers} from '../hooks/useTabHandlers';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface DesignTabContentProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  methods: any;
  guest: any;
  onChangeStatus: (status: any, setServicesValue?: any) => void;
}

export default function DesignTabContent({
  workData,
  serviceData,
  setServiceData,
  onRefresh,
  isRefreshing,
  methods,
  guest,
  onChangeStatus,
}: DesignTabContentProps) {
  const {createBuildTask} = useTabHandlers({
    workData,
    serviceData,
    setServiceData,
    methods,
    onChangeStatus,
  });

  const handleDesignSubmit = async () => {
    if (!serviceData) return;

    const taskController = new DataController('Task');
    const res = await taskController.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData.Id}} @Type:[${TaskType.design} ${TaskType.design}] @Status:[${TaskStatus.open} ${TaskStatus.overdue}]`,
    });

    if (res.code === 200 && res.data.length) {
      taskController.edit(
        res.data.map((e: any) => ({
          ...e,
          Status: TaskStatus.done,
        })),
      );
    }

    if (serviceData.Status < ToiletServiceStatus.build) {
      onChangeStatus(ToiletStatus.build);
      await createBuildTask(taskController);
    }
  };

  if (
    serviceData?.Status != null &&
    serviceData.Status < ToiletServiceStatus.design
  ) {
    return (
      <View style={styles.emptyContainer}>
        <EmptyPage title={'Đơn hàng chưa thống nhất được hợp đồng'} />
      </View>
    );
  }

  return (
    <DesignTab
      toiletData={workData}
      serviceData={serviceData}
      setServiceData={setServiceData}
      onRefreshing={onRefresh}
      customer={guest}
      isRefreshing={isRefreshing}
      onSubmit={handleDesignSubmit}
    />
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
  },
});
