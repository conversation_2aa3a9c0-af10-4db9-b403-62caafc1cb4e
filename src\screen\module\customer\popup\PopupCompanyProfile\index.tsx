import {forwardRef, useRef, useState, useEffect} from 'react';
import {useForm} from 'react-hook-form';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {
  showDialog,
  ComponentStatus,
  closePopup,
  FDialog,
  Winicon,
  AppButton,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {
  TextFieldForm,
  Fselect1Form,
  FAddressPickerForm,
} from '../../../../../project-component/component-form';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import {CompanyProfileActions} from '../../../../../redux/reducers/company/reducer';
import {
  CustomerItem,
  CustomerRole,
} from '../../../../../redux/reducers/user/da';
import {CustomerActions} from '../../../../../redux/reducers/user/reducer';
import {randomGID} from '../../../../../utils/Utils';
import {validatePhoneNumber} from '../../../../../utils/validate';
import {DataController} from '../../../../base-controller';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import {CustomerDa} from '../../customerDa';
import {showSnackbar} from 'component/export-component';

const PopupCompanyProfile = forwardRef(function PopupCompanyProfile(
  data: {},
  ref: any,
) {
  const {} = data;
  const dialogRef = useRef<any>();
  const companyProfile = useSelectorCustomerCompanyState().data;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: companyProfile ? companyProfile : {Id: randomGID()},
  });
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const customerDa = new CustomerDa();

  const newGID = randomGID();
  const dispatch = useDispatch<any>();
  const [bankList, setBankList] = useState<Array<any>>([]);

  useEffect(() => {
    const bankController = new DataController('Bank');
    bankController.getAll().then(res => {
      if (res.code === 200) setBankList(res.data);
    });
  }, []);

  const checkRole = async (companyId: string) => {
    const role = await customerDa.getAllMemberRole(companyId);
    console.log('check-role', role);
    if (role.code === 200) {
      return role;
    }
    return null;
  };

  const _onSubmit = (ev: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.INFOR,
      title:
        'Bạn chắc chắn muốn lưu hồ sơ doanh nghiệp theo các thông tin đã nhập?',
      onSubmit: async () => {
        const controller = new DataController('CompanyProfile');
        let res;
        if (
          (userRole?.CompanyProfileId &&
            userRole?.Role?.includes(CustomerRole.Owner)) ||
          !userRole?.CompanyProfileId
        ) {
          res = await controller.add([{...ev, DateCreated: Date.now()}]);
        } else {
          closePopup(ref);
          showSnackbar({
            message: 'Bạn không có quyền chỉnh sửa hoặc tạo mới doanh nghiệp',
            status: ComponentStatus.SUCCSESS,
          });
        }
        if (res.code === 200) {
          if (companyProfile) {
            CompanyProfileActions.getInfor(dispatch, companyProfile.Id);
            closePopup(ref);
            showSnackbar({
              message: 'Đã lưu hồ sơ doanh nghiệp',
              status: ComponentStatus.SUCCSESS,
            });
          } else {
            dispatch(
              CustomerActions.edit(dispatch, {
                ...user,
                CompanyProfileId: res.data[0].Id ?? newGID,
              } as CustomerItem).then(res => {
                if (res.code === 200) {
                  CustomerActions.editRole(dispatch, {
                    Id: randomGID(),
                    Name: user?.Name ?? '',
                    DateCreated: Date.now(),
                    Status: 1,
                    Role: CustomerRole.Owner,
                    Sort: 1,
                    Description: 'Chủ doanh nghiệp',
                    CustomerId: user?.Id ?? '',
                    CompanyProfileId: res.data[0].CompanyProfileId ?? newGID,
                  });
                  closePopup(ref);
                  showSnackbar({
                    message: 'Đã lưu hồ sơ doanh nghiệp',
                    status: ComponentStatus.SUCCSESS,
                  });
                }
              }),
            );
          }
        } else {
          showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        }
      },
    });
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 40,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={`Hồ sơ doanh nghiệp`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        behavior={'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 56 : 0}
        style={{height: '100%', width: '100%', paddingHorizontal: 16, flex: 1}}>
        <ScrollView
          style={{backgroundColor: ColorThemes.light.transparent, flex: 1}}>
          <TextFieldForm
            label="Họ và tên"
            required
            textFieldStyle={{padding: 16}}
            style={{width: '100%', marginBottom: 20}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Name"
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            label="Ngành nghề"
            textFieldStyle={{padding: 16}}
            style={{width: '100%', marginBottom: 20}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Industry"
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            control={methods.control}
            name="Mobile"
            label="Số điện thoại doanh nghiệp"
            required
            returnKeyType="done"
            style={{flex: 1, marginBottom: 20}}
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
              padding: 16,
            }}
            register={methods.register}
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
            }}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            label="Email"
            type="email-address"
            textFieldStyle={{padding: 16}}
            style={{width: '100%', marginBottom: 20}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Email"
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <FAddressPickerForm
            control={methods.control}
            errors={methods.formState.errors}
            name="Address"
            label="Địa chỉ"
            placeholder="Nhập địa chỉ doanh nghiệp"
            placeName={''}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
            onChange={value => {
              methods.setValue('Long', value.geometry.location.lng);
              methods.setValue('Lat', value.geometry.location.lat);
              methods.setValue('Address', value.formatted_address);
              return value.formatted_address;
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="TaxCode"
            label="Mã số thuế"
            required
            returnKeyType="done"
            style={{flex: 1, marginBottom: 20}}
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
              padding: 16,
            }}
            register={methods.register}
            type="name-phone-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('TaxCode', {
                  message: 'Mã số thuế không hợp lệ',
                });
                return;
              }
              const val = ev.trim().length == 10 || ev.trim().length == 13;
              if (val) methods.clearErrors('TaxCode');
              else
                methods.setError('TaxCode', {
                  message: 'Mã số thuế không hợp lệ',
                });
            }}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            required
            style={{
              width: '100%',
              backgroundColor: '#fff',
              borderRadius: 8,
              marginBottom: 20,
            }}
            label="Số tài khoản ngân hàng"
            placeholder="Số tài khoản ngân hàng"
            control={methods.control}
            type="number-pad"
            maxLength={13}
            register={methods.register}
            errors={methods.formState.errors}
            name="BankAccount"
            textFieldStyle={{padding: 16}}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            required
            style={{
              width: '100%',
              backgroundColor: '#fff',
              borderRadius: 8,
              marginBottom: 20,
            }}
            label="Tên chủ tài khoản"
            placeholder="Tên chủ tài khoản"
            control={methods.control}
            register={methods.register}
            errors={methods.formState.errors}
            name="BankAccountName"
            textFieldStyle={{padding: 16}}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <Fselect1Form
            required
            placeholder="Chọn ngân hàng"
            label="Ngân hàng"
            control={methods.control}
            errors={methods.formState.errors}
            style={{
              width: '100%',
              backgroundColor: '#fff',
              borderRadius: 8,
              marginBottom: 20,
            }}
            name="BankId"
            options={bankList.map((e: any) => ({id: e.Id, name: e.Name}))}
          />
          <TextFieldForm
            label="Người đại diện"
            required
            textFieldStyle={{padding: 16}}
            style={{width: '100%', marginBottom: 20}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Representative"
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            control={methods.control}
            name="Phone"
            label="SĐT người đại diện"
            returnKeyType="done"
            style={{flex: 1, marginBottom: 20}}
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
              padding: 16,
            }}
            register={methods.register}
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Phone', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Phone');
              else
                methods.setError('Phone', {
                  message: 'Số điện thoại không hợp lệ',
                });
            }}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            label="Chức vụ"
            textFieldStyle={{padding: 16}}
            style={{width: '100%', marginBottom: 20}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Position"
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
          <TextFieldForm
            control={methods.control}
            name="Note"
            label="Chú thích"
            errors={methods.formState.errors}
            placeholder={'Chú thích'}
            style={{backgroundColor: ColorThemes.light.transparent}}
            textFieldStyle={{
              height: 100,
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 16,
              marginBottom: 100,
              justifyContent: 'flex-start',
              backgroundColor: ColorThemes.light.transparent,
            }}
            textStyle={{textAlignVertical: 'top'}}
            numberOfLines={10}
            multiline={true}
            register={methods.register}
            disabled={
              userRole?.CompanyProfileId &&
              !userRole?.Role?.includes(CustomerRole.Owner)
                ? true
                : false
            }
          />
        </ScrollView>
      </KeyboardAvoidingView>
      {(userRole?.CompanyProfileId &&
        userRole?.Role?.includes(CustomerRole.Owner)) ||
      (!userRole?.Role && !userRole?.CompanyProfileId) ? (
        <WScreenFooter
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingBottom: 16,
          }}>
          <AppButton
            title={'Lưu'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              methods.handleSubmit(_onSubmit, _onError)();
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      ) : null}
    </SafeAreaView>
  );
});

export default PopupCompanyProfile;
