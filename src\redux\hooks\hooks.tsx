
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';

const useSelectorCustomerState = () => useSelector((state: RootState) => state.customer);
const useSelectorCustomerCompanyState = () => useSelector((state: RootState) => state.company);
const useSelectorToiletState = () => useSelector((state: RootState) => state.toilet);
const useSelectorNotificationState = () => useSelector((state: RootState) => state.notification);
const useSelectorCateServiceState = () => useSelector((state: RootState) => state.services);

export { useSelectorCustomerState, useSelectorCustomerCompanyState, useSelectorToiletState, useSelectorNotificationState, useSelectorCateServiceState };
