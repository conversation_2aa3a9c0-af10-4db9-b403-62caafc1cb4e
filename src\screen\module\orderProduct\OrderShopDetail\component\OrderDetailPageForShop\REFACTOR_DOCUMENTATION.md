# OrderDetailPageForShop Refactor Documentation

## Tổng quan
Tài liệu này mô tả quá trình refactor component `OrderDetailPageForShop` nhằm cải thiện cấu trúc code, tăng tính tái sử dụng và dễ bảo trì.

## Mục tiêu Refactor
1. **Tách logic nghiệp vụ**: Tách logic phức tạp thành các custom hooks
2. **Tách UI components**: Chia nhỏ UI thành các components độc lập
3. **Cải thiện maintainability**: Dễ dàng bảo trì và mở rộng
4. **Tăng tính tái sử dụng**: Các hooks và components có thể sử dụng ở nơi khác

## Cấu trúc trước khi Refactor
```
OrderDetailPageForShop/
├── index.tsx (1254 dòng - tất cả logic và UI trong 1 file)
```

## Cấu trúc sau khi Refactor
```
OrderDetailPageForShop/
├── index.tsx (385 dòng - chỉ chứa logic chính và render)
├── hooks/
│   ├── useOrderData.ts (quản lý data và API calls)
│   ├── useOrderActions.ts (xử lý các actions)
│   └── useOrderStatus.ts (quản lý trạng thái UI)
├── components/
│   ├── OrderStatusTimeline.tsx (timeline trạng thái đơn hàng)
│   ├── OrderInfo.tsx (thông tin chi tiết đơn hàng)
│   ├── OrderItems.tsx (danh sách sản phẩm)
│   └── ActionButtons.tsx (các nút hành động)
└── REFACTOR_DOCUMENTATION.md
```

## Chi tiết các thay đổi

### 1. Custom Hooks

#### useOrderData.ts
**Chức năng**: Quản lý việc fetch và refresh dữ liệu đơn hàng
**Exports**:
- `loading`: trạng thái loading
- `order`: thông tin đơn hàng
- `orderDetails`: chi tiết đơn hàng
- `refreshing`: trạng thái refresh
- `fetchOrderData`: function fetch data
- `handleRefresh`: function refresh data

**Logic được tách**:
- Fetch order data từ API
- Fetch order details và product info
- Xử lý reward history
- Refresh control

#### useOrderActions.ts
**Chức năng**: Xử lý các hành động nghiệp vụ
**Exports**:
- `handleUpdateStatusProcessOrder`: xác nhận đơn hàng
- `handleSubmitRejectOrder`: từ chối/hủy đơn hàng
- `handleSubmitUpdateStatus`: cập nhật trạng thái
- `isSubmittingCancel`: trạng thái submit cancel
- `isSubmittingUpdateStatus`: trạng thái submit update

**Logic được tách**:
- Xác nhận đơn hàng (kiểm tra tồn kho, cập nhật product)
- Hủy đơn hàng
- Cập nhật trạng thái đơn hàng
- Xử lý mission customer khi hoàn thành đơn hàng

#### useOrderStatus.ts
**Chức năng**: Quản lý trạng thái UI (popups, modals)
**Exports**:
- `isCancelPopupVisible`: trạng thái popup hủy đơn
- `isUpdateStatusPopupVisible`: trạng thái popup cập nhật
- `handleRejectOrder`: mở popup hủy đơn
- `closeCancelPopup`: đóng popup hủy đơn
- `closeUpdateStatusPopup`: đóng popup cập nhật
- `setUpdateStatusPopupVisible`: setter cho popup cập nhật

### 2. UI Components

#### OrderStatusTimeline.tsx
**Chức năng**: Hiển thị timeline trạng thái đơn hàng
**Props**:
- `currentStatus`: trạng thái hiện tại
- `order`: thông tin đơn hàng

**Features**:
- Hiển thị các bước: Đặt hàng → Chờ lấy hàng → Hoàn thành
- Xử lý trường hợp đơn hàng bị hủy
- Hiển thị thời gian cho từng bước

#### OrderInfo.tsx
**Chức năng**: Hiển thị thông tin chi tiết đơn hàng
**Props**:
- `order`: thông tin đơn hàng
- `currentStatus`: trạng thái hiện tại
- `CancelReason`: lý do hủy (optional)

**Features**:
- Mã đơn hàng (có thể copy)
- Thời gian đặt hàng
- Phương thức thanh toán
- Thông tin người nhận
- Lý do hủy (nếu có)

#### OrderItems.tsx
**Chức năng**: Hiển thị danh sách sản phẩm trong đơn hàng
**Props**:
- `orderDetails`: danh sách sản phẩm
- `order`: thông tin đơn hàng
- `type`: loại đơn hàng (optional)
- `refundInfo`: thông tin refund (optional)

**Features**:
- Danh sách sản phẩm với hình ảnh, tên, giá
- Xử lý discount
- Tính tổng tiền
- Tổng hoàn CANPOINT
- Xử lý trường hợp refund

#### ActionButtons.tsx
**Chức năng**: Các nút hành động cho shop
**Props**:
- `currentStatus`: trạng thái đơn hàng
- `onUpdateStatusPress`: callback cập nhật trạng thái
- `onConfirmOrderPress`: callback xác nhận đơn hàng
- `onRejectOrderPress`: callback từ chối đơn hàng

**Features**:
- Nút cập nhật trạng thái (hiển thị theo điều kiện)
- Nút chat với người mua
- Nút xác nhận/từ chối đơn hàng (chỉ với đơn mới)

### 3. Component chính (index.tsx)

**Thay đổi chính**:
- Giảm từ 1254 dòng xuống 385 dòng (giảm 69%)
- Sử dụng custom hooks thay vì logic inline
- Sử dụng components con thay vì render functions
- Code sạch hơn, dễ đọc hơn

**Cấu trúc mới**:
```tsx
const OrderDetailPageForShop = () => {
  // Route params
  const {orderId, type, CancelReason, refundInfo} = route.params;
  
  // Custom hooks
  const {...} = useOrderData(orderId);
  const {...} = useOrderActions(order, orderDetails, fetchOrderData);
  const {...} = useOrderStatus();
  
  // Handlers
  const handleConfirmOrder = () => {...};
  const handleUpdateStatus = () => {...};
  const handleCancelOrderSubmit = async (reason) => {...};
  
  // Render
  return (
    <View>
      {/* Popups */}
      <PopupCancelOrder ... />
      <PopupUpdateStatusOrder ... />
      
      {/* Header */}
      <RenderHeaderOrder ... />
      
      {/* Content */}
      <ScrollView>
        <OrderStatusTimeline ... />
        <OrderInfo ... />
        <OrderItems ... />
      </ScrollView>
      
      {/* Actions */}
      <ActionButtons ... />
    </View>
  );
};
```

## Lợi ích của Refactor

### 1. Maintainability
- **Tách biệt concerns**: Mỗi hook/component có trách nhiệm riêng
- **Dễ debug**: Lỗi được cô lập trong từng phần
- **Dễ test**: Có thể test từng hook/component độc lập

### 2. Reusability
- **Custom hooks**: Có thể sử dụng trong các component khác
- **UI components**: Có thể tái sử dụng trong các màn hình tương tự
- **Logic tách biệt**: Dễ dàng adapt cho các use case khác

### 3. Readability
- **Code ngắn gọn**: Component chính chỉ tập trung vào render
- **Tên rõ ràng**: Hooks và components có tên mô tả chức năng
- **Cấu trúc logic**: Dễ hiểu flow của ứng dụng

### 4. Scalability
- **Dễ mở rộng**: Thêm features mới không ảnh hưởng code cũ
- **Modular**: Có thể thay đổi từng phần độc lập
- **Performance**: Có thể optimize từng component riêng

## Các lưu ý khi sử dụng

### 1. Dependencies
- Đảm bảo các import paths đúng
- Kiểm tra các dependencies của hooks

### 2. Props Interface
- Các components có interface rõ ràng
- TypeScript sẽ check type safety

### 3. State Management
- Hooks quản lý state riêng biệt
- Truyền data qua props giữa components

### 4. Error Handling
- Mỗi hook có error handling riêng
- Components hiển thị error states phù hợp

## Kết luận

Quá trình refactor đã thành công trong việc:
- ✅ Giảm complexity của component chính
- ✅ Tăng tính tái sử dụng của code
- ✅ Cải thiện maintainability
- ✅ Tạo cấu trúc code rõ ràng và dễ hiểu
- ✅ Không phá vỡ functionality hiện có

Refactor này tạo nền tảng tốt cho việc phát triển và bảo trì trong tương lai.
