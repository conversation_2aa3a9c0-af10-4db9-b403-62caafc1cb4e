// import React from 'react';
// import {ToiletStatus} from '../../../../module/service/components/da';
// import EmptyPage from '../../../../../project-component/empty-page';
// import {
//   RegisterTabContent,
//   ConsultantTabContent,
//   ContractTabContent,
//   DesignTabContent,
//   BuildTabContent,
//   LiquidTabContent,
// } from './components';
// import {ToiletServiceItem} from '../../../../../types/toiletServiceType';
// import {View} from 'react-native';

// interface WorkData {
//   Id: string;
//   [key: string]: any;
// }

// interface TabContentProps {
//   workData: WorkData[] | null;
//   serviceData: ToiletServiceItem | null;
//   setServiceData: (data: any) => void;
//   onRefresh: () => void;
//   isRefreshing: boolean;
//   methods: any;
//   guest: any;
//   onChangeStatus: (status: any, setServicesValue?: any) => void;
//   handleReject: () => void;
//   handleSubmitRegister: () => void;
// }

// export default function TabContent({
//   workData,
//   serviceData,
//   setServiceData,
//   onRefresh,
//   isRefreshing,
//   methods,
//   guest,
//   onChangeStatus,
//   handleReject,
//   handleSubmitRegister,
// }: TabContentProps) {
//   if (!workData || !workData.length) return <View />;

//   const renderScene = ({route}: any) => {
//     switch (route.key) {
//       case `${ToiletStatus.register}`:
//         return (
//           <RegisterTabContent
//             workData={workData}
//             serviceData={serviceData}
//             setServiceData={setServiceData}
//             onRefresh={onRefresh}
//             isRefreshing={isRefreshing}
//             methods={methods}
//             guest={guest}
//             handleReject={handleReject}
//             handleSubmitRegister={handleSubmitRegister}
//           />
//         );
//       case `${ToiletStatus.consultant}`:
//         return (
//           <ConsultantTabContent
//             workData={workData}
//             serviceData={serviceData}
//             setServiceData={setServiceData}
//             onRefresh={onRefresh}
//             isRefreshing={isRefreshing}
//             methods={methods}
//             guest={guest}
//             onChangeStatus={onChangeStatus}
//           />
//         );
//       case `${ToiletStatus.contract}`:
//         return (
//           <ContractTabContent
//             workData={workData}
//             serviceData={serviceData}
//             setServiceData={setServiceData}
//             onRefresh={onRefresh}
//             isRefreshing={isRefreshing}
//             methods={methods}
//             guest={guest}
//             onChangeStatus={onChangeStatus}
//           />
//         );
//       case `${ToiletStatus.design}`:
//         return (
//           <DesignTabContent
//             workData={workData}
//             serviceData={serviceData}
//             setServiceData={setServiceData}
//             onRefresh={onRefresh}
//             isRefreshing={isRefreshing}
//             methods={methods}
//             guest={guest}
//             onChangeStatus={onChangeStatus}
//           />
//         );
//       case `${ToiletStatus.build}`:
//         return (
//           <BuildTabContent
//             workData={workData}
//             serviceData={serviceData}
//             setServiceData={setServiceData}
//             onRefresh={onRefresh}
//             isRefreshing={isRefreshing}
//             methods={methods}
//             guest={guest}
//             onChangeStatus={onChangeStatus}
//           />
//         );
//       case `${ToiletStatus.liquid}`:
//         return (
//           <LiquidTabContent
//             workData={workData}
//             serviceData={serviceData}
//             setServiceData={setServiceData}
//             onRefresh={onRefresh}
//             isRefreshing={isRefreshing}
//             methods={methods}
//             guest={guest}
//             onChangeStatus={onChangeStatus}
//           />
//         );
//       default:
//         return <EmptyPage />;
//     }
//   };

//   return renderScene;
// }
