import {useEffect, useState} from 'react';
import {CateCriterionItem} from '../../../../../../types/cateCriteriaType';
import CateCriterionDA from '../../../../cateCriterion/cateCriterionDa';

export const useCriterionData = () => {
  const cateCriterionDA = new CateCriterionDA();
  const [dataCriterion, setData] = useState<CateCriterionItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchCriterionData = async () => {
      setLoading(true);
      try {
        const res: any = await cateCriterionDA.getAll();
        if (res.code === 200) {
          setData(res.data);
        }
      } catch (error) {
        console.error('Error fetching criterion data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCriterionData();
  }, []);

  return {
    dataCriterion,
    loading,
  };
};
