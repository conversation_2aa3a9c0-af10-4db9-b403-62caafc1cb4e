import { FlatList, Image, Pressable, Text, TouchableOpacity, View } from "react-native";
import { ColorThemes } from "../../../../../assets/skin/colors";
import { TypoSkin } from "../../../../../assets/skin/typography";
import { FImportMultipleImg } from "../../../../../project-component/component-form";
import ConfigAPI from "../../../../../config/configApi";
import ImageCropPicker from "react-native-image-crop-picker";
import { useEffect, useState } from "react";
import { OutlinePLus } from "../../../../../assets/icon";
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome";
import { faMinusCircle } from "@fortawesome/free-solid-svg-icons";
import ListTile from "../../../../../component/list-tile/list-tile";
import { SkeletonImage } from "../../../../../project-component/skeleton-img";

interface Props {
    methods?: any;
    forConsultant?: boolean;
    display?: string;
}

export default function UploadDeviceImage(props: Props) {
    const { methods, forConsultant = false, display = "flex" } = props;

    const pickerImg = async () => {
        const image = await ImageCropPicker.openPicker({
            multiple: false,
            cropping: false,
            maxFiles: 5,
        })
        if (image) {
            methods.setValue("files", [
                ...(methods.getValues("files") ?? []).filter((e: any) => image.filename !== e.name && image.path !== e.uri && image.size !== e.size),
                {
                    uri: image.path,
                    name: image.filename,
                    width: image.width,
                    height: image.height,
                    type: image.mime,
                    size: image.size
                },
            ])
        }

    }


    return <View style={{ flex: 1, padding: 16, display: display === "flex" ? 'flex' : 'none' }}>
        <Pressable
            style={{
                borderColor: 'transparent',
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
            }}>
            <Text
                style={[
                    TypoSkin.heading5,
                    {
                        color: ColorThemes.light.neutral_text_title_color,
                        paddingBottom: 4,
                    },
                ]}>
                Đăng tải hình ảnh thiết bị hỏng hóc
            </Text>
            <Text
                style={[
                    TypoSkin.body3,
                    {
                        color: ColorThemes.light.neutral_text_body_color,
                        paddingBottom: 8,
                    },
                ]}>
                Giúp chúng tôi chuẩn đoán tình trạng chính xác hơn
            </Text>
        </Pressable>
        <FlatList
            contentContainerStyle={{ paddingVertical: 12 }}
            data={methods.getValues("files")}
            keyExtractor={(item, i) => (item + '-' + i)}
            ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
            ListHeaderComponent={() => {
                return <TouchableOpacity onPress={pickerImg} style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    borderWidth: 0.4,
                    borderColor: ColorThemes.light.neutral_main_border,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                    marginTop: 12,
                }}>
                    <SkeletonImage
                        source={{ uri: "https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png" }}
                        style={{ width: 35, height: 35, objectFit: "cover" }}
                    />
                    <Text numberOfLines={1} style={{ ...TypoSkin.buttonText4, color: ColorThemes.light.neutral_text_subtitle_color }}>Thêm ảnh</Text>
                </TouchableOpacity>
            }}
            renderItem={({ item, index }) => {
                return <ListTile
                    key={`${item.uri}`}
                    leading={
                        <SkeletonImage
                            source={{ uri: item.uri }}
                            style={{ width: 65, height: 65, objectFit: "cover" }}
                        />
                    }
                    title={item.name ?? `Ảnh ${index + 1}`}
                    titleStyle={[
                        TypoSkin.heading7,
                        { color: ColorThemes.light.neutral_text_title_color, },
                    ]}
                    subtitle={`${Math.round(item.size / (1024 * 1024))}MB`}
                    listtileStyle={{ gap: 16, }}
                    style={{
                        borderColor: ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                        marginTop: 16,
                        padding: 8
                    }}
                    trailing={
                        <TouchableOpacity onPress={() => {
                            methods.setValue("files", methods.getValues("files")?.filter((e: any) => e.uri !== item.uri))
                        }} style={{ padding: 4, }}>
                            <FontAwesomeIcon icon={faMinusCircle} size={20} color="#D72525FF" style={{ backgroundColor: '#fff', borderRadius: 20 }} />
                        </TouchableOpacity>
                    }
                />
            }}
        />
    </View>
}