import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  ViewStyle,
  Pressable,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {RootScreen} from '../../../../router/router';
import {useNavigation} from '@react-navigation/native';
import ConfigAPI from '../../../../config/configApi';
import {useSelectorCustomerCompanyState} from '../../../../redux/hooks/hooks';
import {Winicon} from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';

const {width} = Dimensions.get('window');

// Fixed grid configuration: 2 rows x 4 columns = 8 items per page
const COLUMNS_PER_ROW = 4;
const ROWS_PER_PAGE = 2;
const ITEMS_PER_PAGE = COLUMNS_PER_ROW * ROWS_PER_PAGE;

interface Item {
  Id: string;
  Name?: string;
  Img?: string;
  route?: string;
  RouteName?: string;
  color?: string;
  badge?: string;
  Sort: number;
}

interface ServiceGridProps {
  data: Item[];
  style?: ViewStyle;
  styleItem?: ViewStyle;
}

const ServiceGrid: React.FC<ServiceGridProps> = ({data, style, styleItem}) => {
  // Fixed grid configuration: 2 rows x 4 columns
  const padding = 32; // Total horizontal padding
  const gap = 10; // Gap between items
  const totalGaps = (COLUMNS_PER_ROW - 1) * gap;
  const itemWidth = (width - padding - totalGaps) / COLUMNS_PER_ROW;

  // Auto-scroll state
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const autoScrollInterval = useRef<NodeJS.Timeout | null>(null);
  const navigation = useNavigation<any>();
  const company = useSelectorCustomerCompanyState().data;

  // Split data into pages based on responsive layout
  const chunkArray = (array: Item[], size: number): Item[][] => {
    const chunkedArray: Item[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunkedArray.push(array.slice(i, i + size));
    }
    return chunkedArray;
  };

  // Get current data to display with fixed page size (8 items per page)
  const getCurrentData = (): Item[][] => {
    return data && data.length > 0 ? chunkArray(data, ITEMS_PER_PAGE) : [];
  };

  // Auto-scroll effect - scrolls every 4 seconds
  useEffect(() => {
    const totalData = getCurrentData();

    if (totalData.length <= 1) {
      return; // No need to auto-scroll if only one page
    }

    const startAutoScroll = () => {
      autoScrollInterval.current = setInterval(() => {
        setCurrentIndex(prevIndex => {
          const nextIndex = (prevIndex + 1) % totalData.length;
          flatListRef.current?.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
          return nextIndex;
        });
      }, 4000);
    };

    startAutoScroll();

    return () => {
      if (autoScrollInterval.current) {
        clearInterval(autoScrollInterval.current);
      }
    };
  }, [data]);

  // Stop auto-scroll when user interacts
  const handleScrollBeginDrag = () => {
    if (autoScrollInterval.current) {
      clearInterval(autoScrollInterval.current);
      autoScrollInterval.current = null;
    }
  };

  // Resume auto-scroll after user interaction
  const handleScrollEndDrag = () => {
    const totalData = getCurrentData();
    if (totalData.length > 1) {
      setTimeout(() => {
        autoScrollInterval.current = setInterval(() => {
          setCurrentIndex(prevIndex => {
            const nextIndex = (prevIndex + 1) % totalData.length;
            flatListRef.current?.scrollToIndex({
              index: nextIndex,
              animated: true,
            });
            return nextIndex;
          });
        }, 4000);
      }, 2000);
    }
  };

  // Handle item press
  const handleItemPress = (item: Item) => {
    if (!item.RouteName) return;
    if (company?.Id !== ConfigAPI.ktxCompanyId) {
      navigation.push(RootScreen.ServicesWorkFlow, {
        type: item.RouteName,
        serviceId: item.Id,
      });
    }
  };

  // Render grid page with 2 rows x 4 columns layout
  const renderItemPage = ({item}: {item: Item[]}) => {
    // Split items into 2 rows of 4 columns each
    const firstRow = item.slice(0, COLUMNS_PER_ROW);
    const secondRow = item.slice(COLUMNS_PER_ROW, ITEMS_PER_PAGE);

    const renderRow = (rowItems: Item[]) => (
      <View style={[styles.rowContainer, {gap: gap}]}>
        {Array.from({length: COLUMNS_PER_ROW}, (_, index) => {
          const serviceItem = rowItems[index];
          if (!serviceItem) {
            // Empty placeholder to maintain grid structure
            return <View key={`empty-${index}`} style={{width: itemWidth}} />;
          }

          return (
            <TouchableOpacity
              key={serviceItem.Id}
              style={[styles.itemContainer, {width: itemWidth}, styleItem]}
              onPress={() => handleItemPress(serviceItem)}
              activeOpacity={0.7}>
              <View style={styles.itemContent}>
                <View style={styles.iconContainer}>
                  {serviceItem.Img ? (
                    <Winicon src={`${serviceItem.Img}`} size={36} />
                  ) : (
                    <FastImage
                      source={require('../../../../assets/logo.png')}
                      style={{width: 36, height: 36}}
                      resizeMode={FastImage.resizeMode.contain}
                    />
                  )}
                </View>
                <Text style={styles.itemName} numberOfLines={2}>
                  {serviceItem.Name}
                </Text>
                {serviceItem.badge && (
                  <View style={styles.badgeContainer}>
                    <Text style={styles.badgeText}>{serviceItem.badge}</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );

    return (
      <View style={[styles.pageContainer, {width: width - 32}]}>
        {/* First Row */}
        {renderRow(firstRow)}

        {/* Second Row */}
        {renderRow(secondRow)}
      </View>
    );
  };

  return (
    <Pressable>
      <FlatList
        ref={flatListRef}
        horizontal={true}
        style={style}
        data={getCurrentData()}
        renderItem={renderItemPage}
        keyExtractor={(_, index) => `page-${index}`}
        showsHorizontalScrollIndicator={false}
        pagingEnabled={true}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        onScrollToIndexFailed={info => {
          console.warn('ScrollToIndex failed:', info);
        }}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  title: {
    ...TypoSkin.heading6,
  },
  pageContainer: {
    flex: 1,
    gap: 12,
    paddingVertical: 8,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemContainer: {
    flex: 1,
  },
  itemGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  itemContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
    height: 90,
    gap: 8,
  },
  iconContainer: {
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemIcon: {
    width: 30,
    height: 30,
  },
  itemName: {
    lineHeight: 14,
    fontSize: 10,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    fontWeight: '600',
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#8E24AA',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default ServiceGrid;
