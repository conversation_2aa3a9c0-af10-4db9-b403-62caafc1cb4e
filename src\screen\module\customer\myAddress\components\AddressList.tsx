import React from 'react';
import {StyleSheet, RefreshControl} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {AddressListProps} from '../types';
import {AddressItem} from './AddressItem';
import {EmptyAddressState} from './EmptyAddressState';

/**
 * Component to display list of addresses with refresh functionality
 */
export const AddressList: React.FC<AddressListProps> = ({
  addresses,
  chooseAddress,
  onEdit,
  onDelete,
  onSelect,
  refreshing,
  onRefresh,
}) => {
  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[ColorThemes.light.primary_main_color]}
          tintColor={ColorThemes.light.primary_main_color}
        />
      }
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}>
      {addresses.length > 0 ? (
        addresses.map((item, index) => (
          <AddressItem
            key={item.Id}
            item={item}
            index={index}
            chooseAddress={chooseAddress}
            onEdit={onEdit}
            onDelete={onDelete}
            onSelect={onSelect}
          />
        ))
      ) : (
        <EmptyAddressState />
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    marginHorizontal: 12,
  },
});
