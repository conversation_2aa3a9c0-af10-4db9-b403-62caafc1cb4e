import {DataController} from '../../base-controller';
import {randomGID} from '../../../utils/Utils';
import {TicketType} from '../../../types/ticketType';
import {getImage} from '../../../redux/actions/rootAction';
import {TicketStatus} from '../service/components/da';

/**
 * Interface for ticket filter options
 */
export interface TicketFilter {
  customerId?: string;
  toiletId?: string;
  toiletServicesId?: string;
  status?: TicketStatus;
  type?: number;
  dateFrom?: number;
  dateTo?: number;
  sortBy?: 'DateCreated' | 'Status' | 'Type';
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface for ticket list configuration (aggregateList)
 */
export interface TicketListConfig {
  page?: number;
  size?: number;
  query?: string;
  sortby?: Array<{prop: string; direction?: 'ASC' | 'DESC'}>;
  pattern?: any;
  returns?: Array<string>;
}

/**
 * Interface for ticket pattern list configuration (getPatternList)
 */
export interface TicketPatternConfig {
  page?: number;
  size?: number;
  query?: string;
  sortby?: {BY: string; DIRECTION?: 'ASC' | 'DESC'; ORDER?: string};
  pattern?: any;
  returns?: Array<string>;
}

/**
 * Data Access class for Ticket module
 * Provides CRUD operations and specialized queries for ticket management
 */
export class TicketDa {
  private controller: DataController;
  private customerController: DataController;
  private toiletController: DataController;
  private toiletServicesController: DataController;

  constructor() {
    this.controller = new DataController('Ticket');
    this.customerController = new DataController('Customer');
    this.toiletController = new DataController('Toilet');
    this.toiletServicesController = new DataController('ToiletServices');
  }

  /**
   * Create a new ticket
   * @param ticketData - Ticket data to create
   * @returns Promise<TicketType | null>
   */
  async create(ticketData: Partial<TicketType>): Promise<TicketType | null> {
    try {
      const newTicket: TicketType = {
        Id: randomGID(),
        DateCreated: Date.now(),
        Status: TicketStatus.init,
        Sort: 0,
        Ktx: false,
        ...ticketData,
      } as TicketType;

      const response = await this.controller.add([newTicket]);
      if (response?.code === 200) {
        return newTicket;
      }
      return null;
    } catch (error) {
      console.error('Error creating ticket:', error);
      return null;
    }
  }

  /**
   * Update an existing ticket
   * @param ticketData - Ticket data to update (must include Id)
   * @returns Promise<boolean>
   */
  async update(
    ticketData: Partial<TicketType> & {Id: string},
  ): Promise<boolean> {
    try {
      const response = await this.controller.edit([ticketData]);
      return response?.code === 200;
    } catch (error) {
      console.error('Error updating ticket:', error);
      return false;
    }
  }

  /**
   * Delete tickets by IDs
   * @param ids - Array of ticket IDs to delete
   * @returns Promise<boolean>
   */
  async delete(ids: Array<string>): Promise<boolean> {
    try {
      const response = await this.controller.delete(ids);
      return response?.code === 200;
    } catch (error) {
      console.error('Error deleting tickets:', error);
      return false;
    }
  }

  /**
   * Get ticket by ID with related data
   * @param id - Ticket ID
   * @returns Promise<TicketType | null>
   */
  async getById(id: string): Promise<TicketType | null> {
    try {
      if (!id || typeof id !== 'string') {
        console.error('Invalid ID provided to getById:', id);
        return null;
      }
      const response = await this.controller.getById(id);
      if (response?.code !== 200 || !response?.data) {
        console.error('Failed to fetch ticket data:', response);
        return null;
      }
      const ticket = response.data;
      // Fetch related data
      await this.enrichTicketWithRelatedData([ticket]);
      return ticket;
    } catch (error) {
      console.error('Error fetching ticket by ID:', error);
      return null;
    }
  }

  /**
   * Get tickets with pagination and filtering
   * @param config - List configuration
   * @returns Promise<TicketType[]>
   */
  async getList(config: TicketListConfig = {}): Promise<TicketType[]> {
    try {
      const defaultConfig = {
        page: 1,
        size: 10,
        ...config,
      };

      const response = await this.controller.aggregateList(defaultConfig);
      if (response?.code === 200) {
        const tickets = response.data || [];
        await this.enrichTicketWithRelatedData(tickets);
        return tickets;
      }
      return [];
    } catch (error) {
      console.error('Error fetching ticket list:', error);
      return [];
    }
  }

  /**
   * Get tickets with pattern-based query and related data
   * @param config - Pattern list configuration
   * @returns Promise<TicketType[]>
   */
  async getPatternList(
    config: TicketPatternConfig = {},
  ): Promise<TicketType[]> {
    try {
      const defaultConfig = {
        page: 1,
        size: 10,
        pattern: {
          CustomerId: ['Id', 'Name', 'Img'],
          ToiletId: ['Id', 'Name'],
          ToiletServicesId: ['Id', 'Name'],
        },
        ...config,
      };

      const response = await this.controller.getPatternList(defaultConfig);
      if (response?.code === 200) {
        const tickets = response.data || [];

        // Map related data from pattern response
        tickets.forEach((ticket: any) => {
          if (response.Customer) {
            ticket.Customer = response.Customer.find(
              (customer: any) => customer.Id === ticket.CustomerId,
            );
          }
          if (response.Toilet) {
            ticket.Toilet = response.Toilet.find(
              (toilet: any) => toilet.Id === ticket.ToiletId,
            );
          }
          if (response.ToiletServices) {
            ticket.ToiletServices = response.ToiletServices.find(
              (service: any) => service.Id === ticket.ToiletServicesId,
            );
          }
        });

        // Process images
        const ticketsWithImages = await getImage({items: tickets});
        return ticketsWithImages;
      }
      return [];
    } catch (error) {
      console.error('Error fetching pattern list:', error);
      return [];
    }
  }

  /**
   * Get tickets filtered by various criteria
   * @param filter - Filter criteria
   * @returns Promise<TicketType[]>
   */
  async getFilteredTickets(filter: TicketFilter): Promise<TicketType[]> {
    try {
      const queryParts: string[] = [];

      // Build query based on filter criteria
      if (filter.customerId) {
        queryParts.push(`@CustomerId:{${filter.customerId}}`);
      }
      if (filter.toiletId) {
        queryParts.push(`@ToiletId:{${filter.toiletId}}`);
      }
      if (filter.toiletServicesId) {
        queryParts.push(`@ToiletServicesId:{${filter.toiletServicesId}}`);
      }
      if (filter.status !== undefined) {
        queryParts.push(`@Status:{${filter.status}}`);
      }
      if (filter.type !== undefined) {
        queryParts.push(`@Type:{${filter.type}}`);
      }
      if (filter.dateFrom && filter.dateTo) {
        queryParts.push(`@DateCreated:[${filter.dateFrom} ${filter.dateTo}]`);
      } else if (filter.dateFrom) {
        queryParts.push(`@DateCreated:[${filter.dateFrom} +inf]`);
      } else if (filter.dateTo) {
        queryParts.push(`@DateCreated:[-inf ${filter.dateTo}]`);
      }

      const query = queryParts.length > 0 ? queryParts.join(' ') : '*';

      // Build sort configuration
      const sortby = filter.sortBy
        ? {
            BY: filter.sortBy,
            DIRECTION: filter.sortDirection || 'DESC',
          }
        : {BY: 'DateCreated', DIRECTION: 'DESC' as const};

      const config: TicketPatternConfig = {
        query,
        sortby,
        pattern: {
          CustomerId: ['Id', 'Name', 'Img'],
          ToiletId: ['Id', 'Name'],
          ToiletServicesId: ['Id', 'Name'],
        },
      };

      return this.getPatternList(config);
    } catch (error) {
      console.error('Error fetching filtered tickets:', error);
      return [];
    }
  }

  /**
   * Get tickets by toilet ID
   * @param toiletId - Toilet ID
   * @returns Promise<TicketType[]>
   */
  async getTicketsByToiletId(toiletId: string): Promise<TicketType[]> {
    try {
      const config: TicketPatternConfig = {
        page: 1,
        size: 10000,
        query: `@ToiletId:{${toiletId}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'Img'],
          ToiletId: ['Id', 'Name'],
          ToiletServicesId: ['Id', 'Name'],
        },
      };

      return this.getPatternList(config);
    } catch (error) {
      console.error('Error fetching tickets by toilet ID:', error);
      return [];
    }
  }

  /**
   * Get tickets by customer ID
   * @param customerId - Customer ID
   * @returns Promise<TicketType[]>
   */
  async getTicketsByCustomerId(customerId: string): Promise<TicketType[]> {
    try {
      const config: TicketPatternConfig = {
        page: 1,
        size: 10000,
        query: `@CustomerId:{${customerId}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'Img'],
          ToiletId: ['Id', 'Name'],
          ToiletServicesId: ['Id', 'Name'],
        },
      };

      return this.getPatternList(config);
    } catch (error) {
      console.error('Error fetching tickets by customer ID:', error);
      return [];
    }
  }

  /**
   * Get all tickets by customer through their toilets
   * @param customerId - Customer ID
   * @returns Promise<TicketType[]>
   */
  async getAllTicketsByCustomer(customerId: string): Promise<TicketType[]> {
    try {
      // First get toilets owned by customer
      const toiletResponse = await this.toiletController.getListSimple({
        page: 1,
        size: 10000,
        query: `@CustomerId:{${customerId}}`,
      });

      if (toiletResponse?.code === 200) {
        const toiletIds = toiletResponse.data.map((toilet: any) => toilet.Id);

        if (toiletIds.length > 0) {
          // Get tickets for these toilets
          const ticketResponse = await this.controller.getListSimple({
            page: 1,
            size: 10000,
            query: `@ToiletId:{${toiletIds.join(' | ')}}`,
          });

          if (ticketResponse?.code === 200) {
            return ticketResponse.data || [];
          }
        }
      }

      return [];
    } catch (error) {
      console.error('Error fetching all tickets by customer:', error);
      return [];
    }
  }

  /**
   * Update ticket status
   * @param ticketId - Ticket ID
   * @param status - New status
   * @returns Promise<boolean>
   */
  async updateStatus(ticketId: string, status: TicketStatus): Promise<boolean> {
    try {
      return this.update({Id: ticketId, Status: status});
    } catch (error) {
      console.error('Error updating ticket status:', error);
      return false;
    }
  }

  /**
   * Get tickets count by status
   * @param customerId - Optional customer ID filter
   * @returns Promise<{[status: number]: number}>
   */
  async getTicketCountByStatus(
    customerId?: string,
  ): Promise<{[status: number]: number}> {
    try {
      const query = customerId ? `@CustomerId:{${customerId}}` : '*';

      const response = await this.controller.groupBy({
        searchRaw: query,
        reducers: [
          {
            Name: 'StatusCount',
            Reducer: 'COUNT',
            GroupBy: 'Status',
            Query: query,
          },
        ],
      });

      if (response?.code === 200 && response.data) {
        const result: {[status: number]: number} = {};
        response.data.forEach((item: any) => {
          result[item.Status] = item.StatusCount || 0;
        });
        return result;
      }

      return {};
    } catch (error) {
      console.error('Error getting ticket count by status:', error);
      return {};
    }
  }

  /**
   * Search tickets by text
   * @param searchText - Text to search for
   * @param page - Page number
   * @param size - Page size
   * @returns Promise<TicketType[]>
   */
  async searchTickets(
    searchText: string,
    page: number = 1,
    size: number = 10,
  ): Promise<TicketType[]> {
    try {
      const config: TicketPatternConfig = {
        page,
        size,
        query: `@Name:*${searchText}* | @Description:*${searchText}* | @Detail:*${searchText}*`,
        pattern: {
          CustomerId: ['Id', 'Name', 'Img'],
          ToiletId: ['Id', 'Name'],
          ToiletServicesId: ['Id', 'Name'],
        },
      };

      return this.getPatternList(config);
    } catch (error) {
      console.error('Error searching tickets:', error);
      return [];
    }
  }

  /**
   * Enrich tickets with related data (Customer, Toilet, ToiletServices)
   * @param tickets - Array of tickets to enrich
   * @private
   */
  private async enrichTicketWithRelatedData(
    tickets: TicketType[],
  ): Promise<void> {
    try {
      if (!tickets || tickets.length === 0) return;

      // Get unique IDs for batch fetching
      const customerIds = [
        ...new Set(tickets.map(t => t.CustomerId).filter(Boolean)),
      ];
      const toiletIds = [
        ...new Set(tickets.map(t => t.ToiletId).filter(Boolean)),
      ];
      const serviceIds = [
        ...new Set(tickets.map(t => t.ToiletServicesId).filter(Boolean)),
      ];

      // Fetch related data in parallel
      const [customersRes, toiletsRes, servicesRes] = await Promise.all([
        customerIds.length > 0
          ? this.customerController.getByListId(customerIds)
          : null,
        toiletIds.length > 0
          ? this.toiletController.getByListId(toiletIds)
          : null,
        serviceIds.length > 0
          ? this.toiletServicesController.getByListId(serviceIds)
          : null,
      ]);

      // Create lookup maps
      const customersMap = new Map();
      const toiletsMap = new Map();
      const servicesMap = new Map();

      if (customersRes?.code === 200) {
        customersRes.data.forEach((customer: any) => {
          customersMap.set(customer.Id, customer);
        });
      }

      if (toiletsRes?.code === 200) {
        toiletsRes.data.forEach((toilet: any) => {
          toiletsMap.set(toilet.Id, toilet);
        });
      }

      if (servicesRes?.code === 200) {
        servicesRes.data.forEach((service: any) => {
          servicesMap.set(service.Id, service);
        });
      }

      // Enrich tickets with related data
      tickets.forEach((ticket: any) => {
        if (ticket.CustomerId && customersMap.has(ticket.CustomerId)) {
          ticket.Customer = customersMap.get(ticket.CustomerId);
        }
        if (ticket.ToiletId && toiletsMap.has(ticket.ToiletId)) {
          ticket.Toilet = toiletsMap.get(ticket.ToiletId);
        }
        if (
          ticket.ToiletServicesId &&
          servicesMap.has(ticket.ToiletServicesId)
        ) {
          ticket.ToiletServices = servicesMap.get(ticket.ToiletServicesId);
        }
      });

      // Process images
      await getImage({items: tickets});
    } catch (error) {
      console.error('Error enriching tickets with related data:', error);
    }
  }
}
