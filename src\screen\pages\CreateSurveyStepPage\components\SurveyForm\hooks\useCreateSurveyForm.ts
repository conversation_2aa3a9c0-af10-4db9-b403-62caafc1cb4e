import {useForm} from 'react-hook-form';
import {CreateSurveyStepData} from '../types';
import {SurveyTaskDa} from '../../../../../module/surveyTask/surveyTaskDa';
import {showSnackbar} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import {randomGID} from '../../../../../../utils/Utils';
import {SurveyTaskStep} from '../../..';
import {useNavigation} from '@react-navigation/native';
import {get} from '../../../../../../utils/lodash';

interface UseCreateSurveyFormProps {
  initialData?: Partial<CreateSurveyStepData> | null;
  changeStep: (
    step: SurveyTaskStep,
    customerId?: string,
    surveyTaskId?: string,
    customerPhone?: string,
  ) => void;
  step: SurveyTaskStep;
}

/**
 * Custom hook for managing CreateSurvey form state and validation
 *
 * Features:
 * - Type-safe form management with react-hook-form
 * - Proper default values mapping from initialData
 * - Optimized validation mode (onBlur)
 * - Error handling for form submission
 *
 * @param initialData - Optional initial form data for editing
 * @param onSubmit - Callback function when form is successfully submitted
 * @returns Form control objects and handlers
 */
export const useCreateSurveyForm = ({
  initialData,
  changeStep,
  step,
}: UseCreateSurveyFormProps) => {
  const navigation = useNavigation();
  // Initialize form with proper TypeScript typing
  const surveyTaskDa = new SurveyTaskDa();
  const {
    control,
    handleSubmit,
    formState: {errors, isSubmitting, isDirty, isValid},
    register,
    reset,
    watch,
    setValue,
    getValues,
  } = useForm<CreateSurveyStepData>({
    // Use nullish coalescing for better default value handling
    defaultValues: {
      Id: get(initialData || {}, 'Id', randomGID()),
      DateCreated: get(initialData || {}, 'DateCreated', Date.now()),
      // Personal Information
      Executor: get(initialData || {}, 'Executor', null),
      DateStart: get(initialData || {}, 'DateStart', 0),
      DateEnd: get(initialData || {}, 'DateEnd', 0),
      Description: get(initialData || {}, 'Description', ''),

      // Contact Information
      Customer: get(initialData || {}, 'Customer', null),

      // Toilet Infrastructure Information
      ToiletCount: get(initialData || {}, 'ToiletCount', ''),
      UserCount: get(initialData || {}, 'UserCount', ''),
      SanitaryToiletCount: get(initialData || {}, 'SanitaryToiletCount', ''),
      CleaningProcess: get(initialData || {}, 'CleaningProcess', 0),
      SepticTankLocation: get(initialData || {}, 'SepticTankLocation', ''),
      Capacity: get(initialData || {}, 'Capacity', ''),
      TreatmentTechnology: get(initialData || {}, 'TreatmentTechnology', 0),
      Kitchen: get(initialData || {}, 'Kitchen', 0),
      SeparateDrainage: get(initialData || {}, 'SeparateDrainage', 0),
      PumpingFrequency: get(initialData || {}, 'PumpingFrequency', 0),

      // Energy and Sustainability
      RenewableEnergy: get(initialData || {}, 'RenewableEnergy', 0),
      EnergyDetails: get(initialData || {}, 'EnergyDetails', ''),
      WaterReuse: get(initialData || {}, 'WaterReuse', ''),
      WasteClassification: get(initialData || {}, 'WasteClassification', ''),
    },
    // Use onBlur mode for better performance and UX
    mode: 'onBlur',
    // Enable re-validation on change after first submission attempt
    reValidateMode: 'onChange',
  });

  /**
   * Handle form submission with proper error handling
   * @param data - Validated form data
   */
  const handleFormSubmit = async (data: CreateSurveyStepData) => {
    switch (step) {
      case 'create':
        try {
          if (step === 'create') {
            const dataCreate = {
              ...data,
              Id: randomGID(),
              Status: 1,
              DateCreated: new Date().getTime(),
              CustomerId: data.Customer?.Id,
              Executor: data.Executor?.Id,
            };
            delete dataCreate.Customer;
            await surveyTaskDa.createSurveyTask(dataCreate);
            showSnackbar({
              message: 'Tạo mới thành công',
              status: ComponentStatus.SUCCSESS,
            });
            // Call the provided onSubmit callback
            changeStep('view');
          }
        } catch (error) {
          console.error('Error submitting survey form:', error);
          // In a real app, you might want to show a toast or error message
          // For now, we'll just log the error
        }
        break;
      case 'edit':
        try {
          const dataUpdate = {
            ...data,
            CustomerId: data.Customer?.Id,
            Executor: data.Executor?.Id,
          };
          delete dataUpdate.Customer;
          await surveyTaskDa.updateSurveyTask(dataUpdate);
          showSnackbar({
            message: 'Cập nhật thành công',
            status: ComponentStatus.SUCCSESS,
          });
          changeStep('view');
        } catch (error) {
          console.error('Error updating survey form:', error);
          showSnackbar({
            message: 'Có lỗi xảy ra khi cập nhật',
            status: ComponentStatus.ERROR,
          });
        }
        break;
      case 'view':
        changeStep('editInfo');
        break;
      case 'editInfo':
        try {
          const dataUpdate = {
            ...data,
            CustomerId: data.Customer?.Id,
            Executor: data.Executor?.Id,
          };
          delete dataUpdate.Customer;
          await surveyTaskDa.updateSurveyTask(dataUpdate);
          showSnackbar({
            message: 'Cập nhật thông tin thành công',
            status: ComponentStatus.SUCCSESS,
          });
          changeStep(
            'toiletReview',
            data.Customer?.Id,
            data.Id,
            data.Customer?.Mobile,
          );
        } catch (error) {
          console.error('Error updating survey info:', error);
          showSnackbar({
            message: 'Có lỗi xảy ra khi cập nhật thông tin',
            status: ComponentStatus.ERROR,
          });
        }
        break;
      default:
        break;
    }
  };

  const handleCancel = async () => {
    switch (step) {
      case 'create':
        navigation.goBack();
        break;
      case 'edit':
        changeStep('view');
        break;
      case 'view':
        changeStep('edit');
        break;
      case 'editInfo':
        try {
          const data = getValues();
          const dataUpdate = {
            ...data,
            CustomerId: data.Customer?.Id,
            Executor: data.Executor?.Id,
          };
          delete dataUpdate.Customer;
          await surveyTaskDa.updateSurveyTask(dataUpdate);
          showSnackbar({
            message: 'Cập nhật thông tin thành công',
            status: ComponentStatus.SUCCSESS,
          });
          changeStep('view');
        } catch (error) {
          console.error('Error updating survey info on cancel:', error);
          showSnackbar({
            message: 'Có lỗi xảy ra khi cập nhật thông tin',
            status: ComponentStatus.ERROR,
          });
          // Still change step even if update fails
          changeStep('view');
        }
        break;
      default:
        break;
    }
  };

  // Function to set customer data
  const setCustomer = (customer: any) => {
    setValue('Customer', customer);
  };

  // Return all necessary form controls and utilities
  return {
    // Core form controls
    control,
    errors,
    register,
    handleSubmit: handleSubmit(handleFormSubmit),
    handleCancel,

    // Form state information
    isSubmitting,
    isDirty,
    isValid,

    // Additional utilities for advanced form management
    reset,
    watch,
    setValue,
    getValues,
    setCustomer,
  };
};
