import {createSlice, PayloadAction, Dispatch} from '@reduxjs/toolkit';
import {ComponentStatus} from 'wini-mobile-components';
import {StorageContanst} from '../../../config/Contanst';
import {DataController} from '../../../screen/base-controller';
import {CartItem} from '../../../types/cartTypes';
import {
  saveDataToAsyncStorage,
  getDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {randomGID} from '../../../utils/Utils';
import {store} from '../../store/store';
// Import helper functions
import {
  validateProductStock,
  validateCartQuantity,
  fetchShopData,
  enrichCartItemsWithShopInfo,
  createCartItem,
} from './cartActions/addItemToCart';
import {showSnackbar} from '../../../component/export-component';
interface CartState {
  items: CartItem[];
  loading: boolean;
}

// Khởi tạo state ban đầu
const initialState: CartState = {
  items: [],
  loading: false,
};

// Tạo slice cho giỏ hàng
export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    // Thêm sản phẩm vào giỏ hàng
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const existingItemIndex = state.items.findIndex(
        item => item.ProductId === action.payload.ProductId,
      );

      if (existingItemIndex >= 0) {
        // Nếu sản phẩm đã tồn tại, tăng số lượng
        state.items[existingItemIndex].Quantity += action.payload.Quantity;
      } else {
        // Nếu sản phẩm chưa tồn tại, thêm mới
        state.items.push(action.payload);
      }
    },
    loadCart: (state, action: PayloadAction<CartItem[]>) => {
      state.items = action.payload;
      state.loading = false;
    },

    // Cập nhật số lượng sản phẩm
    updateQuantity: (
      state,
      action: PayloadAction<{id: string; quantity: number}>,
    ) => {
      const {id, quantity} = action.payload;
      const itemIndex = state.items.findIndex(item => item.id === id);

      if (itemIndex >= 0) {
        state.items[itemIndex].Quantity = quantity;
      }
      CartActions.saveCartToStorage(state.items);
    },

    // Xóa sản phẩm khỏi giỏ hàng
    removeFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      CartActions.saveCartToStorage(state.items);
    },

    // Cập nhật trạng thái chọn của sản phẩm
    toggleSelectItem: (state, action: PayloadAction<string>) => {
      const itemIndex = state.items.findIndex(
        item => item.id === action.payload,
      );

      if (itemIndex >= 0) {
        state.items[itemIndex].selected = !state.items[itemIndex].selected;
      }
    },

    // Chọn tất cả sản phẩm
    selectAllItems: (state, action: PayloadAction<boolean>) => {
      state.items = state.items.map(item => ({
        ...item,
        selected: action.payload,
      }));
    },

    // Chọn tất cả sản phẩm của một cửa hàng
    selectStoreItems: (
      state,
      action: PayloadAction<{storeId: string; selected: boolean}>,
    ) => {
      const {storeId, selected} = action.payload;
      state.items = state.items.map(item =>
        item.ShopId === storeId ? {...item, selected} : item,
      );
    },

    // Xóa tất cả sản phẩm đã chọn
    removeSelectedItems: state => {
      state.items = state.items.filter(item => !item.selected);
      CartActions.saveCartToStorage(state.items);
    },

    // Xóa các sản phẩm theo danh sách ID
    removeItemsByIds: (state, action: PayloadAction<string[]>) => {
      const idsToRemove = new Set(action.payload);
      state.items = state.items.filter(item => !idsToRemove.has(item.id));
      CartActions.saveCartToStorage(state.items);
    },

    // Xóa tất cả sản phẩm
    clearCart: state => {
      state.items = [];
      CartActions.saveCartToStorage(state.items);
    },

    // Đặt trạng thái loading
    setLoading: state => {
      state.loading = true;
    },

    // Đặt toàn bộ giỏ hàng
    setCart: (state, action: PayloadAction<CartItem[]>) => {
      state.items = action.payload;
    },
  },
});

// Export các actions
export const {
  addToCart,
  updateQuantity,
  removeFromCart,
  toggleSelectItem,
  selectAllItems,
  selectStoreItems,
  removeSelectedItems,
  removeItemsByIds,
  clearCart,
  setLoading,
  setCart,
  loadCart,
} = cartSlice.actions;

// Export reducer
export default cartSlice.reducer;

// Các thunk actions
export class CartActions {
  // Thêm sản phẩm vào giỏ hàng
  static addItemToCart =
    (product: any, quantity: number = 1) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(setLoading());
        // Kiểm tra tồn kho sản phẩm
        const stockValidation = await validateProductStock(product.Id);
        if (!stockValidation.isValid) return;
        // Lấy state hiện tại của giỏ hàng
        const currentCartItems = store.getState().cart.items;
        // Kiểm tra số lượng có thể thêm vào giỏ hàng
        const canAddToCart = await validateCartQuantity(
          product,
          quantity,
          stockValidation.stockQuantity,
          currentCartItems,
        );
        if (!canAddToCart) return;
        // Lấy Thông tin đối tác
        const shopsData = await fetchShopData(currentCartItems, product.ShopId);
        // Cập nhật Thông tin đối tác cho các sản phẩm trong giỏ hàng
        const enrichedCartItems = await enrichCartItemsWithShopInfo(
          currentCartItems,
          shopsData,
        );
        // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
        const existingItem = enrichedCartItems.find(
          item => item.ProductId === product.Id,
        );
        if (existingItem) {
          // Cập nhật số lượng sản phẩm đã có
          dispatch(
            updateQuantity({
              id: existingItem.id,
              quantity: existingItem.Quantity + quantity,
            }),
          );
        } else {
          // Tạo sản phẩm mới và thêm vào giỏ hàng
          const newCartItem = await createCartItem(
            product,
            quantity,
            shopsData,
          );
          dispatch(addToCart(newCartItem));
        }

        // Lưu giỏ hàng vào AsyncStorage
        CartActions.saveCartToStorage(store.getState().cart.items);
        showSnackbar({
          message: 'Đã thêm sản phẩm vào giỏ hàng',
          status: ComponentStatus.SUCCSESS,
        });
      } catch (error) {
        console.error('Error adding item to cart:', error);
        showSnackbar({
          message: 'Không thể thêm sản phẩm vào giỏ hàng',
          status: ComponentStatus.ERROR,
        });
      }
    };

  // Lưu giỏ hàng vào AsyncStorage
  static saveCartToStorage = async (cartItems: CartItem[]) => {
    try {
      await saveDataToAsyncStorage(
        StorageContanst.CartItems,
        JSON.stringify(cartItems),
      );
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  };

  // Tải giỏ hàng từ AsyncStorage
  static loadCartFromStorage = () => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading());
      const cartItemsJson = await getDataToAsyncStorage(
        StorageContanst.CartItems,
      );
      if (cartItemsJson) {
        const cartItems = JSON.parse(cartItemsJson as string) as CartItem[];
        dispatch(setCart(cartItems));
      } else {
        dispatch(setCart([]));
      }
    } catch (error) {
      console.error('Error loading cart from storage:', error);
      dispatch(setCart([]));
      showSnackbar({
        message: 'Không thể tải giỏ hàng',
        status: ComponentStatus.ERROR,
      });
    }
  };
}
