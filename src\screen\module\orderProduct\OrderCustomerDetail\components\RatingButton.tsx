import React from 'react';
import {AppButton} from 'wini-mobile-components';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {RatingButtonProps} from '../types';

const RatingButton: React.FC<RatingButtonProps> = ({
  item,
  onPress,
  disabled = false,
}) => {
  const handlePress = () => {
    onPress(item);
  };

  return (
    <AppButton
      disabled={disabled}
      title={'Đánh giá'}
      onPress={handlePress}
      containerStyle={{
        marginTop: 16,
        paddingHorizontal: 24,
      }}
      height={26}
      backgroundColor={'#FFC043'}
      borderColor="transparent"
      textStyle={{
        ...TypoSkin.buttonText3,
        fontSize: 12,
        color: '#DA251D',
      }}
    />
  );
};

export default RatingButton;
