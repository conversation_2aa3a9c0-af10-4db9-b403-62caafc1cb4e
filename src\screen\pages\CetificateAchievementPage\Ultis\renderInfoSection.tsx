import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../../svgs/iconSvg';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

export const renderInfoSection = (
  selectedToilet?: any,
  isDropdownOpen?: boolean,
  setIsDropdownOpen?: any,
) => {
  return (
    <TouchableOpacity>
      <View style={styles.infoSection}>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.toiletIcon} size={24} />
          </View>
          <Text style={styles.infoLabel}>Nhà vệ sinh</Text>
          <View style={styles.dropdownWrapper}>
            <TouchableOpacity style={styles.dropdownContainer}>
              <Text style={styles.dropdownText}>{selectedToilet?.Name}</Text>
              <View
                style={[
                  styles.chevronIcon,
                  isDropdownOpen && styles.chevronRotated,
                ]}>
                <AppSvg SvgSrc={iconSvg.dropDown} size={15} />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.electronic} size={24} />
          </View>
          <Text style={styles.infoLabel}>Điện năng tiêu thụ</Text>
          <View
            style={{
              flex: 1,
              marginLeft: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 1,
            }}>
            <View style={[styles.valueContainer, {flex: 0.5}]}>
              <Text style={styles.valueText}>
                {selectedToilet?.ElectricityConsumption}
              </Text>
            </View>
            <Text style={[styles.unitText, {flex: 0.4}]}>kWh</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.water} size={24} />
          </View>
          <Text style={styles.infoLabel}>Nước sử dụng</Text>
          <View
            style={{
              flex: 1,
              marginLeft: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 1,
            }}>
            <View style={[styles.valueContainer, {flex: 0.5}]}>
              <Text style={styles.valueText}>{selectedToilet?.WaterUsage}</Text>
            </View>
            <Text style={[styles.unitText, {flex: 0.4}]}>m3</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.biotics} size={24} />
          </View>
          <Text style={styles.infoLabel}>Chế phẩm sinh học</Text>
          <View
            style={{
              flex: 1,
              marginLeft: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 1,
            }}>
            <View style={[styles.valueContainer, {flex: 0.5}]}>
              <Text style={styles.valueText}>
                {selectedToilet?.Bioproducts}
              </Text>
            </View>
            <Text style={[styles.unitText, {flex: 0.4}]}>ml</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.cleaningTime} size={24} />
          </View>
          <Text style={styles.infoLabel}>Số lần vệ sinh lau dọn</Text>
          <View
            style={{
              flex: 1,
              marginLeft: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 1,
            }}>
            <View style={[styles.valueContainer, {flex: 0.5}]}>
              <Text style={styles.valueText}>
                {selectedToilet?.CountCleaning}
              </Text>
            </View>
            <Text style={[styles.unitText, {flex: 0.4}]}>lần</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.calendar2} size={24} />
          </View>
          <Text style={styles.infoLabel}>Thời hạn thực hiện</Text>
          <View style={[styles.valueContainer, {flex: 0.5}]}>
            <Text style={styles.valueText}>
              {selectedToilet?.DateStart
                ? new Date(selectedToilet.DateStart).toLocaleDateString('en-GB')
                : 'N/A'}
            </Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.calendar2} size={24} />
          </View>
          <Text style={styles.infoLabel}>Ngày thực hiện</Text>
          <View style={[styles.valueContainer, {flex: 0.5}]}>
            <Text style={styles.valueText}>
              {' '}
              {selectedToilet?.DateDeclare
                ? new Date(selectedToilet.DateDeclare).toLocaleDateString(
                    'en-GB',
                  )
                : 'N/A'}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContentContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 50, // Extra padding at bottom for better scrolling
  },
  headerTitle: {
    ...TypoSkin.buttonText2,
    marginBottom: 20,
    lineHeight: 22,
  },
  infoSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoLabel: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dropdownWrapper: {
    flex: 1,
    marginLeft: 8,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 120,
  },
  dropdownText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  chevronIcon: {
    // Animation will be handled by React Native's default behavior
  },
  chevronRotated: {
    transform: [{rotate: '180deg'}],
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 200, // Changed from fixed height to maxHeight
    zIndex: 1001,
  },
  dropdownListContent: {
    paddingVertical: 4,
  },
  flatListStyle: {
    flex: 1,
  },

  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: '#e8e8e8',
    backgroundColor: 'white',
  },
  lastDropdownItem: {
    borderBottomWidth: 0,
  },
  dropdownItemContainer: {
    flex: 1,
    paddingRight: 8,
  },
  dropdownItemCode: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  dropdownItemLocation: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },

  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkMark: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 80,
  },
  valueText: {
    ...TypoSkin.buttonText3,
    color: '#333',
    fontWeight: '500',
  },
  unitText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  sectionTitle: {
    ...TypoSkin.buttonText2,
    marginBottom: 12,
  },
  notesContainer: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  notesText: {
    ...TypoSkin.buttonText3,
    height: 80,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  approvalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
  },
  approvalLabel: {
    ...TypoSkin.buttonText2,
    fontWeight: '600',
  },
  approvalButton: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  approvalButtonText: {
    fontSize: 14,
    color: '#2E7D32',
    fontWeight: '500',
  },
});
