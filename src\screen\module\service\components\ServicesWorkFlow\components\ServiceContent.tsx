import React from 'react';
import {View} from 'react-native';
import DescriptionInforForm from '../../form/DescriptionInforForm';
import WebViewServiceFlow from '../../edu/EduFlow';
import type {ServiceContentProps} from '../types';
import {SERVICE_CONFIGS, EDU_FLOW_URL} from '../constants';

export const ServiceContent: React.FC<ServiceContentProps> = ({
  serviceType,
  serviceId,
  customer,
  selectedToilet,
}) => {
  const config = SERVICE_CONFIGS[serviceType];

  switch (serviceType) {
    case 'create':
      return (
        <DescriptionInforForm
          title={config?.title}
          subtitle={config?.subtitle}
          submitTitle={config?.submitTitle}
          serviceId={serviceId}
          customer={customer}
          placeholder={config?.placeholder}
          thumbUrl={config?.thumbUrl}
          type={config?.type || ''}
        />
      );

    case 'repair':
      return (
        <DescriptionInforForm
          title={config?.title}
          subtitle={config?.subtitle}
          thumbUrl={config?.thumbUrl}
          submitTitle={config?.submitTitle}
          serviceId={serviceId}
          customer={customer}
          placeholder={config?.placeholder}
          toiletItem={selectedToilet}
          type={config?.type || ''}
        />
      );

    case 'upgrade':
      return (
        <DescriptionInforForm
          title={config?.title}
          subtitle={config?.subtitle}
          thumbUrl={config?.thumbUrl}
          submitTitle={config?.submitTitle}
          serviceId={serviceId}
          customer={customer}
          placeholder={config?.placeholder}
          type={config?.type || ''}
          toiletItem={selectedToilet}
          upgrade
        />
      );

    case 'clean':
      return (
        <DescriptionInforForm
          title={config?.title}
          thumbUrl={config?.thumbUrl}
          subtitle={config?.subtitle}
          submitTitle={config?.submitTitle}
          serviceId={serviceId}
          customer={customer}
          placeholder={config?.placeholder}
          toiletItem={selectedToilet}
          type={config?.type || ''}
        />
      );

    case 'edu':
      return (
        <WebViewServiceFlow type={config?.type || ''} url={EDU_FLOW_URL} />
      );

    case 'contact':
      return (
        <DescriptionInforForm
          title={config?.title}
          subtitle={config?.subtitle}
          submitTitle={config?.submitTitle}
          placeholder={config?.placeholder}
          thumbUrl={config?.thumbUrl}
          type={config?.type || ''}
        />
      );

    default:
      return <View />;
  }
};
