import { useNavigation, useRoute } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { SafeAreaView, Text, TouchableOpacity, View } from 'react-native';
import SelectFeatureStep from '../form/SelectFeatureStep';
import InputContactStep from '../form/InputContactStep';
import CheckOptStep from '../form/CheckOptStep';
import WScreenFooter from '../../../../layout/footer';
import AppButton from '../../../../../component/button';
import { ColorThemes } from '../../../../../assets/skin/colors';
import ScreenHeader from '../../../../layout/header';
import { showSnackbar, Winicon } from '../../../../../component/export-component';
import { TypoSkin } from '../../../../../assets/skin/typography';
import { DataController } from '../../../../base-controller';
import { randomGID } from '../../../../../utils/Utils';
import {
  CustomerStatus,
  CustomerType,
} from '../../../../../redux/reducers/user/da';
import {
  CateServicesType,
  handleToiletDistribute,
  randomToiletServiceName,
  ToiletCertificateStatus,
  ToiletServiceStatus,
  ToiletStatus,
  ToiletType,
} from '../da';
import { ComponentStatus } from '../../../../../component/component-status';
import { signInWithPhoneFB } from '../../../../../features/otp-loginwFirebase/PhoneSignIn';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import FLoading from '../../../../../component/Loading/FLoading';
import { RoleDa } from 'screen/module/role/roleDa';

export default function CleanFlow() {
  const navigation = useNavigation();
  const route = useRoute();
  const methods = useForm({
    shouldFocusError: false,
    defaultValues: { devices: [] },
  });
  const user = useSelectorCustomerState().data;
  const [step, setStep] = useState(0);
  const { data } = useSelectorCustomerState();
  const owner = useSelectorCustomerCompanyState().owner;
  const [loading, setLoading] = useState(false);
  const [done, setDone] = useState(false);

  useEffect(() => {
    if (route.params) {
      setStep(route.params.step);
      methods.setValue('description', route.params.description);
      methods.setValue('serviceData', route.params.serviceData);
      methods.setValue('guest', route.params?.customer);
    }
  }, []);

  useEffect(() => {
    if (route.params?.toiletItem) {
      methods.setValue('Name', route.params?.toiletItem?.Name);
      methods.setValue('Mobile', route.params?.toiletItem?.Mobile);
      methods.setValue('Address', route.params?.toiletItem?.Address);
      methods.setValue('Lat', route.params?.toiletItem?.Lat);
      methods.setValue('Long', route.params?.toiletItem?.Long);
    }
  }, [route.params]);

  const onSubmitInformation = async () => {
    debugger
    try {
      const customerController = new DataController('Customer');
      const toiletController = new DataController('Toilet');
      const toiletServicesController = new DataController('ToiletServices');

      var findCustomerByPhone = undefined;

      let _mobile = methods.watch('Mobile').replace('+84', '0');
      if (_mobile.startsWith('84')) _mobile = _mobile.replace('84', '0');
      if (data?.CateServicesId?.includes(CateServicesType.clean)) {
        var findCustomerByPhone = { ...data };
      } else if (owner?.CateServicesId?.includes(CateServicesType.clean)) {
        findCustomerByPhone = { ...owner };
      } else if (!data) {
        findCustomerByPhone = await customerController.getListSimple({
          page: 1,
          size: 1,
          query: `@Type:[${CustomerType.partner} ${CustomerType.partner}] @CateServicesId:{${CateServicesType.clean}} @Mobile:(${_mobile})`,
        });
        if (findCustomerByPhone.code === 200 && findCustomerByPhone.data.length)
          findCustomerByPhone = findCustomerByPhone.data[0];
      }

      const newCustomer = methods.watch('guest') ??
      {
        ...data,
        Address: methods.watch('Address'),
        Lat: methods.watch('Lat'),
        Long: methods.watch('Long'),
      } ??
        findCustomerByPhone ?? {
        Id: randomGID(),
        Name: methods.watch('Name'),
        DateCreated: Date.now(),
        Mobile: _mobile,
        Address: methods.watch('Address'),
        Lat: methods.watch('Lat'),
        Long: methods.watch('Long'),
        Status: CustomerStatus.active,
        Type: CustomerType.guest,
      };

      const newToilet = route.params?.toiletItem ?? {
        Id: randomGID(),
        Name: 'Nhà vệ sinh ở ' + methods.watch('Address'),
        DateCreated: Date.now(),
        CustomerId: newCustomer.Id,
        Status: ToiletStatus.register,
        Type: methods.getValues('Type')
          ? parseInt(methods.getValues('Type'))
          : null,
        Place: methods.getValues('Place')
          ? parseInt(methods.getValues('Place'))
          : null,
        Address: methods.watch('Address'),
        CateServicesId: CateServicesType.clean,
        Lat: methods.watch('Lat'),
        Long: methods.watch('Long'),
        CateServicesId: CateServicesType.clean,
        Description: methods.watch('description'),
        Mobile: _mobile,
        Certificate: ToiletCertificateStatus.clean,
      };

      const randomName = await randomToiletServiceName('VSLD');
      let toiletServices = {
        Id: randomGID(),
        Name: randomName,
        DateCreated: Date.now(),
        Description: methods.watch('description'),
        Sort: 1,
        Status: ToiletServiceStatus.register,
        CateServicesId: CateServicesType.clean,
        ToiletId: newToilet.Id,
        CustomerId: findCustomerByPhone?.Id,
        CustomerMobile: _mobile,
      };

      methods.setValue('toiletServiceId', toiletServices.Id);

      if (!toiletServices.CustomerId) {
        const closestConsultant = await handleToiletDistribute({
          lat: newToilet.Lat,
          long: newToilet.Long,
          cateIds: [CateServicesType.clean],
        });
        toiletServices.CustomerId = closestConsultant?.Id;
        // get company
        const roleDa = new RoleDa()
        const customerRole = await roleDa.getCustomerRole(closestConsultant?.Id)
        if (customerRole?.company) {
          toiletServices.CompanyProfileId = customerRole?.company?.Id
        }
      }

      if (
        newCustomer.Id !== user?.Id &&
        !findCustomerByPhone &&
        !methods.watch('guest')
      ) {
        const customerRes = await customerController.add([newCustomer]);
        if (customerRes.code !== 200) {
          setDone(false);
          return showSnackbar({
            message: customerRes.message,
            type: ComponentStatus.ERROR,
          });
        }
      }

      if (!route.params?.toiletItem) {
        const toiletRes = await toiletController.add([newToilet]);
        if (toiletRes.code !== 200) {
          setDone(false);
          return showSnackbar({
            message: toiletRes.message,
            type: ComponentStatus.ERROR,
          });
        }
      }

      const toiletServicesRes = await toiletServicesController.add([
        toiletServices,
      ]);
      if (toiletServicesRes.code !== 200) {
        setDone(false);
        return showSnackbar({
          message: toiletServicesRes.message,
          type: ComponentStatus.ERROR,
        });
      }
      setDone(true);
    } catch (error) {
      setLoading(false);
      setDone(false);
      showSnackbar({ message: 'Đã có lỗi xảy ra', type: ComponentStatus.ERROR });
    } finally {
      setLoading(false);
    }
  };

  const checkValidNextStep = () => {
    if (route.params?.toiletItem) {
      return true;
    }
    switch (step) {
      case 0:
        return methods.watch('Type') && methods.watch('Place');
      case 1:
        return (
          methods.watch('Name')?.length > 0 &&
          methods.watch('Mobile')?.length > 0 &&
          !methods.formState.errors.Mobile?.message &&
          methods.watch('Address')?.length > 0
        );
      default:
        return false;
    }
  };

  const onSendOtp = async () => {
    if (step === 1) setStep(step + 1);
    return;
    // setLoading(true)
    // var rs = await signInWithPhoneFB(methods.watch("Mobile"))
    // if (rs) {
    //     // done
    //     methods.setValue("confirmationResult", rs)
    //     //done
    //     if (step === 1) setStep(step + 1)
    //     showSnackbar({
    //         message: 'Đã gửi mã xác thực đến số diện thoại',
    //         status: ComponentStatus.SUCCSESS,
    //     });
    // } else {
    //     // fail
    //     showSnackbar({ message: "Đã có lỗi xảy ra", status: ComponentStatus.ERROR })
    // }
    // setLoading(false)
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading visible={loading} avt="" />
      <ScreenHeader
        style={{
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        children={
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
              <TouchableOpacity
                style={{
                  padding: 12,
                  gap: 8,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onPress={() => {
                  navigation.pop();
                }}>
                <Winicon
                  src="outline/arrows/left-arrow"
                  color={ColorThemes.light.neutral_text_subtitle_color}
                  size={20}
                />
                <Text
                  style={[
                    TypoSkin.heading8,
                    { color: ColorThemes.light.neutral_text_title_color },
                  ]}>
                  Thoát
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        }
      />
      <View style={{ flex: 1 }}>
        <SelectFeatureStep
          methods={methods}
          display={step === 0 && !route.params?.toiletItem ? 'flex' : 'none'}
        />
        {(step === 1 && !route.params?.toiletItem) ||
          (step === 0 && route.params?.toiletItem) ? (
          <InputContactStep
            methods={methods}
            display={
              (step === 1 && !route.params?.toiletItem) ||
                (step === 0 && route.params?.toiletItem)
                ? 'flex'
                : 'none'
            }
            editable={route.params?.toiletItem ? false : true}
          />
        ) : (
          <View />
        )}
        {(step === 2 && !route.params?.toiletItem) ||
          (step === 1 && route.params?.toiletItem) ? (
          <CheckOptStep
            methods={methods}
            setLoading={setLoading}
            display={
              (step === 2 && !route.params?.toiletItem) ||
                (step === 1 && route.params?.toiletItem)
                ? 'flex'
                : 'none'
            }
            onSendOtp={onSendOtp}
            onSubmitResult={onSubmitInformation}
          />
        ) : (
          <View />
        )}
      </View>
      {done ? null : step < 3 ? (
        <WScreenFooter
          style={{ justifyContent: 'space-between', paddingVertical: 0 }}>
          <View
            style={{ flexDirection: 'row', height: 6, width: '100%', gap: 4 }}>
            {Array.from({ length: route.params?.toiletItem ? 2 : 3 }).map(
              (_, i) => (
                <View
                  key={`step${i}`}
                  style={{
                    flex: 1,
                    height: '100%',
                    width: '100%',
                    backgroundColor:
                      i === step
                        ? ColorThemes.light
                          .neutral_absolute_reverse_background_color
                        : ColorThemes.light.neutral_main_background_color,
                  }}
                />
              ),
            )}
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingHorizontal: 16,
              paddingTop: 12,
            }}>
            {step !== 0 ? (
              <AppButton
                title={'Quay lại'}
                backgroundColor={
                  ColorThemes.light.neutral_main_background_color
                }
                borderColor="transparent"
                containerStyle={{
                  height: 40,
                  borderRadius: 8,
                  alignSelf: 'baseline',
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                onPress={() => {
                  if (step) setStep(step - 1);
                }}
                textColor={ColorThemes.light.neutral_text_subtitle_color}
              />
            ) : (
              <View />
            )}
            <AppButton
              title={step === 1 ? 'Gửi thông tin' : 'Tiếp'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={{
                height: 40,
                borderRadius: 8,
                alignSelf: 'baseline',
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              disabled={!checkValidNextStep()}
              onPress={() => {
                if (step === 1) onSendOtp();
                else {
                  setStep(step + 1);
                }
              }}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          </View>
        </WScreenFooter>
      ) : null}
    </SafeAreaView>
  );
}
