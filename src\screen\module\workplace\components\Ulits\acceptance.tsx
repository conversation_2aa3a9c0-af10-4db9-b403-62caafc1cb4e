import {FlatList, View, Text, ActivityIndicator} from 'react-native';
import {useEffect, useState, useMemo, useCallback} from 'react';
import {showSnackbar} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import ToiletSelectionCard from '../../../toilet/components/card/ToiletSelectionCard';
import SelectToiletCriterionBottomSheet from '../form/RegisterTab/components/SurveyInfo/components/ListToiletCriterion/components/SelectToiletCriterionBottomSheet';
import ListToiletCriterionDA from '../form/RegisterTab/components/SurveyInfo/components/ListToiletCriterion/da';
import ToiletSelectionCriterionsCard from '../../../toilet/components/card/ToiletSelectionCriterionsCard';
import SelectToiletCriterionAcceptanceBottomSheet from '../form/RegisterTab/components/SurveyInfo/components/ListToiletCriterion/components/SelectToiletCriterionAcceptanceBottomSheet';
import EmptyPage from '../../../../../project-component/empty-page';
import {useRoute} from '@react-navigation/native';
import {DataController} from 'screen/base-controller';
import ConfigAPI from 'config/configApi';
import {useSelectorCustomerState} from 'redux/hooks/hooks';

const ListToiletCriterion = ({
  toiletServiceId,
  toiletServiceStatus,
  ToiletId,
  setCountAcceptance,
  toiletCriterionsAcceptance,
  settoiletCriterionsAcceptance,
  serviceData,
}: {
  toiletServiceId: string;
  toiletServiceStatus: number;
  ToiletId: string;
  setCountAcceptance: (value: number) => void;
  toiletCriterionsAcceptance: any[];
  settoiletCriterionsAcceptance: (value: any[]) => void;
  serviceData?: any;
}) => {
  // Memoize DA instance để tránh tạo mới mỗi lần render
  const listToiletCriterionData = useMemo(
    () => new ListToiletCriterionDA(),
    [],
  );
  const [isBsSelectCriterion, setIsBsSelectCriterion] = useState(false);
  const [selectedToilet, setSelectedToilet] = useState<any>(null);
  const [toiletSelected, setToiletSelected] = useState<any[]>([]);
  const [toiletCriterions, setToiletCriterions] = useState<any[]>([]);
  const [criterions, setCriterions] = useState<any[]>([]);
  const [cateCriterions, setCateCriterions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [customerCompany, setCustomerCompany] = useState<string[]>([]);
  const route = useRoute<any>();
  const customer = useSelectorCustomerState().data;

  const handleGetInforCustomer = async () => {
    const customerController = new DataController('Customer');
    customerController.getById(serviceData.CustomerId).then(res => {
      if (res.code === 200) {
        setCustomerCompany([res.data.CompanyProfileId, ConfigAPI.ktxCompanyId]);
      }
    });
  };
  useEffect(() => {
    handleGetInforCustomer();
  }, [serviceData]);

  const findToiletCriterionByToiletId = useCallback(
    (toiletId: string) => {
      // Chuyển toiletId thành array, split bằng dấu phẩy và trim whitespace
      const toiletIdArray = toiletId.split(',').map(id => id.trim());

      return toiletCriterions.find(
        (criterion: any) =>
          toiletIdArray.includes(criterion.ToiletId) && criterion.Type === 2,
      );
    },
    [toiletCriterions],
  );

  const refreshToiletCriterions = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsLoading(false);
    } catch (error) {
      showSnackbar({
        message: 'Lỗi khi làm mới dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  }, [listToiletCriterionData, toiletServiceId]);

  const handleToiletCardPress = useCallback((item: any) => {
    setSelectedToilet(item);
    setIsBsSelectCriterion(true);
  }, []);

  // Hàm đánh dấu toilet đã được cập nhật
  const markToiletAsUpdated = useCallback((toiletIds: string[]) => {
    setToiletSelected(prevToilets =>
      prevToilets.map(toilet =>
        toiletIds.includes(toilet.Id) ? {...toilet, isUpdated: true} : toilet,
      ),
    );
  }, []);
  const initData = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsError(false);

      const result = await listToiletCriterionData.getToiletCriterion(
        toiletServiceId,
        2,
      );
      if (result) {
        const {toilets, cateCriterions, criterions, toiletCriterions} = result;
        setCateCriterions(cateCriterions || []);
        setCriterions(criterions || []);
        setToiletCriterions(toiletCriterions || []);
        setCountAcceptance(toiletCriterions.length);
        // Set toiletSelected to toilets for display
        settoiletCriterionsAcceptance(toiletCriterions);
        setToiletSelected(toilets || []);
      } else {
        setIsError(true);
        showSnackbar({
          message: 'Không thể lấy dữ liệu toilet criterion',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error in initData:', error);
      setIsError(true);
      showSnackbar({
        message: 'Lỗi khi lấy dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  }, [listToiletCriterionData, toiletServiceId]);

  useEffect(() => {
    if (toiletServiceId) {
      initData();
    }
  }, [toiletServiceId]);

  // Loading state
  if (isLoading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" />
        <Text style={{marginTop: 10}}>Đang tải dữ liệu...</Text>
      </View>
    );
  }

  // Error state
  if (isError) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text style={{color: 'red', textAlign: 'center'}}>
          Có lỗi xảy ra khi tải dữ liệu
        </Text>
      </View>
    );
  }

  return (
    <View style={{flex: 1, paddingHorizontal: 16}}>
      {toiletCriterionsAcceptance.length > 0 ? (
        <FlatList
          data={toiletCriterionsAcceptance}
          scrollEnabled={false}
          renderItem={({item}) => (
            <ToiletSelectionCriterionsCard
              item={item}
              disabled={[1].includes(item?.Status)}
              showSelect={false}
              onPress={() => handleToiletCardPress(item)}
            />
          )}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          style={{marginBottom: 16}}
        />
      ) : (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingVertical: 24,
          }}>
          <EmptyPage />
        </View>
      )}
      <View style={{height: 100}}></View>
      <SelectToiletCriterionAcceptanceBottomSheet
        visible={isBsSelectCriterion}
        disabled={
          customer?.CompanyProfileId &&
          !customerCompany.includes(customer?.CompanyProfileId || '')
            ? true
            : false ||
              [1, 3].includes(toiletServiceStatus) ||
              selectedToilet?.Status === 3
        }
        onClose={() => setIsBsSelectCriterion(false)}
        cateCriterionData={
          cateCriterions.map((cateCriterion: any) => ({
            ...cateCriterion,
            Criterions: criterions.filter(
              (criterion: any) =>
                criterion.CateCriterionId === cateCriterion.Id,
            ),
          })) || []
        }
        toiletId={selectedToilet?.Id || ''}
        toiletName={selectedToilet?.Name || ''}
        toiletCriterion={
          selectedToilet
            ? findToiletCriterionByToiletId(selectedToilet.Id)
            : null
        }
        cateCriterion={cateCriterions?.[0]?.Id}
        selectedToilet={selectedToilet}
        cateCriterionId={cateCriterions?.[0]?.Id}
        onConfirmSuccess={async () => {
          // Refresh toilet criterions data after successful save
          await refreshToiletCriterions();
        }}
        markToiletAsUpdated={markToiletAsUpdated}
        toiletServiceId={toiletServiceId}
      />
    </View>
  );
};

export default ListToiletCriterion;
