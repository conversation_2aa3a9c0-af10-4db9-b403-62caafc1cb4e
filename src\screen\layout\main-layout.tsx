import {StyleSheet, View} from 'react-native';
import {Text} from 'react-native-paper';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorSkin, ColorThemes} from '../../assets/skin/colors';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {FDialog, showDialog, Winicon} from '../../component/export-component';
import {
  useSelectorCustomerState,
  useSelectorNotificationState,
} from '../../redux/hooks/hooks';
import {useRef} from 'react';
import {ComponentStatus} from '../../component/component-status';
import {useNavigation, useRoute} from '@react-navigation/native';
import {navigateReset, RootScreen} from '../../router/router';
import TabsWorkIndex from '../module/workplace/TabsWorkIndex';
import ProductIndex from '../module/product/ProductIndex';
import ProfilePage from '../pages/ProfilePage/ProfilePage';
import HomePage from '../pages/HomePage/HomePage';
import ToiletList from '../module/toilet/view/ToiletsList';

const Tab = createBottomTabNavigator();

const getTabBarLabelStyle = (color: string, focused: boolean) => ({
  color: color,
  fontWeight: focused ? ('bold' as const) : ('400' as const),
});

const bottomNavigateData = [
  {
    id: 0,
    name: 'Trang chủ',
    component: HomePage,
    activeIcon: 'fill/user interface/home',
    inActiveIcon: 'outline/user interface/home',
    auth: false,
  },
  {
    id: 1,
    name: 'Tác vụ',
    component: TabsWorkIndex,
    activeIcon: 'fill/development/todo',
    inActiveIcon: 'outline/development/todo',
    auth: true,
  },
  {
    id: 2,
    name: 'Mua sắm',
    component: ProductIndex,
    activeIcon: 'fill/user interface/shopping-cart-2',
    inActiveIcon: 'outline/user interface/shopping-cart-2',
    auth: false,
  },
  {
    id: 3,
    name: 'Nhà vệ sinh',
    component: ToiletList,
    activeIcon: 'fill/buildings/toilet',
    inActiveIcon: 'outline/buildings/toilet',
    auth: true,
  },
  {
    id: 4,
    name: 'Tôi',
    component: ProfilePage,
    activeIcon: 'fill/users/profile',
    inActiveIcon: 'outline/users/profile',
    auth: true,
  },
];

export const dialogCheckAcc = ({ref}: {ref: any; navigation?: any}) => {
  showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: 'Vui lòng đăng nhập để sử dụng!',
    onSubmit: async () => {
      navigateReset(RootScreen.login);
    },
  });
};

export default function MainLayout() {
  const customer = useSelectorCustomerState();
  const navigation = useNavigation<any>();
  const dialogCheckAccRef = useRef<any>();
  const {badge} = useSelectorNotificationState();
  const route = useRoute<any>();

  return (
    <View style={styles.container}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          tabBarStyle: styles.bar,
          tabBarActiveTintColor: ColorSkin.primary,
          tabBarInactiveTintColor: ColorSkin.textColorGrey1,
          headerShown: false,
        }}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: e => {
                  if (item.auth && !customer.data) {
                    dialogCheckAcc({
                      ref: dialogCheckAccRef,
                      navigation: navigation,
                    });
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={() => {
                return {
                  tabBarLabel: ({color, focused}: any) => (
                    <Text
                      style={[
                        {...TypoSkin.medium11},
                        getTabBarLabelStyle(color, focused),
                      ]}>
                      {item.name}
                    </Text>
                  ),
                  tabBarIcon: ({color, focused}: any) =>
                    index == 2 ? (
                      <View style={styles.specialTabIcon}>
                        <Winicon
                          src={focused ? item.activeIcon : item.inActiveIcon}
                          size={20}
                          color={color}
                        />
                        {badge > 0 && <View style={styles.badgeContainer} />}
                      </View>
                    ) : (
                      <Winicon
                        src={focused ? item.activeIcon : item.inActiveIcon}
                        size={20}
                        color={color}
                      />
                    ),
                };
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bar: {
    backgroundColor: '#ffffff',
    borderStyle: 'solid',
    borderTopColor: '#F5F5F5',
  },
  tabBarButton: {
    // width: 64,
    height: '100%',
    // gap: 4,
    transform: [{translateY: -14}],
    alignItems: 'center',
  },
  specialTabIcon: {
    padding: 4,
    borderRadius: 32,
    backgroundColor: '#fff',
    position: 'relative',
  },
  badgeContainer: {
    position: 'absolute',
    top: 4,
    right: 4,
    padding: 4,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.error_main_color,
  },
});
