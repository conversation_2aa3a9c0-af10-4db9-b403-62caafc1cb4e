# Login System Documentation

## Tổng quan

Hệ thống đăng nhập và đăng ký của ứng dụng MobileKTX được thiết kế để hỗ trợ nhiều phương thức xác thực bao gồm:

- Đ<PERSON><PERSON> nhập bằng mật khẩu
- Đăng nhập bằng OTP (Firebase Phone Auth)
- <PERSON><PERSON><PERSON> thực sinh trắ<PERSON> h<PERSON> (Face ID/Touch ID)

## Cấu trúc thành phần

### Các file chính:

1. **`login.tsx`** - Component chính quản lý chuyển đổi giữa Login và Register
2. **`LoginView.tsx`** - Component xử lý đăng nhập
3. **`RegisterView.tsx`** - Component xử lý đăng ký
4. **`components/input-otp.tsx`** - Component nhập OTP

## Luồng hoạt động

### 1. <PERSON><PERSON><PERSON> hình chính (login.tsx)

```
[Logo KTX]
[Chà<PERSON> mừng text]
├─ isSignUp = false → LoginView
└─ isSignUp = true  → RegisterView
[Footer với nút chuyển đổi Login/Register]
```

**Chức năng:**

- Quản lý state `isSignUp` để chuyển đổi giữa đăng nhập và đăng ký
- Load dữ liệu biometric và mobile từ AsyncStorage
- Khởi tạo services cần thiết

### 2. Luồng đăng nhập (LoginView.tsx)

#### 2.1. Đăng nhập bằng mật khẩu

```
[Nhập số điện thoại] → [Nhập mật khẩu] → [Đăng nhập]
                                      ↓
                              [Kiểm tra thông tin]
                                      ↓
                               [Lưu token + điều hướng]
```

#### 2.2. Đăng nhập bằng OTP

```
[Nhập số điện thoại] → [Nhấn "Đăng nhập với OTP"]
                                      ↓
                              [Gửi OTP qua Firebase]
                                      ↓
                               [Nhập mã OTP 6 số]
                                      ↓
                               [Xác thực + đăng nhập]
```

#### 2.3. Đăng nhập bằng sinh trắc học

```
[Icon sinh trắc học] → [Xác thực Face ID/Touch ID]
                                      ↓
                               [Đăng nhập tự động]
```

**Tính năng đặc biệt:**

- Hỗ trợ "Quên mật khẩu"
- Tối đa 5 lần nhập sai OTP trước khi khóa tài khoản
- "Bỏ qua đăng nhập" cho chế độ guest

### 3. Luồng đăng ký (RegisterView.tsx)

```
[Nhập họ tên] → [Nhập số điện thoại] → [Tạo mật khẩu] → [Nhập lại mật khẩu]
                                                                    ↓
                                                         [Gửi OTP xác thực]
                                                                    ↓
                                                           [Nhập mã OTP 6 số]
                                                                    ↓
                                                        [Tạo tài khoản + đăng nhập]
                                                                    ↓
                                                      [Tuỳ chọn thiết lập sinh trắc học]
```

**Validation:**

- Số điện thoại: Format +84 và kiểm tra hợp lệ
- Mật khẩu: 8-16 ký tự, có chữ hoa, thường và số
- Kiểm tra trùng lặp số điện thoại
- Kiểm tra tài khoản bị khóa

## Xử lý OTP

### Firebase Phone Authentication

- Sử dụng `signInWithPhoneFB()` để gửi OTP
- Sử dụng `confirmCode()` để xác thực OTP
- Timeout: 60 giây cho mỗi request

### Bảo mật OTP

- Tối đa 5 lần nhập sai
- Khóa tài khoản nếu vượt quá giới hạn
- Reset counter khi OTP đúng

## Quản lý Session

### AsyncStorage Keys:

- `accessToken` - JWT token cho API calls
- `refreshToken` - Token để làm mới access token
- `Mobile` - Số điện thoại đã đăng nhập
- `Biometrics` - Cài đặt xác thực sinh trắc học
- `spBiometrics` - Trạng thái hỗ trợ sinh trắc học
- `timeRefresh` - Thời gian hết hạn token

### Token Management:

- Access token có thời hạn 9 phút
- Tự động refresh khi hết hạn
- Xóa toàn bộ session khi logout

## Error Handling

### Các loại lỗi:

1. **Validation Errors**: Hiển thị ngay dưới field
2. **Network Errors**: Snackbar thông báo
3. **Authentication Errors**: Snackbar + clear form
4. **OTP Errors**: Đếm số lần sai + khóa tài khoản

### Error Messages:

- Tiếng Việt, rõ ràng và hướng dẫn user
- Không expose thông tin nhạy cảm
- Phân biệt giữa lỗi client và server

## Security Features

### Phone Number Validation:

```typescript
// Chuẩn hóa số điện thoại
if (!/^(\+84|0)/.test(mobile)) {
  mobile = '0' + mobile;
}
validatePhoneNumber(mobile);
```

### Password Security:

- Regex pattern: `regexPassWord.test(password)`
- Hash password trước khi lưu
- Không lưu plain text password

### Account Locking:

- Khóa sau 5 lần OTP sai
- Cần admin can thiệp để mở khóa
- Kiểm tra trạng thái khóa trước khi cho phép đăng nhập

## Navigation Flow

```
Login Screen
├─ Success → Main App (RootScreen.navigateView)
├─ Forgot Password → ForgotPass Screen
├─ Skip Login → Main App (Guest mode)
└─ Register Success → Biometric Setup Dialog → Main App
```

## Dependencies

### Core Libraries:

- `react-hook-form` - Form management
- `@react-navigation/native` - Navigation
- `react-redux` - State management

### Firebase:

- Phone Authentication
- OTP verification

### Utils:

- `validatePhoneNumber` - Phone validation
- `regexPassWord` - Password validation
- `AsyncStorage` - Local storage
- `DataController` - API interactions

## API Integration

### CustomerActions:

- `login(mobile, password?)` - Đăng nhập
- `lockAccount(mobile)` - Khóa tài khoản
- `hashPassword(password)` - Hash mật khẩu
- `getInfor(dispatch, navigation)` - Lấy thông tin user

### DataController:

- `aggregateList()` - Tìm kiếm user
- `getListSimple()` - Kiểm tra trạng thái tài khoản
- `add()` - Tạo tài khoản mới

## Testing Scenarios

### Happy Path:

1. Đăng nhập thành công bằng password
2. Đăng nhập thành công bằng OTP
3. Đăng ký thành công với biometric
4. Đăng nhập bằng biometric

### Error Cases:

1. Sai password > 3 lần
2. Sai OTP > 5 lần
3. Số điện thoại đã tồn tại
4. Tài khoản bị khóa
5. Lỗi network
6. OTP timeout

## Maintenance Notes

### Code Quality:

- TypeScript interfaces cho props
- Proper error handling
- Loading states cho UX
- Cleanup effects và timeouts

### Performance:

- Debounce validation
- Optimize re-renders với useMemo
- Lazy load heavy components

### Future Enhancements:

- Social login (Google, Facebook, Apple)
- Two-factor authentication
- Password strength meter
- Remember device option
- Biometric prompt customization
