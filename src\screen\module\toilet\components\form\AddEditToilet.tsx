import {forwardRef, useEffect, useRef} from 'react';
import {
  SafeAreaView,
  Dimensions,
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {
  Winicon,
  showDialog,
  FDialog,
  showSnackbar,
} from '../../../../../component/export-component';
import {closePopup} from '../../../../../component/popup/popup';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import {useForm} from 'react-hook-form';
import {randomGID} from '../../../../../utils/Utils';
import {
  PlaceStringData,
  ToiletPlace,
  ToiletStatus,
  TypeStringData,
} from '../../../service/components/da';
import {DataController} from '../../../../base-controller';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  TextFieldForm,
  FAddressPickerForm,
  FRadioForm,
} from '../../../../../project-component/component-form';
import {validatePhoneNumber} from '../../../../../utils/validate';
import CetificateAchievemenDa from 'screen/pages/CetificateAchievementPage/CetificateAchievemenDa';

export const AddEditToiletPopup = forwardRef(function AddEditToiletPopup(
  data: {toiletId: any; onDone?: any; customerId?: string; phone?: string},
  ref: any,
) {
  const {toiletId, onDone, customerId, phone} = data;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      Place: `${ToiletPlace.inDoor}`,
      Status: ToiletStatus.run,
    },
  });
  const toiletController = new DataController('Toilet');
  const user = useSelectorCustomerState().data;
  const dialogRef = useRef<any>();

  const _onSubmit = () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.INFOR,
      title: `Bạn chắc chắn muốn ${toiletId ? 'sửa' : 'thêm'} nhà vệ sinh theo những thông tin đã nhập?`,
      onSubmit: async () => {
        const newToilet = {
          ...methods.getValues(),
          Place: methods.getValues('Place')
            ? parseInt(methods.getValues('Place'))
            : undefined,
          Type: methods.getValues('Type')
            ? parseInt(methods.getValues('Type'))
            : undefined,
          DateCreated: methods.getValues('DateCreated') ?? Date.now(),
          CustomerId: customerId || user?.Id,
          Status: methods.getValues('Status') ?? ToiletStatus.run,
        };
        if (toiletId) {
          const res = await toiletController.add([newToilet]);
          if (res.code === 200) {
            showSnackbar({
              message: 'Chỉnh sửa thông tin nhà vệ sinh thành công',
              status: ComponentStatus.SUCCSESS,
            });

            if (onDone) onDone(newToilet);
            await CetificateAchievemenDa.getLogByToiletId(
              newToilet.Id,
              `Tạo mới nhà vệ sinh ${newToilet.Name} thành công`,
            );
          }
        } else {
          const res = await toiletController.add([newToilet]);
          if (res.code === 200) {
            await CetificateAchievemenDa.getLogByToiletId(
              [newToilet.Id],
              `Tạo mới NVS ${res.data[0].Name} thành công`,
            );
            showSnackbar({
              message: 'Tạo mới nhà vệ sinh thành công',
              status: ComponentStatus.SUCCSESS,
            });
            if (onDone) onDone(newToilet);
          }
        }
      },
    });
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  useEffect(() => {
    if (phone) {
      methods.setValue('Mobile', phone);
    } else if (user) methods.setValue('Mobile', user.Mobile);
  }, [user, phone]);

  useEffect(() => {
    if (toiletId) {
      toiletController.getById(toiletId).then(res => {
        if (res.code === 200) {
          Object.keys(res.data).forEach(prop => {
            if (res.data[prop]) {
              if (prop === 'Place' || prop === 'Type') {
                methods.setValue(prop, `${res.data[prop]}`);
              } else methods.setValue(prop, res.data[prop]);
            }
          });
        }
      });
    }
  }, [toiletId]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 56,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <FDialog ref={dialogRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={toiletId ? 'Sửa thông tin nhà vệ sinh' : `Thông tin nhà vệ sinh`}
        prefix={<View style={{width: 50}} />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        behavior="padding"
        style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <ScrollView style={{}}>
          <View style={{gap: 16, paddingBottom: 160}}>
            <TextFieldForm
              label="Tên nhà vệ sinh"
              required
              placeholder="Nhập tên/mã NVS (VD: NVS nam số 1 tầng 8; NVS phòng ngủ 1 tầng 2...)"
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Name"
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              label="Số điện thoại"
              disabled={user !== undefined}
              errors={methods.formState.errors}
              textFieldStyle={{
                backgroundColor: ColorThemes.light.transparent,
                marginBottom: 6,
              }}
              register={methods.register}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: '100%',
                    paddingHorizontal: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                    borderRadius: 8,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.buttonText3,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    +84
                  </Text>
                  <Winicon src="outline/arrows/down-arrow" size={16} />
                </View>
              }
              type="number-pad"
              onBlur={async (ev: any) => {
                var mobile = ev.trim();
                // Check if the number doesn't already start with 0 or +84
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile; // Add 0 at the beginning
                }
                const val = validatePhoneNumber(mobile);
                if (val) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
              }}
            />
            <View>
              <FAddressPickerForm
                control={methods.control}
                errors={methods.formState.errors}
                name="Address"
                label="Địa chỉ"
                placeholder="Nhập địa chỉ của bạn"
                placeName={''}
                onChange={value => {
                  methods.setValue('Long', value.geometry.location.lng);
                  methods.setValue('Lat', value.geometry.location.lat);
                  methods.setValue('Address', value.formatted_address);
                  return value.formatted_address;
                }}
              />
            </View>
            <View style={{gap: 4}}>
              <Text style={{...TypoSkin.label3, marginBottom: 8}}>
                Loại nhà vệ sinh:
              </Text>
              {TypeStringData.map((item: any, i: number) => {
                return (
                  <FRadioForm
                    key={`type ${item.key}`}
                    value={`${item.key}`}
                    label={item.title}
                    control={methods.control}
                    name={`Type`}
                  />
                );
              })}
              <Text style={{...TypoSkin.label3, marginBottom: 8}}>
                {' '}
                Vị trí:
              </Text>
              {PlaceStringData.map((item: any, i: number) => {
                return (
                  <FRadioForm
                    key={`place ${item.key}`}
                    value={`${item.key}`}
                    label={item.title}
                    control={methods.control}
                    name={`Place`}
                  />
                );
              })}
            </View>
            <TextFieldForm
              control={methods.control}
              name="Description"
              label="Mô tả"
              errors={methods.formState.errors}
              placeholder={'Mô tả ngắn gọn về nhà vệ sinh của bạn...'}
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 100,
                width: '100%',
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              register={methods.register}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={toiletId ? 'Sửa' : 'Thêm'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            methods.handleSubmit(_onSubmit, _onError)();
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
