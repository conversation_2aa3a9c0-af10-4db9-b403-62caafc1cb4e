import ConfigAPI from '../../../../config/configApi';
import {StorageContanst} from '../../../../config/Contanst';
import {store} from '../../../../redux/store/store';
import {getDataToAsyncStorage} from '../../../../utils/AsyncStorage';
import {randomGID} from '../../../../utils/Utils';
import {DataController} from '../../../base-controller';

export class SocialDA {
  private postController: DataController;
  private customerController: DataController;
  private likesController: DataController;
  private commentsController: DataController;
  private bookmarkController: DataController;
  private topicController: DataController;
  private cusId: string | null = null;
  constructor(
    postController = new DataController('Posts'),
    customerController = new DataController('Customer'),
    likesController = new DataController('Likes'),
    commentsController = new DataController('Comments'),
    bookmarkController = new DataController('Post_Bookmark'),
    topicController = new DataController('Topic'),
  ) {
    this.postController = postController;
    this.customerController = customerController;
    this.likesController = likesController;
    this.commentsController = commentsController;
    this.bookmarkController = bookmarkController;
    this.topicController = topicController;
  }
  private async getCustomerId(): Promise<string | null> {
    if (!this.cusId) {
      this.cusId = store.getState().customer.data?.Id ?? '';
    }
    return this.cusId;
  }
  async totalPostInTopic(id: string) {
    const response = await this.postController.getListSimple({
      query: `@TopicId:{${id}}`,
      size: 0,
    });
    if (response.code === 200) {
      return response.totalCount ?? 0;
    }
    return 0;
  }
  async getNewFeed(page: number, size: number) {
    var cusId = await this.getCustomerId();
    if (cusId) {
      const customerrespone = await this.customerController.getById(cusId);
      if (customerrespone?.code === 200) {
        var lstFollower = customerrespone.data.Followers;
        if (lstFollower) {
          lstFollower = lstFollower.split(',');
          //push thêm customerId của ITM để hiển thị thêm các bài post của danh sách follow và ITM
          // lstFollower.push();
          const respone = await this.postController.getListSimple({
            page: page,
            size: size,
            query: `@CustomerId:{${lstFollower.join(' | ')}}`,
            sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
          });
          if (respone.code === 200) {
            return respone;
          }
        } else {
          const respone = await this.postController.getListSimple({
            page: page,
            size: size,
            query: `@CustomerId:{${ConfigAPI.adminKtxId}}`,
            sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
          });
          if (respone?.code === 200) {
            return respone;
          }
        }
      }
    } else {
      const respone = await this.postController.getListSimple({
        page: page,
        size: size,
        query: `@CustomerId:{${ConfigAPI.adminKtxId}}`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });
      if (respone.code === 200) {
        return respone;
      }
    }
    return null;
  }
  async getLikes(id: string) {
    const respone = await this.likesController.getListSimple({
      query: `@PostsId:{${id}}`,
      returns: ['Id', 'CustomerId'],
    });
    if (respone?.code === 200) {
      return respone;
    }
    return null;
  }
  async getIsLikePost(id: string) {
    var cusId = await this.getCustomerId();
    if (cusId) {
      const respone = await this.likesController.getListSimple({
        query: `@PostsId:{${id}} @CustomerId: {${cusId}}`,
        size: 1,
        returns: ['Id'],
      });
      if (respone?.data?.length > 0) {
        return true;
      }
    }
    return false;
  }
  async getIsBookmarkPost(id: string) {
    var cusId = await this.getCustomerId();
    if (cusId) {
      const respone = await this.bookmarkController.getListSimple({
        query: `@PostsId:{${id}} @CustomerId: {${cusId}}`,
        size: 1,
      });
      if (respone?.data?.length > 0) {
        return true;
      }
    }
    return false;
  }
  async getComment(id: string) {
    const respone = await this.commentsController.getPatternList({
      query: `@PostsId:{${id}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (respone?.code === 200) {
      return respone;
    }
    return null;
  }
  async getCountComment(id: string) {
    const respone = await this.commentsController.getListSimple({
      query: `@PostsId:{${id}}`,
      size: 0,
      returns: ['Id'],
    });
    if (respone?.code === 200) {
      return respone?.totalCount ?? 0;
    }
    return null;
  }
  async addComment(postId: string, content: string, commentId?: string) {
    var cusId = await this.getCustomerId();
    const data = {
      Id: randomGID(),
      CustomerId: cusId,
      PostsId: postId,
      Content: content,
      ParentId: commentId,
      DateCreated: new Date().getTime(),
      Likes: 0,
    };
    const respone = await this.commentsController.add([data]);
    if (respone.code === 200) {
      return data;
    }
    return null;
  }
  async addBookmark(id: string) {
    var cusId = await this.getCustomerId();
    const data = {
      Id: randomGID(),
      CustomerId: cusId,
      PostsId: id,
      DateCreated: new Date().getTime(),
    };
    const respone = await this.bookmarkController.add([data]);
    if (respone?.code === 200) {
      return respone;
    }
    return null;
  }
  async getNewsBookmark(page: number, size: number) {
    var cusId = await this.getCustomerId();
    const respone = await this.bookmarkController.getListSimple({
      page: page,
      size: size,
      query: `@CustomerId:{${cusId}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone?.code === 200) {
      return respone;
    }
    return null;
  }
  async getTopicNewsFeed() {
    const respone = await this.topicController.getListSimple({
      query: '@IsShowNewsFeed:{true}',
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      returns: ['Id', 'Name', 'Img'],
    });
    if (respone?.code === 200) {
      return respone;
    }
    return null;
  }
  async getListTopic(id: string) {
    const respone = await this.topicController.getListSimple({
      query: `@Id:{${id.replaceAll(',', '|')}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      returns: ['Id', 'Name'],
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
}
