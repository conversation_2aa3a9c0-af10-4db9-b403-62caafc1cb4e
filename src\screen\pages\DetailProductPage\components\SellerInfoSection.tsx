import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import FastImage from 'react-native-fast-image';
import {SellerInfoSectionProps} from '../types';

const SellerInfoSection: React.FC<SellerInfoSectionProps> = ({
  shop,
  onPress,
}) => {
  if (!shop) return null;

  return (
    <TouchableOpacity onPress={onPress} style={styles.sellerContainer}>
      <FastImage
        source={{
          uri:
            shop?.Img != '' && shop?.Img != null
              ? shop?.Img
              : 'https://placehold.co/48/000000/FFFFFF',
        }}
        style={styles.sellerAvatar}
      />
      <View style={styles.sellerInfo}>
        <Text style={styles.sellerName}>{shop?.Name || '-'}</Text>
        <View style={styles.sellerMeta}>
          <Text style={styles.sellerContact}>
            {shop?.Mobile
              ? shop.Mobile?.replace(
                  /(\d{3})(\d+)(\d{4})/g,
                  (_: any, p1: string, p2: string | any[], p3: string) =>
                    `${p1}${'*'.repeat(p2.length)}${p3}`,
                )
              : '-'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  sellerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sellerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  sellerInfo: {
    marginLeft: 12,
  },
  sellerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  sellerMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  sellerContact: {
    fontSize: 12,
    color: '#666',
  },
});

export default SellerInfoSection;
