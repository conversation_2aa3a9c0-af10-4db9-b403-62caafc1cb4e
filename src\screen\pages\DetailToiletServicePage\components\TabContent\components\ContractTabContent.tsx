import React from 'react';
import {
  ToiletStatus,
  ToiletServiceStatus,
  DeviceBioStatus,
} from '../../../../../module/service/components/da';
import EmptyPage from '../../../../../../project-component/empty-page';
import ContractTab from '../../../../../module/workplace/components/form/ContractTab';
import {DataController} from '../../../../../base-controller';
import {showSnackbar} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import {useTabHandlers} from '../hooks/useTabHandlers';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface ContractTabContentProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  methods: any;
  guest: any;
  onChangeStatus: (status: any, setServicesValue?: any) => void;
}

export default function ContractTabContent({
  workData,
  serviceData,
  setServiceData,
  onRefresh,
  isRefreshing,
  methods,
  guest,
  onChangeStatus,
}: ContractTabContentProps) {
  const {
    createTasksFromContract,
    completeContractTasks,
    createBuildTask,
    createDesignTask,
    updateServicesValue,
  } = useTabHandlers({
    workData,
    serviceData,
    setServiceData,
    methods,
    onChangeStatus,
  });

  const handleSurveyAndNextSteps = async (taskController: any) => {
    if (!serviceData) return;

    const surveyController = new DataController('Survey');
    const surveyData = await surveyController.getListSimple({
      page: 1,
      size: 1,
      query: `@ToiletServicesId:{${serviceData.Id}}`,
    });

    if (
      surveyData.data[0] &&
      surveyData.data[0].PassDesign &&
      serviceData.Status < ToiletServiceStatus.build
    ) {
      await onChangeStatus(ToiletStatus.build, true);
      await createBuildTask(taskController);
    } else if (serviceData.Status < ToiletServiceStatus.design) {
      await onChangeStatus(ToiletStatus.design, true);
      await createDesignTask(taskController);
    } else {
      await updateServicesValue();
    }

    showSnackbar({
      message: 'Thống nhất hợp đồng thành công',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleContractSubmit = async () => {
    if (!workData || !serviceData) {
      console.warn('handleContractSubmit called without required data');
      return;
    }

    const deviceController = new DataController('Device');
    deviceController.edit(
      methods.watch('devices').map((e: any) => ({
        ...e,
        ToiletId: workData[0].Id,
        Status: DeviceBioStatus.active,
      })),
    );

    const bioController = new DataController('BioProduct');
    bioController.edit(
      methods.watch('bioProducts').map((e: any) => ({
        ...e,
        ToiletId: workData[0].Id,
        Status: DeviceBioStatus.active,
      })),
    );

    const materialController = new DataController('MaterialToilet');
    materialController.edit(
      methods
        .watch('materials')
        .map((e: any) => ({...e, ToiletId: workData[0].Id})),
    );

    const taskController = new DataController('Task');
    const _tasks = methods.watch('tasks');

    if (_tasks.length) {
      await createTasksFromContract(_tasks, taskController);
    }

    await completeContractTasks(taskController);
    await handleSurveyAndNextSteps(taskController);
  };

  if (
    serviceData?.Status != null &&
    serviceData.Status < ToiletServiceStatus.contract
  ) {
    return <EmptyPage title={`Đơn hàng chưa thống nhất được báo giá`} />;
  }

  return (
    <ContractTab
      serviceData={serviceData}
      data={workData}
      methods={methods}
      onRefreshing={onRefresh}
      isRefreshing={isRefreshing}
      setServiceData={setServiceData}
      customer={guest}
      onSubmit={handleContractSubmit}
    />
  );
}
