import {DataController} from '../../base-controller';

export class CustomerDa {
  controller: DataController;
  customerCompanyController: DataController;
  customerCompanyRoleController: DataController;

  constructor() {
    this.controller = new DataController('Customer');
    this.customerCompanyController = new DataController('CompanyProfile');
    this.customerCompanyRoleController = new DataController('CustomerCompany');
  }

  async fetchByQuery(config: any) {
    const res = await this.controller.getListSimple(config);
    if (res.code === 200) {
      let data = res.data;
      let customerCompany = data.filter((item: any) => item.Type == '2');
      const companies = await this.customerCompanyController.getByListId(
        customerCompany.map((item: any) => item.CompanyProfileId),
      );
      if (companies.code === 200) {
        const dataCompany = companies.data.filter(Boolean);
        customerCompany = customerCompany.map((item: any) => {
          const company = dataCompany.find(
            (e: any) => e.Id === item.CompanyProfileId,
          );
          return {...item, CompanyProfileId: company};
        });
      }

      data = [
        ...data.filter((item: any) => item.Type === '1'),
        ...customerCompany,
      ];
      return data;
    }
    return [];
  }
  async getAllMemberRole(CompanyProfileId: string) {
    const res = await this.customerCompanyRoleController.getListSimple({
      page: 1,
      size: 1000,
      query: `@CompanyProfileId:{${CompanyProfileId}}`,
    });
    if (res.code === 200) {
      return res;
    }
    return [];
  }

  async getAllCustomerInCompany(CompanyProfileId: string) {
    // Step 1: get all CustomerCompany relations for this company
    const relationsRes = await this.customerCompanyRoleController.getListSimple(
      {
        page: 1,
        size: 1000,
        query: `@CompanyProfileId:{${CompanyProfileId}} @Status:[1]`,
      },
    );
    if (relationsRes?.code !== 200 || !Array.isArray(relationsRes?.data)) {
      return [];
    }

    // Step 2: extract distinct CustomerIds
    const customerIds: string[] = Array.from(
      new Set(
        relationsRes.data
          .map((item: any) => item?.CustomerId as string)
          .filter((id: string | undefined) => !!id),
      ),
    ) as string[];
    if (!customerIds.length) {
      return [];
    }

    // Step 3: fetch customer records by list of ids
    const customersRes = await this.controller.getByListId(customerIds);
    if (customersRes?.code === 200 && Array.isArray(customersRes?.data)) {
      return customersRes.data.filter(Boolean);
    }
    return [];
  }
}
