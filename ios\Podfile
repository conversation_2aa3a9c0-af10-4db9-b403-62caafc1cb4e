# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'rncore' do
  use_frameworks! :linkage => :static
  config = use_native_modules!

  # Thêm vào đầu file, sau platform :ios
  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-maps', :path => rn_maps_path
  pod 'GoogleMaps'

  # React Native Firebase dependencies
  pod 'RNFBApp', path: '../node_modules/@react-native-firebase/app'
  pod 'RNFBMessaging', path: '../node_modules/@react-native-firebase/messaging'
  pod 'RNFBAuth', path: '../node_modules/@react-native-firebase/auth'

  # Align Firebase versions
  pod 'Firebase/CoreOnly', :modular_headers => true
  pod 'Firebase/Messaging', :modular_headers => true
  pod 'Firebase/Auth', :modular_headers => true

  # Enable modular headers for specific pods
  pod 'FirebaseAuth', :modular_headers => true
  pod 'FirebaseAuthInterop', :modular_headers => true
  pod 'FirebaseAppCheckInterop', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'RecaptchaInterop', :modular_headers => true

  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-maps', :path => rn_maps_path

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true

  pod 'Firebase/Analytics'

  pod 'VisionCamera', :path => '../node_modules/react-native-vision-camera'

  pod 'RNScreens', :path => '../node_modules/react-native-screens', :modular_headers => true
  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler', :modular_headers => true

  # Fix for RNReanimated
  pod 'RNReanimated', :path => '../node_modules/react-native-reanimated'

  use_frameworks! :linkage => :static

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'rncoreTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    system("chmod -R u+w Pods/RCT-Folly")
    Dir.glob("Pods/RCT-Folly/folly/Portability.h").each do |file|
      text = File.read(file)
      new_contents = text.gsub('#define FOLLY_HAS_COROUTINES 1', '#define FOLLY_HAS_COROUTINES 0')
      File.open(file, "w") { |file| file.puts new_contents }
    end

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
        config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++20'
        config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
        
        # Fix for RNReanimated
        if target.name == 'RNReanimated'
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          config.build_settings['HEADER_SEARCH_PATHS'] << '"$(PODS_ROOT)/Headers/Public/React-Core"'
        end
      end
    end
  end
end
