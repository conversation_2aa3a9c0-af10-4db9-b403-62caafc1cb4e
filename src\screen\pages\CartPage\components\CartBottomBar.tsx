import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Ultis} from '../../../../utils/Utils';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

interface CartBottomBarProps {
  totalPrice: number;
  hasSelectedItems: boolean;
  onCheckout: () => void;
}

const CartBottomBar: React.FC<CartBottomBarProps> = ({
  totalPrice,
  hasSelectedItems,
  onCheckout,
}) => {
  return (
    <View style={styles.bottomBar}>
      <View style={styles.totalContainer}>
        <Text style={styles.totalLabel}>Tổng cộng:</Text>
        <Text style={styles.totalPrice}>{Ultis.money(totalPrice ?? 0)} đ</Text>
      </View>

      <TouchableOpacity
        style={[
          styles.checkoutButton,
          !hasSelectedItems ? styles.checkoutButtonDisabled : {},
        ]}
        onPress={onCheckout}
        disabled={!hasSelectedItems}>
        <Text style={styles.checkoutButtonText}>Thanh toán</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    marginRight: 8,
    fontWeight: '700',
  },
  totalPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
    fontWeight: '700',
  },
  checkoutButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 24,
  },
  checkoutButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  checkoutButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
});

export default CartBottomBar;
