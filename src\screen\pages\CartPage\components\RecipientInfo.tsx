import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Winicon, ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {TypoSkin} from '../../../../assets/skin/typography';
import {dialogCheckAcc} from '../../../layout/main-layout';
import {RootScreen} from '../../../../router/router';

interface RecipientInfoProps {
  dialogRef: React.RefObject<any>;
  isDone?: boolean;
}

interface AddressData {
  Name: string;
  Mobile: string;
  Email?: string;
  Address?: string;
  IsDefault: boolean;
}

const RecipientInfo: React.FC<RecipientInfoProps> = ({dialogRef, isDone}) => {
  const navigation = useNavigation<any>();
  const customerAddress = useSelectorCustomerState().myAddress;
  const customer = useSelectorCustomerState().data;
  const address: AddressData | undefined = customerAddress?.find(
    (item: any) => item.IsDefault,
  );

  const handleEditAddress = () => {
    if (!customer) {
      dialogCheckAcc({ref: dialogRef});
      showSnackbar({
        message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    navigation.navigate(RootScreen.MyAddress, {chooseAddress: true});
  };

  const renderRecipientDetails = () => {
    if (!address) {
      return (
        <View style={styles.recipientDetails}>
          <Text style={styles.recipientName}>Chưa có điểm giao hàng</Text>
        </View>
      );
    }

    return (
      <View style={styles.recipientDetails}>
        <Text style={styles.recipientName}>
          {`${address.Name} • ${address.Mobile} • ${address.Email ?? ''}`}
        </Text>
        <Text style={styles.recipientAddress}>{address.Address ?? ''}</Text>
      </View>
    );
  };

  const renderEditButton = () => {
    if (isDone) return null;

    return (
      <TouchableOpacity onPress={handleEditAddress} style={styles.editButton}>
        <Winicon src="outline/user interface/edit" size={20} color="#2962FF" />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.recipientInfoContainer}>
      <View style={styles.recipientHeader}>
        <Text style={styles.recipientTitle}>Thông tin Người nhận</Text>
        {renderEditButton()}
      </View>
      {renderRecipientDetails()}
    </View>
  );
};

const styles = StyleSheet.create({
  recipientInfoContainer: {
    backgroundColor: '#E6F7FF',
    borderRadius: 10,
    padding: 16,
    marginTop: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#ccc',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recipientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recipientTitle: {
    ...TypoSkin.title5,
    color: '#000000',
    fontWeight: '700',
  },
  editButton: {
    padding: 4,
  },
  recipientDetails: {
    gap: 4,
  },
  recipientName: {
    color: '#000000',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '400',
  },
  recipientAddress: {
    ...TypoSkin.body3,
    color: '#666666',
  },
});

export default RecipientInfo;
