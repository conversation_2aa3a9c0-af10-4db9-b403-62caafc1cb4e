// import { AppleButton, appleAuth, appleAuthAndroid } from '@invertase/react-native-apple-authentication';
// import React, { useEffect } from 'react';
// import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
// import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
// import { faAppleAlt } from '@fortawesome/free-solid-svg-icons';
// import { User } from '@react-native-google-signin/google-signin';
// import auth from '@react-native-firebase/auth';
// import { ColorSkin } from '../../../assets/skin/colors';
// import { TypoSkin } from '../../../assets/skin/typography';
// import AppSvg from '../../../component/AppSvg';
// import { AppIcons } from '../../../component/AppSvg/AppIcons';

// interface Props {
//     onAuthSuccess: (value: any) => void;
//     onLoading: (value: boolean) => void;
//     isLoading: boolean;
// }

// export default function AppleSignIn(props: Props) {
//     const { onAuthSuccess, onLoading, isLoading } = props;

//     async function onAppleButtonPress() {
//         try {
//             if (isLoading) {
//                 return;
//             }
//             onLoading(true);
//             // performs login request
//             const appleAuthRequestResponse = await appleAuth.performRequest({
//                 requestedOperation: appleAuth.Operation.LOGIN,
//                 requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
//             });

//             // Ensure Apple returned a user identityToken
//             if (!appleAuthRequestResponse.identityToken) {
//                 console.log("Apple Sign-In failed - no identify token returned");
//                 onLoading(false);
//                 throw new Error('Apple Sign-In failed - no identify token returned');
//             }

//             // Create a Firebase credential from the response
//             const { identityToken, nonce } = appleAuthRequestResponse;
//             const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

//             // Sign the user in with the credential
//             return auth().signInWithCredential(appleCredential).then(async (data) => {
//                 // console.log(data);
//                 onAuthSuccess(appleAuthRequestResponse);
//                 // get current authentication state for user
//                 // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
//                 const credentialState = await appleAuth.getCredentialStateForUser(appleAuthRequestResponse.user);

//                 // use credentialState response to ensure the user is authenticated
//                 if (credentialState === appleAuth.State.AUTHORIZED) {
//                     // user is authenticated
//                     // console.log('====================================');
//                     // console.log(credentialState);
//                     // console.log('====================================');
//                 }
//                 onLoading(false);
//             });

//         } catch (error) {
//             console.log('====================================');
//             console.log("Apple Sign-In failed - no identify token returned", error);
//             console.log('====================================');
//             onLoading(false);
//         }
//     }

//     return (
//         <TouchableOpacity onPress={onAppleButtonPress} style={styles.TouchStyle}>
//             {isLoading ? (
//                 <View style={styles.TouchStyle}>
//                     <View style={styles.loading}>
//                         <ActivityIndicator size="large" />
//                     </View>
//                 </View>
//             ) : (
//                 <View style={styles.TouchStyle}>
//                     <AppSvg SvgSrc={AppIcons.logoApple} size={24} />
//                     <Text
//                         style={[
//                             TypoSkin.buttonText1,
//                             { color: ColorSkin.body, fontSize: 18, paddingLeft: 4, paddingTop: 2 },
//                         ]}>
//                         Đăng nhập với Apple
//                     </Text>
//                 </View>
//             )}
//         </TouchableOpacity>
//     );
// }
// const styles = StyleSheet.create({
//     TouchStyle: {
//         flexDirection: 'row',
//         width: '100%',
//         height: 44,
//         borderRadius: 25,
//         borderWidth: 1,
//         borderColor: ColorSkin.border1,
//         alignItems: 'center',
//         justifyContent: 'center',
//     },
//     loading: {
//         alignItems: 'center',
//         justifyContent: 'center',
//     },
// });