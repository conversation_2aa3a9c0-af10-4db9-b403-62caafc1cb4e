import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View, FlatList} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Ultis} from '../../../../utils/Utils';
import {navigate, RootScreen} from '../../../../router/router';

interface WorkItem {
  id: number;
  Name: string;
  DateEnd: number;
  Status: number;
  ToiletServicesId: string;
}
interface TodayWorkProps {
  GetToiletInfo?: any;
  tasks?: any[];
}

export const StatusTaskData = [
  {key: 1, title: 'Mở'},
  {key: 3, title: 'Quá hạn'},
  {key: 4, title: '<PERSON>àn thành'},
  {key: 5, title: 'Đóng'},
];

export const TypeStringData = [
  {key: 1, title: 'Tư vấn/Kh<PERSON>o sát'},
  {key: 2, title: '<PERSON><PERSON><PERSON> hợp đồng'},
  {key: 3, title: '<PERSON><PERSON><PERSON><PERSON> kế'},
  {key: 4, title: '<PERSON><PERSON> công'},
  {key: 5, title: '<PERSON>hiệm thu'},
  {key: 6, title: 'Vận hành'},
];

const TodayWork: React.FC<TodayWorkProps> = ({tasks}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Inpro':
        return '#FF8F00';
      case 'Completed':
        return '#4CAF50';
      case 'Pending':
        return '#2196F3';
      case 'Overdue':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const Header: React.FC = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>Công việc hôm nay</Text>
    </View>
  );

  const TableHeader: React.FC = () => (
    <View style={styles.tableHeader}>
      <View style={styles.taskColumn}>
        <Text style={styles.tableHeaderText}>Task</Text>
      </View>
      <View style={styles.dateColumn}>
        <Text style={styles.tableHeaderText}>End date</Text>
      </View>
      <View style={styles.statusColumn}>
        <Text style={styles.tableHeaderText}>Status</Text>
        <Text style={styles.dropdownIcon}>▼</Text>
      </View>
    </View>
  );

  const WorkItem: React.FC<{
    item: WorkItem;
    onPress: (item: WorkItem) => void;
  }> = ({item, onPress}) => (
    <TouchableOpacity style={styles.workItem} onPress={() => onPress(item)}>
      <View style={styles.taskColumn}>
        <Text style={styles.taskText}>{item.Name}</Text>
      </View>
      <View style={styles.dateColumn}>
        <Text style={styles.dateText}>
          {item?.DateEnd ? Ultis.datetoString(new Date(item?.DateEnd)) : '-'}
        </Text>
      </View>
      <View style={styles.statusColumn}>
        <Text
          style={[
            styles.statusText,
            {color: ColorThemes.light.secondary1_sub_color},
          ]}>
          {StatusTaskData.find(e => e.key === item?.Status)?.title ?? '-'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const handlePress = (item: WorkItem) => {
    navigate(RootScreen.DetailWorkView, {
      Id: item.ToiletServicesId,
    });
  };

  const renderWorkItem = ({item}: {item: WorkItem}) => (
    <WorkItem item={item} onPress={handlePress} />
  );

  return (
    <View style={styles.container}>
      <Header />
      <View style={styles.tableContainer}>
        <TableHeader />
        {tasks && tasks.length > 0 ? (
          <FlatList
            data={tasks}
            scrollEnabled={false}
            nestedScrollEnabled
            renderItem={renderWorkItem}
            keyExtractor={(item: any) => item.Id?.toString()}
            showsVerticalScrollIndicator={false}
            style={styles.flatList}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Không có dữ liệu</Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    margin: 16,
    borderRadius: 12,
  },
  header: {
    marginBottom: 16,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_overlay_background_color,
    fontWeight: 'bold',
  },
  tableContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tableHeaderText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  dropdownIcon: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 4,
  },
  taskColumn: {
    flex: 2,
    justifyContent: 'center',
  },
  dateColumn: {
    flex: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusColumn: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  workItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  taskText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  dateText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
  },
  statusText: {
    ...TypoSkin.body3,
    fontWeight: '600',
  },
  flatList: {
    flex: 1,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  emptyText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
});

export default TodayWork;
