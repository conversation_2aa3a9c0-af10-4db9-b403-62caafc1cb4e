import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Pressable,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {StoreInfoFormProps} from '../../type';

import {RegisterPartnerFormStyles} from '../styles/RegisterPartnerFormStyles';
import GeneralInformationForm from './GeneralInformationForm';
import RepresentativeInformationForm from './RepresentativeInformationForm';
import BankInformationForm from './BankInformationForm';
import {store} from '../../../../../redux/store/store';
import {PartnerDa} from '../../partnerDa';
import {CustomerType} from '../../../../../redux/reducers/user/da';

const RegisterPartnerForm = (props: StoreInfoFormProps) => {
  const methods = useForm({shouldFocusError: false});
  const cusInfo = store.getState().customer.data;
  const partnerDa = new PartnerDa();
  const [getDataCompany, setGetDataCompany] = useState<any>();
  const [bankData, setBankData] = useState<any[]>([]);

  const getBank = async () => {
    const res = await partnerDa.getBank();
    if (res.code === 200 && res.data.length) setBankData(res.data);
    else setBankData([]);
  };
  const getCompanyInfo = async () => {
    const response = await partnerDa.getInforCompanyById(
      cusInfo?.CompanyProfileId as string,
    );
    if (response.code === 200) {
      setGetDataCompany(response.data);
    }
  };

  useEffect(() => {
    if (cusInfo?.CompanyProfileId && cusInfo?.Type !== CustomerType.partner)
      getCompanyInfo();
  }, [cusInfo]);

  useEffect(() => {
    getBank();
  }, []);

  return (
    <View style={{flex: 1, position: 'relative'}}>
      <KeyboardAvoidingView behavior="padding" enabled style={{flex: 1}}>
        <ScrollView style={[RegisterPartnerFormStyles.containerInputShopInfo]}>
          <KeyboardAvoidingView
            style={RegisterPartnerFormStyles.inputContainer}>
            <Pressable>
              <GeneralInformationForm
                methods={methods}
                getDataCompany={getDataCompany}
              />
            </Pressable>
            <Pressable>
              <RepresentativeInformationForm
                methods={methods}
                getDataCompany={getDataCompany}
              />
            </Pressable>
            <Pressable>
              <BankInformationForm
                methods={methods}
                BankData={bankData}
                getDataCompany={getDataCompany}
              />
            </Pressable>
          </KeyboardAvoidingView>
        </ScrollView>
      </KeyboardAvoidingView>
      <TouchableOpacity
        style={RegisterPartnerFormStyles.buyButtonInputShopInfo}
        onPress={methods.handleSubmit(props.ChangeStep1)}>
        <Text style={RegisterPartnerFormStyles.buyActionButtonInputShopInfo}>
          Bước tiếp
        </Text>
      </TouchableOpacity>
    </View>
  );
};
export default RegisterPartnerForm;
