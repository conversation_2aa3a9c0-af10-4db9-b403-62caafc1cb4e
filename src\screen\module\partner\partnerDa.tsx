import {async} from 'validate.js';
import {TransactionType} from '../../../config/Contanst';
import {DataController} from '../../base-controller';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';

export class PartnerDa {
  private orderController: DataController;
  private orderDetailController: DataController;
  private productController: DataController;
  private historyRewardController: DataController;
  private rewardController: DataController;
  private shopRewardController: DataController;
  private shopCateController: DataController;
  private cateServiceController: DataController;
  private policyController: DataController;
  private shopController: DataController;
  private companyController: DataController;
  private bankController: DataController;

  constructor() {
    this.orderController = new DataController('Order');
    this.orderDetailController = new DataController('OrderDetail');
    this.productController = new DataController('Product');
    this.historyRewardController = new DataController('HistoryReward');
    this.rewardController = new DataController('Reward');
    this.shopRewardController = new DataController('ShopReward');
    this.shopCateController = new DataController('ShopCategory');
    this.cateServiceController = new DataController('CateServices');
    this.policyController = new DataController('Policy');
    this.shopController = new DataController('Shop');
    this.companyController = new DataController('CompanyProfile');
    this.bankController = new DataController('Bank');
  }

  async getAllServiceCate() {
    try {
      const response = await this.cateServiceController.getAll();
      console.log('check-response', response);
      if (response?.code === 200) {
        return response;
      }
    } catch (error) {
      console.error('getAllCateService error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }
  async getPolicy(url: string) {
    try {
      const response = await this.policyController.getListSimple({
        query: `@Url:(${url.length ? url : '/'})`,
      });
      if (response?.code === 200) {
        return response;
      }
    } catch (error) {
      console.error('getPolicy error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }
  async registerPartner(data: any) {
    try {
      const response = await this.shopController.add([data]);
      if (response?.code === 200) {
        return response;
      }
    } catch (error) {
      console.error('registerPartner error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }

  async getBank() {
    try {
      const response = await this.bankController.getAll();
      if (response?.code === 200) {
        return response;
      }
    } catch (error) {
      console.error('getAllBank error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm khuyến mãi của shop với error handling đầy đủ
   * @param shopId - ID của shop
   * @param categoryId - ID của category (optional)
   * @returns Promise với danh sách sản phẩm khuyến mãi
   */
  async getInforProductPromotionWithErrorHandling(
    shopId: string,
    categoryId?: string,
  ) {
    try {
      let query;
      if (categoryId) {
        query = `@ShopId:{${shopId}} @CategoryId:{${categoryId}}`;
      } else {
        query = `@ShopId:{${shopId}}`;
      }

      const response = await this.productController.getListSimple({
        query: query,
      });

      if (response?.code === 200) {
        if (response?.data && response?.data?.length > 0) {
          const filterData = response.data.filter(
            (item: any) => item?.Discount,
          );
          return {
            code: 200,
            data: filterData,
            message: 'Lấy danh sách sản phẩm khuyến mãi thành công',
          };
        } else {
          return {
            code: 200,
            data: [],
            message: 'Không có sản phẩm khuyến mãi',
          };
        }
      } else {
        console.error('Failed to fetch promotion products:', response?.message);
        return {
          code: response?.code || 500,
          data: [],
          message:
            response?.message || 'Không thể tải danh sách sản phẩm khuyến mãi',
          showError: true,
        };
      }
    } catch (error) {
      console.error('Error fetching promotion products:', error);
      return {
        code: 500,
        data: [],
        message: 'Có lỗi xảy ra khi tải danh sách sản phẩm',
        showError: true,
      };
    }
  }

  async getAllCompanyInfo() {
    const res = await this.companyController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }

  async getInforCompanyById(CompanyProfileId: string) {
    const res = await this.companyController.getById(CompanyProfileId);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async updateCompanyData(data: any) {
    const res = await this.companyController.edit([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async CreateCompanyData(data: any) {
    const res = await this.companyController.add([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async GetShopById(customerId: string) {
    const response = await this.shopController.getPatternList({
      page: 1,
      size: 1000,
      query: `@CustomerId:{${customerId}}`,
      pattern: {
        CateServicesId: ['Id', 'Name'],
      },
    });

    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async updateShopData(data: any) {
    const res = await this.shopController.edit([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
}

// Export instance để sử dụng trong các component
export default new PartnerDa();
