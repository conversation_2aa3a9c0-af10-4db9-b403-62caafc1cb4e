import React from 'react';
import {View, ScrollView, StyleSheet} from 'react-native';
import {Winicon, AppButton, ListTile} from 'wini-mobile-components';
import WScreenFooter from '../../../../../layout/footer';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import type {ToiletSelectorProps} from '../types';
import {TOILET_SELECTION_TITLES} from '../constants';

export const ToiletSelector: React.FC<ToiletSelectorProps> = ({
  toilets,
  selectedToilet,
  onSelectToilet,
  onSkip,
  serviceType,
}) => {
  const title = TOILET_SELECTION_TITLES[serviceType];

  return (
    <View style={styles.container}>
      <ListTile title={title} />
      <ScrollView style={styles.scrollView}>
        {toilets.map(item => (
          <ListTile
            key={item?.Id}
            listtileStyle={styles.listTile}
            leading={
              <Winicon
                src="outline/buildings/toilet"
                size={32}
                color={ColorThemes.light.neutral_text_title_color}
              />
            }
            title={item?.Name ?? '-'}
            subtitle={item?.Address ?? undefined}
            onPress={() => onSelectToilet(item)}
            trailing={
              selectedToilet?.Id === item.Id ? (
                <Winicon
                  src="fill/layout/circle-check"
                  size={16}
                  color={ColorThemes.light.primary_main_color}
                />
              ) : (
                <View />
              )
            }
          />
        ))}
      </ScrollView>
      <WScreenFooter style={styles.footer}>
        <AppButton
          title="Bỏ qua"
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={styles.skipButton}
          onPress={onSkip}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        />
      </WScreenFooter>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  listTile: {
    gap: 16,
  },
  footer: {
    paddingHorizontal: 16,
  },
  skipButton: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
