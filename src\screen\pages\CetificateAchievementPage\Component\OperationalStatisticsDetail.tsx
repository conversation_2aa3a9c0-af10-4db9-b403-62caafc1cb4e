import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, FlatList} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {renderInfoSection} from '../Ultis/renderInfoSection';

const OperationalStatisticsDetail: React.FC<{item: any}> = ({item}) => {
  const [selectedToilet, setSelectedToilet] = useState<any>();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    setSelectedToilet(item);
  }, [item]);

  const renderNote = () => {
    return (
      <View>
        <Text style={styles.sectionTitle}>Ghi chú</Text>
        <View style={styles.notesContainer}>
          <Text style={styles.notesText}>{selectedToilet?.Name || ''}</Text>
        </View>
      </View>
    );
  };

  const renderStatus = () => {
    return (
      <View style={styles.approvalSection}>
        <Text style={styles.approvalLabel}>Trạng thái phê duyệt</Text>

        {selectedToilet?.ApproveStatus === 2 ? (
          <TouchableOpacity style={styles.approvalButton}>
            <Text style={styles.approvalButtonText}>Đã duyệt</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.RejectButton}>
            <Text style={styles.RejectButtonText}>Từ chối</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderReasonCancel = () => {
    // Only show reason cancel section if ApproveStatus is 1 (rejected)
    if (selectedToilet?.ApproveStatus !== 1) {
      return null;
    }

    return (
      <View>
        <Text style={styles.sectionTitle}>Lý do từ chối</Text>
        <View style={styles.notesContainer}>
          <Text style={styles.notesText}>{selectedToilet?.Reason || ''}</Text>
        </View>
      </View>
    );
  };

  return (
    <View>
      <Text style={styles.headerTitle}>{item.data}</Text>
      {renderInfoSection(selectedToilet, isDropdownOpen, setIsDropdownOpen)}
      {renderNote()}
      {renderStatus()}
      {renderReasonCancel()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContentContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 50, // Extra padding at bottom for better scrolling
  },
  headerTitle: {
    ...TypoSkin.buttonText2,
    marginBottom: 20,
    lineHeight: 22,
  },
  infoSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoLabel: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dropdownWrapper: {
    flex: 1,
    marginLeft: 8,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 120,
  },
  dropdownText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  chevronIcon: {
    // Animation will be handled by React Native's default behavior
  },
  chevronRotated: {
    transform: [{rotate: '180deg'}],
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 200, // Changed from fixed height to maxHeight
    zIndex: 1001,
  },
  dropdownListContent: {
    paddingVertical: 4,
  },
  flatListStyle: {
    flex: 1,
  },

  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: '#e8e8e8',
    backgroundColor: 'white',
  },
  lastDropdownItem: {
    borderBottomWidth: 0,
  },
  dropdownItemContainer: {
    flex: 1,
    paddingRight: 8,
  },
  dropdownItemCode: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  dropdownItemLocation: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },

  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkMark: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 80,
  },
  valueText: {
    ...TypoSkin.buttonText2,
    color: '#333',
    fontWeight: '500',
  },
  unitText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  sectionTitle: {
    ...TypoSkin.buttonText2,
    marginBottom: 12,
  },
  notesContainer: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  notesText: {
    ...TypoSkin.buttonText3,
    height: 80,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  approvalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
  },
  approvalLabel: {
    ...TypoSkin.buttonText2,
    fontWeight: '600',
  },
  approvalButton: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  RejectButton: {
    backgroundColor: '#FFCCCC',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  approvalButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  RejectButtonText: {
    fontSize: 12,
    color: 'red',
    fontWeight: '500',
  },
});

export default OperationalStatisticsDetail;
