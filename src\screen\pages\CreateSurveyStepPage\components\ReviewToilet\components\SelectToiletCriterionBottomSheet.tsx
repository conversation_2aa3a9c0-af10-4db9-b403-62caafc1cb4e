import {<PERSON><PERSON>View} from 'react-native';
import {CustomBottomSheet} from '../../../../../../project-component/form/DateRangePicker/CustomBottomSheet';
import SurveySection from './SurveySection';
import {useEffect, useState} from 'react';
import {ToiletCriterionSurveyTask} from '../../../../../../types/toiletCriterionSurveyTask';
import {CateCriterionItem} from '../../../../../../types/cateCriteriaType';
import {SurveyTaskDa} from '../../../../../module/surveyTask/surveyTaskDa';
import {showSnackbar} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import {randomGID} from '../../../../../../utils/Utils';

interface SelectToiletCriterionBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  cateCriterionData: CateCriterionItem[];
  toiletId: string;
  toiletName: string;
  toiletCriterion: ToiletCriterionSurveyTask | null;
  surveyTaskId: string;
  onConfirmSuccess?: () => void;
  toiletSelected?: any[]; // Danh sách toilet được chọn để xử lý "tất cả"
  toiletCriterions?: any[]; // Danh sách toilet criterions hiện tại
  markToiletAsUpdated?: (toiletIds: string[]) => void;
}

export default function SelectToiletCriterionBottomSheet({
  visible,
  onClose,
  cateCriterionData,
  toiletId,
  toiletName,
  toiletCriterion,
  surveyTaskId,
  onConfirmSuccess,
  toiletSelected = [],
  toiletCriterions = [],
  markToiletAsUpdated,
}: SelectToiletCriterionBottomSheetProps) {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const surveyTaskDa = new SurveyTaskDa();

  const handleConfirm = async () => {
    try {
      setLoading(true);
      const criterionIds = checkedItems.join(',');

      if (toiletId === 'all') {
        // Xử lý cho tất cả toilet
        const updatePromises = [];
        const addPromises = [];

        for (const toilet of toiletSelected) {
          const existingCriterion = toiletCriterions.find(
            tc => tc.ToiletId === toilet.Id,
          );

          if (existingCriterion) {
            // Update existing
            const updateData = {
              ...existingCriterion,
              CriterionId: criterionIds,
            };
            updatePromises.push(surveyTaskDa.updateToiletCriterion(updateData));
          } else {
            // Add new
            const addData = {
              Id: randomGID(),
              SurveyTaskId: surveyTaskId,
              ToiletId: toilet.Id,
              CriterionId: criterionIds,
              DateCreated: new Date().getTime(),
            };
            addPromises.push(surveyTaskDa.addToiletCriterion(addData));
          }
        }

        // Thực hiện tất cả các operations
        await Promise.all([...updatePromises, ...addPromises]);

        // Đánh dấu tất cả toilet đã được cập nhật
        const allToiletIds = toiletSelected.map(toilet => toilet.Id);
        markToiletAsUpdated?.(allToiletIds);

        showSnackbar({
          message: 'Cập nhật tiêu chí cho tất cả nhà vệ sinh thành công',
          status: ComponentStatus.SUCCSESS,
        });
      } else {
        // Xử lý cho toilet đơn lẻ (logic cũ)
        if (toiletCriterion && toiletCriterion.Id) {
          // Update existing toilet criterion
          const updateData = {
            ...toiletCriterion,
            CriterionId: criterionIds,
          };
          await surveyTaskDa.updateToiletCriterion(updateData);

          // Đánh dấu toilet đã được cập nhật
          markToiletAsUpdated?.([toiletId]);

          showSnackbar({
            message: 'Cập nhật tiêu chí thành công',
            status: ComponentStatus.SUCCSESS,
          });
        } else {
          // Add new toilet criterion
          const addData = {
            Id: randomGID(),
            SurveyTaskId: surveyTaskId,
            ToiletId: toiletId,
            CriterionId: criterionIds,
            DateCreated: new Date().getTime(),
          };
          await surveyTaskDa.addToiletCriterion(addData);

          // Đánh dấu toilet đã được cập nhật
          markToiletAsUpdated?.([toiletId]);

          showSnackbar({
            message: 'Thêm tiêu chí thành công',
            status: ComponentStatus.SUCCSESS,
          });
        }
      }

      onConfirmSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error saving toilet criterion:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi lưu tiêu chí',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleCheck = (itemId: string) => {
    const index = checkedItems.indexOf(itemId);
    if (index === -1) {
      setCheckedItems(prev => [...prev, itemId]);
    } else {
      setCheckedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  useEffect(() => {
    if (toiletId === 'all') {
      setCheckedItems([]);
    } else {
      if (toiletCriterion && toiletCriterion.CriterionId) {
        const criterionIds = toiletCriterion.CriterionId.split(',');
        setCheckedItems(criterionIds);
      } else {
        setCheckedItems([]);
      }
    }
  }, [toiletCriterion, toiletId, toiletSelected, toiletCriterions]);

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title={`Chọn tiêu chí`}
      subTitle={`${toiletName}`}
      onCancel={onClose}
      onConfirm={handleConfirm}
      cancelText="Hủy"
      confirmText={loading ? 'Đang lưu...' : 'Xác nhận'}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {cateCriterionData.map((category: any) => (
          <SurveySection
            key={category.Id}
            title={`${category.Name || 'Tiêu chí'}`}
            items={category.Criterions}
            checkedItems={checkedItems}
            onToggleCheck={toggleCheck}
            disabled={loading} // Disable khi đang loading
          />
        ))}
      </ScrollView>
    </CustomBottomSheet>
  );
}
