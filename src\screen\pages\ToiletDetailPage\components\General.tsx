import React, {useEffect} from 'react';
import {View, FlatList, Text, ScrollView, Pressable} from 'react-native';
import RegisterKtxServices from './RegisterKtxServices';
import NvsInfo from './NvsInfo';
import TodayWork from './TodayWork';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import {ToiletDa} from '../../../module/toilet/toiletDa';
import ManageTicket from './ManageTicket';
import ToiletDetailDa from '../ToiletDetailDa';

const General: React.FC<{}> = ({}) => {
  const route = useRoute<any>();
  const toiletDa = new ToiletDa();
  const [GetToiletInfo, setGetToiletInfo] = React.useState<any>(null);
  const [tickets, setTickets] = React.useState<any[]>([]);
  const [tasks, setTasks] = React.useState<any[]>([]);
  const [infoService, setInfoService] = React.useState<any>();

  const fetchTicketByToiletId = async () => {
    if (route.params && route.params.item && route.params.item.Id) {
      const tickets = await toiletDa.fetchTicket(route.params.item.Id);
      if (tickets.code == 200) {
        setTickets(tickets.data);
      }
    }
  };

  const fetchTaskByToiletId = async () => {
    if (route.params && route.params.item && route.params.item.Id) {
      const tasks = await toiletDa.fetchTaskToday(route.params.item.Id);
      if (tasks.length > 0) {
        setTasks(tasks);
      } else {
        setTasks([]);
      }
    }
  };
  const getInfoService = async () => {
    let response = await ToiletDetailDa.getAllServiceCate(route.params.item.Id);
    if (response && response.length > 0) {
      setInfoService(response[0]);
    }
    return [];
  };

  useEffect(() => {
    fetchTicketByToiletId();
    fetchTaskByToiletId();
    getInfoService();
  }, []);

  useEffect(() => {
    if (route.params && route.params.item && route.params.item.Id) {
      setGetToiletInfo(route.params.item);
    }
  }, [route.params]);

  return (
    <ScrollView style={{flex: 1}}>
      <Pressable style={{paddingBottom: 50}}>
        <Text
          style={{
            paddingHorizontal: 16,
            ...TypoSkin.heading6,
            color: ColorThemes.light.neutral_text_title_color,
            paddingBottom: 10,
          }}>
          {GetToiletInfo?.Name ?? ''} - {GetToiletInfo?.Address ?? ''}
        </Text>
        <View style={{paddingHorizontal: 16}}>
          <RegisterKtxServices
            GetToiletInfo={GetToiletInfo}
            infoService={infoService}
          />
        </View>
        <View style={{paddingHorizontal: 16, paddingVertical: 16}}>
          <NvsInfo GetToiletInfo={GetToiletInfo} />
        </View>
        {tickets.length == 0 ? null : (
          <ManageTicket
            GetToiletInfo={GetToiletInfo}
            tickets={tickets.slice(0, 5)} // Chỉ lấy 5 giá trị đầu tiên
            allTickets={tickets}
          />
        )}
        {tasks.length == 0 ? null : (
          <TodayWork GetToiletInfo={GetToiletInfo} tasks={tasks} />
        )}
      </Pressable>
    </ScrollView>
  );
};

export default General;
