import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {TypoSkin} from '../../../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../../../assets/skin/colors';
import ListTile from '../../../../../../../../../component/list-tile/list-tile';
import {SkeletonImage} from '../../../../../../../../../project-component/skeleton-img';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import ConfigAPI from '../../../../../../../../../config/configApi';
import FastImage from 'react-native-fast-image';

interface FileUploadSectionProps {
  files: any[];
  onShowFilePicker: () => void;
  onRemoveFile: (item: any) => void;
  onOpenFileViewer: (item: any) => void;
  loading?: boolean;
}

export const FileUploadSection: React.FC<FileUploadSectionProps> = ({
  files,
  onShowFilePicker,
  onRemoveFile,
  onOpenFileViewer,
  loading = false,
}) => {
  return (
    <View style={styles.container}>
      <Text numberOfLines={1} style={styles.fieldLabel}>
        Thiết kế ({files?.length || 0} tệp)
      </Text>
      <TouchableOpacity
        onPress={onShowFilePicker}
        style={[styles.uploadButton, loading && styles.uploadButtonDisabled]}
        disabled={loading}>
        {loading ? (
          <>
            <ActivityIndicator
              size="small"
              color={ColorThemes.light.primary_main_color}
            />
            <Text numberOfLines={1} style={styles.uploadText}>
              Đang tải...
            </Text>
          </>
        ) : (
          <>
            <SkeletonImage
              source={{
                uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
              }}
            />
            <Text numberOfLines={1} style={styles.uploadText}>
              Thêm tệp
            </Text>
          </>
        )}
      </TouchableOpacity>
      {files?.map((item: any, index: number) => (
        <ListTile
          key={`${index} ${item.Id}`}
          leading={
            <FastImage
              source={{
                uri: ConfigAPI.url.replace('/api/', '') + item.Url,
              }}
              style={styles.fileImage}
            />
          }
          onPress={() => onOpenFileViewer(item)}
          title={item.Name ?? `Ảnh ${index + 1}`}
          titleStyle={styles.fileTitle}
          subtitle={`${Math.round(item.Size / (1024 * 1024))}MB`}
          listtileStyle={styles.fileListTile}
          style={styles.fileItem}
          trailing={
            <TouchableOpacity
              onPress={() => onRemoveFile(item)}
              style={styles.removeButton}>
              <FontAwesomeIcon
                icon={faMinusCircle}
                size={20}
                color="#D72525FF"
              />
            </TouchableOpacity>
          }
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  fileImage: {
    width: 35,
    height: 35,
    objectFit: 'cover',
    borderRadius: 4,
  },
  fieldLabel: {
    ...TypoSkin.label3,
  },
  uploadButton: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 0.15,
    borderColor: ColorThemes.light.neutral_main_border,
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 8,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadText: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  fileTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
  },
  fileListTile: {
    gap: 16,
  },
  fileItem: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
    padding: 8,
  },
  removeButton: {
    padding: 4,
  },
});
