import React, {useCallback} from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import ListTile from 'component/list-tile/list-tile';
import {ColorThemes} from 'assets/skin/colors';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {useTicketPermissions, usePopupHandlers} from './hooks';
import {
  TicketHeader,
  TicketInfo,
  FileAttachments,
  TicketActions,
  Status as TicketStatusComponent,
  PopupEditTicket as EditTicketPopup,
  PopupViewTicket as ViewTicketPopup,
  FilePreviewPopup,
} from './components';
import {TicketType} from 'types/ticketType';
import {CustomerItem} from 'redux/reducers/user/da';
import {TicketDa} from '../../ticketDa';
import {FPopup} from 'wini-mobile-components';

interface TicketCardProps {
  item: TicketType;
  index: number;
  relativeData?: any;
  methods?: any;
  fileInfor?: Array<any>;
  edit?: boolean;
  customer?: CustomerItem;
  typeLabel?: string;
  onUpdateTicket?: (ticket: TicketType) => void;
  type?: string;
}

export default function TicketCard({
  item,
  index,
  relativeData,
  methods,
  fileInfor,
  customer,
  typeLabel,
  onUpdateTicket,
  type,
  edit,
}: TicketCardProps) {
  const {checkEdit} = useTicketPermissions(item);
  const {
    popupRef,
    showViewPopup,
    showEditPopup,
    showFilePreviewPopup,
    closeCurrentPopup,
  } = usePopupHandlers();

  const ticketDa = new TicketDa();

  /**
   * Handle ticket changes internally and notify parent component
   * @param updatedTicket - The updated ticket data
   */
  const onChangeTicket = useCallback(
    async (updatedTicket: TicketType) => {
      try {
        // Update the ticket using TicketDa
        const success = await ticketDa.update(updatedTicket);

        if (!success) {
          showSnackbar({
            message: 'Không thể cập nhật yêu cầu. Vui lòng thử lại.',
            status: ComponentStatus.ERROR,
          });
          return;
        }

        // Show success message and close popup
        closeCurrentPopup();
        showSnackbar({
          message: 'Cập nhật thông tin yêu cầu thành công',
          status: ComponentStatus.SUCCSESS,
        });

        // Notify parent component about the successful update
        if (onUpdateTicket) {
          onUpdateTicket(updatedTicket);
        }
      } catch (error) {
        console.error('Error updating ticket:', error);
        showSnackbar({
          message: 'Đã có lỗi xảy ra khi cập nhật yêu cầu',
          status: ComponentStatus.ERROR,
        });
      }
    },
    [ticketDa, onUpdateTicket, closeCurrentPopup],
  );

  const handleViewPress = () => {
    showViewPopup(
      <ViewTicketPopup
        relativeData={relativeData}
        item={item}
        fileInfor={fileInfor || []}
        typeLabel={typeLabel ?? ''}
        showAction={edit}
        onClose={closeCurrentPopup}
        onEdit={handleEditPress}
        ref={popupRef}
      />,
    );
  };

  const handleEditPress = () => {
    if (methods) {
      methods.setValue('Status', item?.Status);
      methods.setValue('Content', undefined);
    }
    showEditPopup(
      <EditTicketPopup
        item={item}
        methods={methods}
        fileInfor={fileInfor || []}
        customer={customer}
        ref={popupRef}
        onChange={(vl: any) => {
          if (onChangeTicket) {
            onChangeTicket({
              ...item,
              Status: vl.Status,
              Detail: vl.Detail,
              Ktx: vl.Ktx ?? undefined,
            });
          }
        }}
      />,
    );
  };

  const handleFilePress = (file: any) => {
    showFilePreviewPopup(
      <FilePreviewPopup
        file={file}
        onClose={() => {
          // Close handled by popup component
        }}
      />,
    );
  };

  return (
    <View style={[styles.container]}>
      <FPopup ref={popupRef} />
      <ListTile
        key={item.Id}
        onPress={handleViewPress}
        leading={
          <TicketHeader item={item} index={index} typeLabel={typeLabel} />
        }
        title={<></>}
        subtitle={<></>}
        listtileStyle={styles.listTileStyle}
        titleStyle={styles.titleStyle}
        bottom={
          <View style={styles.bottomContainer}>
            <TicketInfo
              item={item}
              customer={customer}
              relativeData={relativeData}
            />
            <FileAttachments
              fileInfor={fileInfor || []}
              onFilePress={handleFilePress}
            />
            <View style={styles.statusActionsContainer}>
              <View>
                <TicketStatusComponent status={item.Status} />
              </View>
              {edit && (
                <TicketActions
                  checkEdit={checkEdit}
                  onEditPress={handleEditPress}
                />
              )}
            </View>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: Dimensions.get('window').width - 32,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
    borderRadius: 9,
    paddingBottom: 32,
  },
  listTileStyle: {
    gap: 16,
  },
  titleStyle: {
    paddingBottom: 8,
  },
  bottomContainer: {
    alignItems: 'flex-start',
    width: '100%',
    gap: 8,
    paddingTop: 16,
  },
  statusActionsContainer: {
    paddingTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
});
