import { forwardRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { closePopup } from "../../../../../component/popup/popup";
import { Dimensions, SafeAreaView, View } from "react-native";
import { ScreenFooter } from "react-native-screens";
import { ColorThemes } from "../../../../../assets/skin/colors";
import AppButton from "../../../../../component/button";
import { Winicon } from "../../../../../component/export-component";
import ScreenHeader from "../../../../layout/header";
import SelectProductStep from "../../../service/components/form/SelectProductStep";
import SelectMaterials from "../../../service/components/form/SelectMaterials";
import SelectTemplateTask from "../../../service/components/form/SelectTemplateTask";
import WScreenFooter from "../../../../layout/footer";

export const PopupSelectTasks
    = forwardRef(function PopupSelectTasks(data: { onSubmit: any }, ref: any) {
        const { onSubmit } = data
        const methods = useForm({ shouldFocusError: false, defaultValues: { tasks: [] } })

        const _onSubmit = (ev: any) => {
            onSubmit(ev.tasks)
            closePopup(ref)
        }

        return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height - 65, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: '#fff' }}>
            <ScreenHeader
                style={{
                    backgroundColor: ColorThemes.light.transparent,
                    flexDirection: 'row',
                }}
                title={`Danh sách công việc`}
                prefix={<View />}
                action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                    <Winicon src="outline/layout/xmark" onClick={() => closePopup(ref)} size={20} color={ColorThemes.light.neutral_text_body_color} />
                </View>}
            />
            <View style={{ flex: 1, height: "100%" }}>
                <SelectTemplateTask methods={methods} />
            </View>
            <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16, paddingBottom: 16 }}>
                <AppButton
                    title={'Xong'}
                    backgroundColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                    onPress={methods.handleSubmit(_onSubmit)}
                    textColor={ColorThemes.light.neutral_absolute_background_color}
                />
            </WScreenFooter>
        </SafeAreaView>
    });
