import { Di<PERSON><PERSON>, FlatList, KeyboardAvoidingView, Platform, RefreshControl, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { ColorThemes } from "../../../assets/skin/colors";
import ScreenHeader from "../../layout/header";
import { useNavigation } from "@react-navigation/native";
import WScreenFooter from "../../layout/footer";
import AppButton from "../../../component/button";
import { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import { ComponentStatus } from "../../../component/component-status";
import { FDialog, FSelect1, FTextField, showDialog, showSnackbar, Winicon } from "../../../component/export-component";
import EmptyPage from "../../../project-component/empty-page";
import { CardToiletHoriSkeleton } from "../../../project-component/skeletonCard";
import { DataController, IntergrationController } from "../../base-controller";
import { useSelectorCustomerCompanyState, useSelectorCustomerState } from "../../../redux/hooks/hooks";
import { useDispatch } from "react-redux";
import { CustomerCompanyItem, CustomerRole, CustomerStatus, CustomerType } from "../../../redux/reducers/user/da";
import { TypoSkin } from "../../../assets/skin/typography";
import ListTile from "../../../component/list-tile/list-tile";
import { SkeletonImage } from "../../../project-component/skeleton-img";
import ConfigAPI from "../../../config/configApi";
import { closePopup, FPopup, showPopup } from "../../../component/popup/popup";
import { useForm } from "react-hook-form";
import { randomGID, regexGetVariables, Ultis } from "../../../utils/Utils";
import MultiSelect from "../../../component/select1/multi-select";
import { CustomerActions } from "../../../redux/reducers/user/reducer";

export default function CompanyView() {

    const [isLoading, setLoading] = useState(false);
    const user = useSelectorCustomerState().data
    const [isRefreshing, setRefreshing] = useState(false);
    const [managerData, setManagerData] = useState({ data: Array<any>(), totalCount: undefined })
    const navigation = useNavigation<any>()
    const userRole = useSelectorCustomerState().role
    const owner = useSelectorCustomerCompanyState().owner
    const company = useSelectorCustomerCompanyState().data
    const [customers, setCustomers] = useState<Array<any>>([])
    const popupRef = useRef<any>()
    const dialogDelAccRef = useRef<any>()
    const dispatch = useDispatch<any>()
    const isEditable = useMemo(() => {
        if (company?.Id === ConfigAPI.ktxCompanyId) {
            return !userRole?.Role?.includes(CustomerRole.Coordinator)
        } else {
            return user?.CompanyProfileId || userRole?.Role?.includes(CustomerRole.Coordinator);
        }
    }, [userRole, user, company])
    const listRole = useMemo(() => {
        if (owner?.Id === ConfigAPI.adminKtxId) return [CustomerRole.KTX, CustomerRole.Coordinator]

        if (userRole && user) {
            let tmp = [CustomerRole.Coordinator, CustomerRole.Consultant, CustomerRole.SCBD, CustomerRole.VSLD]
            if (owner?.Type !== CustomerType.partner) tmp = tmp.filter(e => e !== CustomerRole.Coordinator)
            return tmp
        }
        return []
    }, [userRole, user, owner])

    const getData = async ({ page, size }: any) => {
        setLoading(true)
        const controller = new DataController("CustomerCompany")
        const customerController = new DataController("Customer")
        const res = await controller.aggregateList({ page: page ?? 1, size: size ?? 100, searchRaw: `@CompanyProfileId:{${company?.Id}}` })
        if (res.code === 200) {
            const resCustomer = await customerController.getByListId(res.data.map((e: any) => e.CustomerId))
            if (resCustomer.code === 200) setCustomers(resCustomer.data.map((e: any) => ({ ...e, bgColor: Ultis.generateDarkColorRgb() })))
            setManagerData({ data: res.data, totalCount: res.totalCount })
            setLoading(false)
        } else {
            showSnackbar({ message: res.message, status: ComponentStatus.ERROR })
            setLoading(false)
        }
    }

    useEffect(() => {
        if (user) getData({})
    }, [user, userRole])

    const returnButtonChangeStatus = (item: any, customer: any) => {
        if (!item) return <View />
        switch (customer?.Status) {
            case CustomerStatus.locked:
                return <Text style={{ color: ColorThemes.light.neutral_text_title_color, backgroundColor: ColorThemes.light.neutral_main_background_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Đang khóa</Text>
            case CustomerStatus.active:
                switch (item.Status) {
                    case 1:
                        return <Text style={{ color: ColorThemes.light.neutral_absolute_background_color, backgroundColor: ColorThemes.light.primary_main_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Đã tham gia</Text>
                    default:
                        return <Text style={{ color: ColorThemes.light.neutral_absolute_background_color, backgroundColor: ColorThemes.light.warning_main_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Chờ xác nhận</Text>
                }
            default:
                return <Text style={{ color: ColorThemes.light.neutral_text_title_color, backgroundColor: ColorThemes.light.neutral_main_background_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Chưa kích hoạt</Text>
        }

    }

    const [pageDetails, setPageDetails] = useState({ page: 1, size: 10 })
    const showPopupInviteMember = () => {
        showPopup({
            ref: popupRef,
            enableDismiss: true,
            children: <PopupInviteMember ref={popupRef} onSuccess={() => { getData({ page: 1, size: 10 }) }} />
        })
    }

    const [searchValue, setSearchValue] = useState("")

    return <View style={{ flex: 1, backgroundColor: ColorThemes.light.neutral_absolute_background_color }}>
        <FDialog ref={dialogDelAccRef} />
        <FPopup ref={popupRef} />
        <ScreenHeader onBack={() => { navigation.pop() }} title="Phân quyền tài khoản"
            bottom={<View style={{ paddingHorizontal: 16 }}><FTextField style={{ paddingHorizontal: 16, width: "100%", height: 40 }} onChange={(vl) => {
                setSearchValue(vl.trim())
            }} prefix={<Winicon src='outline/development/zoom' size={14} color={ColorThemes.light.neutral_text_subtitle_color} />} value={searchValue} placeholder="Tìm kiếm" />
            </View>} />
        <FlatList
            data={searchValue.length ? managerData.data.filter((e: any) => e.Name.toLowerCase().includes(searchValue.toLowerCase()) || customers.find((c: any) => c.Id === e.CustomerId).Mobile.toLowerCase().includes(searchValue.toLowerCase())) : managerData.data}
            refreshControl={
                <RefreshControl refreshing={isRefreshing} onRefresh={() => {
                    setRefreshing(true); getData({ page: 1, size: 10 }).then(() => {
                        setLoading(false)
                        setRefreshing(false)
                    })
                }} />
            }
            style={{ flex: 1, gap: 8, marginHorizontal: 16, marginVertical: 16 }}
            ListFooterComponent={() => <View style={{ height: 65 }} />}
            ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
            renderItem={({ item, index }: any) => {
                const customer = customers.find((e: any) => e.Id === item.CustomerId) as any
                return <ListTile
                    key={item.Id}
                    listtileStyle={{ alignItems: "flex-start", gap: 16 }}
                    leading={customer?.Img ? <SkeletonImage source={{ uri: customer.Img.startsWith("https") ? customer.Img : (ConfigAPI.imgUrlId + customer?.Img) }}
                        style={{ width: 32, height: 32, borderRadius: 50, objectFit: "cover", marginTop: 8 }} /> : <View style={{ marginTop: 8, width: 32, height: 32, borderRadius: 50, alignItems: "center", justifyContent: "center", backgroundColor: Ultis.generateDarkColorRgb(), }}>
                        <Text style={{ color: "#fff", display: "flex", justifyContent: "center", alignItems: "center" }}>{customer?.Name?.substring(0, 1)}</Text></View>}
                    style={{ marginBottom: index === managerData.data.length - 1 ? 50 : 0, borderColor: ColorThemes.light.neutral_main_background_color, borderWidth: 1 }}
                    title={`${item?.Name ?? '-'}`}
                    titleStyle={{ ...TypoSkin.title3, paddingBottom: 8 }}
                    subtitle={<View style={{ alignItems: "flex-start", flex: 1, gap: 4 }}>
                        <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Số điện thoại: ${customer?.Mobile ?? "-"}`}</Text>
                    </View>}
                    bottom={
                        <View style={{ flex: 1, width: "100%", paddingTop: 8, gap: 8 }}>
                            {item.Role?.includes(CustomerRole.Owner) ?
                                <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_title_color }}>Chủ doanh nghiệp</Text> :
                                (company?.Id === ConfigAPI.ktxCompanyId && isEditable) ?
                                    <FSelect1
                                        value={(item.Role.split(",") ?? [])[0]}
                                        data={[CustomerRole.KTX, CustomerRole.Coordinator].map(e => ({ id: e, name: e === CustomerRole.KTX ? "Lãnh đạo KTX" : "Điều phối" }))}
                                        onChange={async (ev) => {
                                            if (userRole?.Id === item) {
                                                var obj = { ...userRole as CustomerCompanyItem, Role: ev.id as string }
                                                await CustomerActions.editRole(dispatch, obj)
                                                setManagerData({ data: managerData.data.map((e: any) => e.Id === item.Id ? { ...e, Role: ev.id } : e), totalCount: managerData.totalCount })
                                            } else {
                                                const roleController = new DataController("CustomerCompany")
                                                const res = await roleController.edit([{ ...item, Role: ev.id }])
                                                if (res.code === 200) setManagerData({ data: managerData.data.map((e: any) => e.Id === item.Id ? { ...e, Role: ev.id } : e), totalCount: managerData.totalCount })
                                            }
                                        }}
                                    /> : (isEditable && customer?.Type !== CustomerType.partner) ? <MultiSelect
                                        placeholder="Chọn vai trò"
                                        style={{ paddingHorizontal: 8 }}
                                        value={item.Role.split(",") ?? []}
                                        onChange={async (ev: any) => {
                                            if (userRole?.Id === item) {
                                                var obj: CustomerCompanyItem = { ...userRole as CustomerCompanyItem, Role: ev?.join(",") }
                                                await CustomerActions.editRole(dispatch, obj)
                                                setManagerData({ data: managerData.data.map((e: any) => e.Id === item.Id ? { ...e, Role: ev.join(",") } : e), totalCount: managerData.totalCount })
                                            } else {
                                                const roleController = new DataController("CustomerCompany")
                                                const res = await roleController.edit([{ ...item, Role: ev.join(",") }])
                                                if (res.code === 200) setManagerData({ data: managerData.data.map(e => e.Id === item.Id ? { ...e, Role: ev.join(",") } : e), totalCount: managerData.totalCount })
                                            }
                                        }}
                                        data={listRole.map(e => {
                                            switch (e) {
                                                case CustomerRole.Owner:
                                                    return { id: e, name: "Chủ doanh nghiệp" }
                                                case CustomerRole.Consultant:
                                                    return { id: e, name: "Tư vấn/khảo sát" }
                                                case CustomerRole.Coordinator:
                                                    return { id: e, name: "Điều phối" }
                                                case CustomerRole.SCBD:
                                                    return { id: e, name: "Sửa chữa bảo dưỡng" }
                                                case CustomerRole.VSLD:
                                                    return { id: e, name: "Vệ sinh lau dọn" }
                                                case CustomerRole.KTX:
                                                    return { id: e, name: "Lãnh đạo KTX" }
                                                default:
                                                    return { id: e, name: e }
                                            }
                                        })}
                                    /> :
                                        <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Vai trò: ${item.Role.split(",").map((e: any) => {
                                            switch (e) {
                                                case CustomerRole.Owner:
                                                    return "Chủ doanh nghiệp"
                                                case CustomerRole.Consultant:
                                                    return "Tư vấn/khảo sát"
                                                case CustomerRole.Coordinator:
                                                    return "Điều phối"
                                                case CustomerRole.SCBD:
                                                    return "Sửa chữa bảo dưỡng"
                                                case CustomerRole.VSLD:
                                                    return "Vệ sinh lau dọn"
                                                case CustomerRole.KTX:
                                                    return "Lãnh đạo KTX"
                                                default:
                                                    return ""
                                            }
                                        }).join(", ")}`}</Text>}
                            {customer?.Type === CustomerType.partner ?
                                <View style={{ alignItems: "center", flexDirection: "row" }}>
                                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Đối tác: `}</Text>
                                    <Winicon src="fill/user interface/c-check" size={16} color={ColorThemes.light.primary_main_color} />
                                </View> : null}
                            <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Địa chỉ: ${customer?.Address ?? "-"}`}</Text>
                            <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Mô tả: ${item?.Description ?? "-"}`}</Text>


                            <View style={{ flexDirection: "row", flex: 1, alignItems: "center", justifyContent: "space-between", alignContent: "space-between" }}>
                                {returnButtonChangeStatus(item, customer)}
                                {/* actions */}
                                {!item.Role?.includes(CustomerRole.Owner) ? <View style={{ flexDirection: "row", gap: 16, flex: 1, alignItems: "center", justifyContent: "flex-end", }}>
                                    {!item.Role?.includes(CustomerRole.Owner) ? <Winicon src="outline/user interface/trash-can" size={16} color={ColorThemes.light.error_main_color} onClick={() => {
                                        showDialog({
                                            ref: dialogDelAccRef,
                                            status: ComponentStatus.WARNING,
                                            title: "Bạn chắc chắn muốn xóa",
                                            onSubmit: async () => {
                                                const roleController = new DataController("CustomerCompany")
                                                const res = await roleController.delete([item.Id])
                                                if (res.code === 200) getData({ page: pageDetails.page, size: pageDetails.size })
                                                const customerController = new DataController("Customer")
                                                customerController.edit([{ ...customer, Type: CustomerType.guest }])
                                            }
                                        })
                                    }} /> : <View />}
                                </View> : <View style={{ flex: 1 }} />}
                            </View></View>}
                />
            }
            }
            ListEmptyComponent={() => isLoading ? Array.from(Array(10)).map((_, index) => <View key={index} style={{ gap: 16 }}>
                <CardToiletHoriSkeleton />
            </View>
            ) : <EmptyPage title="Bạn không thuộc doanh nghiệp nào" />}
        />
        {/* {isEditable ?
            <WScreenFooter style={{ paddingHorizontal: 16 }}>
                <AppButton
                    title={'Mời thành viên'}
                    backgroundColor={ColorThemes.light.primary_main_color}
                    borderColor="transparent"
                    containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                    onPress={showPopupInviteMember}
                    textColor={ColorThemes.light.neutral_absolute_background_color}
                />
            </WScreenFooter> : null} */}
    </View>
}

const configNotificationId = "0820ebdbe13047d3ad6a3f4a181c9582"
const PopupInviteMember = forwardRef(function PopupInviteMember(data: { onSuccess: any }, ref: any) {
    const { onSuccess } = data
    const company = useSelectorCustomerCompanyState().data
    const user = useSelectorCustomerState().data
    const methods = useForm({ shouldFocusError: false, defaultValues: { customers: Array<any>() } })

    const _onSubmit = async (ev: any) => {
        const roleController = new DataController("CustomerCompany")
        const newCustomerCompanys = []
        const newNotis = []
        const configController = new DataController("ConfigNotification")
        const configRes = await configController.getById(configNotificationId)
        for (const e of ev.customers) {
            const tmp = {
                Id: randomGID(),
                Name: e.Name,
                DateCreated: Date.now(),
                Sort: 1,
                Role: e.Role,
                Status: 0,
                Description: e.Role,
                CustomerId: e.Id,
                CompanyProfileId: company?.Id,
            }
            newCustomerCompanys.push(tmp)
            newNotis.push({
                Id: randomGID(),
                Name: "Mời thành viên",
                DateCreated: Date.now(),
                Sort: 1,
                Content: configRes?.data?.Content?.replace(regexGetVariables, (match: any, key: any) => {
                    switch (key) {
                        case "CustomerName":
                            return user?.Name
                        case "CompanyName":
                            return company?.Name
                        default:
                            return match
                    }
                }),
                Status: 0,
                LinkWeb: "/setting/company",
                CustomerId: e.Id,
                CustomerCompanyId: tmp.Id,
                ConfigNotificationId: configNotificationId,
            })
        }
        const res = await roleController.add(newCustomerCompanys)
        if (res.code !== 200) return showSnackbar({ message: res.message, status: ComponentStatus.ERROR })
        const controller = new DataController("Notification")
        controller.add(newNotis)
        const intergrationController = new IntergrationController()
        intergrationController.sendMessageToGroup({
            customers: '',
            imageUrl: '',
            type: '',
            searchRaw: `@Id:{${ev.customers.map((e: any) => e.Id).join(" | ")}}`,
            title: `Bạn có lời mời vào ${company?.Name}.`,
            body: `Bạn được ${company?.Name} mời vào hệ thống. Vui lòng truy cập hệ thống và xác nhận.`
        })
        closePopup(ref)
        onSuccess()
    }

    const _onError = (ev: any) => {
        console.log(ev);
    }

    return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height - 40, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: ColorThemes.light.neutral_absolute_background_color }}>
        <ScreenHeader
            style={{
                backgroundColor: ColorThemes.light.transparent,
                flexDirection: 'row',
                paddingVertical: 4
            }}
            title={`Mời thành viên`}
            prefix={<View />}
            action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                <Winicon src="outline/layout/xmark" onClick={() => closePopup(ref)} size={20} color={ColorThemes.light.neutral_text_body_color} />
            </View>}
        />
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0} style={{ height: "100%", width: "100%", paddingHorizontal: 16, flex: 1 }}>
            <View style={{ height: 46, paddingVertical: 4 }}>
                <FSelect1
                    allowSearch
                    data={[]}
                    style={{ flex: 1, gap: 12 }}
                    placeholder="Nhập số điện thoại"
                    onChange={(vl) => { }}
                    onSearch={async (ev: any) => {
                        let _mobile = ev.replace("+84", "0")
                        if (_mobile.startsWith("84")) _mobile = _mobile.replace("84", "0")
                        if (_mobile.length === 10) {
                            const controller = new DataController("Customer")
                            const roleController = new DataController("CustomerCompany")
                            const res = await controller.aggregateList({ page: 1, size: 10, searchRaw: `@Mobile:(*${_mobile}*)` })
                            if (res.code === 200 && res.data.length > 0) {
                                const isMemeber = await roleController.getListSimple({ page: 1, size: 1000, query: `@CustomerId:{${res.data.map((e: any) => e.Id).join(" | ")}}` })
                                return res.data.map((e: any) => {
                                    const _mem = isMemeber?.data?.find((m: any) => m.CustomerId === e.Id)
                                    const _disabled = _mem !== undefined || methods.watch("customers").some((c: any) => c.Id === e.Id) || (_mem && _mem.CompanyProfileId !== company?.Id)
                                    const bgColor = Ultis.generateRandomColor()
                                    return {
                                        ...e,
                                        id: e.Id,
                                        "_bgColor": bgColor,
                                        disabled: _disabled,
                                        name: <TouchableOpacity key={e.Id} disabled={_disabled} style={{ gap: 8, width: "100%" }}
                                            onPress={() => {
                                                methods.setValue("customers", [...methods.watch("customers"), { ...e, "_bgColor": bgColor, Role: CustomerRole.SCBD }])
                                            }}>
                                            {e?.Img ? <SkeletonImage source={{ uri: e.Img.startsWith("https") ? e.Img : (ConfigAPI.imgUrlId + e?.Img) }} style={{ width: 32, height: 32, borderRadius: 50, objectFit: "cover" }} /> : <View style={{ width: 32, height: 32, borderRadius: 50, backgroundColor: bgColor ?? Ultis.generateRandomColor(), }}><Text style={{ color: "#fff", display: "flex", justifyContent: "center", alignItems: "center" }}>{e?.Name?.substring(0, 1)}</Text></View>}
                                            <View style={{ gap: 2, flex: 1 }}>
                                                <Text style={{ ...TypoSkin.label3, color: ColorThemes.light.neutral_text_body_color }}>{e.Name}</Text>
                                                <Text style={{ ...TypoSkin.subtitle4, color: ColorThemes.light.neutral_text_subtitle_color }}>{e.Mobile}</Text>
                                            </View>
                                            {_mem ?
                                                _mem.CompanyProfileId !== company?.Id ?
                                                    <Text style={{ color: ColorThemes.light.neutral_text_title_color, backgroundColor: ColorThemes.light.neutral_main_background_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Doanh nghiệp khác</Text>
                                                    :
                                                    _mem.Status === 0 || methods.watch("customers").some(c => c.Id === e.Id) ?
                                                        <Text style={{ color: ColorThemes.light.neutral_absolute_background_color, backgroundColor: ColorThemes.light.primary_main_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Đã mời</Text>

                                                        :
                                                        <Text style={{ color: ColorThemes.light.neutral_absolute_background_color, backgroundColor: ColorThemes.light.infor_main_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 8 }}>Thành viên</Text>
                                                : null}
                                        </TouchableOpacity>
                                    }
                                })
                            }
                        }
                        return []
                    }}
                />
            </View>
            <ScrollView style={{ backgroundColor: ColorThemes.light.transparent, flex: 1, gap: 16 }} >

                <Text style={{ ...TypoSkin.heading8, color: ColorThemes.light.neutral_text_body_color, paddingTop: 16 }}>Tài khoản đã chọn</Text>
                {methods.watch("customers").length ? methods.watch("customers").map((item) => {
                    return <ListTile leading={item?.Img ? <SkeletonImage source={{ uri: item.Img.startsWith("https") ? item.Img : (ConfigAPI.imgUrlId + item.Img) }} style={{ width: 32, height: 32, borderRadius: 50, objectFit: "cover" }} /> : <View style={{ width: 32, height: 32, borderRadius: 50, backgroundColor: item["_bgColor"] ?? Ultis.generateRandomColor(), }}><Text style={{ color: "#fff", display: "flex", justifyContent: "center", alignItems: "center" }}>{item?.Name?.substring(0, 1)}</Text></View>} title={item.Name} subtitle={item.Mobile} trailing={<Winicon src="outline/user interface/trash-can" size={16} color={ColorThemes.light.error_main_color} style={{ padding: 8 }} onClick={() => {
                        methods.setValue("customers", methods.watch("customers").filter(e => e.Id !== item.Id))
                    }} />}
                        bottom={
                            <MultiSelect
                                placeholder="Chọn vai trò"
                                style={{ width: "100%" }}
                                value={item.Role.split(",") ?? []}
                                data={Object.values(CustomerRole).filter(e => !(user?.Type !== CustomerType.partner && e === CustomerRole.Consultant)).map(e => {
                                    switch (e) {
                                        case CustomerRole.Consultant:
                                            return { id: e, name: "Tư vấn/khảo sát" }
                                        case CustomerRole.Coordinator:
                                            return { id: e, name: "Điều phối" }
                                        case CustomerRole.SCBD:
                                            return { id: e, name: "Sửa chữa bảo dưỡng" }
                                        case CustomerRole.VSLD:
                                            return { id: e, name: "Vệ sinh lau dọn" }
                                        default:
                                            return { id: e, name: e }
                                    }
                                })}
                                onChange={(ev) => {
                                    methods.setValue("customers", methods.watch("customers").map(e => {
                                        if (e.Id === item.Id) return { ...e, Role: ev.join(",") }
                                        return e
                                    }))
                                }}
                            />
                        } />

                }) : <Text style={{ ...TypoSkin.subtitle4, color: ColorThemes.light.neutral_text_body_color, paddingTop: 16 }}>Bạn chưa chọn tài khoản nào.</Text>}
            </ScrollView>
        </KeyboardAvoidingView>
        <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16, paddingBottom: 16 }}>
            <AppButton
                title={'Mời'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={() => {
                    methods.handleSubmit(_onSubmit, _onError)()
                }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
            />
        </WScreenFooter>
    </SafeAreaView>
})
