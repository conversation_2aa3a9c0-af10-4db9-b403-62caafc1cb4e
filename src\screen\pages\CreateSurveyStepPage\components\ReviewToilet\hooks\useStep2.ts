import {useEffect, useState} from 'react';
import {UseStep2Return} from '../types';
import CateCriterionDA from '../../../../../module/cateCriterion/cateCriterionDa';
import {ToiletDa} from '../../../../../module/toilet/toiletDa';
import {ToiletItem} from '../../../../../../types/toiletType';
import {ToiletCriterionSurveyTask} from '../../../../../../types/toiletCriterionSurveyTask';
import {CateCriterionItem} from '../../../../../../types/cateCriteriaType';
import {showSnackbar} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import {SurveyTaskDa} from '../../../../../module/surveyTask/surveyTaskDa';

export const useStep2 = (
  customerId: string,
  surveyTaskId: string,
): UseStep2Return => {
  const cateCriterionDA = new CateCriterionDA();
  const toiletDA = new ToiletDa();
  const surveyTaskDa = new SurveyTaskDa();

  const [cateCriterionData, setCateCriterionData] = useState<
    CateCriterionItem[]
  >([]);
  const [toiletCustomer, setToiletCustomer] = useState<ToiletItem[]>([]);
  const [toiletSelected, setToiletSelected] = useState<ToiletItem[]>([]);
  const [toiletCriterions, setToiletCriterions] = useState<
    ToiletCriterionSurveyTask[]
  >([]);
  const [selectedToilet, setSelectedToilet] = useState<ToiletItem | null>(null);
  const [loading, setLoading] = useState(true);

  const handleAddNew = (data: any) => {
    setToiletCustomer(prev => [...prev, data]);
  };

  const setSelectedToilets = (toilets: ToiletItem[]) => {
    setToiletSelected(toilets);
  };

  const findToiletCriterionByToiletId = (toiletId: string) => {
    return toiletCriterions.find(criterion => criterion.ToiletId === toiletId);
  };

  const handleToiletPress = (toilet: ToiletItem) => {
    setSelectedToilet(toilet);
  };

  const initData = async () => {
    try {
      setLoading(true);
      const res = await cateCriterionDA.getAllWithCriterion();
      const toilets = await toiletDA.fetchByCustomer(customerId);
      const {toiletCriterions} =
        await surveyTaskDa.fetchToiletCriterion(surveyTaskId);
      setCateCriterionData(res);
      setToiletCustomer(toilets);
      setToiletCriterions(toiletCriterions);
      setToiletSelected(
        toilets.map((t: ToiletItem) => ({...t, isUpdated: false})),
      );
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra. Vui lòng thử lại',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshToiletCriterions = async () => {
    try {
      const {toiletCriterions} =
        await surveyTaskDa.fetchToiletCriterion(surveyTaskId);
      setToiletCriterions(toiletCriterions);
    } catch (error) {
      console.error('Error refreshing toilet criterions:', error);
    }
  };

  const markToiletAsUpdated = (toiletIds: string[]) => {
    setToiletSelected(prev =>
      prev.map(toilet =>
        toiletIds.includes(toilet.Id) ? {...toilet, isUpdated: true} : toilet,
      ),
    );
  };

  useEffect(() => {
    initData();
  }, []);

  return {
    handleAddNew,
    setSelectedToilets,
    cateCriterionData,
    toiletCustomer,
    toiletSelected,
    toiletCriterions,
    loading,
    selectedToilet,
    handleToiletPress,
    findToiletCriterionByToiletId,
    refreshToiletCriterions,
    markToiletAsUpdated,
  };
};
