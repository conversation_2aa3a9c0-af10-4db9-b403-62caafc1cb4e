import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '../store/store';
import {DataController} from '../../screen/base-controller';
import {Brand} from '../../types/brandType';
import {showSnackbar} from '../../component/export-component';
import {ComponentStatus} from '../../component/component-status';

const brandAction = {
  fetch: async (config: any) => {
    const controller = new DataController('Brands');
    const params: any = {
      page: config?.page ?? 1,
      size: config?.size ?? 1000,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    };
    if (config?.searchRaw) params.searchRaw = config.searchRaw;
    if (config?.sortby) params.sortby = config.sortby;
    const res = await controller.aggregateList(params);
    if (res.code === 200) {
      return res.data;
    }
    return [];
  },
  findOne: async (id: string) => {
    const controller = new DataController('Brands');
    const res = await controller.getById(id);
    return res.data;
  },
};

const fetchBrands = createAsyncThunk<
  Brand[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('brand/fetchBrands', async (config, thunkAPI: any) => {
  const controller = new DataController('Brands');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      return res.data;
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchBrands, brandAction};
