import React, {useMemo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {Ultis} from '../../../../../../../utils/Utils';
import {
  CateServicesType,
  ToiletPlace,
} from '../../../../../service/components/da';

interface OrderInfoProps {
  serviceData: any;
  data: any;
  customer: any;
  cateServices: any[];
  faq: any[];
}

export const OrderInfo: React.FC<OrderInfoProps> = ({
  serviceData,
  data,
  customer,
  cateServices,
  faq,
}) => {
  const serviceNames = useMemo(() => {
    if (!cateServices || !serviceData?.CateServicesId) return '';
    return cateServices
      ?.filter((e: any) => serviceData.CateServicesId?.includes(e.Id))
      .map((item: any) => item.Name)
      .join(', ');
  }, [cateServices, serviceData]);

  const faqNames = useMemo(() => {
    return faq
      .map((e: any) => (e?.Content?.length ? e?.Content : (e?.Name ?? '')))
      .join(', ');
  }, [faq]);

  const positionText = useMemo(() => {
    if (!data?.Place) return '';
    return data?.Place === ToiletPlace.outDoor
      ? 'Ngoài trời'
      : 'Gắn liền toà nhà';
  }, [data?.Place]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Thông tin đơn hàng</Text>
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          Đơn hàng: {serviceData?.Name ?? '-'}
        </Text>
        <Text style={styles.infoText}>
          Ngày đăng ký:{' '}
          {serviceData?.DateCreated &&
            Ultis.datetoString(new Date(serviceData?.DateCreated))}
        </Text>
        <Text style={styles.infoText}>Dịch vụ: {serviceNames}</Text>
        <Text style={styles.infoText}>
          Tên khách hàng: {customer.data?.Name}
        </Text>
        <Text style={styles.infoText}>
          Số điện thoại: {serviceData?.CustomerMobile}
        </Text>
        <Text style={styles.infoText}>
          Địa chỉ: {data?.Address ?? customer?.data?.Address}
        </Text>
        {data && <Text style={styles.infoText}>Vị trí: {positionText}</Text>}
        <Text style={styles.infoText}>
          Mô tả: {serviceData?.Description ?? '-'}
        </Text>
        {serviceData?.FAQId && (
          <Text style={styles.infoText}>Vấn đề gặp phải: {faqNames}</Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  title: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  infoContainer: {
    gap: 4,
  },
  infoText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
  },
});
