import {
  Text,
  type TextStyle,
  type ViewStyle,
  TouchableOpacity,
} from 'react-native';

interface Props {
  title: string | React.ReactNode;
  textStyles?: TextStyle;
  styles?: ViewStyle;
  onPress?: () => void;
}

const HashTag = (props: Props) => {
  const {title, textStyles, styles} = props;
  return (
    <TouchableOpacity
      style={{
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 12,
        margin: 4,
        alignSelf: 'flex-start',
        backgroundColor: '#F4F4F5',
        borderRadius: 100,
        ...styles,
      }}
      onPress={props.onPress}>
      {title && typeof title === 'string' ? (
        <Text
          style={{
            fontSize: 14,
            color: '#161C24',
            ...textStyles,
          }}>
          {title}
        </Text>
      ) : (
        title
      )}
    </TouchableOpacity>
  );
};

export default HashTag;
