import React from 'react';
import {SafeAreaView, StyleSheet} from 'react-native';
import {FDialog} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import {useCompanyData, useCompanyRegistration} from './hooks';
import {
  NoCompanyView,
  CompanyInfoView,
  CompanyRegistrationForm,
  LoadingView,
} from './components';
import TitleHeader from 'screen/layout/headers/TitleHeader';

const CompanyInfoPage = () => {
  const {
    companyProfile,
    user,
    userRole,
    isLoading,
    bankList,
    refreshCompanyData,
  } = useCompanyData();
  const {
    isRegistering,
    dialogRef,
    methods,
    handleRegisterCompany,
    handleEditCompany,
    handleCancelRegistration,
    onSubmit,
    onError,
  } = useCompanyRegistration(user, companyProfile, refreshCompanyData);

  return (
    <SafeAreaView style={styles.container}>
      <FDialog ref={dialogRef} />
      <TitleHeader
        title={
          isRegistering ? 'Đăng ký doanh nghiệp' : 'Thông tin doanh nghiệp'
        }
      />

      {isLoading ? (
        <LoadingView />
      ) : isRegistering ? (
        <CompanyRegistrationForm
          methods={methods}
          bankList={bankList}
          onCancel={handleCancelRegistration}
          onSubmit={() => methods.handleSubmit(onSubmit, onError)()}
        />
      ) : !user?.CompanyProfileId || !companyProfile ? (
        <NoCompanyView onRegister={handleRegisterCompany} />
      ) : (
        <CompanyInfoView
          companyProfile={companyProfile}
          userRole={userRole}
          onEdit={handleEditCompany}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  backButton: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CompanyInfoPage;
