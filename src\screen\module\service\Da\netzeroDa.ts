import {DataController} from '../../../base-controller';
import {randomGID} from '../../../../utils/Utils';
import {CustomerType} from 'config/Contanst';

export class NetZeroDa {
  toiletServiceController: DataController;
  toiletOperationalController: DataController;
  toiletServiceCriterionController: DataController;
  toilet: DataController;
  CateServices: DataController;
  customerController: DataController;
  constructor() {
    this.toiletServiceController = new DataController('ToiletServices');
    this.toiletOperationalController = new DataController('ToiletOperational');
    this.toiletServiceCriterionController = new DataController(
      'ToiletServiceCriterion',
    );
    this.toilet = new DataController('Toilet');
    this.CateServices = new DataController('CateServices');
    this.customerController = new DataController('Customer');
  }

  create = async (data: any) => {
    const dataCreate = {
      ...data,
      Id: randomGID(),
      DateCreated: Date.now(),
    };
    const res = await this.toiletServiceController.add([dataCreate]);
    if (res.code === 200) {
      return true;
    }
    return false;
  };
  getServiceManager = async (customer: string, cusType: number) => {
    try {
      let res: any;
      if (cusType == 1) {
        // Customer type 1: Lấy theo CustomerId
        res = await this.toiletServiceController.getPatternList({
          query: `@CustomerId: {${customer}}`,
          pattern: {
            CateServicesId: ['Id', 'Name'],
            ToiletId: ['Id', 'Name'],
          },
        });
      }
      if (res && res.code === 200) {
        let dataMap = res.data.map((item: any) => {
          // Với getPatternList, pattern data sẽ có key tương ứng với pattern name (bỏ "Id" suffix)
          item.CateServices = res.CateServices?.find(
            (cate: any) => cate.Id === item.CateServicesId,
          );
          item.Toilet = res.Toilet?.find(
            (toilet: any) => toilet.Id === item.ToiletId,
          );
          return item;
        });
        return {
          data: dataMap,
          code: 200,
        };
      }

      return {data: [], code: res?.code || 500};
    } catch (error) {
      console.error('Error in getServiceManager:', error);
      return {data: [], code: 500};
    }
  };
  getNestZeroService = async () => {
    const res = await this.CateServices.getListSimple({
      query: `@Name: "Netzero"`,
    });
    if (res?.code === 200) {
      return res;
    }
    return null;
  };

  createToiletOperational = async (data: any[]) => {
    const res = await this.toiletOperationalController.add(data);
    if (res.code === 200) {
      return res;
    }
    return null;
  };
  createAcceptanceByToiletId = async (
    toiletId: string[],
    ToiletServicesId: string,
    CateCriterionId: string,
  ) => {
    try {
      const array: any[] = [];
      let toiletInfo = await this.toilet.getByListId(toiletId);
      if (!toiletInfo?.data) {
        return null;
      }
      {
        toiletInfo?.data.forEach(async (item: any) => {
          array.push({
            Id: randomGID(),
            DateCreated: Date.now(),
            ToiletId: item.Id,
            Name: `Nvs ${item.Name}`,
            Type: 2,
            Status: 1,
            ToiletServicesId: ToiletServicesId,
            CateCriterionId: CateCriterionId,
          });
        });
      }
      const res = await this.toiletServiceCriterionController.add(array);
      if (res.code === 200) {
        return res;
      }
      return null;
    } catch (error) {
      console.error('Error in createAcceptanceByToiletId:', error);
      return null;
    }
  };
  findCustomner = async () => {
    const res = await this.customerController.getListSimple({
      page: 1,
      size: 10000,
      query: `@Type:[2 2]`,
    });
    if (res?.code === 200) {
      return res;
    }
    return null;
  };
}
