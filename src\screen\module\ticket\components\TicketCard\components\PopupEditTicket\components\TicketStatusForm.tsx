import React from 'react';
import {Fselect1Form} from 'project-component/component-form';
import {TicketStatus} from 'screen/module/service/components/da';

interface TicketStatusFormProps {
  methods: any;
  editable: boolean;
  listStatus: Array<any>;
}

export const TicketStatusForm: React.FC<TicketStatusFormProps> = ({
  methods,
  editable,
  listStatus,
}) => {
  return (
    <Fselect1Form
      label="Trạng thái"
      placeholder="Đổi trạng thái"
      control={methods.control}
      disabled={!editable}
      errors={methods.formState.errors}
      onChange={vl => {
        if (vl.id === TicketStatus.cancel) {
          methods.setValue('Ktx', true);
        } else {
          methods.setValue('Ktx', undefined);
        }
      }}
      name="Status"
      options={listStatus.map((e: any) => ({
        id: e.id,
        name: e.name,
      }))}
    />
  );
};
