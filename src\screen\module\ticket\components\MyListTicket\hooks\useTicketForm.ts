import {useRef, useState} from 'react';
import {useForm} from 'react-hook-form';
import {DataController} from 'screen/base-controller';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {
  TicketStatus,
  TicketType as TicketTypeEnum,
} from 'screen/module/service/components/da';
import {TicketType} from 'types/ticketType';
import {randomGID} from 'utils/Utils';
import {BaseDA} from 'screen/baseDA';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {closePopup} from 'wini-mobile-components';

export const useTicketForm = (
  onDone: (value: TicketType) => void,
  parentPopupRef?: any,
) => {
  const user = useSelectorCustomerState().data;
  const [isLoading, setLoading] = useState(false);
  const popupRef = useRef<any>();

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      Type: `${TicketTypeEnum.feedback}`,
      Status: TicketStatus.init,
    },
  });

  const onSubmit = async (ev: any) => {
    setLoading(true);

    try {
      let File;
      if (ev.Files?.length) {
        const res = await BaseDA.uploadFiles(ev.Files);
        if (res?.length) File = res.map((e: any) => e.Id).join(',');
      }

      delete ev.Files;

      const newTicket = {
        ...ev,
        DateCreated: Date.now(),
        Sort: 1,
        File: File,
        CustomerId: user?.Id,
        Type: ev.Type ? parseInt(ev.Type) : undefined,
        Ktx: true, // Default for KTX tickets
      };

      const ticketController = new DataController('Ticket');
      const res = await ticketController.add([newTicket]);

      if (res.code !== 200) {
        return showSnackbar({
          message: res.message,
          status: ComponentStatus.ERROR,
        });
      }

      // Close the parent popup if provided, otherwise close the internal popup
      if (parentPopupRef) {
        closePopup(parentPopupRef);
      } else {
        closePopup(popupRef);
      }

      showSnackbar({
        message: 'Gửi yêu cầu thành công',
        status: ComponentStatus.SUCCSESS,
      });

      if (onDone) onDone(newTicket);
    } catch (error) {
      showSnackbar({
        message: 'Có lỗi xảy ra khi gửi yêu cầu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const onError = (ev: any) => {
    console.log('Form validation error:', ev);
  };

  return {
    methods,
    popupRef,
    isLoading,
    onSubmit,
    onError,
  };
};
