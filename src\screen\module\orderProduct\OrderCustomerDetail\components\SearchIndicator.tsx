import React from 'react';
import {View, ActivityIndicator} from 'react-native';
import {SearchIndicatorProps} from '../types';

const SearchIndicator: React.FC<SearchIndicatorProps> = ({isVisible}) => {
  if (!isVisible) return null;

  return (
    <View
      style={{
        position: 'absolute',
        right: 20,
        top: '50%',
        transform: [{translateY: -10}],
      }}>
      <ActivityIndicator size="small" color="#0000ff" />
    </View>
  );
};

export default SearchIndicator;
