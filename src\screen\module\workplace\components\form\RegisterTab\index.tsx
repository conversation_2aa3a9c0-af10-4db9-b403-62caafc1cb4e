import {
  KeyboardAvoidingView,
  Platform,
  Pressable,
  RefreshControl,
  ScrollView,
  View,
  StyleSheet,
} from 'react-native';
import {useRef} from 'react';
import {ToiletServiceStatus} from 'screen/module/service/components/da';
import {FDialog} from 'component/export-component';
import {FPopup} from 'component/popup/popup';
import FLoading from 'component/Loading/FLoading';
import {ButtonViewRejectReason} from 'screen/module/workplace/components/popup/DialogCustomize';
import {OrderInfo, SurveyInfo, SurveyForm, ActionButtons} from './components';
import {useRegisterTab} from './hooks/useRegisterTab';
import WScreenFooter from 'screen/layout/footer';

interface RegisterTabProps {
  data: any;
  serviceData: any;
  onSubmit: () => void;
  onReject: () => void;
  setServiceData: (data: any) => void;
  isRefreshing: boolean;
  onRefreshing: () => void;
  customer: any;
  methodParent: any;
}

export default function RegisterTab({
  data,
  serviceData,
  onSubmit,
  onReject,
  setServiceData,
  isRefreshing,
  onRefreshing,
  customer,
  methodParent,
}: RegisterTabProps) {
  // Use custom hook for all logic
  const {
    setSurveyData,
    faq,
    isLoading,
    setLoading,
    methods,
    cateServices,
    isEditable,
    rejectReasons,
    initSurvey,
    getSurveyData,
    uploadFiles,
  } = useRegisterTab({data, serviceData, methodParent});

  const dialogRef = useRef<any>();
  const popupRef = useRef<any>();

  const renderTabData = () => {
    if (!data || !serviceData) return null;
    if (serviceData?.Status >= ToiletServiceStatus.research) {
      return (
        <Pressable style={styles.surveySection}>
          {isEditable &&
          serviceData?.Status === ToiletServiceStatus.research ? (
            <SurveyForm
              methods={methods}
              serviceData={serviceData}
              uploadFiles={uploadFiles}
              initSurvey={initSurvey}
            />
          ) : (
            <SurveyInfo serviceData={serviceData} />
          )}
        </Pressable>
      );
    }
  };

  return (
    <View style={styles.container}>
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      <FLoading visible={isLoading} />
      {rejectReasons.length > 0 && (
        <ButtonViewRejectReason
          customers={[customer.data]}
          rejectReasons={rejectReasons}
        />
      )}
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 140 : 0}
        style={styles.keyboardAvoidingView}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing ?? false}
              onRefresh={onRefreshing}
            />
          }>
          <Pressable style={styles.content}>
            <OrderInfo
              serviceData={serviceData}
              data={data}
              customer={customer}
              cateServices={cateServices}
              faq={faq}
            />
            {renderTabData()}
          </Pressable>
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter style={styles.footer}>
        <ActionButtons
          dialogRef={dialogRef}
          popupRef={popupRef}
          serviceData={serviceData}
          data={data}
          customer={customer}
          isEditable={isEditable}
          cateServices={cateServices}
          rejectReasons={rejectReasons}
          onReject={onReject}
          onSubmit={onSubmit}
          setServiceData={setServiceData}
          setSurveyData={setSurveyData}
          setLoading={setLoading}
          getSurveyData={getSurveyData}
        />
      </WScreenFooter>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
    height: '100%',
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    gap: 16,
  },
  surveySection: {
    gap: 16,
  },
  footer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
  },
});
