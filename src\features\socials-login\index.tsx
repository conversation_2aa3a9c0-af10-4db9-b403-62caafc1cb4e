// import { useNavigation } from "@react-navigation/native";
// import React, { useState, useEffect } from "react";
// import { Image, Platform, Text, View } from "react-native";
// import { useSelector, useDispatch } from "react-redux";
// import { RootState } from "../../redux/store/store";
// import { RootScreen } from "../../router/router";
// import AppleSignIn from "./AppleSignIn/apple-sign-in";
// import GoogleLogin from "./GoogleSignIn/GoogleSignIn";
// import ScreenHeader from "../../screen/layout/header";
// import MicrosoftSignIn from "./MicrosoftSignIn/microsoft-login";

// export default function SocialLogin() {
//     const navigation = useNavigation<any>()
//     const dispatch = useDispatch<any>()
//     const [isLoading, setLoading] = useState(false)
//     const [isLoadingM, setLoadingM] = useState(false)
//     const [isLoadingG, setLoadingG] = useState(false)
//     const [isLoadingA, setLoadingA] = useState(false)

//     useEffect(() => {
//         if (isLoadingG || isLoadingA || isLoadingM) {
//             setLoading(true)
//         } else {
//             setLoading(false)
//         }
//     }, [isLoadingG, isLoadingA, isLoadingM, isLoading])


//     return <View style={{ flex: 1, alignItems: 'center', width: '100%', backgroundColor: '#ffffff' }}>
//         <ScreenHeader onBack={() => { navigation.goBack() }} />
//         <View
//             style={{
//                 width: "100%",
//                 flex: 1,
//                 paddingHorizontal: 32,
//                 marginTop: 20,
//                 paddingBottom: 32,
//                 justifyContent: 'center',
//                 alignItems: 'center',
//                 gap: 24,
//             }}
//         >
//             <Text style={{ fontSize: 24, fontWeight: 'bold' }}>SOCIAL LOGIN</Text>
//             <GoogleLogin
//                 isLoading={isLoadingG}
//                 onLoading={setLoadingG}
//                 onAuthSuccess={async (value) => {
//                     console.log('===============GoogleLogin=====================');
//                     console.log(value);
//                     console.log('====================================');
//                     if (value?.idToken != null) {
//                         // const res = await AccountController.extendLogin({
//                         //     type: 0,
//                         //     token: value.idToken
//                         // })
//                         // if (res) AccountActions.getInfor(dispatch)
//                     }
//                 }}
//             />
//             <MicrosoftSignIn
//                 isLoading={isLoadingM}
//                 onLoading={setLoadingM}
//                 onAuthSuccess={async (value: any) => {
//                     console.log('===============MicrosoftSignIn=====================');
//                     console.log(value);
//                     console.log('====================================');
//                 }}
//             />
//             {Platform.OS === 'ios' && <AppleSignIn
//                 isLoading={isLoadingA}
//                 onLoading={setLoadingA}
//                 onAuthSuccess={async (value) => {
//                     if (value?.identityToken != null) {
//                         console.log('================AppleSignIn====================');
//                         console.log(value);
//                         console.log('====================================');
//                     }
//                 }}
//             />}
//         </View>
//     </View>
// }