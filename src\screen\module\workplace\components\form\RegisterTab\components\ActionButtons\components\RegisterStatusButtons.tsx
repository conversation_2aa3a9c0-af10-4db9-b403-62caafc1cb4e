import React from 'react';
import {View, StyleSheet} from 'react-native';
import {AssigneeSelectionPopup} from './AssigneeSelectionPopup';
import {showPopup, FPopup} from 'component/popup/popup';
import AppButton from 'component/button';
import {FDialog, showDialog, showSnackbar} from 'component/export-component';

import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';
import {ComponentStatus} from 'wini-mobile-components';
import {CustomerItem} from 'redux/reducers/user/da';

interface RegisterStatusButtonsProps {
  methods: any;
  dialogRef: any;
  popupRef: any;
  onReject: () => void;
  onAcceptOrder: () => void;
}

export const RegisterStatusButtons: React.FC<RegisterStatusButtonsProps> = ({
  methods,
  dialogRef,
  popupRef,
  onReject,
  onAcceptOrder,
}) => {
  const showAssigneePopup = () => {
    if (popupRef?.current) {
      showPopup({
        ref: popupRef,
        children: (
          <AssigneeSelectionPopup
            methods={methods}
            popupRef={popupRef}
            onAcceptOrder={onAcceptOrder}
          />
        ),
      });
    } else {
      showSnackbar({
        message: 'Không có tư vấn viên khả dụng',
        status: ComponentStatus.WARNING,
      });
    }
  };

  return (
    <View style={styles.buttonRow}>
      <AppButton
        title={'Từ chối'}
        backgroundColor={ColorThemes.light.neutral_main_background_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={() => {
          showDialog({
            ref: dialogRef,
            title: 'Bạn chắc chắn muốn từ chối đơn hàng này?',
            content: 'Đơn hàng sẽ được chuyển tiếp sang tư vấn viên khác.',
            onSubmit: onReject,
          });
        }}
        textColor={ColorThemes.light.neutral_text_subtitle_color}
      />
      <AppButton
        title={'Tiếp nhận'}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={showAssigneePopup}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
    </View>
  );
};

const styles = StyleSheet.create({
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
    flex: 1,
  },
  button: {
    ...TypoSkin.subtitle4,
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingVertical: 5,
  },
});
