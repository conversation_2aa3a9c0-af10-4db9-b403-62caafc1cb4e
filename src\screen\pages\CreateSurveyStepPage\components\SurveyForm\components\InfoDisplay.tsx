import {AppSvg} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {View, Text, StyleSheet} from 'react-native';

const renderIcon = (iconName: string) => {
  return (
    <View style={styles.iconContainer}>
      <AppSvg SvgSrc={iconName} size={22} color="#4CAF50" />
    </View>
  );
};
// Component to display customer information
const InfoDisplay = ({
  icon,
  label,
  value,
}: {
  icon: string;
  label: string;
  value?: string;
}) => (
  <View
    style={[
      {
        marginTop: 12,
        paddingBottom: 4,
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderColor: ColorThemes.light.primary_border_color,
      },
    ]}>
    {renderIcon(icon)}
    <View style={{flex: 1}}>
      <Text
        style={{
          fontSize: 16,
          color: value
            ? ColorThemes.light.neutral_text_body_color
            : ColorThemes.light.neutral_text_subtitle_color,
        }}>
        {value || `Chưa có ${label.toLowerCase()}`}
      </Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
});

export default InfoDisplay;
