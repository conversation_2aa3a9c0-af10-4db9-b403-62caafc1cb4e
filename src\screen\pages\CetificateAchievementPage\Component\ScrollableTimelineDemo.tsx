import React from 'react';
import {View, Text, StyleSheet, FlatList, ScrollView} from 'react-native';
import {Winicon} from '../../../../component/export-component';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

const ScrollableTimelineDemo: React.FC = () => {
  // Mock data để test scroll
  const mockData = [
    {
      Id: '1',
      Name: 'Đăng ký tiêu chí sạch - xanh - tuần hoàn',
      DateCreated: new Date().toISOString(),
      ProgressStatus: 2,
    },
    {
      Id: '2', 
      Name: 'Kiểm tra và đánh giá cơ sở vật chất toilet',
      DateCreated: new Date(Date.now() - 86400000).toISOString(),
      ProgressStatus: 2,
    },
    {
      Id: '3',
      Name: '<PERSON>hự<PERSON> hiện cải thiện theo yêu cầu tiêu chí môi trường',
      DateCreated: new Date(Date.now() - 172800000).toISOString(),
      ProgressStatus: 1,
    },
    {
      Id: '4',
      Name: 'Kiểm tra lại và xác nhận đạt tiêu chuẩn',
      DateCreated: new Date(Date.now() - 259200000).toISOString(),
      ProgressStatus: 0,
    },
    {
      Id: '5',
      Name: 'Cấp chứng nhận sạch - xanh - tuần hoàn',
      DateCreated: new Date(Date.now() - 345600000).toISOString(),
      ProgressStatus: 0,
    },
    {
      Id: '6',
      Name: 'Theo dõi và duy trì tiêu chuẩn định kỳ hàng tháng',
      DateCreated: new Date(Date.now() - 432000000).toISOString(),
      ProgressStatus: 0,
    },
    {
      Id: '7',
      Name: 'Đánh giá lại và gia hạn chứng nhận hàng năm',
      DateCreated: new Date(Date.now() - 518400000).toISOString(),
      ProgressStatus: 0,
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 2: return 'Hoàn thành';
      case 1: return 'Đang thực hiện';
      case 0: return 'Chờ thực hiện';
      default: return 'Không xác định';
    }
  };

  const renderTimelineItem = ({item, index}: {item: any; index: number}) => (
    <View style={styles.timelineItem}>
      <View style={styles.leftSection}>
        <Text style={styles.dateText}>
          {formatDate(item.DateCreated)}
        </Text>
      </View>
      
      <View style={styles.centerSection}>
        <View
          style={[
            styles.dot,
            item.ProgressStatus === 2 && styles.completedDot,
            item.ProgressStatus === 1 && styles.ProcessingDot,
            item.ProgressStatus === 0 && styles.pendingDot,
          ]}>
          {item.ProgressStatus === 2 && (
            <Winicon
              src="outline/user interface/check"
              size={12}
              color="white"
            />
          )}
        </View>
        {index < mockData.length - 1 && <View style={styles.line} />}
      </View>
      
      <View style={styles.rightSection}>
        <Text style={styles.titleText}>{item.Name}</Text>
        <Text
          style={[
            styles.statusText,
            item.ProgressStatus === 2
              ? styles.completedStatus
              : item.ProgressStatus === 1
              ? styles.processingStatus
              : styles.pendingStatus,
          ]}>
          {getStatusText(item.ProgressStatus)}
        </Text>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Demo Timeline có thể Scroll</Text>
      
      <View style={styles.timelineContainer}>
        <Text style={styles.progressTitle}>
          Tiến trình đăng ký chứng nhận
        </Text>
        
        <FlatList
          data={mockData}
          renderItem={renderTimelineItem}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={true}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          contentContainerStyle={{flexGrow: 1}}
          style={styles.flatList}
        />
      </View>
      
      <View style={styles.infoBox}>
        <Text style={styles.infoTitle}>Cải thiện đã thực hiện:</Text>
        <Text style={styles.infoText}>✅ scrollEnabled={'{true}'} - Cho phép scroll</Text>
        <Text style={styles.infoText}>✅ showsVerticalScrollIndicator={'{true}'} - Hiển thị thanh scroll</Text>
        <Text style={styles.infoText}>✅ nestedScrollEnabled={'{true}'} - Hỗ trợ nested scroll</Text>
        <Text style={styles.infoText}>✅ contentContainerStyle - Đảm bảo content có thể grow</Text>
        <Text style={styles.infoText}>✅ flexDirection: 'column' - Text có thể xuống dòng</Text>
        <Text style={styles.infoText}>✅ flexWrap: 'wrap' - Cho phép wrap text</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 20,
    color: ColorThemes.light.neutral_text_title_color,
  },
  timelineContainer: {
    marginHorizontal: 16,
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressTitle: {
    ...TypoSkin.buttonText2,
    color: '#333',
    lineHeight: 24,
    paddingBottom: 20,
    textAlign: 'center',
  },
  flatList: {
    maxHeight: 400, // Giới hạn chiều cao để test scroll
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  leftSection: {
    width: 80,
    alignItems: 'flex-end',
    paddingRight: 16,
    paddingTop: 2,
  },
  dateText: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  centerSection: {
    alignItems: 'center',
    width: 20,
  },
  dot: {
    width: 20,
    height: 20,
    borderRadius: 20,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedDot: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
  },
  pendingDot: {
    backgroundColor: '#e0e0e0',
    borderRadius: 20,
  },
  ProcessingDot: {
    borderWidth: 1,
    borderColor: '#2EB553',
    backgroundColor: '#e0e0e0',
    width: 22,
    height: 22,
    borderRadius: 20,
  },
  line: {
    width: 2,
    flex: 1,
    backgroundColor: '#4CAF50',
    marginTop: -8,
    minHeight: 40,
  },
  rightSection: {
    flex: 1,
    paddingLeft: 16,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    paddingBottom: 40,
  },
  titleText: {
    ...TypoSkin.buttonText6,
    color: '#333',
    marginBottom: 4,
    flexWrap: 'wrap',
    lineHeight: 18,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    flexWrap: 'wrap',
    lineHeight: 16,
  },
  completedStatus: {
    color: '#4CAF50',
  },
  processingStatus: {
    color: '#FF9800',
  },
  pendingStatus: {
    color: '#999',
  },
  infoBox: {
    margin: 16,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  infoText: {
    fontSize: 12,
    marginBottom: 6,
    color: '#666',
    fontFamily: 'monospace',
  },
});

export default ScrollableTimelineDemo;
