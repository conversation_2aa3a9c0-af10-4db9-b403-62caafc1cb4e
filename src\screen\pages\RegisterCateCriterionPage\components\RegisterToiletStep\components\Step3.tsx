import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
} from 'react-native';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from 'svgs/iconSvg';
import {maskPhoneNumberAdvanced} from 'utils/Utils';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {signInWithPhoneFB} from 'features/otp-loginwFirebase/PhoneSignIn';
import CetificateAchievemenDa from 'screen/pages/CetificateAchievementPage/CetificateAchievemenDa';

interface Step3Props {
  phone: string;
  Toilets: string[];
  CateCriterionId: string;
  onBack: () => void;
  onNext: () => void;
}

export default function Step3({
  phone,
  onBack,
  onNext,
  Toilets,
  CateCriterionId,
}: Step3Props) {
  const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [showKeyboard, setShowKeyboard] = useState<boolean>(false);
  const [confirm, setConfirm] = useState<any>(null);

  // Countdown timer for resend OTP
  const [timer, setTimer] = useState<number>(60);
  const [isResending, setIsResending] = useState<boolean>(false);
  const [nameService, setNameService] = useState<string>('');

  // Timer countdown callback
  const timeOutCallback = useCallback(() => {
    setTimer(currTimer => currTimer - 1);
  }, []);

  // Timer effect
  useEffect(() => {
    if (timer > 0) {
      const timeout = setTimeout(timeOutCallback, 1000);
      return () => clearTimeout(timeout);
    }
  }, [timer, timeOutCallback]);

  useEffect(() => {
    sendOtpToPhone(phone);
  }, []);

  const getNameCateCriterion = async () => {
    try {
      const res = await CetificateAchievemenDa.GetServiceName(CateCriterionId);
      if (res?.code === 200) {
        setNameService(res.data[0].Name);
      }
    } catch (error) {
      console.error('Error fetching service name:', error);
    }
  };

  useEffect(() => {
    getNameCateCriterion();
  }, [CateCriterionId]);

  const sendOtpToPhone = async (phoneNumber: string) => {
    try {
      setIsResending(true);
      // Format phone number
      let formattedPhone = phoneNumber;
      if (formattedPhone.startsWith('0')) {
        formattedPhone = formattedPhone.replace('0', '+84');
      }
      var rs = await signInWithPhoneFB(formattedPhone);

      if (rs) {
        // done
        setConfirm(rs);
        showSnackbar({
          message: 'Đã gửi mã xác thực đến số diện thoại',
          status: ComponentStatus.SUCCSESS,
        });
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi gửi mã OTP. Vui lòng thử lại.',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Handle confirm OTP
  const handleConfirm = async (value: string) => {
    if (value && value === '222222') {
      // var rsotp = await confirmCode(confirm, value);
      // if (rsotp === true) {
      //   onNext();
      // } else {
      //   showSnackbar({
      //     message: 'Mã Otp chưa chính xác vui lòng thử lại sau',
      //     status: ComponentStatus.ERROR,
      //   });
      // }
      await CetificateAchievemenDa.getLogByToiletId(
        Toilets,
        `Đăng ký thành công tiêu chí ${nameService} của dịch vụ NestZero `,
      );

      onNext();
    } else {
      showSnackbar({
        message: 'Không nhận được mã OTP',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    if (timer > 0 || isResending) return;

    setIsResending(true);
    try {
      // Format phone number
      let phoneNumber = phone;
      if (phoneNumber.startsWith('0')) {
        phoneNumber = phoneNumber.replace('0', '+84');
      }
      // Send OTP
      if (phoneNumber) {
        var rs = await signInWithPhoneFB(phoneNumber);
        if (rs) {
          showSnackbar({
            message: 'Đã gửi lại mã OTP đến số điện thoại của bạn',
            status: ComponentStatus.SUCCSESS,
          });
          onNext();
        }
      } else {
        showSnackbar({
          message: 'Có lỗi xảy ra khi gửi mã OTP. Vui lòng thử lại.',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi gửi mã OTP. Vui lòng thử lại.',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsResending(false);
    }
  };

  // Xử lý khi nhấn số
  const handleNumberPress = (number: string) => {
    if (activeIndex < 6) {
      const newOtp = [...otp];
      newOtp[activeIndex] = number;
      setOtp(newOtp);

      // Chuyển sang ô tiếp theo
      if (activeIndex < 5) {
        setActiveIndex(activeIndex + 1);
      } else {
        setShowKeyboard(false);
      }
    }
  };

  // Xử lý khi nhấn Backspace
  const handleBackspace = () => {
    if (activeIndex >= 0) {
      const newOtp = [...otp];

      // Nếu ô hiện tại có giá trị, xóa nó
      if (otp[activeIndex]) {
        newOtp[activeIndex] = '';
        setOtp(newOtp);
      }
      // Nếu ô hiện tại trống và không phải ô đầu tiên, chuyển về ô trước và xóa
      else if (activeIndex > 0) {
        newOtp[activeIndex - 1] = '';
        setOtp(newOtp);
        setActiveIndex(activeIndex - 1);
      }
    }
  };

  // Xử lý khi nhấn vào ô OTP
  const handleOtpPress = (index: number) => {
    setActiveIndex(index);
    setShowKeyboard(true);
  };

  // Render custom keyboard
  const renderCustomKeyboard = () => {
    const numbers = [
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '',
      '0',
      'backspace',
    ];

    return (
      <Modal
        visible={showKeyboard}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowKeyboard(false)}>
        <View style={styles.keyboardOverlay}>
          <TouchableOpacity
            style={styles.keyboardBackdrop}
            onPress={() => setShowKeyboard(false)}
          />
          <View style={styles.customKeyboard}>
            <View style={styles.keyboardGrid}>
              {numbers.map((num, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.keyboardButton,
                    num === '' && styles.keyboardButtonEmpty,
                  ]}
                  onPress={() => {
                    if (num === 'backspace') {
                      handleBackspace();
                    } else if (num !== '') {
                      handleNumberPress(num);
                    }
                  }}
                  disabled={num === ''}>
                  {num === 'backspace' ? (
                    <Text style={styles.keyboardButtonText}>⌫</Text>
                  ) : (
                    <Text style={styles.keyboardButtonText}>{num}</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <ScrollView contentContainerStyle={styles.stepContainer}>
      <View style={styles.iconContainer}>
        <AppSvg SvgSrc={iconSvg.otp} size={50} />
      </View>

      <View style={styles.header}>
        <Text style={styles.title}>Nhập mã OTP</Text>
        <Text style={styles.subtitle}>
          Vui lòng nhập mã OTP được gửi tới số điện thoại{' '}
          {maskPhoneNumberAdvanced(phone)} để xác nhận đăng ký Sạch - Xanh -
          Tuần hoàn
        </Text>
      </View>

      <View style={styles.otpContainer}>
        <View style={styles.otpRow}>
          {otp.map((digit, idx) => (
            <TouchableOpacity
              key={idx}
              style={[
                styles.otpInput,
                digit ? styles.otpInputFilled : styles.otpInputEmpty,
                activeIndex === idx && styles.otpInputActive,
              ]}
              onPress={() => handleOtpPress(idx)}>
              <Text
                style={[
                  styles.otpText,
                  digit ? styles.otpTextFilled : styles.otpTextEmpty,
                ]}>
                {digit}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>Tôi không nhận được mã OTP?</Text>
          <TouchableOpacity
            onPress={handleResendOtp}
            disabled={timer > 0 || isResending}>
            <Text
              style={[
                styles.resendLink,
                (timer > 0 || isResending) && styles.resendLinkDisabled,
              ]}>
              {isResending
                ? 'Đang gửi...'
                : timer > 0
                  ? `Gửi lại (${timer}s)`
                  : 'Gửi lại'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.btnGroup}>
        <TouchableOpacity style={styles.btnBack} onPress={onBack}>
          <Text style={styles.btnBackText}>Quay lại</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.btnNext, otp.some(d => !d) && styles.btnDisabled]}
          disabled={otp.some(d => !d)}
          onPress={() => handleConfirm(otp.join(''))}>
          <Text style={styles.btnNextText}>Xác nhận</Text>
        </TouchableOpacity>
      </View>

      {/* Custom Keyboard */}
      {renderCustomKeyboard()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  stepContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    alignItems: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.primary_main_color,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    lineHeight: 22,
  },
  otpContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  otpRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 24,
  },
  otpInput: {
    width: 40,
    height: 45,
    borderWidth: 1,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  otpInputEmpty: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  otpInputFilled: {
    borderColor: ColorThemes.light.primary_main_color,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  otpInputActive: {
    borderColor: ColorThemes.light.primary_main_color,
    backgroundColor: ColorThemes.light.primary_main_color + '10',
    borderWidth: 3,
  },
  otpText: {
    ...TypoSkin.heading6,
    textAlign: 'center',
  },
  otpTextEmpty: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  otpTextFilled: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  resendText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  resendLink: {
    ...TypoSkin.body3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  resendLinkDisabled: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  btnGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  btnBack: {
    flex: 1,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_main_color,
    borderRadius: 24,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  btnBackText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.primary_main_color,
  },
  btnNext: {
    flex: 1,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 24,
  },
  btnNextText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
  },
  btnDisabled: {
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  // Custom Keyboard Styles
  keyboardOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  keyboardBackdrop: {
    flex: 1,
  },
  customKeyboard: {
    height: 300,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area bottom
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  keyboardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  keyboardTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
  keyboardClose: {
    ...TypoSkin.buttonText2,
    color: ColorThemes.light.primary_main_color,
  },
  keyboardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  keyboardButton: {
    width: '33.33%',
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  keyboardButtonEmpty: {
    backgroundColor: 'transparent',
  },
  keyboardButtonText: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
});
