import { Pressable, Text, View } from "react-native";
import { ColorThemes } from "../../../../../assets/skin/colors";
import { TypoSkin } from "../../../../../assets/skin/typography";
import { SkeletonImage } from "../../../../../project-component/skeleton-img";
import AppButton from "../../../../../component/button";

interface Props {
    methods?: any;
    forConsultant?: boolean;
    display?: string;
    setStep?: (value: any) => void
}

export default function FindRepairDevices(props: Props) {
    const { methods, forConsultant = false, display = "flex", setStep } = props;
    return <View style={{ padding: 16, display: display === "flex" ? 'flex' : 'none' }}>
        <Pressable
            style={{
                borderColor: 'transparent',
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
            }}>
            <Text
                style={[
                    TypoSkin.heading5,
                    {
                        color: ColorThemes.light.neutral_text_title_color,
                        paddingBottom: 4,
                    },
                ]}>
                T<PERSON><PERSON> thiết bị thay thế phù hợp với bạn
            </Text>
            <Text
                style={[
                    TypoSkin.body3,
                    {
                        color: ColorThemes.light.neutral_text_body_color,
                        paddingBottom: 8,
                    },
                ]}>
                Bạn có thể tự chọn sản phẩm thay thế từ các nhà cung cấp thiết bị uy tín của chúng tôi
            </Text>
        </Pressable>
        <View style={{ flexDirection: "row", justifyContent: "center", width: "100%", marginVertical: 16 }}>
            <SkeletonImage source={{ uri: "https://file-mamager.wini.vn/Upload/2024/12/chicken_8307.png" }}
                style={{ width: 265, height: 265, objectFit: "cover" }}
            />
        </View>
        <View style={{ flexDirection: "row", justifyContent: "center", gap: 8, width: "100%" }}>
            <AppButton
                title={'Tư vấn cho tôi'}
                backgroundColor={ColorThemes.light.neutral_main_background_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={() => {
                    methods.setValue("extend-step", null)
                    if (setStep) setStep((st: number) => st + 1)
                }}
                textColor={ColorThemes.light.neutral_text_subtitle_color}
            />
            <AppButton
                title={'Tôi tự tìm'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={() => {
                    methods.setValue("extend-step", true)
                    if (setStep) setStep((st: number) => st + 1)
                }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
            />
        </View>
    </View>
}