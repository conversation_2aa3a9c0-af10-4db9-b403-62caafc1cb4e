import React from 'react';
import { View, StyleSheet } from 'react-native';
import { FTextField, Winicon } from '../../../../../component/export-component';
import { ColorThemes } from '../../../../../assets/skin/colors';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = 'Tìm kiếm'
}) => {
  return (
    <View style={styles.container}>
      <FTextField
        style={styles.textField}
        onChange={(vl) => {
          onChange(vl.trim());
        }}
        value={value}
        placeholder={placeholder}
        prefix={
          <Winicon
            src="outline/development/zoom"
            size={14}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  textField: {
    paddingHorizontal: 16,
    flex: 1,
    height: 40,
  },
});
