import {PayloadAction, createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {useDispatch} from 'react-redux';
import {AppDispatch, RootState} from '../../store/store';
import {OrderProductDA} from '../../../screen/module/orderProduct/orderProductDA';
import {getImage} from '../../actions/rootAction';
import {showSnackbar} from '../../../component/export-component';
import {ComponentStatus} from '../../../component/component-status';

interface OrderData {
  data: any[];
  number: number;
}

interface OrderStoreState {
  orderInfo: {
    NewOrder: OrderData;
    ProcessOrder: OrderData;
    DoneOrder: OrderData;
    CancelOrder: OrderData;
  };
  loading: boolean;
  error: string | null;
}

const initialState: OrderStoreState = {
  orderInfo: {
    NewOrder: {data: [], number: 0},
    ProcessOrder: {data: [], number: 0},
    DoneOrder: {data: [], number: 0},
    CancelOrder: {data: [], number: 0},
  },
  loading: false,
  error: null,
};

// Async thunk để lấy tất cả đơn hàng theo shopId
export const fetchAllOrdersByShopId = createAsyncThunk<
  {
    NewOrder: OrderData;
    ProcessOrder: OrderData;
    DoneOrder: OrderData;
    CancelOrder: OrderData;
  },
  string,
  {state: RootState}
>('order/fetchAllOrdersByShopId', async (shopId, thunkAPI) => {
  try {
    const orderProductDA = new OrderProductDA();
    const response = await orderProductDA.getAllOrdersByShopId(shopId);

    if (response?.code === 200) {
      console.log('check-response-------------', response);
      // Lấy ảnh cho các đơn hàng
      const dataWithImages = await getImage({items: response.data});

      // Lọc đơn hàng theo trạng thái
      const newOrder = dataWithImages.filter(
        (order: any) => order.Status === 1,
      );
      const processingOrder = dataWithImages.filter(
        (order: any) => order.Status === 2,
      );
      const doneOrder = dataWithImages.filter(
        (order: any) => order.Status === 3,
      );
      const cancelOrder = dataWithImages.filter(
        (order: any) => order.Status === 4,
      );

      return {
        NewOrder: {data: newOrder, number: newOrder.length},
        ProcessOrder: {data: processingOrder, number: processingOrder.length},
        DoneOrder: {data: doneOrder, number: doneOrder.length},
        CancelOrder: {data: cancelOrder, number: cancelOrder.length},
      };
    } else {
      return thunkAPI.rejectWithValue(
        response?.message || 'Không thể lấy danh sách đơn hàng',
      );
    }
  } catch (error: any) {
    const errorMessage =
      error.message || 'Có lỗi xảy ra khi lấy danh sách đơn hàng';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

// Async thunk để lấy đơn hàng theo shopId và status
export const fetchOrdersByShopIdAndStatus = createAsyncThunk<
  any[],
  {shopId: string; status: number},
  {state: RootState}
>('order/fetchOrdersByShopIdAndStatus', async ({shopId, status}, thunkAPI) => {
  try {
    const orderProductDA = new OrderProductDA();
    const response = await orderProductDA.getOrderByShopId(shopId, status);

    if (response?.code === 200) {
      // Lấy ảnh cho các đơn hàng
      const dataWithImages = await getImage({items: response.data});
      return dataWithImages;
    } else {
      return thunkAPI.rejectWithValue(
        response?.message || 'Không thể lấy danh sách đơn hàng',
      );
    }
  } catch (error: any) {
    const errorMessage =
      error.message || 'Có lỗi xảy ra khi lấy danh sách đơn hàng';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setOrderInfo: (
      state,
      action: PayloadAction<OrderStoreState['orderInfo']>,
    ) => {
      state.orderInfo = action.payload;
    },
    updateOrderStatus: (
      state,
      action: PayloadAction<{orderId: string; newStatus: number}>,
    ) => {
      const {orderId, newStatus} = action.payload;

      // Tìm và cập nhật đơn hàng trong tất cả các trạng thái
      Object.keys(state.orderInfo).forEach(key => {
        const orderType = key as keyof typeof state.orderInfo;
        const orderIndex = state.orderInfo[orderType].data.findIndex(
          (order: any) => order.Id === orderId,
        );

        if (orderIndex !== -1) {
          // Xóa đơn hàng khỏi trạng thái cũ
          const [removedOrder] = state.orderInfo[orderType].data.splice(
            orderIndex,
            1,
          );
          state.orderInfo[orderType].number -= 1;

          // Thêm vào trạng thái mới
          removedOrder.Status = newStatus;
          switch (newStatus) {
            case 1:
              state.orderInfo.NewOrder.data.push(removedOrder);
              state.orderInfo.NewOrder.number += 1;
              break;
            case 2:
              state.orderInfo.ProcessOrder.data.push(removedOrder);
              state.orderInfo.ProcessOrder.number += 1;
              break;
            case 3:
              state.orderInfo.DoneOrder.data.push(removedOrder);
              state.orderInfo.DoneOrder.number += 1;
              break;
            case 4:
              state.orderInfo.CancelOrder.data.push(removedOrder);
              state.orderInfo.CancelOrder.number += 1;
              break;
          }
        }
      });
    },
    clearOrders: state => {
      state.orderInfo = {
        NewOrder: {data: [], number: 0},
        ProcessOrder: {data: [], number: 0},
        DoneOrder: {data: [], number: 0},
        CancelOrder: {data: [], number: 0},
      };
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      // fetchAllOrdersByShopId
      .addCase(fetchAllOrdersByShopId.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllOrdersByShopId.fulfilled, (state, action) => {
        state.loading = false;
        state.orderInfo = action.payload;
        state.error = null;
      })
      .addCase(fetchAllOrdersByShopId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // fetchOrdersByShopIdAndStatus
      .addCase(fetchOrdersByShopIdAndStatus.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrdersByShopIdAndStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchOrdersByShopIdAndStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setOrderInfo,
  updateOrderStatus,
  clearOrders,
  setLoading,
  setError,
} = orderSlice.actions;

export default orderSlice.reducer;

// Hook để sử dụng order actions
export const useOrderActions = () => {
  const dispatch = useDispatch<AppDispatch>();

  return {
    fetchAllOrdersByShopId: (shopId: string) =>
      dispatch(fetchAllOrdersByShopId(shopId)),
    fetchOrdersByShopIdAndStatus: (shopId: string, status: number) =>
      dispatch(fetchOrdersByShopIdAndStatus({shopId, status})),
    setOrderInfo: (orderInfo: OrderStoreState['orderInfo']) =>
      dispatch(setOrderInfo(orderInfo)),
    updateOrderStatus: (orderId: string, newStatus: number) =>
      dispatch(updateOrderStatus({orderId, newStatus})),
    clearOrders: () => dispatch(clearOrders()),
    setLoading: (loading: boolean) => dispatch(setLoading(loading)),
    setError: (error: string | null) => dispatch(setError(error)),
  };
};

// Selectors
export const selectOrderInfo = (state: RootState) => state.order.orderInfo;
export const selectOrderLoading = (state: RootState) => state.order.loading;
export const selectOrderError = (state: RootState) => state.order.error;
export const selectNewOrders = (state: RootState) =>
  state.order.orderInfo.NewOrder;
export const selectProcessOrders = (state: RootState) =>
  state.order.orderInfo.ProcessOrder;
export const selectDoneOrders = (state: RootState) =>
  state.order.orderInfo.DoneOrder;
export const selectCancelOrders = (state: RootState) =>
  state.order.orderInfo.CancelOrder;
