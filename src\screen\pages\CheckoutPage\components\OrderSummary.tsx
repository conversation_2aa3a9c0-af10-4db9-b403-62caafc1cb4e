import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import {Ultis} from 'utils/Utils';
import iconSvg from 'svgs/iconSvg';
import {OrderSummaryProps} from '../types';

const OrderSummary: React.FC<OrderSummaryProps> = ({storeGroup}) => {
  const totalReward = storeGroup.items.reduce(
    (sum, item) => sum + (item.reward || 0),
    0,
  );

  const hasFreeShipping = storeGroup.items?.find(item => item?.IsFreeShip);

  return (
    <View style={styles.container}>
      {/* Free Shipping Row */}
      <View style={styles.shippingRow}>
        {hasFreeShipping ? (
          <View style={styles.freeShippingContainer}>
            <AppSvg SvgSrc={iconSvg.delivery} size={20} />
            <Text style={styles.freeShippingText}>Free</Text>
          </View>
        ) : (
          <View />
        )}
      </View>

      {/* Total Price Row */}
      <View style={styles.totalRow}>
        <View style={styles.totalInfo}>
          <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
          <Text style={styles.totalLabel}>
            Tổng tiền ({storeGroup.items.length} sản phẩm):
          </Text>
        </View>
        <Text style={styles.totalPrice}>
          {Ultis.money(storeGroup.totalPrice ?? 0)} đ
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  shippingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  freeShippingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  freeShippingText: {
    ...TypoSkin.body2,
    color: '#3FB993',
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '700',
  },
  rewardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  rewardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rewardLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 8,
  },
  rewardAmount: {
    ...TypoSkin.heading7,
    fontWeight: '700',
    color: '#3FB993',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 16,
  },
  totalInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 8,
  },
  totalPrice: {
    ...TypoSkin.heading7,
    fontWeight: '700',
    color: '#FF3B30',
  },
});

export default OrderSummary;
