import {TypeMenuPorduct} from '../../../config/Contanst';
import {brandAction} from '../../../redux/actions/brandAction';
import {productAction} from '../../../redux/actions/productAction';
import {getImage} from '../../../redux/actions/rootAction';
import {ProductItem} from '../../../types/ProductType';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../base-controller';

class ProductDA {
  private ProductController: DataController;
  private orderDetailController: DataController;
  private ratingController: DataController;
  private cateController: DataController;
  private cateattributeController: DataController;
  private attributeController: DataController;
  private brandController: DataController;
  private consumeController: DataController;
  private colorController: DataController;
  private customerController: DataController;
  private productAttributeController: DataController;
  private productFavoriteController: DataController;
  private shopController: DataController;

  constructor(
    ProductController = new DataController('Product'),
    orderDetailController = new DataController('OrderDetail'),
    ratingController = new DataController('Rating'),
    cateController = new DataController('Category'),
    cateattributeController = new DataController('CateAttribute'),
    attributeController = new DataController('Attribute'),
    brandController = new DataController('Brands'),
    consumeController = new DataController('Consume'),
    colorController = new DataController('Color'),
    customerController = new DataController('Customer'),
    productAttributeController = new DataController('ProductAttribute'),
    productFavoriteController = new DataController('ProductFavorite'),
    shopController = new DataController('Shop'),
  ) {
    this.ProductController = ProductController;
    this.orderDetailController = orderDetailController;
    this.ratingController = ratingController;
    this.cateController = cateController;
    this.cateattributeController = cateattributeController;
    this.attributeController = attributeController;
    this.brandController = brandController;
    this.consumeController = consumeController;
    this.colorController = colorController;
    this.customerController = customerController;
    this.productAttributeController = productAttributeController;
    this.productFavoriteController = productFavoriteController;
    this.shopController = shopController;
  }

  async getProductPopular(pageSize: number) {
    const responseProduct = await this.orderDetailController.group({
      searchRaw: '*',
      reducers: 'LOAD * GROUPBY 1 @ProductId REDUCE COUNT 0 AS CountProduct',
    });

    if (responseProduct.code === 200) {
      var lstProduct = responseProduct.data;
      //sắp xếp lại khóa học sau khi đã count trong đơn hàng để lấy khóa đc mua nhiều nhất. và lấy top 10
      if (lstProduct.length > 0) {
        if (pageSize > 0) {
          lstProduct = [...lstProduct]
            .sort(
              (a, b) =>
                parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
            )
            .slice(0, pageSize);
        } else {
          lstProduct = [...lstProduct].sort(
            (a, b) =>
              parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
          );
        }

        const respone = await this.ProductController.getListSimple({
          page: 1,
          size: 50,
          query: `@Id: {${lstProduct
            .map((item: any) => item.ProductId)
            .join(' | ')}}`,
          returns: [
            'Id',
            'Name',
            'Price',
            'Img',
            'CategoryId',
            'ShopId',
            'Discount',
          ],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      } else {
        const respone = await this.ProductController.getListSimple({
          page: 1,
          size: 20,
          query: '@IsHot: {true}',
          returns: [
            'Id',
            'Name',
            'Price',
            'Img',
            'CategoryId',
            'ShopId',
            'Discount',
          ],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      }
    }
    return null;
  }
  //lấy danh sach sản phẩm hot
  async getProductHot(page?: number, size?: number) {
    const respone = await this.ProductController.getPatternList({
      page: page,
      size: size,
      query: `@IsHot: {true} @Status: [${TypeMenuPorduct.InStock.id}]`,
      returns: [
        'Id',
        'Name',
        'Price',
        'Img',
        'Discount',
        'ShopId',
        'Sold',
        'CategoryId',
      ],
      pattern: {
        ShopId: ['Id', 'Name', 'Avatar', 'Address', 'Mobile', 'Email'],
      },
    });

    if (respone.code === 200) {
      // lấy rating trong ratingController

      let lstId = respone.data.map((item: any) => item.Id);
      lstId = [...new Set(lstId)];
      const ratingResult = await this.ratingController.getListSimple({
        query: `@ProductId: {${lstId.join(' | ')}}`,
      });
      if (ratingResult.code === 200) {
        respone.data.map((item: any) => {
          const rating = ratingResult.data.filter(
            (a: any) => a.ProductId === item.Id,
          );
          item.rating =
            rating.length > 0
              ? rating.reduce((acc: any, cur: any) => acc + cur.Value, 0) /
                rating.length
              : 0;
          return item;
        });
      }

      return respone;
    }
    return null;
  }
  //lấy danh sách sản phẩm bán chạy
  async getProductBestSeller(page?: number, size?: number) {
    const respone = await this.ProductController.getListSimple({
      page: 1,
      size: 50,
      query: `@Sold: [0 +inf] @Status: [${TypeMenuPorduct.InStock.id}]`,
      returns: [
        'Id',
        'Name',
        'Price',
        'Img',
        'Discount',
        'ShopId',
        'Sold',
        'CategoryId',
      ],
      sortby: {BY: 'Sold', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      let lstId = respone.data.map((item: any) => item.Id);
      lstId = [...new Set(lstId)];
      const ratingResult = await this.ratingController.getListSimple({
        query: `@ProductId: {${lstId.join(' | ')}}`,
      });
      if (ratingResult.code === 200) {
        respone.data.map((item: any) => {
          const rating = ratingResult.data.filter(
            (a: any) => a.ProductId === item.Id,
          );
          item.rating =
            rating.length > 0
              ? rating.reduce((acc: any, cur: any) => acc + cur.Value, 0) /
                rating.length
              : 0;
          return item;
        });
      }
      const data = getImage({items: respone.data});
      return data;
    }
    return [];
  }

  async getAllList(
    page: number,
    size: number,
    query: string,
    returns?: Array<string>,
  ) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: query ?? '*',
      returns: returns,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyCategory(
    page?: any,
    size?: any,
    cateId?: string,
    isHot?: boolean,
  ) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: isHot
        ? `@IsHot:{true} @CategoryId:{${cateId}}`
        : `@CategoryId:{${cateId}}`,
      // returns: ['Id', 'Name', 'Price', 'Img', 'CustomerId'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });

    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getAllListbyCustomerId(page: number, size: number, customerId: string) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: `@CustomerId:{${customerId}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getProductDetail(ProductId: string) {
    const respone = await this.ProductController.getPatternList({
      query: `@Id:{${ProductId}}`,
      pattern: {
        ShopId: ['Id', 'Name', 'Address', 'Mobile', 'Email', 'CustomerId'],
        CategoryId: ['Id', 'Name', 'ParentId'],
        BrandId: ['Id', 'Name'],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async filterProductByCategories(
    {filter, pageNumber = 1, size = 10}: any,
    customerId?: string,
  ) {
    const config: any = {
      page: pageNumber,
      size: 10,
      searchRaw: '',
      sortby: [],
      query: '',
    };

    if (filter.categoryId)
      config.searchRaw += `@CategoryId:{${filter.categoryId}}`;

    if (filter.activeFilters.IsHot) config.searchRaw += ` @IsHot:{true}`;
    if (filter.activeFilters.IsFreeShip)
      config.searchRaw += ` @IsFreeShip:{true}`;

    if (filter.maxPrice) {
      config.searchRaw += `@Price:[0 ${filter.maxPrice}] `;
    }

    if (filter.textSearch && filter.textSearch.length)
      config.searchRaw += `(@Name:*${filter.textSearch}*) `;

    if (filter.activeFilters.IsNew)
      config.sortby = [{prop: 'DateCreated', direction: 'DESC'}];

    if (filter.brandId) config.searchRaw += ` @BrandId:{${filter.brandId}}`;

    if (filter.activeFilters.FavoriteBrand) {
      try {
        const brands = await brandAction.fetch({
          searchRaw: `@IsFavorite:{true}`,
        });
        if (brands.length)
          config.searchRaw += ` @BrandId:{${brands
            .map((brand: any) => brand.Id)
            .join(' | ')}}`;
      } catch (brandError) {
        console.error('Error fetching favorite brands:', brandError);
      }
    }

    if (filter.sortOption) {
      switch (filter.sortOption) {
        case 'newest':
          config.sortby = [{prop: 'DateCreated', direction: 'DESC'}];
          break;
        case 'price_asc':
          config.sortby = [{prop: 'Price', direction: 'ASC'}];
          break;
        case 'price_desc':
          config.sortby = [{prop: 'Price', direction: 'DESC'}];
          break;
        default:
          break;
      }
    }

    if (!config.searchRaw?.length) delete config.searchRaw;
    if (!config.sortby?.length) delete config.sortby;
    if (!config.query?.length) delete config.query;

    const products = await productAction.find(config, customerId);

    return products;
  }
  async getShopById(CustomerId: string) {
    const res = await this.shopController.getPatternList({
      page: 1,
      size: 10000,
      query: `@CustomerId: {${CustomerId}}`,
    });
    if (res.code === 200) {
      return res;
    }
    return null;
  }

  async getProductsByShopId(ShopId: string) {
    const res = await this.ProductController.getListSimple({
      page: 1,
      size: 10000,
      query: `@ShopId: {${ShopId}}`,
    });
    if (res.code === 200) {
      let data = await getImage({items: res.data});
      return data;
    }
    return [];
  }

  async getProductsByBrandId(
    brandId: string,
    page: number = 1,
    size: number = 50,
  ) {
    try {
      const res = await this.ProductController.getListSimple({
        page: page,
        size: size,
        query: `@BrandsId: {${brandId}}`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      if (res.code === 200) {
        let data = await getImage({items: res.data});
        return {
          data: data,
          totalCount: res.totalCount,
        };
      }
      return {
        data: [],
        totalCount: 0,
      };
    } catch (error) {
      return {
        data: [],
        totalCount: 0,
      };
    }
  }
  async updateStatusProduct(data: any) {
    const res = await this.ProductController.edit([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }

  async getFavoriteProduct(config: any, customerId: string) {
    const favoriteProductController = new DataController('ProductFavorite');
    const productController = new DataController('Product');
    const params = {
      page: config.page,
      size: config.size,
      query: `@CustomerId: {${customerId}}`,
    };
    if (config.search) params.query += ` @Name: {${config.search}}`;
    const res = await favoriteProductController.getListSimple(params);
    const products = await productController.getByListId(
      res.data.map((item: any) => item.ProductId),
    );
    const data = await getImage({items: products.data});
    data.forEach((item: any) => {
      item.IsFavorite = true;
    });
    return {
      data,
      totalCount: res.totalCount,
    };
  }

  async favoriteProduct({
    product,
    customerId,
  }: {
    product: ProductItem;
    customerId: string;
  }) {
    const favoriteProductController = new DataController('ProductFavorite');
    const params = {
      Id: randomGID(),
      DateCreated: Date.now(),
      ProductId: product.Id,
      CustomerId: customerId,
      Name: product.Name,
    };
    return favoriteProductController.add([params]);
  }

  async unFavoriteProduct({
    productId,
    customerId,
  }: {
    productId: string;
    customerId: string;
  }) {
    const favoriteProductController = new DataController('ProductFavorite');

    const res = await favoriteProductController.getListSimple({
      query: `@ProductId: {${productId}} @CustomerId: {${customerId}}`,
      size: 1,
    });
    if (res.code === 200 && res.data.length > 0) {
      const favoriteId = res.data[0].Id;
      await favoriteProductController.delete([favoriteId]);
      return true;
    }
    return false;
  }
  async GetAllCate() {
    const res = await this.cateController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getAllCateAttribute() {
    const res = await this.cateattributeController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getAllAttribute() {
    const res = await this.attributeController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getAllBrands() {
    const res = await this.brandController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getAllConsume() {
    const res = await this.consumeController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getAllColor() {
    const res = await this.colorController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async CreateProductPartner(data: any) {
    const res = await this.ProductController.add([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async EditProductPartner(data: any) {
    const res = await this.ProductController.edit([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async DeleteProductPartner(data: any) {
    const res = await this.ProductController.delete([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getProductById(ProductId: string) {
    const res = await this.ProductController.getById(ProductId);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async updateProduct(data: any) {
    // Đảm bảo data là array cho ProductController.edit
    const dataArray = Array.isArray(data) ? data : [data];
    const response = await this.ProductController.edit(dataArray);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async UpdateCustomerCompany(data: any) {
    const res = await this.customerController.edit([data]);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async CreateAttributeProduct(data: any[]) {
    const res = await this.productAttributeController.add(data);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async EditAttributeProduct(data: any[]) {
    const res = await this.productAttributeController.edit(data);
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getProductAttribute(ProductId: string) {
    const res = await this.productAttributeController.getListSimple({
      query: `@ProductId: {${ProductId}}`,
    });
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async getAllAttributeInStore() {
    const res = await this.productAttributeController.getAll();
    if (res.code === 200) {
      return res;
    }
    return null;
  }
  async handleCheckProductFavorite({
    products,
    customerId,
  }: {
    products: ProductItem[];
    customerId: string;
  }): Promise<ProductItem[]> {
    try {
      // Lấy danh sách ProductId từ products
      const productIds = products.map(item => item.Id);

      // Query ProductFavorite table để tìm các bản ghi favorite của customer này
      const favoriteRes = await this.productFavoriteController.getListSimple({
        query: `@ProductId: {${productIds.join(' | ')}} @CustomerId: {${customerId}}`,
        page: 1,
        size: 10000,
        returns: ['ProductId'], // Chỉ cần ProductId để check
      });
      // Tạo Set chứa các ProductId đã được favorite để lookup nhanh
      const favoriteProductIds = new Set<string>();
      if (favoriteRes.code === 200 && favoriteRes.data) {
        favoriteRes.data.forEach((item: any) => {
          favoriteProductIds.add(item.ProductId);
        });
      }

      // Thêm trường IsFavorite vào từng product
      const productsWithFavorite = products.map(product => ({
        ...product,
        IsFavorite: favoriteProductIds.has(product.Id),
      }));

      return productsWithFavorite;
    } catch (error) {
      console.error('Error in handleCheckProductFavorite:', error);
      // Trả về products với IsFavorite = false nếu có lỗi
      return products.map(product => ({
        ...product,
        IsFavorite: false,
      }));
    }
  }
}
export default new ProductDA();
