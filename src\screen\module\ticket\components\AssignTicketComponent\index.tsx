import React from 'react';
import {useFetchTickets} from './hooks/useFetchTickets';
import TicketList from './components/TicketList';
import LoadingIndicator from 'screen/pages/CheckoutPage/components/LoadingIndicator';

const AssignTicketComponent: React.FC = () => {
  const {
    isLoading,
    assignees,
    data,
    relatives,
    methods,
    onUpdateTicket,
  } = useFetchTickets();

  if (isLoading)
    return (
      <LoadingIndicator isLoading={isLoading} text="Đang tải dữ liệu..." />
    );

  return (
    <TicketList
      isLoading={isLoading}
      data={data}
      relatives={relatives}
      assignees={assignees}
      methods={methods}
      onUpdateTicket={onUpdateTicket}
    />
  );
};

export default AssignTicketComponent;
