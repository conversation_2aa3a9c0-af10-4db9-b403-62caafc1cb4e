# Tài liệu Hook useListToiletCriterion

## Tổng quan

Hook `useListToiletCriterion` đư<PERSON><PERSON> sử dụng để quản lý danh sách toilet và các tiêu chí đánh giá toilet trong ứng dụng. Hook này cung cấp các chức năng để tải dữ liệu, quản lý trạng thái UI và xử lý các tương tác của người dùng.

## Tham số đầu vào

```typescript
type UseListToiletCriterionParams = {
  toiletServiceId: string; // ID của dịch vụ toilet
};
```

## Các kiểu dữ liệu

- `ToiletItem`: Đại diện cho một toilet (hiện tại là `any`, cần được thay thế bằng kiểu cụ thể)
- `ToiletCriterion`: <PERSON><PERSON><PERSON> diện cho tiêu chí đánh giá toilet
- `CategoryCriterion`: Đ<PERSON><PERSON> diện cho danh mục tiêu chí

## Trạng thái (States)

### 1. Trạng thái UI

- `isBsSelectCriterion`: Boolean điều khiển việc hiển thị modal chọn tiêu chí
- `selectedToilet`: Toilet hiện tại được chọn để đánh giá
- `isLoading`: Trạng thái loading khi đang tải dữ liệu
- `isError`: Trạng thái lỗi khi có sự cố xảy ra

### 2. Trạng thái dữ liệu

- `toiletSelected`: Danh sách các toilet đã được chọn
- `toiletCriterions`: Danh sách các tiêu chí đánh giá toilet
- `criterions`: Danh sách tất cả các tiêu chí có sẵn (bao gồm cả từ các cấp thấp hơn)
- `cateCriterion`: Danh mục tiêu chí hiện tại
- `allCateCriterions`: Danh sách tất cả các danh mục tiêu chí (cấp hiện tại + các cấp thấp hơn)

## Các hàm chính

### 1. `findToiletCriterionByToiletId(toiletId: string)`

**Mục đích**: Tìm tiêu chí đánh giá của một toilet cụ thể
**Logic**:

- Duyệt qua danh sách `toiletCriterions`
- Trả về tiêu chí có `ToiletId` khớp với `toiletId` được truyền vào
- Sử dụng `useCallback` để tối ưu hiệu suất

### 2. `refreshToiletCriterions()`

**Mục đích**: Làm mới dữ liệu tiêu chí toilet từ server
**Logic**:

- Bật trạng thái loading
- Gọi API `getToiletCriterion` với `toiletServiceId`
- Cập nhật `toiletCriterions` nếu thành công
- Hiển thị thông báo lỗi nếu thất bại
- Tắt trạng thái loading trong `finally`

### 3. `handleToiletCardPress(item: ToiletItem)`

**Mục đích**: Xử lý khi người dùng nhấn vào card toilet
**Logic**:

- Lưu toilet được chọn vào `selectedToilet`
- Mở modal chọn tiêu chí bằng cách set `isBsSelectCriterion = true`

### 4. `markToiletAsUpdated(toiletIds: string[])`

**Mục đích**: Đánh dấu các toilet đã được cập nhật
**Logic**:

- Duyệt qua danh sách `toiletSelected`
- Với mỗi toilet có ID trong `toiletIds`, thêm thuộc tính `isUpdated: true`
- Giữ nguyên các toilet khác

### 5. `initData()`

**Mục đích**: Khởi tạo dữ liệu ban đầu khi component mount
**Logic**:

- Bật trạng thái loading và tắt trạng thái error
- Gọi API `getToiletCriterion` để lấy toàn bộ dữ liệu
- **Logic mới trong `getToiletCriterion`**: Nếu `cateCriterion.Sort > 1`, hệ thống sẽ tự động lấy thêm:
  - Tất cả các `cateCriterion` có `Sort` nhỏ hơn `Sort` hiện tại (từ 1 đến Sort-1)
  - Tất cả các `criterions` thuộc về các `cateCriterion` đó
  - Trả về `allCateCriterions` chứa tất cả các danh mục tiêu chí để hiển thị title trên giao diện
  - Điều này đảm bảo người dùng có thể thấy và đánh giá theo tất cả các tiêu chí từ cấp thấp đến cấp hiện tại
- Destructure kết quả và cập nhật các state tương ứng:
  - `toilets` → `toiletSelected`
  - `cateCriterion` → `cateCriterion`
  - `criterions` → `criterions` (bao gồm cả criterions từ các cấp thấp hơn)
  - `toiletCriterions` → `toiletCriterions`
  - `allCateCriterions` → `allCateCriterions` (để hiển thị title các cấp)
- Xử lý lỗi và hiển thị thông báo phù hợp

## Effect Hook

```typescript
useEffect(() => {
  if (toiletServiceId) {
    initData();
  }
}, [toiletServiceId]);
```

**Mục đích**: Tự động tải dữ liệu khi `toiletServiceId` thay đổi
**Logic**: Chỉ gọi `initData()` khi `toiletServiceId` có giá trị

## Giá trị trả về

Hook trả về một object chứa:

### Trạng thái dữ liệu:

- `isLoading`: Trạng thái loading
- `isError`: Trạng thái lỗi
- `toiletSelected`: Danh sách toilet đã chọn
- `toiletCriterions`: Danh sách tiêu chí toilet
- `criterions`: Danh sách tất cả tiêu chí (bao gồm từ các cấp thấp hơn)
- `cateCriterion`: Danh mục tiêu chí hiện tại
- `allCateCriterions`: Danh sách tất cả danh mục tiêu chí (để hiển thị title)

### Trạng thái modal:

- `isBsSelectCriterion`: Trạng thái hiển thị modal
- `setIsBsSelectCriterion`: Hàm để điều khiển modal
- `selectedToilet`: Toilet hiện tại được chọn

### Các hàm action:

- `handleToiletCardPress`: Xử lý nhấn card toilet
- `findToiletCriterionByToiletId`: Tìm tiêu chí theo ID toilet
- `refreshToiletCriterions`: Làm mới dữ liệu tiêu chí
- `markToiletAsUpdated`: Đánh dấu toilet đã cập nhật

## Xử lý lỗi

Hook sử dụng `showSnackbar` để hiển thị thông báo lỗi với:

- Trạng thái `ComponentStatus.ERROR`
- Thông báo bằng tiếng Việt phù hợp với từng tình huống

## Tối ưu hiệu suất

- Sử dụng `useMemo` cho Data Access instance
- Sử dụng `useCallback` cho các hàm để tránh re-render không cần thiết
- Dependency array được quản lý cẩn thận trong các hook

## Logic mới trong Data Access Layer (DA)

### Cải tiến trong `getToiletCriterion()`

Hàm `getToiletCriterion` trong file `da.ts` đã được cải tiến để hỗ trợ logic phân cấp tiêu chí:

#### Logic cũ:

- Chỉ lấy các `criterions` thuộc về `cateCriterion` hiện tại
- Query: `@CateCriterionId:{cateCriterion.Id}`

#### Logic mới:

1. **Kiểm tra Sort level**: Nếu `cateCriterion.Sort > 1`
2. **Lấy các cateCriterion cấp thấp hơn**:
   - Query: `@Sort:[1 ${cateCriterion.Sort - 1}]`
   - Lấy tất cả cateCriterion có Sort từ 1 đến (Sort hiện tại - 1)
3. **Tổng hợp dữ liệu**:
   - Kết hợp ID của cateCriterion hiện tại với các ID của cateCriterion cấp thấp hơn
   - Tạo mảng `allCateCriterions` chứa tất cả cateCriterion (để hiển thị title)
4. **Lấy tất cả criterions**:
   - Query: `@CateCriterionId:{id1 | id2 | id3 | ...}`
   - Lấy criterions từ tất cả các cateCriterion (cấp hiện tại + các cấp thấp hơn)

#### Ví dụ thực tế:

- Nếu `cateCriterion.Sort = 3`:
  - Sẽ lấy thêm các cateCriterion có Sort = 1, 2
  - Kết quả: criterions từ cấp 1, 2, và 3
- Nếu `cateCriterion.Sort = 1`:
  - Chỉ lấy criterions của cấp 1 (logic cũ)

#### Lợi ích:

- **Tính kế thừa**: Các tiêu chí cấp cao tự động bao gồm tiêu chí cấp thấp
- **Đánh giá toàn diện**: Người dùng có thể thấy và đánh giá theo tất cả tiêu chí từ cơ bản đến nâng cao
- **Tự động hóa**: Không cần phải manually chọn các cấp tiêu chí

## Ghi chú

- Các kiểu `ToiletItem`, `ToiletCriterion`, `CategoryCriterion` hiện tại là `any` và cần được thay thế bằng kiểu cụ thể
- Hook này phụ thuộc vào `ListToiletCriterionDA` để thực hiện các API call
- Logic mới đảm bảo tính nhất quán trong việc đánh giá tiêu chí theo từng cấp độ
- Trường `Sort` trong `CateCriterion` đóng vai trò quan trọng trong việc xác định thứ tự và phân cấp tiêu chí
