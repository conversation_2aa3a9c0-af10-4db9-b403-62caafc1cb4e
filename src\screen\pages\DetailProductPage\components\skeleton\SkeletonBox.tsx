import React from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {SkeletonBoxProps} from '../../types';

const SkeletonBox: React.FC<SkeletonBoxProps> = ({width, height, style}) => (
  <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
    <SkeletonPlaceholder.Item
      width={width}
      height={height}
      borderRadius={8}
      style={style}
    />
  </SkeletonPlaceholder>
);

export default SkeletonBox;
