# Cấu trúc màn hình DetailToiletServicePage

Đây là tài liệu mô tả cấu trúc và luồng hoạt động của màn hình `DetailToiletServicePage`.

## Tổng quan

`DetailToiletServicePage` là màn hình chính hiển thị chi tiết của một công việc dịch vụ toilet. Màn hình này sử dụng `react-native-tab-view` để tạo một giao diện theo các tab, tương ứng với các giai đoạn khác nhau của dịch vụ.

Màn hình chịu trách nhiệm fetch và quản lý dữ liệu liên quan đến dịch vụ, bao gồm thông tin công việc, dữ liệu dịch vụ và thông tin người dùng.

## Cấu trúc thư mục

```
.
├── README.md
├── AGENTS.md
├── components
│   ├── HeaderActions.tsx
│   ├── TabBarComponent.tsx
│   └── TabContent
│       ├── components
│       │   ├── BuildTabContent.tsx
│       │   ├── ConsultantTabContent.tsx
│       │   ├── ContractTabContent.tsx
│       │   ├── DesignTabContent.tsx
│       │   ├── LiquidTabContent.tsx
│       │   ├── RegisterTabContent.tsx
│       │   └── index.ts
│       ├── hooks
│       │   └── useTabHandlers.ts
│       └── index.tsx
├── constants
│   └── statusData.tsx
├── docs
│   └── README.md
├── hooks
│   └── useToiletService.tsx
└── index.tsx
```

## Quản lý State

### State nội bộ (Component State)

-   **`useState`**:
    -   `routes`: Quản lý danh sách các tab cho `TabView`. Dữ liệu các tab được lấy từ `StatusWorkSpaceData` và lọc bỏ đi trạng thái "run".
-   **`useMemo`**:
    -   `renderTabBar`: Ghi nhớ (memoize) component `TabBarComponent` để tránh việc render lại không cần thiết.
    -   `renderScene`: Ghi nhớ (memoize) hàm render nội dung cho mỗi tab.

### State toàn cục (Global State)

-   **Redux**:
    -   `useSelectorCustomerState`: Lấy dữ liệu khách hàng (người dùng) từ Redux store.
    -   `useSelectorCustomerCompanyState`: Lấy dữ liệu công ty từ Redux store.

## Custom Hooks

### `useToiletService`

Đây là một custom hook quan trọng, đóng gói toàn bộ logic nghiệp vụ cho màn hình.

-   **Tham số**:
    -   `toiletServiceId`: ID của dịch vụ toilet, lấy từ route params.
    -   `user`: Thông tin người dùng.
-   **Giá trị trả về**:
    -   `workData`: Dữ liệu liên quan đến công việc.
    -   `serviceData`: Dữ liệu liên quan đến dịch vụ.
    -   `setServiceData`: Hàm để cập nhật dữ liệu dịch vụ.
    -   `index`, `setIndex`: Dùng để quản lý tab đang hoạt động.
    -   `isLoading`, `isRefreshing`: Trạng thái loading và refresh.
    -   `guest`: Thông tin người dùng khách.
    -   `methods`: Các phương thức từ `react-hook-form`.
    -   `onRefresh`: Hàm xử lý sự kiện pull-to-refresh.
    -   `onChangeStatus`: Hàm thay đổi trạng thái dịch vụ.
    -   `getServicesData`: Hàm để fetch dữ liệu dịch vụ.
    -   `handleReject`: Hàm xử lý khi từ chối.
    -   `handleSubmitRegister`: Hàm xử lý khi submit đăng ký.

## Routing

-   **`useRoute`**:
    -   Lấy các tham số từ route, bao gồm `Id` (ID dịch vụ toilet), `Name`, và `Status`.

## Logic Render

### `renderTabBar`

-   Render một thanh tab tùy chỉnh bằng component `TabBarComponent`.
-   Được truyền vào `serviceData` và `getServicesData`.

### `renderScene`

-   Sử dụng `switch-case` để render nội dung tab phù hợp dựa trên `route.key`.
-   `route.key` tương ứng với các trạng thái dịch vụ khác nhau (`register`, `consultant`, `contract`, `design`, `build`, `liquid`).
-   Mỗi `case` sẽ trả về một component nội dung tab cụ thể (ví dụ: `RegisterTabContent`, `ConsultantTabContent`).
-   Các component này được truyền các dữ liệu và hàm cần thiết từ hook `useToiletService`.

### Render chính

-   Hiển thị `FLoading` khi `isLoading` là `true`.
-   Render `TitleHeader` với tên của dịch vụ.
-   Render có điều kiện:
    -   `RegisterTab`: Nếu công ty là KTX hoặc loại dịch vụ là "contact".
    -   `TabView`: Cho tất cả các trường hợp khác, hiển thị giao diện theo tab.

## Các Components con

-   **`TabBarComponent`**: Thanh tab tùy chỉnh.
-   **Components nội dung Tab**:
    -   `RegisterTabContent`
    -   `ConsultantTabContent`
    -   `ContractTabContent`
    -   `DesignTabContent`
    -   `BuildTabContent`
    -   `LiquidTabContent`
-   **`FLoading`**: Component hiển thị loading toàn màn hình.
-   **`TitleHeader`**: Header của màn hình.
-   **`EmptyPage`**: Hiển thị trong trường hợp `default` của `renderScene`.

## Styling

-   Sử dụng `StyleSheet.create` để định nghĩa styles.
-   Container chính có `flex: 1` và màu nền được lấy từ `ColorThemes`.

