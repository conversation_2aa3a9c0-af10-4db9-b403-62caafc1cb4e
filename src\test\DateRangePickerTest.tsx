import React from 'react';
import {View, StyleSheet, Alert} from 'react-native';
import {useForm} from 'react-hook-form';
import {DateRangePicker} from '../project-component/form/DateRangePicker/DateRangePicker';

interface FormData {
  startDate: Date;
  endDate: Date;
}

export const DateRangePickerTest = () => {
  const {
    control,
    formState: {errors},
    watch,
  } = useForm<FormData>({
    defaultValues: {
      startDate: undefined,
      endDate: undefined,
    },
  });

  const watchedValues = watch();

  const handleDateRangeChange = (startDate: Date, endDate: Date) => {
    console.log('Date range changed:', {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    });
    
    Alert.alert(
      'Date Range Selected',
      `Start: ${startDate.toLocaleDateString()}\nEnd: ${endDate.toLocaleDateString()}`
    );
  };

  return (
    <View style={styles.container}>
      <DateRangePicker
        control={control}
        startDateName="startDate"
        endDateName="endDate"
        errors={errors}
        placeholder="Chọn khoảng thời gian"
        onDateRangeChange={handleDateRangeChange}
        required={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
  },
});
