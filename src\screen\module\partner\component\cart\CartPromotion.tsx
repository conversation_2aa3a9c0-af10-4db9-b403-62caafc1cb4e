import {Text} from 'react-native-paper';
import {AppSvg, Checkbox, Winicon} from 'wini-mobile-components';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faAngleRight} from '@fortawesome/free-solid-svg-icons';
import {Image, ScrollView, TouchableOpacity, View} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import iconSvg from '../../../../../svgs/iconSvg';
import ConfigAPI from '../../../../../config/configApi';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {FCheckbox} from '../../../../../component/export-component';

export default function CartPromotion({
  item,
  index,
  handleSelectCategory,
  handleSelectAllCategory,
  selectTwo,
  getdataByMenuTwo,
  selectThree,
  handleGetDataDiscount,
  selectAll,
  selectedItems,
  isSelected,
  handleSelectChild,
  selectProduct,
  typeSelect,
  handleRemoveItem,
  checkSelectAllItem,
  selectItem,
}: {
  item: any;
  index: number;
  handleSelectCategory: (item: any) => void;
  handleSelectAllCategory: (item: any) => void;
  selectTwo: boolean;
  getdataByMenuTwo: (item: any) => void;
  selectThree: boolean;
  handleGetDataDiscount: (data: any, value?: boolean) => void;
  selectAll: boolean;
  selectedItems: Set<string>;
  isSelected: boolean;
  handleSelectChild?: (child: any) => void;
  selectProduct: boolean;
  typeSelect: boolean;
  handleRemoveItem?: (item: any) => void;
  checkSelectAllItem: string;
  selectItem: string[];
}) {
  // Kiểm tra xem danh mục này có children không
  const hasChildren = item?.Children;

  // Kiểm tra xem item có phải là sản phẩm không (có Price và InStock)
  const isProduct = item?.Price && item?.InStock;

  // Kiểm tra xem item có phải là danh mục con không (có ParentId)
  const isChildCategory = item?.ParentId;

  // Kiểm tra xem item có phải là danh mục cha không (có Children)
  const isParentCategory = hasChildren && !item?.ParentId;

  // console.log('check-selectThree ', selectThree);
  // console.log('check-selectProduct ', selectProduct);
  // console.log('check-typeSelect ', typeSelect);
  // console.log('check-selectItem', selectItem);
  // console.log('check-select', selectItem);

  return (
    <View
      key={`title-${index}`}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingBottom: 10,
        paddingTop: 10,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_main_background_color,
        marginLeft: 16,
        width: '100%',
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginLeft: 15,
        }}>
        {!item?.Price && !selectProduct && selectThree && !typeSelect && (
          <FCheckbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item, isSelected);
            }}
            size={24}
          />
        )}
        {!item?.Price && selectProduct && selectThree && !typeSelect && (
          <FCheckbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item, isSelected);
            }}
            size={24}
          />
        )}
        {!item?.Price && !selectProduct && !selectThree && !typeSelect && (
          <FCheckbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item, isSelected);
            }}
            size={24}
          />
        )}
        {item?.Price && !selectThree && !typeSelect && selectProduct && (
          <FCheckbox
            value={selectProduct}
            onChange={() => {
              handleGetDataDiscount(item);
            }}
            size={24}
          />
        )}
        {!item?.Price && !selectThree && !typeSelect && selectProduct && (
          <FCheckbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item, isSelected);
            }}
            size={24}
          />
        )}
        {item?.Price && selectThree && !typeSelect && !selectProduct && (
          <FCheckbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item, isSelected);
            }}
            size={24}
          />
        )}

        {typeSelect && (
          <TouchableOpacity onPress={() => handleRemoveItem?.(item)}>
            <AppSvg SvgSrc={iconSvg.exit} size={24} />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          onPress={
            item?.Price && item?.InStock
              ? undefined
              : () => handleSelectCategory(item)
          }
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            width: '80%',
          }}>
          {/* Hiển thị ảnh cho sản phẩm */}
          {item?.Price && (
            <Image
              source={{uri: ConfigAPI.imgUrlId + item?.Img}}
              style={{height: 46, width: 46, borderRadius: 50, marginLeft: 15}}
            />
          )}

          <Text
            style={{
              marginLeft: isProduct ? 14 : 14,
              ...TypoSkin.body2,
              textAlign: 'center',
            }}>
            {item?.Name}
          </Text>
        </TouchableOpacity>
      </View>
      {!(selectItem.includes(item?.Id) || selectAll) ? (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {/* Hiển thị mũi tên cho danh mục cha hoặc danh mục con */}
          {!selectTwo &&
            !selectThree &&
            (isParentCategory || isChildCategory) && (
              <TouchableOpacity
                style={{marginRight: 16, width: '10%'}}
                onPress={() => handleSelectCategory(item)}>
                <FontAwesomeIcon
                  icon={faAngleRight}
                  color={ColorThemes.light.neutral_overlay_background_color}
                  size={16}
                />
              </TouchableOpacity>
            )}
          {selectTwo &&
            !selectThree &&
            (isParentCategory || isChildCategory) && (
              <TouchableOpacity
                style={{marginRight: 16, width: '10%'}}
                onPress={() => getdataByMenuTwo(item)}>
                <FontAwesomeIcon
                  icon={faAngleRight}
                  color={ColorThemes.light.neutral_overlay_background_color}
                  size={16}
                />
              </TouchableOpacity>
            )}
        </View>
      ) : null}
    </View>
  );
}
