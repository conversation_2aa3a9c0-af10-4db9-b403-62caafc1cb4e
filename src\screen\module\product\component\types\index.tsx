import {ImageOrVideo} from 'react-native-image-crop-picker';

export interface MenuProductProps {
  menu: string;
  setMenu: (menu: string) => void;
  data: any[];
}

export interface ManageProductDetailProps {
  menu?: string;
  dataShop?: any[];
  partnerId: string;
  getAllProductByShopId: (partnerId: string) => void;
}
export interface ProductState {
  image: ImageOrVideo[] | undefined;
  avataProduct: string;
  lengText: {
    ProductName: number;
    DesProduct: number;
    SpecificationsProduct: number;
  };
}

export interface CreateInputFeildProps {
  control: any;
  nameFeild: string;
  checkLengthText?: number;
  name: string | any;
  rule: string | any;
  placeholder: string;
  maxlength?: number;
  mutiline: boolean;
  large?: boolean;
}
