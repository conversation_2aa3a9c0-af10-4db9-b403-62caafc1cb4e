import {useCallback, useEffect} from 'react';
import {useSelector} from 'react-redux';
import {StyleSheet, View} from 'react-native';
import ScrollableTabs from '../../../../component/scrollable/ScrollableTabs';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';
import {RootState} from '../../../../redux/store/store';
import iconSvg from '../../../../svgs/iconSvg';

const TABS_DATA = [
  {
    id: 'IsHot',
    type: 'svg' as const,
    label: 'HOT',
    icon: iconSvg.isHot,
    size: 20,
  },
  {
    id: 'IsFreeShip',
    type: 'svg' as const,
    label: 'Freeship',
    icon: iconSvg.isFreeShip,
    size: 20,
  },
  {
    id: 'IsNew',
    type: 'svg' as const,
    label: 'Mớ<PERSON>',
    icon: iconSvg.isNew,
    size: 20,
  },
  {
    id: 'FavoriteBrand',
    type: 'svg' as const,
    label: 'Nhãn hàng ưa chuộng',
    icon: iconSvg.isBrand,
    size: 20,
  },
];

const ScrollOption = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);

  // // Handle filter change
  const handleFilterChange = useCallback(
    (filterId: string) => {
      const newActiveFilters = {
        [filterId]: true,
      };
      productByCategoryHook.setData('filter', {
        ...filter,
        activeFilters: newActiveFilters,
      });
    },
    [filter, productByCategoryHook],
  );

  useEffect(() => {
    handleFilterChange('IsHot');
  }, []);

  return (
    <View style={styles.container}>
      <ScrollableTabs onChangeTab={handleFilterChange} data={TABS_DATA} />
    </View>
  );
};

export default ScrollOption;

const styles = StyleSheet.create({
  container: {
    marginVertical: 6,
  },
});
