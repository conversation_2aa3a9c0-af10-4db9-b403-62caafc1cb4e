import {useRef} from 'react';
import {showPopup, closePopup} from 'component/popup/popup';
import {showDialog} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {TicketStatus} from 'screen/module/service/components/da';
import {FilePreviewPopup} from '../../FilePreviewPopup';

interface UsePopupHandlersProps {
  methods: any;
  onChange: any;
  item: any;
  user: any;
  details: Array<any>;
  parentPopupRef: React.ForwardedRef<any>;
}

interface UsePopupHandlersReturn {
  dialogRef: React.RefObject<any>;
  handleFilePress: (file: any) => void;
  handleSubmit: () => void;
}

export const usePopupHandlers = ({
  methods,
  onChange,
  item,
  user,
  details,
  parentPopupRef,
}: UsePopupHandlersProps): UsePopupHandlersReturn => {
  const dialogRef = useRef<any>();

  const handleFilePress = (file: any) => {
    if (file.Url && parentPopupRef) {
      showPopup({
        ref: parentPopupRef as any,
        enableDismiss: true,
        children: (
          <FilePreviewPopup
            file={file}
            onClose={() => parentPopupRef && closePopup(parentPopupRef as any)}
          />
        ),
      });
    }
  };

  const handleSubmit = () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title:
        'Bạn chắc chắn muốn cập nhật thông tin yêu cầu theo những thông tin đã nhập?',
      onSubmit: async () => {
        let label = '';
        switch (item?.Status) {
          case TicketStatus.init:
            label = 'Đang mở';
            break;
          case TicketStatus.processing:
            label = 'Đang xử lý';
            break;
          case TicketStatus.done:
            label = 'Hoàn thành';
            break;
          case TicketStatus.cancel:
            label = 'Đã hủy';
            break;
          case TicketStatus.end:
            label = 'Kết thúc';
            break;
          default:
            break;
        }

        const content = methods.getValues('Content');
        const newDetail = {
          CustomerId: user?.Id,
          DateCreated: Date.now(),
          Content: content?.length
            ? content
            : `Yêu cầu đc chuyển sang trạng thái ${label}`,
        };

        onChange({
          Ktx: methods.getValues('Ktx') ?? undefined,
          Status: methods.getValues('Status'),
          Detail: JSON.stringify([...details, newDetail]),
        });

        setTimeout(() => {
          parentPopupRef && closePopup(parentPopupRef as any);
        }, 100);
      },
    });
  };

  return {
    dialogRef,
    handleFilePress,
    handleSubmit,
  };
};
