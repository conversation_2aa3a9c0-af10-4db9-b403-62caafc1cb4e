import React from 'react';
import {View, TouchableOpacity, Text, StyleSheet} from 'react-native';
import {ColorThemes} from 'assets/skin/colors';
import {useSelectorCustomerCompanyState} from 'redux/hooks/hooks';
import ConfigAPI from 'config/configApi';

interface TabSelectorProps {
  tab: number;
  setTab: (tab: number) => void;
}

const TabSelector: React.FC<TabSelectorProps> = ({tab, setTab}) => {
  const company = useSelectorCustomerCompanyState().data;
  const tabCount = company?.Id === ConfigAPI.ktxCompanyId ? 1 : 2;

  const getTabLabel = (index: number) => {
    switch (index) {
      case 0:
        return 'Ticket cần xử lý';
      case 1:
        return 'Ticket của tôi';
      default:
        return '';
    }
  };

  return (
    <View style={styles.container}>
      {Array.from({length: tabCount}).map((_, i) => (
        <TouchableOpacity
          key={`tab-${i}`}
          style={[
            styles.tabButton,
            {
              borderBottomColor: ColorThemes.light.primary_main_color,
              borderBottomWidth: i === tab ? 1 : 0,
            },
          ]}
          onPress={() => setTab(i)}>
          <Text
            style={[
              styles.tabText,
              {
                color:
                  i === tab
                    ? ColorThemes.light.primary_main_color
                    : ColorThemes.light.neutral_text_title_color,
                fontWeight: i === tab ? 'bold' : 'normal',
              },
            ]}>
            {getTabLabel(i)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 40,
    marginBottom: 16,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    fontSize: 14,
  },
});

export default TabSelector;
