import React from 'react';
import {View, Text, ActivityIndicator, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {LoadingFooterProps} from '../types';

const LoadingFooter: React.FC<LoadingFooterProps> = ({isVisible}) => {
  if (!isVisible) return null;

  return (
    <View style={styles.container}>
      <ActivityIndicator size="small" color="#0000ff" />
      <Text style={styles.loadingText}><PERSON>ang tải thêm...</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadingText: {
    ...TypoSkin.body3,
    color: '#999',
  },
});

export default LoadingFooter;
