import {ScrollView, View, Linking} from 'react-native';
import {useRoute} from '@react-navigation/native';
import React, {useCallback} from 'react';

import TitleHeader from '../../../layout/headers/TitleHeader';
import {navigate, RootScreen} from '../../../../router/router';
import {ShopProfile, ShopDetails, ShopStatistics} from './components';
import {ShopData} from './types';
import {styles} from './styles';

export default function InforShopView() {
  const route = useRoute<any>();
  const shop = route?.params?.shop;

  const handleCall = (phoneNumber: string) => {
    if (phoneNumber) {
      Linking.openURL(`tel:${phoneNumber}`);
    }
  };

  const handleMessage = async (phoneNumber: string) => {
    // TODO: Implement chat functionality
    console.log('Message to:', phoneNumber);
  };

  const handleShopInfoPress = (
    type: 'rating' | 'product' | 'order',
    shop: ShopData | null,
  ) => {
    if (type === 'rating')
      navigate(RootScreen.RatingPage, {
        shopId: shop?.Id,
        rate: shop?.rating,
        countRate: shop?.countRate,
      });
    if (type === 'product')
      navigate(RootScreen.ProductShopPage, {id: shop?.Id});
    if (type === 'order') {
      // navigate(RootScreen.OrderDetailPageForShop, {id: shop?.Id});
    }
  };

  const handleShopInfoPressLocal = useCallback(
    (type: 'rating' | 'product' | 'order') => {
      handleShopInfoPress(type, shop);
    },
    [shop],
  );

  return (
    <View style={styles.container}>
      <TitleHeader title={'Thông tin cửa hàng'} />
      <ScrollView style={styles.scrollView}>
        {/* Shop Profile Section */}
        <ShopProfile
          shop={shop}
          onCall={handleCall}
          onMessage={handleMessage}
        />

        {/* Shop Details Section */}
        <ShopDetails shop={shop} onCall={handleCall} />
        {/* Statistics Section */}
        <ShopStatistics
          shop={shop}
          onProductPress={() => handleShopInfoPressLocal('product')}
        />
      </ScrollView>
    </View>
  );
}
