import React, {useState, useRef} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Controller, FieldValues} from 'react-hook-form';
import DatePicker from 'react-native-date-picker';
import {Ultis} from '../../../utils/Utils';
import {SingleDatePickerProps} from '../DateRangePicker/DatePickerTypes';
import {datePickerStyles} from '../DateRangePicker/DatePickerStyles';
import {CustomBottomSheet} from '../DateRangePicker/CustomBottomSheet';

export const DateSinglePicker = <T extends FieldValues = FieldValues>({
  control,
  name,
  errors,
  placeholder = 'Chọn ngày',
  disabled = false,
  required = false,
  style = {},
  textStyle = {},
  icon,
  prefix,
  minDate,
  maxDate,
  onDateChange,
  useTimestamp = true, // Mặc định trả về timestamp
  onTimestampChange,
}: SingleDatePickerProps<T>) => {
  const [tempDate, setTempDate] = useState<Date>(new Date());
  const [isBottomSheetVisible, setIsBottomSheetVisible] =
    useState<boolean>(false);
  const onConfirmCallbackRef = useRef<((date: Date) => void) | null>(null);

  // Utility functions for timestamp conversion
  const timestampToDate = (timestamp?: number): Date | undefined => {
    if (!timestamp) return undefined;
    return new Date(timestamp);
  };

  const dateToTimestamp = (date?: Date): number | undefined => {
    if (!date) return undefined;
    return date.getTime();
  };

  const formatDate = (date?: Date): string => {
    if (!date) return '';
    return Ultis.datetoString(date, 'dd/MM/yyyy');
  };

  const showDatePicker = (
    currentValue?: Date,
    onConfirm?: (date: Date) => void,
  ) => {
    setTempDate(currentValue || new Date());
    onConfirmCallbackRef.current = onConfirm || null;
    setIsBottomSheetVisible(true);
  };

  const handleBottomSheetClose = () => {
    setIsBottomSheetVisible(false);
    onConfirmCallbackRef.current = null;
  };

  const handleConfirm = () => {
    onConfirmCallbackRef.current?.(tempDate);
    onDateChange?.(tempDate);

    // Call timestamp callback if using timestamp format
    if (useTimestamp && onTimestampChange) {
      const timestamp = dateToTimestamp(tempDate);
      onTimestampChange(timestamp);
    }
    handleBottomSheetClose();
  };

  const renderBottomSheetContent = () => {
    return (
      <View style={datePickerStyles.bottomSheetContent}>
        <View style={datePickerStyles.pickerContainer}>
          <DatePicker
            date={tempDate}
            mode="date"
            style={datePickerStyles.datePicker}
            minimumDate={minDate}
            maximumDate={maxDate}
            dividerColor={'#f2f5f8'}
            theme="auto"
            onDateChange={setTempDate}
          />
        </View>
      </View>
    );
  };

  return (
    <>
      <CustomBottomSheet
        visible={isBottomSheetVisible}
        onClose={handleBottomSheetClose}
        title="Chọn ngày"
        onCancel={handleBottomSheetClose}
        onConfirm={handleConfirm}
        cancelText="Hủy"
        confirmText="Xác nhận">
        {renderBottomSheetContent()}
      </CustomBottomSheet>

      <Controller
        control={control}
        name={name as any}
        rules={{required}}
        render={({field}) => {
          // Convert timestamp to Date for display if using timestamp format
          const currentDate = useTimestamp
            ? timestampToDate(field.value)
            : field.value;

          const displayValue = formatDate(currentDate);

          return (
            <View style={[datePickerStyles.container, style]}>
              <TouchableOpacity
                style={[datePickerStyles.inputWrapper]}
                onPress={() => {
                  if (!disabled) {
                    showDatePicker(currentDate, selectedDate => {
                      if (useTimestamp) {
                        // Convert Date to timestamp before saving
                        field.onChange(dateToTimestamp(selectedDate));
                      } else {
                        // Save as Date object
                        field.onChange(selectedDate);
                      }
                    });
                  }
                }}
                disabled={disabled}>
                {(prefix || icon) && (
                  <View style={[datePickerStyles.iconContainer]}>
                    {prefix || icon}
                  </View>
                )}

                <Text
                  style={[
                    datePickerStyles.displayText,
                    !displayValue && datePickerStyles.placeholderText,
                    textStyle,
                  ]}>
                  {displayValue || placeholder}
                </Text>
              </TouchableOpacity>

              {errors[name] && (
                <Text style={datePickerStyles.errorText}>
                  {(errors[name]?.message as string) || 'Vui lòng chọn ngày'}
                </Text>
              )}
            </View>
          );
        }}
      />
    </>
  );
};
