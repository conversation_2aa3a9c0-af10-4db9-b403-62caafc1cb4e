import { useCallback } from 'react';
import { TicketType } from 'screen/module/service/components/da';

export const useTicketUtils = () => {
  const getTypeLabel = useCallback((type: number) => {
    switch (type) {
      case TicketType.services:
        return '<PERSON><PERSON>n ánh chất lượng dịch vụ';
      case TicketType.feedback:
        return 'Phản hồi/góp ý';
      case TicketType.accident:
        return 'Tai nạn sự cố';
      default:
        return '';
    }
  }, []);

  return {
    getTypeLabel,
  };
};
