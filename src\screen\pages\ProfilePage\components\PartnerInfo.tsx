import React from 'react';
import {View, Text} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

const PartnerInfo: React.FC = () => {
  const {currentPartner, isLoading, error} = usePartner();

  if (isLoading) {
    return (
      <View style={{padding: 16}}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.neutral_text_subtitle_color,
          }}>
          Đang tải thông tin đối tác...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={{padding: 16}}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.error_main_color,
          }}>
          Lỗi: {error}
        </Text>
      </View>
    );
  }

  if (!currentPartner) {
    return (
      <View style={{padding: 16}}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.neutral_text_subtitle_color,
          }}>
          <PERSON><PERSON><PERSON> có thông tin đối tác
        </Text>
      </View>
    );
  }

  return (
    <View
      style={{
        padding: 16,
        backgroundColor: 'white',
        margin: 16,
        borderRadius: 8,
      }}>
      <Text
        style={{
          ...TypoSkin.title2,
          marginBottom: 12,
          color: ColorThemes.light.primary_main_color,
        }}>
        Thông tin đối tác (từ Redux)
      </Text>

      <View style={{gap: 8}}>
        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>ID: </Text>
          {currentPartner.Id}
        </Text>

        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>Tên: </Text>
          {currentPartner.Name || 'N/A'}
        </Text>

        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>Email: </Text>
          {currentPartner.GroupEmail || 'N/A'}
        </Text>

        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>Số điện thoại: </Text>
          {currentPartner.GroupPhone || 'N/A'}
        </Text>

        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>Địa chỉ: </Text>
          {currentPartner.GroupAddress || 'N/A'}
        </Text>

        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>Trạng thái: </Text>
          {currentPartner.Status === 1 ? 'Hoạt động' : 'Chưa kích hoạt'}
        </Text>

        <Text style={{...TypoSkin.body3}}>
          <Text style={{fontWeight: 'bold'}}>Customer ID: </Text>
          {currentPartner.CustomerId || 'N/A'}
        </Text>
      </View>
    </View>
  );
};

export default PartnerInfo;
function usePartner(): {currentPartner: any; isLoading: any; error: any} {
  throw new Error('Function not implemented.');
}
