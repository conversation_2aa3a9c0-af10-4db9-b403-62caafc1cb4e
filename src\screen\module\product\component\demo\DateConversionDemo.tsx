import React, {useState} from 'react';
import {View, Text, StyleSheet, ScrollView, TouchableOpacity} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';

const DateConversionDemo = () => {
  const [testResults, setTestResults] = useState<any[]>([]);

  // Hàm CovertDate được cải thiện
  const CovertDate = (date: any): number => {
    // Nếu không có dữ liệu, tr<PERSON> về 0
    if (!date) return 0;

    // Nếu đã là số (timestamp), trả về luôn
    if (typeof date === 'number') return date;

    // Nếu là chuỗi, xử lý chuyển đổi
    if (typeof date === 'string') {
      // Kiểm tra nếu là chuỗi số (timestamp dạng string)
      if (/^\d+$/.test(date)) {
        return parseInt(date);
      }

      // X<PERSON> lý định dạng dd/mm/yyyy
      if (date.includes('/')) {
        const [day, month, year] = date.split('/');
        if (day && month && year) {
          const getDate = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
          );
          return getDate.getTime();
        }
      }
    }

    // Nếu là Date object
    if (date instanceof Date) {
      return date.getTime();
    }

    // Trường hợp khác, thử parse trực tiếp
    try {
      const parsedDate = new Date(date);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.getTime();
      }
    } catch (error) {
      console.warn('Cannot convert date:', date, error);
    }

    // Trả về 0 nếu không thể chuyển đổi
    return 0;
  };

  const testCases = [
    {input: '25/12/2024', description: 'Chuỗi dd/mm/yyyy'},
    {input: '01/01/2025', description: 'Chuỗi dd/mm/yyyy khác'},
    {input: 1703462400000, description: 'Timestamp số'},
    {input: '1703462400000', description: 'Timestamp chuỗi'},
    {input: new Date('2024-12-25'), description: 'Date object'},
    {input: null, description: 'Null'},
    {input: undefined, description: 'Undefined'},
    {input: '', description: 'Chuỗi rỗng'},
    {input: 'invalid-date', description: 'Chuỗi không hợp lệ'},
  ];

  const runTests = () => {
    const results = testCases.map(testCase => {
      const result = CovertDate(testCase.input);
      const isNumber = typeof result === 'number';
      const isValidTimestamp = result > 0;
      
      return {
        ...testCase,
        result,
        isNumber,
        isValidTimestamp,
        formattedDate: result > 0 ? new Date(result).toLocaleDateString('vi-VN') : 'N/A',
      };
    });
    
    setTestResults(results);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Demo Chuyển Đổi Thời Gian</Text>
      <Text style={styles.subtitle}>
        Hàm CovertDate luôn trả về timestamp (số)
      </Text>

      <TouchableOpacity style={styles.button} onPress={runTests}>
        <Text style={styles.buttonText}>Chạy Test</Text>
      </TouchableOpacity>

      {testResults.length > 0 && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Kết quả Test:</Text>
          
          {testResults.map((result, index) => (
            <View key={index} style={styles.resultItem}>
              <Text style={styles.resultDescription}>{result.description}</Text>
              <Text style={styles.resultInput}>
                Input: {JSON.stringify(result.input)}
              </Text>
              <Text style={[
                styles.resultOutput,
                result.isValidTimestamp ? styles.success : styles.warning
              ]}>
                Output: {result.result} ({typeof result.result})
              </Text>
              <Text style={styles.resultFormatted}>
                Ngày: {result.formattedDate}
              </Text>
              <View style={styles.statusContainer}>
                <Text style={[
                  styles.status,
                  result.isNumber ? styles.success : styles.error
                ]}>
                  {result.isNumber ? '✓ Là số' : '✗ Không phải số'}
                </Text>
                <Text style={[
                  styles.status,
                  result.isValidTimestamp ? styles.success : styles.warning
                ]}>
                  {result.isValidTimestamp ? '✓ Timestamp hợp lệ' : '⚠ Timestamp = 0'}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_darker_color,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    marginTop: 10,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: ColorThemes.light.neutral_text_title_color,
  },
  resultItem: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  resultDescription: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  resultInput: {
    fontSize: 12,
    marginBottom: 4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  resultOutput: {
    fontSize: 12,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  resultFormatted: {
    fontSize: 12,
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  status: {
    fontSize: 11,
    fontWeight: '500',
  },
  success: {
    color: 'green',
  },
  warning: {
    color: 'orange',
  },
  error: {
    color: 'red',
  },
});

export default DateConversionDemo;
