// import {
//   View,
//   Text,
//   Button,
//   Image,
//   Alert,
//   ActivityIndicator,
// } from 'react-native';

// import { TouchableOpacity, StyleSheet } from 'react-native';
// import { User, GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
// import auth from '@react-native-firebase/auth';
// import AppSvg from '../../../component/AppSvg';
// import { ColorSkin } from '../../../assets/skin/colors';
// import { TypoSkin } from '../../../assets/skin/typography';
// import { AppIcons } from '../../../component/AppSvg/AppIcons';

// // google
// const webClientId =
//   '632449728851-i72fbju5hl0jhkan6ncbv3uplk7ej736.apps.googleusercontent.com';
// interface Props {
//   onAuthSuccess: (value: User) => void;
//   onLoading: (value: boolean) => void;
//   isLoading: boolean;
// }

// GoogleSignin.configure({
//   offlineAccess: true,
//   webClientId: webClientId,
// });
// const GoogleLogin = (props: Props) => {
//   const { onAuthSuccess, onLoading, isLoading } = props;

//   const signIn = async () => {
//     if (isLoading) {
//       return;
//     }
//     onLoading(true);
//     try {
//       await GoogleSignin.signOut();
//       await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
//       const userInfo = await GoogleSignin.signIn();
//       // console.log("userInfo google: ", userInfo);
//       let userProfile = userInfo.data;

//       onAuthSuccess(userProfile as User);
//       onLoading(false);
//       // Create a Google credential with the token
//       const googleCredential = auth.GoogleAuthProvider.credential(userProfile?.idToken ?? '');
//       // Sign-in the user with the credential
//       auth().signInWithCredential(googleCredential);
//     } catch (error: any) {

//       if (error?.code === statusCodes.SIGN_IN_CANCELLED) {
//         onLoading(false);

//       } else if (error?.code === statusCodes.IN_PROGRESS) {
//       } else if (error?.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
//         Alert.alert(
//           'Thông báo',
//           'Dịch vụ hiển tại đang không có sẵn, thử lại sau!',
//           [{ text: 'OK', onPress: () => { } }],
//           { cancelable: false },
//         );
//       } else {
//         // console.log(error?.toString());

//         Alert.alert(
//           'Thông báo',
//           'Error! Try again later!',
//           [{ text: 'OK', onPress: () => { } }],
//           {
//             cancelable: false,
//           },
//         );
//       }
//       onLoading(false);

//     }
//   };
//   return (
//     <TouchableOpacity onPress={signIn} style={styles.TouchStyle}>
//       {isLoading ? (
//         <View style={styles.TouchStyle}>
//           <View style={styles.loading}>
//             <ActivityIndicator size="large" />
//           </View>
//         </View>
//       ) : (
//         <View style={styles.TouchStyle}>
//           <AppSvg SvgSrc={AppIcons.logoGoogle} size={20} />
//           <Text
//             style={[
//               TypoSkin.buttonText1,
//               { color: ColorSkin.body, fontSize: 18, paddingLeft: 8 },
//             ]}>
//             Đăng nhập với Google
//           </Text>
//         </View>
//       )}
//     </TouchableOpacity>
//   );
// };
// const styles = StyleSheet.create({
//   TouchStyle: {
//     flexDirection: 'row',
//     width: '100%',
//     height: 44,
//     borderRadius: 25,
//     borderWidth: 1,
//     borderColor: ColorSkin.border1,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   loading: {
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
// });
// export default GoogleLogin;
