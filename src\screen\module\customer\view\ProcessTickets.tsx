import {useNavigation} from '@react-navigation/native';
import React, {useState, useEffect, useRef} from 'react';
import {View, FlatList} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import AppButton from '../../../../component/button';
import {Winicon} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import EmptyPage from '../../../../project-component/empty-page';
import {CardToiletHoriSkeleton} from '../../../../project-component/skeletonCard';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
  useSelectorToiletState,
} from '../../../../redux/hooks/hooks';
import {RootScreen} from '../../../../router/router';
import {DataController} from '../../../base-controller';
import {useForm} from 'react-hook-form';
import {BaseDA} from '../../../baseDA';
import {Ultis} from '../../../../utils/Utils';
import {TicketType} from '../../service/components/da';
import ConfigAPI from '../../../../config/configApi';
import {CustomerType} from '../../../../redux/reducers/user/da';
import {FPopup, showPopup} from '../../../../component/popup/popup';
import {PopupViewTicket} from '../../ticket/components/TicketCard';

export default function ProcessTicketHome({isRefresh}: any) {
  const navigation = useNavigation<any>();
  const ticketController = new DataController('Ticket');
  const [data, setData] = useState({data: Array<any>(), totalCount: undefined});
  const [loading, setLoading] = useState(false);
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const methods = useForm({shouldFocusError: false});
  const [relatives, setRelatives] = useState<Array<any>>([]);
  const {onLoading, myToilet} = useSelectorToiletState();
  const popupRef = useRef<any>();

  const [assignees, setAssignees] = useState<Array<any>>([]);

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (isRefresh) {
      setData({data: [], totalCount: undefined});
      getData();
    }
  }, [isRefresh]);

  const getData = async () => {
    setLoading(true);
    let query = '';

    if (company?.Id === ConfigAPI.ktxCompanyId) {
      query += ` @Ktx:{true}`;
    } else {
      if (user?.Type === CustomerType.partner) {
        const toiletServicesController = new DataController('ToiletServices');
        const toiletServiceIds = await toiletServicesController.group({
          searchRaw: `@CustomerId:{${user.Id}} @Status:[1 +inf]`,
          reducers: `LOAD * GROUPBY 0 REDUCE TOLIST 1 @Id AS ids`,
        });
        if (toiletServiceIds.code === 200)
          var servicesIds = toiletServiceIds.data[0]?.ids;
      }
      if (servicesIds?.length && myToilet?.length)
        query += `((@ToiletId:{${myToilet.map(e => e.Id).join(' | ')}}) | (@ToiletServicesId:{${servicesIds.join(' | ')}}))`;
      else if (servicesIds?.length)
        query += ` @ToiletServicesId:{${servicesIds.join(' | ')}}`;
      else if (myToilet?.length)
        query += ` @ToiletId:{${myToilet.map(e => e.Id).join(' | ')}}`;
      else if (company?.Id !== ConfigAPI.ktxCompanyId) {
        setLoading(false);
        return setData({data: [], totalCount: undefined});
      } else {
        setLoading(false);
        return setData({data: [], totalCount: undefined});
      }
    }

    const res = await ticketController.aggregateList({
      page: 1,
      size: 5,
      searchRaw: query.length ? query : '*',
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const customerIds = res.data
        .map((e: any) => e.CustomerId)
        .filter(
          (id: any, i: any, arr: string | any[]) => id && arr.indexOf(id) === i,
        );
      if (customerIds.length) {
        const customerController = new DataController('Customer');
        customerController.getByListId(customerIds).then(cusRes => {
          if (cusRes.code === 200)
            setAssignees(
              cusRes.data.map((e: any) => ({
                ...e,
                bgColor: Ultis.generateRandomColor(),
              })),
            );
        });
      }

      const _fileId = methods.getValues('_files') ?? [];
      const _tmpFileIds = res.data
        .map((e: any) => e?.File?.split(','))
        ?.flat(Infinity)
        .filter(
          (id: any) =>
            id?.length &&
            _fileId.every((e: any) => e.Id !== id) &&
            !id.startsWith('http'),
        );
      BaseDA.getFilesInfor(_tmpFileIds).then(resFile => {
        if (resFile.code === 200)
          methods.setValue('_files', [
            ..._fileId,
            ...resFile.data.filter((e: any) => e !== undefined && e !== null),
          ]);
      });
      setData({data: res.data, totalCount: res.totalCount});
    }
    setLoading(false);
  };

  const getRelatives = async () => {
    const toiletIds = data.data
      .map((e: any) => e.ToiletId)
      .filter(
        (id: any, i: any, arr: string | any[]) => id && arr.indexOf(id) === i,
      );
    const toiletServicesIds = data.data
      .map((e: any) => e.ToiletServicesId)
      .filter(
        (id: any, i: any, arr: string | any[]) => id && arr.indexOf(id) === i,
      );
    const toiletController = new DataController('Toilet');
    const toiletServicesController = new DataController('ToiletServices');
    const results = [];
    if (toiletIds.length) {
      const res = await toiletController.getByListId(toiletIds);
      if (res.code === 200) results.push(...res.data);
    }
    if (toiletServicesIds.length) {
      const res = await toiletServicesController.getByListId(toiletServicesIds);
      if (res.code === 200) results.push(...res.data);
    }
    setRelatives(results);
  };

  useEffect(() => {
    if (data.data.length) getRelatives();
  }, [data.data.length]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
        marginTop: 32,
        borderRadius: 8,
      }}>
      <FPopup ref={popupRef} />
      <ListTile
        title="Yêu cầu cần xử lý"
        style={{
          borderRadius: 0,
          backgroundColor: ColorThemes.light.transparent,
        }}
        titleStyle={[
          TypoSkin.heading7,
          {color: ColorThemes.light.neutral_text_title_color},
        ]}
        trailing={
          user || owner ? (
            <AppButton
              onPress={() => {
                navigation.navigate(RootScreen.MyTicketList);
              }}
              title={'Tất cả'}
              backgroundColor={ColorThemes.light.transparent}
              borderColor="transparent"
              textStyle={TypoSkin.buttonText3}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              suffixIconSize={16}
            />
          ) : null
        }
      />
      {data.data.length == 0 && !loading ? (
        <View style={{flex: 1}}>
          <EmptyPage hideImg title="Không có yêu cầu nào" />
        </View>
      ) : null}
      <FlatList
        scrollEnabled={false}
        // keyExtractor={item => "_" + item.id}
        data={data.data}
        style={{marginHorizontal: 16, marginBottom: 16}}
        ItemSeparatorComponent={() => <View style={{height: 8}} />}
        renderItem={({item, index}) => {
          var typeLabel = '';
          switch (item.Type) {
            case TicketType.services:
              typeLabel = 'Phản ánh chất lượng dịch vụ';
              break;
            case TicketType.feedback:
              typeLabel = 'Phản hồi/góp ý';
              break;
            case TicketType.accident:
              typeLabel = 'Tai nạn sự cố';
              break;
            default:
              break;
          }
          var relativeData: any = undefined;
          if (item?.ToiletId) {
            relativeData = relatives.find(e => e?.Id === item.ToiletId);
          } else if (item?.ToiletServicesId) {
            relativeData = relatives.find(e => e?.Id === item.ToiletServicesId);
          }
          const customer = assignees.find(e => e.Id === item?.CustomerId);
          const _files = methods.getValues('_files') ?? [];
          const _fileInfor = _files.filter((e: any) =>
            item.File?.includes(e?.Id),
          );
          return (
            <ListTile
              key={`tickets-${index}`}
              onPress={() => {
                console.log(1);
                showPopup({
                  ref: popupRef,
                  enableDismiss: true,
                  children: (
                    <PopupViewTicket
                      relativeData={relativeData}
                      item={item}
                      index={index}
                      fileInfor={_fileInfor}
                      typeLabel={typeLabel}
                      ref={popupRef}
                    />
                  ),
                });
              }}
              style={{
                padding: 12,
                gap: 8,
                backgroundColor: ColorThemes.light.white,
              }}
              title={`${index + 1}. ${item?.Name ?? '-'}`}
              subtitle={`Loại yêu cầu: ${typeLabel ?? '-'}`}
              titleStyle={[
                TypoSkin.heading8,
                {color: ColorThemes.light.neutral_text_title_color},
              ]}
              trailing={<Winicon src="outline/user interface/view" size={16} />}
            />
          );
        }}
        ListEmptyComponent={() =>
          loading ? (
            Array.from({length: 3}).map((_, index) => (
              <CardToiletHoriSkeleton key={index} />
            ))
          ) : (
            <View />
          )
        }
      />
    </View>
  );
}
