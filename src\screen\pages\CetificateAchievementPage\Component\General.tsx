import React from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import TimelineProgress from './TimelineProgress';
import InfoSXT from './InfoSXT';
import {ColorThemes} from '../../../../assets/skin/colors';
const General: React.FC<{toiletId: string}> = ({toiletId}) => {
  return (
    <ScrollView style={styles.container}>
      <InfoSXT toiletId={toiletId} />
      <TimelineProgress toiletId={toiletId} />
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 16,
  },
});
export default General;
