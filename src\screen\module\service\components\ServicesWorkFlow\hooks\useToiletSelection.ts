import {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {
  useSelectorToiletState,
  useSelectorCustomerState,
} from '../../../../../../redux/hooks/hooks';
import {ToiletActions} from '../../../../../../redux/reducers/toilet/reducer';
import {ToiletStatus} from '../../da';
import type {ToiletItem, ServiceType} from '../types';
import {SERVICES_REQUIRING_TOILET_SELECTION, TABS} from '../constants';

export const useToiletSelection = (serviceType: ServiceType) => {
  const dispatch = useDispatch();
  const myToilet = useSelectorToiletState().myToilet;
  const loadingToilet = useSelectorToiletState().onLoading;
  const user = useSelectorCustomerState().data;

  const [tab, setTab] = useState<number>(TABS.SERVICE_FORM);
  const [selected, setSelected] = useState<ToiletItem | undefined>();

  // Fetch toilets when user is available
  useEffect(() => {
    if (user) {
      ToiletActions.getAll(dispatch, user?.Id ?? '');
    }
  }, [user, dispatch]);

  // Auto-switch to toilet selection tab if needed
  useEffect(() => {
    const requiresToiletSelection =
      SERVICES_REQUIRING_TOILET_SELECTION.includes(serviceType);
    const hasRunningToilets =
      myToilet &&
      myToilet.filter(e => e.Status === ToiletStatus.run).length > 0;

    if (requiresToiletSelection && user && hasRunningToilets) {
      setTab(TABS.TOILET_SELECTION);
    }
  }, [myToilet?.length, user, serviceType]);

  const runningToilets =
    myToilet?.filter(toilet => toilet.Status === ToiletStatus.run) || [];

  const selectToilet = (toilet: ToiletItem) => {
    setSelected(toilet);
    setTab(TABS.SERVICE_FORM);
  };

  const skipToiletSelection = () => {
    setTab(TABS.SERVICE_FORM);
  };

  return {
    tab,
    setTab,
    selected,
    setSelected: selectToilet,
    runningToilets,
    loadingToilet: loadingToilet || false,
    skipToiletSelection,
  };
};
