import React, {useState} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {CheckoutRouteParams, PaymentMethod, UseCheckoutReturn} from '../types';

export const paymentData: PaymentMethod[] = [
  {id: 1, name: 'Ship COD'},
  {id: 2, name: 'VNPay'},
];

export const useCheckout = (): UseCheckoutReturn => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const dispatch = useDispatch<any>();
  
  // State
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDone, setDone] = useState(false);
  const [payment, setPayment] = useState<PaymentMethod>(paymentData[0]);
  
  // Refs
  const btsRef = React.useRef<any>(null);
  const dialogRef = React.useRef<any>(null);
  
  // Get data from route params
  const {items = [], address = null} = (route.params as CheckoutRouteParams) || {};
  const customer = useSelectorCustomerState().data;
  const customerAddress = useSelectorCustomerState().myAddress;
  
  // Initialize customer addresses
  React.useEffect(() => {
    if (customer) {
      dispatch(CustomerActions.getAddresses(customer.Id));
    }
  }, [customer, dispatch]);
  
  // Placeholder for submit order function - will be implemented in useOrderProcessing
  const submitOrder = () => {
    console.log('Submit order called');
  };
  
  return {
    // State
    items,
    address,
    customer,
    customerAddress,
    isProcessing,
    isDone,
    payment,
    
    // Actions
    setPayment,
    submitOrder,
    
    // Refs
    dialogRef,
    btsRef,
    
    // Internal state setters (for use by other hooks)
    setIsProcessing,
    setDone,
  } as UseCheckoutReturn & {
    setIsProcessing: (processing: boolean) => void;
    setDone: (done: boolean) => void;
  };
};
