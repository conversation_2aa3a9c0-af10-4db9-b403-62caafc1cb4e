import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {Ultis} from '../../../../../utils/Utils';
import {OrderListItemProps} from '../types';
import OrderStatusBadge from './OrderStatusBadge';
import RatingButton from './RatingButton';

const OrderListItem: React.FC<OrderListItemProps> = ({
  item,
  status,
  onPress,
  onRatingPress,
}) => {
  const handlePress = () => {
    onPress(item.Id);
  };

  const handleRatingPress = () => {
    if (onRatingPress) {
      onRatingPress(item);
    }
  };

  return (
    <View style={{flex: 1}}>
      <ListTile
        onPress={handlePress}
        style={styles.listTileContainer}
        listtileStyle={styles.listTileContent}
        title={
          <View style={styles.shopTitleContainer}>
            <Winicon
              src="fill/shopping/store"
              size={16}
              color={ColorThemes.light.primary_main_color}
            />
            <Text style={styles.shopName}>{item?.Shop?.Name}</Text>
          </View>
        }
        subtitle={
          <View style={styles.orderInfoContainer}>
            <Text style={styles.orderCode}>Mã đơn hàng: {item?.Code}</Text>
            <Text style={styles.orderDate}>
              {item?.DateCreated
                ? Ultis.formatDateTime(item?.DateCreated, true)
                : ''}
            </Text>
          </View>
        }
        trailing={
          <View style={styles.trailingContainer}>
            <OrderStatusBadge status={status} />
          </View>
        }
        bottom={
          <View style={styles.bottomContainer}>
            <View style={styles.totalAmountContainer}>
              <Text style={styles.totalAmountLabel}>Tổng tiền:</Text>
              <Text style={styles.totalAmountValue}>
                {Ultis.money(item?.Value)} đ
              </Text>
            </View>
            {status === 3 && onRatingPress && (
              <RatingButton
                item={item}
                onPress={handleRatingPress}
                disabled={item.isRated}
              />
            )}
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  listTileContainer: {
    padding: 16,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  listTileContent: {
    alignItems: 'flex-start',
  },
  shopTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shopName: {
    ...TypoSkin.title3,
    marginLeft: 8,
  },
  orderInfoContainer: {
    gap: 8,
    paddingTop: 8,
  },
  orderCode: {
    ...TypoSkin.title4,
    color: '#999',
  },
  orderDate: {
    ...TypoSkin.title4,
    color: '#999',
  },
  trailingContainer: {
    paddingTop: 4,
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  bottomContainer: {
    paddingTop: 16,
    flex: 1,
    width: '100%',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  totalAmountContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  totalAmountLabel: {
    ...TypoSkin.title4,
  },
  totalAmountValue: {
    ...TypoSkin.title3,
    color: '#DA251D',
  },
});

export default OrderListItem;
