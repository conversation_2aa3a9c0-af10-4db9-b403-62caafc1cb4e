import React, {useState, useEffect} from 'react';
import {useForm} from 'react-hook-form';
import {
  useSelectorCustomerState,
  useSelectorToiletState,
} from 'redux/hooks/hooks';
import {DataController} from 'screen/base-controller';
import {ToiletServiceStatus} from 'screen/module/service/components/da';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {RoleDa} from 'screen/module/role/roleDa';
import {CustomerItem} from 'redux/reducers/user/da';

export interface WorkData {
  data: any[];
  totalCount: number | undefined;
}

export interface UseMyOrderTabReturn {
  // State
  works: WorkData;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  searchValue: string;
  guests: any[];
  customer?: CustomerItem;
  // Form methods
  filterMethods: any;

  // Actions
  setSearchValue: (value: string) => void;
  setWorks: React.Dispatch<React.SetStateAction<WorkData>>;
  getData: (page?: number, isLoadMore?: boolean) => Promise<void>;
  onRefresh: () => Promise<void>;
  loadMore: () => Promise<void>;
}

export const useMyOrderTab = (): UseMyOrderTabReturn => {
  const {onLoading} = useSelectorToiletState();
  const customer = useSelectorCustomerState().data;
  const roleDa = new RoleDa();
  const [isRefreshing, setRefreshing] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [works, setWorks] = useState<WorkData>({
    data: [],
    totalCount: undefined,
  });
  const [searchValue, setSearchValue] = useState('');
  const [guests, setGuests] = useState<Array<any>>([]);

  const PAGE_SIZE = 10;

  const filterMethods = useForm({shouldFocusError: false});
  const serviceController = new DataController('ToiletServices');

  const getData = async (page: number = 1, isLoadMore: boolean = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setCurrentPage(1);
        setHasMore(true);
        if (!isRefreshing) {
          setWorks({data: [], totalCount: undefined});
        }
      }

      // Build search query components
      const statusQuery = buildStatusQuery();
      const categoryQuery = buildCategoryQuery();
      const textSearchQuery = buildTextSearchQuery();
      const customerQuery = await buildCustomerQuery();

      // Combine all query parts
      const searchRawQuery = [
        statusQuery,
        categoryQuery,
        textSearchQuery,
        customerQuery,
      ]
        .filter(Boolean)
        .join(' ');

      // Helper functions for building query parts
      function buildStatusQuery(): string {
        const attributeIds = filterMethods.watch('AttributeId');

        if (attributeIds?.length) {
          const statusParts = attributeIds.map((id: any, index: number) => {
            const separator = index === attributeIds.length - 1 ? '' : ' | ';
            return `@Status:[${id}]${separator}`;
          });
          return `(${statusParts.join('')})`;
        }

        return `(@Status:[${ToiletServiceStatus.register} ${ToiletServiceStatus.liquid}])`;
      }

      function buildCategoryQuery(): string {
        const categoryIds = filterMethods.watch('CateServicesId');

        if (categoryIds?.length) {
          return `(@CateServicesId:{${categoryIds.join(' | ')}})`;
        }

        return '';
      }

      function buildTextSearchQuery(): string {
        if (searchValue && searchValue.trim()) {
          // Search in multiple fields: ToiletName, CustomerName, etc.
          const trimmedSearch = searchValue.trim();
          return `@Name:(*${trimmedSearch}*) `;
        }
        return '';
      }

      async function buildCustomerQuery() {
        const roleInfo = await roleDa.getCustomerRole(customer?.Id ?? '');
        if (roleInfo.role === 'customer')
          return `@CustomerMobile: (${customer?.Mobile})`;
        if (!roleInfo.isCompany)
          return `@CustomerMobile: (${customer?.Mobile})`;
        return `@CompanyOrderId: (${roleInfo.company?.Id})`;
      }
      const servicesToilet = await serviceController.aggregateList({
        page: page,
        size: PAGE_SIZE,
        searchRaw: searchRawQuery,
      });
      if (servicesToilet.code === 200) {
        const toiletIds = servicesToilet.data
          .map((e: any) => e.ToiletId)
          .filter(
            (v: any, i: any, a: any | any[]) => v?.length && a.indexOf(v) === i,
          );

        if (toiletIds.length) {
          const toiletController = new DataController('Toilet');
          const customerIds = await toiletController.group({
            searchRaw: `@Id:{${toiletIds.join(' | ')}}`,
            reducers: 'GROUPBY 1 @CustomerId REDUCE TOLIST 1 @Id AS ids',
          });

          const customerController = new DataController('Customer');
          customerController
            .getByListId(customerIds.data.map((e: any) => e.CustomerId))
            .then(res => {
              if (res.code === 200)
                setGuests(
                  customerIds.data.map((e: any) => {
                    const customer = res.data.find(
                      (el: any) => el.Id === e.CustomerId,
                    );
                    return customer ? {...customer, ...e} : e;
                  }),
                );
            });

          await toiletController
            .getListSimple({
              page: page,
              size: PAGE_SIZE,
              query: `@Id:{${toiletIds.join(' | ')}}`,
              returns: ['Id', 'Name'],
            })
            .then(res => {
              if (res.code === 200) {
                servicesToilet.data = servicesToilet.data.map((e: any) => {
                  const toilet = res.data.find(
                    (el: any) => el.Id === e.ToiletId,
                  );
                  return toilet ? {...e, ToiletName: toilet?.Name} : e;
                });
                const newData = servicesToilet.data.map((e: any) => {
                  const toilet = res.data.find(
                    (el: any) => el.Id === e.ToiletId,
                  );
                  return toilet ? {...e, ToiletName: toilet?.Name} : e;
                });

                if (isLoadMore) {
                  setWorks(prev => ({
                    data: [...prev.data, ...newData],
                    totalCount: servicesToilet.totalCount,
                  }));
                } else {
                  setWorks({
                    data: newData,
                    totalCount: servicesToilet.totalCount,
                  });
                }
              } else {
                if (isLoadMore) {
                  setWorks(prev => ({
                    data: [...prev.data, ...servicesToilet.data],
                    totalCount: servicesToilet.totalCount,
                  }));
                } else {
                  setWorks({
                    data: servicesToilet.data,
                    totalCount: servicesToilet.totalCount,
                  });
                }
              }
            });
        } else {
          if (isLoadMore) {
            setWorks(prev => ({
              data: [...prev.data, ...servicesToilet.data],
              totalCount: servicesToilet.totalCount,
            }));
          } else {
            setWorks({
              data: servicesToilet.data,
              totalCount: servicesToilet.totalCount,
            });
          }
        }
        setLoading(false);
        setLoadingMore(false);

        // Check if there are more items to load
        const currentDataLength = isLoadMore ? works.data.length : 0;
        const totalLoaded = currentDataLength + servicesToilet.data.length;
        setHasMore(totalLoaded < (servicesToilet.totalCount || 0));

        if (isLoadMore) {
          setCurrentPage(page);
        } else {
          setCurrentPage(1);
        }
      } else {
        showSnackbar({
          message: servicesToilet.message,
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    setCurrentPage(1);
    setHasMore(true);
    setWorks({data: [], totalCount: undefined});
    await getData(1, false);
  };

  const loadMore = async () => {
    if (!isLoadingMore && hasMore && !isLoading) {
      await getData(currentPage + 1, true);
    }
  };

  useEffect(() => {
    getData();
  }, [customer, onLoading, searchValue]);

  return {
    works,
    customer,
    isLoading,
    isRefreshing,
    isLoadingMore,
    hasMore,
    searchValue,
    guests,
    filterMethods,
    setSearchValue,
    setWorks,
    getData,
    onRefresh,
    loadMore,
  };
};
