import React from 'react';
import {useActionButtonsLogic} from './hooks/useActionButtonsLogic';
import {
  RegisterStatusButtons,
  ResearchStatusButtons,
  KtxUserButtons,
} from './components';
import {ToiletServiceItem} from 'types/toiletServiceType';
import {ToiletServiceStatus} from 'screen/module/service/components/da';
import {CustomerItem} from 'redux/reducers/user/da';

interface ActionButtonsProps {
  serviceData: ToiletServiceItem;
  data: any;
  customer: CustomerItem;
  isEditable: boolean;
  cateServices: any[];
  rejectReasons: any[];
  onReject: () => void;
  onSubmit: () => void;
  setServiceData: (data: any) => void;
  setSurveyData: (data: any) => void;
  setLoading: (loading: boolean) => void;
  getSurveyData: () => Promise<void>;
  dialogRef: any;
  popupRef: any;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  serviceData,
  data,
  customer,
  isEditable,
  cateServices,
  rejectReasons,
  onReject,
  onSubmit,
  setServiceData,
  setSurveyData,
  setLoading,
  getSurveyData,
  dialogRef,
  popupRef,
}) => {
  const {
    navigation,
    isKtxUser,
    methods,
    handleAcceptOrder,
    handleCompleteConsulting,
  } = useActionButtonsLogic({
    serviceData,
    rejectReasons,
    setServiceData,
    setSurveyData,
    setLoading,
    getSurveyData,
    onSubmit,
    dialogRef,
    popupRef,
  });

  // Register status buttons
  if (isEditable && serviceData?.Status === ToiletServiceStatus.register) {
    return (
      <RegisterStatusButtons
        methods={methods}
        dialogRef={dialogRef}
        popupRef={popupRef}
        onReject={onReject}
        onAcceptOrder={handleAcceptOrder}
      />
    );
  }

  // Research status buttons (survey completion)
  if (isEditable && serviceData?.Status === ToiletServiceStatus.research) {
    return (
      <ResearchStatusButtons
        dialogRef={dialogRef}
        onCompleteSurvey={handleAcceptOrder}
      />
    );
  }

  // KTX user buttons
  if (isKtxUser && serviceData?.Status === ToiletServiceStatus.register) {
    return (
      <KtxUserButtons
        data={data}
        dialogRef={dialogRef}
        popupRef={popupRef}
        serviceData={serviceData}
        customer={customer}
        cateServices={cateServices}
        navigation={navigation}
        setServiceData={setServiceData}
        onCompleteConsulting={handleCompleteConsulting}
      />
    );
  }

  return null;
};
