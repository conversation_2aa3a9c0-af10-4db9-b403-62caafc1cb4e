import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import {productAction} from '../../../../redux/actions/productAction';
import {getRandomObjects} from '../../../../utils/arrayUtils';
import {ProductItem} from '../../../../types/ProductType';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import ProductFavoriteCard from '../component/ProductFavoriteCard';
import {navigate, RootScreen} from '../../../../router/router';

const {width: screenWidth} = Dimensions.get('window');

// Header Component
interface HeaderProps {
  title: string;
  onSeeMore?: () => void;
}

const Header: React.FC<HeaderProps> = ({title, onSeeMore}) => (
  <View style={styles.header}>
    <Text style={styles.headerTitle}>{title}</Text>
    <TouchableOpacity onPress={onSeeMore}>
      <Text style={styles.seeMore}>Xem thêm</Text>
    </TouchableOpacity>
  </View>
);

// Function to chunk array into groups of specified size
const chunkArray = (array: ProductItem[], size: number): ProductItem[][] => {
  const chunkedArray: ProductItem[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunkedArray.push(array.slice(i, i + size));
  }
  return chunkedArray;
};

// Main Screen Component
const MostSearchedScreen: React.FC<{
  onSeeMore: () => void;
  onRefresh: boolean;
  title?: string; // Optional title
}> = ({onSeeMore, onRefresh, title = 'Được tìm kiếm nhiều'}) => {
  const [chunkedProducts, setChunkedProducts] = useState<ProductItem[][]>([]);

  useEffect(() => {
    getData();
  }, [onRefresh]);

  const getData = async () => {
    let data = await productAction.find({
      page: 1,
      size: 100,
    });
    if (data.length > 15) {
      data = getRandomObjects(data, 15);
    }
    setChunkedProducts(chunkArray(data, 5));
  };

  const navigationToDetail = (product: ProductItem) => {
    navigate(RootScreen.DetailProductPage, {id: product.Id});
  };

  const handleSeeMore = useCallback(() => {
    onSeeMore();
  }, [onSeeMore]);

  const renderProductPage = ({item}: {item: ProductItem[]}) => (
    <View style={styles.productPage}>
      {item.map((product: ProductItem) => (
        <ProductFavoriteCard
          key={product.Id}
          item={product}
          onPressFavorite={navigationToDetail}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.screen}>
      <Header title={title} onSeeMore={handleSeeMore} />
      {chunkedProducts?.length !== 0 && (
        <FlatList
          data={chunkedProducts}
          renderItem={renderProductPage}
          keyExtractor={(item, index) => `page-${index}`}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          contentContainerStyle={styles.flatListContent}
        />
      )}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  screen: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 12,
  },
  headerTitle: {
    ...TypoSkin.heading6,
  },
  seeMore: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.secondary3_text_color,
  },
  separator: {
    height: 1,
    backgroundColor: '#EAEAEA',
    marginLeft: 96,
  },
  flatListContent: {
    paddingBottom: 16,
  },
  productPage: {
    width: screenWidth - 40,
  },
});

export default MostSearchedScreen;
