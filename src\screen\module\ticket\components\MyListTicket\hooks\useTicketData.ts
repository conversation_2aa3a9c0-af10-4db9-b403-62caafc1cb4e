import {useState, useEffect} from 'react';
import {DataController} from '../../../../../../../base-controller';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
  useSelectorToiletState,
} from '../../../../../../../../redux/hooks/hooks';
import {CustomerType} from '../../../../../../../../redux/reducers/user/da';
import {TicketStatus} from '../../../../../../service/components/da';
import ConfigAPI from '../../../../../../../../config/configApi';

export const useTicketData = (tab: number, searchValue: string) => {
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const {onLoading, myToilet} = useSelectorToiletState();

  const [result, setResult] = useState<Array<any>>([]);
  const [isRefreshing, setRefreshing] = useState(false);

  const ticketController = new DataController('Ticket');

  const getResult = async () => {
    if (tab === 0) {
      if (user?.Id !== ConfigAPI.adminKtxId) {
        if (user?.Type === CustomerType.partner) {
          const toiletServicesController = new DataController('ToiletServices');
          const toiletServiceIds = await toiletServicesController.group({
            searchRaw: `@CustomerId:{${user.Id}} @Status:[1 +inf]`,
            reducers: `LOAD * GROUPBY 0 REDUCE TOLIST 1 @Id AS ids`,
          });
          if (toiletServiceIds.code === 200)
            var servicesIds = toiletServiceIds.data[0]?.ids;
        }
      }
      let query = user?.Id === ConfigAPI.adminKtxId ? '*' : ``;
      if (servicesIds?.length && myToilet?.length)
        query += `((@ToiletId:{${myToilet.map(e => e.Id).join(' | ')}}) | (@ToiletServicesId:{${servicesIds.join(' | ')}}))`;
      else if (servicesIds?.length)
        query += ` @ToiletServicesId:{${servicesIds.join(' | ')}}`;
      else if (myToilet?.length)
        query += ` @ToiletId:{${myToilet.map(e => e.Id).join(' | ')}}`;

      const res = await ticketController.group({
        searchRaw: query,
        reducers: `GROUPBY 1 @Status REDUCE COUNT 0 AS _count`,
      });

      if (res.code === 200) setResult(res.data);
    }
  };

  const getTicketStats = () => {
    const processing = result
      .filter(
        e =>
          parseInt(e.Status ?? e) === TicketStatus.init ||
          parseInt(e.Status ?? e) === TicketStatus.processing,
      )
      .reduce((a, b) => (b['_count'] ? a + parseInt(b['_count']) : a + b), 0);

    const done = result
      .filter(e => parseInt(e.Status ?? e) === TicketStatus.done)
      .reduce((a, b) => (b['_count'] ? a + parseInt(b['_count']) : a + b), 0);

    const cancelled = result
      .filter(e => parseInt(e.Status ?? e) === TicketStatus.cancel)
      .reduce((a, b) => (b['_count'] ? a + parseInt(b['_count']) : a + b), 0);

    return {processing, done, cancelled};
  };

  useEffect(() => {
    if (user && !onLoading) getResult();
  }, [user, onLoading, tab]);

  return {
    result,
    isRefreshing,
    setRefreshing,
    getResult,
    getTicketStats,
  };
};
