import {Text, View} from 'react-native';
import ListTile from '../../../../../component/list-tile/list-tile';
import {Ultis} from '../../../../../utils/Utils';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {
  FDialog,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {ComponentStatus} from '../../../../../component/component-status';
import {useMemo, useRef} from 'react';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../../router/router';
import {
  TicketType,
  ToiletServiceStatus,
  ToiletStatus,
} from '../../../service/components/da';
import {useSelectorToiletState} from '../../../../../redux/hooks/hooks';
import {useDispatch} from 'react-redux';
import {ToiletActions} from '../../../../../redux/reducers/toilet/reducer';
import {DataController} from '../../../../base-controller';
import {FPopup, showPopup} from '../../../../../component/popup/popup';
import AddTicketPopup from 'screen/module/ticket/components/MyListTicket/components/AddTicketPopup';

export default function NewWorkCard({item, user, setWorks, customer}: any) {
  const dialogDelAccRef = useRef<any>();
  const popupRef = useRef<any>();
  const navigation = useNavigation<any>();
  const {myToilet} = useSelectorToiletState();
  const toiletItem = useMemo(
    () => myToilet?.find(e => e.Id === item.ToiletId),
    [item, myToilet],
  );

  const dispatch = useDispatch();

  return (
    <View>
      <FDialog ref={dialogDelAccRef} />
      <FPopup ref={popupRef} />
      <ListTile
        key={item.Id}
        onPress={() => {
          navigation.navigate(RootScreen.DetailWorkView, {
            Id: item.Id,
            Status: item.Status,
            Name: item.Name,
          });
        }}
        title={item?.Name ?? ''}
        titleStyle={{...TypoSkin.title3, paddingBottom: 8}}
        subtitle={
          <View style={{gap: 4}}>
            {toiletItem ? (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>{`Tên NVS: ${item?.ToiletName ?? toiletItem?.Name ?? ''}`}</Text>
            ) : null}
            {customer?.Id !== user?.Id &&
            item.CustomerMobile !== user?.Mobile ? (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>{`Khách hàng: ${customer?.Name ?? item.CustomerMobile ?? '-'}`}</Text>
            ) : null}
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>{`Ngày tạo: ${Ultis.datetoString(new Date(item.DateCreated))}`}</Text>
          </View>
        }
        bottom={
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
              paddingTop: 16,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            {/* actions */}
            {customer?.Id === user?.Id ? (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 16,
                  flex: 1,
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                }}>
                <Winicon
                  src="outline/user interface/view"
                  size={16}
                  onClick={() => {
                    console.log(1);
                    navigation.push(RootScreen.detailProject, {item: item});
                  }}
                />
                <Winicon
                  src={
                    toiletItem?.Favor
                      ? 'fill/user interface/fav-list'
                      : 'outline/user interface/fav-list'
                  }
                  onClick={() => {
                    ToiletActions.edit(dispatch, [
                      {...toiletItem, Favor: !toiletItem?.Favor},
                    ]);
                  }}
                  size={16}
                  color={
                    toiletItem?.Favor
                      ? ColorThemes.light.secondary1_main_color
                      : ColorThemes.light.neutral_text_subtitle_color
                  }
                />
                <Winicon
                  src="outline/sport/ticket"
                  size={16}
                  onClick={() => {
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <AddTicketPopup
                          type={TicketType.services}
                          toiletServicesId={item.Id}
                          parentPopupRef={popupRef}
                          onDone={() => {}}
                        />
                      ),
                    });
                  }}
                />
              </View>
            ) : null}
            {item?.CustomerId === user?.Id &&
            toiletItem?.CustomerId !== user?.Id ? (
              <View style={{flexDirection: 'row', flex: 1}}>
                <Winicon
                  src={
                    item?.Favor
                      ? 'fill/user interface/fav-list'
                      : 'outline/user interface/fav-list'
                  }
                  onClick={async () => {
                    const controller = new DataController('ToiletServices');
                    const newItem = {...item, Favor: !item.Favor};
                    const res = await controller.edit([newItem]);
                    if (res.code === 200) {
                      setWorks((w: any) => ({
                        data: w.data.map((e: any) =>
                          e.Id === newItem.Id ? newItem : e,
                        ),
                        totalCount: w.totalCount,
                      }));
                      showSnackbar({
                        message: 'Thao tác thành công',
                        status: ComponentStatus.SUCCSESS,
                      });
                    }
                  }}
                  size={16}
                  color={
                    toiletItem?.Favor
                      ? ColorThemes.light.secondary1_main_color
                      : ColorThemes.light.neutral_text_subtitle_color
                  }
                />
              </View>
            ) : null}
            <Text
              style={{
                flexDirection: 'row',
                flex: 1,
                width: '100%',
                alignItems: 'center',
                textAlign: 'right',
              }}>
              {' '}
              <Status item={item} status={item.Status} />
            </Text>
          </View>
        }
      />
    </View>
  );
}

// 1:
// Đơn hàng mới
//  2:
// Báo giá
//  3:
// Hợp đồng
//  4:
// Thiết kế
//  5:
// Thi công
//  6:
// Vận hành
//  7:
// Thanh lý hợp đồng
export const StatusData = [
  {
    key: ToiletStatus.register,
    title: 'Đơn hàng mới',
    backgrColor: ColorThemes.light.neutral_text_subtitle_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.consultant,
    title: 'Tư vấn/Báo giá',
    backgrColor: ColorThemes.light.primary_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.contract,
    title: 'Hợp đồng',
    backgrColor: ColorThemes.light.warning_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.design,
    title: 'Thiết kế',
    backgrColor: ColorThemes.light.success_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.build,
    title: 'Thi công',
    backgrColor: ColorThemes.light.secondary1_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.liquid,
    title: 'Thanh lý hợp đồng',
    backgrColor: ColorThemes.light.secondary2_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletStatus.run,
    title: 'Vận hành',
    backgrColor: ColorThemes.light.secondary3_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
];

// reject = 0,
// register = 1,
// research = 2,
// consultant = 3,
// sendCompleteQuote = 4,
// contract = 5,
// sendCompleteContract = 6,
// design = 7,
// sendCompleteDesign = 8,
// build = 9,
// sendCompleteBuild = 10,
// liquid = 11,
// run = 12
export const StatusToiletServiceData = [
  {
    key: ToiletServiceStatus.register,
    title: 'Đơn hàng mới',
    backgrColor: ColorThemes.light.neutral_text_subtitle_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.research,
    title: 'Đang tiếp nhận',
    backgrColor: ColorThemes.light.neutral_text_subtitle_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.consultant,
    title: 'Báo giá',
    backgrColor: ColorThemes.light.primary_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.sendCompleteQuote,
    title: 'Đang gửi báo giá',
    backgrColor: ColorThemes.light.primary_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.contract,
    title: 'Hợp đồng',
    backgrColor: ColorThemes.light.warning_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.sendCompleteContract,
    title: 'Đang gửi hợp đồng',
    backgrColor: ColorThemes.light.warning_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.design,
    title: 'Thiết kế',
    backgrColor: ColorThemes.light.success_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.sendCompleteDesign,
    title: 'Đang gửi thiết kế',
    backgrColor: ColorThemes.light.success_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.build,
    title: 'Thi công',
    backgrColor: ColorThemes.light.secondary1_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.sendCompleteBuild,
    title: 'Đang thi công',
    backgrColor: ColorThemes.light.secondary1_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.liquid,
    title: 'Thanh lý hợp đồng',
    backgrColor: ColorThemes.light.secondary2_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  {
    key: ToiletServiceStatus.run,
    title: 'Vận hành',
    backgrColor: ColorThemes.light.secondary3_main_color,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
];

export function Status({item, status}: any) {
  // console.log('==============item, status======================');
  // console.log(item, status);
  // console.log('====================================');
  if (!status) return <View></View>;
  var st = item?.CateServicesId
    ? StatusToiletServiceData.find(e => e.key === status)
    : StatusData.find(e => e.key === status);

  if (!st)
    return (
      <View
        style={{
          backgroundColor: ColorThemes.light.neutral_text_subtitle_color,
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 20,
        }}>
        <Text
          style={{color: ColorThemes.light.neutral_absolute_background_color}}>
          {status}
        </Text>
      </View>
    );
  if (st)
    return (
      <View
        style={{
          backgroundColor: st.backgrColor,
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 20,
        }}>
        <Text
          style={{
            color: st.color ?? ColorThemes.light.neutral_text_title_color,
          }}>
          {st.title ?? status}
        </Text>
      </View>
    );
}
