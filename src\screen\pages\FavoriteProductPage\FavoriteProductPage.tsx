import React, {useEffect, useState, useRef, useCallback} from 'react';
import {View, FlatList, StyleSheet, ActivityIndicator} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import TitleHeader from '../../layout/headers/TitleHeader';
import ProductFavoriteCard from '../../module/product/component/ProductFavoriteCard';
import Header from './components/Header';
import LoadMoreButton from '../../../component/LoadMoreButton';
import {useSelectorCustomerState} from '../../../redux/hooks/hooks';
import {ProductItem} from '../../../types/ProductType';
import productDA from '../../module/product/productDA';
import {DataController} from '../../base-controller';

const FavouriteProduct = () => {
  const customer = useSelectorCustomerState().data;
  const [data, setData] = useState<ProductItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  //state
  const [page, setPage] = useState(1);
  const size = 10; // Fixed size for pagination
  const [query, setQuery] = useState('');
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  //effect
  useEffect(() => {
    loadData();
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]); // Add query dependency to reload when search changes

  const loadData = async (isLoadMore = false) => {
    if (!customer?.Id) return;

    if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
      setPage(1); // Reset page for new search
    }

    try {
      const currentPage = isLoadMore ? page : 1;
      const res = await productDA.getFavoriteProduct(
        {
          page: currentPage,
          size: size,
          search: query,
        },
        customer.Id,
      );
      const {data, totalCount} = res;
      if (isLoadMore) {
        setData(prevData => [...prevData, ...data]);
      } else {
        setData(data);
      }
      setTotalCount(totalCount);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const onLoadMore = () => {
    if (!loadingMore) {
      setPage(prevPage => prevPage + 1);
      loadData(true);
    }
  };

  const handleSearch = useCallback((text: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setQuery(text);
    }, 1000);
  }, []);

  const handleFavoriteProduct = async (item: ProductItem) => {
    const customerId = customer?.Id;
    if (!customerId) {
      return;
    }

    try {
      await productDA.unFavoriteProduct({
        productId: item.Id,
        customerId,
      });

      // Update local state by removing the item
      setData(prevData => prevData.filter(product => product.Id !== item.Id));
      setTotalCount(prevCount => prevCount - 1);
    } catch (error) {
      console.error('Error removing favorite product:', error);
    }
  };

  return (
    <View style={styles.screen}>
      <TitleHeader
        title="Sản phẩm yêu thích"
        showSearch
        onSearch={handleSearch}></TitleHeader>
      {loading ? (
        <View style={styles.center}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_main_color}
          />
        </View>
      ) : (
        <View style={{flex: 1}}>
          <View style={styles.container}>
            <Header title="Sản phẩm yêu thích" productCount={data.length} />
            <FlatList
              data={data}
              renderItem={({item}) => (
                <ProductFavoriteCard
                  item={item}
                  showFavorite={true}
                  onPressFavorite={handleFavoriteProduct}
                />
              )}
              keyExtractor={item => item.Id}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={
                data.length < totalCount ? (
                  <LoadMoreButton onPress={onLoadMore} loading={loadingMore} />
                ) : null
              }
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  separator: {
    height: 16,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FavouriteProduct;
