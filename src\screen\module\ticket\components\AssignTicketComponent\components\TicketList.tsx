import React from 'react';
import {View, FlatList, StyleSheet} from 'react-native';
import EmptyPage from 'project-component/empty-page';
import {CardToiletHoriSkeleton} from 'project-component/skeletonCard';
import {TicketStatus, TicketType} from 'screen/module/service/components/da';
import {TicketType as TicketTypeInterface} from 'types/ticketType';
import TicketCard from '../../TicketCard';
import ConfigAPI from 'config/configApi';
import {useSelectorCustomerCompanyState} from 'redux/hooks/hooks';

interface TicketListProps {
  isLoading: boolean;
  data: any;
  relatives: any[];
  assignees: any[];
  methods: any;
  onUpdateTicket: (updatedTicket: TicketTypeInterface) => void;
}

const TicketList: React.FC<TicketListProps> = ({
  isLoading,
  data,
  relatives,
  assignees,
  methods,
  onUpdateTicket,
}) => {
  const company = useSelectorCustomerCompanyState().data;
  const getTypeLabel = (type: number) => {
    switch (type) {
      case TicketType.services:
        return 'Phản ánh chất lượng dịch vụ';
      case TicketType.feedback:
        return 'Phản hồi/góp ý';
      case TicketType.accident:
        return 'Tai nạn sự cố';
      default:
        return '';
    }
  };

  const renderItem = ({item, index}: any) => {
    const typeLabel = getTypeLabel(item.Type);
    let relativeData = undefined;

    if (item?.ToiletId) {
      relativeData = relatives.find(e => e?.Id === item.ToiletId);
    } else if (item?.ToiletServicesId) {
      relativeData = relatives.find(e => e?.Id === item.ToiletServicesId);
    }

    const customer = assignees.find(e => e.Id === item?.CustomerId);
    const _files = methods.watch('_files') ?? [];
    const _fileInfor = _files.filter((e: any) => item.File?.includes(e?.Id));

    const checkEdit =
      (item.Status !== TicketStatus.done &&
        item.Status !== TicketStatus.cancel) ||
      company?.Id === ConfigAPI.ktxCompanyId;

    return (
      <View style={{marginBottom: 16}}>
        <TicketCard
          key={index}
          index={index}
          relativeData={relativeData}
          customer={customer}
          edit={checkEdit}
          fileInfor={_fileInfor}
          methods={methods}
          item={item}
          typeLabel={typeLabel}
          onUpdateTicket={onUpdateTicket}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={data.data}
        scrollEnabled={false}
        style={styles.flatList}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${index}`}
        ListEmptyComponent={() =>
          isLoading ? (
            Array.from(Array(10)).map((_, index) => (
              <View key={index} style={styles.skeletonContainer}>
                <CardToiletHoriSkeleton />
              </View>
            ))
          ) : (
            <EmptyPage
              title="Không có yêu cầu nào cần xử lý"
              subtitle="Hãy nhanh chóng xử lý các yêu cầu của khách hàng gửi đến bạn nhé."
            />
          )
        }
        ListFooterComponent={() => <View style={styles.footer} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flatList: {
    flex: 1,
    marginHorizontal: 16,
  },
  skeletonContainer: {
    gap: 16,
  },
  footer: {
    height: 100,
  },
});

export default TicketList;
