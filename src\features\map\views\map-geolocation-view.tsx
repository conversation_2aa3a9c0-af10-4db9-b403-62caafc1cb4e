import React, { useEffect, useRef, useState } from 'react';
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { Dimensions, FlatList, Image, Linking, Platform, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import ScreenHeader from '../../../screen/layout/header';
import { ColorSkin } from '../../../assets/skin/colors';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faLocation, faLocationArrow, faLocationDot } from '@fortawesome/free-solid-svg-icons';
import { TypoSkin } from '../../../assets/skin/typography';
import { OutlineLocation } from '../../../assets/icon';
import { Ultis } from '../../../utils/Utils';
const sizess = Dimensions.get("screen");

export default function MapGeolocationView(props: any) {

    const mapRef = useRef<any>(null);
    const [currentRegion, setcurrentRegion] = useState<any>();

    const [isLoading, setIsLoading] = useState(false);
    const [address, setAddress] = useState<any>(null);

    useEffect(() => {
        animatedCurrentRegion()
    }, [])

    const animatedCurrentRegion = () => {
        Geolocation.getCurrentPosition((position) => {
            getAddressFromCoordinates(position.coords.latitude, position.coords.longitude)
            mapRef.current.animateToRegion({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
            });
            setcurrentRegion({
                latitude: position.coords.latitude ?? 21.040531,
                longitude: position.coords.longitude ?? 105.774083,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
            })
        });

    }

    const getAddressFromCoordinates = async (lat: number, lng: number) => {
        setIsLoading(true);
        const GEOCODING_API_URL = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=AIzaSyBrjZpmgCpST9GWPt7fCnr_EiQi-uL9SQM`;
        try {
            const response = await fetch(GEOCODING_API_URL);
            const data = await response.json();
            if (data && data.results && data.results.length > 0) {
                // console.log('Address:', data.results[0]);
                setAddress(Ultis.substringLastForMap(data.results[0].formatted_address))
            } else {
                console.error('Geocoding API error:', data.status);
            }
        } catch (error) {
            console.error('Geocoding request error:', error);
        }
        setIsLoading(false);
    };

    return (<View style={styles.container}>
        <ScreenHeader
            onBack={props.onBack}
            title='Chọn địa điểm'
            style={{ zIndex: 99, backgroundColor: ColorSkin.white, paddingTop: 38 }}
            action={<TouchableOpacity style={{ paddingRight: 16 }} onPress={() => {
                props.onDone(
                    { "formatted_address": address, "geometry": { "location": { "lat": currentRegion.latitude, "lng": currentRegion.longitude } } },
                )
            }}><Text style={[TypoSkin.title3, { color: ColorSkin.primary }]}>Xong</Text></TouchableOpacity>}
            bottom={
                <View style={{ gap: 16, paddingHorizontal: 16, paddingVertical: 8, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', borderTopWidth: 1, borderTopColor: ColorSkin.borderSide }}>
                    <OutlineLocation color={ColorSkin.primary} size={24} />
                    <Text style={[TypoSkin.title3, { color: ColorSkin.subtitle, flex: 1 }]} numberOfLines={2}>
                        {isLoading ? 'Đang tìm, đợi tí nhá...' : address}
                    </Text>
                </View>
            }
        />
        {/* map view */}
        <MapView
            ref={mapRef}
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={currentRegion}
            zoomEnabled={true}
            showsUserLocation={true}
            zoomControlEnabled={true}
            onRegionChangeComplete={(region) => {
                setcurrentRegion(region)
                getAddressFromCoordinates(region.latitude, region.longitude)
            }}
        >
        </MapView>

        <View style={{
            marginBottom: 16,
            position: 'absolute', left: sizess.width / 2 - 16, top: sizess.height / 2 - 16,
        }}>
            <FontAwesomeIcon icon={faLocation} color={ColorSkin.errorColor} size={32} />
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
            <TouchableOpacity
                onPress={animatedCurrentRegion}
                style={{
                    padding: 12,
                    margin: 24,
                    backgroundColor: ColorSkin.primary,
                    borderRadius: 40,
                }}>
                <FontAwesomeIcon icon={faLocationArrow} color={ColorSkin.white} size={28} />
            </TouchableOpacity>
        </View>
    </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "flex-end",
        justifyContent: "space-between",
        gap: 16,
    },
    map: {
        ...StyleSheet.absoluteFillObject,
        position: 'absolute'
    },
    ViewPitchContainer: {
        height: 150,
        paddingBottom: 16,
        width: '100%',
        backgroundColor: ColorSkin.white,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        shadowOpacity: 1,
        elevation: 20,
        shadowRadius: 20,
        shadowOffset: {
            width: 0,
            height: -4
        },
        shadowColor: "rgba(0, 0, 0, 0.03)"
    },
    viewPitchCard: {
        backgroundColor: "red",
        borderRadius: 8,
    },

    searchContainer: {
        width: '100%',
        flexDirection: 'row',
        backgroundColor: '#fff',
        gap: 8,
        paddingHorizontal: 16,
        paddingVertical: 8
    },

});
