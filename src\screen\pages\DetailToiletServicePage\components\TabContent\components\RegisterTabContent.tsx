import React from 'react';
import RegisterTab from 'screen/module/workplace/components/form/RegisterTab';

interface WorkData {
  Id: string;
  [key: string]: any;
}

interface RegisterTabContentProps {
  workData: WorkData[] | null;
  serviceData: any;
  setServiceData: (data: any) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  methods: any;
  guest: any;
  handleReject: () => void;
  handleSubmitRegister: () => void;
}

export default function RegisterTabContent({
  workData,
  serviceData,
  setServiceData,
  onRefresh,
  isRefreshing,
  methods,
  guest,
  handleReject,
  handleSubmitRegister,
}: RegisterTabContentProps) {
  return (
    <RegisterTab
      data={workData as any}
      serviceData={serviceData}
      setServiceData={setServiceData}
      onRefreshing={onRefresh}
      isRefreshing={isRefreshing}
      methodParent={methods}
      customer={guest}
      onReject={handleReject}
      onSubmit={handleSubmitRegister}
    />
  );
}
