import {View, TouchableOpacity, Text, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import {RootScreen} from '../../../../router/router';
import CommunityNavigation from '../miniApp/communityNavigator';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {KtxStackNavigator} from '../miniApp/ktxNavigator';
import {ColorThemes} from '../../../../assets/skin/colors';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Winicon} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import FastImage from 'react-native-fast-image';

const menuItems = [
  {
    title: 'KTX',
    route: 'navigateESchoolView',
    icon: 'outline/business/presentation',
    activeIcon: 'fill/education/presentation',
  },
  {
    title: 'Community',
    route: 'navigateCommunityView',
    icon: 'outline/user interface/f-chat',
    activeIcon: 'fill/user interface/f-chat',
  },
];

function CustomDrawerContent(props: any) {
  const {navigation} = props;
  const [selectedRoute, setSelectedRoute] = useState(menuItems[0].route); // Default to first route

  // Use require only once to avoid recreation on each render
  const logoSource = require('../../../../assets/logo.png');

  return (
    <View style={styles.container}>
      <SafeAreaView edges={['top']} style={styles.header}>
        <TouchableOpacity onPress={() => navigation.closeDrawer()}>
          <FastImage
            source={logoSource}
            style={styles.logo}
            resizeMode={FastImage.resizeMode.contain}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.closeDrawer()}>
          <Winicon src="outline/user interface/e-remove" size={24} />
        </TouchableOpacity>
      </SafeAreaView>

      <View style={{paddingTop: 16, marginHorizontal: 16}}>
        {menuItems.map((item, index) => (
          <ListTile
            key={index}
            style={{
              backgroundColor:
                selectedRoute === item.route
                  ? ColorThemes.light.neutral_main_background_color
                  : 'transparent',
            }}
            onPress={() => {
              setSelectedRoute(item.route);
              navigation.navigate(item.route);
              navigation.closeDrawer();
            }}
            leading={
              <Winicon
                src={selectedRoute === item.route ? item.activeIcon : item.icon}
                size={20}
                color={
                  selectedRoute === item.route
                    ? ColorThemes.light.primary_main_color
                    : undefined
                }
              />
            }
            title={
              <Text
                style={[
                  styles.menuText,
                  {
                    color:
                      selectedRoute === item.route
                        ? ColorThemes.light.primary_main_color
                        : ColorThemes.light.neutral_text_title_color,
                  },
                ]}>
                {item.title}
              </Text>
            }
          />
        ))}
      </View>
    </View>
  );
}

const Drawer = createDrawerNavigator();

export function DrawerMain() {
  // Use a simpler drawer configuration to avoid the makeMutable error
  return (
    <Drawer.Navigator
      initialRouteName={RootScreen.navigateKtxView}
      screenOptions={{
        headerShown: false,
        drawerType: 'slide',
        drawerStyle: {
          width: '70%',
        },
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}>
      <Drawer.Screen
        name={RootScreen.navigateKtxView}
        component={KtxStackNavigator}
      />
      <Drawer.Screen
        name={RootScreen.navigateCommunityView}
        component={CommunityNavigation}
      />
    </Drawer.Navigator>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  logo: {
    resizeMode: 'contain',
    height: 30,
    aspectRatio: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 8,
    borderRadius: 8,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
  },
  selectedItem: {
    backgroundColor: '#f0f0f0',
  },
});
