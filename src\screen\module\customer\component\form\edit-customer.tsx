import {useNavigation} from '@react-navigation/native';
import {KeyboardAvoidingView, Pressable, Text, View} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import ScreenHeader from '../../../../layout/header';
import React from 'react';
import {useForm} from 'react-hook-form';
import {
  TextFieldForm,
  FDatePickerForm,
  Fselect1Form,
  FAddressPickerForm,
} from '../../../../../project-component/component-form';
import {Winicon} from '../../../../../component/export-component';
import {ListTile} from 'wini-mobile-components';
import {TypoSkin} from 'assets/skin/typography';
import FastImage from 'react-native-fast-image';

export default function EditCustomer({
  methods,
  bankList,
}: {
  methods: any;
  bankList: any;
}) {
  return (
    <Pressable
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <KeyboardAvoidingView>
        <View
          style={{
            paddingHorizontal: 16,
            gap: 16,
            paddingVertical: 8,
            paddingBottom: 100,
          }}>
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder="Họ và tên"
            label="Họ và tên"
            control={methods.control}
            errors={methods.formState.errors}
            register={methods.register}
            name="Name"
            textFieldStyle={{padding: 16}}
          />
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder="Số điện thoại"
            label="Số điện thoại"
            disabled
            control={methods.control}
            register={methods.register}
            errors={methods.formState.errors}
            name="Mobile"
            textFieldStyle={{padding: 16}}
          />
          <FAddressPickerForm
            control={methods.control}
            errors={methods.formState.errors}
            name="Address"
            label="Địa chỉ"
            placeholder="Nhập địa chỉ của bạn"
            placeName={''}
            onChange={value => {
              methods.setValue('Long', value.geometry.location.lng);
              methods.setValue('Lat', value.geometry.location.lat);
              methods.setValue('Address', value.formatted_address);
              return value.formatted_address;
            }}
          />
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            label="Số tài khoản ngân hàng"
            placeholder="Số tài khoản ngân hàng"
            control={methods.control}
            type="number-pad"
            maxLength={13}
            prefix={
              <Winicon src="outline/business/contactless-card" size={14} />
            }
            register={methods.register}
            errors={methods.formState.errors}
            name="BankAccount"
            textFieldStyle={{padding: 16}}
          />
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            label="Tên chủ tài khoản"
            placeholder="Tên chủ tài khoản"
            control={methods.control}
            prefix={
              <Winicon src="outline/business/contactless-card" size={14} />
            }
            register={methods.register}
            errors={methods.formState.errors}
            name="BankAccountName"
            textFieldStyle={{padding: 16}}
          />
          {/* chọn banks */}
          <Fselect1Form
            required
            placeholder="Chọn ngân hàng"
            label="Ngân hàng"
            control={methods.control}
            errors={methods.formState.errors}
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            name="BankId"
            options={bankList.map((e: any) => ({
              id: e.Id,
              name: (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: '100%',
                    gap: 4,
                  }}>
                  <FastImage
                    source={{uri: e.Logo}}
                    style={{height: 24, width: 45}}
                    resizeMode="contain"
                  />
                  <Text
                    style={{
                      ...TypoSkin.label3,
                      flex: 1,
                    }}
                    numberOfLines={2}>{`${e.ShortName} - ${e.Name}`}</Text>
                </View>
              ),
            }))}
            allowSearch
            onSearch={async (value?: string) => {
              return bankList.filter(
                (e: any) =>
                  e.ShortName?.toLowerCase().includes(
                    value?.toLowerCase() ?? '',
                  ) ||
                  e.Name?.toLowerCase().includes(value?.toLowerCase() ?? ''),
              );
            }}
          />
          {/* <Fselect1Form
                    required
                    placeholder="Giới tính"
                    label="Giới tính"
                    control={methods.control}
                    errors={methods.formState.errors}
                    style={{ width: '100%', backgroundColor: '#fff', borderRadius: 8 }}
                    name="Gender"
                    options={[{ id: 1, name: 'Nam' }, { id: 0, name: 'Nữ' }]}
                /> */}
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
}
