// import {
//   Dimensions,
//   KeyboardAvoidingView,
//   Platform,
//   ScrollView,
//   Text,
//   TouchableOpacity,
//   View,
// } from 'react-native';
// import {
//   useSelectorCustomerCompanyState,
//   useSelectorCustomerState,
// } from '../../../redux/hooks/hooks';
// import {ColorThemes} from '../../../assets/skin/colors';
// import {TypoSkin} from '../../../assets/skin/typography';
// import {
//   FDialog,
//   showDialog,
//   showSnackbar,
//   Winicon,
// } from '../../../component/export-component';
// import {CustomerActions} from '../../../redux/reducers/user/reducer';
// import ScreenHeader from '../../layout/header';
// import {useNavigation} from '@react-navigation/native';
// import ListTile from '../../../component/list-tile/list-tile';
// import {SkeletonImage} from '../../../project-component/skeleton-img';
// import ConfigAPI from '../../../config/configApi';
// import {FaceID, TouchID} from '../../../features/local-authen/local-authen';
// import {
//   getDataToAsyncStorage,
//   saveDataToAsyncStorage,
// } from '../../../utils/AsyncStorage';
// import {RootScreen} from '../../../router/router';
// import {
//   CustomerItem,
//   CustomerRole,
//   CustomerType,
// } from '../../../redux/reducers/user/da';
// import {forwardRef, useEffect, useMemo, useRef, useState} from 'react';
// import ImagePicker from 'react-native-image-crop-picker';
// import {ComponentStatus} from '../../../component/component-status';
// import {BaseDA} from '../../baseDA';
// import {useDispatch} from 'react-redux';
// import AppButton from '../../../component/button';
// import {randomGID, Ultis} from '../../../utils/Utils';
// import {closePopup, FPopup, showPopup} from '../../../component/popup/popup';
// import {
//   Fselect1Form,
//   TextFieldForm,
// } from '../../../project-component/component-form';
// import WScreenFooter from '../../layout/footer';
// import {useForm} from 'react-hook-form';
// import {validatePhoneNumber} from '../../../utils/validate';
// import {CompanyProfileActions} from '../../../redux/reducers/company/reducer';
// import {RegisterPartnerView} from './RegisterPartnerView';
// import ReactNativeBiometrics from 'react-native-biometrics';
// import {dialogCheckAcc} from '../../layout/main-layout';
// import {WSwitch} from '../../../component/switch/switch';
// import {DataController} from '../../base-controller';
// import FLoading from '../../../component/Loading/FLoading';
// import {SafeAreaView} from 'react-native-safe-area-context';

// export default function Profile() {
//   const customer = useSelectorCustomerState();
//   const company = useSelectorCustomerCompanyState().data;
//   const loadingCompany = useSelectorCustomerCompanyState().onLoading;
//   const navigation = useNavigation<any>();
//   const [biometric, setBiometric] = useState(false);
//   const [biometricType, setBiometricType] = useState<any>();
//   const [avaiBiometric, setAvaiBiometric] = useState(false);
//   const dispatch = useDispatch<any>();
//   const [subtitleRole, setRole] = useState('');
//   const [showRegis, setRegis] = useState(true);
//   const popupRef = useRef<any>();
//   const dialogRef = useRef<any>();
//   const rnBiometrics = new ReactNativeBiometrics({
//     allowDeviceCredentials: true,
//   });

//   const isLoading = useMemo(() => {
//     return (customer.onLoading || loadingCompany) ?? false;
//   }, [customer.onLoading, loadingCompany]);

//   useEffect(() => {
//     getDataToAsyncStorage('spBiometrics').then(rs => {
//       if (rs == 'true') {
//         setAvaiBiometric(true);
//         getDataToAsyncStorage('biometryType').then(result => {
//           setBiometricType(result);
//         });
//         getDataToAsyncStorage('Biometrics').then(result => {
//           setBiometric(result == 'true' ? true : false);
//         });
//       } else {
//         setAvaiBiometric(false);
//         setBiometric(false);
//       }
//     });
//   }, []);

//   useEffect(() => {
//     if (customer?.role && !customer.data?.CompanyProfileId) {
//       const listRole = customer?.role?.Role.split(',').map(e => {
//         switch (e) {
//           case CustomerRole.Consultant:
//             return 'Tư vấn/khảo sát viên';
//           case CustomerRole.Coordinator:
//             return 'Điều phối viên';
//           case CustomerRole.SCBD:
//             return 'Nhân viên sửa chữa bảo dưỡng';
//           case CustomerRole.VSLD:
//             return 'Nhân viên vệ sinh lau dọn';
//           case CustomerRole.KTX:
//             return 'Lãnh đạo KTX';
//           default:
//             return '';
//         }
//       });
//       if (listRole) {
//         if (listRole?.length > 1) {
//           setRole(`${listRole[0]},+${listRole.length}`);
//         } else {
//           setRole(listRole[0]);
//         }
//       }
//     }
//   }, [customer]);

//   const pickerImg = async () => {
//     const img = await ImagePicker.openPicker({
//       multiple: false,
//       cropping: true,
//       cropperCircleOverlay: true,
//     });
//     if (img) {
//       const resImgs = await BaseDA.uploadFiles([
//         {
//           uri: img.path,
//           type: img.mime,
//           name: img.filename ?? 'new file img',
//         },
//       ]);
//       if (resImgs) {
//         await dispatch(
//           CustomerActions.edit(dispatch, {
//             ...customer.data,
//             Img: resImgs[0].Id,
//           } as CustomerItem),
//         ).then(() => {
//           showSnackbar({
//             message: 'Cập nhật ảnh đại diện thành công',
//             status: ComponentStatus.SUCCSESS,
//             bottom: 60,
//           });
//           CustomerActions.getInfor(dispatch);
//         });
//       }
//     }
//   };

//   const showDrawerCompanyForm = () => {
//     if (company) CompanyProfileActions.getInfor(dispatch, company.Id);
//     if (loadingCompany) return;
//     showPopup({
//       ref: popupRef,
//       enableDismiss: true,
//       children: <PopupCompanyProfile ref={popupRef} />,
//     });
//   };
//   const showRegisterPartnerView = () => {
//     showPopup({
//       ref: popupRef,
//       enableDismiss: true,
//       children: <RegisterPartnerView ref={popupRef} />,
//     });
//   };

//   const partnerManagement = [
//     {
//       id: randomGID(),
//       ic: 'outline/buildings/toilet',
//       title: 'Nhà vệ sinh',
//       route: RootScreen.ToiletList,
//     },
//     {
//       id: randomGID(),
//       ic: 'outline/business/algorithm',
//       title: 'Sản phẩm',
//       route: RootScreen.ProductList,
//     },
//     {
//       id: randomGID(),
//       ic: 'outline/business/decision-process',
//       title: 'Vật tư',
//       route: RootScreen.MaterialList,
//     },
//     {
//       id: randomGID(),
//       ic: 'outline/multimedia/event-ticket',
//       title: 'Các yêu cầu',
//       route: RootScreen.MyTicketList,
//     },
//     {
//       id: randomGID(),
//       ic: 'outline/shopping/list',
//       title: 'Giỏ hàng',
//       route: RootScreen.CartPage,
//     },
//   ];

//   const othersManagement = [
//     // {
//     //     id: randomGID(),
//     //     ic: "outline/shopping/list",
//     //     title: "Sản phẩm uy tín",
//     //     route: RootScreen.ProductView
//     // },
//     {
//       id: randomGID(),
//       ic: 'outline/shopping/list',
//       title: 'Cẩm nang sự cố',
//       route: RootScreen.FAQView,
//     },
//     {
//       id: randomGID(),
//       ic: 'outline/shopping/list',
//       title: 'Chính sách & điều khoản',
//       route: RootScreen.PolicyView,
//     },
//   ];

//   return (
//     <SafeAreaView
//       style={{
//         flex: 1,
//         backgroundColor: ColorThemes.light.neutral_absolute_background_color,
//       }}>
//       <View
//         style={{
//           flex: 1,
//           backgroundColor: ColorThemes.light.neutral_main_border,
//         }}>
//         <FPopup ref={popupRef} />
//         <FDialog ref={dialogRef} />
//         <FLoading visible={isLoading} />
//         <ScreenHeader
//           style={{paddingLeft: 16, paddingVertical: 8}}
//           prefix={
//             <View style={{flex: 1}}>
//               <ListTile
//                 style={{padding: 0, paddingBottom: 4, alignItems: 'center'}}
//                 isClickLeading
//                 leading={
//                   <TouchableOpacity
//                     onPress={customer?.data ? pickerImg : undefined}>
//                     <SkeletonImage
//                       source={{uri: ConfigAPI.imgUrlId + customer?.data?.Img}}
//                       style={{width: 45, height: 45, borderRadius: 100}}
//                     />
//                     <View
//                       style={{
//                         position: 'absolute',
//                         padding: 5,
//                         borderRadius: 24,
//                         backgroundColor: '#fff',
//                         right: -2,
//                         bottom: -2,
//                       }}>
//                       <Winicon
//                         src="fill/entertainment/camera"
//                         size={10}
//                         color={'#000'}
//                       />
//                     </View>
//                   </TouchableOpacity>
//                 }
//                 title={customer?.data?.Name ?? 'Người dùng'}
//                 titleStyle={[
//                   TypoSkin.heading7,
//                   {
//                     color: ColorThemes.light.neutral_text_title_color,
//                     paddingBottom: 4,
//                   },
//                 ]}
//                 subtitle={
//                   !customer.data ? null : customer?.role &&
//                     !customer.data?.CompanyProfileId ? (
//                     subtitleRole
//                   ) : (
//                     <TouchableOpacity
//                       style={{alignSelf: 'baseline'}}
//                       onPress={
//                         customer.data
//                           ? showDrawerCompanyForm
//                           : () =>
//                               dialogCheckAcc({
//                                 ref: dialogRef,
//                                 navigation: navigation,
//                               })
//                       }>
//                       <Text
//                         selectionColor={ColorThemes.light.transparent}
//                         suppressHighlighting
//                         style={{
//                           ...TypoSkin.body3,
//                           color: ColorThemes.light.primary_main_color,
//                         }}>
//                         Cập nhật hồ sơ doanh nghiệp
//                       </Text>
//                     </TouchableOpacity>
//                   )
//                 }
//               />
//             </View>
//           }
//           action={
//             <View style={{flexDirection: 'row'}}>
//               {customer?.data && (
//                 <TouchableOpacity
//                   onPress={async () => {
//                     navigation.navigate(RootScreen.SettingProfile);
//                   }}
//                   style={{
//                     padding: 16,
//                     borderRadius: 32,
//                   }}>
//                   <Winicon
//                     src="outline/user interface/settings-gear"
//                     color={ColorThemes.light.neutral_text_subtitle_color}
//                     size={20}
//                   />
//                 </TouchableOpacity>
//               )}
//             </View>
//           }
//         />
//         {!customer.data ? null : customer.data?.Type ===
//           CustomerType.partner ? (
//           <View
//             style={{
//               width: '100%',
//               padding: 8,
//               justifyContent: 'center',
//               alignItems: 'center',
//               backgroundColor: ColorThemes.light.primary_main_color,
//               gap: 4,
//             }}>
//             <ListTile
//               style={{
//                 padding: 0,
//                 paddingHorizontal: 8,
//                 alignItems: 'center',
//                 backgroundColor: ColorThemes.light.primary_main_color,
//               }}
//               leading={
//                 <Winicon
//                   src="outline/users/user-c-frame"
//                   size={20}
//                   color={ColorThemes.light.neutral_absolute_background_color}
//                 />
//               }
//               title={`Tài khoản ${customer.role?.Role?.includes(CustomerRole.KTX) || customer.data?.Id === ConfigAPI.adminKtxId ? 'KTX' : 'đối tác'}`}
//               titleStyle={[
//                 TypoSkin.heading7,
//                 {color: ColorThemes.light.neutral_absolute_background_color},
//               ]}
//               trailing={
//                 !customer.role?.Role?.includes(CustomerRole.KTX) &&
//                 customer.data?.Id !== ConfigAPI.adminKtxId ? (
//                   <Winicon
//                     onClick={showRegisterPartnerView}
//                     src="outline/user interface/d-edit"
//                     size={20}
//                     color={ColorThemes.light.neutral_absolute_background_color}
//                   />
//                 ) : null
//               }
//             />
//           </View>
//         ) : showRegis ? (
//           <View
//             style={{
//               width: '100%',
//               padding: 16,
//               justifyContent: 'center',
//               alignItems: 'center',
//               backgroundColor: ColorThemes.light.primary_main_color,
//               gap: 4,
//             }}>
//             <Text
//               style={{
//                 ...TypoSkin.heading7,
//                 color: ColorThemes.light.neutral_absolute_background_color,
//               }}>
//               Kiến tạo Xanh toả sáng tâm hồn Việt
//             </Text>
//             <Text
//               style={{
//                 ...TypoSkin.subtitle3,
//                 color: ColorThemes.light.neutral_absolute_background_color,
//                 textAlign: 'center',
//               }}>
//               Trở thành một phần của cộng đồng đang phát triển của chúng tôi và
//               tận hưởng những lợi ích độc quyền.
//             </Text>
//             <View
//               style={{
//                 flexDirection: 'row',
//                 width: '100%',
//                 gap: 8,
//                 justifyContent: 'center',
//               }}>
//               <AppButton
//                 onPress={() => setRegis(false)}
//                 backgroundColor={
//                   ColorThemes.light.neutral_main_background_color
//                 }
//                 borderColor="transparent"
//                 textColor={ColorThemes.light.neutral_text_subtitle_color}
//                 containerStyle={{
//                   borderRadius: 8,
//                   paddingHorizontal: 16,
//                   marginTop: 8,
//                   flex: 1,
//                 }}
//                 title={'Lúc khác'}
//               />
//               <AppButton
//                 onPress={() => {
//                   if (!customer.data) {
//                     dialogCheckAcc({ref: dialogRef, navigation: navigation});
//                     return;
//                   }
//                   if (!customer?.data?.Address) {
//                     showSnackbar({
//                       message:
//                         'Vui lòng cập nhật địa chỉ của bạn để trở thành đối tác',
//                       status: ComponentStatus.WARNING,
//                     });
//                     navigation.navigate(RootScreen.SettingProfile);
//                     return;
//                   }
//                   showRegisterPartnerView();
//                   setRegis(false);
//                 }}
//                 backgroundColor={
//                   ColorThemes.light.neutral_absolute_background_color
//                 }
//                 borderColor="transparent"
//                 textColor={ColorThemes.light.primary_main_color}
//                 containerStyle={{
//                   borderRadius: 8,
//                   flex: 1,
//                   paddingHorizontal: 16,
//                   marginTop: 8,
//                 }}
//                 prefixIcon={'outline/users/people-network'}
//                 prefixIconSize={16}
//                 title={'Trở thành đối tác'}
//               />
//             </View>
//           </View>
//         ) : null}
//         <ScrollView style={{flex: 1, paddingVertical: 16}}>
//           {customer?.data && (
//             <ListTile
//               title="QUẢN LÝ"
//               style={{backgroundColor: 'transparent'}}
//               titleStyle={[
//                 TypoSkin.heading8,
//                 {color: ColorThemes.light.neutral_text_placeholder_color},
//               ]}
//             />
//           )}
//           {/* Đối tác */}
//           {(customer.data?.CompanyProfileId ||
//             customer?.role?.Status === 1) && (
//             <ListTile
//               onPress={async () => {
//                 navigation.navigate(RootScreen.CompanyView);
//               }}
//               leading={
//                 <Winicon
//                   src="outline/users/meeting"
//                   color={ColorThemes.light.neutral_text_subtitle_color}
//                   size={20}
//                 />
//               }
//               title={'Phân quyền tài khoản'}
//               titleStyle={[
//                 TypoSkin.heading8,
//                 {color: ColorThemes.light.neutral_text_title_color},
//               ]}
//               trailing={
//                 <Winicon
//                   src="outline/arrows/right-arrow"
//                   color={ColorThemes.light.neutral_text_subtitle_color}
//                   size={16}
//                 />
//               }
//             />
//           )}
//           {/* NVS */}
//           {customer?.data && company?.Id !== ConfigAPI.ktxCompanyId && (
//             <ListTile
//               key={partnerManagement[0].id}
//               onPress={async () => {
//                 if (partnerManagement[0].route)
//                   navigation.navigate(partnerManagement[0].route);
//               }}
//               leading={
//                 <Winicon
//                   src={partnerManagement[0].ic}
//                   color={ColorThemes.light.neutral_text_subtitle_color}
//                   size={20}
//                 />
//               }
//               title={partnerManagement[0].title}
//               titleStyle={[
//                 TypoSkin.heading8,
//                 {color: ColorThemes.light.neutral_text_title_color},
//               ]}
//               trailing={
//                 <Winicon
//                   src="outline/arrows/right-arrow"
//                   color={ColorThemes.light.neutral_text_subtitle_color}
//                   size={16}
//                 />
//               }
//             />
//           )}
//           {/* ticket */}
//           {customer?.data &&
//             (company?.Id !== ConfigAPI.ktxCompanyId ||
//               customer.role?.Role.includes(CustomerRole.Coordinator)) && (
//               <ListTile
//                 key={partnerManagement.slice(-1)[0].id}
//                 onPress={async () => {
//                   if (partnerManagement.slice(-1)[0].route)
//                     navigation.navigate(partnerManagement.slice(-1)[0].route);
//                 }}
//                 leading={
//                   <Winicon
//                     src={partnerManagement.slice(-1)[0].ic}
//                     color={ColorThemes.light.neutral_text_subtitle_color}
//                     size={20}
//                   />
//                 }
//                 title={partnerManagement.slice(-1)[0].title}
//                 titleStyle={[
//                   TypoSkin.heading8,
//                   {color: ColorThemes.light.neutral_text_title_color},
//                 ]}
//                 trailing={
//                   <Winicon
//                     src="outline/arrows/right-arrow"
//                     color={ColorThemes.light.neutral_text_subtitle_color}
//                     size={16}
//                   />
//                 }
//               />
//             )}
//           {/* quản lý partner */}
//           {company?.Id !== ConfigAPI.ktxCompanyId ||
//             (customer.role?.Role.includes(CustomerRole.Coordinator) &&
//               partnerManagement.slice(1, -1).map((item, index) => {
//                 return (
//                   <ListTile
//                     key={item.id}
//                     onPress={async () => {
//                       if (item.route) navigation.navigate(item.route);
//                     }}
//                     leading={
//                       <Winicon
//                         src={item.ic}
//                         color={ColorThemes.light.neutral_text_subtitle_color}
//                         size={20}
//                       />
//                     }
//                     title={item.title}
//                     titleStyle={[
//                       TypoSkin.heading8,
//                       {color: ColorThemes.light.neutral_text_title_color},
//                     ]}
//                     trailing={
//                       <Winicon
//                         src="outline/arrows/right-arrow"
//                         color={ColorThemes.light.neutral_text_subtitle_color}
//                         size={16}
//                       />
//                     }
//                   />
//                 );
//               }))}

//           {/*  */}
//           {avaiBiometric && customer?.data ? (
//             <ListTile
//               leading={
//                 <View>
//                   {(() => {
//                     if (biometricType == 'TouchID') {
//                       return (
//                         <TouchID
//                           size={20}
//                           color={ColorThemes.light.neutral_text_subtitle_color}
//                         />
//                       );
//                     } else {
//                       return (
//                         <FaceID
//                           size={20}
//                           color={ColorThemes.light.neutral_text_subtitle_color}
//                         />
//                       );
//                     }
//                   })()}
//                 </View>
//               }
//               title="Sử dụng sinh trắc học"
//               titleStyle={[
//                 TypoSkin.heading8,
//                 {color: ColorThemes.light.neutral_text_title_color},
//               ]}
//               trailing={
//                 <WSwitch
//                   color={ColorThemes.light.primary_main_color}
//                   value={biometric}
//                   onChange={vl => {
//                     rnBiometrics
//                       .simplePrompt({promptMessage: 'Xác nhận sinh trắc học'})
//                       .then(resultObject => {
//                         const {success} = resultObject;
//                         if (success) {
//                           if (vl) {
//                             setTimeout(() => {
//                               saveDataToAsyncStorage('Biometrics', 'true');
//                               setBiometric(true);
//                             }, 100);
//                             return;
//                           } else {
//                             setTimeout(() => {
//                               saveDataToAsyncStorage('Biometrics', 'false');
//                               setBiometric(false);
//                             }, 100);
//                           }
//                         } else {
//                           setTimeout(() => {
//                             saveDataToAsyncStorage(
//                               'Biometrics',
//                               biometric ? 'true' : 'false',
//                             );
//                             setBiometric(biometric);
//                           }, 100);
//                           console.log(
//                             'user cancelled biometric prompt',
//                             biometric,
//                           );
//                         }
//                       })
//                       .catch(() => {
//                         setTimeout(() => {
//                           saveDataToAsyncStorage('Biometrics', 'false');
//                           setBiometric(false);
//                         }, 100);
//                         console.log('biometrics failed', biometric);
//                       });
//                   }}
//                 />
//               }
//             />
//           ) : null}
//           {/* Khác */}
//           <ListTile
//             title="KHÁC"
//             style={{marginTop: 8, backgroundColor: 'transparent'}}
//             titleStyle={[
//               TypoSkin.heading8,
//               {color: ColorThemes.light.neutral_text_placeholder_color},
//             ]}
//           />
//           {othersManagement.map((item, index) => {
//             return (
//               <ListTile
//                 key={item.id}
//                 onPress={async () => {
//                   if (item.route) navigation.navigate(item.route);
//                 }}
//                 leading={
//                   <Winicon
//                     src={item.ic}
//                     color={ColorThemes.light.neutral_text_subtitle_color}
//                     size={20}
//                   />
//                 }
//                 title={item.title}
//                 titleStyle={[
//                   TypoSkin.heading8,
//                   {color: ColorThemes.light.neutral_text_title_color},
//                 ]}
//                 trailing={
//                   <Winicon
//                     src="outline/arrows/right-arrow"
//                     color={ColorThemes.light.neutral_text_subtitle_color}
//                     size={16}
//                   />
//                 }
//               />
//             );
//           })}
//           <ListTile
//             onPress={async () => {
//               CustomerActions.logout(dispatch, navigation);
//             }}
//             leading={
//               <Winicon
//                 src="outline/arrows/logout"
//                 color={ColorThemes.light.error_main_color}
//                 size={20}
//               />
//             }
//             title={customer.data ? 'Đăng xuất' : 'Về đăng nhập'}
//             titleStyle={[
//               TypoSkin.heading8,
//               {color: ColorThemes.light.error_main_color},
//             ]}
//           />
//           <View style={{height: 65}} />
//         </ScrollView>
//       </View>
//     </SafeAreaView>
//   );
// }

// const PopupCompanyProfile = forwardRef(function PopupCompanyProfile(
//   data: {},
//   ref: any,
// ) {
//   const {} = data;
//   const dialogRef = useRef<any>();
//   const companyProfile = useSelectorCustomerCompanyState().data;
//   const methods = useForm<any>({
//     shouldFocusError: false,
//     defaultValues: companyProfile ? companyProfile : {Id: randomGID()},
//   });
//   const user = useSelectorCustomerState().data;
//   const newGID = randomGID();
//   const dispatch = useDispatch<any>();

//   const [bankList, setBankList] = useState<Array<any>>([]);

//   useEffect(() => {
//     const bankController = new DataController('Bank');
//     bankController.getAll().then(res => {
//       if (res.code === 200) setBankList(res.data);
//     });
//   }, []);

//   const _onSubmit = (ev: any) => {
//     showDialog({
//       ref: dialogRef,
//       status: ComponentStatus.INFOR,
//       title:
//         'Bạn chắc chắn muốn lưu hồ sơ doanh nghiệp theo các thông tin đã nhập?',
//       onSubmit: async () => {
//         const controller = new DataController('CompanyProfile');
//         // Đảm bảo sử dụng cùng một ID cho CompanyProfile
//         const companyData = {
//           ...ev,
//           Id: ev.Id || newGID,
//           DateCreated: Date.now(),
//         };
//         const res = await controller.add([companyData]);

//         if (res.code === 200) {
//           if (companyProfile) {
//             CompanyProfileActions.getInfor(dispatch, companyProfile.Id);
//           } else {
//             // Sử dụng cùng ID đã tạo CompanyProfile
//             const companyProfileId = companyData.Id;

//             dispatch(
//               CustomerActions.edit(dispatch, {
//                 ...user,
//                 CompanyProfileId: companyProfileId,
//               } as CustomerItem),
//             );
//             CustomerActions.editRole(dispatch, {
//               Id: randomGID(),
//               Name: user?.Name ?? '',
//               DateCreated: Date.now(),
//               Status: 1,
//               Role: CustomerRole.Owner,
//               Sort: 1,
//               Description: 'Chủ doanh nghiệp',
//               CustomerId: user?.Id ?? '',
//               CompanyProfileId: companyProfileId,
//             });
//           }
//           showSnackbar({
//             message: 'Đã lưu hồ sơ doanh nghiệp',
//             status: ComponentStatus.SUCCSESS,
//           });
//           setTimeout(() => {
//             closePopup(ref);
//           }, 300);
//         } else {
//           showSnackbar({message: res.message, status: ComponentStatus.ERROR});
//         }
//       },
//     });
//   };

//   const _onError = (ev: any) => {
//     console.log(ev);
//   };

//   return (
//     <SafeAreaView
//       style={{
//         width: '100%',
//         height: Dimensions.get('window').height - 40,
//         borderTopLeftRadius: 12,
//         borderTopRightRadius: 12,
//         backgroundColor: ColorThemes.light.neutral_absolute_background_color,
//       }}>
//       <FDialog ref={dialogRef} />
//       <ScreenHeader
//         style={{
//           backgroundColor: ColorThemes.light.transparent,
//           flexDirection: 'row',
//           paddingVertical: 4,
//         }}
//         title={`Hồ sơ doanh nghiệp`}
//         prefix={<View />}
//         action={
//           <TouchableOpacity
//             onPress={() => closePopup(ref)}
//             style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
//             <Winicon
//               src="outline/layout/xmark"
//               size={20}
//               color={ColorThemes.light.neutral_text_body_color}
//             />
//           </TouchableOpacity>
//         }
//       />
//       <KeyboardAvoidingView
//         behavior={'height'}
//         keyboardVerticalOffset={Platform.OS === 'ios' ? 56 : 0}
//         style={{height: '100%', width: '100%', paddingHorizontal: 16, flex: 1}}>
//         <ScrollView
//           style={{backgroundColor: ColorThemes.light.transparent, flex: 1}}>
//           <TextFieldForm
//             label="Họ và tên"
//             required
//             textFieldStyle={{padding: 16}}
//             style={{width: '100%', marginBottom: 20}}
//             register={methods.register}
//             control={methods.control}
//             errors={methods.formState.errors}
//             name="Name"
//           />
//           <TextFieldForm
//             label="Ngành nghề"
//             textFieldStyle={{padding: 16}}
//             style={{width: '100%', marginBottom: 20}}
//             register={methods.register}
//             control={methods.control}
//             errors={methods.formState.errors}
//             name="Industry"
//           />
//           <TextFieldForm
//             control={methods.control}
//             name="Mobile"
//             label="Số điện thoại doanh nghiệp"
//             required
//             returnKeyType="done"
//             style={{flex: 1, marginBottom: 20}}
//             errors={methods.formState.errors}
//             textFieldStyle={{
//               height: 48,
//               backgroundColor: ColorThemes.light.transparent,
//               padding: 16,
//             }}
//             register={methods.register}
//             type="number-pad"
//             onBlur={async (ev: string) => {
//               if (ev === undefined || ev.length == 0) {
//                 methods.setError('Mobile', {
//                   message: 'Số điện thoại không hợp lệ',
//                 });
//                 return;
//               }
//               var mobile = ev.trim();
//               // Check if the number doesn't already start with 0 or +84
//               if (!/^(\+84|0)/.test(mobile)) {
//                 mobile = '0' + mobile; // Add 0 at the beginning
//               }
//               const val = validatePhoneNumber(mobile);
//               if (val) methods.clearErrors('Mobile');
//               else
//                 methods.setError('Mobile', {
//                   message: 'Số điện thoại không hợp lệ',
//                 });
//             }}
//           />
//           <TextFieldForm
//             label="Email"
//             type="email-address"
//             textFieldStyle={{padding: 16}}
//             style={{width: '100%', marginBottom: 20}}
//             register={methods.register}
//             control={methods.control}
//             errors={methods.formState.errors}
//             name="Email"
//           />
//           <TextFieldForm
//             label="Địa chỉ"
//             required
//             textFieldStyle={{padding: 16}}
//             style={{width: '100%', marginBottom: 20}}
//             register={methods.register}
//             control={methods.control}
//             errors={methods.formState.errors}
//             name="Address"
//           />
//           <TextFieldForm
//             control={methods.control}
//             name="TaxCode"
//             label="Mã số thuế"
//             required
//             returnKeyType="done"
//             style={{flex: 1, marginBottom: 20}}
//             errors={methods.formState.errors}
//             textFieldStyle={{
//               height: 48,
//               backgroundColor: ColorThemes.light.transparent,
//               padding: 16,
//             }}
//             register={methods.register}
//             type="name-phone-pad"
//             onBlur={async (ev: string) => {
//               if (ev === undefined || ev.length == 0) {
//                 methods.setError('TaxCode', {
//                   message: 'Mã số thuế không hợp lệ',
//                 });
//                 return;
//               }
//               const val = ev.trim().length == 10 || ev.trim().length == 13;
//               if (val) methods.clearErrors('TaxCode');
//               else
//                 methods.setError('TaxCode', {
//                   message: 'Mã số thuế không hợp lệ',
//                 });
//             }}
//           />
//           <TextFieldForm
//             required
//             style={{
//               width: '100%',
//               backgroundColor: '#fff',
//               borderRadius: 8,
//               marginBottom: 20,
//             }}
//             label="Số tài khoản ngân hàng"
//             placeholder="Số tài khoản ngân hàng"
//             control={methods.control}
//             type="number-pad"
//             maxLength={13}
//             register={methods.register}
//             errors={methods.formState.errors}
//             name="BankAccount"
//             textFieldStyle={{padding: 16}}
//           />
//           <TextFieldForm
//             required
//             style={{
//               width: '100%',
//               backgroundColor: '#fff',
//               borderRadius: 8,
//               marginBottom: 20,
//             }}
//             label="Tên chủ tài khoản"
//             placeholder="Tên chủ tài khoản"
//             control={methods.control}
//             register={methods.register}
//             errors={methods.formState.errors}
//             name="BankAccountName"
//             textFieldStyle={{padding: 16}}
//           />
//           <Fselect1Form
//             required
//             placeholder="Chọn ngân hàng"
//             label="Ngân hàng"
//             control={methods.control}
//             errors={methods.formState.errors}
//             style={{
//               width: '100%',
//               backgroundColor: '#fff',
//               borderRadius: 8,
//               marginBottom: 20,
//             }}
//             name="BankId"
//             options={bankList.map((e: any) => ({id: e.Id, name: e.Name}))}
//           />
//           <TextFieldForm
//             label="Người đại diện"
//             required
//             textFieldStyle={{padding: 16}}
//             style={{width: '100%', marginBottom: 20}}
//             register={methods.register}
//             control={methods.control}
//             errors={methods.formState.errors}
//             name="Representative"
//           />
//           <TextFieldForm
//             control={methods.control}
//             name="Phone"
//             label="SĐT người đại diện"
//             returnKeyType="done"
//             style={{flex: 1, marginBottom: 20}}
//             errors={methods.formState.errors}
//             textFieldStyle={{
//               height: 48,
//               backgroundColor: ColorThemes.light.transparent,
//               padding: 16,
//             }}
//             register={methods.register}
//             type="number-pad"
//             onBlur={async (ev: string) => {
//               if (ev === undefined || ev.length == 0) {
//                 methods.setError('Phone', {
//                   message: 'Số điện thoại không hợp lệ',
//                 });
//                 return;
//               }
//               var mobile = ev.trim();
//               // Check if the number doesn't already start with 0 or +84
//               if (!/^(\+84|0)/.test(mobile)) {
//                 mobile = '0' + mobile; // Add 0 at the beginning
//               }
//               const val = validatePhoneNumber(mobile);
//               if (val) methods.clearErrors('Phone');
//               else
//                 methods.setError('Phone', {
//                   message: 'Số điện thoại không hợp lệ',
//                 });
//             }}
//           />
//           <TextFieldForm
//             label="Chức vụ"
//             textFieldStyle={{padding: 16}}
//             style={{width: '100%', marginBottom: 20}}
//             register={methods.register}
//             control={methods.control}
//             errors={methods.formState.errors}
//             name="Position"
//           />
//           <TextFieldForm
//             control={methods.control}
//             name="Note"
//             label="Chú thích"
//             errors={methods.formState.errors}
//             placeholder={'Chú thích'}
//             style={{backgroundColor: ColorThemes.light.transparent}}
//             textFieldStyle={{
//               height: 100,
//               paddingHorizontal: 16,
//               paddingTop: 16,
//               paddingBottom: 16,
//               marginBottom: 100,
//               justifyContent: 'flex-start',
//               backgroundColor: ColorThemes.light.transparent,
//             }}
//             textStyle={{textAlignVertical: 'top'}}
//             numberOfLines={10}
//             multiline={true}
//             register={methods.register}
//           />
//         </ScrollView>
//       </KeyboardAvoidingView>
//       <WScreenFooter
//         style={{
//           flexDirection: 'row',
//           gap: 8,
//           paddingHorizontal: 16,
//           paddingBottom: 16,
//         }}>
//         <AppButton
//           title={'Lưu'}
//           backgroundColor={ColorThemes.light.primary_main_color}
//           borderColor="transparent"
//           containerStyle={{
//             height: 40,
//             flex: 1,
//             borderRadius: 8,
//             paddingHorizontal: 12,
//             paddingVertical: 5,
//           }}
//           onPress={() => {
//             methods.handleSubmit(_onSubmit, _onError)();
//           }}
//           textColor={ColorThemes.light.neutral_absolute_background_color}
//         />
//       </WScreenFooter>
//     </SafeAreaView>
//   );
// });
