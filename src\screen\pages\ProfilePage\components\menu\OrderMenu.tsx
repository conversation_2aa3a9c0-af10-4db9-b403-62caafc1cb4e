import React, {useEffect} from 'react';
import {View, StyleSheet, ViewStyle} from 'react-native';
import MenuOrders from './MenuOrders';
import iconSvg from '../../../../../svgs/iconSvg';
import {StatusOrder, Title} from '../../../../../config/Contanst';
import {OrderProduct} from '../../../../../types/orderProduct';
import {RootScreen} from '../../../../../router/router';
import {useSelector} from 'react-redux';
import {
  selectOrderInfo,
  useOrderActions,
} from '../../../../../redux/reducers/order/OrderReducer';
import {store} from '../../../../../redux/store/store';

interface OrderMenuProps {
  orderDetail: OrderProduct[];
  customer: any;
  type?: string;
  style?: ViewStyle;
}

const OrderMenu: React.FC<OrderMenuProps> = ({
  orderDetail,
  customer,
  type,
  style,
}) => {
  if (!orderDetail) return null;
  if (!customer) return null;

  const orderInfo = useSelector(selectOrderInfo);
  const shopInfo = store.getState().partner.data;
  const shopId = shopInfo && shopInfo.length > 0 ? shopInfo[0].Id : null;
  const orderActions = useOrderActions();

  useEffect(() => {
    if (shopId) {
      orderActions.fetchAllOrdersByShopId(shopId);
    }
  }, [shopId]);

  return (
    <View style={[styles.container, style]}>
      {/* Uncomment when needed */}
      <MenuOrders
        svgIcon={iconSvg.walletAction}
        title="Đơn hàng mới"
        getBadgeOrder={
          type && type === 'shop'
            ? orderInfo && orderInfo.NewOrder?.number
            : orderDetail?.filter((item: any) => item.Status == StatusOrder.new)
                .length
        }
        Type={Title.New}
        orderRoute={
          type && type === 'shop'
            ? RootScreen.OrderShopDetail
            : RootScreen.OrderCustomerDetail
        }
        status={StatusOrder.new}
      />
      <MenuOrders
        svgIcon={iconSvg.deliveryIcon}
        title="Đang xử lý"
        getBadgeOrder={
          type && type === 'shop'
            ? orderInfo && orderInfo.ProcessOrder?.number
            : orderDetail?.filter(
                (item: any) => item.Status == StatusOrder.proccess,
              ).length
        }
        Type={Title.Processing}
        orderRoute={
          type && type === 'shop'
            ? RootScreen.OrderShopDetail
            : RootScreen.OrderCustomerDetail
        }
        status={StatusOrder.proccess}
      />
      <MenuOrders
        svgIcon={iconSvg.done}
        title="Hoàn thành"
        Type={Title.Done}
        orderRoute={
          type && type === 'shop'
            ? RootScreen.OrderShopDetail
            : RootScreen.OrderCustomerDetail
        }
        status={StatusOrder.success}
      />
      <MenuOrders
        svgIcon={iconSvg.cancel}
        title="Hủy/hoàn"
        getBadgeOrder={
          type && type === 'shop'
            ? orderInfo && orderInfo.CancelOrder?.number
            : orderDetail?.filter(
                (item: any) => item.Status == StatusOrder.cancel,
              ).length
        }
        Type={Title.Cancel}
        orderRoute={
          type && type === 'shop'
            ? RootScreen.OrderShopDetail
            : RootScreen.OrderCustomerDetail
        }
        status={StatusOrder.cancel}
      />
      <MenuOrders
        svgIcon={iconSvg.star}
        title="Đánh giá"
        orderRoute={RootScreen.Review}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 8,
    marginBottom: 8,
  },
});

export default OrderMenu;
