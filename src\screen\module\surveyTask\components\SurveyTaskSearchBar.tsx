import React from 'react';
import {View, StyleSheet} from 'react-native';
import {FTextField, Winicon} from '../../../../component/export-component';
import {ColorThemes} from '../../../../assets/skin/colors';

interface SurveyTaskSearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSubmit?: () => void;
  placeholder?: string;
  style?: any;
}

const SurveyTaskSearchBar: React.FC<SurveyTaskSearchBarProps> = ({
  value,
  onChangeText,
  onSubmit,
  placeholder = 'Tìm kiếm công việc khảo sát...',
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <FTextField
        style={styles.textField}
        value={value}
        onChange={onChangeText}
        onSubmit={onSubmit}
        placeholder={placeholder}
        prefix={
          <Winicon
            src="outline/development/zoom"
            size={16}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        }
        returnKeyType="search"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  textField: {
    paddingHorizontal: 16,
    height: 40,
    backgroundColor: ColorThemes.light.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_bolder_border_color,
  },
});

export default SurveyTaskSearchBar;
