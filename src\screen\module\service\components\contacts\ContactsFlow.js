import { useNavigation, useRoute } from "@react-navigation/native";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { SafeAreaView, Text, TouchableOpacity, View } from "react-native";
import { signInWithPhoneFB } from "../../../../../features/otp-loginwFirebase/PhoneSignIn";
import { DataController } from "../../../../base-controller";
import { useSelectorCustomerState } from "../../../../../redux/hooks/hooks";
import { CustomerStatus, CustomerType } from "../../../../../redux/reducers/user/da";
import { CateServicesType, randomToiletServiceName, ToiletServiceStatus, ToiletType } from "../da";
import { randomGID } from "../../../../../utils/Utils";
import FLoading from "../../../../../component/Loading/FLoading";
import ScreenHeader from "../../../../layout/header";
import { ColorThemes } from "../../../../../assets/skin/colors";
import InputContactStep from "../form/InputContactStep";
import CheckOptStep from "../form/CheckOptStep";
import { showSnackbar, Winicon } from "../../../../../component/export-component";
import { ComponentStatus } from "../../../../../component/component-status";
import WScreenFooter from "../../../../layout/footer";
import AppButton from "../../../../../component/button";
import { TypoSkin } from "../../../../../assets/skin/typography";
import { validatePhoneNumber } from "../../../../../utils/validate";
import ConfigAPI from "../../../../../config/configApi";
import { getDataToAsyncStorage } from "../../../../../utils/AsyncStorage";

export default function ContactFlow() {
    const navigation = useNavigation()
    const route = useRoute()
    const [step, setStep] = useState(0)
    const methods = useForm({ shouldFocusError: false })
    const user = useSelectorCustomerState().data

    const [loading, setLoading] = useState(false)
    const [done, setDone] = useState(false)

    useEffect(() => {
        if (route?.params) {
            setStep(route?.params?.step);
            methods.setValue("description", route.params.description)
        }
    }, [route.params])

    const onSubmitInformation = async () => {
        setLoading(true)
        const customerController = new DataController("Customer")
        let _mobile = methods.watch("Mobile").replace("+84", "0")
        if (_mobile.startsWith("84")) _mobile = _mobile.replace("84", "0")

        if (!user) {
            var findCustomerByPhone = await customerController.getListSimple({ page: 1, size: 1, query: `@Mobile:(${_mobile})` })
            if (findCustomerByPhone.code === 200) findCustomerByPhone = findCustomerByPhone.data[0]
        }
        const newCustomer = user ?? findCustomerByPhone ?? {
            Id: randomGID(),
            Name: methods.watch("Name"),
            DateCreated: Date.now(),
            Mobile: _mobile,
            Address: methods.watch("Address"),
            Lat: methods.watch("Lat"),
            Long: methods.watch("Long"),
            Status: CustomerStatus.active,
            Type: CustomerType.guest,
        }
        _toiletId = await getDataToAsyncStorage("contact-toiletid")
        const randomName = await randomToiletServiceName("TV")
        let toiletServices = {
            Id: randomGID(),
            Name: randomName,
            DateCreated: Date.now(),
            Description: methods.watch("description"),
            Sort: 1,
            Status: ToiletServiceStatus.register,
            CateServicesId: CateServicesType.contact,
            CustomerMobile: _mobile,
            CustomerId: ConfigAPI.adminKtxId,
            ToiletId: _toiletId ?? undefined
        }
        methods.setValue("toiletServiceId", toiletServices.Id)
        const toiletServicesController = new DataController("ToiletServices")
        if (newCustomer.Id !== user?.Id && !findCustomerByPhone) {
            const customerRes = await customerController.add([newCustomer])
            if (customerRes.code !== 200) {
                setLoading(false)
                setDone(false)
                return showSnackbar({ message: customerRes.message, type: ComponentStatus.ERROR })
            }
        }
        const toiletServicesRes = await toiletServicesController.add([toiletServices])
        if (toiletServicesRes.code !== 200) {
            setLoading(false)
            setDone(false)
            return showSnackbar({ message: toiletServicesRes.message, type: ComponentStatus.ERROR })
        }
        setLoading(false)
        setDone(true)
    }

    const onSendOtp = async () => {
        if (__DEV__) {
            if (step === 0) setStep(step + 1)
            return
        }
        if (step === 0) setStep(step + 1)
        // var rs = await signInWithPhoneFB(methods.watch("Mobile"))
        // if (rs) {
        // done
        //done
        // showSnackbar({
        //     message: 'Đã gửi mã xác thực đến số diện thoại',
        //     status: ComponentStatus.SUCCSESS,
        // });
        // } else {
        // fail
        // showSnackbar({ message: "Đã có lỗi xảy ra", status: ComponentStatus.ERROR })
        // }
        setLoading(false)
    }

    useEffect(() => {
        if (user) {
            methods.setValue("Lat", user.Lat)
            methods.setValue("Long", user.Long)
            methods.setValue("Address", user.Address)
        }
    }, [user])

    const editable = useMemo(() => {
        return (methods.watch("Name")?.length > 0 && validatePhoneNumber(methods.watch("Mobile")) && methods.watch("Address")?.length > 0)
    }, [user, methods.watch("Name"), methods.watch("Mobile"), methods.watch("Address")])

    return <SafeAreaView style={{ flex: 1, backgroundColor: ColorThemes.light.neutral_absolute_background_color }}>
        <FLoading visible={loading} avt="" />
        <ScreenHeader
            style={{
                shadowColor: "rgba(0, 0, 0, 0.03)",
                shadowOffset: {
                    width: 0,
                    height: 4
                },
                shadowRadius: 20,
                elevation: 20,
                shadowOpacity: 1,
            }}
            children={
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <TouchableOpacity
                            style={{ padding: 12, gap: 8, flexDirection: 'row', alignItems: 'center' }}
                            onPress={() => {
                                navigation.pop();
                            }}>
                            <Winicon
                                src="outline/arrows/left-arrow"
                                color={ColorThemes.light.neutral_text_subtitle_color}
                                size={20}
                            />
                            <Text
                                style={[
                                    TypoSkin.heading8,
                                    { color: ColorThemes.light.neutral_text_title_color },
                                ]}>
                                Thoát
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            }
        />
        <View style={{ flex: 1 }}>
            {step === 0 ? <InputContactStep methods={methods} display={step === 0 ? 'flex' : 'none'} editable={user && user.Lat ? false : true} /> : <View />}
            {step === 1 ? <CheckOptStep methods={methods} setLoading={setLoading} display={step === 1 ? 'flex' : 'none'} onSendOtp={onSendOtp} onSubmitResult={onSubmitInformation} /> : <View />}
        </View>
        {done ? null : step < 2 ? <WScreenFooter style={{ justifyContent: "space-between", paddingVertical: 0 }}>
            <View style={{ flexDirection: "row", height: 6, width: "100%", gap: 4 }}>
                {Array.from({ length: 2 }).map((_, i) => <View key={`step${i}`} style={{ flex: 1, height: "100%", width: "100%", backgroundColor: i === step ? ColorThemes.light.neutral_absolute_reverse_background_color : ColorThemes.light.neutral_main_background_color }} />)}
            </View>
            <View style={{
                flexDirection: 'row', justifyContent: "space-between", paddingHorizontal: 16, paddingTop: 12
            }}>
                {step !== 0 ? <AppButton
                    title={'Quay lại'}
                    backgroundColor={ColorThemes.light.neutral_main_background_color}
                    borderColor="transparent"
                    containerStyle={{ height: 40, borderRadius: 8, alignSelf: "baseline", paddingHorizontal: 12, paddingVertical: 5 }}
                    onPress={() => {
                        if (step) setStep(step - 1)
                    }}
                    textColor={ColorThemes.light.neutral_text_subtitle_color}
                /> : <View />}
                {step === 0 ?
                    <AppButton
                        title={step === 0 ? 'Gửi thông tin' : 'Tiếp'}
                        backgroundColor={ColorThemes.light.primary_main_color}
                        borderColor="transparent"
                        containerStyle={{ height: 40, borderRadius: 8, alignSelf: "baseline", paddingHorizontal: 12, paddingVertical: 5 }}
                        disabled={!editable}
                        onPress={() => {
                            onSendOtp()
                        }}
                        textColor={ColorThemes.light.neutral_absolute_background_color}
                    /> : <View />}
            </View>
        </WScreenFooter > : null}
    </SafeAreaView>
}