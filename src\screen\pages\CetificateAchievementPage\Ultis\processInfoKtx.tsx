import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {AppSvg, Winicon} from 'wini-mobile-components';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import iconSvg from '../../../../svgs/iconSvg';
import {ColorThemes} from '../../../../assets/skin/colors';
import {RootScreen} from '../../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {CateServicesType} from '../../../module/service/components/da';

export const processInfoKtx = (data: any) => {
  let checkS = data.find((item: any) => item.CateCriterion?.Name == 'Sạch');
  let checkX = data.find((item: any) => item.CateCriterion?.Name == 'Xanh');
  let checkTH = data.find(
    (item: any) => item.CateCriterion?.Name == 'Tuần hoàn',
  );

  if (data?.length == 0) {
    return (
      <View style={styles.timelineContainer}>
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <Text style={styles.timelineDate}></Text>
          <Text style={styles.timelineLabel}>Sạch</Text>
        </View>
        <View style={styles.timelineLineOff} />
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <Text style={styles.timelineDate}></Text>
          <Text style={styles.timelineLabel}>Xanh</Text>
        </View>
        <View style={styles.timelineLineOff} />
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <Text style={styles.timelineDate}></Text>
          <Text style={styles.timelineLabel}>Tuần hoàn</Text>
        </View>
      </View>
    );
  } else if (data?.length == 1 && checkS?.Id) {
    return (
      <View style={styles.timelineContainer}>
        <View style={styles.timelineItem}>
          <View style={[styles.timelineDot, styles.timelineDotActive]}>
            <Winicon
              src="outline/user interface/check"
              size={15}
              color="white"
            />
          </View>
          <Text style={styles.timelineDate}>
            {Ultis.datetoString(new Date(data[0]?.DateCreated), 'dd/mm/yyyy')}
          </Text>
          <Text style={styles.timelineLabel}>Sạch</Text>
        </View>
        <View style={styles.timelineLineOff} />
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <Text style={styles.timelineDate}></Text>
          <Text style={styles.timelineLabel}>Xanh</Text>
        </View>
        <View style={styles.timelineLineOff} />
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <Text style={styles.timelineDate}></Text>
          <Text style={styles.timelineLabel}>Tuần hoàn</Text>
        </View>
      </View>
    );
  } else if (data?.length == 2 && checkX?.Id) {
    return (
      <View style={styles.timelineContainer}>
        <View style={styles.timelineItem}>
          <View style={[styles.timelineDot, styles.timelineDotActive]}>
            <Winicon
              src="outline/user interface/check"
              size={15}
              color="white"
            />
          </View>
          <Text style={styles.timelineDate}>
            {Ultis.datetoString(new Date(data[0]?.DateCreated), 'dd/mm/yyyy')}
          </Text>
          <Text style={styles.timelineLabel}>Sạch</Text>
        </View>
        <View style={styles.timelineLine} />
        <View style={styles.timelineItem}>
          <View style={[styles.timelineDot, styles.timelineDotActive]}>
            <Winicon
              src="outline/user interface/check"
              size={15}
              color="white"
            />
          </View>
          <Text style={styles.timelineDate}>
            {Ultis.datetoString(new Date(data[1]?.DateCreated), 'dd/mm/yyyy')}
          </Text>
          <Text style={styles.timelineLabel}>Xanh</Text>
        </View>
        <View style={styles.timelineLineOff} />
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <Text style={styles.timelineDate}></Text>
          <Text style={styles.timelineLabel}>Tuần hoàn</Text>
        </View>
      </View>
    );
  } else if (data?.length == 3 && checkTH?.Id) {
    return (
      <View style={styles.timelineContainer}>
        <View style={styles.timelineItem}>
          <View style={[styles.timelineDot, styles.timelineDotActive]}>
            <Winicon
              src="outline/user interface/check"
              size={15}
              color="white"
            />
          </View>
          <Text style={styles.timelineDate}>
            {Ultis.datetoString(new Date(data[0]?.DateCreated), 'dd/mm/yyyy')}
          </Text>
          <Text style={styles.timelineLabel}>Sạch</Text>
        </View>
        <View style={styles.timelineLine} />
        <View style={styles.timelineItem}>
          <View style={[styles.timelineDot, styles.timelineDotActive]}>
            <Winicon
              src="outline/user interface/check"
              size={15}
              color="white"
            />
          </View>
          <Text style={styles.timelineDate}>
            {Ultis.datetoString(new Date(data[1]?.DateCreated), 'dd/mm/yyyy')}
          </Text>
          <Text style={styles.timelineLabel}>Sạch</Text>
        </View>
        <View style={styles.timelineLine} />
        <View style={styles.timelineItem}>
          <View style={[styles.timelineDot, styles.timelineDotActive]}>
            <Winicon
              src="outline/user interface/check"
              size={15}
              color="white"
            />
          </View>
          <Text style={styles.timelineDate}>
            {Ultis.datetoString(new Date(data[2]?.DateCreated), 'dd/mm/yyyy')}
          </Text>
          <Text style={styles.timelineLabel}>Tuần hoàn</Text>
        </View>
      </View>
    );
  }
};

export const ImageCetificateAchievemenDa = (data: any) => {
  let checkS = data.find((item: any) => item.CateCriterion?.Name == 'Sạch');
  let checkX = data.find((item: any) => item.CateCriterion?.Name == 'Xanh');
  let checkTH = data.find(
    (item: any) => item.CateCriterion?.Name == 'Tuần hoàn',
  );
  if (data?.length == 0) {
    return (
      <View style={styles.imageWrapper}>
        <Image
          source={require('../../../../assets/logo.png')}
          style={styles.appStoreImage}
          resizeMode="contain"
        />
      </View>
    );
  } else if (data.length == 1 && checkS?.Id) {
    return (
      <View>
        <Image
          source={{
            uri: data[0]?.Img,
          }}
          style={{
            height: 200,
            width: '100%',
          }}
        />
      </View>
    );
  } else if (data.length == 2 && checkX?.Id) {
    return (
      <View>
        <Image
          source={{
            uri: data[1]?.Img,
          }}
          style={{
            height: 200,
            width: '100%',
          }}
        />
      </View>
    );
  } else if (data.length == 3 && checkTH?.Id) {
    return (
      <View>
        <Image
          source={{
            uri: data[2]?.Img,
          }}
          style={{
            height: 200,
            width: '100%',
          }}
        />
      </View>
    );
  }
};

export const RenderButton = (data: any, downloadImg: any) => {
  const navigation = useNavigation<any>();

  let check = data.find((item: any) => item.CateCriterion?.Name == 'Tuần hoàn');

  if (check && check.Id) {
    return (
      <View style={styles.actionButtons}>
        <TouchableOpacity onPress={downloadImg} style={styles.downloadButton}>
          <AppSvg SvgSrc={iconSvg.dowload} size={12} />
          <Text style={styles.buttonTextOne}>Tải xuống</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.shareButton}>
          <AppSvg SvgSrc={iconSvg.share} size={12} />
          <Text style={styles.buttonTextOne}>Chia sẻ</Text>
        </TouchableOpacity>
      </View>
    );
  } else {
    return (
      <View style={styles.actionButtons}>
        <TouchableOpacity onPress={downloadImg} style={styles.downloadButton}>
          <AppSvg SvgSrc={iconSvg.dowload} size={12} />
          <Text style={styles.buttonText}>Tải xuống</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.shareButton}>
          <AppSvg SvgSrc={iconSvg.share} size={12} />
          <Text style={styles.buttonText}>Chia sẻ</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.registerButton}
          onPress={() =>
            navigation.navigate(RootScreen.ServicesWorkFlow, {
              type: 'netzero',
              serviceId: CateServicesType.netzero,
            })
          }>
          <AppSvg SvgSrc={iconSvg.plus} size={20} />
          <Text style={styles.registerButtonText}>Đăng ký thêm dịch vụ</Text>
        </TouchableOpacity>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  timelineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  timelineItem: {
    alignItems: 'center',
    flex: 1,
  },
  timelineDate: {
    fontSize: 10,
    color: '#666',
    marginBottom: 4,
  },
  timelineLabel: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  timelineDot: {
    width: 25,
    height: 25,
    borderRadius: 30,
    backgroundColor: '#e0e0e0',
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineDotActive: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  timelineLine: {
    height: 2,
    backgroundColor: ColorThemes.light.primary_main_color,
    flex: 1,
    marginBottom: 40,
    marginHorizontal: -42,
  },
  timelineLineOff: {
    height: 2,
    backgroundColor: '#e0e0e0',
    flex: 1,
    marginBottom: 40,
    marginHorizontal: -42,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    marginTop: 10,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  registerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 8,
    justifyContent: 'center',
  },
  buttonText: {
    ...TypoSkin.buttonText6,
    color: '#666',
    marginLeft: 8,
  },
  buttonTextOne: {
    ...TypoSkin.semibold3,
    color: '#666',
    marginLeft: 8,
  },
  registerButtonText: {
    ...TypoSkin.buttonText6,
    color: '#fff',
    marginLeft: 8,
  },
  imageWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ColorThemes.light.neutral_bolder_background_color,
    borderRadius: 16,
    padding: 20,
  },
  appStoreImage: {
    height: 160,
    width: '85%',
    maxWidth: 320,
  },
  certificateImage: {
    height: 180,
    width: '90%',
    maxWidth: 350,
  },
});
