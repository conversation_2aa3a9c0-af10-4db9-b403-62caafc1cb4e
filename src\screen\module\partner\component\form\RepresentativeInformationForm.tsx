import React, {useEffect} from 'react';
import {FieldValues, useForm, UseFormReturn} from 'react-hook-form';
import {View, Text, TouchableOpacity, KeyboardAvoidingView} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {AppSvg} from 'wini-mobile-components';
import {StoreInfoFormProps} from '../../type';
import {TextFieldForm} from '../../../../../project-component/component-form';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {RegisterPartnerFormStyles} from '../styles/RegisterPartnerFormStyles';
import {TypoSkin} from '../../../../../assets/skin/typography';
import iconSvg from '../../../../../svgs/iconSvg';
import {validatePhoneNumber} from '../../../../../utils/validate';
import {store} from '../../../../../redux/store/store';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {CustomerRole, CustomerType} from 'redux/reducers/user/da';

const RepresentativeInformationForm = ({
  methods,
  getDataCompany,
}: {
  methods: UseFormReturn<FieldValues, any, FieldValues>;
  getDataCompany: any;
}) => {
  const cusInfo = store.getState().customer.data;
  const userRole = useSelectorCustomerState().role;

  const prefix = (icon: string) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          height: 32,
          width: 32,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <AppSvg SvgSrc={icon} size={18} />
      </View>
    );
  };

  useEffect(() => {
    if (getDataCompany) {
      methods.setValue(
        'Representative',
        getDataCompany.Representative
          ? getDataCompany.Representative
          : getDataCompany.Name,
      );
      methods.setValue(
        'Phone',
        getDataCompany.Phone ? getDataCompany.Phone : getDataCompany.Mobile,
      );
      methods.setValue('Email', getDataCompany.Email);
      methods.setValue('Position', getDataCompany.Position);
    } else if (!getDataCompany) {
      methods.setValue('Representative', cusInfo?.Name);
      methods.setValue('Phone', cusInfo?.Mobile);
      methods.setValue('Email', cusInfo?.Email);
    }
  }, [getDataCompany]);

  return (
    <TouchableOpacity>
      <View style={{gap: 20, marginBottom: 10}}>
        <Text
          style={{
            color: ColorThemes.light.neutral_text_title_color,
            ...TypoSkin.title2,
            fontWeight: 'bold',
          }}>
          Thông tin người đại diện
        </Text>
        <TextFieldForm
          control={methods.control}
          name="Representative"
          placeholder="Người đại diện *"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formRepresentativeUser)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
        <TextFieldForm
          control={methods.control}
          name="Phone"
          placeholder="Số điện thoại người đại diện *"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          type="phone-pad"
          required
          prefix={prefix(iconSvg.formRepresentativePhone)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
          onBlur={async (ev: string) => {
            if (ev === undefined || ev.length == 0) {
              methods.setError('Phone', {
                message: 'Số điện thoại không hợp lệ',
              });
              return;
            }
            var mobile = ev.trim();
            // Check if the number doesn't already start with 0 or +84
            if (!/^(\+84|0)/.test(mobile)) {
              mobile = '0' + mobile; // Add 0 at the beginning
            }
            const val = validatePhoneNumber(mobile);
            if (val) methods.clearErrors('Phone');
            else
              methods.setError('Phone', {
                message: 'Số điện thoại không hợp lệ',
              });
          }}
        />
        <TextFieldForm
          control={methods.control}
          name="Email"
          placeholder="Email người đại diện *"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formEmail)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
          onBlur={async (ev: string) => {
            if (ev === undefined || ev.length == 0) {
              methods.setError('Email', {
                message: 'Email không hợp lệ',
              });
              return;
            }
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            var email = ev.trim();
            let checkMail = emailRegex.test(email);
            if (checkMail) methods.clearErrors('Email');
            else
              methods.setError('Email', {
                message: 'Email không hợp lệ',
              });
          }}
        />
        <TextFieldForm
          control={methods.control}
          name="Position"
          placeholder="Chức vụ"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={RegisterPartnerFormStyles.textFieldStyleInputShopInfo}
          register={methods.register}
          required
          prefix={prefix(iconSvg.formRepresentativePosition)}
          disabled={
            cusInfo &&
            cusInfo?.Type === CustomerType.partner &&
            !userRole?.Role?.includes(CustomerRole.Owner)
              ? true
              : false
          }
        />
      </View>
    </TouchableOpacity>
  );
};
export default RepresentativeInformationForm;
