import React, {useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
const DropdownViolation = (props: {
  setShowDropdown(type: boolean): void;
  setOpenModal(type: boolean): void;
}) => {
  return (
    <View
      style={{
        position: 'absolute',
        top: 100,
        right: 16,
        backgroundColor: 'white',
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        zIndex: 1000,
        minWidth: 200,
      }}>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 12,
          paddingHorizontal: 16,
        }}
        onPress={() => {
          props.setShowDropdown(false);
          props.setOpenModal(true);
        }}>
        <Text
          style={{
            ...TypoSkin.buttonText2,
            color: ColorThemes.light.error_darker_color,
            fontWeight: '500',
          }}>
          ⚠️ Báo cáo vi phạm
        </Text>
      </TouchableOpacity>
    </View>
  );
};
export default DropdownViolation;
