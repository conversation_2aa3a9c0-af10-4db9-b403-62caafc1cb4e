import {
  Dimensions,
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {SkeletonImage} from '../../../project-component/skeleton-img';
import {useForm} from 'react-hook-form';
import {useEffect, useState} from 'react';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import AppButton from '../../../component/button';
import WScreenFooter from '../../layout/footer';
import ListTile from '../../../component/list-tile/list-tile';
import {useDispatch} from 'react-redux';
import {ServiceActions} from '../../../redux/reducers/cateServices/reducer';
import {LoginView} from './LoginView';
import {RegisterView} from './RegisterView';
import AppSvg from '../../../component/AppSvg';
import brandSvg from '../../../svgs/brandSvg';
import {SafeAreaView} from 'react-native-safe-area-context';

export default function Login() {
  const methods = useForm({shouldFocusError: false});
  const dispatch = useDispatch<any>();
  const [isSignUp, setSignUp] = useState(false);

  useEffect(() => {
    ServiceActions.getCateServices(dispatch);
  });

  const [bio, setBio] = useState<any>('');
  useEffect(() => {
    getDataToAsyncStorage('Mobile').then(result => {
      methods.setValue('LastMobile', result);
      methods.setValue('Mobile', result);
    });
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          if (result) {
            setBio(result);
          }
        });
      }
    });
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView style={styles.keyboardAvoidingView}>
        <View style={{alignItems: 'center'}}>
          <AppSvg SvgSrc={brandSvg.logo} size={70} />
        </View>
        <Text
          style={
            styles.welcomeText
          }>{`Để bắt đầu với chúng tôi, hãy ${isSignUp ? 'đăng ký' : 'đăng nhập'} tài khoản`}</Text>
        {isSignUp ? (
          <RegisterView methods={methods} />
        ) : (
          <LoginView bio={bio} methods={methods} />
        )}
      </KeyboardAvoidingView>
      <WScreenFooter
        style={styles.footer}
        children={
          <View>
            <ListTile
              title={
                isSignUp
                  ? 'Chuyển sang đăng nhập ngay?'
                  : 'Bạn chưa có tài khoản?'
              }
              style={styles.listTile}
              titleStyle={styles.listTileTitle}
              trailing={
                <AppButton
                  title={isSignUp ? 'Đăng nhập' : 'Đăng ký'}
                  textColor={ColorThemes.light.neutral_text_subtitle_color}
                  textStyle={styles.buttonText}
                  containerStyle={styles.buttonContainer}
                  borderColor={ColorThemes.light.transparent}
                  backgroundColor={
                    ColorThemes.light.neutral_main_background_color
                  }
                  onPress={() => {
                    setSignUp(!isSignUp);
                    methods.reset();
                  }}
                />
              }
            />
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: Dimensions.get('window').height,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  keyboardAvoidingView: {
    width: '100%',
    height: '100%',
    paddingHorizontal: 32,
  },
  logo: {
    width: 65,
    height: 65,
    alignSelf: 'center',
  },
  welcomeText: {
    ...TypoSkin.body3,
    textAlign: 'center',
    color: ColorThemes.light.neutral_text_body_color,
    marginBottom: 24,
    marginTop: 16,
  },
  footer: {
    width: '100%',
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  listTile: {
    borderRadius: 0,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
    borderTopWidth: 1,
  },
  listTileTitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
  },
  buttonText: {
    ...TypoSkin.buttonText3,
  },
  buttonContainer: {
    height: 32,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
});
