import {ColorThemes} from '../../../../assets/skin/colors';

export interface SurveyTaskFilter {
  Description?: string;
  Status?: number[];
  dateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  sortBy?: SortOption;
  CustomerId?: string;
  ExecutorId?: string;
}

export interface SortOption {
  field: string;
  direction: 'ASC' | 'DESC';
  label: string;
}

// Survey Task Status based on the SurveyStatus enum from da.tsx
export enum SurveyTaskStatus {
  SEND_SURVEY = 1,
  DONE_SURVEY = 2,
}

export const SURVEY_TASK_STATUS_OPTIONS = [
  {
    key: SurveyTaskStatus.SEND_SURVEY,
    title: '<PERSON>ang thực hiện',
    color: ColorThemes.light.secondary1_sub_color,
  },
  {
    key: SurveyTaskStatus.DONE_SURVEY,
    title: 'Hoàn thành khảo sát',
    color: ColorThemes.light.secondary2_sub_color,
  },
];

export const SORT_OPTIONS: SortOption[] = [
  {
    field: 'DateCreated',
    direction: 'DESC',
    label: 'Mớ<PERSON> nhất',
  },
  {
    field: 'DateCreated',
    direction: 'ASC',
    label: 'C<PERSON> nhất',
  },
  {
    field: 'DateStart',
    direction: 'DESC',
    label: 'Ngày bắt đầu (Mới nhất)',
  },
  {
    field: 'DateStart',
    direction: 'ASC',
    label: 'Ngày bắt đầu (Cũ nhất)',
  },
  {
    field: 'DateEnd',
    direction: 'DESC',
    label: 'Ngày kết thúc (Mới nhất)',
  },
  {
    field: 'DateEnd',
    direction: 'ASC',
    label: 'Ngày kết thúc (Cũ nhất)',
  },
  {
    field: 'Name',
    direction: 'ASC',
    label: 'Tên A-Z',
  },
  {
    field: 'Name',
    direction: 'DESC',
    label: 'Tên Z-A',
  },
];

export const DEFAULT_FILTER: SurveyTaskFilter = {
  Description: '',
  Status: [],
  dateRange: {
    startDate: undefined,
    endDate: undefined,
  },
  sortBy: SORT_OPTIONS[0], // Default to newest first
};

// Helper function to build search query for the API
export const buildSearchQuery = (filter: SurveyTaskFilter): string => {
  const queryParts: string[] = [];

  // Text search across multiple fields
  if (filter.Description && filter.Description.trim()) {
    const searchText = filter.Description.trim();
    queryParts.push(
      `(@Name:*${searchText}* | @Description:*${searchText}* | @Address:*${searchText}*)`,
    );
  }

  // Status filter
  if (filter.Status && filter.Status.length > 0) {
    const statusQuery = filter.Status.map(status => `${status}`).join(' ');
    queryParts.push(`@Status:[${statusQuery}]`);
  }

  // Date range filter for DateStart and DateEnd
  if (filter.dateRange?.startDate && filter.dateRange?.endDate) {
    const startTimestamp = filter.dateRange.startDate.getTime();
    const endTimestamp = filter.dateRange.endDate.getTime();
    queryParts.push(`@DateStart:[${startTimestamp} ${endTimestamp}]`);
    queryParts.push(`@DateEnd:[${startTimestamp} ${endTimestamp}]`);
  } else if (filter.dateRange?.startDate) {
    const startTimestamp = filter.dateRange.startDate.getTime();
    queryParts.push(`@DateStart:[${startTimestamp} *]`);
    queryParts.push(`@DateEnd:[${startTimestamp} *]`);
  } else if (filter.dateRange?.endDate) {
    const endTimestamp = filter.dateRange.endDate.getTime();
    queryParts.push(`@DateStart:[* ${endTimestamp}]`);
    queryParts.push(`@DateEnd:[* ${endTimestamp}]`);
  }

  // Customer filter
  if (filter.CustomerId) {
    queryParts.push(`@CustomerId:{${filter.CustomerId}}`);
  }

  // Executor filter
  if (filter.ExecutorId) {
    queryParts.push(`@Executor:{${filter.ExecutorId}}`);
  }

  return queryParts.length > 0 ? queryParts.join(' ') : '*';
};

// Helper function to get sort configuration for API
export const getSortConfig = (sortOption?: SortOption) => {
  if (!sortOption) {
    return {BY: 'DateCreated', DIRECTION: 'DESC'};
  }

  return {
    BY: sortOption.field,
    DIRECTION: sortOption.direction,
  };
};
