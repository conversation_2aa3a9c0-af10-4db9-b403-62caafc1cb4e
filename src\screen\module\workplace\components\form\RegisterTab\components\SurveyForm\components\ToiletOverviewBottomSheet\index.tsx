import React from 'react';
import {View, StyleSheet, ActivityIndicator, Text} from 'react-native';
import {CustomBottomSheet} from 'project-component/form/DateRangePicker/CustomBottomSheet';
import {useToiletSurvey} from './hooks/useToiletSurvey';
import {ToiletSurveyForm} from './components/ToiletSurveyForm';
import {ToiletSurveyFooter} from './components/ToiletSurveyFooter';
import {FPopup} from 'wini-mobile-components';
import {ToiletServiceItem} from 'types/toiletServiceType';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';

// Refactored ToiletOverviewBottomSheet component

interface ToiletOverviewBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  toiletId: string;
  onSuccess?: () => void;
  dataService: ToiletServiceItem;
}

export const ToiletOverviewBottomSheet: React.FC<
  ToiletOverviewBottomSheetProps
> = ({visible, onClose, toiletId, onSuccess, dataService}) => {
  const {
    noDesign,
    loading,
    initialLoading,
    fileUploadLoading,
    methods,
    popupRef,
    updateDesign,
    handleTogglePassDesign,
    handleShowFilePicker,
    handleRemoveFile,
    handleOpenFileViewer,
    handleCompleteSurvey,
  } = useToiletSurvey({
    visible,
    toiletId,
    onSuccess,
    onClose,
    dataService,
  });

  // Render loading overlay for initial data loading
  const renderLoadingOverlay = () => {
    if (!initialLoading) return null;

    return (
      <View style={styles.loadingOverlay}>
        <View style={styles.loadingContent}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_main_color}
          />
          <Text style={styles.loadingText}>Đang tải dữ liệu...</Text>
        </View>
      </View>
    );
  };

  // Render file upload loading indicator (less intrusive)
  const renderFileUploadLoading = () => {
    if (!fileUploadLoading) return null;

    return (
      <View style={styles.fileUploadLoadingBanner}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
        <Text style={styles.fileUploadLoadingText}>Đang tải file...</Text>
      </View>
    );
  };

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title="Khảo sát tổng quan"
      subTitle="Tổng quan về nhà vệ sinh"
      onCancel={onClose}
      isShowConfirm={false}
      height={'85%'}>
      <View style={styles.wrapper}>
        <FPopup ref={popupRef} />

        {/* File upload loading banner */}
        {renderFileUploadLoading()}

        {/* Main content */}
        <ToiletSurveyForm
          updateDesign={updateDesign}
          methods={methods}
          noDesign={noDesign}
          fileUploadLoading={fileUploadLoading}
          onTogglePassDesign={handleTogglePassDesign}
          onShowFilePicker={handleShowFilePicker}
          onRemoveFile={handleRemoveFile}
          onOpenFileViewer={handleOpenFileViewer}
        />
        <ToiletSurveyFooter
          loading={loading}
          onCompleteSurvey={handleCompleteSurvey}
        />

        {/* Initial loading overlay */}
        {renderLoadingOverlay()}
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_body_color,
    marginTop: 12,
    textAlign: 'center',
  },
  fileUploadLoadingBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 8,
    marginBottom: 8,
    gap: 8,
  },
  fileUploadLoadingText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
});
