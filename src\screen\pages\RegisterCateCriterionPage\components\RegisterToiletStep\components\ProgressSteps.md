# ProgressSteps Component

## Overview
The `ProgressSteps` component is a customizable progress indicator that displays horizontal progress bars to show the current step in a multi-step process.

## Features
- ✅ **Customizable step count**: Configure any number of steps through the `totalSteps` prop
- ✅ **Backward compatible**: Existing usage continues to work without changes (defaults to 3 steps)
- ✅ **Custom colors**: Customize active and inactive colors
- ✅ **Flexible styling**: Apply custom styles through the `style` prop
- ✅ **TypeScript support**: Full TypeScript interface with proper type checking

## Props

| Prop | Type | Default | Required | Description |
|------|------|---------|----------|-------------|
| `step` | `number` | - | ✅ | Current active step (1-based index) |
| `totalSteps` | `number` | `3` | ❌ | Total number of steps to display |
| `style` | `ViewStyle` | - | ❌ | Custom styles for the container |
| `activeColor` | `string` | `'#4CAF50'` | ❌ | Color for completed/active steps |
| `inactiveColor` | `string` | `'#E0E0E0'` | ❌ | Color for inactive/pending steps |

## Usage Examples

### Basic Usage (Backward Compatible)
```tsx
import ProgressSteps from './components/ProgressSteps';

// This continues to work exactly as before
<ProgressSteps step={currentStep} />
```

### Custom Number of Steps
```tsx
// 5-step process
<ProgressSteps step={currentStep} totalSteps={5} />

// 7-step process
<ProgressSteps step={currentStep} totalSteps={7} />
```

### Custom Colors
```tsx
<ProgressSteps
  step={currentStep}
  totalSteps={4}
  activeColor="#FF6B35"
  inactiveColor="#F0F0F0"
/>
```

### With Custom Styling
```tsx
<ProgressSteps
  step={currentStep}
  totalSteps={6}
  style={{ marginVertical: 20 }}
  activeColor="#2196F3"
  inactiveColor="#E3F2FD"
/>
```

## Implementation Details

### How It Works
1. The component generates an array of step indices based on `totalSteps`
2. Each step is rendered as a `View` with a background color determined by the current step
3. Steps with index ≤ current step get the `activeColor`
4. Steps with index > current step get the `inactiveColor`

### Step Logic
```tsx
const getStepColor = (stepIndex: number): string => {
  return stepIndex <= step ? activeColor : inactiveColor;
};
```

### Dynamic Rendering
```tsx
const stepIndices = Array.from({length: totalSteps}, (_, index) => index + 1);

return (
  <View style={[styles.container, style]}>
    {stepIndices.map(stepIndex => (
      <View
        key={stepIndex}
        style={[styles.progressBar, {backgroundColor: getStepColor(stepIndex)}]}
      />
    ))}
  </View>
);
```

## Migration Guide

### From Old Version
If you're using the old hardcoded 3-step version, no changes are required. The component is fully backward compatible.

### To Add More Steps
Simply add the `totalSteps` prop:
```tsx
// Before
<ProgressSteps step={currentStep} />

// After (for 5 steps)
<ProgressSteps step={currentStep} totalSteps={5} />
```

## Styling

### Default Styles
- Container: `flexDirection: 'row'`, centered alignment
- Progress bars: 100px width, 4px height, 4px border radius, 4px horizontal margin

### Customization
You can override container styles using the `style` prop. Individual progress bar styles are controlled through the component's internal stylesheet.

## Best Practices

1. **Step numbering**: Always use 1-based indexing for the `step` prop
2. **Validation**: Ensure `step` is between 1 and `totalSteps`
3. **Responsive design**: Consider how the component looks with different numbers of steps on various screen sizes
4. **Accessibility**: Consider adding accessibility labels for screen readers
5. **Color contrast**: Ensure sufficient contrast between active and inactive colors

## Testing

A test component `ProgressStepsExample.tsx` is available to demonstrate different configurations and test the component's functionality.

## Related Components

- `StepIndicator`: Alternative circular step indicator
- `StepCircleProgress`: Circular progress indicator with percentage
- `FProgressBar`: Linear progress bar with percentage display
