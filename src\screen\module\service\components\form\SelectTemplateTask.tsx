import React, { useState, useEffect, useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import { DataController } from '../../../../base-controller';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { <PERSON><PERSON><PERSON>kin } from '../../../../../assets/skin/typography';
import ListTile from '../../../../../component/list-tile/list-tile';
import { Winicon } from '../../../../../component/export-component';
import AppButton from '../../../../../component/button';

interface Props {
    methods?: any;
}

export default function SelectTemplateTask(props: Props) {
    const { methods } = props;

    const [templates, setTemplates] = useState([]);
    useEffect(() => {
        const templateController = new DataController('TeamplateTask');
        templateController.getAll().then(res => {
            if (res.code === 200) setTemplates(res.data);
        });
    }, []);

    return (
        <ScrollView style={{ flex: 1 }}>
            {/* cate */}
            {templates
                .filter((e: any) => !e.ParentId)
                .map((e: any, index: number) => (
                    <View key={e.Id} style={{
                        marginBottom: index === templates
                            .filter((e: any) => !e.ParentId).length - 1 ? 65 : 0
                    }}>
                        <TemplateTile

                            item={e}
                            listTemplates={templates}
                            methods={methods}
                        />
                    </View>
                ))}
        </ScrollView>
    );
}

const TemplateTile = ({
    item,
    listTemplates,
    methods,
}: {
    item: any;
    listTemplates: Array<any>;
    methods: any;
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const children = useMemo(() => {
        return item.ParentId
            ? []
            : listTemplates.filter(e => e.ParentId === item.Id);
    }, [listTemplates.length]);
    const isSelected = useMemo(() => {
        return item.ParentId
            ? methods.watch('tasks').some((e: any) => e.Id === item.Id)
            : methods
                .watch('tasks')
                .some((e: any) => children.some(c => c.Id === e.Id));
    }, [methods.watch('tasks'), children.length]);
    return (
        <ListTile
            key={item.Id}
            onPress={() => {
                setIsOpen(!isOpen);
            }}
            leading={
                <View style={{ flexDirection: 'row', gap: 16 }}>
                    {item.ParentId ? (
                        <View />
                    ) : (
                        <Winicon
                            src={isOpen ? 'fill/arrows/triangle-down' : 'fill/arrows/triangle-right'}
                            size={16}
                        />
                    )}
                    <Winicon
                        src={
                            item.ParentId
                                ? 'outline/files/document-2'
                                : 'outline/files/catalog'
                        }
                        style={{ width: 32, height: 32 }}
                    />
                </View>
            }
            title={item.Name}
            titleStyle={[
                TypoSkin.heading7,
                { color: ColorThemes.light.neutral_text_title_color },
            ]}
            subtitle={
                item.ParentId
                    ? `${item.Day ?? 0} ngày công (MD)`
                    : `${children.length ?? 0} đầu việc`
            }
            subTitleStyle={[
                TypoSkin.subtitle4,
                { color: ColorThemes.light.neutral_text_subtitle_color },
            ]}
            listtileStyle={{ gap: 16 }}
            style={{
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderBottomWidth: 1,
            }}
            trailing={
                <AppButton
                    title={isSelected ? 'Đã chọn' : 'Chọn'}
                    onPress={() => {
                        if (isSelected) {
                            if (item.ParentId) methods.setValue("tasks", methods.getValues("tasks").filter((e: any) => e.Id !== item.Id))
                            else methods.setValue("tasks", methods.getValues("tasks").filter((e: any) => !children.some(c => c.Id === e.Id)))
                        } else {
                            if (item.ParentId) methods.setValue("tasks", [...methods.getValues("tasks"), item])
                            else methods.setValue("tasks", [...methods.getValues("tasks").filter((e: any) => !children.some(c => c.Id === e.Id)), ...children])
                        }
                    }}
                    containerStyle={{
                        borderRadius: 8, backgroundColor: !item.ParentId && !children.length
                            ? ColorThemes.light.neutral_main_border_color
                            : isSelected
                                ? ColorThemes.light.primary_background
                                : ColorThemes.light.primary_main_color
                    }}
                    textStyle={{ paddingHorizontal: 12, paddingVertical: 4 }}
                    disabled={!item.ParentId && !children.length}
                    height={'auto'}
                    width={'auto'}
                    borderColor={isSelected ? ColorThemes.light.primary_border_color : "transparent"}
                    textColor={!item.ParentId && !children.length
                        ? ColorThemes.light.neutral_text_subtitle_color
                        :
                        isSelected
                            ? ColorThemes.light.primary_main_color
                            : ColorThemes.light.neutral_absolute_background_color
                    }
                />
            }
            bottom={
                <View
                    style={{
                        marginTop: 8,
                        alignContent: 'flex-start',
                        width: '100%',
                    }}>
                    {isOpen
                        ? children?.map((e: any) => {
                            return (
                                <TemplateTile
                                    key={e.Id}
                                    item={e}
                                    listTemplates={listTemplates}
                                    methods={methods}
                                />
                            );
                        })
                        : null}
                </View>
            }
        />
    );
};
