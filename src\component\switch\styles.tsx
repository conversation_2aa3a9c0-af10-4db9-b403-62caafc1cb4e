import {StyleSheet} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';

export const switchStyles = StyleSheet.create({
  switchContainer: {
    width: 70,
    height: 30,
    borderRadius: 16,
    backgroundColor: '#ccc',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
    justifyContent: 'flex-start',
  },
  switchOn: {
    backgroundColor: ColorThemes.light.primary_darker_color,
    justifyContent: 'flex-end',
  },
  switchDisabled: {
    opacity: 0.5,
  },
  switchText: {
    color: '#fff',
    fontWeight: 'bold',
    marginHorizontal: 8,
    zIndex: 1,
    marginRight: 15,
    marginLeft: 8,
  },
  switchCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    position: 'absolute',
    right: 4,
  },
  switchCircleOn: {
    left: 4,
    right: undefined,
  },
});
