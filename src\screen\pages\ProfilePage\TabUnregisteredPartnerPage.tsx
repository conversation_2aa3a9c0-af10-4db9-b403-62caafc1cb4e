import React from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import {profilePageStyles} from './styles/ProfilePageStyles';
import {TabUnregisteredPartnerStyles} from './styles/TabUnregisteredPartnerStyles';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';

export default function TabUnregisteredPartnerPage() {
  const navigation = useNavigation<any>();
  const handleRegisterShop = () => {
    navigation.navigate(RootScreen.TabRegisterPartner);
  };
  return (
    <View style={profilePageStyles.container}>
      <View style={TabUnregisteredPartnerStyles.containetNotShop}>
        <Image
          source={require('../../../assets/Shopping.png')}
          style={TabUnregisteredPartnerStyles.illustrationNotShop}
        />
        <Text style={TabUnregisteredPartnerStyles.descriptionNotShop}>
          Bạn là khách hàng cá nhân, Chọn{'\n'}
          <Text style={TabUnregisteredPartnerStyles.boldTextNotShop}>
            “Đăng ký”
          </Text>{' '}
          để chuyển đổi thành cửa hàng
        </Text>
        <TouchableOpacity
          style={TabUnregisteredPartnerStyles.buyButtonNotShop}
          onPress={() => handleRegisterShop()}>
          <Text style={TabUnregisteredPartnerStyles.actionButtonTextNotShop}>
            Đăng Ký
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
