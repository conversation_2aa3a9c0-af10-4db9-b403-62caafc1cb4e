import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  Modal,
  SafeAreaView,
  Pressable,
} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';
import {ColorThemes} from 'assets/skin/colors';
import {TypeStringData} from 'screen/module/service/components/da';
import {useForm} from 'react-hook-form';
import TicketCard from 'screen/module/ticket/components/TicketCard';
interface ManageTicketProps {
  GetToiletInfo?: any;
  tickets?: any[];
  allTickets?: any[];
  type?: string;
}

const ManageTicket: React.FC<ManageTicketProps> = ({
  GetToiletInfo,
  tickets,
  allTickets,
  type,
}) => {
  // Initialize form methods for TicketCard
  const methods = useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const seeMore = () => {
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };
  const Header: React.FC<{title: string}> = ({title}) => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>{title}</Text>
      <TouchableOpacity onPress={seeMore}>
        <Text style={styles.seeMore}>Xem thêm</Text>
      </TouchableOpacity>
    </View>
  );

  const HeaderAll: React.FC<{title: string}> = ({title}) => (
    <View style={styles.headerContainer}>
      <Text style={[TypoSkin.semibold4, styles.headerTitleAll]}>{title}</Text>
      <TouchableOpacity onPress={seeMore}>
        <Text style={styles.viewAllText}>Xem thêm</Text>
      </TouchableOpacity>
    </View>
  );

  const TicketList: React.FC = () => {
    const renderTicketItem = ({item, index}: {item: any; index: number}) => (
      <TicketCard
        item={item}
        index={index}
        relativeData={GetToiletInfo}
        methods={methods}
        fileInfor={[]}
        edit={true}
        customer={undefined}
        typeLabel={
          TypeStringData.find((e: any) => e.key === item?.Type)?.title ?? '-'
        }
      />
    );
    if (tickets && tickets?.length == 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Không có dữ liệu</Text>
        </View>
      );
    }
    return (
      <FlatList
        data={tickets || []}
        nestedScrollEnabled
        renderItem={renderTicketItem}
        keyExtractor={item => item.Id}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={!type && {paddingHorizontal: 10}}
        ItemSeparatorComponent={() => <View style={{width: 10}} />}
      />
    );
  };
  return (
    <Pressable style={{paddingBottom: 16}}>
      {type == 'all' ? (
        <HeaderAll title="Quản lý Ticket" />
      ) : (
        <Header title="Quản lý Ticket" />
      )}
      <TicketList />

      {/* Modal Bottom Sheet */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={closeModal}>
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Tất cả Tickets</Text>
            <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>Đóng</Text>
            </TouchableOpacity>
          </View>

          {/* Modal Content */}
          <FlatList
            data={allTickets || []}
            renderItem={({item}) => (
              <TicketCard
                item={item}
                index={0}
                type="More"
                methods={methods}
                typeLabel={
                  TypeStringData.find((e: any) => e.key === item?.Type)
                    ?.title ?? '-'
                }
              />
            )}
            keyExtractor={(item, index) =>
              item.Id?.toString() || index.toString()
            }
            contentContainerStyle={styles.modalListContainer}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      </Modal>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '700',
    paddingHorizontal: 15,
  },
  seeMore: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    paddingHorizontal: 15,
  },
  ticketCard: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginRight: 12,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,

    width: 300, // Chiều rộng cố định cho mỗi card
    minHeight: 200, // Chiều cao tối thiểu
  },
  ticketHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    marginRight: 12,
  },
  gradientIcon: {
    width: 48,
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: 'bold',
  },
  titleContainer: {
    flex: 1,
  },
  ticketTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
    marginBottom: 4,
  },
  ticketSubtitle: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  ticketDetails: {
    marginBottom: 16,
  },
  detailText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
    marginBottom: 4,
    lineHeight: 20,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusButton: {
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusButtonText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_lighter_reverse_border,
    fontWeight: '500',
  },
  actionIcons: {
    flexDirection: 'row',
    gap: 8,
  },
  iconButton: {
    padding: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  emptyText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitleAll: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  viewAllText: {
    ...TypoSkin.title5,
    fontWeight: 500,
    color: ColorThemes.light.primary_main_color,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.secondary2_border_color,
  },
  modalTitle: {
    ...TypoSkin.semibold4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 8,
  },
  closeButtonText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.white,
    fontWeight: '500',
  },
  modalListContainer: {
    padding: 16,
    flexGrow: 1,
    justifyContent: 'center',
  },
});

export default ManageTicket;
