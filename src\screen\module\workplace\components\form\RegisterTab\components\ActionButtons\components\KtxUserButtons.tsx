import React from 'react';
import {View, StyleSheet} from 'react-native';
import {AsignForConsultant} from '../../AsignForConsultant';
import {CreateService} from '../../CreateService';
import {ToiletServiceStatus} from 'screen/module/service/components/da';
import {DataController} from 'screen/base-controller';
import {AppButton, showPopup} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';
import {ComponentStatus} from 'component/component-status';
import {FDialog, showDialog} from 'component/export-component';
import {CustomerItem} from 'redux/reducers/user/da';
import {ToiletServiceItem} from 'types/toiletServiceType';
import {CateServicesItem} from 'types/cateServiceType';

interface KtxUserButtonsProps {
  data: CustomerItem;
  serviceData: ToiletServiceItem;
  customer: any;
  cateServices: CateServicesItem[];
  navigation: any;
  setServiceData: (data: ToiletServiceItem) => void;
  onCompleteConsulting: () => void;
  dialogRef: any;
  popupRef: any;
}

export const KtxUserButtons: React.FC<KtxUserButtonsProps> = ({
  data,
  serviceData,
  customer,
  cateServices,
  navigation,
  dialogRef,
  popupRef,
  setServiceData,
  onCompleteConsulting,
}) => {
  const handleAcceptConsulting = async () => {
    const controller = new DataController('ToiletServices');
    controller.edit([{...serviceData, Status: ToiletServiceStatus.research}]);
    setServiceData({
      ...serviceData,
      Status: ToiletServiceStatus.research,
    });
  };

  if (data) {
    return (
      <View style={styles.buttonRow}>
        <AppButton
          title={'Tìm tư vấn viên phù hợp'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={styles.button}
          onPress={() => {
            showPopup({
              ref: popupRef,
              children: (
                <AsignForConsultant
                  onDone={() => {
                    navigation.goBack();
                  }}
                  ref={popupRef}
                  serviceData={serviceData}
                  lat={data.Lat}
                  lng={data.Long}
                  cateServices={cateServices}
                />
              ),
            });
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </View>
    );
  }

  if (serviceData?.Status === ToiletServiceStatus.register) {
    return (
      <View style={styles.buttonRow}>
        <AppButton
          title={'Tiếp nhận tư vấn'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={styles.button}
          onPress={() => {
            showDialog({
              ref: dialogRef,
              status: ComponentStatus.INFOR,
              title: 'Xác nhận tiếp nhận tư vấn đơn hàng?',
              onSubmit: handleAcceptConsulting,
            });
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
        <FDialog ref={dialogRef} />
      </View>
    );
  }

  return (
    <View style={styles.buttonRow}>
      <AppButton
        title={'Tạo mới dịch vụ'}
        backgroundColor={ColorThemes.light.neutral_main_background_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={() => {
          showPopup({
            ref: popupRef,
            enableDismiss: true,
            children: (
              <CreateService
                ref={popupRef}
                customer={customer.data}
                serviceId={serviceData?.Id}
              />
            ),
          });
        }}
        textColor={ColorThemes.light.neutral_text_subtitle_color}
      />
      <AppButton
        title={'Hoàn thành tư vấn'}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={() => {
          showDialog({
            ref: dialogRef,
            status: ComponentStatus.INFOR,
            title: 'Xác nhận hoàn thành tư vấn đơn hàng?',
            onSubmit: onCompleteConsulting,
          });
        }}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
    flex: 1,
  },
  button: {
    ...TypoSkin.subtitle4,
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingVertical: 5,
  },
});
