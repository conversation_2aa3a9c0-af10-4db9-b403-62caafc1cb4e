import React from 'react';
import {Text, View, TouchableOpacity, StyleSheet} from 'react-native';
import {CheckboxProps} from '../types';

const Checkbox: React.FC<CheckboxProps> = ({
  isChecked,
  onToggle,
  disabled = false,
}) => {
  const handlePress = () => {
    if (!disabled) {
      onToggle();
    }
  };

  return (
    <TouchableOpacity
      style={[styles.checkboxContainer, disabled && styles.disabledContainer]}
      onPress={handlePress}
      disabled={disabled}>
      <View
        style={[
          styles.checkbox,
          isChecked && styles.checkedBox,
          disabled && styles.disabledCheckbox,
          disabled && isChecked && styles.disabledCheckedBox,
        ]}>
        {isChecked && (
          <Text
            style={[styles.checkmark, disabled && styles.disabledCheckmark]}>
            ✓
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkboxContainer: {
    marginRight: 12,
    marginTop: 2,
  },
  disabledContainer: {
    opacity: 0.5,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  disabledCheckbox: {
    borderColor: '#ccc',
    backgroundColor: '#f5f5f5',
  },
  disabledCheckedBox: {
    backgroundColor: '#ccc',
    borderColor: '#ccc',
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  disabledCheckmark: {
    color: '#999',
  },
});

export default Checkbox;
