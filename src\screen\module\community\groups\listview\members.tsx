import {
  Dimensions,
  FlatList,
  Linking,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {GroupDA} from '../da';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  FBottomSheet,
  FDialog,
  Winicon,
  HashTag,
  showDialog,
  showSnackbar,
  FTextField,
} from '../../../../../component/export-component';
import ListTile from '../../../../../component/list-tile/list-tile';
import {
  FPopup,
  showPopup,
  closePopup,
} from '../../../../../component/popup/popup';
import ConfigAPI from '../../../../../config/configApi';
import {groupRole} from '../../../../../config/Contanst';
import EmptyPage from '../../../../../project-component/empty-page';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {navigate, RootScreen} from '../../../../../router/router';
import {Ultis} from '../../../../../utils/Utils';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';

export default function MembersGroup(pros: any) {
  const [searchValue, setSearchValue] = useState('');
  // const currentUser = useSelectorCustomerState().data;
  const [lstMember, setGroup] = useState<any>(pros.Members);

  const groupDA = new GroupDA();
  const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const popupRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const status = 0;

  useEffect(() => {
    if (searchValue) {
      // Lọc danh sách thành viên theo tên
      const filteredMembers = lstMember.filter((member: any) =>
        member.Name.toLowerCase().includes(searchValue.toLowerCase()),
      );
      setGroup(filteredMembers);
    } else {
      // Hiển thị danh sách thành viên ban đầu
      setGroup(pros.Members);
    }
  }, [searchValue]);

  return (
    <View
      style={{
        height: Dimensions.get('window').height - 200,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          height: 56,
          gap: 8,
          paddingHorizontal: 16,
        }}>
        <FTextField
          style={{paddingHorizontal: 16, flex: 1, height: 40}}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
          }}
          value={searchValue}
          placeholder="Tìm kiếm..."
          prefix={
            <Winicon
              src="outline/development/zoom"
              size={14}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
          }
        />
      </View>
      <Pressable
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <FlatList
          data={lstMember}
          scrollEnabled={pros.scrollEnabled}
          nestedScrollEnabled
          style={{
            height: '100%',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}
          keyExtractor={(item, index) => index.toString()}
          ListEmptyComponent={() => {
            return <EmptyPage title="Không có dữ liệu" />;
          }}
          ListFooterComponent={() => {
            return <View style={{height: 32}} />;
          }}
          renderItem={({item}) => {
            return (
              <ListTile
                key={item?.Id}
                onPress={() => {
                  navigate(RootScreen.ProfileCommunity, {Id: item?.Id});
                }}
                listtileStyle={{gap: 8}}
                leading={
                  <SkeletonImage
                    source={{
                      uri: item?.AvatarUrl
                        ? `${ConfigAPI.imgUrlId + item?.AvatarUrl}`
                        : 'https://placehold.co/48/FFFFFF/000000/png',
                    }}
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: 50,
                      backgroundColor: '#f0f0f0',
                    }}
                  />
                }
                title={
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}
                    numberOfLines={1}>
                    {item?.Name}
                  </Text>
                }
                subtitle={
                  <View style={{flex: 1, width: '100%'}}>
                    <Text
                      style={{
                        ...TypoSkin.subtitle3,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}
                      numberOfLines={1}>
                      Đã tham gia {Ultis.getDiffrentTime(item?.DateCreated)}
                    </Text>
                  </View>
                }
                subTitleStyle={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}
                trailing={
                  item.Id === currentUser?.Id ? null : (
                    <View
                      style={{
                        alignItems: 'center',
                        flexDirection: 'row',
                        gap: 8,
                      }}>
                      <HashTag
                        onPress={() => {
                          if (
                            pros.role === groupRole.admin ||
                            (pros.role === groupRole.subadmin &&
                              item.Role === groupRole.member)
                          ) {
                            showPopup({
                              ref: popupRef,
                              enableDismiss: true,
                              children: (
                                <EditRole
                                  ref={popupRef}
                                  role={item.Role}
                                  GroupId={pros.GroupId}
                                  CustomerId={item.Id}
                                  onUpdate={(value: any) => {
                                    const lstUpdate = lstMember.map(
                                      (member: any) => {
                                        if (member.Id === value.Id) {
                                          return {...member, Role: value.Role};
                                        }
                                        return member;
                                      },
                                    );
                                    setGroup(lstUpdate);
                                  }}
                                />
                              ),
                            });
                          }
                        }}
                        title={
                          item.Role === groupRole.admin
                            ? 'Owner'
                            : item.Role === groupRole.subadmin
                              ? 'Quản trị viên'
                              : 'Thành viên'
                        }
                      />

                      {pros.role === groupRole.member || !pros.role ? null : (
                        <TouchableOpacity
                          style={{padding: 4}}
                          onPress={() => {
                            showDialog({
                              ref: dialogRef,
                              status: ComponentStatus.WARNING,
                              title: 'Bạn có chắc muốn xóa thành viên này?',
                              onSubmit: async () => {
                                const res = await groupDA.deleteRoles(
                                  pros.GroupId,
                                  item.Id,
                                );

                                if (res) {
                                  const lstUpdate = lstMember.filter(
                                    (member: any) => member.Id !== item.Id,
                                  );
                                  setGroup(lstUpdate);
                                  pros.onDelete({Id: item.Id});
                                  closePopup(dialogRef);
                                  showSnackbar({
                                    message: `Đã xóa ${item.Name} thành công`,
                                    status: ComponentStatus.SUCCSESS,
                                  });
                                  return;
                                }
                              },
                            });
                          }}>
                          <Winicon
                            src="fill/user interface/c-delete"
                            color={ColorThemes.light.error_main_color}
                            size={16}
                          />
                        </TouchableOpacity>
                      )}
                    </View>
                  )
                }
                // bottom={
                //   status != 0 ? null : (
                //     <View
                //       style={{
                //         flexDirection: 'row',
                //         justifyContent: 'space-between',
                //         gap: 8,
                //         paddingTop: 12,
                //         alignItems: 'center',
                //       }}>
                //       <AppButton
                //         title={'Chấp nhận'}
                //         backgroundColor={ColorThemes.light.primary_main_color}
                //         borderColor="transparent"
                //         containerStyle={{
                //           height: 35,
                //           flex: 1,
                //           borderRadius: 8,
                //           paddingHorizontal: 12,
                //           paddingVertical: 5,
                //         }}
                //         onPress={async () => {}}
                //         textColor={
                //           ColorThemes.light.neutral_absolute_background_color
                //         }
                //       />
                //       <AppButton
                //         title={'Từ chối'}
                //         backgroundColor={
                //           ColorThemes.light.neutral_main_background_color
                //         }
                //         borderColor="transparent"
                //         containerStyle={{
                //           height: 35,
                //           flex: 1,
                //           borderRadius: 8,
                //           paddingHorizontal: 12,
                //           paddingVertical: 5,
                //         }}
                //         onPress={async () => {}}
                //         textColor={
                //           ColorThemes.light.neutral_text_subtitle_color
                //         }
                //       />
                //     </View>
                //   )
                // }
              />
            );
          }}
        />
      </Pressable>
    </View>
  );
}

const EditRole = (pros: any) => {
  const [role, setRole] = useState(pros.role);
  const roles = [
    {id: groupRole.member, name: 'Thành viên'},
    {id: groupRole.subadmin, name: 'Quản trị viên'},
    {id: groupRole.admin, name: 'Owner'},
  ];
  const groupDA = new GroupDA();
  useEffect(() => {
    setRole(pros.role);
  }, [pros.role]);
  return (
    <Pressable
      style={{
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        height: Dimensions.get('window').height / 2.5,
        width: '100%',
      }}>
      <ScreenHeader
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
        }}
        title="Chọn vai trò"
        backIcon={<Winicon src="outline/user interface/e-remove" size={20} />}
        onBack={() => closePopup(pros.ref)}
      />
      {roles.map((item, index) => {
        return (
          <TouchableOpacity
            key={index}
            onPress={() => {
              setRole(item.id);
            }}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
              width: '100%',
              paddingVertical: 16,
              paddingHorizontal: 16,
            }}>
            <View
              style={{
                borderRadius: 100,
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
                width: 20,
                height: 20,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              {role === item.id ? (
                <View
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: 100,
                    backgroundColor: ColorThemes.light.primary_main_color,
                  }}
                />
              ) : null}
            </View>
            <Text
              style={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              {item.name}
            </Text>
          </TouchableOpacity>
        );
      })}
      <WScreenFooter style={{marginHorizontal: 16}}>
        <AppButton
          title={'Lưu'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={async () => {
            const result = await groupDA.updateMemberRole(
              pros.GroupId,
              pros.CustomerId,
              role,
            );
            if (result) {
              closePopup(pros.ref);
              pros.onUpdate({Id: pros.CustomerId, Role: role});
            }
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </Pressable>
  );
};

const SkeletonPlaceCard = () => {
  return (
    <SkeletonPlaceholder
      backgroundColor="#f0f0f0"
      highlightColor="#e0e0e0"
      speed={800}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        {/* Avatar placeholder */}
        <View
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            marginRight: 12,
          }}
        />

        {/* Title and subtitle container */}
        <View style={{flex: 1, gap: 8}}>
          {/* Title placeholder */}
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
            }}
          />

          {/* Subtitle placeholder */}
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Icons container */}
        <View style={{flexDirection: 'row', gap: 8}}>
          {/* Phone icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />

          {/* Chat icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
