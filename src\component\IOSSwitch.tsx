import React, {useEffect, useRef} from 'react';
import {
  Animated,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';

interface IOSSwitchProps {
  /**
   * Boolean value indicating whether the switch is on (true) or off (false)
   */
  value: boolean;

  /**
   * Function called when the switch value changes
   */
  onValueChange: (value: boolean) => void;

  /**
   * Color of the switch track when it's on
   * @default '#34C759' (iOS green)
   */
  onColor?: string;

  /**
   * Color of the switch track when it's off
   * @default '#E9E9EA' (iOS light gray)
   */
  offColor?: string;

  /**
   * Color of the switch thumb/knob
   * @default '#FFFFFF'
   */
  thumbColor?: string;

  /**
   * Size multiplier for the switch (1 = default iOS size)
   * @default 1
   */
  size?: number;

  /**
   * Whether the switch is disabled
   * @default false
   */
  disabled?: boolean;

  /**
   * Additional style for the container
   */
  style?: ViewStyle;

  /**
   * Test ID for testing
   */
  testID?: string;
}

/**
 * An iOS-style switch component with smooth animation
 */
const IOSSwitch: React.FC<IOSSwitchProps> = ({
  value,
  onValueChange,
  onColor = '#34C759',
  offColor = '#E9E9EA',
  thumbColor = '#FFFFFF',
  size = 1,
  disabled = false,
  style,
  testID,
}) => {
  // Base dimensions (iOS default)
  const TRACK_WIDTH = 51 * size;
  const TRACK_HEIGHT = 31 * size;
  const THUMB_SIZE = 27 * size;
  const THUMB_MARGIN = 2 * size;
  const TRAVEL_DISTANCE = TRACK_WIDTH - THUMB_SIZE - THUMB_MARGIN * 2;

  // Animation values
  const thumbPosition = useRef(
    new Animated.Value(value ? TRAVEL_DISTANCE : 0),
  ).current;
  const trackColor = useRef(new Animated.Value(value ? 1 : 0)).current;
  const thumbScale = useRef(new Animated.Value(1)).current;

  // Update animation when value changes
  useEffect(() => {
    Animated.parallel([
      Animated.spring(thumbPosition, {
        toValue: value ? TRAVEL_DISTANCE : 0,
        friction: 6,
        tension: 100,
        useNativeDriver: true,
      }),
      Animated.timing(trackColor, {
        toValue: value ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  }, [value, TRAVEL_DISTANCE, thumbPosition, trackColor]);

  // Interpolate track color
  const interpolatedTrackColor = trackColor.interpolate({
    inputRange: [0, 1],
    outputRange: [offColor, onColor],
  });

  // Handle press animation
  const handlePressIn = () => {
    Animated.spring(thumbScale, {
      toValue: 0.9,
      friction: 5,
      tension: 300,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(thumbScale, {
      toValue: 1,
      friction: 5,
      tension: 300,
      useNativeDriver: true,
    }).start();
  };

  // Toggle the switch
  const toggle = () => {
    if (!disabled) {
      onValueChange(!value);
    }
  };

  return (
    <TouchableWithoutFeedback
      onPress={toggle}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}>
      <View
        style={[styles.container, style, {opacity: disabled ? 0.5 : 1}]}
        testID={testID}>
        <Animated.View
          style={[
            styles.track,
            {
              backgroundColor: interpolatedTrackColor,
              width: TRACK_WIDTH,
              height: TRACK_HEIGHT,
              borderRadius: TRACK_HEIGHT / 2,
            },
          ]}>
          <Animated.View
            style={[
              styles.thumb,
              {
                width: THUMB_SIZE,
                height: THUMB_SIZE,
                borderRadius: THUMB_SIZE / 2,
                backgroundColor: thumbColor,
                margin: THUMB_MARGIN,
                transform: [{translateX: thumbPosition}, {scale: thumbScale}],
              },
            ]}
          />
        </Animated.View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  track: {
    justifyContent: 'center',
  },
  thumb: {
    position: 'absolute',
  },
});

export default IOSSwitch;
