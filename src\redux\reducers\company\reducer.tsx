import { Dispatch, PayloadAction, UnknownAction, createSlice } from '@reduxjs/toolkit'
import { CompanyProfileItem } from './da'
import { CustomerItem } from '../user/da'
import { DataController } from '../../../screen/base-controller'
import ConfigAPI from '../../../config/configApi'

interface CompanySimpleResponse {
    data?: CompanyProfileItem,
    owner?: CustomerItem,
    ktxgroup?: CompanyProfileItem,
    onLoading?: boolean,
    type?: string
}

const initState: CompanySimpleResponse = {
    data: undefined,
    owner: undefined,
    ktxgroup: undefined,
    onLoading: false
}

export const companySlice = createSlice({
    name: 'company',
    initialState: initState,
    reducers: {
        handleActions: (state, action: PayloadAction<any>) => {
            switch (action.payload.type) {
                case 'GETINFOR':
                    state.data = action.payload.data.company
                    state.owner = action.payload.data.owner
                    break;
                case "EDIT":
                    state.data = action.payload.data
                    break;
                case "GETKTXINFOR":
                    state.ktxgroup = action.payload.data
                    break;
                default:
                    break;
            }
            state.onLoading = false
        },
        onFetching: (state) => {
            state.onLoading = true
        },
        onReset: (state) => {
            state.data = undefined
            state.onLoading = false
            state.owner = undefined
        }
    },
})

const { handleActions, onFetching, onReset } = companySlice.actions

export default companySlice.reducer

export class CompanyProfileActions {
    static getInfor = async (dispatch: Dispatch<UnknownAction>, id: string) => {
        dispatch(onFetching())
        const controller = new DataController("CompanyProfile")
        const res = await controller.getById(id)
        if (res.code === 200) {
            let data = { company: res.data, owner: undefined }
            const customerController = new DataController("Customer")
            const ownerRes = await customerController.getListSimple({ page: 1, size: 2, query: `@CompanyProfileId:{${id}}` })
            if (ownerRes.code === 200 && ownerRes.data.length) data.owner = ownerRes.data[0]
            dispatch(handleActions({
                type: 'GETINFOR',
                data: data,
            }))
        }
    }

    static getKTXGroupInfor = async (dispatch: Dispatch<UnknownAction>) => {
        const controller = new DataController("CompanyProfile")
        const res = await controller.getById(ConfigAPI.ktxCompanyId)
        if (res.code === 200) {
            dispatch(handleActions({
                type: 'GETKTXINFOR',
                data: res.data,
            }))
        }
    }

    static edit = async (dispatch: Dispatch<UnknownAction>, profile: CompanyProfileItem) => {
        dispatch(onFetching())
        const controller = new DataController("CompanyProfile")
        const res = await controller.edit([profile])
        if (res.code === 200) {
            dispatch(handleActions({
                type: 'EDIT',
                data: profile,
            }))
        }
    }

    static delete = async (dispatch: Dispatch<UnknownAction>, profileId: string) => {
        dispatch(onFetching())
        const controller = new DataController("CompanyProfile")
        const res = await controller.delete([profileId])
        if (res.code === 200) dispatch(onReset())
    }

    static reset = (dispatch: Dispatch<UnknownAction>) => {
        dispatch(onReset())
    }
}