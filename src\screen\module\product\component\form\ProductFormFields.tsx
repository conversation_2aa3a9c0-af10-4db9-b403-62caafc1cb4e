import React, {useEffect} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Controller, Control, FieldErrors} from 'react-hook-form';
import {AppSvg} from 'wini-mobile-components';
import {InputForm} from './InputForm';
import {CreatePartnerProductFormStyles} from '../styles/CreatePartnerProductFormStyles';
import {TextFieldForm} from '../../../../../project-component/component-form';
import iconSvg from '../../../../../svgs/iconSvg';
import {Title} from 'config/Contanst';

interface ProductFormFieldsProps {
  control: Control<any>;
  errors: FieldErrors<any>;
  watch: (name: string) => any;
  register: any;
  selectType: string;
  selectCate: string;
  selectCateattribute: string;
  selectAttribute: string;
  openBottomSheet: (title: string, currentValue?: any) => void;
  styles: any;
}

const ProductFormFields: React.FC<ProductFormFieldsProps> = ({
  control,
  errors,
  watch,
  register,
  selectType,
  selectCate,
  selectCateattribute,
  selectAttribute,
  openBottomSheet,
  styles,
}) => {
  const prefix = (icon: string) => {
    return (
      <View style={{paddingRight: 10}}>
        <AppSvg SvgSrc={icon} size={20} />
      </View>
    );
  };

  return (
    <>
      <View style={[{marginBottom: 12}]}>
        <InputForm
          control={control}
          checkLengthText={watch('Name')?.length || 0}
          nameFeild="Tên sản phẩm"
          name="Name"
          rule="Vui lòng nhập tên sản phẩm"
          placeholder="Tên sản phẩm"
          maxlength={50}
          mutiline={true}
        />
        {errors.Name &&
          (!watch('Name') ||
            (Array.isArray(watch('Name')) && watch('Name').length === 0)) && (
            <Text style={CreatePartnerProductFormStyles.error}>
              {(errors.Name?.message as string) || 'Vui lòng chọn tên sản phẩm'}
            </Text>
          )}
      </View>

      <View style={[styles.option, {marginBottom: 12}]}>
        <TextFieldForm
          control={control}
          name="Code"
          placeholder="Mã sản phẩm"
          returnKeyType="done"
          errors={errors}
          textFieldStyle={{
            height: 48,
            borderWidth: 0,
          }}
          style={{
            color: '#000000', // Màu chữ đen giống InputForm
          }}
          register={register}
          required
          prefix={prefix(iconSvg.QrCode)}
        />
      </View>

      {/* Type Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="Type"
          rules={{
            required: 'Vui lòng chọn phân loại',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
              }}
              onPress={() => {
                openBottomSheet('phân loại', value);
              }}>
              {prefix(iconSvg.Type)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  fontWeight: '400',
                  color: value || selectType ? '#000' : '#999',
                }}>
                {selectType || 'Phân loại'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.Type && !watch('Type') && (
        <Text style={CreatePartnerProductFormStyles.error}>
          {(errors.Type?.message as string) || 'Vui lòng chọn loại sản phẩm'}
        </Text>
      )}

      {/* Category Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="CategoryId"
          rules={{
            required: 'Vui lòng chọn danh mục sản phẩm',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
              }}
              onPress={() => {
                openBottomSheet('danh mục sản phẩm', selectCate);
              }}>
              {prefix(iconSvg.category)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  fontWeight: '400',
                  color: value ? '#000' : '#999',
                }}>
                {selectCate || 'Danh mục sản phẩm'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.CategoryId && !watch('CategoryId') && (
        <Text style={CreatePartnerProductFormStyles.error}>
          {(errors.CategoryId?.message as string) ||
            'Vui lòng chọn danh mục sản phẩm'}
        </Text>
      )}

      {/* CateAttribute Field */}
      <View style={[styles.option, {marginBottom: 12}]}>
        <Controller
          control={control}
          name="CateAttributeId"
          disabled={!watch('CategoryId')}
          rules={{
            required: 'Vui lòng chọn nhóm thuộc tính',
          }}
          render={({field: {value, onChange}}) => (
            <TouchableOpacity
              style={{
                height: 48,
                borderWidth: 0,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 0,
                opacity: !watch('CategoryId') ? 0.5 : 1, // Visual indication based on CategoryId
              }}
              disabled={!watch('CategoryId')}
              onPress={() => {
                openBottomSheet('nhóm thuộc tính', value);
              }}>
              {prefix(iconSvg.ListAttribute)}
              <Text
                style={{
                  flex: 1,
                  marginLeft: 8,
                  fontWeight: '400',
                  color: selectCateattribute ? '#000' : '#999',
                }}>
                {selectCateattribute || 'Nhóm thuộc tính'}
              </Text>
              {prefix(iconSvg.arrowRight)}
            </TouchableOpacity>
          )}
        />
      </View>
      {errors.CateAttributeId && !watch('CateAttributeId') && (
        <Text style={CreatePartnerProductFormStyles.error}>
          {(errors.CateAttributeId?.message as string) ||
            'Vui lòng chọn thuộc tính'}
        </Text>
      )}
    </>
  );
};

export default ProductFormFields;
