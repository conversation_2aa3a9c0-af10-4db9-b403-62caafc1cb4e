import { SvgXml } from "react-native-svg";

export const FilledHome = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.4863 9.86375L10.4414 1.82469C10.3835 1.76665 10.3147 1.72061 10.2389 1.68919C10.1632 1.65777 10.082 1.6416 10 1.6416C9.918 1.6416 9.83681 1.65777 9.76106 1.68919C9.68532 1.72061 9.61652 1.76665 9.55859 1.82469L1.51367 9.86375C1.2793 10.0981 1.14648 10.4165 1.14648 10.7485C1.14648 11.438 1.70703 11.9985 2.39648 11.9985H3.24414V17.7348C3.24414 18.0805 3.52344 18.3598 3.86914 18.3598H8.75V13.9848H10.9375V18.3598H16.1309C16.4766 18.3598 16.7559 18.0805 16.7559 17.7348V11.9985H17.6035C17.9355 11.9985 18.2539 11.8677 18.4883 11.6313C18.9746 11.143 18.9746 10.352 18.4863 9.86375Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineHome = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.4864 9.86382L10.9395 2.32085L10.4337 1.81499C10.3184 1.70049 10.1625 1.63623 10.0001 1.63623C9.83762 1.63623 9.68175 1.70049 9.56649 1.81499L1.51375 9.86382C1.39565 9.98146 1.30231 10.1216 1.23925 10.2759C1.17618 10.4302 1.14467 10.5956 1.14657 10.7623C1.15438 11.4498 1.72664 11.9986 2.41414 11.9986H3.24422V18.3599H16.7559V11.9986H17.6036C17.9376 11.9986 18.252 11.8677 18.4884 11.6314C18.6047 11.5154 18.6969 11.3775 18.7596 11.2256C18.8223 11.0737 18.8543 10.9109 18.8536 10.7466C18.8536 10.4146 18.7227 10.1001 18.4864 9.86382V9.86382ZM11.0938 16.9537H8.90633V12.9693H11.0938V16.9537ZM15.3497 10.5923V16.9537H12.3438V12.5005C12.3438 12.0689 11.9942 11.7193 11.5626 11.7193H8.43758C8.00594 11.7193 7.65633 12.0689 7.65633 12.5005V16.9537H4.65047V10.5923H2.77547L10.002 3.37163L10.4532 3.8228L17.2266 10.5923H15.3497Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineAudit = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.78125 4.88281C5.69531 4.88281 5.625 4.95312 5.625 5.03906V5.97656C5.625 6.0625 5.69531 6.13281 5.78125 6.13281H13.2812C13.3672 6.13281 13.4375 6.0625 13.4375 5.97656V5.03906C13.4375 4.95312 13.3672 4.88281 13.2812 4.88281H5.78125ZM9.375 7.69531H5.78125C5.69531 7.69531 5.625 7.76562 5.625 7.85156V8.78906C5.625 8.875 5.69531 8.94531 5.78125 8.94531H9.375C9.46094 8.94531 9.53125 8.875 9.53125 8.78906V7.85156C9.53125 7.76562 9.46094 7.69531 9.375 7.69531ZM8.4375 16.6406H4.0625V2.89062H15V9.14062C15 9.22656 15.0703 9.29688 15.1562 9.29688H16.25C16.3359 9.29688 16.4062 9.22656 16.4062 9.14062V2.10938C16.4062 1.76367 16.127 1.48438 15.7812 1.48438H3.28125C2.93555 1.48438 2.65625 1.76367 2.65625 2.10938V17.4219C2.65625 17.7676 2.93555 18.0469 3.28125 18.0469H8.4375C8.52344 18.0469 8.59375 17.9766 8.59375 17.8906V16.7969C8.59375 16.7109 8.52344 16.6406 8.4375 16.6406ZM17.0312 14.9219H14.2188V14.207C15.123 13.9375 15.7812 13.1016 15.7812 12.1094C15.7812 10.9004 14.8027 9.92188 13.5938 9.92188C12.3848 9.92188 11.4062 10.9004 11.4062 12.1094C11.4062 13.0996 12.0645 13.9375 12.9688 14.207V14.9219H10.1562C9.98437 14.9219 9.84375 15.0625 9.84375 15.2344V18.2031C9.84375 18.375 9.98437 18.5156 10.1562 18.5156H17.0312C17.2031 18.5156 17.3438 18.375 17.3438 18.2031V15.2344C17.3438 15.0625 17.2031 14.9219 17.0312 14.9219ZM12.6172 12.1094C12.6172 11.5703 13.0547 11.1328 13.5938 11.1328C14.1328 11.1328 14.5703 11.5703 14.5703 12.1094C14.5703 12.6484 14.1328 13.0859 13.5938 13.0859C13.0547 13.0859 12.6172 12.6484 12.6172 12.1094ZM16.1328 17.3047H11.0547V16.1328H16.1328V17.3047Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlinePLus = ({ size, color }: { size?: number, color?: string }) => <SvgXml xml={`<svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.5625 4.75H17.4375C17.6042 4.75 17.6875 4.83333 17.6875 5V27C17.6875 27.1667 17.6042 27.25 17.4375 27.25H15.5625C15.3958 27.25 15.3125 27.1667 15.3125 27V5C15.3125 4.83333 15.3958 4.75 15.5625 4.75Z" fill="${color ?? '#667994'}"/>
<path d="M6 14.8125H27C27.1667 14.8125 27.25 14.8958 27.25 15.0625V16.9375C27.25 17.1042 27.1667 17.1875 27 17.1875H6C5.83333 17.1875 5.75 17.1042 5.75 16.9375V15.0625C5.75 14.8958 5.83333 14.8125 6 14.8125Z" fill="${color ?? '#667994'}"/></svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledBooks = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.65338 2.02262H1.59786C1.1762 2.02262 0.833984 2.38716 0.833984 2.83814V17.5176C0.833984 17.9686 1.1762 18.3332 1.59786 18.3332H4.65338C5.07504 18.3332 5.41726 17.9686 5.41726 17.5176V2.83814C5.41726 2.38716 5.07504 2.02262 4.65338 2.02262Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M10.7644 2.02262H7.70889C7.28723 2.02262 6.94502 2.38716 6.94502 2.83814V17.5176C6.94502 17.9686 7.28723 18.3332 7.70889 18.3332H10.7644C11.1861 18.3332 11.5283 17.9686 11.5283 17.5176V2.83814C11.5283 2.38716 11.1861 2.02262 10.7644 2.02262Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M19.1457 16.5578L15.9099 2.29093C15.8106 1.85299 15.3943 1.58142 14.9879 1.6907L12.0179 2.45729C11.8209 2.50786 11.6513 2.64079 11.5459 2.8251C11.4404 3.00941 11.4076 3.23123 11.455 3.44163L14.69 17.7093C14.7373 17.9197 14.8619 18.1007 15.0345 18.2133C15.1567 18.2924 15.2942 18.3332 15.4332 18.3332C15.4936 18.3332 15.5539 18.325 15.6127 18.3103L18.5827 17.5429C18.9929 17.4361 19.245 16.9957 19.1457 16.5578Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledUser = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.308 11.4047C11.6167 11.8276 10.8393 12.0832 10.0007 12.0832C9.16198 12.0832 8.38465 11.8276 7.69332 11.4047C5.26332 11.5707 3.33398 13.6776 3.33398 16.2498V17.4512L3.79732 17.6061C3.88665 17.6353 6.02598 18.3332 10.0007 18.3332C13.9753 18.3332 16.1147 17.6353 16.204 17.6061L16.6673 17.4512V16.2498C16.6673 13.6776 14.738 11.5707 12.308 11.4047Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M10.0007 10.6943C12.2533 10.6943 14.0007 8.08109 14.0007 5.83317C14.0007 3.53525 12.2067 1.6665 10.0007 1.6665C7.79465 1.6665 6.00065 3.53525 6.00065 5.83317C6.00065 8.08109 7.74798 10.6943 10.0007 10.6943Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineUser = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.33398 16.2498C3.33398 13.5649 5.42313 11.3887 8.00065 11.3887H12.0007C14.5782 11.3887 16.6673 13.5649 16.6673 16.2498V17.4512L16.2038 17.6057L16.0007 16.9443C16.2038 17.6057 16.2038 17.6057 16.2038 17.6057L16.2021 17.6063L16.1996 17.6071L16.1923 17.6095L16.1685 17.617C16.1486 17.6233 16.1206 17.6319 16.0845 17.6425C16.0123 17.6637 15.9079 17.6929 15.7718 17.7275C15.4994 17.7966 15.1002 17.887 14.5779 17.9767C13.5333 18.1562 11.997 18.3332 10.0007 18.3332C8.00435 18.3332 6.46805 18.1562 5.4234 17.9767C4.90113 17.887 4.50186 17.7966 4.22955 17.7275C4.0934 17.6929 3.98898 17.6637 3.91681 17.6425C3.88072 17.6319 3.85269 17.6233 3.83278 17.617L3.80901 17.6095L3.80171 17.6071L3.7992 17.6063L3.79824 17.6059C3.79824 17.6059 3.79747 17.6057 4.00065 16.9443L3.79747 17.6057L3.33398 17.4512V16.2498ZM4.66732 16.4083C4.90346 16.4652 5.22859 16.5356 5.6404 16.6063C6.61659 16.774 8.08029 16.9443 10.0007 16.9443C11.921 16.9443 13.3847 16.774 14.3609 16.6063C14.7727 16.5356 15.0978 16.4652 15.334 16.4083V16.2498C15.334 14.332 13.8418 12.7776 12.0007 12.7776H8.00065C6.15951 12.7776 4.66732 14.332 4.66732 16.2498V16.4083Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0007 3.05539C8.52817 3.05539 7.33398 4.29934 7.33398 5.83317C7.33398 6.61913 7.6463 7.51296 8.1685 8.20721C8.69271 8.90413 9.35133 9.30539 10.0007 9.30539C10.65 9.30539 11.3086 8.90413 11.8328 8.20721C12.355 7.51296 12.6673 6.61913 12.6673 5.83317C12.6673 4.29934 11.4731 3.05539 10.0007 3.05539ZM6.00065 5.83317C6.00065 3.53228 7.79179 1.6665 10.0007 1.6665C12.2095 1.6665 14.0007 3.53228 14.0007 5.83317C14.0007 6.96458 13.5666 8.15408 12.8823 9.06382C12.2 9.97089 11.192 10.6943 10.0007 10.6943C8.8093 10.6943 7.80125 9.97089 7.11896 9.06382C6.43467 8.15408 6.00065 6.96458 6.00065 5.83317Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineUserProfile = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.74963 13.1359C3.75531 13.1408 3.7611 13.1456 3.76698 13.1502C4.91836 14.0978 6.39306 14.6668 8.00065 14.6668C9.60696 14.6668 11.0806 14.0987 12.2316 13.1525L12.2345 13.1502C12.2412 13.1449 12.2478 13.1394 12.2543 13.1337C13.7284 11.9109 14.6673 10.0652 14.6673 8.00016C14.6673 4.31826 11.6825 1.3335 8.00065 1.3335C4.31875 1.3335 1.33398 4.31826 1.33398 8.00016C1.33398 10.0664 2.27396 11.913 3.74963 13.1359ZM2.4451 8.00016C2.4451 4.93191 4.9324 2.44461 8.00065 2.44461C11.0689 2.44461 13.5562 4.93191 13.5562 8.00016C13.5562 9.33434 13.0859 10.5587 12.302 11.5164C12.1222 11.0401 11.7811 10.6317 11.325 10.3711L10.1837 9.718C10.5562 9.24529 10.7784 8.64867 10.7784 8.00016V6.88905C10.7784 5.355 9.5347 4.11127 8.00065 4.11127C6.4666 4.11127 5.22287 5.355 5.22287 6.88905V8.00016C5.22287 8.64815 5.44479 9.24434 5.81676 9.71687L4.67577 10.3713C4.21982 10.6319 3.87885 11.0401 3.69917 11.5163C2.91536 10.5586 2.4451 9.33429 2.4451 8.00016ZM11.334 12.445C10.4055 13.1425 9.25132 13.5557 8.00065 13.5557C6.74998 13.5557 5.59583 13.1424 4.66732 12.445V12.3007C4.66732 11.9017 4.88102 11.5336 5.22728 11.3359L6.73282 10.4724C7.11292 10.6677 7.54392 10.7779 8.00065 10.7779C8.4566 10.7779 8.8869 10.6681 9.26652 10.4734L10.774 11.336C11.1203 11.5337 11.334 11.9018 11.334 12.3008V12.445ZM8.00065 5.22238C7.08025 5.22238 6.33398 5.96865 6.33398 6.88905V8.00016C6.33398 8.5676 6.61763 9.06885 7.05085 9.36985L7.05746 9.37441C7.32567 9.55886 7.65057 9.66683 8.00065 9.66683C8.34596 9.66683 8.66677 9.56179 8.93285 9.38191C8.94116 9.37583 8.94963 9.36999 8.95824 9.3644C9.38707 9.06282 9.66732 8.56419 9.66732 8.00016V6.88905C9.66732 5.96865 8.92105 5.22238 8.00065 5.22238Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineStar = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.00065 0.333496C7.23136 0.333496 7.44205 0.46448 7.54414 0.671368L9.27586 4.18075L13.1484 4.74342C13.3767 4.77659 13.5663 4.93649 13.6376 5.15589C13.7089 5.37529 13.6495 5.61613 13.4843 5.77716L10.6821 8.5087L11.3434 12.3656C11.3824 12.593 11.2889 12.8228 11.1023 12.9584C10.9156 13.0939 10.6682 13.1118 10.464 13.0044L7.00065 11.1831L3.5373 13.0044C3.33311 13.1118 3.08567 13.0939 2.89902 12.9584C2.71236 12.8228 2.61888 12.593 2.65786 12.3656L3.31917 8.5087L0.517016 5.77716C0.351825 5.61613 0.292377 5.37529 0.363668 5.15589C0.434959 4.93649 0.624624 4.77659 0.852918 4.74342L4.72544 4.18075L6.45716 0.671368C6.55925 0.46448 6.76995 0.333496 7.00065 0.333496ZM7.00065 2.30915L5.67142 5.00288C5.58314 5.18178 5.41249 5.30577 5.21507 5.33446L2.24244 5.76637L4.3934 7.86313C4.53623 8.00236 4.60141 8.20294 4.5677 8.39953L4.0601 11.36L6.71856 9.96191C6.89516 9.86904 7.10615 9.86904 7.28274 9.96191L9.94121 11.36L9.4336 8.39953C9.3999 8.20294 9.46508 8.00236 9.6079 7.86313L11.7589 5.76637L8.78623 5.33446C8.58881 5.30577 8.41816 5.18178 8.32988 5.00288L7.00065 2.30915Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineShoppingCart = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.39065 1.6665C1.99045 1.6665 1.66602 1.99094 1.66602 2.39114C1.66602 2.79135 1.99045 3.11578 2.39065 3.11578H3.93013L5.29556 13.3565C5.34356 13.7165 5.65065 13.9853 6.01384 13.9853H16.1588C16.5042 13.9853 16.8016 13.7415 16.8693 13.4028L18.3186 6.15644C18.3612 5.94356 18.3061 5.72281 18.1684 5.55492C18.0308 5.38703 17.8251 5.28969 17.608 5.28969H5.68209L5.28285 2.29537C5.23485 1.93537 4.92776 1.6665 4.56457 1.6665H2.39065ZM6.64827 12.5361L5.87533 6.73897H16.7241L15.5647 12.5361H6.64827Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M6.01384 15.4346C5.21343 15.4346 4.56456 16.0835 4.56456 16.8839C4.56456 17.6843 5.21343 18.3332 6.01384 18.3332C6.81425 18.3332 7.46311 17.6843 7.46311 16.8839C7.46311 16.0835 6.81425 15.4346 6.01384 15.4346Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M14.7095 16.8839C14.7095 16.0835 15.3584 15.4346 16.1588 15.4346C16.9592 15.4346 17.608 16.0835 17.608 16.8839C17.608 17.6843 16.9592 18.3332 16.1588 18.3332C15.3584 18.3332 14.7095 17.6843 14.7095 16.8839Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineVideoPlaylist = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.00065 5.33333C2.85331 5.33333 2.712 5.39187 2.60781 5.49605C2.50363 5.60024 2.4451 5.74155 2.4451 5.88889V12.5556C2.4451 12.7029 2.50363 12.8442 2.60781 12.9484C2.712 13.0526 2.85331 13.1111 3.00065 13.1111H13.0007C13.148 13.1111 13.2893 13.0526 13.3935 12.9484C13.4977 12.8442 13.5562 12.7029 13.5562 12.5556V5.88889C13.5562 5.74155 13.4977 5.60024 13.3935 5.49605C13.2893 5.39187 13.148 5.33333 13.0007 5.33333H3.00065ZM3.00065 4.22222H13.0007C13.4427 4.22222 13.8666 4.39782 14.1792 4.71038C14.4917 5.02294 14.6673 5.44686 14.6673 5.88889V12.5556C14.6673 12.9976 14.4917 13.4215 14.1792 13.7341C13.8666 14.0466 13.4427 14.2222 13.0007 14.2222H3.00065C2.55862 14.2222 2.1347 14.0466 1.82214 13.7341C1.50958 13.4215 1.33398 12.9976 1.33398 12.5556V5.88889C1.33398 5.44686 1.50958 5.02294 1.82214 4.71038C2.1347 4.39782 2.55862 4.22222 3.00065 4.22222Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.61582 7.07211C6.78999 6.9735 7.00374 6.97619 7.17537 7.07917L9.95315 8.74584C10.1205 8.84624 10.2229 9.02708 10.2229 9.22222C10.2229 9.41737 10.1205 9.59821 9.95315 9.69861L7.17537 11.3653C7.00374 11.4683 6.78999 11.4709 6.61582 11.3723C6.44165 11.2737 6.33398 11.089 6.33398 10.8889V7.55556C6.33398 7.3554 6.44165 7.17073 6.61582 7.07211ZM7.4451 8.53677V9.90767L8.58751 9.22222L7.4451 8.53677Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.55621 2.55556C3.55621 2.24873 3.80494 2 4.11176 2H11.8895C12.1964 2 12.4451 2.24873 12.4451 2.55556C12.4451 2.86238 12.1964 3.11111 11.8895 3.11111H4.11176C3.80494 3.11111 3.55621 2.86238 3.55621 2.55556Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineBooks = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.2799 13.2603L9.17863 8.354V13.1376C9.17863 13.4471 8.92776 13.698 8.6183 13.698H1.89432C1.58485 13.698 1.33398 13.4471 1.33398 13.1376V3.05168C1.33398 2.74222 1.58485 2.49135 1.89432 2.49135H8.6183C8.76373 2.49135 8.89621 2.54675 8.99581 2.6376L11.7753 2.01372C12.0773 1.94595 12.377 2.13578 12.4448 2.43773L13.9097 8.9642C13.9129 8.97525 13.9157 8.98647 13.9183 8.99784C13.9209 9.00927 13.9231 9.02069 13.9249 9.0321L14.6536 12.2786C14.7214 12.5805 14.5315 12.8803 14.2296 12.948L10.9493 13.6843C10.6474 13.7521 10.3476 13.5623 10.2799 13.2603ZM12.7012 8.69692L11.474 3.22987L9.28719 3.72072L10.5144 9.18806L12.7012 8.69692ZM10.7598 10.2815L12.9466 9.79038L13.4375 11.9773L11.2506 12.4682L10.7598 10.2815ZM4.69597 12.5773V10.336H2.45465V12.5773H4.69597ZM4.69597 9.21533V3.61201H2.45465V9.21533H4.69597ZM5.81664 12.5773H8.05796V10.336H5.81664V12.5773ZM5.81664 9.21533H8.05796V3.61201H5.81664V9.21533Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineGChart = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.33398 9.33317C1.33398 8.99845 1.60533 8.72711 1.94004 8.72711H4.36429C4.69901 8.72711 4.97035 8.99845 4.97035 9.33317V12.9695C4.97035 13.3043 4.69901 13.5756 4.36429 13.5756H1.94004C1.60533 13.5756 1.33398 13.3043 1.33398 12.9695V9.33317ZM2.54611 9.93923V12.3635H3.75823V9.93923H2.54611Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.18247 6.30287C6.18247 5.96815 6.45381 5.69681 6.78853 5.69681H9.21277C9.54749 5.69681 9.81883 5.96815 9.81883 6.30287V12.9695C9.81883 13.3043 9.54749 13.5756 9.21277 13.5756H6.78853C6.45381 13.5756 6.18247 13.3043 6.18247 12.9695V6.30287ZM7.39459 6.90893V12.3635H8.60671V6.90893H7.39459Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.031 3.27256C11.031 2.93785 11.3023 2.6665 11.637 2.6665H14.0613C14.396 2.6665 14.6673 2.93785 14.6673 3.27256V12.9695C14.6673 13.3043 14.396 13.5756 14.0613 13.5756H11.637C11.3023 13.5756 11.031 13.3043 11.031 12.9695V3.27256ZM12.2431 3.87863V12.3635H13.4552V3.87863H12.2431Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineCalendarDate = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.11176 7.44461C3.80494 7.44461 3.55621 7.69334 3.55621 8.00016V8.55572C3.55621 8.86254 3.80494 9.11127 4.11176 9.11127H5.22287C5.5297 9.11127 5.77843 8.86254 5.77843 8.55572V8.00016C5.77843 7.69334 5.5297 7.44461 5.22287 7.44461H4.11176Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M7.4451 7.44461C7.13827 7.44461 6.88954 7.69334 6.88954 8.00016V8.55572C6.88954 8.86254 7.13827 9.11127 7.4451 9.11127H8.55621C8.86303 9.11127 9.11176 8.86254 9.11176 8.55572V8.00016C9.11176 7.69334 8.86303 7.44461 8.55621 7.44461H7.4451Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M3.55621 10.7779C3.55621 10.4711 3.80494 10.2224 4.11176 10.2224H5.22287C5.5297 10.2224 5.77843 10.4711 5.77843 10.7779V11.3335C5.77843 11.6403 5.5297 11.8891 5.22287 11.8891H4.11176C3.80494 11.8891 3.55621 11.6403 3.55621 11.3335V10.7779Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M7.4451 10.2224C7.13827 10.2224 6.88954 10.4711 6.88954 10.7779V11.3335C6.88954 11.6403 7.13827 11.8891 7.4451 11.8891H8.55621C8.86303 11.8891 9.11176 11.6403 9.11176 11.3335V10.7779C9.11176 10.4711 8.86303 10.2224 8.55621 10.2224H7.4451Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M10.2229 8.00016C10.2229 7.69334 10.4716 7.44461 10.7784 7.44461H11.8895C12.1964 7.44461 12.4451 7.69334 12.4451 8.00016V8.55572C12.4451 8.86254 12.1964 9.11127 11.8895 9.11127H10.7784C10.4716 9.11127 10.2229 8.86254 10.2229 8.55572V8.00016Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.22287 1.88905C5.22287 1.58223 4.97414 1.3335 4.66732 1.3335C4.36049 1.3335 4.11176 1.58223 4.11176 1.88905V2.44461H3.00065C2.08018 2.44461 1.33398 3.1908 1.33398 4.11127V12.4446C1.33398 13.3651 2.08018 14.1113 3.00065 14.1113H13.0007C13.9211 14.1113 14.6673 13.3651 14.6673 12.4446V4.11127C14.6673 3.1908 13.9211 2.44461 13.0007 2.44461H11.8895V1.88905C11.8895 1.58223 11.6408 1.3335 11.334 1.3335C11.0272 1.3335 10.7784 1.58223 10.7784 1.88905V2.44461H5.22287V1.88905ZM3.00065 3.55572C2.69383 3.55572 2.4451 3.80445 2.4451 4.11127V5.22238H13.5562V4.11127C13.5562 3.80445 13.3075 3.55572 13.0007 3.55572H11.8895C11.8895 3.86254 11.6408 4.11127 11.334 4.11127C11.0272 4.11127 10.7784 3.86254 10.7784 3.55572H5.22287C5.22287 3.86254 4.97414 4.11127 4.66732 4.11127C4.36049 4.11127 4.11176 3.86254 4.11176 3.55572H3.00065ZM3.00065 13.0002C2.69383 13.0002 2.4451 12.7514 2.4451 12.4446V6.3335H13.5562V12.4446C13.5562 12.7514 13.3075 13.0002 13.0007 13.0002H3.00065Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineLocation = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.88341 2.49292C5.88426 2.49292 3.82544 3.97 3.82544 6.55089C3.82544 7.1218 4.06473 7.84829 4.48515 8.66023C4.89906 9.45959 5.45758 10.2837 6.02649 11.0339C6.59387 11.7822 7.16265 12.4453 7.59027 12.9219C7.69787 13.0418 7.79635 13.1498 7.88341 13.244C7.97047 13.1498 8.06895 13.0418 8.17655 12.9219C8.60416 12.4453 9.17295 11.7822 9.74032 11.0339C10.3092 10.2837 10.8678 9.45959 11.2817 8.66023C11.7021 7.84829 11.9414 7.1218 11.9414 6.55089C11.9414 3.97 9.88255 2.49292 7.88341 2.49292ZM7.88341 14.0871C7.47095 14.4945 7.47087 14.4944 7.47078 14.4943L7.46662 14.4901L7.45568 14.4789C7.44623 14.4693 7.43252 14.4552 7.41482 14.4369C7.37944 14.4004 7.32813 14.347 7.26316 14.2784C7.13325 14.1411 6.94854 13.9428 6.72727 13.6962C6.28532 13.2036 5.69469 12.5152 5.10264 11.7345C4.51213 10.9558 3.91123 10.0733 3.45557 9.19335C3.00643 8.32594 2.66602 7.40027 2.66602 6.55089C2.66602 3.21873 5.36081 1.3335 7.88341 1.3335C10.406 1.3335 13.1008 3.21873 13.1008 6.55089C13.1008 7.40027 12.7604 8.32594 12.3112 9.19335C11.8556 10.0733 11.2547 10.9558 10.6642 11.7345C10.0721 12.5152 9.48149 13.2036 9.03954 13.6962C8.81827 13.9428 8.63357 14.1411 8.50365 14.2784C8.43868 14.347 8.38737 14.4004 8.35199 14.4369C8.3343 14.4552 8.32059 14.4693 8.31114 14.4789L8.3002 14.4901L8.29632 14.494C8.29622 14.4941 8.29586 14.4945 7.88341 14.0871ZM7.88341 14.0871L8.29632 14.494C8.1874 14.6043 8.0384 14.6668 7.88341 14.6668C7.72841 14.6668 7.57969 14.6046 7.47078 14.4943L7.88341 14.0871Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.88341 5.39147C7.24308 5.39147 6.72399 5.91056 6.72399 6.55089C6.72399 7.19122 7.24308 7.71031 7.88341 7.71031C8.52374 7.71031 9.04283 7.19122 9.04283 6.55089C9.04283 5.91056 8.52374 5.39147 7.88341 5.39147ZM5.56457 6.55089C5.56457 5.27023 6.60275 4.23205 7.88341 4.23205C9.16407 4.23205 10.2022 5.27023 10.2022 6.55089C10.2022 7.83155 9.16407 8.86973 7.88341 8.86973C6.60275 8.86973 5.56457 7.83155 5.56457 6.55089Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineFilesCopy = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4 7.63588C4 7.08359 4.44772 6.63588 5 6.63588H15.9091C16.4614 6.63588 16.9091 7.08359 16.9091 7.63588V20.3631C16.9091 20.9154 16.4614 21.3631 15.9091 21.3631H5C4.44772 21.3631 4 20.9154 4 20.3631V7.63588ZM6 8.63588V19.3631H14.9091V8.63588H6Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.63636 3.99951C7.63636 3.44723 8.08408 2.99951 8.63636 2.99951H19.5455C20.0977 2.99951 20.5455 3.44723 20.5455 3.99951V16.7268C20.5455 17.2791 20.0977 17.7268 19.5455 17.7268C18.9932 17.7268 18.5455 17.2791 18.5455 16.7268V4.99951H8.63636C8.08408 4.99951 7.63636 4.5518 7.63636 3.99951Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineFilter = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.70923 3.88333L8.33423 9.16667V17.5L11.6676 15.8333V9.16667L16.2926 3.88333C16.3983 3.76302 16.4671 3.6148 16.4907 3.45641C16.5144 3.29802 16.4919 3.13616 16.4259 2.99022C16.36 2.84428 16.2534 2.72043 16.1189 2.63349C15.9844 2.54655 15.8277 2.5002 15.6676 2.5H4.33423C4.17409 2.5002 4.01739 2.54655 3.88289 2.63349C3.7484 2.72043 3.64181 2.84428 3.57587 2.99022C3.50993 3.13616 3.48744 3.29802 3.51109 3.45641C3.53474 3.6148 3.60354 3.76302 3.70923 3.88333Z" stroke="${color ?? '#667994'}" stroke-opacity="${opacity}" stroke-linecap="round" stroke-linejoin="round" />
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledCamera = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.8125 3.55469H9.95312L9.51016 2.31328C9.47955 2.22834 9.42347 2.15491 9.34957 2.10303C9.27567 2.05116 9.18756 2.02336 9.09727 2.02344H4.90273C4.71816 2.02344 4.55273 2.13965 4.49121 2.31328L4.04688 3.55469H2.1875C1.5832 3.55469 1.09375 4.04414 1.09375 4.64844V10.8828C1.09375 11.4871 1.5832 11.9766 2.1875 11.9766H11.8125C12.4168 11.9766 12.9062 11.4871 12.9062 10.8828V4.64844C12.9062 4.04414 12.4168 3.55469 11.8125 3.55469ZM7 9.78906C5.79141 9.78906 4.8125 8.81016 4.8125 7.60156C4.8125 6.39297 5.79141 5.41406 7 5.41406C8.20859 5.41406 9.1875 6.39297 9.1875 7.60156C9.1875 8.81016 8.20859 9.78906 7 9.78906ZM5.6875 7.60156C5.6875 7.94966 5.82578 8.2835 6.07192 8.52964C6.31806 8.77578 6.6519 8.91406 7 8.91406C7.3481 8.91406 7.68194 8.77578 7.92808 8.52964C8.17422 8.2835 8.3125 7.94966 8.3125 7.60156C8.3125 7.25347 8.17422 6.91963 7.92808 6.67348C7.68194 6.42734 7.3481 6.28906 7 6.28906C6.6519 6.28906 6.31806 6.42734 6.07192 6.67348C5.82578 6.91963 5.6875 7.25347 5.6875 7.60156Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledGoldCoin = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.0003 1.6665C8.35215 1.6665 6.74099 2.15525 5.37058 3.07092C4.00017 3.9866 2.93206 5.28809 2.30133 6.81081C1.6706 8.33352 1.50558 10.0091 1.82712 11.6256C2.14866 13.2421 2.94233 14.727 4.10777 15.8924C5.27321 17.0578 6.75807 17.8515 8.37458 18.173C9.99108 18.4946 11.6666 18.3296 13.1894 17.6988C14.7121 17.0681 16.0136 16 16.9292 14.6296C17.8449 13.2592 18.3337 11.648 18.3337 9.99984C18.3337 7.7897 17.4557 5.67008 15.8929 4.10728C14.3301 2.54448 12.2105 1.6665 10.0003 1.6665V1.6665ZM12.2955 5.65886L13.1121 4.53525C13.1658 4.46143 13.2334 4.39889 13.3112 4.35121C13.389 4.30353 13.4754 4.27163 13.5655 4.25735C13.6556 4.24307 13.7477 4.24667 13.8364 4.26796C13.9251 4.28925 14.0088 4.32781 14.0826 4.38143C14.1564 4.43506 14.219 4.5027 14.2667 4.58049C14.3143 4.65828 14.3462 4.7447 14.3605 4.83482C14.3748 4.92494 14.3712 5.01699 14.3499 5.10571C14.3286 5.19443 14.2901 5.2781 14.2364 5.35192L13.4198 6.47553C13.3115 6.62462 13.1484 6.72459 12.9664 6.75343C12.7844 6.78228 12.5984 6.73765 12.4493 6.62935C12.3002 6.52105 12.2002 6.35797 12.1714 6.17596C12.1425 5.99396 12.1872 5.80796 12.2955 5.65886ZM5.91838 4.38178C5.99222 4.32782 6.076 4.28898 6.16489 4.26749C6.25378 4.246 6.34604 4.24227 6.43638 4.25653C6.52671 4.27079 6.61334 4.30276 6.69129 4.35059C6.76924 4.39842 6.83698 4.46117 6.89061 4.53525L7.70866 5.65886C7.81696 5.80842 7.86141 5.99486 7.83224 6.17719C7.80306 6.35952 7.70266 6.52279 7.55311 6.63109C7.40355 6.73938 7.2171 6.78383 7.03478 6.75466C6.85245 6.72549 6.68918 6.62508 6.58088 6.47553L5.76422 5.35192C5.71062 5.2781 5.67209 5.19445 5.65083 5.10574C5.62957 5.01703 5.626 4.925 5.64031 4.83491C5.65463 4.74482 5.68656 4.65843 5.73427 4.58068C5.78198 4.50293 5.84454 4.43535 5.91838 4.38178ZM5.59199 12.1596L4.27255 12.5887C4.18482 12.6214 4.09139 12.636 3.99788 12.6316C3.90437 12.6272 3.8127 12.604 3.7284 12.5633C3.6441 12.5226 3.56889 12.4653 3.50731 12.3947C3.44572 12.3242 3.39903 12.242 3.37003 12.153C3.34103 12.064 3.33033 11.97 3.33857 11.8768C3.3468 11.7835 3.37381 11.6929 3.41796 11.6104C3.46211 11.5278 3.52249 11.4551 3.59548 11.3964C3.66847 11.3378 3.75256 11.2946 3.84269 11.2693L5.16213 10.8401C5.24996 10.8071 5.34357 10.7923 5.4373 10.7964C5.53103 10.8006 5.62294 10.8237 5.70748 10.8644C5.79203 10.9051 5.86745 10.9625 5.92921 11.0331C5.99096 11.1037 6.03776 11.1862 6.06678 11.2754C6.0958 11.3646 6.10644 11.4588 6.09806 11.5522C6.08967 11.6457 6.06244 11.7365 6.01801 11.8191C5.97357 11.9017 5.91285 11.9745 5.83951 12.033C5.76617 12.0915 5.68174 12.1346 5.5913 12.1596H5.59199ZM10.6948 16.2498C10.6948 16.434 10.6216 16.6106 10.4914 16.7409C10.3611 16.8711 10.1845 16.9443 10.0003 16.9443C9.81615 16.9443 9.63951 16.8711 9.50928 16.7409C9.37905 16.6106 9.30588 16.434 9.30588 16.2498V14.8609C9.30588 14.6768 9.37905 14.5001 9.50928 14.3699C9.63951 14.2397 9.81615 14.1665 10.0003 14.1665C10.1845 14.1665 10.3611 14.2397 10.4914 14.3699C10.6216 14.5001 10.6948 14.6768 10.6948 14.8609V16.2498ZM10.0003 12.2221L7.38644 13.5971L7.88574 10.6866L5.77116 8.62553L8.69338 8.20122L10.0003 5.55539L11.3073 8.20122L14.2295 8.62553L12.1149 10.6866L12.6142 13.5971L10.0003 12.2221ZM15.7295 12.5901L14.41 12.1609C14.3196 12.136 14.2352 12.0929 14.1618 12.0344C14.0885 11.9759 14.0278 11.9031 13.9833 11.8205C13.9389 11.7378 13.9117 11.6471 13.9033 11.5536C13.8949 11.4602 13.9055 11.366 13.9346 11.2768C13.9636 11.1876 14.0104 11.1051 14.0721 11.0345C14.1339 10.9639 14.2093 10.9065 14.2939 10.8658C14.3784 10.8251 14.4703 10.802 14.5641 10.7978C14.6578 10.7936 14.7514 10.8085 14.8392 10.8415L16.1587 11.2707C16.2488 11.296 16.3329 11.3392 16.4059 11.3978C16.4789 11.4565 16.5392 11.5292 16.5834 11.6118C16.6275 11.6943 16.6545 11.7849 16.6628 11.8782C16.671 11.9714 16.6603 12.0654 16.6313 12.1544C16.6023 12.2434 16.5556 12.3256 16.494 12.3961C16.4325 12.4666 16.3573 12.524 16.2729 12.5647C16.1886 12.6054 16.097 12.6286 16.0035 12.633C15.91 12.6373 15.8165 12.6228 15.7288 12.5901H15.7295Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledMail = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.667 3.3335H3.33366C2.41699 3.3335 1.67533 4.0835 1.67533 5.00016L1.66699 15.0002C1.66699 15.9168 2.41699 16.6668 3.33366 16.6668H16.667C17.5837 16.6668 18.3337 15.9168 18.3337 15.0002V5.00016C18.3337 4.0835 17.5837 3.3335 16.667 3.3335ZM16.667 6.66683L10.0003 10.8335L3.33366 6.66683V5.00016L10.0003 9.16683L16.667 5.00016V6.66683Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledDocument = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.333 13.4721C11.333 13.2879 11.4032 13.1112 11.5283 12.981C11.6533 12.8508 11.8229 12.7776 11.9997 12.7776H16.6663V2.36095C16.6663 2.17677 16.5961 2.00014 16.4711 1.8699C16.3461 1.73967 16.1765 1.6665 15.9997 1.6665H3.99967C3.82286 1.6665 3.65329 1.73967 3.52827 1.8699C3.40325 2.00014 3.33301 2.17677 3.33301 2.36095V17.6387C3.33301 17.8229 3.40325 17.9995 3.52827 18.1298C3.65329 18.26 3.82286 18.3332 3.99967 18.3332H11.333V13.4721ZM5.33301 5.13873H14.6663V6.52762H5.33301V5.13873ZM5.33301 8.61095H14.6663V9.99984H5.33301V8.61095ZM9.33301 13.4721H5.33301V12.0832H9.33301V13.4721Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M16.2757 14.1665H12.6663V17.9262L16.2757 14.1665Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledRemove = ({ size, color }: { size?: number, color?: string }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C10.0222 2 8.08879 2.58649 6.4443 3.6853C4.79981 4.78412 3.51809 6.3459 2.76121 8.17316C2.00433 10.0004 1.8063 12.0111 2.19215 13.9509C2.578 15.8907 3.53041 17.6725 4.92894 19.0711C6.32746 20.4696 8.10929 21.422 10.0491 21.8078C11.9889 22.1937 13.9996 21.9957 15.8268 21.2388C17.6541 20.4819 19.2159 19.2002 20.3147 17.5557C21.4135 15.9112 22 13.9778 22 12C21.9923 9.3502 20.9363 6.81113 19.0626 4.93743C17.1889 3.06373 14.6498 2.00769 12 2V2ZM16.125 14.9467L14.9467 16.125L12 13.1783L9.05334 16.125L7.875 14.9467L10.8217 12L7.875 9.05333L9.05334 7.875L12 10.8217L14.9467 7.875L16.125 9.05333L13.1783 12L16.125 14.9467Z" fill="${color ?? '#667994'}" style="mix-blend-mode:multiply"/></svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledBell = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.8353 10.3344V8.6675C18.8353 6.89917 18.1329 5.20327 16.8825 3.95287C15.6321 2.70247 13.9361 2 12.1678 2C10.3995 2 8.70358 2.70247 7.45318 3.95287C6.20278 5.20327 5.50031 6.89917 5.50031 8.6675V10.3344C5.47985 10.8427 5.35479 11.3414 5.13295 11.7993C4.91111 12.2572 4.59726 12.6644 4.21099 12.9955C3.51689 13.587 3.08216 14.4268 3 15.335C3 17.4186 6.4271 18.6688 12.1678 18.6688C17.9085 18.6688 21.3356 17.4186 21.3356 15.335C21.2535 14.4268 20.8187 13.587 20.1246 12.9955C19.7384 12.6644 19.4245 12.2572 19.2027 11.7993C18.9808 11.3414 18.8558 10.8427 18.8353 10.3344Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M9.78835 20.2523C9.9472 20.759 10.2637 21.2018 10.6916 21.5161C11.1196 21.8305 11.6368 22 12.1678 22C12.6988 22 13.216 21.8305 13.644 21.5161C14.072 21.2018 14.3884 20.759 14.5473 20.2523C13.8022 20.3073 13.0104 20.3356 12.1678 20.3356C11.3252 20.3356 10.5334 20.309 9.78835 20.2523Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledPhone = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.3085 14.185L13.1676 16.295C12.0515 15.6216 11.0147 14.8248 10.0767 13.9196C9.17321 12.9804 8.37682 11.9438 7.70224 10.8287L9.81039 8.68784C9.937 8.55838 10.0225 8.39437 10.0562 8.21644C10.0898 8.03851 10.0701 7.8546 9.99948 7.68785L7.80224 2.55521C7.71561 2.35356 7.55956 2.18966 7.3624 2.09325C7.16525 1.99684 6.94006 1.97431 6.72771 2.02976L2.68777 3.09611C2.48592 3.14794 2.30779 3.26709 2.18283 3.43387C2.05786 3.60065 1.99355 3.80508 2.00051 4.01336C2.25722 8.70471 4.20798 13.1434 7.49042 16.505C10.8527 19.7888 15.2929 21.7402 19.9857 21.9967C20.194 22.0032 20.3983 21.9388 20.5651 21.8139C20.7319 21.689 20.8514 21.5111 20.9039 21.3095L21.9693 17.2732C22.0258 17.0604 22.0039 16.8344 21.9076 16.6365C21.8113 16.4386 21.647 16.2819 21.4448 16.195L16.3166 13.9968C16.149 13.9242 15.9634 13.9033 15.7838 13.9369C15.6042 13.9704 15.4387 14.0568 15.3085 14.185V14.185Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledDirection = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_7881_9366)"><path d="M9.00037 9.99988C8.73516 9.99988 8.4808 10.1052 8.29327 10.2928C8.10573 10.4803 8.00037 10.7347 8.00037 10.9999V14.9999H10.0004V11.9999H13.0004V14.4999L16.5004 10.9999L13.0004 7.49988V9.99988H9.00037ZM12.7074 1.39288L22.6074 11.2929C22.7948 11.4804 22.9002 11.7347 22.9002 11.9999C22.9002 12.265 22.7948 12.5194 22.6074 12.7069L12.7074 22.6069C12.5198 22.7944 12.2655 22.8997 12.0004 22.8997C11.7352 22.8997 11.4809 22.7944 11.2934 22.6069L1.39337 12.7069C1.2059 12.5194 1.10059 12.265 1.10059 11.9999C1.10059 11.7347 1.2059 11.4804 1.39337 11.2929L11.2934 1.39288C11.4809 1.20541 11.7352 1.1001 12.0004 1.1001C12.2655 1.1001 12.5198 1.20541 12.7074 1.39288Z" fill="${color ?? '#667994'}"/></g><defs><clipPath id="clip0_7881_9366"><rect width="24" height="24" fill="${color ?? '#667994'}"/></clipPath></defs></svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledPitch = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.02223 2.62939H3.98106C3.28929 2.62939 2.73047 3.15881 2.73047 3.8141V8.36116H5.04106C5.34929 6.99057 6.56106 5.96116 8.01282 5.96116C9.46341 5.96116 10.6775 6.99057 10.9846 8.36116H13.2199V3.8141C13.2199 3.15881 12.6587 2.62939 11.9693 2.62939H10.9964V3.76116H5.02223V2.62939Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.97987 7.28027C7.22693 7.28027 6.58693 7.75086 6.33398 8.41204H9.6281C9.37516 7.75086 8.73634 7.28027 7.97987 7.28027Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.01154 10.72C8.7833 10.72 9.43036 10.2565 9.66566 9.61768H6.35742C6.59272 10.2565 7.23977 10.72 8.01154 10.72Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.01282 12.113C6.53635 12.113 5.30223 11.0506 5.02223 9.64355H2.73047V14.2365C2.73047 14.8894 3.29047 15.4212 3.98341 15.4212H4.98576V14.22H10.9411V15.4212H11.9869C12.6787 15.4212 13.2387 14.8894 13.2387 14.2365V9.64355H11.0034C10.7222 11.0494 9.48929 12.113 8.01282 12.113Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.9876 0.226074H0.939398C0.615869 0.226074 0.353516 0.487251 0.353516 0.808427V17.2414C0.353516 17.5625 0.615869 17.8237 0.939398 17.8237H14.9876C15.3112 17.8237 15.5735 17.5625 15.5735 17.2414V0.808427C15.5747 0.487251 15.3123 0.226074 14.9876 0.226074ZM14.4959 16.7202H9.74057V15.4708H6.2594V16.7202H1.45587V1.27902H6.2594V2.45549H9.70999V1.27902H14.4959V16.7202Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineEarthGlobal = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.39961 13.2001H15.5996M4.39961 6.8001H15.5996M3.59961 10.0001H16.3996M3.59961 10.0001C3.59961 13.5347 6.46499 16.4001 9.99961 16.4001M3.59961 10.0001C3.59961 6.46547 6.46499 3.6001 9.99961 3.6001M16.3996 10.0001C16.3996 13.5347 13.5342 16.4001 9.99961 16.4001M16.3996 10.0001C16.3996 6.46547 13.5342 3.6001 9.99961 3.6001M9.99961 16.4001C9.99961 16.4001 6.39961 14.8001 6.39961 10.0001C6.39961 5.2001 9.99961 3.6001 9.99961 3.6001M9.99961 16.4001C9.99961 16.4001 13.5996 14.8001 13.5996 10.0001C13.5996 5.2001 9.99961 3.6001 9.99961 3.6001M9.99961 16.4001V3.6001" stroke="${color ?? '#667994'}" stroke-opacity="${opacity}" stroke-width="0.7"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineClock = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10 1.25C5.16797 1.25 1.25 5.16797 1.25 10C1.25 14.832 5.16797 18.75 10 18.75C14.832 18.75 18.75 14.832 18.75 10C18.75 5.16797 14.832 1.25 10 1.25ZM10 17.2656C5.98828 17.2656 2.73438 14.0117 2.73438 10C2.73438 5.98828 5.98828 2.73438 10 2.73438C14.0117 2.73438 17.2656 5.98828 17.2656 10C17.2656 14.0117 14.0117 17.2656 10 17.2656Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M13.4121 12.4726L10.6269 10.459V5.625C10.6269 5.53906 10.5566 5.46875 10.4707 5.46875H9.53125C9.44531 5.46875 9.375 5.53906 9.375 5.625V11.0039C9.375 11.0547 9.39844 11.1015 9.43945 11.1308L12.6699 13.4863C12.7402 13.5371 12.8379 13.5215 12.8887 13.4531L13.4473 12.6914C13.498 12.6191 13.4824 12.5215 13.4121 12.4726V12.4726Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledBox = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.9804 18.8123C21.9911 18.8633 21.9975 18.9148 21.9994 18.9668L22 18.9999V19.9999C22 21.1044 19.9853 21.9999 17.5 21.9999C15.0396 21.9999 13.0403 21.1223 13.0006 20.0329L13 19.9999V18.9999L13.0006 18.9668C13.0025 18.9149 13.0088 18.8635 13.0195 18.8127C13.2951 19.0198 13.6306 19.2108 14.0194 19.3744C14.9268 19.7561 16.1504 19.9999 17.5 19.9999C18.9031 19.9999 20.1698 19.736 21.0881 19.3279C21.4314 19.1753 21.7301 19.0006 21.9804 18.8123ZM11 1.60986L20 6.60986L20.0001 10.3C19.2629 10.1107 18.4101 9.99986 17.5 9.99986C16.1294 9.99986 14.8889 10.2518 13.9737 10.6586C12.7206 11.2155 12.0826 12.0673 12.002 12.9292L11.9997 12.9554L12.0054 21.0513L11 21.6099L2 16.6099V6.60986L11 1.60986ZM21.9804 15.8123C21.9911 15.8633 21.9975 15.9148 21.9994 15.9668L22 15.9999V16.9999C22 18.1044 19.9853 18.9999 17.5 18.9999C15.0396 18.9999 13.0403 18.1223 13.0006 17.0329L13 16.9999V15.9999L13.0006 15.9668C13.0025 15.9149 13.0088 15.8635 13.0195 15.8127C13.2951 16.0198 13.6306 16.2108 14.0194 16.3744C14.9268 16.7561 16.1504 16.9999 17.5 16.9999C18.9031 16.9999 20.1698 16.736 21.0881 16.3279C21.4314 16.1753 21.7301 16.0006 21.9804 15.8123ZM7.99995 10.9431L8 17.7211L10 18.8322V12.0541L7.99995 10.9431ZM4 8.72089V15.4988L5.99998 16.6099V9.83197L4 8.72089ZM17.5 10.9999C19.9604 10.9999 21.9597 11.8775 21.9994 12.9668L22 12.9999V13.9999C22 15.1044 19.9853 15.9999 17.5 15.9999C15.0396 15.9999 13.0403 15.1223 13.0006 14.0329L13 13.9999V12.9999L13.0067 12.8901C13.1348 11.8366 15.0976 10.9999 17.5 10.9999ZM14.9371 6.01935L9.05056 9.30463L11 10.3876L16.9 7.10986L14.9371 6.01935ZM11 3.83209L5.1 7.10986L7.05289 8.19482L12.9394 4.90954L11 3.83209Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledPeople = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.1667 4C15.4126 4 14.7251 4.26 14.1667 4.68167C14.8959 5.655 15.3334 6.85917 15.3334 8.16667V9C15.3334 9.79417 15.1726 10.5517 14.8834 11.2425C15.2792 11.4075 15.7117 11.5 16.1667 11.5C18.0076 11.5 19.5001 10.0075 19.5001 8.16667V7.33333C19.5001 5.4925 18.0076 4 16.1667 4Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M9.5 13.1667C7.19917 13.1667 5.33333 11.3008 5.33333 9V8.16667C5.33333 5.86583 7.19917 4 9.5 4C11.8008 4 13.6667 5.86583 13.6667 8.16667V9C13.6667 11.3008 11.8008 13.1667 9.5 13.1667Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M21.0325 14.3375C19.9625 13.8458 18.1284 13.1667 16.1667 13.1667C15.1709 13.1667 14.2084 13.3425 13.3609 13.5792C13.6742 13.6442 13.9884 13.71 14.3025 13.79C16.7284 14.4083 18.4442 16.5192 18.6292 19H21.1667C21.6267 19 22 18.6267 22 18.1667V15.8508C22 15.1992 21.625 14.6092 21.0325 14.3375Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M16.1667 20.6667H2.83333C2.37333 20.6667 2 20.2933 2 19.8333V19.4375C2 17.5417 3.2725 15.8733 5.10917 15.405C6.3375 15.0917 7.85083 14.8333 9.5 14.8333C11.1492 14.8333 12.6625 15.0917 13.8908 15.405C15.7275 15.8733 17 17.5417 17 19.4375V19.8333C17 20.2933 16.6267 20.6667 16.1667 20.6667Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledChartBar = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.6667 12.8333H10.3333C9.87333 12.8333 9.5 13.2067 9.5 13.6667V21.1667C9.5 21.6267 9.87333 22 10.3333 22H13.6667C14.1267 22 14.5 21.6267 14.5 21.1667V13.6667C14.5 13.2067 14.1267 12.8333 13.6667 12.8333Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M6.16667 16.1667H2.83333C2.37333 16.1667 2 16.54 2 17V21.1667C2 21.6267 2.37333 22 2.83333 22H6.16667C6.62667 22 7 21.6267 7 21.1667V17C7 16.54 6.62667 16.1667 6.16667 16.1667Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M21.1667 9.5H17.8333C17.3733 9.5 17 9.87333 17 10.3333V21.1667C17 21.6267 17.3733 22 17.8333 22H21.1667C21.6267 22 22 21.6267 22 21.1667V10.3333C22 9.87333 21.6267 9.5 21.1667 9.5Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M12.5891 10.0892L17.8333 4.845V7.83333H19.5V2.83333C19.5 2.37333 19.1266 2 18.6666 2H13.6666V3.66667H16.655L12 8.32167L9.25581 5.5775C8.92997 5.25167 8.40331 5.25167 8.07747 5.5775L3.91081 9.74417L5.08914 10.9225L8.66664 7.345L11.4108 10.0892C11.7366 10.415 12.2633 10.415 12.5891 10.0892Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledHeadPhoneMic = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.4917 8.505C19.405 4.90417 16.455 2 12.8333 2H11.1667C7.545 2 4.595 4.90417 4.50833 8.505C3.01167 9.37 2 10.9842 2 12.8333C2 14.6792 3.00833 16.2908 4.5 17.1567V17.8333C4.5 20.1308 6.36917 22 8.66667 22H12C12.46 22 12.8333 21.6267 12.8333 21.1667C12.8333 20.7067 12.46 20.3333 12 20.3333H8.66667C7.28833 20.3333 6.16667 19.2117 6.16667 17.8333V17.7583C6.43833 17.8042 6.71583 17.8333 7 17.8333C7.33333 17.8333 7.67 17.7992 7.99833 17.7325C8.3875 17.6542 8.66667 17.3125 8.66667 16.9158V8.75083C8.66667 8.35417 8.3875 8.0125 7.99833 7.93417C7.67 7.8675 7.33333 7.83333 7 7.83333C6.73833 7.83333 6.48333 7.86 6.23167 7.89833C6.60333 5.50583 8.67167 3.66667 11.1667 3.66667H12.8333C15.3283 3.66667 17.3967 5.50583 17.7683 7.89833C17.5167 7.86 17.2617 7.83333 17 7.83333C16.6667 7.83333 16.33 7.8675 16.0017 7.93417C15.6125 8.0125 15.3333 8.35417 15.3333 8.75083V16.9167C15.3333 17.3133 15.6125 17.655 16.0017 17.7333C16.33 17.7992 16.6667 17.8333 17 17.8333C19.7575 17.8333 22 15.5908 22 12.8333C22 10.9842 20.9883 9.37 19.4917 8.505Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledLogout = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.6664 20.3322H5.66785V3.66785H15.6664V5.33428H17.3329V2.83463C17.3395 2.72347 17.3226 2.61216 17.2831 2.50804C17.2435 2.40392 17.1824 2.30937 17.1037 2.23062C17.0249 2.15188 16.9304 2.09073 16.8262 2.05122C16.7221 2.01171 16.6108 1.99474 16.4996 2.00142H4.83463C4.72347 1.99474 4.61216 2.01171 4.50804 2.05122C4.40392 2.09073 4.30937 2.15188 4.23062 2.23062C4.15188 2.30937 4.09073 2.40392 4.05122 2.50804C4.01171 2.61216 3.99474 2.72347 4.00142 2.83463V21.1654C3.99474 21.2765 4.01171 21.3878 4.05122 21.492C4.09073 21.5961 4.15188 21.6906 4.23062 21.7694C4.30937 21.8481 4.40392 21.9093 4.50804 21.9488C4.61216 21.9883 4.72347 22.0053 4.83463 21.9986H16.4996C16.6108 22.0053 16.7221 21.9883 16.8262 21.9488C16.9304 21.9093 17.0249 21.8481 17.1037 21.7694C17.1824 21.6906 17.2435 21.5961 17.2831 21.492C17.3226 21.3878 17.3395 21.2765 17.3329 21.1654V18.6657H15.6664V20.3322Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path d="M19.8323 12.0002L13.9998 7.00092V11.167H8.1673V12.8334H13.9998V16.9995L19.8323 12.0002Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineChat = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 11.3333C22 6.55739 17.3788 3 12 3C6.62117 3 2 6.55739 2 11.3333C2 13.2934 2.83348 15.0521 4.12726 16.4603L3.51091 21.802L9.46697 19.3792C10.303 19.596 11.132 19.6667 12 19.6667C17.3788 19.6667 22 16.1093 22 11.3333ZM12 4.66667C16.7878 4.66667 20.3333 7.77594 20.3333 11.3333C20.3333 14.8907 16.7878 18 12 18C11.1348 18 10.3877 17.9213 9.65612 17.7018L9.37476 17.6174L5.48909 19.198L5.874 15.8622L5.58926 15.5774C4.37683 14.365 3.66667 12.9002 3.66667 11.3333C3.66667 7.77594 7.21216 4.66667 12 4.66667Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineStack = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.7381 2.11876C12.002 1.96041 12.3317 1.96041 12.5956 2.11876L20.9289 7.11876C21.1799 7.26936 21.3335 7.54061 21.3335 7.83333C21.3335 8.12605 21.1799 8.39731 20.9289 8.54791L12.5956 13.5479C12.3317 13.7063 12.002 13.7063 11.7381 13.5479L3.40474 8.54791C3.15373 8.39731 3.00015 8.12605 3.00015 7.83333C3.00015 7.54061 3.15373 7.26936 3.40474 7.11876L11.7381 2.11876ZM5.45319 7.83333L12.1668 11.8615L18.8804 7.83333L12.1668 3.80516L5.45319 7.83333Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.11888 11.5713C3.35567 11.1766 3.86756 11.0487 4.26221 11.2854L12.1668 16.0282L20.0714 11.2854C20.466 11.0487 20.9779 11.1766 21.2147 11.5713C21.4515 11.9659 21.3235 12.4778 20.9289 12.7146L12.5955 17.7146C12.3316 17.8729 12.0019 17.8729 11.738 17.7146L3.40471 12.7146C3.01006 12.4778 2.88209 11.9659 3.11888 11.5713Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.11888 15.7379C3.35567 15.3433 3.86756 15.2153 4.26221 15.4521L12.1668 20.1949L20.0714 15.4521C20.466 15.2153 20.9779 15.3433 21.2147 15.7379C21.4515 16.1326 21.3235 16.6445 20.9289 16.8813L12.5955 21.8813C12.3316 22.0396 12.0019 22.0396 11.738 21.8813L3.40471 16.8813C3.01006 16.6445 2.88209 16.1326 3.11888 15.7379Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineShop = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.00052 7.76832L4.86151 3H19.1385L21.9995 7.76832L22 7.99813C22.0017 8.74793 21.75 9.4763 21.2858 10.0651C20.8216 10.6539 20.1721 11.0687 19.4426 11.2421C18.7132 11.4155 17.9465 11.3373 17.267 11.0204C16.9658 10.8798 16.6895 10.6961 16.446 10.4766C16.2986 10.61 16.1389 10.7304 15.9685 10.8361C15.444 11.1613 14.8392 11.3335 14.2221 11.3335C13.605 11.3335 13.0002 11.1613 12.4757 10.8361C12.306 10.7309 12.1469 10.611 12 10.4782C11.8531 10.611 11.694 10.7309 11.5243 10.8361C10.9998 11.1613 10.395 11.3335 9.77792 11.3335C9.16083 11.3335 8.556 11.1613 8.03154 10.8361C7.86112 10.7304 7.70139 10.61 7.55398 10.4766C7.31048 10.6961 7.03422 10.8798 6.73301 11.0204C6.05351 11.3373 5.28685 11.4155 4.55738 11.2421C3.8279 11.0687 3.1784 10.6539 2.7142 10.0651C2.25 9.4763 1.99833 8.74793 2.00001 7.99813L2.00052 7.76832ZM5.80516 4.66667L3.67927 8.20981C3.71639 8.50923 3.83464 8.7943 4.02305 9.03329C4.25468 9.32711 4.57879 9.53406 4.94279 9.62059C5.3068 9.70711 5.68936 9.66812 6.02843 9.50995C6.3675 9.35178 6.64319 9.0837 6.81079 8.7492L7.55928 7.25534L8.30225 8.75195C8.43866 9.02671 8.64906 9.25794 8.90978 9.41958C9.17049 9.58123 9.47116 9.66687 9.77792 9.66687C10.0847 9.66687 10.3853 9.58123 10.6461 9.41958C10.9068 9.25794 11.1172 9.02671 11.2536 8.75195L12 7.2484L12.7464 8.75195C12.8828 9.02671 13.0932 9.25794 13.3539 9.41958C13.6147 9.58123 13.9153 9.66687 14.2221 9.66687C14.5288 9.66687 14.8295 9.58123 15.0902 9.41958C15.3509 9.25794 15.5613 9.02671 15.6977 8.75195L16.4407 7.25534L17.1892 8.7492C17.3568 9.0837 17.6325 9.35178 17.9716 9.50995C18.3106 9.66812 18.6932 9.70711 19.0572 9.62059C19.4212 9.53407 19.7453 9.32711 19.977 9.03329C20.1654 8.79429 20.2836 8.50923 20.3207 8.20981L18.1948 4.66667H5.80516Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.16666 12.1667V19.6667H9.5V14.6667H14.5V19.6667H17.8333V12.1667H19.5V21.3333H4.5V12.1667H6.16666ZM12.8333 19.6667V16.3333H11.1667V19.6667H12.8333Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineViewOn = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.82586 10.9934C3.72226 11.1421 3.66671 11.3189 3.66671 11.5002C3.66671 11.6815 3.72227 11.8584 3.82588 12.0071C4.45624 12.8993 5.53697 14.2483 6.95969 15.3675C8.38511 16.4889 10.0939 17.3337 12 17.3337C13.9061 17.3337 15.6149 16.4889 17.0403 15.3675C18.463 14.2483 19.5438 12.8993 20.1741 12.0071C20.2777 11.8584 20.3333 11.6815 20.3333 11.5002C20.3333 11.3189 20.2777 11.142 20.1741 10.9933C19.5438 10.1011 18.463 8.75211 17.0403 7.63287C15.6149 6.5115 13.9061 5.66671 12 5.66671C10.0939 5.66671 8.38511 6.5115 6.95969 7.63287C5.53696 8.75212 4.45622 10.1012 3.82586 10.9934ZM5.92917 6.32292C7.52466 5.06776 9.58844 4 12 4C14.4116 4 16.4753 5.06776 18.0708 6.32292C19.6681 7.57945 20.8561 9.06986 21.5368 10.0336L21.539 10.0367C21.8391 10.4657 22 10.9766 22 11.5002C22 12.0238 21.8391 12.5347 21.539 12.9637L21.5368 12.9669C20.8561 13.9306 19.6681 15.421 18.0708 16.6775C16.4753 17.9327 14.4116 19.0004 12 19.0004C9.58844 19.0004 7.52466 17.9327 5.92917 16.6775C4.33193 15.421 3.14394 13.9306 2.46324 12.9669L2.46102 12.9637C2.16094 12.5347 2 12.0238 2 11.5002C2 10.9766 2.16095 10.4657 2.46103 10.0367L2.46323 10.0336C3.14393 9.06985 4.33193 7.57945 5.92917 6.32292Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.0001 9.00014C10.6193 9.00014 9.5 10.1195 9.5 11.5002C9.5 12.881 10.6193 14.0003 12.0001 14.0003C13.3808 14.0003 14.5001 12.881 14.5001 11.5002C14.5001 10.1195 13.3808 9.00014 12.0001 9.00014ZM7.83329 11.5002C7.83329 9.19896 9.69882 7.33343 12.0001 7.33343C14.3013 7.33343 16.1669 9.19896 16.1669 11.5002C16.1669 13.8015 14.3013 15.667 12.0001 15.667C9.69882 15.667 7.83329 13.8015 7.83329 11.5002Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineSoccerArea = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 21H22C22.2652 21 22.5196 20.8946 22.7071 20.7071C22.8946 20.5196 23 20.2652 23 20V4C23 3.73478 22.8946 3.48043 22.7071 3.29289C22.5196 3.10536 22.2652 3 22 3H2C1.73478 3 1.48043 3.10536 1.29289 3.29289C1.10536 3.48043 1 3.73478 1 4V20C1 20.2652 1.10536 20.5196 1.29289 20.7071C1.48043 20.8946 1.73478 21 2 21ZM21 14H19V10H21V14ZM13 5H21V8H18C17.7348 8 17.4804 8.10536 17.2929 8.29289C17.1054 8.48043 17 8.73478 17 9V15C17 15.2652 17.1054 15.5196 17.2929 15.7071C17.4804 15.8946 17.7348 16 18 16H21V19H13V15.858C13.8576 15.6397 14.618 15.1419 15.1611 14.4433C15.7042 13.7446 15.9991 12.8849 15.9991 12C15.9991 11.1151 15.7042 10.2554 15.1611 9.55672C14.618 8.85806 13.8576 8.36027 13 8.142V5ZM14 12C14 12.3956 13.8827 12.7822 13.6629 13.1111C13.4432 13.44 13.1308 13.6964 12.7654 13.8478C12.3999 13.9991 11.9978 14.0387 11.6098 13.9616C11.2219 13.8844 10.8655 13.6939 10.5858 13.4142C10.3061 13.1345 10.1156 12.7781 10.0384 12.3902C9.96126 12.0022 10.0009 11.6001 10.1522 11.2346C10.3036 10.8692 10.56 10.5568 10.8889 10.3371C11.2178 10.1173 11.6044 10 12 10C12.5304 10 13.0391 10.2107 13.4142 10.5858C13.7893 10.9609 14 11.4696 14 12ZM3 10H5V14H3V10ZM3 16H6C6.26522 16 6.51957 15.8946 6.70711 15.7071C6.89464 15.5196 7 15.2652 7 15V9C7 8.73478 6.89464 8.48043 6.70711 8.29289C6.51957 8.10536 6.26522 8 6 8H3V5H11V8.142C10.1424 8.36027 9.382 8.85806 8.83889 9.55672C8.29578 10.2554 8.00094 11.1151 8.00094 12C8.00094 12.8849 8.29578 13.7446 8.83889 14.4433C9.382 15.1419 10.1424 15.6397 11 15.858V19H3V16Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineAddressArea = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_7875_9199)">
<path d="M11.9998 23.7373C6.05605 23.7373 0.0185547 22.4623 0.0185547 20.0248C0.0185547 17.8498 4.66855 16.9498 6.65605 16.6686H6.73105C7.0123 16.6686 7.25605 16.8748 7.29355 17.1561C7.3123 17.3061 7.2748 17.4561 7.18105 17.5686C7.0873 17.6811 6.95605 17.7748 6.80605 17.7936C2.6623 18.3936 1.1248 19.4811 1.1248 20.0436C1.1248 21.0936 5.3623 22.6311 11.9811 22.6311C18.5998 22.6311 22.8373 21.0936 22.8373 20.0436C22.8373 19.4811 21.2811 18.3936 17.0998 17.7936C16.9498 17.7748 16.8186 17.6998 16.7248 17.5686C16.6311 17.4561 16.5936 17.3061 16.6123 17.1561C16.6498 16.8748 16.8936 16.6686 17.1748 16.6686H17.2498C19.2561 16.9498 23.9436 17.8498 23.9436 20.0248C23.9623 22.4436 17.9436 23.7373 11.9998 23.7373ZM11.6061 19.7623C11.3248 19.4998 4.8373 13.3686 4.8373 7.34981C4.8373 3.41231 8.04355 0.206055 11.9811 0.206055C15.9186 0.206055 19.1248 3.41231 19.1248 7.34981C19.1248 13.1998 12.6373 19.4811 12.3561 19.7436L11.9811 20.1186L11.6061 19.7623ZM11.9998 1.33105C8.68105 1.33105 5.98105 4.03105 5.98105 7.34981C5.98105 12.0561 10.5748 17.0998 11.9998 18.5436C13.4436 17.0623 18.0373 11.9248 18.0373 7.34981C18.0186 4.04981 15.3186 1.33105 11.9998 1.33105ZM11.9998 10.5373C10.1248 10.5373 8.60605 9.01855 8.60605 7.14355C8.60605 5.26855 10.1248 3.7498 11.9998 3.7498C13.8748 3.7498 15.3936 5.26855 15.3936 7.14355C15.3936 9.01855 13.8748 10.5373 11.9998 10.5373ZM11.9998 4.8748C10.7436 4.8748 9.73105 5.8873 9.73105 7.14355C9.73105 8.3998 10.7436 9.41231 11.9998 9.41231C13.2561 9.41231 14.2686 8.3998 14.2686 7.14355C14.2686 5.8873 13.2373 4.8748 11.9998 4.8748Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</g>
<defs>
<clipPath id="clip0_7875_9199">
<rect width="24" height="24"/>
</clipPath>
</defs>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const FilledUserCircle = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.877014 7.49988C0.877014 3.84219 3.84216 0.877045 7.49985 0.877045C11.1575 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1575 14.1227 7.49985 14.1227C3.84216 14.1227 0.877014 11.1575 0.877014 7.49988ZM7.49985 1.82704C4.36683 1.82704 1.82701 4.36686 1.82701 7.49988C1.82701 8.97196 2.38774 10.3131 3.30727 11.3213C4.19074 9.94119 5.73818 9.02499 7.50023 9.02499C9.26206 9.02499 10.8093 9.94097 11.6929 11.3208C12.6121 10.3127 13.1727 8.97172 13.1727 7.49988C13.1727 4.36686 10.6328 1.82704 7.49985 1.82704ZM10.9818 11.9787C10.2839 10.7795 8.9857 9.97499 7.50023 9.97499C6.01458 9.97499 4.71624 10.7797 4.01845 11.9791C4.97952 12.7272 6.18765 13.1727 7.49985 13.1727C8.81227 13.1727 10.0206 12.727 10.9818 11.9787ZM5.14999 6.50487C5.14999 5.207 6.20212 4.15487 7.49999 4.15487C8.79786 4.15487 9.84999 5.207 9.84999 6.50487C9.84999 7.80274 8.79786 8.85487 7.49999 8.85487C6.20212 8.85487 5.14999 7.80274 5.14999 6.50487ZM7.49999 5.10487C6.72679 5.10487 6.09999 5.73167 6.09999 6.50487C6.09999 7.27807 6.72679 7.90487 7.49999 7.90487C8.27319 7.90487 8.89999 7.27807 8.89999 6.50487C8.89999 5.73167 8.27319 5.10487 7.49999 5.10487Z" fill="${color ?? '#667994'}" opacity="${opacity}"/>
</svg>`} width={size ?? 16} height={size ?? 16} />

export const OutlineGradute = ({ size, color, opacity = 1 }: { size?: number, color?: string, opacity?: number }) => <SvgXml xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21 10L12 5L3 10L6 11.6667M21 10L18 11.6667M21 10C21.6129 10.3064 22 10.9328 22 11.618V16.9998M6 11.6667L12 15L18 11.6667M6 11.6667V17.6667L12 21L18 17.6667V11.6667" stroke="${color ?? '#667994'}" stroke-opacity="${opacity}" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`} width={size ?? 16} height={size ?? 16} />