export const formatTimestampToDate = (timestamp) => {
    const now = Date.now();
    const secondsPast = (now - timestamp) / 1000;
    const date = new Date(timestamp);

    // Xử lý cho các sự kiện trong tương lai
    if (secondsPast < 0) {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    }

    // Xử lý thời gian tương đối
    if (secondsPast < 60) {
        return 'vài giây trước';
    }
    if (secondsPast < 3600) {
        // Dưới 1 giờ
        return `${Math.round(secondsPast / 60)} phút trước`;
    }
    if (secondsPast < 86400) {
        // Dưới 1 ngày
        return `${Math.round(secondsPast / 3600)} giờ trước`;
    }
    if (secondsPast < 604800) {
        // Dưới 7 ngày
        return `${Math.round(secondsPast / 86400)} ngày trước`;
    }

    // Hiển thị ngày/tháng/năm cho các tin cũ hơn
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};