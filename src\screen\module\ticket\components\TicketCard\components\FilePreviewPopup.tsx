import React from 'react';
import {View, TouchableOpacity, Dimensions, StyleSheet} from 'react-native';
import WebView from 'react-native-webview';
import {Winicon} from '../../../../../../component/export-component';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import ScreenHeader from '../../../../../layout/header';
import ConfigAPI from '../../../../../../config/configApi';

interface FilePreviewPopupProps {
  file: any;
  onClose: () => void;
}

export const FilePreviewPopup: React.FC<FilePreviewPopupProps> = ({
  file,
  onClose,
}) => {
  return (
    <View style={styles.container}>
      <ScreenHeader
        style={styles.header}
        title={`Xem nhanh`}
        prefix={<View />}
        action={
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <WebView
        onHttpError={() => {
          console.log('onHttpError');
        }}
        style={styles.webView}
        source={{
          uri: ConfigAPI.url.replace('/api/', '') + file.Url,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  webView: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});
