import React, {useCallback, useEffect} from 'react';
import {useSelector} from 'react-redux';
import {View, StyleSheet} from 'react-native';
import ScrollableTabs from '../../../../component/scrollable/ScrollableTabs';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';
import {RootState} from '../../../../redux/store/store';
import iconSvg from '../../../../svgs/iconSvg';

const TABS_DATA = [
  {
    id: '1',
    type: 'svg' as const,
    label: 'Tổng quan',
    icon: iconSvg.All,
    size: 20,
  },
  {
    id: '2',
    type: 'svg' as const,
    label: 'Chứng nhận',
    icon: iconSvg.work,
    size: 20,
  },
  {
    id: '3',
    type: 'svg' as const,
    label: 'Báo c<PERSON>o vận hành',
    icon: iconSvg.work,
    size: 20,
  },
];

const ScrollOptionForm = ({setTabId}: {setTabId?: (tabId: string) => void}) => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);

  // Handle filter change
  const handleFilterChange = useCallback(
    (filterId: string) => {
      const newActiveFilters = {
        [filterId]: true,
      };
      productByCategoryHook.setData('filter', {
        ...filter,
        activeFilters: newActiveFilters,
      });
      setTabId?.(filterId);
    },
    [filter, productByCategoryHook],
  );

  useEffect(() => {
    handleFilterChange('1');
  }, []);

  return (
    <View style={styles.container}>
      <ScrollableTabs onChangeTab={handleFilterChange} data={TABS_DATA} />
    </View>
  );
};

export default ScrollOptionForm;

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  LastIcon: {
    paddingHorizontal: 10,
  },
});
