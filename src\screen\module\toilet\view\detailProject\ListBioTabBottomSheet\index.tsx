import React, {useRef} from 'react';
import {FlatList, RefreshControl, StyleSheet, Text, View} from 'react-native';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import ListTile from '../../../../../../component/list-tile/list-tile';
import EmptyPage from '../../../../../../project-component/empty-page';
import {useSelectorCustomerState} from '../../../../../../redux/hooks/hooks';
import {Ultis} from '../../../../../../utils/Utils';
import {DeviceBioStatus} from '../../../../service/components/da';
import {SkeletonImage} from '../../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../../config/configApi';
import {CardToiletHoriSkeleton} from '../../../../../../project-component/skeletonCard';
import {
  FDialog,
  FTextField,
  showDialog,
  Winicon,
} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import AppButton from '../../../../../../component/button';
import WScreenFooter from '../../../../../layout/footer';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../../component/popup/popup';
import {PopupSelectDevices} from '../../../../workplace/components/popup/PopupSelectDevices';
import {useListBioTabBottomSheet} from './hooks/useListBioTabBottomSheet';
import {PopupAddEditDevice} from './components/PopupAddEditDevice';
import {CustomBottomSheet} from '../../../../../../project-component/form/DateRangePicker/CustomBottomSheet';

interface Props {
  data: any;
  refreshing: any;
  onRefresh: any;
  disabled?: any;
  formId?: any;
  visible?: boolean;
  onClose?: () => void;
  title?: string;
  subTitle?: string;
  height?: number | string;
  isShowConfirm?: boolean;
  onConfirm?: () => void;
  cancelText?: string;
  confirmText?: string;
}

export default function ListBioTabBottomSheet(props: Props) {
  const {
    data,
    refreshing,
    onRefresh,
    disabled,
    formId,
    visible = true,
    onClose = () => {},
    title = 'Chế phẩm sinh học',
    subTitle,
    height = '80%',
    isShowConfirm = false,
    onConfirm,
    cancelText,
    confirmText,
  } = props;
  const user = useSelectorCustomerState().data;
  const popupRef = useRef<any>();
  const dialogRef = useRef<any>();

  const {
    devices,
    isLoading,
    searchValue,
    setSearchValue,
    products,
    files,
    getData,
    deleteItems,
    addDevicesFromProducts,
  } = useListBioTabBottomSheet({toiletId: data?.Id, formId});

  const showPopupSelectProduct = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupSelectDevices
          ref={popupRef}
          isBio={true}
          selectOther={() => {
            closePopup(popupRef);
            showAddEdit(undefined, true);
          }}
          onSubmit={async (selectedProducts: any) => {
            await addDevicesFromProducts(selectedProducts);
          }}
        />
      ),
    });
  };

  const showAddEdit = (item: any, isAddNew = false) => {
    if (item || isAddNew) {
      showPopup({
        ref: popupRef,
        enableDismiss: true,
        children: (
          <PopupAddEditDevice
            ref={popupRef}
            id={item?.Id}
            formId={formId}
            toiletId={data.Id}
            onSubmit={() => {
              getData({});
            }}
          />
        ),
      });
    } else {
      showPopupSelectProduct();
    }
  };

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title={title}
      subTitle={subTitle}
      height={height}
      isShowConfirm={isShowConfirm}
      onCancel={onClose}
      onConfirm={onConfirm}
      cancelText={cancelText}
      confirmText={confirmText}>
      <View style={styles.container}>
        <FDialog ref={dialogRef} />
        <FPopup ref={popupRef} />
        {!formId ? (
          <View style={styles.searchContainer}>
            <FTextField
              style={styles.searchField}
              onChange={(vl: any) => {
                setSearchValue(vl);
              }}
              value={searchValue}
              onBlur={() => getData({})}
              onSubmit={() => getData({})}
              placeholder="Tìm kiếm chế phẩm sinh học"
            />
          </View>
        ) : null}
        <FlatList
          nestedScrollEnabled
          data={devices.data}
          scrollEnabled={formId ? false : true}
          keyExtractor={(item: any, index) => `${item?.Id ?? index}`}
          refreshControl={
            <RefreshControl
              refreshing={refreshing ?? false}
              onRefresh={() => {
                if (onRefresh) onRefresh();
              }}
            />
          }
          style={styles.list}
          ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
          renderItem={({item, index}: {item: any; index: number}) => {
            const discount =
              (item?.Price * item?.Quantity * (item?.Discount ?? 0)) / 100;
            let devTotalPrice = item?.Price * item?.Quantity - discount;
            const vat = devTotalPrice * ((item.Vat ?? 0) / 100);
            if (item.ProductId) {
              var _product = products?.find(e => e?.Id === item?.ProductId);
            }
            const _fileInfor = files
              .filter((e: any) => (_product ?? item).Img?.includes(e.Id))
              .filter(
                (e: any, i: number, a: any[]) =>
                  a.findIndex((eL: any) => eL.Id === e.Id) === i,
              );

            return (
              <ListTile
                key={`${item?.Id} bio ${index}`}
                leading={
                  _fileInfor.length ? (
                    <SkeletonImage
                      source={{uri: ConfigAPI.imgUrlId + _fileInfor[0]?.Id}}
                      style={styles.itemImage}
                    />
                  ) : (
                    <View />
                  )
                }
                style={styles.itemContainer}
                listtileStyle={styles.itemContent}
                title={`${index + 1}. ${item?.Name ?? '-'}`}
                titleStyle={[TypoSkin.title3, styles.itemTitle]}
                bottom={
                  <View style={styles.itemBottom}>
                    <Text
                      style={[
                        TypoSkin.body3,
                        styles.subtitleText,
                      ]}>{`Ngày tạo: ${item.DateCreated ? Ultis.datetoString(new Date(item.DateCreated)) : '-'}`}</Text>
                    <Text
                      style={[
                        TypoSkin.body3,
                        styles.subtitleText,
                      ]}>{`Số lượng: ${item?.Quantity ?? '-'} ${item.Unit ?? _product?.Unit ?? '-'}`}</Text>
                    {!formId ? (
                      <View style={styles.itemPriceWrap}>
                        <Text
                          style={[
                            TypoSkin.body3,
                            styles.subtitleText,
                          ]}>{`Giá tiền (VNĐ): ${Ultis.money(item.Price) ?? '-'}`}</Text>
                        <Text
                          style={[
                            TypoSkin.body3,
                            styles.subtitleText,
                          ]}>{`Giảm giá (VNĐ): ${Ultis.money(discount) ?? '-'} - Thuế VAT (VNĐ): ${Ultis.money(vat) ?? '-'}`}</Text>
                        <Text
                          style={[
                            TypoSkin.body2,
                            styles.subtitleText,
                          ]}>{`Thành tiền: ${Ultis.money(devTotalPrice + vat)}`}</Text>
                      </View>
                    ) : null}
                    {!disabled ? (
                      <View style={styles.itemActions}>
                        <Winicon
                          src="outline/user interface/s-edit"
                          size={16}
                          onClick={() => {
                            showAddEdit(item);
                          }}
                        />
                        {data?.CustomerId === user?.Id ||
                        item.Status !== DeviceBioStatus.active ? (
                          <Winicon
                            src="outline/user interface/trash-can"
                            size={16}
                            onClick={() => {
                              showDialog({
                                ref: dialogRef,
                                status: ComponentStatus.WARNING,
                                title: 'Bạn chắc chắn muốn xóa',
                                onSubmit: () => {
                                  deleteItems([item?.Id]);
                                },
                              });
                            }}
                          />
                        ) : null}
                      </View>
                    ) : null}
                  </View>
                }
                subtitle={`${Ultis.money(item.Price)} VNĐ`}
              />
            );
          }}
          ListEmptyComponent={() =>
            isLoading && searchValue != '' ? (
              Array.from(Array(10)).map((_, index) => (
                <View key={index} style={styles.skeletonItem}>
                  <CardToiletHoriSkeleton />
                </View>
              ))
            ) : (
              <View style={styles.emptyWrap}>
                <EmptyPage title="Nhà vệ sinh chưa có chế phẩm sinh học nào" />
              </View>
            )
          }
          ListFooterComponent={() => <View style={styles.footerSpacer} />}
        />
        {!disabled ? (
          <WScreenFooter style={styles.footer}>
            <AppButton
              title={'Thêm chế phẩm sinh học'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={styles.addBtn}
              onPress={() => {
                showAddEdit(null);
              }}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          </WScreenFooter>
        ) : null}
      </View>
    </CustomBottomSheet>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  searchContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  searchField: {
    paddingHorizontal: 16,
    width: '100%',
  },
  list: {
    flex: 1,
    marginVertical: 16,
  },
  itemSeparator: {
    height: 8,
  },
  itemContainer: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
    borderRadius: 8,
  },
  itemContent: {
    gap: 16,
  },
  itemTitle: {
    paddingBottom: 8,
  },
  itemImage: {
    width: 55,
    height: 60,
    borderRadius: 4,
    overflow: 'hidden',
    paddingTop: 6,
  },
  itemBottom: {
    flex: 1,
    alignItems: 'flex-start',
    width: '100%',
    paddingTop: 4,
    gap: 4,
  },
  subtitleText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  itemPriceWrap: {
    flex: 1,
    alignItems: 'flex-start',
    width: '100%',
    gap: 4,
  },
  itemActions: {
    alignSelf: 'flex-end',
    flex: 1,
    gap: 16,
    paddingTop: 16,
    flexDirection: 'row',
  },
  skeletonItem: {marginBottom: 16},
  emptyWrap: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  footerSpacer: {height: 65},
  footer: {paddingHorizontal: 16},
  addBtn: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
