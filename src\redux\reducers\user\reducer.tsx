import {
  createSlice,
  PayloadAction,
  UnknownAction,
  Dispatch,
} from '@reduxjs/toolkit';
import {BaseDA} from 'screen/baseDA';
import {Ultis} from 'utils/Utils';
import {CustomerCompanyItem, CustomerItem, CustomerStatus} from './da';
import ConfigAPI from 'config/configApi';
import {RootScreen} from 'router/router';
import {
  clearDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
} from 'utils/AsyncStorage';
import {DataController} from 'screen/base-controller';
import {store} from '../../store/store';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {getFcmToken} from 'features/notifications/fcm/fcm_helper';
import {CompanyProfileActions} from '../company/reducer';
import {ToiletActions} from '../toilet/reducer';
import {NotificationActions} from '../notification/reducer';
import {signOutPhoneFb} from 'features/otp-loginwFirebase/PhoneSignIn';
import {getImage} from 'redux/actions/rootAction';

interface CustomerSimpleResponse {
  data?: CustomerItem;
  myAddress?: Array<any>;
  role?: CustomerCompanyItem;
  onLoading?: boolean;
  bgColor?: string;
  type?: string;
  status?: CustomerStatus;
}

const initState: CustomerSimpleResponse = {
  data: undefined,
  myAddress: undefined,
  role: undefined,
  bgColor: Ultis.generateRandomColor(),
  onLoading: false,
};

export const customerSlice = createSlice({
  name: 'customer',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFOR':
          state.data = action.payload.data.customer;
          state.role = action.payload.data.customerCompany;
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        case 'UPDATEROLE':
          state.role = action.payload.data;
          break;
        case 'ADDADDRESS':
          if (
            state.myAddress &&
            state.myAddress.length > 0 &&
            state.myAddress.find(el => el.Id === action.payload.data.Id)
          ) {
            state.myAddress = state.myAddress.map(el => {
              if (el.Id === action.payload.data.Id) {
                return action.payload.data;
              }
              return el;
            });
          } else {
            if (state.myAddress && state.myAddress.length == 0) {
              action.payload.data.IsDefault = true;
            }
            state.myAddress = [action.payload.data, ...(state.myAddress || [])];
          }
          if (action.payload.data.IsDefault) {
            state.myAddress = state.myAddress.map(el => {
              if (el.Id === action.payload.data.Id) {
                return action.payload.data;
              }
              return {
                ...el,
                IsDefault: false,
              };
            });
          }
          break;
        case 'GETMYADDRESS':
          state.myAddress = action.payload.data;
          break;
        case 'DELETEADDRESS':
          if (state.myAddress) {
            // kiem tra địa chỉ là mặc định thì chuyển địa chỉ khác làm mặc định
            const defaultAddress = state.myAddress.find(
              el => el.Id === action.payload.data,
            );

            state.myAddress = state.myAddress.filter(
              el => el.Id !== action.payload.data,
            );
            if (defaultAddress.IsDefault) {
              if (state.myAddress?.length > 0) {
                state.myAddress[0].IsDefault = true;
              }
            }
          }
          break;
        case 'LOGOUT':
          state.role = undefined;
          state.data = undefined;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    setData: (
      state,
      action: PayloadAction<{
        stateName: keyof CustomerSimpleResponse;
        data: any;
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
});

const {handleActions, onFetching, setData} = customerSlice.actions;

export default customerSlice.reducer;

export class CustomerActions {
  static getInfor = async (
    dispatch: Dispatch<UnknownAction>,
    navigation?: any,
  ) => {
    dispatch(onFetching());
    const res = await BaseDA.get(ConfigAPI.url + 'data/getInfo', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
    });

    if (res.code === 200) {
      const controller = new DataController('Customer');
      const deviceToken = await getDataToAsyncStorage('fcmToken');
      if (!res.data.DeviceToken?.includes(deviceToken) && deviceToken) {
        res.data.DeviceToken ??= '';
        res.data.DeviceToken += `,${deviceToken}`;
        res.data.DeviceToken = res.data.DeviceToken.split(',').filter(
          (devTk: string, i: number, arr: Array<string>) =>
            devTk.length && arr.indexOf(devTk) === i,
        );
        if (res.data.DeviceToken.length > 3)
          res.data.DeviceToken = res.data.DeviceToken.slice(
            res.data.DeviceToken.length - 3,
          );
        res.data.DeviceToken = res.data.DeviceToken.join(',');
        await controller.edit([res.data]);
      }

      switch (res.data.Status) {
        case CustomerStatus.inactive:
          const activeCustomer = await controller.edit([
            {
              ...res.data,
              Status: CustomerStatus.active,
              DeviceToken: deviceToken,
            },
          ]);
          if (activeCustomer.code === 200)
            res.data.Status = CustomerStatus.active;
          break;
        case CustomerStatus.locked:
          showSnackbar({
            message:
              'Tài khoản của bạn đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
            status: ComponentStatus.ERROR,
          });
          await removeDataToAsyncStorage('accessToken');
          await removeDataToAsyncStorage('refreshToken');
          await removeDataToAsyncStorage('timeRefresh');
          return navigation.reset({
            index: 0,
            routes: [{name: RootScreen.login}],
          });
        default:
          break;
      }
      const dataImage = await getImage({items: [res.data]});
      let data = {
        customer: dataImage[0],
        customerCompany: undefined,
      };

      const roleController = new DataController('CustomerCompany');
      const roleRes = await roleController.getListSimple({
        page: 1,
        size: 1,
        query: `@CustomerId:{${res.data.Id}}`,
      });
      if (roleRes.code === 200 && roleRes.data.length) {
        data.customerCompany = roleRes.data[0];
        if (roleRes.data[0]?.Status === 1) {
          // get company infor
          CompanyProfileActions.getInfor(
            store.dispatch,
            roleRes.data[0]?.CompanyProfileId,
          );
        }
      } else {
        // get company infor user
        if (data.customer?.CompanyProfileId)
          CompanyProfileActions.getInfor(
            store.dispatch,
            data.customer.CompanyProfileId,
          );
      }
      if (res.data?.Id) ToiletActions.getAll(store.dispatch, res.data.Id);
      dispatch(
        handleActions({
          type: 'GETINFOR',
          data: data,
        }),
      );
    }
  };

  static login = async (phone: string, password?: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/login', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {
        type: password ? 'account' : 'phone',
        phone: phone,
        password: password,
      },
    });
    return res;
  };

  static checkPassword = async (phone: string, password?: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/checkPassword', {
      headers: {module: 'Customer', pid: ConfigAPI.pid},
      body: {phone: phone, password: password},
    });
    return res;
  };

  static hashPassword = async (password?: string) => {
    const res = await BaseDA.get(
      ConfigAPI.url + `data/bcrypt?password=${password}`,
      {
        headers: {module: 'Customer', pid: ConfigAPI.pid},
      },
    );
    return res;
  };

  static lockAccount = async (phone: string) => {
    const controller = new DataController('Customer');
    const res = (await controller.getListSimple({
      page: 1,
      size: 1,
      query: `@Mobile:{${phone}}`,
    })) as any;
    if (res.data.length) {
      await controller.edit([{...res.data[0], Status: CustomerStatus.locked}]);
      return {
        code: 200,
        message:
          'Tài khoản đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
      };
    } else return {code: 400, message: 'Tài khoản không tồn tại'};
  };

  static logout = async (
    dispatch: Dispatch<UnknownAction>,
    navigation: any,
  ) => {
    // write clear data
    // await clearDataToAsyncStorage()
    const deviceToken = await getDataToAsyncStorage('fcmToken');
    const user = store.getState().customer.data;
    if (!user) {
      navigation.reset({
        index: 0,
        routes: [{name: RootScreen.login}],
      });
      dispatch(
        handleActions({
          type: 'LOGOUT',
        }),
      );
      return;
    }
    const customerController = new DataController('Customer');
    if (deviceToken && user?.DeviceToken?.includes(deviceToken)) {
      const controller = new DataController('Customer');
      // console.log('====================================');
      // console.log(deviceToken, { ...user, DeviceToken: user.DeviceToken.split(",").filter(e => e.length && e !== deviceToken).join(",") });
      // console.log('====================================');
      controller.edit([
        {
          ...user,
          DeviceToken: user.DeviceToken.split(',')
            .filter(e => e.length && e !== deviceToken)
            .join(','),
        },
      ]);
    }
    const res = await customerController.edit([
      {...store.getState().customer.data, DeviceToken: undefined},
    ]);
    if (res.code == 200) {
      await removeDataToAsyncStorage('accessToken');
      await removeDataToAsyncStorage('refreshToken');
      await removeDataToAsyncStorage('timeRefresh');
      await signOutPhoneFb();
      navigation.reset({
        index: 0,
        routes: [{name: RootScreen.login}],
      });
      NotificationActions.reset(store.dispatch);
      CompanyProfileActions.reset(store.dispatch);
      dispatch(
        handleActions({
          type: 'LOGOUT',
        }),
      );
    }
  };

  static edit = async (dispatch: Dispatch<UnknownAction>, user: any) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.edit([user]);
    if (res.code === 200) {
      dispatch(
        handleActions({
          type: 'UPDATE',
          data: user,
        }),
      );
    }
    return res;
  };

  static addRole = async (
    dispatch: Dispatch<UnknownAction>,
    userRole: CustomerCompanyItem,
  ) => {
    const controller = new DataController('CustomerCompany');
    const res = await controller.add([userRole]);
    if (res.code === 200) {
      CustomerActions.getInfor(dispatch);
      dispatch(
        handleActions({
          type: 'UPDATEROLE',
          data: userRole,
        }),
      );
    }
  };

  static editRole = async (
    dispatch: Dispatch<UnknownAction>,
    userRole: CustomerCompanyItem,
  ) => {
    dispatch(onFetching());
    const controller = new DataController('CustomerCompany');
    const res = await controller.edit([userRole]);
    if (res.code === 200) {
      CustomerActions.getInfor(dispatch);
      dispatch(
        handleActions({
          type: 'UPDATEROLE',
          data: userRole,
        }),
      );
    }
  };

  static deleteRole = async (
    dispatch: Dispatch<UnknownAction>,
    userRoleId: string,
  ) => {
    dispatch(onFetching());
    const controller = new DataController('CustomerCompany');
    const res = await controller.delete([userRoleId]);
    if (res.code === 200) {
      CustomerActions.getInfor(dispatch);
      dispatch(
        handleActions({
          type: 'UPDATEROLE',
          data: undefined,
        }),
      );
    }
  };

  static delete = async (
    dispatch: Dispatch<UnknownAction>,
    userId: string,
    navigation: any,
  ) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.delete([userId]);
    if (res.code === 200) {
      clearDataToAsyncStorage();
      getFcmToken();
      showSnackbar({
        message:
          'Tài khoản đã bị xóa khỏi hệ thống, vui lòng đăng nhập lại để sử dụng',
        status: ComponentStatus.WARNING,
      });
      navigation.reset({
        index: 0,
        routes: [{name: RootScreen.login}],
      });
    }
  };

  static getAddresses = (cusId: any) => async (dispatch: Dispatch) => {
    try {
      if (!cusId) return;
      dispatch(onFetching());
      const controller = new DataController('Address');
      const res = await controller.getListSimple({
        page: 1,
        size: 10,
        query: `@CustomerId: {${cusId}}`,
      });

      if (res.code === 200) {
        dispatch(
          handleActions({
            type: 'GETMYADDRESS',
            data: res.data,
          }),
        );
      }
      return res;
    } finally {
      dispatch(setData({stateName: 'onLoading', data: false}));
    }
  };

  static editAddress =
    (address: any, isAdd?: boolean) => async (dispatch: Dispatch) => {
      dispatch(onFetching());
      const controller = new DataController('Address');
      if (address?.IsDefault) {
        const resCheck = await controller.getListSimple({
          page: 1,
          size: 1,
          query: `@CustomerId: {${address.CustomerId}} @IsDefault: {true}`,
        });
        if (resCheck.code === 200) {
          if (resCheck.data.length) {
            await controller.edit([{...resCheck.data[0], IsDefault: false}]);
          }
        }
      }
      const res = isAdd
        ? await controller.add([address])
        : await controller.edit([address]);
      if (res.code === 200) {
        if (address?.Name?.includes('khởi tạo')) {
          // nothing
        } else {
          showSnackbar({
            message: 'Thao tác thành công',
            status: ComponentStatus.SUCCSESS,
          });
        }
        dispatch(
          handleActions({
            type: 'ADDADDRESS',
            data: address,
          }),
        );
      }
      return res;
    };

  static deleteAddress = (addressId: any) => async (dispatch: Dispatch) => {
    const controller = new DataController('Address');
    const res = await controller.delete([addressId]);
    if (res.code === 200) {
      showSnackbar({
        message: 'Thao tác thành công',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(
        handleActions({
          type: 'DELETEADDRESS',
          data: addressId,
        }),
      );
    }
    return res;
  };
}
