import {
  View,
  TouchableOpacity,
  ActivityIndicator,
  Text,
  StyleSheet,
} from 'react-native';
import {ColorThemes} from '../assets/skin/colors';

interface LoadMoreButtonProps {
  onPress: () => void;
  loading: boolean;
}

const LoadMoreButton = ({onPress, loading}: LoadMoreButtonProps) => {
  return (
    <View style={styles.loadMoreButtonContainer}>
      <TouchableOpacity
        style={styles.loadMoreButton}
        onPress={onPress}
        disabled={loading}>
        {loading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={styles.loadMoreButtonText}>Xem thêm</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  loadMoreButtonContainer: {
    marginVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadMoreButton: {
    height: 35,
    width: 100,
    backgroundColor: ColorThemes.light.primary_main_color,
    padding: 6,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadMoreButtonText: {
    color: '#fff',
  },
});

export default LoadMoreButton;
