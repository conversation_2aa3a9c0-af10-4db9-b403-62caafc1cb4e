import React, {useEffect} from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity} from 'react-native';
import {Winicon} from '../../../../component/export-component';
import {TypoSkin} from '../../../../assets/skin/typography';
import CetificateAchievemenDa from '../CetificateAchievemenDa';
import {Ultis} from '../../../../utils/Utils';
import EmptyPage from '../../../../project-component/empty-page';
const TimelineProgress: React.FC<{toiletId: string}> = ({toiletId}) => {
  const [dataLog, setDataLog] = React.useState<any>([]);
  const getDataLogByToiletId = async () => {
    const respone = await CetificateAchievemenDa.getLogByTOiletId(toiletId);
    if (respone?.code === 200) {
      setDataLog(respone.data);
    }
  };
  useEffect(() => {
    getDataLogByToiletId();
  }, []);
  const renderTimelineItem = ({item, index}: {item: any; index: number}) => (
    <View style={styles.timelineItem}>
      <View style={styles.leftSection}>
        <Text style={styles.dateText}>
          {Ultis.datetoString(new Date(item?.DateCreated), 'dd/mm/yyyy')}
        </Text>
      </View>
      <View style={styles.centerSection}>
        <View
          style={[
            styles.dot,
            item.ProgressStatus === 2 && styles.completedDot,
            item.ProgressStatus === 1 && styles.ProcessingDot,
            item.ProgressStatus === 0 && styles.pendingDot,
          ]}>
          {item.ProgressStatus === 2 && (
            <Winicon
              src="outline/user interface/check"
              size={12}
              color="white"
            />
          )}
        </View>
        {index < dataLog.length - 1 && <View style={styles.line} />}
      </View>
      <View style={styles.rightSection}>
        <Text style={styles.titleText}>{item.Name}</Text>
        <Text
          style={[
            styles.statusText,
            item.ProgressStatus === 2
              ? styles.completedStatus
              : item.ProgressStatus === 1 && styles.processingStatus,
          ]}>
          {item.ProgressStatus === 2
            ? 'Hoàn thành'
            : item.ProgressStatus === 1 && 'Đang thực hiện'}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.progressTitle}>
        Tiến độ chuyển đổi nhà vệ sinh theo tiêu chí Sạch - Xanh - Tuần hoàn
      </Text>
      <TouchableOpacity>
        <FlatList
          data={dataLog}
          renderItem={renderTimelineItem}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              <EmptyPage />
            </View>
          )}
        />
      </TouchableOpacity>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    paddingTop: 10,
  },
  progressTitle: {
    ...TypoSkin.buttonText2,
    color: '#333',
    lineHeight: 24,
    paddingBottom: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  leftSection: {
    width: 80,
    alignItems: 'flex-end',
    paddingRight: 16,
    paddingBottom: 40,
  },
  timeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  dateText: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  centerSection: {
    alignItems: 'center',
    width: 20,
  },
  dot: {
    width: 20,
    height: 20,
    borderRadius: 20,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedDot: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
  },
  pendingDot: {
    backgroundColor: '#e0e0e0',
    borderRadius: 20,
  },
  ProcessingDot: {
    borderWidth: 1,
    borderColor: '#2EB553',
    backgroundColor: '#e0e0e0',
    width: 22,
    height: 22,
    borderRadius: 20,
  },
  line: {
    width: 2,
    flex: 1,
    backgroundColor: '#4CAF50',
    marginTop: -8,
  },
  rightSection: {
    flex: 1,
    paddingLeft: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleText: {
    ...TypoSkin.buttonText6,
    color: '#333',
    marginBottom: 4,
    flex: 1,
    marginRight: 10,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  completedStatus: {
    color: '#4CAF50',
  },
  processingStatus: {
    color: '#FF9800',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
});

export default TimelineProgress;
