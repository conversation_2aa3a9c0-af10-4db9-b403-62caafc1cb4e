import React, {useCallback, useEffect} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import iconSvg from '../../../../svgs/iconSvg';
import ScrollableTabs from '../../../../component/scrollable/ScrollableTabs';
import {useSelectorCustomerCompanyState} from '../../../../redux/hooks/hooks';
import ConfigAPI from '../../../../config/configApi';

interface ScrollTabProfileProps {
  containerStyle?: ViewStyle;
  onTabChange?: (tabId: string) => void;
}

const ScrollTabProfile: React.FC<ScrollTabProfileProps> = ({
  containerStyle,
  onTabChange,
}) => {
  const company = useSelectorCustomerCompanyState().data;
  const TABS_DATA = [
    {
      id: 'profile',
      type: 'svg' as const,
      label: '<PERSON><PERSON> nhân',
      icon: iconSvg.profile,
      size: 20,
    },
    {
      id: 'partner',
      type: 'svg' as const,
      label: 'QL Đối tác',
      icon: iconSvg.partner,
      size: 20,
    },
    {
      id: 'ticket',
      type: 'svg' as const,
      label: 'Ticket',
      icon: iconSvg.ticket,
      size: 20,
    },
    {
      id: 'task',
      type: 'svg' as const,
      label: 'Công việc',
      icon: iconSvg.task,
      size: 20,
    },
  ];
  // Handle filter change
  const handleTabChange = useCallback(
    (tabId: string) => {
      console.log('Selected tab:', tabId);
      onTabChange?.(tabId);
    },
    [onTabChange],
  );

  // useEffect(() => {
  //   if (company?.Id === ConfigAPI.ktxCompanyId) {
  //     TABS_DATA.push({
  //       id: 'task',
  //       type: 'svg' as const,
  //       label: 'Công việc',
  //       icon: iconSvg.task,
  //       size: 20,
  //     });
  //   }
  // }, []);

  return (
    <View style={[styles.container, containerStyle]}>
      <ScrollableTabs onChangeTab={handleTabChange} data={TABS_DATA} />
    </View>
  );
};

export default ScrollTabProfile;

const styles = StyleSheet.create({
  container: {
    marginVertical: 6,
  },
});
