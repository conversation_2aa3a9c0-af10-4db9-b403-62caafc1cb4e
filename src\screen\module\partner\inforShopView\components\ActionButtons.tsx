import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ActionButtonsProps} from '../types';
import {styles} from '../styles';

const ActionButtons: React.FC<ActionButtonsProps> = ({onCall, onMessage}) => {
  return (
    <View style={styles.actionButtonsContainer}>
      <TouchableOpacity style={styles.actionButton} onPress={onCall}>
        <Winicon
          src="fill/user interface/phone-call"
          size={24}
          color="white"
        />
      </TouchableOpacity>

      <TouchableOpacity style={styles.actionButton} onPress={onMessage}>
        <Winicon
          src="fill/social media/logo-messenger"
          size={24}
          color="white"
        />
      </TouchableOpacity>
    </View>
  );
};

export default ActionButtons;
