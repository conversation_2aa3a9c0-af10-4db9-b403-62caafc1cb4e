import React, {useState, useEffect, useRef, useCallback} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Slider from '@react-native-community/slider';
import {ColorThemes} from '../../../../../assets/skin/colors';

interface PriceSliderProps {
  value: number;
  onValueChange: (value: number) => void;
  maxPrice: number;
}

const PriceSlider = ({value, onValueChange, maxPrice}: PriceSliderProps) => {
  const [internalValue, setInternalValue] = useState(maxPrice);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  // Sync internal state if the external value prop changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  useEffect(() => {
    setInternalValue(maxPrice);
  }, [maxPrice]);

  const handleValueChange = useCallback(
    (newValue: number) => {
      setInternalValue(newValue);

      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        onValueChange(newValue);
      }, 300);
    },
    [onValueChange],
  );

  // Cleanup the timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, []);

  return (
    <View>
      <Text style={styles.priceRangeText}>
        {`0 — ${internalValue.toLocaleString()}đ`}
      </Text>
      <Slider
        style={styles.slider}
        minimumValue={0}
        maximumValue={maxPrice}
        step={maxPrice / 1000}
        minimumTrackTintColor={ColorThemes.light.primary_main_color}
        maximumTrackTintColor={ColorThemes.light.primary_main_color}
        thumbTintColor={ColorThemes.light.primary_main_color}
        value={internalValue}
        onValueChange={handleValueChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  priceRangeText: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  slider: {
    width: '100%',
    height: 40,
  },
});

export default PriceSlider;
