import React, {useEffect, useRef, useState} from 'react';
import {Alert, View} from 'react-native';
import CetificateAchievemenDa from '../CetificateAchievemenDa';
import {
  ImageCetificateAchievemenDa,
  processInfoKtx,
  RenderButton,
} from '../Ultis/processInfoKtx';
import ViewShot from 'react-native-view-shot';
import {CameraRollHelper} from 'utils/CameraRollHelper';
const Progess: React.FC<{
  toiletId: string;
  setGetStatusForm?: (value: string) => void;
}> = ({toiletId, setGetStatusForm}) => {
  const [data, setData] = React.useState<any>([]);
  const viewShotRef = useRef<ViewShot>(null);
  const getCetificateAchievemenDa = async () => {
    try {
      const respone =
        await CetificateAchievemenDa.getALlToiletCertificateByToiletId(
          toiletId,
        );
      if (respone?.code === 200) {
        setData(respone.data);

        // Xử lý logic set status form dựa trên số lượng CateCriterion
        if (respone.data && respone.data.length > 0 && setGetStatusForm) {
          const dataLength = respone.data.length;

          if (dataLength === 1) {
            const sach = respone.data.find(
              (item: any) => item.CateCriterion?.Name?.toLowerCase() === 'sạch',
            );
            if (sach) {
              setGetStatusForm(sach.Id);
            }
          } else if (dataLength === 2) {
            const xanh = respone.data.find(
              (item: any) => item.CateCriterion?.Name?.toLowerCase() === 'xanh',
            );
            if (xanh) {
              setGetStatusForm(xanh.Id);
            }
          } else if (dataLength === 3) {
            const tuanHoan = respone.data.find(
              (item: any) =>
                item.CateCriterion?.Name?.toLowerCase() === 'tuần hoàn',
            );
            if (tuanHoan) {
              setGetStatusForm(tuanHoan.Id);
            }
          }
        }
      }
    } catch (error) {
      console.log(`check-error`, error);
    }
  };

  useEffect(() => {
    getCetificateAchievemenDa();
  }, [toiletId]);

  const downloadImg = async () => {
    try {
      if (viewShotRef.current && viewShotRef.current.capture) {
        const uri = await viewShotRef.current.capture();

        // Sử dụng CameraRollHelper để lưu ảnh vào thư viện ảnh
        const result = await CameraRollHelper.saveImage(uri, 'KTX-Certificate');

        console.log('Saved to camera roll:', result);

        Alert.alert('Thành công!', 'Ảnh đã được lưu vào thư viện ảnh', [
          {
            text: 'OK',
            onPress: () => {},
          },
        ]);
      }
    } catch (error) {
      console.error('Error downloading image:', error);

      // Nếu lỗi do không có quyền, hiển thị dialog yêu cầu quyền
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('quyền')) {
        CameraRollHelper.showPermissionDialog();
      } else {
        Alert.alert(
          'Lỗi',
          `Không thể lưu ảnh: ${errorMessage || 'Lỗi không xác định'}`,
          [{text: 'OK'}],
        );
      }
    }
  };

  return (
    <View>
      {processInfoKtx(data)}
      {
        <ViewShot ref={viewShotRef} options={{format: 'png', quality: 0.9}}>
          {ImageCetificateAchievemenDa(data)}
        </ViewShot>
      }
      {RenderButton(data, downloadImg)}
    </View>
  );
};
export default Progess;
