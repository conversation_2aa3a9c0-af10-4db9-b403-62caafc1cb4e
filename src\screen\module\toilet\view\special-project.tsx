import {
  View,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ColorThemes} from '../../../../assets/skin/colors';
import ListTile from '../../../../component/list-tile/list-tile';
import {TypoSkin} from '../../../../assets/skin/typography';
import {SkeletonImage} from '../../../../project-component/skeleton-img';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../router/router';
import {TypicalProjectItem} from 'types/typicalProject';
import TypicalProjectDa from 'screen/module/TypicalProject/TypicalProjectDa';

export default function SpecialProjects({
  title = 'Dự án tiêu biểu',
}: {
  title?: string;
}) {
  const navigation = useNavigation<any>();
  const screenWidth = Dimensions.get('window').width;
  const [projectData, setProjectData] = useState<TypicalProjectItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Initialize API
  const projectDa = TypicalProjectDa;

  // Fetch projects from API
  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await projectDa.fetch({page: 1, size: 5});

      setProjectData(response ?? []);
    } catch (error) {
      console.error('Error fetching projects:', error);
      setProjectData([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchProjects();
  }, []);

  const handlePress = (item: TypicalProjectItem) => {
    navigation.navigate(RootScreen.DetailTypicalProject, {id: item.Id});
  };

  const renderProjectItem = ({item}: {item: TypicalProjectItem}) => (
    <TouchableOpacity
      onPress={() => handlePress(item)}
      style={[styles.touchableContainer, {width: screenWidth - 32}]}>
      {/* background img */}
      <SkeletonImage source={{uri: item.Img}} style={styles.backgroundImage} />
      <ListTile
        style={styles.overlayListTile}
        title={item?.Name ?? '-'}
        titleStyle={[TypoSkin.heading5, styles.overlayTitleColor]}
        subtitle={item?.Description ?? '-'}
        subTitleStyle={[TypoSkin.subtitle4, styles.overlaySubtitleColor]}
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ListTile
        title={title}
        style={styles.listTileStyle}
        titleStyle={[TypoSkin.heading6, styles.titleColor]}
      />
      {loading ? (
        <View style={styles.loadingContainer}>
          {/* You can add a loading spinner here if needed */}
          <View style={styles.loadingPlaceholder} />
        </View>
      ) : projectData?.length ? (
        <FlatList
          data={projectData}
          renderItem={renderProjectItem}
          keyExtractor={item => item.Id.toString()}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          snapToInterval={screenWidth - 32 + 16} // item width + gap
          snapToAlignment="start"
          decelerationRate="fast"
          contentContainerStyle={styles.flatListContainer}
          ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
        />
      ) : (
        <View style={styles.emptyContainer}>
          {/* Empty state - you can customize this */}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.transparent,
    gap: 16,
    borderRadius: 8,
  },
  listTileStyle: {
    fontSize: 20,
    padding: 0,
    color: ColorThemes.light.neutral_text_title_color,
  },
  subTitleStyle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  titleColor: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  touchableContainer: {
    height: 396,
    borderTopLeftRadius: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: 396,
    objectFit: 'cover',
    aspectRatio: 1,
  },
  overlayListTile: {
    padding: 0,
    pointerEvents: 'none',
    backgroundColor: ColorThemes.light.transparent,
    justifyContent: 'flex-end',
    paddingVertical: 24,
    paddingHorizontal: 16,
    shadowColor: '#000000',
    height: '100%',
    width: '100%',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 5,
    shadowOpacity: 1.0,
  },
  overlayTitleColor: {
    color: ColorThemes.light.neutral_text_stable_color,
  },
  overlaySubtitleColor: {
    color: ColorThemes.light.neutral_text_label_reverse_color,
  },
  flatListContainer: {},
  itemSeparator: {
    width: 16,
  },
  loadingContainer: {
    height: 396,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingPlaceholder: {
    width: '100%',
    height: 396,
    backgroundColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
  },
  emptyContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
