import React, {useEffect, useState} from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../../svgs/iconSvg';
import {
  CustomDropdownNoBorder,
  CustomDropdownNoBorderWithMutiSelect,
} from '../../../module/partner/component/CustomDropdownNoBorder';
import {useForm} from 'react-hook-form';
import {randomGID} from '../../../../utils/Utils';
import {TextFieldForm} from '../../../../project-component/component-form';
import CetificateAchievemenDa from '../CetificateAchievemenDa';
import {Snackbar} from 'react-native-paper';
import {showSnackbar} from '../../../../component/export-component';
import {ComponentStatus} from '../../../../component/component-status';
import {useNavigation} from '@react-navigation/native';

const OperationalStatisticsActionForCustomer: React.FC<{
  item: any;
  listToilet: any[];
}> = ({item, listToilet}) => {
  const [selectedOption, setSelectedOption] = useState('single');
  const navigate = useNavigation<any>();
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      toiletId: '',
      ElecComsume: '',
      WaterComsume: '',
      Probiotics: '',
      CleanTime: '',
      TimeAction: getToday(),
      DayAction: '',
      Note: '',
    },
  });

  function getToday() {
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0'); // Lấy ngày, thêm số 0 nếu < 10
    const month = String(today.getMonth() + 1).padStart(2, '0'); // Tháng trong JS tính từ 0
    const year = today.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Debug: Watch form values
  const watchedValues = methods.watch();
  useEffect(() => {
    console.log('item ,listToilet:', item, listToilet);
  }, []);

  useEffect(() => {
    if (item) {
      const toiletIdValue = item.Id || item.ToiletId || '';
      methods.setValue('toiletId', toiletIdValue);
      methods.setValue(
        'ElecComsume',
        item.ElectricityConsumption?.toString() || '',
      );
      methods.setValue('WaterComsume', item.WaterUsage?.toString() || '');
      methods.setValue('Probiotics', item.Bioproducts?.toString() || '');
      methods.setValue(
        'CleanTime',
        item.CleaningFrequency?.toString() ||
          item.Bioproducts?.toString() ||
          '',
      );
      if (item.DateStart) {
        const dateStart = new Date(item.DateStart);
        const formattedDate = `${dateStart.getDate().toString().padStart(2, '0')}/${(dateStart.getMonth() + 1).toString().padStart(2, '0')}/${dateStart.getFullYear()}`;

        // Find the option that matches this date
        const timeOptions = generateAllDaysWithCurrentTime();
        let matchingOption = timeOptions.find(
          option => option.name === formattedDate,
        );

        if (matchingOption) {
          methods.setValue('TimeAction', matchingOption.id);
        } else {
          // For now, set the formatted date directly as a fallback
          methods.setValue('TimeAction', formattedDate);
        }
      } else {
        methods.setValue('TimeAction', '');
      }

      // For DayAction - find matching option by date or create custom option
      if (item.DateDeclare) {
        const dateDeclare = new Date(item.DateDeclare);
        const formattedDate = `${dateDeclare.getDate().toString().padStart(2, '0')}/${(dateDeclare.getMonth() + 1).toString().padStart(2, '0')}/${dateDeclare.getFullYear()}`;

        // Find the option that matches this date
        const dayOptions = generateAllDaysWithCurrentTime();
        let matchingOption = dayOptions.find(
          option => option.name === formattedDate,
        );

        if (matchingOption) {
          methods.setValue('DayAction', matchingOption.id);
        } else {
          // For now, set the formatted date directly as a fallback
          methods.setValue('DayAction', formattedDate);
        }
      } else {
        methods.setValue('DayAction', '');
      }
      methods.setValue('Note', item.Note || '');
    } else {
      console.log('No item data available');
    }
  }, [item]);

  const onSubmit = async (data: any) => {
    // Get options to find selected values
    const options = generateAllDaysWithCurrentTime();

    // Find selected TimeAction and DayAction values
    const timeActionOption = options.find(
      option => option.id === data.TimeAction,
    );
    const dayActionOption = options.find(
      option => option.id === data.DayAction,
    );

    let dataUpdate: any[] = [];
    let response: any;
    if (data && data?.toiletId?.length > 0) {
      // Filter all toilets that match the selected toilet IDs
      let selectedToilets =
        listToilet?.filter((toilet: any) => {
          return data.toiletId && data.toiletId.includes(toilet.Id);
        }) || [];

      // Update all selected toilets with form data
      const updatedToilets = selectedToilets.map((toilet: any) => ({
        ...toilet, // Keep original toilet properties (Id, Name, Location, etc.)
        ElectricityConsumption: Number(data.ElecComsume) || 0,
        WaterUsage: Number(data.WaterComsume) || 0,
        Bioproducts: Number(data.Probiotics) || 0,
        CleaningFrequency: Number(data.CleanTime) || 0,
        DateCreated: data?.DateCreated,
        DateDeclare: dayActionOption
          ? dayActionOption.value
          : new Date().toISOString(),
        Note: data.Note ?? '',
        Status: 2,
      }));

      // Prepare data for API calls
      dataUpdate = updatedToilets.map((toilet: any) => ({
        Id: toilet.Id,
        ToiletId: toilet.ToiletId || toilet.Id,
        Name: toilet.Name,
        ElectricityConsumption: Number(toilet.ElectricityConsumption),
        WaterUsage: Number(toilet.WaterUsage),
        Bioproducts: Number(toilet.Bioproducts),
        CleaningFrequency: Number(toilet.CleaningFrequency),
        DateCreated: data?.DateCreated,
        DateDeclare: new Date(toilet.DateDeclare).getTime(),
        Note: toilet.Note,
        Status: toilet.Status,
      }));
    }
    if (dataUpdate) {
      response = await CetificateAchievemenDa.editOperationalData(dataUpdate);
      if (response?.code === 200) {
        showSnackbar({
          message: 'Cập nhật thông tin thành công',
          status: ComponentStatus.SUCCSESS,
        });
        // Get toilet IDs from the updated data
        const toiletIds = dataUpdate.map(item => item.ToiletId || item.Id);
        const nameToilets = dataUpdate.map(item => item.Name);
        await CetificateAchievemenDa.getLogByToiletId(
          toiletIds,
          `Thực hiện đánh giá ${nameToilets.join(', ')} thành công`,
        );
        navigate.goBack();
      }
    }
  };

  const renderStatus = () => {
    if (item?.Status === 2) {
      return (
        <View style={styles.approvalSection}>
          <Text style={styles.approvalLabel}>Trạng thái phê duyệt</Text>

          {item?.ApproveStatus === 2 ? (
            <TouchableOpacity style={styles.approvalButton}>
              <Text style={styles.approvalButtonText}>Đã duyệt</Text>
            </TouchableOpacity>
          ) : null}

          {item?.ApproveStatus === 1 ? (
            <TouchableOpacity style={styles.RejectButton}>
              <Text style={styles.RejectButtonText}>Từ chối</Text>
            </TouchableOpacity>
          ) : null}

          {!item?.ApproveStatus ? (
            <TouchableOpacity style={styles.RejectButton}>
              <Text style={styles.RejectButtonText}>Chưa phê duyệt</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      );
    }
  };

  const renderStatusSuccess = () => {
    if (item?.Status === 2) {
      return (
        <View style={styles.approvalSection}>
          <Text style={styles.approvalLabel}>Trạng thái thực hiện</Text>

          {item?.Status === 2 ? (
            <TouchableOpacity style={styles.approvalButton}>
              <Text style={styles.approvalButtonText}>Đã duyệt</Text>
            </TouchableOpacity>
          ) : null}

          {item?.Status === 1 ? (
            <TouchableOpacity style={styles.RejectButton}>
              <Text style={styles.RejectButtonText}>Từ chối</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      );
    }
  };

  const renderReasonCancel = () => {
    // Only show reason cancel section if ApproveStatus is 1 (rejected)
    if (item?.ApproveStatus !== 1) {
      return null;
    }

    return (
      <View>
        <Text style={styles.sectionTitleCancel}>Lý do từ chối</Text>
        <View style={styles.notesContainerCancel}>
          <Text style={styles.notesTextCancel}>{item?.Reason || ''}</Text>
        </View>
      </View>
    );
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };
  // Generate all days with current time (including past dates)
  const generateAllDaysWithCurrentTime = () => {
    const options = [];
    const currentDate = new Date();
    const currentHour = currentDate.getHours();
    const currentMinute = currentDate.getMinutes();
    // Generate options for the last 5 days (today and 4 previous days)
    for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
      const date = new Date();
      date.setDate(currentDate.getDate() - dayOffset);
      date.setHours(currentHour, currentMinute, 0, 0);
      const formattedDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
      // Create option with current time for each day
      options.push({
        id: `${formattedDate}`,
        name: `${formattedDate}`,
        value: date.toISOString(),
      });
    }

    return options;
  };
  const renderInfoSection = () => {
    // ✅ Kiểm tra status để disable form inputs
    const isFormDisabled = item?.Status === 2; // Ví dụ: Status 2 = Approved, Status 3 = Completed
    const isReadOnly = isFormDisabled;
    return (
      <View style={styles.infoSection}>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.toiletIcon} size={24} />
          </View>
          <Text style={styles.infoLabel}>Nhà vệ sinh</Text>
          <View style={styles.dropdownWrapper}>
            {selectedOption === 'multiple' ? (
              <CustomDropdownNoBorderWithMutiSelect
                control={methods.control}
                errors={methods.formState.errors}
                required
                placeholder="Chọn nhà vệ sinh"
                name="toiletId"
                icon={iconSvg.dropDown}
                style={{
                  borderBottomWidth: 0.5,
                  borderBottomColor: '#4CAF50',
                }}
                options={
                  listToilet?.map((toilet: any) => ({
                    id: toilet.Id,
                    name: toilet.Name,
                  })) || []
                }
              />
            ) : (
              <CustomDropdownNoBorder
                control={methods.control}
                errors={methods.formState.errors}
                required
                placeholder="Chọn nhà vệ sinh"
                name="toiletId"
                icon={iconSvg.dropDown}
                style={{
                  borderBottomWidth: 0.5,
                  borderBottomColor: '#4CAF50',
                }}
                options={
                  listToilet?.map((toilet: any) => ({
                    id: toilet.Id || toilet.ToiletId,
                    name: toilet.Name,
                  })) || []
                }
              />
            )}
          </View>
        </View>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.electronic} size={24} />
          </View>
          <Text style={styles.infoLabel}>Điện năng tiêu thụ</Text>
          <View style={styles.inputContainer}>
            <TextFieldForm
              textFieldStyle={{
                padding: 10,
                borderWidth: 0,
                borderBottomWidth: 1,
                backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
                opacity: isReadOnly ? 0.6 : 1,
              }}
              textStyle={{
                fontSize: 12,
                color: isReadOnly ? '#999' : '#333',
              }}
              placeholder="Nhập nội dung"
              style={{width: '80%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="ElecComsume"
              required
              disabled={isReadOnly}
            />
            <Text style={styles.unitText}>kWh</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.water} size={24} />
          </View>
          <Text style={styles.infoLabel}>Nước sử dụng</Text>
          <View style={styles.inputContainer}>
            <TextFieldForm
              textFieldStyle={{
                padding: 10,
                borderWidth: 0,
                borderBottomWidth: 1,
                backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
                opacity: isReadOnly ? 0.6 : 1,
              }}
              textStyle={{
                fontSize: 12,
                color: isReadOnly ? '#999' : '#333',
              }}
              placeholder="Nhập nội dung"
              style={{width: '80%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="WaterComsume"
              required
              disabled={isReadOnly}
            />
            <Text style={styles.unitText}>m3</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.biotics} size={24} />
          </View>
          <Text style={styles.infoLabel}>Chế phẩm sinh học</Text>
          <View style={styles.inputContainer}>
            <TextFieldForm
              textFieldStyle={{
                padding: 10,
                borderWidth: 0,
                borderBottomWidth: 1,
                backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
                opacity: isReadOnly ? 0.6 : 1,
              }}
              textStyle={{
                fontSize: 12,
                color: isReadOnly ? '#999' : '#333',
              }}
              placeholder="Nhập nội dung"
              style={{width: '80%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Probiotics"
              required
              disabled={isReadOnly}
            />
            <Text style={styles.unitText}>ml</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.cleaningTime} size={24} />
          </View>
          <Text style={styles.infoLabel}>Số lần vệ sinh lau dọn</Text>
          <View style={styles.inputContainer}>
            <TextFieldForm
              textFieldStyle={{
                padding: 10,
                borderWidth: 0,
                borderBottomWidth: 1,
                backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
                opacity: isReadOnly ? 0.6 : 1,
              }}
              textStyle={{
                fontSize: 12,
                color: isReadOnly ? '#999' : '#333',
              }}
              placeholder="Nhập nội dung"
              style={{width: '80%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="CleanTime"
              required
              disabled={isReadOnly}
            />
            <Text style={styles.unitText}>lần</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.calendar2} size={24} />
          </View>
          <Text style={styles.infoLabel}>Thời hạn thực hiện</Text>
          <View style={styles.valueContainer}>
            <TextFieldForm
              textFieldStyle={{
                padding: 10,
                borderWidth: 0,
                borderBottomWidth: 1,
                backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
                opacity: isReadOnly ? 0.6 : 1,
              }}
              textStyle={{
                fontSize: 12,
                color: isReadOnly ? '#999' : '#333',
              }}
              placeholder="Chọn thời gian"
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="TimeAction"
              required
              disabled={true}
            />
          </View>
        </View>
        <View style={[styles.infoRow]}>
          <View style={styles.iconContainer}>
            <AppSvg SvgSrc={iconSvg.calendar2} size={24} />
          </View>
          <Text style={styles.infoLabel}>Ngày thực hiện</Text>
          <View style={styles.valueContainer}>
            <TextFieldForm
              textFieldStyle={{
                padding: 10,
                borderWidth: 0,
                borderBottomWidth: 1,
                backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
                opacity: isReadOnly ? 0.6 : 1,
              }}
              textStyle={{
                fontSize: 12,
                color: isReadOnly ? '#999' : '#333',
              }}
              placeholder={getToday()}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="DayAction"
              disabled={true}
            />
          </View>
        </View>
      </View>
    );
  };
  const renderNote = () => {
    // ✅ Kiểm tra status để disable form inputs
    const isFormDisabled = item?.Status === 2 || item?.Status === 3;
    const isReadOnly = isFormDisabled;

    return (
      <View>
        <Text style={styles.sectionTitle}>Ghi chú</Text>
        <View style={styles.notesContainer}>
          <TextFieldForm
            textFieldStyle={{
              padding: 10,
              height: 150,
              backgroundColor: isReadOnly ? '#f5f5f5' : 'transparent',
              opacity: isReadOnly ? 0.6 : 1,
            }}
            textStyle={{
              fontSize: 12,
              color: isReadOnly ? '#999' : '#333',
            }}
            placeholder="Ghi chú thêm về tình hình nhà vệ sinh"
            style={{flex: 1}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            name="Note"
            multiline
            disabled={isReadOnly}
          />
        </View>
      </View>
    );
  };
  const buttonAction = () => {
    return (
      <View style={[styles.buttonActionContainer]}>
        <TouchableOpacity
          style={styles.rejectButton}
          onPress={() => console.log('Lưu nhập pressed')}>
          <Text style={styles.rejectButtonText}>Lưu nháp</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.approveButton}
          onPress={() => methods.handleSubmit(onSubmit, _onError)()}>
          <Text style={styles.approveButtonText}>Hoàn thành</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContentContainer}
        showsVerticalScrollIndicator={false}
        bounces={true}
        scrollEventThrottle={16}>
        {/* Header */}
        <TouchableOpacity style={styles.headerContainer}>
          <View>
            <Text style={styles.headerTitle}>
              Báo cáo thống kê vận hành nhiều nhà vệ sinh
            </Text>
            <Text style={styles.headerSubtitle}>
              Vui lòng điền đầy đủ số liệu mà nhà vệ sinh tiêu thụ trong tháng.
            </Text>
          </View>
          {/* Toilet Info Section */}
          {renderInfoSection()}
          {/* Notes Section */}
          {renderNote()}
          {renderStatus()}
          {renderReasonCancel()}
          {renderStatusSuccess()}
        </TouchableOpacity>
      </ScrollView>
      {/* Fixed buttons at bottom */}
      {item?.Status == 1 && buttonAction()}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContentContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 100, // Space for fixed buttons at bottom
  },
  headerContainer: {
    marginBottom: 24,
  },
  headerTitle: {
    ...TypoSkin.highlight6,
    fontWeight: '700',
    marginBottom: 8,
    lineHeight: 28,
  },
  headerSubtitle: {
    ...TypoSkin.buttonText5,
    fontWeight: '400',
    color: '#757575',
    lineHeight: 20,
  },
  infoSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoLabel: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dropdownWrapper: {
    flex: 1,
    marginLeft: 8,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 120,
  },
  dropdownText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  chevronIcon: {
    // Animation will be handled by React Native's default behavior
  },
  chevronRotated: {
    transform: [{rotate: '180deg'}],
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 200, // Changed from fixed height to maxHeight
    zIndex: 1001,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  unitText: {
    ...TypoSkin.buttonText5,
    color: '#666',
  },
  sectionTitle: {
    ...TypoSkin.buttonText2,
  },
  notesContainer: {
    borderRadius: 8,
    marginBottom: 20,
    marginTop: 10,
  },
  notesText: {
    ...TypoSkin.buttonText3,
    height: 80,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  approvalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
  },
  approvalLabel: {
    ...TypoSkin.buttonText2,
    fontWeight: '600',
  },
  approvalButton: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },

  inputContainer: {
    flex: 1,
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },

  buttonActionContainer: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    marginBottom: 30,
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#E5E5E5',
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rejectButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '600',
  },
  approveButton: {
    flex: 1,
    backgroundColor: '#4CAF50',
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  approveButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  radioContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 24,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  radioSelected: {
    borderColor: '#4CAF50',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4CAF50',
  },
  radioText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  approvalButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  RejectButton: {
    backgroundColor: '#FFCCCC',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  RejectButtonText: {
    ...TypoSkin.title4,
    fontWeight: '500',
    color: 'red',
  },
  sectionTitleCancel: {
    ...TypoSkin.buttonText2,
    marginBottom: 12,
  },
  notesContainerCancel: {
    borderRadius: 8,
    marginBottom: 20,
    marginTop: 10,
    backgroundColor: '#f8f8f8',
  },
  notesTextCancel: {
    ...TypoSkin.buttonText3,
    height: 80,
    color: ColorThemes.light.neutral_text_subtitle_color,
    padding: 12,
  },
});

export default OperationalStatisticsActionForCustomer;
