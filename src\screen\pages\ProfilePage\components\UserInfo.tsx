import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  Pressable,
} from 'react-native';
import {ComponentStatus, showSnackbar, Winicon} from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import {useDispatch} from 'react-redux';
import ImagePicker from 'react-native-image-crop-picker';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {BaseDA} from '../../../baseDA';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {CustomerRole} from '../../../../redux/reducers/user/da';
import ConfigAPI from '../../../../config/configApi';

const UserInfo = () => {
  const user = useSelectorCustomerState();
  const customer = user.data;
  const dispatch = useDispatch<any>();

  const [avt, setAvt] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [role, setRole] = useState('');

  useEffect(() => {
    if (user?.role && !user.data?.CompanyProfileId) {
      const listRole = user?.role?.Role.split(',').map(e => {
        switch (e) {
          case CustomerRole.Consultant:
            return 'Tư vấn/khảo sát viên';
          case CustomerRole.Coordinator:
            return 'Điều phối viên';
          case CustomerRole.SCBD:
            return 'Nhân viên sửa chữa bảo dưỡng';
          case CustomerRole.VSLD:
            return 'Nhân viên vệ sinh lau dọn';
          case CustomerRole.KTX:
            return 'Lãnh đạo KTX';
          default:
            return '';
        }
      });
      if (listRole) {
        if (listRole?.length > 1) {
          setRole(`${listRole[0]},+${listRole.length}`);
        } else {
          setRole(listRole[0]);
        }
      }
    }
  }, [customer]);

  useEffect(() => {
    if (customer) {
      setAvt(customer.AvatarUrl);
    }
  }, [customer]);

  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: true,
      cropperCircleOverlay: true,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        setIsLoading(true);
        await dispatch(
          CustomerActions.edit(dispatch, {
            ...customer,
            AvatarUrl: resImgs[0].Id,
          }),
        ).then(() => {
          setAvt(resImgs[0].Id);
          setIsLoading(false);
          showSnackbar({
            message: 'Cập nhật ảnh đại diện thành công',
            status: ComponentStatus.SUCCSESS,
            bottom: 60,
          });
          dispatch(CustomerActions.getInfor(dispatch));
        });
      }
    }
  };
  const renderAvatarContent = () => {
    if (avt) {
      return <FastImage source={{uri: avt}} style={styles.avatarImage} />;
    }

    return (
      <View style={styles.avatarPlaceholder}>
        <Text style={styles.avatarText}>
          {customer?.Name ? customer.Name.charAt(0).toUpperCase() : ''}
        </Text>
      </View>
    );
  };

  return (
    <Pressable style={styles.userInfo}>
      {customer ? (
        <View style={styles.container}>
          <View style={styles.avatarContainer}>
            <TouchableOpacity
              style={styles.avatarTouchable}
              onPress={customer ? pickerImg : undefined}>
              {renderAvatarContent()}
              <View style={styles.cameraIconContainer}>
                <Winicon
                  src="fill/entertainment/camera"
                  size={10}
                  color="#000"
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.userInfoContainer}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Text style={styles.userName}>
                {customer?.Name ?? customer.Email ?? role ?? ''}
              </Text>
            </View>
          </View>
        </View>
      ) : (
        <View />
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  userInfo: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    gap: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  avatarTouchable: {
    width: 80,
    height: 80,
    borderRadius: 100,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  avatarText: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
  },
  cameraIconContainer: {
    position: 'absolute',
    padding: 5,
    borderRadius: 24,
    backgroundColor: '#fff',
    right: -2,
    bottom: -2,
  },
  userInfoContainer: {
    gap: 4,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  userName: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
});

export default UserInfo;
