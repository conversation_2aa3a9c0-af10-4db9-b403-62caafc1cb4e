import {
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Pressable,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import OTPInput from '../../../login/components/input-otp';
import {showSnackbar, Winicon} from '../../../../../component/export-component';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {useCallback, useEffect, useState} from 'react';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import AppButton from '../../../../../component/button';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../../router/router';
import {CustomerActions} from '../../../../../redux/reducers/user/reducer';
import {ComponentStatus} from '../../../../../component/component-status';
import {Ultis} from '../../../../../utils/Utils';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {
  confirmCode,
  signInWithPhoneFB,
} from '../../../../../features/otp-loginwFirebase/PhoneSignIn';
import {useDispatch} from 'react-redux';
import {getDataToAsyncStorage} from '../../../../../utils/AsyncStorage';
import LocalAuthen from '../../../../../features/local-authen/local-authen';

interface Props {
  methods?: any;
  display?: string;
  onSubmitResult?: () => Promise<void>;
  onSendOtp?: () => void;
  //   confirmationResult?: any;
  setLoading?: any;
}

export default function CheckOptStep(props: Props) {
  const {
    methods,
    display = 'flex',
    onSubmitResult,
    onSendOtp,
    // confirmationResult,
    setLoading,
  } = props;
  const [step, setStep] = useState(0);
  const [timer, setTimer] = useState(0);
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState();
  const dispatch = useDispatch<any>();
  const [loadingOTP, setLoadingOTP] = useState(false);
  const [allowErrorOpt, setAllowErrorOpt] = useState(0);
  const [biometric, setBiometric] = useState(false);
  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<any>(null);

  const timeOutCallback = useCallback(() => {
    setTimer(currTimer => currTimer - 1);
  }, []);

  useEffect(() => {
    if (timer > 0) setTimeout(timeOutCallback, 1000);
  }, [timer, timeOutCallback]);

  useEffect(() => {
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          setBiometric(result == 'true' ? true : false);
        });
      } else {
        setBiometric(false);
      }
    });
  }, []);

  // const submitOtp = async (value: string) => {
  //   setLoading(true);

  //   // if (__DEV__) {
  //   //   setLoading(false);
  //   //   if (onSubmitResult) onSubmitResult().then(() => setStep(1));
  //   //   return;
  //   // }
  //   if (confirm == null) {
  //     setLoading(false);
  //     return;
  //   }
  //   var rsotp = await confirmCode(confirm, value);

  //   if (rsotp === true) {
  //     console.log('===============otp done=====================');
  //     setLoading(false);
  //     if (onSubmitResult) onSubmitResult();
  //     setStep(1);
  //   } else {
  //     if (allowErrorOpt < 5) {
  //       setAllowErrorOpt(allowErrorOpt + 1);
  //     } else {
  //       const r = await CustomerActions.lockAccount(methods.watch('Mobile'));
  //       setTimeout(() => {
  //         CustomerActions.logout(dispatch, navigation);
  //       }, 2000);
  //       return showSnackbar({
  //         message: r.message,
  //         status: ComponentStatus.ERROR,
  //       });
  //     }
  //     setLoading(false);

  //     showSnackbar({
  //       message:
  //         5 - (allowErrorOpt + 1) == 0
  //           ? 'Mã xác thực không chính xác'
  //           : `Mã xác thực không chính xác, bạn còn ${5 - (allowErrorOpt + 1)} lần nhập lại`,
  //       status: ComponentStatus.ERROR,
  //     });
  //   }
  // };

  const [password, setPassword] = useState('');
  const [isVisiblePass, setVisiblePass] = useState(true);
  const handlePasswordConfirm = async () => {
    if (!password.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    const phone = customer?.data?.Mobile;

    if (!phone) {
      showSnackbar({
        message: 'Không tìm thấy số diện thoại',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    Keyboard?.dismiss();
    try {
      const res = await CustomerActions.checkPassword(phone || '', password);

      if (res?.code === 200) {
        console.log('===============pass done=====================');
        setLoading(false);
        if (onSubmitResult) onSubmitResult();
        setStep(1);
        setAllowErrorOpt(0);
        return;
      } else {
        if (allowErrorOpt < 5) {
          setAllowErrorOpt(allowErrorOpt + 1);
        } else {
          const r = await CustomerActions.lockAccount(phone);
          setTimeout(() => {
            CustomerActions.logout(dispatch, navigation);
          }, 2000);
          return showSnackbar({
            message: r.message,
            status: ComponentStatus.ERROR,
          });
        }
        setLoading(false);

        showSnackbar({
          message:
            5 - (allowErrorOpt + 1) == 0
              ? 'Mật khẩu không chính xác'
              : `Mật khẩu không chính xác, bạn còn ${5 - (allowErrorOpt + 1)} lần nhập lại`,
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        padding: 16,
        display: display === 'flex' ? 'flex' : 'none',
        alignItems: 'center',
        gap: 16,
      }}>
      {step !== 0 ? (
        <Pressable
          style={{
            borderColor: 'transparent',
            alignItems: 'center',
            gap: 8,
            width: '100%',
          }}>
          <SkeletonImage
            source={{
              uri: 'https://file-mamager.wini.vn/Upload/2024/12/register success_03ff.png',
            }}
            style={{height: 150, width: 150}}
          />
          <Text
            style={[
              TypoSkin.heading5,
              {
                color: ColorThemes.light.neutral_text_title_color,
                paddingBottom: 4,
              },
            ]}>
            Thông tin đã gửi thành công
          </Text>
          <Text
            style={[
              TypoSkin.body3,
              {
                color: ColorThemes.light.neutral_text_body_color,
                paddingBottom: 8,
                textAlign: 'center',
              },
            ]}>
            Chúng tôi sẽ liên hệ lại trong 1 ngày làm việc.
          </Text>
          <AppButton
            title={'Xem đơn hàng'}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
            textStyle={{...TypoSkin.buttonText1, textAlign: 'center'}}
            containerStyle={{
              borderRadius: 8,
              paddingHorizontal: 16,
              marginBottom: 8,
            }}
            borderColor={ColorThemes.light.transparent}
            backgroundColor={ColorThemes.light.neutral_main_background_color}
            onPress={async () => {
              if (customer.data) {
                navigation.reset({
                  index: 0,
                  routes: [
                    {
                      name: RootScreen.navigateView,
                      params: {rootName: 'Tác vụ'},
                    },
                  ],
                });
                setTimeout(() => {
                  navigation.push(RootScreen.DetailWorkView, {
                    Id: methods.getValues('toiletServiceId'),
                  });
                }, 100);
                return;
              }
              const res = await CustomerActions.login(methods.watch('Mobile'));
              if (res.code === 200) {
                showSnackbar({
                  message: 'Đăng nhập thành công',
                  status: ComponentStatus.SUCCSESS,
                });
                Ultis.setStorage(
                  'timeRefresh',
                  `${Date.now() / 1000 + 9 * 60}`,
                );
                Ultis.setStorage('accessToken', `${res.data.accessToken}`);
                Ultis.setStorage('refreshToken', `${res.data.accessToken}`);
                await CustomerActions.getInfor(dispatch, navigation).then(
                  () => {
                    navigation.reset({
                      index: 0,
                      routes: [
                        {
                          name: RootScreen.navigateView,
                          params: {rootName: 'Tác vụ'},
                        },
                      ],
                    });
                    setTimeout(() => {
                      navigation.push(RootScreen.DetailWorkView, {
                        Id: methods.getValues('toiletServiceId'),
                      });
                    }, 100);
                  },
                );
              }
            }}
          />
          <AppButton
            title={customer.data ? 'Về trang chủ' : 'Thêm thông tin tài khoản'}
            textColor={ColorThemes.light.neutral_absolute_background_color}
            textStyle={{...TypoSkin.buttonText1, textAlign: 'center'}}
            containerStyle={{
              borderRadius: 8,
              paddingHorizontal: 16,
            }}
            borderColor={ColorThemes.light.transparent}
            backgroundColor={ColorThemes.light.primary_main_color}
            onPress={async () => {
              if (customer.data) {
                navigation.reset({
                  index: 0,
                  routes: [{name: RootScreen.navigateView}],
                });
                return;
              }
              const res = await CustomerActions.login(methods.watch('Mobile'));
              if (res.code === 200) {
                showSnackbar({
                  message: 'Đăng nhập thành công',
                  status: ComponentStatus.SUCCSESS,
                });
                Ultis.setStorage(
                  'timeRefresh',
                  `${Date.now() / 1000 + 9 * 60}`,
                );
                Ultis.setStorage('accessToken', `${res.data.accessToken}`);
                Ultis.setStorage('refreshToken', `${res.data.accessToken}`);
                await CustomerActions.getInfor(dispatch, navigation).then(
                  () => {
                    navigation.replace(RootScreen.navigateView);
                  },
                );
              }
            }}
          />
        </Pressable>
      ) : (
        <View style={{gap: 16}}>
          <Pressable
            style={{
              borderColor: 'transparent',
              alignItems: 'center',
              gap: 8,
              marginHorizontal: 16,
            }}>
            <Winicon src="outline/technology/mobile-chat" size={32} />
            <Text
              style={[
                TypoSkin.heading5,
                {
                  color: ColorThemes.light.neutral_text_title_color,
                  paddingBottom: 4,
                },
              ]}>
              Nhập mật khẩu của bạn
            </Text>
            {/* <Text
              style={[
                TypoSkin.body3,
                {
                  color: ColorThemes.light.neutral_text_body_color,
                  paddingBottom: 8,
                  textAlign: 'center',
                },
              ]}>
              {`Nhập mã otp được gửi đến số điện thoại"${methods.watch('Mobile')?.substring(0, 4)}xxxxxx"`}
            </Text> */}
          </Pressable>
          {/* <OTPInput
            autoFocus={false}
            length={6}
            isResend={false}
            // onReSendOtp={forService ? undefined : reSendingOtp}
            onSubmit={value => {
              submitOtp(value);
            }}
          />
          <AppButton
            title={
              loadingOTP
                ? 'Đang gửi mã...'
                : `Gửi mã xác thức ${timer > 0 ? timer : ''}${timer > 0 ? 's' : ''}`
            }
            disabled={allowErrorOpt > 5 || loadingOTP || timer > 0}
            textColor={ColorThemes.light.neutral_absolute_background_color}
            textStyle={{...TypoSkin.buttonText1}}
            containerStyle={{
              borderRadius: 8,
              marginVertical: 16,
              marginHorizontal: 32,
            }}
            borderColor={ColorThemes.light.neutral_main_border_color}
            backgroundColor={ColorThemes.light.primary_main_color}
            onPress={async () => {
              if (loadingOTP) return;
              var phoneNB = methods.watch('Mobile');
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(phoneNB)) {
                phoneNB = '0' + phoneNB; // Add 0 at the beginning
              }

              if (phoneNB.startsWith('0'))
                phoneNB = phoneNB.replace('0', '+84');

              setLoadingOTP(true);
              const rs = await signInWithPhoneFB(phoneNB);
              if (rs) {
                setLoadingOTP(false);
                setTimer(60);
                showSnackbar({
                  message: 'Đã gửi mã xác nhận đến số diện thoại đăng ký',
                  status: ComponentStatus.SUCCSESS,
                });
                setConfirm(rs);
              } else {
                setLoadingOTP(false);
              }
            }}
          /> */}

          {/* add check password */}
          <View
            style={{
              borderWidth: 1,
              borderColor: ColorThemes.light.neutral_text_subtitle_color,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <TextInput
              style={{
                flex: 1,
                height: 40,
                paddingBottom: 4,
                paddingHorizontal: 16,
                ...TypoSkin.body2,
                color: ColorThemes.light.neutral_text_body_color,
              }}
              placeholder="Nhập mật khẩu của bạn"
              placeholderTextColor={
                ColorThemes.light.neutral_text_disabled_color
              }
              value={password}
              onChangeText={setPassword}
              secureTextEntry={isVisiblePass}
              autoFocus={true}
            />
            <TouchableOpacity
              style={{padding: 12}}
              onPress={() => setVisiblePass(!isVisiblePass)}>
              <Winicon
                src={
                  isVisiblePass
                    ? `outline/user interface/view`
                    : `outline/user interface/hide`
                }
                size={14}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
            </TouchableOpacity>
          </View>

          <View
            style={{
              flexDirection: 'row',
              gap: 12,
            }}>
            <TouchableOpacity
              style={{
                flex: 1,
                height: 48,
                backgroundColor: ColorThemes.light.primary_main_color,
                borderRadius: 8,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={handlePasswordConfirm}>
              <Text
                style={{
                  ...TypoSkin.buttonText3,
                  color: ColorThemes.light.neutral_absolute_background_color,
                }}>
                Xác nhận
              </Text>
            </TouchableOpacity>
          </View>
          {/*  */}

          {biometric ? (
            <View
              style={{
                flexDirection: 'row',
                width: Dimensions.get('screen').width - 32,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <LocalAuthen
                sizeIcon={42}
                isFirstTime={false}
                onSuccess={async value => {
                  if (value === true) {
                    setLoading(false);
                    if (onSubmitResult) onSubmitResult();
                    setStep(1);
                    setAllowErrorOpt(0);
                    return;
                  }
                }}
              />
            </View>
          ) : null}
        </View>
      )}
    </KeyboardAvoidingView>
  );
}
