import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import {DataController} from '../../../screen/base-controller';

interface PartnerSimpleResponse {
  data?: any[];
  currentPartner?: any;
  onLoading?: boolean;
  error?: string | null;
}

const initState: PartnerSimpleResponse = {
  data: undefined,
  currentPartner: undefined,
  onLoading: false,
  error: null,
};

export const partnerSlice = createSlice({
  name: 'partner',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'FIND_ONE':
          state.data = action.payload.data;
          state.currentPartner = action.payload.data?.[0] || null;
          state.error = null;
          break;
        case 'GET_ALL':
          state.data = action.payload.data;
          state.error = null;
          break;
        case 'SET_CURRENT_PARTNER':
          state.currentPartner = action.payload.data;
          break;
        case 'SET_ERROR':
          state.error = action.payload.error;
          break;
        case 'CLEAR_ERROR':
          state.error = null;
          break;
        case 'RESET':
          state.data = undefined;
          state.currentPartner = undefined;
          state.error = null;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
    onReset: state => {
      state.data = undefined;
      state.currentPartner = undefined;
      state.onLoading = false;
      state.error = null;
    },
  },
});

const {handleActions, onFetching, onReset} = partnerSlice.actions;

export default partnerSlice.reducer;

export class PartnerActions {
  static findOne = async (
    dispatch: Dispatch<UnknownAction>,
    config: {
      page?: number;
      size?: number;
      sortby?: any;
      query?: string;
    },
  ) => {
    try {
      dispatch(onFetching());
      const controller = new DataController('Shop');
      const res = await controller.getListSimple(config);

      if (res.code === 200) {
        dispatch(
          handleActions({
            type: 'FIND_ONE',
            data: res.data,
          }),
        );
        return res.data;
      } else {
        dispatch(
          handleActions({
            type: 'SET_ERROR',
            error: res.message || 'Không thể tìm thấy đối tác',
          }),
        );
        return [];
      }
    } catch (error: any) {
      dispatch(
        handleActions({
          type: 'SET_ERROR',
          error: error.message || 'Có lỗi xảy ra khi tìm đối tác',
        }),
      );
      return [];
    }
  };

  static getAll = async (
    dispatch: Dispatch<UnknownAction>,
    config: {
      page?: number;
      size?: number;
      sortby?: any;
      query?: string;
    },
  ) => {
    try {
      dispatch(onFetching());
      const controller = new DataController('Shop');
      const res = await controller.aggregateList(config);

      if (res.code === 200) {
        dispatch(
          handleActions({
            type: 'GET_ALL',
            data: res.data,
          }),
        );
        return res.data;
      } else {
        dispatch(
          handleActions({
            type: 'SET_ERROR',
            error: res.message || 'Không thể lấy danh sách đối tác',
          }),
        );
        return [];
      }
    } catch (error: any) {
      dispatch(
        handleActions({
          type: 'SET_ERROR',
          error: error.message || 'Có lỗi xảy ra',
        }),
      );
      return [];
    }
  };

  static setCurrentPartner = (
    dispatch: Dispatch<UnknownAction>,
    partner: any,
  ) => {
    dispatch(
      handleActions({
        type: 'SET_CURRENT_PARTNER',
        data: partner,
      }),
    );
  };

  static reset = (dispatch: Dispatch<UnknownAction>) => {
    dispatch(
      handleActions({
        type: 'RESET',
      }),
    );
  };

  static clearError = (dispatch: Dispatch<UnknownAction>) => {
    dispatch(
      handleActions({
        type: 'CLEAR_ERROR',
      }),
    );
  };
}
