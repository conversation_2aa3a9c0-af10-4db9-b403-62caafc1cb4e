import {
  Dimensions,
  FlatList,
  Linking,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {GroupDA} from '../groups/da';
import ActionSheet, {ActionSheetOption} from '../components/ActionSheet';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {
  showBottomSheet,
  hideBottomSheet,
  FBottomSheet,
  Winicon,
  FTextField,
} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import ConfigAPI from '../../../../config/configApi';
import {FollowStatus} from '../../../../config/Contanst';
import EmptyPage from '../../../../project-component/empty-page';
import {SkeletonImage} from '../../../../project-component/skeleton-img';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {CustomerDA} from '../../../../redux/reducers/user/da';
import {navigate, RootScreen} from '../../../../router/router';

export default function Friends(pros: any) {
  const [searchValue, setSearchValue] = useState('');
  // const currentUser = useSelectorCustomerState().data;
  const [listFriend, setlistFriend] = useState<Array<any>>([]);

  const customerDA = new CustomerDA();
  const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const [currentActions, setCurrentActions] = useState<ActionSheetOption[]>([]);
  useEffect(() => {
    if (searchValue) {
      // Lọc danh sách thành viên theo tên
      const filteredMembers = listFriend.filter((member: any) =>
        member.Name.toLowerCase().includes(searchValue.toLowerCase()),
      );
      setlistFriend({...filteredMembers});
    } else {
      // Hiển thị danh sách thành viên ban đầu
      setlistFriend(listFriend);
    }
  }, [searchValue]);

  useEffect(() => {
    getListFriend();
  }, []);

  const getListFriend = async () => {
    if (!pros.profile) return;
    const result = await customerDA.getListFriend(pros.profile.Id);
    if (result) {
      setlistFriend(result);
    }
  };

  const openSheetWithActions = (actions: ActionSheetOption[]) => {
    setCurrentActions(actions);
    // // Delay 1 frame để chắc chắn actions được cập nhật trước khi mở
    // requestAnimationFrame(() => {
    //   SheetRef.current?.open();
    // });
    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,
      title: 'Actions',
      children: (
        <View
          style={{
            height: Dimensions.get('window').height / 5,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <ActionSheet actions={actions} onSelect={handleSelect} />
        </View>
      ),
    });
  };
  const handleSelect = (key: string, payload: any) => {
    console.log('Selected:', key);
    switch (key) {
      case 'addfriend':
        customerDA.Acceptfollow(payload.Id).then((res: any) => {
          if (res) {
            setlistFriend(
              listFriend.map((a: any) => {
                if (a.Id === payload.Id)
                  return {...a, Status: FollowStatus.Accept};
                return a;
              }),
            );
          }
        });
        break;
      case 'cancelfriend':
        customerDA.unfollow(payload.Id).then((res: any) => {
          if (res) {
            setlistFriend(listFriend.filter((a: any) => a.Id !== payload.Id));
          }
        });
        break;
      default:
        break;
    }
    hideBottomSheet(bottomSheetRef);
  };
  return (
    <View
      style={{
        height: Dimensions.get('window').height - 200,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      {/* <ActionSheet
        ref={SheetRef}
        actions={currentActions}
        onSelect={handleSelect}
      /> */}
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          height: 56,
          gap: 8,
          padding: 16,
        }}>
        <FTextField
          style={{paddingHorizontal: 16, flex: 1, height: 40}}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
          }}
          value={searchValue}
          placeholder="Tìm kiếm..."
          prefix={
            <Winicon
              src="outline/development/zoom"
              size={14}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
          }
        />
      </View>
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <FlatList
          data={listFriend}
          scrollEnabled={pros.scrollEnabled}
          style={{
            height: '100%',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}
          keyExtractor={(item, index) => index.toString()}
          ListEmptyComponent={() => {
            return <EmptyPage title="Không có dữ liệu" />;
          }}
          renderItem={({item}) => {
            return (
              <ListTile
                key={item?.Id}
                onPress={() => {
                  navigate(RootScreen.ProfileCommunity, {Id: item?.Id});
                }}
                listtileStyle={{gap: 8}}
                leading={
                  item?.AvatarUrl ? (
                    <SkeletonImage
                      key={item?.AvatarUrl}
                      source={{
                        uri: item?.AvatarUrl
                          ? item?.AvatarUrl.includes('http')
                            ? item?.AvatarUrl
                            : `${ConfigAPI.imgUrlId + item?.AvatarUrl}`
                          : 'https://placehold.co/48/FFFFFF/000000/png',
                      }}
                      style={{
                        width: 48,
                        height: 48,
                        borderRadius: 50,
                        backgroundColor: '#f0f0f0',
                      }}
                    />
                  ) : (
                    <View
                      style={{
                        width: 48,
                        height: 48,
                        borderRadius: 50,
                        backgroundColor: ColorThemes.light.primary_main_color,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.heading7,
                          color:
                            ColorThemes.light.neutral_absolute_background_color,
                        }}>
                        {item?.Name ? item.Name.charAt(0).toUpperCase() : 'U'}
                      </Text>
                    </View>
                  )
                }
                title={
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}
                    numberOfLines={1}>
                    {item?.Name}
                  </Text>
                }
                subTitleStyle={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}
                trailing={
                  <View
                    style={{
                      alignItems: 'center',
                      flexDirection: 'row',
                      gap: 8,
                    }}>
                    {item.Status === FollowStatus.Pending &&
                    pros.profile.Id === currentUser?.Id ? (
                      <>
                        <TouchableOpacity
                          style={{padding: 4}}
                          onPress={() => {
                            customerDA
                              .Acceptfollow(item?.Id)
                              .then((res: any) => {
                                if (res) {
                                  setlistFriend(
                                    listFriend.map((a: any) => {
                                      if (a.Id === item.Id)
                                        return {
                                          ...a,
                                          Status: FollowStatus.Accept,
                                        };
                                      return a;
                                    }),
                                  );
                                }
                              });
                          }}>
                          <Winicon
                            src="fill/layout/circle-half-dotted-check"
                            color={ColorThemes.light.success_main_color}
                            size={20}
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{padding: 4}}
                          onPress={() => {
                            customerDA.unfollow(item?.Id).then((res: any) => {
                              if (res) {
                                setlistFriend(
                                  listFriend.filter(
                                    (a: any) => a.Id !== item.Id,
                                  ),
                                );
                              }
                            });
                          }}>
                          <Winicon
                            src="outline/layout/circle-xmark"
                            color={ColorThemes.light.error_main_color}
                            size={20}
                          />
                        </TouchableOpacity>
                      </>
                    ) : null}
                    {item.Status === FollowStatus.Accept &&
                    pros.profile.Id === currentUser?.Id ? (
                      <TouchableOpacity
                        style={{padding: 4}}
                        onPress={() => {
                          openSheetWithActions([
                            {
                              key: 'cancelfriend',
                              label: 'Hủy kết bạn',
                              payload: item,
                              icon: (
                                <Winicon
                                  src="fill/users/user-delete-line"
                                  size={16}
                                  color={ColorThemes.light.error_main_color}
                                />
                              ),
                            },
                          ]);
                        }}>
                        <Winicon
                          src="fill/editing/dots"
                          color={ColorThemes.light.neutral_text_subtitle_color}
                          size={16}
                        />
                      </TouchableOpacity>
                    ) : null}
                  </View>
                }
              />
            );
          }}
        />
      </View>
    </View>
  );
}

export const SkeletonFriendCard = () => {
  return (
    <SkeletonPlaceholder
      backgroundColor="#f0f0f0"
      highlightColor="#e0e0e0"
      speed={800}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        {/* Avatar placeholder */}
        <View
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            marginRight: 12,
          }}
        />

        {/* Title and subtitle container */}
        <View style={{flex: 1, gap: 8}}>
          {/* Title placeholder */}
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
            }}
          />

          {/* Subtitle placeholder */}
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Icons container */}
        <View style={{flexDirection: 'row', gap: 8}}>
          {/* Phone icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />

          {/* Chat icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
