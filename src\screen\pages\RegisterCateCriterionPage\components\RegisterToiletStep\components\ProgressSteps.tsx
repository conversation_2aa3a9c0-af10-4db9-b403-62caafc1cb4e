import React from 'react';
import {View, StyleSheet, ViewStyle} from 'react-native';

interface ProgressStepsProps {
  step: number;
  totalSteps?: number;
  style?: ViewStyle;
  activeColor?: string;
  inactiveColor?: string;
}

export default function ProgressSteps({
  step,
  totalSteps = 3,
  style,
  activeColor = '#4CAF50',
  inactiveColor = '#E0E0E0',
}: ProgressStepsProps) {
  const getStepColor = (stepIndex: number): string => {
    return stepIndex <= step ? activeColor : inactiveColor;
  };

  const stepIndices = Array.from({length: totalSteps}, (_, index) => index + 1);

  return (
    <View style={[styles.container, style]}>
      {stepIndices.map(stepIndex => (
        <View
          key={stepIndex}
          style={[
            styles.progressBar,
            {backgroundColor: getStepColor(stepIndex)},
          ]}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 16,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});
