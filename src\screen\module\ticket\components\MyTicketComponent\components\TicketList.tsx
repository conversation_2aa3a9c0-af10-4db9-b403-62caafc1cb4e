import React from 'react';
import {View, FlatList, RefreshControl, StyleSheet} from 'react-native';
import {TicketType as TicketTypeInterface} from 'types/ticketType';
import TicketListItem from './TicketListItem';
import EmptyTicketState from './EmptyTicketState';

interface TicketListProps {
  data: any[];
  relatives: any[];
  methods: any;
  isRefreshing: boolean;
  onRefresh: () => void;
  onUpdateTicket: (updatedTicket: TicketTypeInterface) => void;
  getTypeLabel: (type: number) => string;
}

const TicketList: React.FC<TicketListProps> = ({
  data,
  relatives,
  methods,
  isRefreshing,
  onRefresh,
  onUpdateTicket,
  getTypeLabel,
}) => {
  const renderItem = ({item, index}: any) => {
    const typeLabel = getTypeLabel(item.Type);

    return (
      <TicketListItem
        item={item}
        index={index}
        relatives={relatives}
        methods={methods}
        typeLabel={typeLabel}
        onUpdateTicket={onUpdateTicket}
      />
    );
  };

  const renderFooter = () => <View style={styles.footer} />;

  return (
    <FlatList
      data={data}
      scrollEnabled={false}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      style={styles.flatList}
      renderItem={renderItem}
      ListEmptyComponent={EmptyTicketState}
      ListFooterComponent={renderFooter}
    />
  );
};

const styles = StyleSheet.create({
  flatList: {
    flex: 1,
    marginHorizontal: 16,
    marginVertical: 16,
  },
  separator: {
    height: 16,
  },
  footer: {
    height: 100,
  },
});

export default TicketList;
