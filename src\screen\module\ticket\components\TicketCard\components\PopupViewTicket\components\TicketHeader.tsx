import React from 'react';
import {Text, View, StyleSheet} from 'react-native';
import ScreenHeader from 'screen/layout/header';
import {Winicon} from 'component/export-component';
import {ColorThemes} from 'assets/skin/colors';

interface TicketHeaderProps {
  item: any;
  onClose: () => void;
}

export const TicketHeader: React.FC<TicketHeaderProps> = ({item, onClose}) => {
  return (
    <ScreenHeader
      style={styles.header}
      title={item ? `${item?.Name}` : `Xem chi tiết`}
      prefix={<View />}
      action={
        <View style={styles.headerAction}>
          <Winicon
            src="outline/layout/xmark"
            onClick={onClose}
            size={20}
            color={ColorThemes.light.neutral_text_body_color}
          />
        </View>
      }
    />
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  headerAction: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
});
