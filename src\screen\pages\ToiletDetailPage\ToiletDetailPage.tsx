import React, {useEffect} from 'react';
import {View} from 'react-native';
import TitleHeader from '../../layout/headers/TitleHeader';
import {useRoute} from '@react-navigation/native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScrollOptionToilet from './components/ScrollOptionToilet';
import General from './components/General';
import Work from './components/Work';
import {ToiletDa} from '../../module/toilet/toiletDa';
import {DataController} from '../../base-controller';
import CalendarTab from '../../module/toilet/view/detailProject/calendarTab';
import FilesList from '../../module/toilet/view/detailProject/filesList';
import ContractToiletListTab from '../../module/toilet/view/detailProject/ToiletListContractTab';
import NoteTab from '../../module/toilet/view/detailProject/noteTab';
import {ToiletServiceStatus} from '../../module/service/components/da';
import EmptyPage from '../../../project-component/empty-page';
import CetificateAchievementPage from '../CetificateAchievementPage/CetificateAchievementPage';
import ToiletListBioProductTab from '../../module/toilet/view/detailProject/listBioTab';
import DevicesListTab from '../../module/toilet/view/detailProject/devicesListTab';
import {SafeAreaView} from 'react-native-safe-area-context';
const ToiletDetailPage: React.FC<{}> = () => {
  const router = useRoute<any>();
  const [tabId, setTabId] = React.useState('');
  const [tasks, setTasks] = React.useState<any[]>([]);
  const [workData, setWorkData] = React.useState<any>();
  const [serviceData, setServiceData] = React.useState();
  const [isRefreshing, setRefreshing] = React.useState(false);

  const toiletDa = new ToiletDa();
  useEffect(() => {
    setTabId('1');
  }, []);

  const fetchAllTaskByToiletId = async () => {
    if (router.params && router.params.item && router.params.item.Id) {
      const tasks = await toiletDa.fetchAllTaskByToiletId(
        router.params.item.Id,
      );
      if (tasks?.length > 0) {
        setTasks(tasks);
      } else {
        setTasks([]);
      }
    }
  };
  useEffect(() => {
    if (tabId == '3') fetchAllTaskByToiletId();
  }, [tabId]);

  const getData = async () => {
    if (!router.params.item.Id) return;
    var id = router.params.item.Id;
    const servicesController = new DataController('ToiletServices');
    const controller = new DataController('Toilet');
    const serviceRes = await servicesController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@ToiletId:{${id}} -@Status:[${ToiletServiceStatus.reject} ${ToiletServiceStatus.reject}]`,
    });
    if (serviceRes.code === 200 && serviceRes.data.length)
      setServiceData(serviceRes.data[0]);
    const res = await controller.getById(id);
    if (res.code === 200 && res.data) setWorkData(res.data);
  };

  useEffect(() => {
    getData();
  }, [router?.params?.item?.Id]);

  const onRefreshing = async () => {
    await getData().then(() => {
      setRefreshing(false);
    });
  };
  return (
    <SafeAreaView
      edges={['bottom']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.white,
      }}>
      {tabId !== '7' && (
        <>
          <TitleHeader
            title={router.params?.item ? router.params?.item?.Name : ''}
          />
          <ScrollOptionToilet setTabId={setTabId} tabId={tabId} />
        </>
      )}
      {tabId === '1' && <General />}
      {tabId === '2' && <EmptyPage />}
      {tabId === '3' && (
        <Work
          data={router.params.item}
          toiletId={router.params.item.Id}
          dataTask={tasks}
        />
      )}
      {tabId === '4' && (
        <CalendarTab data={workData} serviceData={serviceData} />
      )}
      {tabId === '5' && (
        <DevicesListTab
          refreshing={isRefreshing}
          onRefresh={onRefreshing}
          data={workData}
          serviceData={serviceData}
        />
      )}
      {tabId === '6' && (
        <ToiletListBioProductTab
          refreshing={isRefreshing}
          onRefresh={onRefreshing}
          data={workData}
          serviceData={serviceData}
        />
      )}
      {tabId === '7' && (
        <CetificateAchievementPage
          type={'3'}
          toiletId={router.params.item.Id}
        />
      )}
      {tabId === '8' && (
        <FilesList
          refreshing={isRefreshing}
          onRefresh={onRefreshing}
          data={workData}
          serviceData={serviceData}
        />
      )}
      {tabId === '9' && (
        <ContractToiletListTab
          refreshing={isRefreshing}
          onRefresh={onRefreshing}
          data={workData}
          serviceData={serviceData}
        />
      )}
      {tabId === '10' && <EmptyPage />}
      {tabId === '11' && <NoteTab data={workData} serviceData={serviceData} />}
    </SafeAreaView>
  );
};
export default ToiletDetailPage;
