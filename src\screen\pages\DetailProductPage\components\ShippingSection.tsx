import React from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {ShippingSectionProps} from '../types';
import iconSvg from '../../../../svgs/iconSvg';

const ShippingSection: React.FC<ShippingSectionProps> = ({isFreeShip}) => {
  return (
    <Pressable style={styles.shippingContainer}>
      <AppSvg SvgSrc={iconSvg.delivery} size={20} />
      {isFreeShip ? (
        <Text style={styles.shippingFree}><PERSON><PERSON>n phí giao hàng</Text>
      ) : (
        <Text style={styles.shippingDistance}>3.0km Free</Text>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  shippingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  shippingDistance: {
    fontSize: 12,
    color: '#3FB993',
    marginLeft: 8,
  },
  shippingFree: {
    fontSize: 13,
    color: '#3FB993',
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default ShippingSection;
