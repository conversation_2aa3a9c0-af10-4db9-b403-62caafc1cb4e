import React, {useEffect, useCallback} from 'react';
import {Pressable, View} from 'react-native';
import TitleHeader from '../../layout/headers/TitleHeader';
import ReviewToilet from './components/ReviewToilet/ReviewToilet';
// Import styles

// Import constants
import ProgressSteps from '../RegisterCateCriterionPage/components/RegisterToiletStep/components/ProgressSteps';
import SurveyForm from './components/SurveyForm/SurveyForm';
import {styles} from './components/SurveyForm/styles';
import {useRoute} from '@react-navigation/native';
import {SurveyTaskDa} from '../../module/surveyTask/surveyTaskDa';
import {FLoading} from 'wini-mobile-components';
import {CreateSurveyStepData} from './components/SurveyForm/types';
import {showSnackbar} from '../../../component/export-component';
import {ComponentStatus} from '../../../component/component-status';

export type SurveyTaskStep =
  | 'create'
  | 'view'
  | 'edit'
  | 'editInfo'
  | 'toiletReview';

export default function CreateSurveyStepPage() {
  const surveyTaskDa = new SurveyTaskDa();
  const router = useRoute<any>();
  const {id, type} = router.params;
  const [step, setStep] = React.useState<SurveyTaskStep>('view');
  const [countProgress, setCountProgress] = React.useState<1 | 2>(1);
  const [detail, setDetail] = React.useState<CreateSurveyStepData | null>();
  const [loading, setLoading] = React.useState(false);
  const [customerId, setCustomerId] = React.useState<string>('');
  const [customerPhone, setCustomerPhone] = React.useState<string>('');
  const [surveyTaskId, setSurveyTaskId] = React.useState<string>('');

  const handleChangeStep = (
    step: SurveyTaskStep,
    customerId?: string,
    surveyTaskId?: string,
    customerPhone?: string,
  ) => {
    if (step == 'toiletReview') {
      setCustomerId(customerId || '');
      setSurveyTaskId(surveyTaskId || '');
      setCustomerPhone(customerPhone || '');
      setCountProgress(2);
    } else {
      setCountProgress(1);
    }
    setStep(step);
  };

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      if (!id) return;

      const res = await surveyTaskDa.fetchById(id);

      if (res === null) {
        setDetail(null);
      } else {
        setDetail(res);
        setCustomerId(res.Customer?.Id || '');
        setCustomerPhone(res.Customer?.Mobile || '');
      }
    } catch (error) {
      console.error('Error fetching survey data:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (type === 'create') {
      setStep('create');
      setDetail(null);
      setLoading(false);
    } else if (type === 'view') {
      setStep('view');
      fetchData();
    }
  }, [type, fetchData]);

  const renderContent = () => {
    if (
      step == 'create' ||
      step == 'view' ||
      step == 'edit' ||
      step == 'editInfo'
    ) {
      return (
        <SurveyForm
          initialData={detail}
          step={step}
          changeStep={handleChangeStep}
        />
      );
    }

    if (step == 'toiletReview') {
      return (
        <ReviewToilet
          customerId={customerId}
          customerPhone={customerPhone}
          surveyTaskId={id || surveyTaskId}
        />
      );
    }

    return null;
  };
  return (
    <View style={styles.container}>
      <FLoading visible={loading} />
      {/* Header */}
      <TitleHeader title="Khảo sát tình trạng nhà vệ sinh" />
      {step != 'create' && step != 'edit' && (
        <ProgressSteps
          step={countProgress}
          totalSteps={2}
          style={{marginTop: 16}}
        />
      )}
      {!loading && renderContent()}
    </View>
  );
}
