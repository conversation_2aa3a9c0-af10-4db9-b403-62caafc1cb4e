import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, FlatList} from 'react-native';
import {TypoSkin} from '../../../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../../../assets/skin/colors';

interface TabNavigationProps {
  tabs: string[];
  activeTab: number;
  onTabChange: (index: number) => void;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
}) => {
  return (
    <View style={styles.tabContainer}>
      <FlatList
        horizontal
        data={tabs}
        keyExtractor={(item, index) => `${item}-${index}`}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabRow}
        renderItem={({item, index}) => (
          <TouchableOpacity
            onPress={() => onTabChange(index)}
            style={[
              styles.tabButton,
              {
                borderBottomWidth: activeTab === index ? 1 : 0,
              },
            ]}>
            <Text
              style={[
                styles.tabText,
                {
                  color:
                    activeTab === index
                      ? ColorThemes.light.primary_main_color
                      : ColorThemes.light.neutral_text_disabled_color,
                },
              ]}>
              {item}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    borderTopColor: ColorThemes.light.neutral_main_background_color,
    borderTopWidth: 1,
  },
  tabRow: {
    alignItems: 'center',
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
    borderBottomColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.title3,
  },
});
