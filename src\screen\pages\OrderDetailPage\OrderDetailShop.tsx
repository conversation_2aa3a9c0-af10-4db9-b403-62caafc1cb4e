import React from 'react';
import {
  StyleSheet,
  ScrollView,
  RefreshControl,
  View,
  Pressable,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  showSnackbar,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {RootScreen} from '../../../router/router';
import Clipboard from '@react-native-clipboard/clipboard';
import PopupCancelOrder from '../../module/orderProduct/popup/PopupCancelOrder';
import PopupUpdateStatusOrder from '../../module/orderProduct/popup/PopupUpdateStatusOrder/PopupUpdateStatusOrder';
import TitleHeader from '../../layout/headers/TitleHeader';
import {StatusOrder, Title} from '../../../config/Contanst';
import {
  OrderStatusTimeline,
  OrderInfo,
  OrderItems,
  OrderActionButtons,
  useOrderData,
  useOrderActions,
} from './index';
import {OrderProductDA} from '../../module/orderProduct/orderProductDA';
import productDA from '../../module/product/productDA';

const OrderDetailShop: React.FC<{orderId: string}> = ({
  orderId,
}: {
  orderId: string;
}) => {
  const navigation = useNavigation<any>();
  const [loading, setLoading] = React.useState(false);
  const orderDA = new OrderProductDA();

  // Use custom hooks
  const {order, orderDetails, refreshing, handleRefresh, fetchOrderData} =
    useOrderData(orderId);

  const {
    isCancelPopupVisible,
    isSubmittingCancel,
    isUpdateStatusPopupVisible,
    setCancelPopupVisible,
    setUpdateStatusPopupVisible,
    handleCancelOrder,
    handleSubmitRejectOrder,
    handleSubmitUpdateStatus,
    dialogRef,
  } = useOrderActions(order, async () => {});

  const currentStatus = order?.Status || StatusOrder.new;

  // Handle copy order code
  const handleCopyOrderCode = () => {
    Clipboard.setString(order?.Code || '');
    showSnackbar({
      message: 'Đã sao chép mã đơn hàng',
      status: ComponentStatus.SUCCSESS,
    });
  };

  // Handle product press
  const handleProductPress = (productId: string) => {
    navigation.push(RootScreen.DetailProductPage, {
      id: productId,
    });
  };

  // Handle action buttons
  const handleChatWithShop = () => {
    // TODO: Implement chat functionality
    console.log('Chat with shop');
  };

  const handleContactShop = () => {
    // TODO: Implement contact functionality
    console.log('Contact shop');
  };

  const handleUpdateStatusProcessOrder = async () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn muốn xác nhận đơn hàng này?',
      onSubmit: async () => {
        try {
          setLoading(true);
          // Kiểm tra số lượng tồn kho của tất cả sản phẩm trong đơn hàng
          for (const orderDetail of orderDetails) {
            const productId = orderDetail?.ProductId;
            const orderQuantity = orderDetail?.Quantity || 1;
            if (productId) {
              const productResponse =
                await orderDA.getInfoProductById(productId);
              if (
                productResponse?.code === 200 &&
                productResponse?.data?.length > 0
              ) {
                const product = productResponse.data[0];
                const currentStock = product?.InStock || 0;

                // Kiểm tra nếu sản phẩm hết hàng
                if (currentStock <= 0) {
                  showSnackbar({
                    message: `Sản phẩm "${product?.Name}" đã hết hàng. Không thể xử lý đơn hàng này.`,
                    status: ComponentStatus.ERROR,
                  });
                  return;
                }

                // Kiểm tra nếu số lượng đặt hàng vượt quá tồn kho
                if (orderQuantity > currentStock) {
                  showSnackbar({
                    message: `Sản phẩm "${product?.Name}" chỉ còn ${currentStock} sản phẩm trong kho. Đơn hàng yêu cầu ${orderQuantity} sản phẩm.`,
                    status: ComponentStatus.ERROR,
                  });
                  return;
                }
              } else {
                showSnackbar({
                  message: 'Không tìm thấy thông tin sản phẩm',
                  status: ComponentStatus.ERROR,
                });
                return;
              }
            }
          }
          // Cập nhật số lượng tồn kho của tất cả sản phẩm
          // Lọc và map ID của sản phẩm vào mảng
          const productUpdates = orderDetails
            .filter(orderDetail => orderDetail?.ProductId)
            .map(orderDetail => ({
              productId: orderDetail.ProductId,
              orderQuantity: orderDetail?.Quantity || 1,
            }));

          if (productUpdates.length > 0) {
            // Lấy danh sách ID sản phẩm
            const productIds = productUpdates.map(item => item.productId);

            try {
              // Gọi API để lấy thông tin từng sản phẩm (batch requests)
              const productPromises = productIds.map(productId =>
                orderDA.getInfoProductById(productId),
              );

              const productsResponses = await Promise.all(productPromises);
              const updatedProducts = [];

              // Xử lý cập nhật cho từng sản phẩm
              for (let i = 0; i < productUpdates.length; i++) {
                const productUpdate = productUpdates[i];
                const productResponse = productsResponses[i];

                if (
                  productResponse?.code === 200 &&
                  productResponse?.data?.length > 0
                ) {
                  const product = productResponse.data[0];
                  const currentStock = product?.InStock || 0;
                  const newStock = currentStock - productUpdate.orderQuantity;

                  const updatedProduct = {
                    ...product,
                    InStock: newStock,
                    // Tự động cập nhật status thành 2 (hết hàng) nếu newStock = 0
                    Status: newStock === 0 ? 2 : product.Status,
                  };

                  console.log(
                    'updatedProduct',
                    updatedProduct.InStock,
                    'Status:',
                    updatedProduct.Status,
                  );

                  updatedProducts.push(updatedProduct);
                }
              }

              // Cập nhật từng sản phẩm (batch updates)
              if (updatedProducts.length > 0) {
                const updatePromises = updatedProducts.map(product =>
                  productDA.updateProduct(product),
                );

                const updateResponses = await Promise.all(updatePromises);

                // Kiểm tra kết quả cập nhật
                const failedUpdates = updateResponses.filter(
                  response => response?.code !== 200,
                );

                if (failedUpdates.length > 0) {
                  console.warn(
                    `${failedUpdates.length} sản phẩm cập nhật thất bại`,
                  );
                }
              }
            } catch (error) {
              console.error('Lỗi cập nhật sản phẩm:', error);
            }
          }
          // Cập nhật trạng thái đơn hàng
          const res = await orderDA.updateOrder([
            {
              Id: order?.Id,
              CustomerId: order?.CustomerId,
              ShopId: order?.ShopId,
              Code: order?.Code,
              Status: StatusOrder.proccess,
              DateProcess: new Date().getTime(),
            },
          ]);

          if (res?.code === 200) {
            showSnackbar({
              message: 'Xác nhận đơn hàng thành công!',
              status: ComponentStatus.SUCCSESS,
            });
            // Refresh dữ liệu đơn hàng
            fetchOrderData();
            navigation.navigate(RootScreen.OrderDetailPage, {
              orderId: order?.Id,
              type: 'shop',
            });
          } else {
            showSnackbar({
              message: 'Xác nhận đơn hàng thất bại!',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error confirming order:', error);
          showSnackbar({
            message: 'Xác nhận đơn hàng thất bại!',
            status: ComponentStatus.ERROR,
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <PopupCancelOrder
        visible={isCancelPopupVisible}
        onClose={() => setCancelPopupVisible(false)}
        onSubmit={handleSubmitRejectOrder}
        loading={isSubmittingCancel}
      />
      <PopupUpdateStatusOrder
        item={order}
        visible={isUpdateStatusPopupVisible}
        onClose={() => setUpdateStatusPopupVisible(false)}
        handleUpdateStatusProcessOrder={handleSubmitUpdateStatus}
      />
      <TitleHeader title={'Chi tiết đơn hàng'} />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        <Pressable>
          <OrderStatusTimeline currentStatus={currentStatus} order={order} />
        </Pressable>
        <Pressable>
          <OrderInfo
            order={order}
            currentStatus={currentStatus}
            onCopyOrderCode={handleCopyOrderCode}
          />
        </Pressable>
        <Pressable>
          <OrderItems
            orderDetails={orderDetails}
            order={order}
            isCustomer={false}
            onProductPress={handleProductPress}
            onCancelOrder={handleCancelOrder}
            onRequestRefund={() => {}}
            onReviewProduct={() => {}}
          />
        </Pressable>
      </ScrollView>
      <OrderActionButtons
        currentStatus={currentStatus}
        isCustomer={false}
        onChatWithShop={handleChatWithShop}
        onContactShop={handleContactShop}
        onConfirmOrder={handleUpdateStatusProcessOrder}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
});

export default OrderDetailShop;
