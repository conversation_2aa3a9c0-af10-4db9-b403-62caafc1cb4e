import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useSelectorCustomerState } from '../../../../../redux/hooks/hooks';
import { CustomerActions } from '../../../../../redux/reducers/user/reducer';
import { AddressItem, UseMyAddressReturn } from '../types';

/**
 * Custom hook for managing address data and operations
 */
export const useMyAddress = (): UseMyAddressReturn => {
  const dispatch = useDispatch<any>();
  const { data: user, myAddress, onLoading } = useSelectorCustomerState();
  const [refreshing, setRefreshing] = useState(false);

  // Fetch addresses when user is available
  useEffect(() => {
    if (user?.Id) {
      dispatch(CustomerActions.getAddresses(user.Id));
    }
  }, [user?.Id, dispatch]);

  // Fetch addresses function
  const fetchAddresses = useCallback(async () => {
    if (user?.Id) {
      await dispatch(CustomerActions.getAddresses(user.Id));
    }
  }, [user?.Id, dispatch]);

  // Delete address function
  const deleteAddress = useCallback(async (addressId: string) => {
    await dispatch(CustomerActions.deleteAddress(addressId));
  }, [dispatch]);

  // Refresh function
  const refresh = useCallback(async () => {
    if (!user?.Id) return;
    
    setRefreshing(true);
    try {
      await dispatch(CustomerActions.getAddresses(user.Id));
    } finally {
      setRefreshing(false);
    }
  }, [user?.Id, dispatch]);

  return {
    addresses: (myAddress as AddressItem[]) || [],
    loading: onLoading ?? false,
    refreshing,
    user,
    fetchAddresses,
    deleteAddress,
    refresh,
  };
};
