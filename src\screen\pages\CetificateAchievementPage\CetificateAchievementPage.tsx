import React, {useEffect} from 'react';
import {View} from 'react-native';
import TitleHeader from '../../layout/headers/TitleHeader';
import ScrollOptionForm from './Ultis/ScrollOptionform';
import General from './Component/General';
import Cetificate from './Component/Cetificate';
import OperationalStatistics from './Component/OperationalStatistics';
import {useRoute} from '@react-navigation/native';
const CetificateAchievementPage: React.FC<{
  type?: string;
  toiletId?: string;
}> = ({type, toiletId}) => {
  const route = useRoute<any>();
  const [tabId, setTabId] = React.useState('');
  useEffect(() => {
    if (type && type === '3') {
      setTabId('3');
    } else {
      setTabId('1');
    }
  }, [type]);

  return (
    <View style={{flex: 1}}>
      <TitleHeader title="Sach - Xanh - Tuần hoàn" />
      <ScrollOptionForm setTabId={setTabId} />
      {tabId === '1' && (
        <General toiletId={toiletId || route.params?.toiletId} />
      )}
      {tabId === '2' && (
        <Cetificate toiletId={toiletId || route.params?.toiletId} />
      )}
      {tabId === '3' && (
        <OperationalStatistics
          toiletId={(toiletId as string) || route.params?.toiletId}
          type={type === '3' ? 'statistics' : 'normal'}
        />
      )}
    </View>
  );
};

export default CetificateAchievementPage;
