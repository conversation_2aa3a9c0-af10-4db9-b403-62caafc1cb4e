import {TypicalProjectItem} from 'types/typicalProject';
import {getImage} from '../../../redux/actions/rootAction';
import {DataController} from '../../base-controller';

/**
 * Data Access class for TypicalProject module
 */
class TypicalProjectDa {
  private projectController: DataController;

  constructor() {
    this.projectController = new DataController('TypicalProject');
  }

  /**
   * Fetch typical projects with pagination
   * @param page - Page number (default: 1)
   * @param size - Number of items per page (default: 10)
   * @returns Promise with API response
   */
  async fetch({
    page = 1,
    size = 10,
  }: {
    page?: number;
    size?: number;
  }): Promise<TypicalProjectItem[]> {
    try {
      const response = await this.projectController.aggregateList({
        page,
        size,
        searchRaw: '*',
        sortby: [{prop: 'DateCreated', direction: 'DESC'}],
      });

      if (response.code === 200) {
        const data = await getImage({items: response.data});
        return data;
      }
      throw new Error(response.message);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Fetch a single typical project by ID
   * @param id - Project ID
   * @returns Promise with project data or null if not found
   */
  async fetchById(id: string): Promise<TypicalProjectItem | null> {
    try {
      const response = await this.projectController.getById(id);
      if (response.code === 200) {
        const data = await getImage({items: [response.data]});
        return data[0] || null;
      }
      return null;
    } catch (error) {
      console.error('Error fetching project by ID:', error);
      return null;
    }
  }
}

export default new TypicalProjectDa();
