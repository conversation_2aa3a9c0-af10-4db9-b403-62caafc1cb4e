import {useState, useEffect, useMemo} from 'react';
import {
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {CustomBottomSheet} from 'project-component/form/DateRangePicker/CustomBottomSheet';
import {CateCriterionItem} from 'types/cateCriteriaType';
import {ToiletCriterionSurveyTask} from 'types/toiletCriterionSurveyTask';
import {randomGID} from 'utils/Utils';
import {SurveySection} from 'screen/pages/CreateSurveyStepPage/components/ReviewToilet/components';
import ToiletServiceCriterionDA from 'screen/module/workplace/components/form/RegisterTab/components/SurveyInfo/components/ListToiletCriterion/da';
import {ToiletServiceCriterion} from 'types/toiletServiceCriterionType';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import Checkbox from 'screen/pages/CreateSurveyStepPage/components/ReviewToilet/components/Checkbox';

interface SelectToiletCriterionBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  cateCriterionData: CateCriterionItem[];
  toiletId: string;
  toiletName: string;
  toiletCriterion?: ToiletCriterionSurveyTask | null;
  toiletServiceId: string;
  cateCriterionId?: string;
  onConfirmSuccess?: () => void;
  toiletSelected?: any[]; // Danh sách toilet được chọn để xử lý "tất cả"
  toiletCriterions?: any[]; // Danh sách toilet criterions hiện tại
  markToiletAsUpdated?: (toiletIds: string[]) => void;
  disabled?: boolean;
}

export default function SelectToiletCriterionBottomSheet({
  visible,
  onClose,
  disabled = false,
  cateCriterionData,
  toiletId,
  toiletServiceId,
  toiletName,
  toiletCriterion,
  onConfirmSuccess,
  toiletSelected = [],
  toiletCriterions = [],
  markToiletAsUpdated,
  cateCriterionId,
}: SelectToiletCriterionBottomSheetProps) {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const toiletServiceCriterionData = useMemo(
    () => new ToiletServiceCriterionDA(),
    [],
  );

  // Tính toán tất cả các criterion IDs
  const allCriterionIds = useMemo(() => {
    const ids: string[] = [];
    cateCriterionData.forEach(category => {
      if (category.Criterions) {
        category.Criterions.forEach(criterion => {
          ids.push(criterion.Id);
        });
      }
    });
    return ids;
  }, [cateCriterionData]);

  // Kiểm tra xem có phải tất cả đều được chọn không
  const isAllSelected = useMemo(() => {
    return (
      allCriterionIds.length > 0 &&
      allCriterionIds.every(id => checkedItems.includes(id))
    );
  }, [allCriterionIds, checkedItems]);

  // Hàm toggle chọn tất cả
  const toggleSelectAll = () => {
    if (isAllSelected) {
      setCheckedItems([]);
    } else {
      setCheckedItems([...allCriterionIds]);
    }
  };

  const handleConfirm = async () => {
    try {
      setLoading(true);
      const criterionIds = checkedItems.join(',');

      if (toiletId === 'all') {
        // Xử lý cho tất cả toilet
        const listUpdate = [];
        const listAdd = [];

        for (const toilet of toiletSelected) {
          const existingCriterion = toiletCriterions.find(
            tc => tc.ToiletId === toilet.Id,
          );

          if (existingCriterion) {
            // Update existing
            const updateData: ToiletServiceCriterion = {
              ...existingCriterion,
              CriterionId: criterionIds,
            };
            listUpdate.push(updateData);
          } else {
            // Add new
            const addData: ToiletServiceCriterion = {
              Id: randomGID(),
              ToiletId: toilet.Id,
              Type: 1,
              CateCriterionId: toilet.CateCriterionId,
              CriterionId: criterionIds,
              ToiletServicesId: toiletServiceId,
              DateCreated: new Date().getTime(),
            };
            listAdd.push(addData);
          }
        }

        // Thực hiện tất cả các operations
        await toiletServiceCriterionData.updateToiletCriterion(listUpdate);
        await toiletServiceCriterionData.createToiletServiceCriterion(listAdd);

        // Đánh dấu tất cả toilet đã được cập nhật
        const allToiletIds = toiletSelected.map(toilet => toilet.Id);
        markToiletAsUpdated?.(allToiletIds);

        showSnackbar({
          message: 'Cập nhật tiêu chí cho tất cả nhà vệ sinh thành công',
          status: ComponentStatus.SUCCSESS,
        });
      } else {
        // Xử lý cho toilet đơn lẻ (logic cũ)
        if (toiletCriterion && toiletCriterion.Id) {
          // Update existing toilet criterion
          const updateData = {
            ...toiletCriterion,
            CriterionId: criterionIds,
          };
          await toiletServiceCriterionData.updateToiletCriterion([updateData]);

          // Đánh dấu toilet đã được cập nhật
          markToiletAsUpdated?.([toiletId]);

          showSnackbar({
            message: 'Cập nhật tiêu chí thành công',
            status: ComponentStatus.SUCCSESS,
          });
        } else {
          // Add new toilet criterion
          const addData = {
            Id: randomGID(),
            ToiletId: toiletId,
            Type: 1,
            CriterionId: criterionIds,
            ToiletServicesId: toiletServiceId,
            DateCreated: new Date().getTime(),
            CateCriterionId: cateCriterionId,
          };
          await toiletServiceCriterionData.createToiletServiceCriterion([
            addData,
          ]);

          // Đánh dấu toilet đã được cập nhật
          markToiletAsUpdated?.([toiletId]);

          showSnackbar({
            message: 'Thêm tiêu chí thành công',
            status: ComponentStatus.SUCCSESS,
          });
        }
      }

      onConfirmSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error saving toilet criterion:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi lưu tiêu chí',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleCheck = (itemId: string) => {
    const index = checkedItems.indexOf(itemId);
    if (index === -1) {
      setCheckedItems(prev => [...prev, itemId]);
    } else {
      setCheckedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  useEffect(() => {
    if (toiletId === 'all') {
      setCheckedItems([]);
    } else {
      if (toiletCriterion && toiletCriterion.CriterionId) {
        const criterionIds = toiletCriterion.CriterionId.split(',');
        setCheckedItems(criterionIds);
      } else {
        setCheckedItems([]);
      }
    }
  }, [toiletCriterion, toiletId, toiletSelected, toiletCriterions]);

  return (
    <CustomBottomSheet
      visible={visible}
      onClose={onClose}
      title={`Chọn tiêu chí`}
      subTitle={`${toiletName}`}
      onCancel={onClose}
      onConfirm={handleConfirm}
      cancelText="Hủy"
      isShowConfirm={!disabled}
      confirmText={loading ? 'Đang lưu...' : 'Xác nhận'}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Checkbox chọn tất cả */}
        {allCriterionIds.length > 0 && (
          <View style={styles.selectAllContainer}>
            <TouchableOpacity
              style={styles.selectAllItem}
              onPress={disabled ? undefined : toggleSelectAll}>
              <Checkbox
                isChecked={isAllSelected}
                onToggle={toggleSelectAll}
                disabled={disabled}
              />
              <Text
                style={[styles.selectAllText, disabled && styles.disabledText]}>
                Chọn tất cả
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {cateCriterionData.map((category: any) => (
          <SurveySection
            key={category.Id}
            title={`${category.Name || 'Tiêu chí'}`}
            items={category.Criterions}
            checkedItems={checkedItems}
            onToggleCheck={toggleCheck}
            disabled={disabled}
          />
        ))}
      </ScrollView>
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  selectAllContainer: {
    marginBottom: 16,
    paddingVertical: 12,
  },
  selectAllItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 6,
  },
  disabledText: {
    color: '#999',
  },
});
