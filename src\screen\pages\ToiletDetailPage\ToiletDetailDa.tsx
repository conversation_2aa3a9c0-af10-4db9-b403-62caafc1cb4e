import {TransactionType} from '../../../config/Contanst';
import {DataController} from '../../base-controller';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {ToiletServiceStatus} from '../../module/service/components/da';
import {getImage} from '../../../redux/actions/rootAction';

export class ToiletDetailDa {
  private ToiletServicesController: DataController;
  private CateServicesController: DataController;
  private ToiletCertificateController: DataController;

  constructor() {
    this.ToiletServicesController = new DataController('ToiletServices');
    this.CateServicesController = new DataController('CateServices');
    this.ToiletCertificateController = new DataController('ToiletCertificate');
  }

  async getAllServiceCate(toiletID: string) {
    try {
      const response = await this.CateServicesController.getListSimple({
        query: `@Name: "Netzero"`,
      });
      if (response?.code === 200 && response?.data.length > 0) {
        let data: any;
        const cateService = await this.ToiletServicesController.getPatternList({
          query: `@CateServicesId:{${response.data[0].Id}} @ToiletId: {${toiletID}} `,
          pattern: {
            CateCriterionId: [
              'Id',
              'Name',
              'Description',
              'Sort',
              'TypeCriterion',
              'DateCreated',
              'Content',
            ],
          },
        });
        if (cateService?.code === 200) {
          const CateCriterion = cateService?.CateCriterion;
          const cateServiceData = cateService?.data;
          const ToiletCertificateService =
            await this.ToiletCertificateController.getListSimple({
              query: `@ToiletId: {${toiletID}} `,
            });

          if (ToiletCertificateService?.code === 200) {
            let dataToiletCertificateService = await getImage({
              items: ToiletCertificateService.data,
            });
            data = cateServiceData.map((serviceItem: any) => {
              return {
                ...serviceItem,
                CateCriterion: CateCriterion.find(
                  (cate: any) => cate.Id === serviceItem.CateCriterionId,
                ),
                ToiletCertificate: dataToiletCertificateService.find(
                  (cate: any) => cate.ToiletId === serviceItem.ToiletId,
                ),
              };
            });
          }

          return data;
        }
      }
    } catch (error) {
      console.log(`check-error`, error);
    }
  }
}

// Export instance để sử dụng trong các component
export default new ToiletDetailDa();
