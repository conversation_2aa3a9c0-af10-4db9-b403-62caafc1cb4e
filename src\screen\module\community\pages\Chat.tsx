import {useNavigation, DrawerActions} from '@react-navigation/native';
import {FlatList, Image, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ScrollView} from 'react-native-gesture-handler';
import React, {useEffect, useRef, useState} from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {
  FDialog,
  FTextField,
  Winicon,
} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import ConfigAPI from '../../../../config/configApi';
import EmptyPage from '../../../../project-component/empty-page';
import {SkeletonImage} from '../../../../project-component/skeleton-img';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {CustomerDA} from '../../../../redux/reducers/user/da';
import {navigate, RootScreen} from '../../../../router/router';
import {dialogCheckAcc} from '../../../layout/main-layout';

export const ProfileView = () => {
  const user = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <FDialog ref={dialogRef} />
      <TouchableOpacity
        onPress={() => {
          if (!user) {
            dialogCheckAcc({ref: dialogRef});
            return;
          }
          navigate(RootScreen.ProfileCommunity, {Id: user?.Id});
        }}>
        {user?.AvatarUrl ? (
          <SkeletonImage
            source={{uri: ConfigAPI.imgUrlId + user?.AvatarUrl}}
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              backgroundColor: '#f0f0f0',
            }}
          />
        ) : user?.Name ? (
          <View
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              backgroundColor: ColorThemes.light.primary_main_color,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.neutral_absolute_background_color,
              }}>
              {user?.Name ? user.Name.charAt(0).toUpperCase() : ''}
            </Text>
          </View>
        ) : (
          <View
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              backgroundColor: ColorThemes.light.primary_main_color,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Winicon
              src="fill/users/profile"
              size={20}
              color={'white'}
              style={{padding: 8}}
            />
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default function Chat() {
  const navigation = useNavigation<any>();
  const [searchValue, setSearchValue] = useState('');
  const user = useSelectorCustomerState().data;
  const [listFriend, setlistFriend] = useState<Array<any>>([]);
  const customerDA = new CustomerDA();
  const bottomSheetRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (searchValue) {
      // Lọc danh sách thành viên theo tên
      const filteredMembers = listFriend.filter((member: any) =>
        member.Name.toLowerCase().includes(searchValue.toLowerCase()),
      );
      setlistFriend({...filteredMembers});
    } else {
      // Hiển thị danh sách thành viên ban đầu
      setlistFriend(listFriend);
    }
  }, [searchValue]);

  useEffect(() => {
    getListFriend();
  }, []);

  const getListFriend = async () => {
    if (!user) return;
    setIsLoading(true);
    const result = await customerDA.getListFriend(user.Id);
    if (result) {
      setlistFriend(result);
    }
    setIsLoading(false);
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <Winicon src="fill/user interface/apps" size={20} />
          </TouchableOpacity>
        }
        title="Chat"
        trailing={<ProfileView />}
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              height: 56,
              gap: 8,
              paddingTop: 16,
              paddingBottom: 16,
            }}>
            <FTextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={async (vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder="Tìm kiếm..."
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              }
            />
          </View>
        }
      />
      {/* content */}
      <FlatList
        data={listFriend}
        style={{height: '100%'}}
        keyExtractor={(item, index) => index.toString()}
        ListEmptyComponent={() => {
          if (isLoading) {
            return (
              <View style={{flex: 1, paddingTop: 16, gap: 16}}>
                <SkeletonPlaceCard />
                <SkeletonPlaceCard />
                <SkeletonPlaceCard />
              </View>
            );
          }
          return <EmptyPage title="Không có dữ liệu" />;
        }}
        renderItem={({item, index}) => {
          return (
            <ListTile
              key={index}
              leading={
                <SkeletonImage
                  key={item?.AvatarUrl}
                  source={{
                    uri: item?.AvatarUrl
                      ? `${ConfigAPI.imgUrlId + item?.AvatarUrl}`
                      : 'https://placehold.co/48/FFFFFF/000000/png',
                  }}
                  style={{
                    width: 48,
                    height: 48,
                    borderRadius: 50,
                    backgroundColor: '#f0f0f0',
                  }}
                />
              }
              title={
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}
                  numberOfLines={1}>
                  {item.Name}
                </Text>
              }
              subtitle={item.Description ?? ''}
              subTitleStyle={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}
              onPress={() => {}}
              trailing={
                <View
                  style={{alignItems: 'center', flexDirection: 'row', gap: 8}}>
                  <TouchableOpacity style={{padding: 4}} onPress={() => {}}>
                    <Winicon
                      src="fill/user interface/phone-call"
                      color={ColorThemes.light.neutral_text_subtitle_color}
                      size={16}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity style={{padding: 4}} onPress={() => {}}>
                    <Winicon
                      src="fill/user interface/f-chat"
                      color={ColorThemes.light.neutral_text_subtitle_color}
                      size={16}
                    />
                  </TouchableOpacity>
                </View>
              }
            />
          );
        }}
      />
    </View>
  );
}

const SkeletonPlaceCard = () => {
  return (
    <SkeletonPlaceholder
      backgroundColor="#f0f0f0"
      highlightColor="#e0e0e0"
      enabled={true}
      speed={800}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        {/* Avatar placeholder */}
        <View
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            marginRight: 12,
          }}
        />

        {/* Title and subtitle container */}
        <View style={{flex: 1, gap: 8}}>
          {/* Title placeholder */}
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
            }}
          />

          {/* Subtitle placeholder */}
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Icons container */}
        <View style={{flexDirection: 'row', gap: 8}}>
          {/* Phone icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />

          {/* Chat icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
