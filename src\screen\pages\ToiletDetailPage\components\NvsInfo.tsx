import React from 'react';
import {Text, View} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../../svgs/iconSvg';
import {TypeStringData} from '../../../module/service/components/da';
import {Ultis} from '../../../../utils/Utils';

interface NvsInfoProps {
  GetToiletInfo?: any;
}

const NvsInfo: React.FC<NvsInfoProps> = ({GetToiletInfo}) => {
  return (
    <View style={{flex: 1, gap: 24, marginVertical: 16}}>
      <View style={{}}>
        <View style={{flexDirection: 'row', gap: 10, alignItems: 'flex-start'}}>
          <View style={{marginTop: 2}}>
            <AppSvg SvgSrc={iconSvg.filter} size={20} />
          </View>
          <View style={{flex: 1}}>
            <Text style={{...TypoSkin.regular2, flexWrap: 'wrap'}}>
              <Text style={{...TypoSkin.regular2}}>Phân loại: </Text>
              <Text style={{...TypoSkin.regular2}}>
                {TypeStringData.find((e: any) => e.key === GetToiletInfo?.Type)
                  ?.title ?? '-'}
              </Text>
            </Text>
          </View>
        </View>
      </View>
      <View style={{}}>
        <View style={{flexDirection: 'row', gap: 10, alignItems: 'flex-start'}}>
          <View style={{marginTop: 2}}>
            <AppSvg SvgSrc={iconSvg.phoneNumber} size={20} />
          </View>
          <View style={{flex: 1}}>
            <Text style={{...TypoSkin.regular2, flexWrap: 'wrap'}}>
              <Text style={{...TypoSkin.regular2}}>
                Số điện thoại liên hệ:{' '}
              </Text>
              <Text style={{...TypoSkin.regular2}}>
                {GetToiletInfo?.Mobile}
              </Text>
            </Text>
          </View>
        </View>
      </View>
      <View
        style={{
          flexWrap: 'wrap',
        }}>
        <View
          style={{
            flexDirection: 'row',
            gap: 10,
            alignItems: 'flex-start',
          }}>
          <View style={{marginTop: 2}}>
            <AppSvg SvgSrc={iconSvg.address} size={20} />
          </View>
          <View style={{flex: 1}}>
            <Text style={{...TypoSkin.regular2}}>
              <Text style={{...TypoSkin.regular2}}>Địa chỉ: </Text>
              <Text style={{...TypoSkin.regular2}}>
                {GetToiletInfo?.Address}
              </Text>
            </Text>
          </View>
        </View>
      </View>
      <View
        style={{
          flexWrap: 'wrap',
        }}>
        <View
          style={{
            flexDirection: 'row',
            gap: 10,
            alignItems: 'flex-start',
          }}>
          <View style={{marginTop: 2}}>
            <AppSvg SvgSrc={iconSvg.calendar} size={20} />
          </View>
          <View style={{flex: 1}}>
            <Text style={{...TypoSkin.regular2}}>
              <Text style={{...TypoSkin.regular2}}>Ngày tạo: </Text>
              <Text style={{...TypoSkin.regular2}}>
                {GetToiletInfo?.DateCreated
                  ? Ultis.datetoString(new Date(GetToiletInfo.DateCreated))
                  : '-'}
              </Text>
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};
export default NvsInfo;
