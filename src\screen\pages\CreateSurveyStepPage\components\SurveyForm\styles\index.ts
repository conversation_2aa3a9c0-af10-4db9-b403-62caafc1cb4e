import {StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../../assets/skin/typography';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 12,
    marginTop: 8,
    backgroundColor: ColorThemes.light.white,
  },
  section: {
    marginTop: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.primary_main_color,
    marginBottom: 20,
    fontSize: 16,
    fontWeight: '600',
  },
  inputField: {
    marginTop: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationRow: {
    height: 48,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  locationInput: {
    flex: 1,
    marginRight: 12,
  },
  createButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 6,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
  },
  createButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 13,
    fontWeight: '500',
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    gap: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    paddingVertical: 14,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#BDBDBD',
  },
  cancelButtonText: {
    ...TypoSkin.buttonText3,
    color: '#424242',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#4CAF50',
    borderRadius: 25,
  },
  btnAdd: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 6,
  },
  btnAddText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  errorText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
});
