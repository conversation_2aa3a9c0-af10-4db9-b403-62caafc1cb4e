import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {
  AppSvg,
  showBottomSheet,
  hideBottomSheet,
  ListTile,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Radio, RadioAction} from '../../../../component/Field/Radio';
import iconSvg from '../../../../svgs/iconSvg';
import {PaymentMethodSelectorProps} from '../types';
import {paymentData} from '../hooks/useCheckout';

interface PaymentMethodSelectorInternalProps extends PaymentMethodSelectorProps {
  btsRef: React.RefObject<any>;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorInternalProps> = ({
  payment,
  onPaymentChange,
  isDone,
  btsRef,
}) => {
  const handlePaymentMethodPress = () => {
    showBottomSheet({
      ref: btsRef,
      title: 'Phương thức thanh toán',
      enableDismiss: true,
      dismiss: () => {
        hideBottomSheet(btsRef);
      },
      children: (
        <View
          key={`payment-list-${payment.id}`}
          style={styles.bottomSheetContainer}>
          {paymentData.map((item, index) => {
            const isSelected = payment.id === item.id;

            return (
              <ListTile
                key={index}
                title={item.name}
                trailing={
                  <TouchableOpacity
                    onPress={() => {
                      onPaymentChange(item);
                      hideBottomSheet(btsRef);
                    }}>
                    {isSelected ? <RadioAction /> : <Radio />}
                  </TouchableOpacity>
                }
                onPress={() => {
                  onPaymentChange(item);
                  hideBottomSheet(btsRef);
                }}
              />
            );
          })}
        </View>
      ),
    });
  };

  return (
    <View style={styles.paymentRow}>
      <View style={styles.paymentInfo}>
        <AppSvg SvgSrc={iconSvg.money} size={20} />
        <Text style={styles.paymentLabel}>Phương thức thanh toán:</Text>
        <Text style={styles.paymentMethod}>{payment.name}</Text>
      </View>
      {!isDone && (
        <TouchableOpacity
          style={styles.changeButton}
          onPress={handlePaymentMethodPress}>
          <Text style={styles.changeButtonText}>Đổi PTTT</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  paymentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    marginLeft: 8,
    marginRight: 4,
    fontSize: 12,
    fontWeight: '700',
  },
  paymentMethod: {
    ...TypoSkin.body2,
    color: '#3FB993',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '700',
  },
  changeButton: {
    padding: 4,
  },
  changeButtonText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.infor_main_color,
  },
  bottomSheetContainer: {
    height: 200,
    width: '100%',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});

export default PaymentMethodSelector;
