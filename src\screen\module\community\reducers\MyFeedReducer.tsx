import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {ComponentStatus} from '../../../../component/component-status';
import {showSnackbar} from '../../../../component/export-component';
import {store} from '../../../../redux/store/store';
import {randomGID, Ultis} from '../../../../utils/Utils';
import {DataController} from '../../../base-controller';
export const FETCH_NEWS_REQUEST = 'FETCH_NEWS_REQUEST';
export const FETCH_NEWS_REQUEST_LOADMORE = 'FETCH_NEWS_REQUEST_LOADMORE';
export const UPDATE_LIKE = 'UPDATE_LIKE';
export const UPDATE_LIKE_COMMENT = 'UPDATE_LIKE_COMMENT';
export const ADD_COMMENT = 'ADD_COMMENT';
export const ADD_BOOKMARK = 'ADD_BOOKMARK';
export const UPDATE_COMMENT_COUNT = 'UPDATE_COMMENT_COUNT';
export const ADD_POST = 'ADD_POST';
export const HIDE_POST = 'HIDE_POST';
export const UPDATE_POST = 'UPDATE_POST';
const initialState: {
  data: any[];
  loading: boolean;
  loadmore: boolean;
  page: number;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  data: [],
  page: 1,
  loading: true,
  loadmore: false,
  success: false,
  error: null,
};
export const MyFeedSlice = createSlice({
  name: 'MyFeed',
  initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case ADD_POST: {
          // Ensure new post is added at the beginning of the array
          state.data = [action.payload.post, ...state.data];
          break;
        }
        case FETCH_NEWS_REQUEST:
          state.data = action.payload.data;
          state.page = action.payload.page;
          break;
        case FETCH_NEWS_REQUEST_LOADMORE:
          // Tạo một Set các ID hiện có để kiểm tra trùng lặp
          const existingIds = new Set(state.data.map((item: any) => item.Id));
          // Chỉ thêm các mục không trùng lặp
          const newItems = action.payload.data.filter(
            (item: any) => !existingIds.has(item.Id),
          );
          state.data = [...state.data, ...newItems];
          state.page = action.payload.page;
          break;
        case UPDATE_LIKE:
          state.data = state.data.map((news: any) => {
            if (news.Id === action.payload.Id) {
              return {
                ...news,
                Likes: action.payload.IsLike
                  ? (news.Likes ?? 0) + 1
                  : (news.Likes ?? 0) - 1,
                IsLike: action.payload.IsLike,
              };
            }
            return news;
          });
          break;
        case ADD_BOOKMARK:
          state.data = state.data.map((news: any) => {
            if (news.Id === action.payload.Id) {
              return {
                ...news,
                IsBookmark: action.payload.IsBookmark,
              };
            }
            return news;
          });
          break;
        case UPDATE_COMMENT_COUNT: {
          const {postId, increment} = action.payload;
          return {
            ...state,
            data: state.data.map(post =>
              post.Id === postId
                ? {...post, Comment: (post.Comment || 0) + increment}
                : post,
            ),
          };
        }
        case HIDE_POST: {
          // Xử lý ẩn bài đăng: cập nhật IsHidden và lọc khỏi danh sách hiển thị
          const postId = action.payload.postId;
          state.data = state.data.filter(post => post.Id !== postId);
          break;
        }
        case UPDATE_POST: {
          // Cập nhật bài đăng trong state
          state.data = state.data.map(post =>
            post.Id === action.payload.post.Id
              ? {
                  ...post,
                  Content: action.payload.post.Content,
                  Img: action.payload.post.Img,
                  DateModified: action.payload.post.DateModified,
                }
              : post,
          );
          break;
        }
        // Remove comment-related cases
      }
      state.loading = false;
      state.loadmore = false;
    },
    onFetching: state => {
      state.loading = true;
    },
    onLoadmore: state => {
      state.loadmore = true;
    },
  },
});
export default MyFeedSlice.reducer;
const {handleActions, onFetching} = MyFeedSlice.actions;
export class myFeedActions {
  static getNewFeed =
    (page: number, size: number, cusId: string) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());

        // Get posts based on authentication status
        const posts = await getPostsForUser(cusId, page, size);
        if (!posts || posts.code !== 200) {
          return;
        }
        // Get all required data in parallel
        const enrichedPosts = await enrichPostsWithData(posts.data, cusId);

        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: 'FETCH_ERROR', error}));
      }
    };
  static updatePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      // Dispatch action để cập nhật post trong Redux store
      dispatch(
        handleActions({
          type: UPDATE_POST,
          post: post,
        }),
      );
      return post;
    } catch (error) {
      console.error('Error updating post in Redux store:', error);
      return null;
    }
  };
  static addBookmark =
    (id: string, isBookmark: boolean) => async (dispatch: Dispatch) => {
      const cusId = store.getState().customer.data?.Id ?? '';

      const controller = new DataController('Post_Bookmark');

      if (!cusId) {
        return;
      }
      if (isBookmark === true) {
        const result = await controller.getListSimple({
          query: `@CustomerId: {${cusId}} @PostsId:{${id}}`,
        });
        if (result.data?.length > 0) {
          const unbookmark = await controller.delete([result.data[0].Id]);
          if (unbookmark.code === 200) {
            dispatch(
              handleActions({
                type: ADD_BOOKMARK,
                Id: id,
                IsBookmark: false,
              }),
            );
          }
        }
      } else {
        const data = {
          Id: randomGID(),
          CustomerId: cusId,
          PostsId: id,
          DateCreated: new Date().getTime(),
        };
        const result = await controller.add([data]);
        if (result.code === 200) {
          dispatch(
            handleActions({
              type: ADD_BOOKMARK,
              Id: id,
              IsBookmark: true,
            }),
          );
        }
      }
    };
  static updateLike =
    (id: string, isUnLike: boolean) => async (dispatch: Dispatch) => {
      const likeController = new DataController('Likes');
      const cusId = store.getState().customer.data?.Id ?? '';

      if (cusId) {
        if (isUnLike === true) {
          const result = await likeController.getListSimple({
            query: `@CustomerId: {${cusId}} @PostsId:{${id}}`,
          });

          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(
                handleActions({
                  type: UPDATE_LIKE,
                  Id: id,
                  IsLike: false,
                }),
              );
            }
          }
        } else {
          const data = {
            Id: randomGID(),
            CustomerId: cusId,
            PostsId: id,
            Type: 1,
            DateCreated: new Date().getTime(),
          };
          const result = await likeController.add([data]);
          if (result.code === 200) {
            dispatch(
              handleActions({
                type: UPDATE_LIKE,
                Id: id,
                IsLike: true,
              }),
            );
          }
        }
      }
    };
  static setLike =
    (id: string, isUnLike: boolean) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: UPDATE_LIKE,
          Id: id,
          IsLike: isUnLike,
        }),
      );
    };
  static setBookmark =
    (id: string, IsBookmark: boolean) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: ADD_BOOKMARK,
          Id: id,
          IsBookmark: IsBookmark,
        }),
      );
    };
  static updateCommentCount =
    (id: string, increment: number) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: UPDATE_COMMENT_COUNT,
          postId: id,
          increment: increment,
        }),
      );
    };
  static addPost = (postData: any) => async (dispatch: Dispatch) => {
    try {
      const customer = store.getState().customer.data;

      if (!customer) {
        return null;
      }
      const postController = new DataController('Posts');
      const response = await postController.add([postData]);
      if (response?.code === 200) {
        // Get user info for the post
        if (customer) {
          const postWithUser = {
            ...postData,
            Likes: 0,
            IsLike: false,
            Comment: 0,
            IsBookmark: false,
            relativeUser: {
              image: customer.AvatarUrl,
              title: customer.Name,
              subtitle: 'Just now',
            },
          };
          // Dispatch action to add post to redux store
          dispatch(
            handleActions({
              type: ADD_POST,
              post: postWithUser,
            }),
          );

          return postWithUser;
        }
      }
      return null;
    } catch (error) {
      console.error('Error adding post:', error);
      return null;
    }
  };
  static hidePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Posts');
      const cusId = store.getState().customer.data?.Id ?? '';

      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể ẩn
      if (post.CustomerId !== cusId) {
        // console.error('Not authorized to hide this post');
        showSnackbar({
          message: 'Bạn không có quyền ẩn bài đăng này',
          status: ComponentStatus.ERROR,
        });
        return false;
      }

      // Cập nhật trạng thái IsHidden của bài đăng
      const updateResult = await postController.edit([
        {
          ...post,
          IsHidden: true,
        },
      ]);

      if (updateResult.code === 200) {
        // Cập nhật state trong Redux
        dispatch(
          handleActions({
            type: HIDE_POST,
            postId: post.Id,
          }),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error hiding post:', error);
      return false;
    }
  };
  static hidePostNocall = (PostId: string) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: HIDE_POST,
        postId: PostId,
      }),
    );
  };
  static deletePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Posts');
      const cusId = store.getState().customer.data?.Id ?? '';

      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể xóa
      // const post = await postController.getById(postId);
      if (!post || post.CustomerId !== cusId) {
        // console.error('Not authorized to delete this post');
        showSnackbar({
          message: 'Bạn không có quyền xóa bài đăng này',
          status: ComponentStatus.ERROR,
        });
        return false;
      }
      const deleteResult = await postController.delete([post.Id]);
      if (deleteResult.code === 200) {
        dispatch(
          handleActions({
            type: HIDE_POST,
            postId: post.Id,
          }),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting post:', error);
      return false;
    }
  };
  static addPostNoCall = (postData: any) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: ADD_POST,
        post: postData,
      }),
    );
  };
}

// Helper functions
async function getPostsForUser(
  cusId: string | null,
  page: number,
  size: number,
) {
  const Postcontroller = new DataController('Posts');
  const query = `@CustomerId:{${cusId}} -@IsHidden:{true}`;
  return Postcontroller.getListSimple({
    page,
    size,
    query,
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
}
async function enrichPostsWithData(posts: any[], cusId: string | null) {
  // Get all unique customer IDs and post IDs
  // const customerIds = [...new Set(posts.map(post => post.CustomerId))];
  const postIds = posts.map(post => post.Id);

  // Fetch all data in parallel
  const [likes, comments, bookmarks] = await Promise.all([
    // fetchCustomers(customerIds),
    fetchLikes(postIds),
    fetchComments(postIds),
    fetchBookmarks(postIds),
  ]);

  // Enrich posts with fetched data
  return posts.map(post => {
    const customer = store.getState().customer.data;
    const postLikes = likes.filter((l: any) => l.PostsId === post.Id);
    const commentCount =
      comments.find((c: any) => c.PostsId === post.Id)?.CommentsCount || 0;
    const isBookmarked = bookmarks.some((b: any) => b.PostsId === post.Id);

    return {
      ...post,
      Likes: postLikes.length,
      IsLike: cusId
        ? postLikes.some((like: any) => like.CustomerId === cusId)
        : false,
      IsBookmark: isBookmarked,
      Comment: commentCount,
      relativeUser: customer
        ? {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: Ultis.getDiffrentTime(post.DateCreated),
          }
        : null,
    };
  });
}

async function fetchCustomers(customerIds: string[]) {
  const customerController = new DataController('Customer');
  const response = await customerController.getListSimple({
    query: `@Id:{${customerIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}

async function fetchLikes(postIds: string[]) {
  const likeController = new DataController('Likes');
  const response = await likeController.getListSimple({
    query: `@PostsId:{${postIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}

async function fetchComments(postIds: string[]) {
  const commentController = new DataController('Comments');
  const response = await commentController.group({
    searchRaw: `@PostsId:{${postIds.join(' | ')}}`,
    reducers: 'LOAD * GROUPBY 1 @PostsId REDUCE COUNT 0 AS CommentsCount',
  });
  return response.code === 200 ? response.data : [];
}

async function fetchBookmarks(postIds: string[]) {
  const bookmarkController = new DataController('Post_Bookmark');
  const response = await bookmarkController.getListSimple({
    query: `@PostsId:{${postIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}
