// Types and interfaces for CreateSurveyStepPage
import {Control, FieldErrors, UseFormRegister} from 'react-hook-form';
import {CustomerItem} from '../../../../../../redux/reducers/user/da';
import {SurveyTaskStep} from '../../..';

/**
 * Main data structure for survey form
 * Organized by logical sections for better maintainability
 */

export interface CreateSurveyStepData {
  Id: string;
  DateCreated: number;
  // Personal Information Section
  Executor?: CustomerItem | null;
  DateStart: number;
  DateEnd: number;
  Description: string;

  // Contact Information Section
  Customer?: CustomerItem | null;

  // Toilet Infrastructure Section
  ToiletCount: string;
  UserCount: string;
  SanitaryToiletCount: string;
  CleaningProcess: number;
  SepticTankLocation: string;
  Capacity: string;
  TreatmentTechnology: number;
  Kitchen: number;
  SeparateDrainage: number;
  PumpingFrequency: number;

  // Energy and Sustainability Section
  RenewableEnergy: number;
  EnergyDetails: string;
  WaterReuse: string;
  WasteClassification: string;
}

/**
 * Props for the main CreateSurveyStep component
 */
export interface CreateSurveyStepProps {
  changeStep: (step: SurveyTaskStep) => void;
  initialData?: Partial<CreateSurveyStepData> | null;
  step: SurveyTaskStep;
}

/**
 * Base props for form sections with proper typing
 */
export interface FormSectionProps {
  control: Control<CreateSurveyStepData>;
  errors: FieldErrors<CreateSurveyStepData>;
  register: UseFormRegister<CreateSurveyStepData>;
}

/**
 * Props for PersonalInfoSection component
 */
export interface PersonalInfoSectionProps extends FormSectionProps {
  isEdit: boolean;
}

/**
 * Props for ContactInfoSection component
 */
export interface ContactInfoSectionProps extends FormSectionProps {
  isEdit: boolean;
  onCreateCustomer?: () => void;
}

/**
 * Props for ToiletInfoSection component
 */
export interface ToiletInfoSectionProps extends FormSectionProps {
  isEdit: boolean;
}

/**
 * Props for IconRenderer component
 */
export interface IconRendererProps {
  iconName: string;
  size?: number;
  color?: string;
}

/**
 * Props for ActionButtons component
 */
export interface ActionButtonsProps {
  onCancel: () => void;
  onConfirm: () => void;
  isSubmitting?: boolean;
  style?: any;
}
