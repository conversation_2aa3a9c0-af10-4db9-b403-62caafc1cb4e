import { useRef } from "react"
import { Dimensions, ScrollView, View } from "react-native"
import { Winicon } from "../../../../../component/export-component"
import { ColorThemes } from "../../../../../assets/skin/colors"
import { Text } from "react-native"
import { <PERSON><PERSON><PERSON>kin } from "../../../../../assets/skin/typography"
import AppButton from "../../../../../component/button"
import WebView from "react-native-webview"
import { showPopup, closePopup, FPopup } from "../../../../../component/popup/popup"
import ConfigAPI from "../../../../../config/configApi"
import ScreenHeader from "../../../../layout/header"
import ListTile from "../../../../../component/list-tile/list-tile"
import { SkeletonImage } from "../../../../../project-component/skeleton-img"
import { Ultis } from "../../../../../utils/Utils"

export const ButtonViewRejectReason = ({ customers, rejectReasons }: { customers: Array<any>, rejectReasons: Array<any> }) => {
    const popupRef = useRef<any>()
    return <View style={{ width: "100%" }}>
        <FPopup ref={popupRef} />
        <ListTile leading={<Winicon src="fill/layout/circle-info" size={20} color={ColorThemes.light.error_main_color} />}
            style={{ padding: 0, backgroundColor: ColorThemes.light.error_background, paddingHorizontal: 16, paddingVertical: 8 }}
            title={<Text style={{ ...TypoSkin.buttonText3, color: ColorThemes.light.error_main_color }}>Lý do từ chối</Text>}
            onPress={() => {
                showPopup({
                    ref: popupRef,
                    enableDismiss: true,
                    children: <View style={{ backgroundColor: ColorThemes.light.neutral_absolute_background_color, height: Dimensions.get('window').height - 165, borderTopLeftRadius: 12, borderTopRightRadius: 12, }}>
                        <ScreenHeader
                            style={{
                                backgroundColor: ColorThemes.light.transparent,
                                flexDirection: 'row',
                                paddingVertical: 4
                            }}
                            title={`Chi tiết từ chối`}
                            prefix={<View />}
                            action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                                <Winicon src="outline/layout/xmark" onClick={() => closePopup(popupRef)} size={20} color={ColorThemes.light.neutral_text_body_color} />
                            </View>}
                        />
                        <ScrollView style={{ paddingHorizontal: 16 }}>
                            {rejectReasons.map((dt, i) => {
                                const customer = dt.CustomerId ? customers.find(e => dt.CustomerId === e.Id) : customers[0]
                                return <ListTile key={i} style={{ padding: 0, borderBottomColor: ColorThemes.light.neutral_main_border_color, borderBottomWidth: 1, paddingVertical: 16 }}
                                    leading={customer?.Img ? <SkeletonImage source={{ uri: customer.Img.startsWith("https") ? customer.Img : (ConfigAPI.imgUrlId + customer?.Img) }} style={{ width: 32, height: 32, borderRadius: 50, objectFit: "cover" }} /> : <View style={{ width: 32, height: 32, borderRadius: 50, alignItems: "center", justifyContent: "center", backgroundColor: Ultis.generateDarkColorRgb(), }}><Text style={{ color: "#fff", display: "flex", justifyContent: "center", alignItems: "center" }}>{customer?.Name?.substring(0, 1)}</Text></View>}
                                    title={`${customer?.Name ?? "-"}`} subtitle={`${dt.DateCreated ? Ultis.datetoString(new Date(dt.DateCreated), "dd/MM/yyyy hh:mm") : ""}`}
                                    bottom={<View style={{ flex: 1, alignItems: "flex-start", paddingTop: 8, width: "100%" }}>
                                        <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_body_color }}>
                                            Lý do từ chối: {dt.Content ?? ""}
                                        </Text>
                                    </View>}
                                />
                            })}
                        </ScrollView>
                    </View>
                })
            }}
            trailing={<View><Text style={{ ...TypoSkin.subtitle3 }}>Xem chi tiết</Text></View>} />
    </View>
}
