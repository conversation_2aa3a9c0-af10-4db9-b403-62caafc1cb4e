import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import {Ultis} from '../../../../../../../utils/Utils';
import ConfigAPI from '../../../../../../../config/configApi';
import {RootScreen} from '../../../../../../../router/router';

interface OrderItemsProps {
  orderDetails: any[];
  order: any;
  type?: string;
}

const OrderItems: React.FC<OrderItemsProps> = ({orderDetails, order, type}) => {
  const navigation = useNavigation<any>();

  return (
    <View style={styles.orderItemsContainer}>
      {orderDetails && orderDetails?.length > 0
        ? orderDetails?.map((item, index) => (
            <TouchableOpacity
              onPress={() =>
                navigation.push(RootScreen.PostDetail, {
                  id: item.ProductId,
                })
              }
              key={item.Id}
              style={styles.orderItemRow}>
              <FastImage
                style={styles.productImage}
                source={{
                  uri: item.Img?.startsWith('http')
                    ? item.Img
                    : `${ConfigAPI.imgUrlId}${item.Img}`,
                  priority: FastImage.priority.normal,
                }}
                resizeMode={FastImage.resizeMode.cover}
              />
              <View style={styles.productDetails}>
                <Text style={styles.productName}>
                  {item?.Name || 'Sản phẩm'}
                </Text>
                <View style={styles.productPriceRow}>
                  {item?.Discount ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 8,
                        alignItems: 'center',
                      }}>
                      <Text style={styles.originalPrice}>
                        {Ultis.money(item.Price ?? 0)} đ
                      </Text>
                      <Text style={styles.productPrice}>
                        {Ultis.money(
                          item.Price - (item.Price * item.Discount) / 100,
                        )}{' '}
                        đ
                      </Text>
                    </View>
                  ) : (
                    <Text style={styles.productPrice}>
                      {Ultis.money(item.Price ?? 0)} đ
                    </Text>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))
        : null}

      {/* Tổng tiền */}
      {!type ? (
        <>
          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>Tổng hoàn CANPOINT:</Text>
            <Text style={{...TypoSkin.heading7, color: '#FF3B30'}}>
              {Ultis.money(
                orderDetails?.reduce(
                  (total: number, item: any) => total + (item.Reward ?? 0),
                  0,
                ) ?? 0,
              )}{' '}
              đ
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingTop: 16,
            }}>
            <Text style={styles.totalLabel}>
              Tổng tiền ({orderDetails?.length ?? 0} sản phẩm):
            </Text>
            <Text style={styles.totalPrice}>
              {Ultis.money(order?.Value ?? 0)} đ
            </Text>
          </View>
        </>
      ) : (
        <View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingTop: 16,
            }}>
            <Text style={styles.totalLabel}>
              Tổng tiền ({orderDetails?.length ?? 0} sản phẩm):
            </Text>
            <Text style={styles.totalPrice}>
              {Ultis.money(order?.Value ?? 0)} đ
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  orderItemsContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  orderItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.body3,
    color: '#212121',
    marginBottom: 4,
  },
  productPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  productPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
    marginRight: 8,
  },
  originalPrice: {
    ...TypoSkin.body3,
    color: '#757575',
    textDecorationLine: 'line-through',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
  },
  totalLabel: {
    ...TypoSkin.body2,
    color: '#212121',
  },
  totalPrice: {
    ...TypoSkin.heading6,
    color: '#FF3B30',
  },
});

export default OrderItems;
