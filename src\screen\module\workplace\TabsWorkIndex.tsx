import {useWindowDimensions, View, StyleSheet} from 'react-native';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from 'redux/hooks/hooks';
import React, {useEffect, useState} from 'react';
import {ColorThemes} from 'assets/skin/colors';
import {TabBar, TabView} from 'react-native-tab-view';
import MyWorkList from './MyWorkList';
import ConfigAPI from 'config/configApi';
import {CustomerRole} from 'redux/reducers/user/da';
import EmptyPage from 'project-component/empty-page';
import HeaderLogo from 'screen/layout/headers/HeaderLogo';
import MyOrderTab from './MyOrderTab';
import NewOrderWorkList from './NewOrderWorkList';
import {RoleDa} from '../role/roleDa';

export default function TabsWorkIndex() {
  const userRole = useSelectorCustomerState().role;
  const layout = useWindowDimensions();
  const company = useSelectorCustomerCompanyState().data;
  const customer = useSelectorCustomerState().data;
  const roleDa = new RoleDa();

  const [index, setIndex] = useState(0);

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      activeColor={ColorThemes.light.primary_main_color}
      indicatorStyle={styles.tabIndicator}
      onTabPress={() => {}}
      tabStyle={styles.tabStyle}
      inactiveColor={ColorThemes.light.neutral_text_subtitle_color}
      style={styles.tabBarStyle}
    />
  );

  const renderScene = ({route}: any) => {
    switch (route.key) {
      case 'work_orders':
        return <NewOrderWorkList />;
      case 'my_orders':
        return <MyOrderTab />;
      case 'myjob':
        return <MyWorkList />;
      default:
        return null;
    }
  };

  const [routes, setRoutes] = useState<any[]>([]);

  const handleGetRole = async () => {
    try {
      if (!customer?.Id) return;

      const roleInfo = await roleDa.getCustomerRole(customer.Id);
      if (roleInfo.role === 'partner') {
        setRoutes([
          {key: 'work_orders', title: 'Đơn hàng cần làm'},
          {key: 'my_orders', title: 'Đơn hàng của tôi'},
          {key: 'myjob', title: 'Việc của tôi'},
        ]);
        setIndex(0);
      } else {
        setRoutes([
          {key: 'my_orders', title: 'Đơn hàng của tôi'},
          {key: 'myjob', title: 'Việc của tôi'},
        ]);
        setIndex(0);
      }
    } catch (error) {
      console.error('Error getting customer role:', error);
    }
  };

  useEffect(() => {
    handleGetRole();
  }, [customer?.Id]);

  return (
    <View style={styles.container}>
      <HeaderLogo />
      {/* tabs */}
      {routes.length > 0 && (
        <TabView
          navigationState={{index, routes}}
          renderScene={renderScene}
          renderTabBar={renderTabBar}
          onIndexChange={setIndex}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabIndicator: {
    backgroundColor: ColorThemes.light.primary_main_color,
    height: 1.5,
  },
  tabStyle: {
    paddingHorizontal: 4,
    paddingTop: 0,
  },
  tabBarStyle: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    height: 45,
  },
});
