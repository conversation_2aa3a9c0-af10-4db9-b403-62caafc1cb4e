import {
  FlatList,
  RefreshControl,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import {ColorThemes} from 'assets/skin/colors';
import React, {useEffect, useState} from 'react';
import {
  useSelectorCustomerState,
  useSelectorNotificationState,
} from 'redux/hooks/hooks';
import {TypoSkin} from 'assets/skin/typography';
import NotificationCard from '../components/card/notification-card';
import {CardNotificationSkeleton} from '../components/card/notification-shimmer';
import {NotificationActions} from 'redux/reducers/notification/reducer';
import {useDispatch} from 'react-redux';
import EmptyPage from 'project-component/empty-page';
import TitleHeader from 'screen/layout/headers/TitleHeader';

export default function NotificationIndex() {
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const noti = useSelectorNotificationState();
  const [isRefreshing, setRefresh] = useState(false);
  const dispatch = useDispatch<any>();
  const [tab, setTab] = useState(0);

  const getData = () => {
    NotificationActions.getData(dispatch, user?.Id ?? '', {
      page: 1,
      status: tab ? 0 : undefined,
    });
  };

  useEffect(() => {
    if (user) {
      getData();
    }
  }, [user, userRole, tab]);

  const onRefresh = async () => {
    setRefresh(true);
    if (user) getData();
    if (noti.onLoading === false) {
      setRefresh(false);
    } else {
      setTimeout(() => {
        setRefresh(false);
      }, 2000);
    }
  };

  return (
    <View style={styles.safeArea}>
      <View style={styles.container}>
        <TitleHeader title="Thông báo" />
        <View style={styles.content}>
          <View style={styles.tabContainer}>
            <TouchableOpacity
              onPress={() => {
                setTab(0);
              }}
              style={[
                styles.tabButton,
                {
                  backgroundColor:
                    tab === 0
                      ? ColorThemes.light.primary_main_color
                      : ColorThemes.light.neutral_absolute_background_color,
                },
              ]}>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    color:
                      tab === 0
                        ? ColorThemes.light.neutral_absolute_background_color
                        : ColorThemes.light
                            .neutral_absolute_reverse_background_color,
                  },
                ]}>
                Tất cả
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setTab(1);
              }}
              style={[
                styles.tabButton,
                {
                  backgroundColor:
                    tab === 1
                      ? ColorThemes.light.primary_main_color
                      : ColorThemes.light.neutral_absolute_background_color,
                },
              ]}>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    color:
                      tab === 1
                        ? ColorThemes.light.neutral_absolute_background_color
                        : ColorThemes.light
                            .neutral_absolute_reverse_background_color,
                  },
                ]}>
                Chưa đọc
              </Text>
            </TouchableOpacity>
          </View>
          {noti.data.length === 0 && !noti.onLoading ? (
            <View style={styles.emptyPageContainer}>
              <EmptyPage title="Bạn không có thông báo nào" />
            </View>
          ) : (
            <FlatList
              nestedScrollEnabled={true}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={onRefresh}
                />
              }
              style={styles.flatList}
              data={noti.data}
              renderItem={({item}) => {
                return <NotificationCard tab={tab} item={item} />;
              }}
              keyExtractor={(item, i) => `${i}`}
              ListEmptyComponent={() => {
                if (noti.onLoading)
                  return Array.from({length: 5}).map((_, i) => (
                    <CardNotificationSkeleton key={i} />
                  ));
              }}
            />
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    flex: 1,
    alignContent: 'center',
  },
  tabContainer: {
    marginTop: 16,
    marginBottom: 8,
    height: 40,
    paddingVertical: 4,
    paddingHorizontal: 16,
    gap: 16,
    flexDirection: 'row',
  },
  tabButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  emptyPageContainer: {
    flex: 1,
  },
  flatList: {
    flex: 1,
    height: '100%',
    alignContent: 'center',
  },
});
