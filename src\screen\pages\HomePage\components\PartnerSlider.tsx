import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {navigate, RootScreen} from '../../../../router/router';
import brandDa, {BrandItem} from '../../../module/brand/brandDa';

const {width} = Dimensions.get('window');

const PartnerSlider: React.FC = React.memo(() => {
  const [activeSlide, setActiveSlide] = useState(0);
  const [brands, setBrands] = useState<BrandItem[]>([]);
  const [loading, setLoading] = useState(true);
  const scrollViewRef = useRef<ScrollView>(null);
  const autoScrollInterval = useRef<NodeJS.Timeout | null>(null);

  // Calculate item width for proper scrolling
  const itemWidth = width / 3;

  // Fetch hot brands data
  const fetchHotBrands = useCallback(async () => {
    try {
      setLoading(true);
      const hotBrands = await brandDa.fetch({
        searchRaw: '@IsHot:{true} @ParentId:{empty}',
        page: 1,
        size: 20,
        sortby: [{prop: 'Sort', direction: 'ASC'}],
      });
      setBrands(hotBrands);
    } catch (error) {
      console.error('Error fetching hot brands:', error);
      setBrands([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-scroll function
  const startAutoScroll = useCallback(() => {
    if (autoScrollInterval.current) {
      clearInterval(autoScrollInterval.current);
    }

    if (brands.length === 0) return;

    autoScrollInterval.current = setInterval(() => {
      setActiveSlide(prevIndex => {
        const nextIndex = (prevIndex + 1) % brands.length;

        // Scroll to the next item
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollTo({
            x: nextIndex * itemWidth,
            animated: true,
          });
        }

        return nextIndex;
      });
    }, 3000); // Auto-scroll every 3 seconds
  }, [itemWidth, brands.length]);

  // Fetch data when component mounts
  useEffect(() => {
    fetchHotBrands();
  }, [fetchHotBrands]);

  // Start auto-scroll when brands are loaded
  useEffect(() => {
    if (brands.length > 0) {
      startAutoScroll();
    }

    return () => {
      if (autoScrollInterval.current) {
        clearInterval(autoScrollInterval.current);
      }
    };
  }, [startAutoScroll, brands.length]);

  // Handle manual scroll to update active slide
  const handleScroll = useCallback(
    (event: any) => {
      const scrollX = event.nativeEvent.contentOffset.x;
      const currentIndex = Math.round(scrollX / itemWidth);
      setActiveSlide(currentIndex);
    },
    [itemWidth],
  );

  // Stop auto-scroll when user starts scrolling manually
  const handleScrollBeginDrag = useCallback(() => {
    if (autoScrollInterval.current) {
      clearInterval(autoScrollInterval.current);
    }
  }, []);

  // Resume auto-scroll after user stops scrolling
  const handleScrollEndDrag = useCallback(() => {
    // Resume auto-scroll after 2 seconds of inactivity
    setTimeout(() => {
      startAutoScroll();
    }, 2000);
  }, [startAutoScroll]);

  const handlePress = (item: BrandItem) => {
    navigate(RootScreen.DetailBrand, {id: item.Id});
  };

  // Show loading state
  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Đối tác uy tín</Text>
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_main_color}
          />
        </View>
      </View>
    );
  }

  // Show empty state if no brands
  if (brands.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Đối tác uy tín</Text>
        <Text style={styles.emptyText}>
          Không có thương hiệu nào được tìm thấy
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Đối tác uy tín</Text>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.sliderContainer}
        onScroll={handleScroll}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        scrollEventThrottle={16}
        snapToInterval={itemWidth}
        snapToAlignment="start"
        decelerationRate="fast">
        {brands.map(item => (
          <TouchableOpacity
            key={item.Id}
            style={styles.slide}
            onPress={() => handlePress(item)}>
            <Image
              source={{uri: item.Img}}
              style={styles.image}
              resizeMode="contain"
            />
            <Text style={styles.brandName} numberOfLines={1}>
              {item.Name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      <View style={styles.paginationContainer}>
        {brands.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              activeSlide === index ? styles.activeDot : styles.inactiveDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
});

PartnerSlider.displayName = 'PartnerSlider';

export default PartnerSlider;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingVertical: 20,
  },
  title: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 20,
    textAlign: 'center',
  },
  loadingContainer: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginTop: 20,
  },
  sliderContainer: {
    flexDirection: 'row',
  },
  slide: {
    justifyContent: 'center',
    alignItems: 'center',
    width: width / 3,
    padding: 10,
    borderRadius: 8,
  },
  image: {
    width: '100%',
    height: 50,
    marginBottom: 8,
  },
  brandName: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
    textAlign: 'center',
    fontSize: 12,
  },
  paginationContainer: {
    flexDirection: 'row',
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  activeDot: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  inactiveDot: {
    backgroundColor: ColorThemes.light.primary_border_color,
  },
});
