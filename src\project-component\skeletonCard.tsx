import { Pressable } from "react-native";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

export const CardHoriSkeleton = () => {
    return (
        <Pressable style={{ paddingHorizontal: 12 }}>
            <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item flexDirection="row" gap={16} alignItems="center">
                    <SkeletonPlaceholder.Item height={56} width={56} borderRadius={100} />
                    <SkeletonPlaceholder.Item flexDirection="column">
                        <SkeletonPlaceholder.Item marginTop={12}>
                            <SkeletonPlaceholder.Item height={22} />
                        </SkeletonPlaceholder.Item>
                        <SkeletonPlaceholder.Item marginTop={8}>
                            <SkeletonPlaceholder.Item height={16} width={200} />
                            <SkeletonPlaceholder.Item height={16} width={300} marginTop={8} />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
        </Pressable>
    );
};

export const CardOrderHoriSkeleton = () => {
    return (
        <Pressable style={{ paddingHorizontal: 12 }}>
            <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item flexDirection="row" gap={16} alignItems="center">
                    <SkeletonPlaceholder.Item flexDirection="column">
                        <SkeletonPlaceholder.Item marginTop={12}>
                            <SkeletonPlaceholder.Item height={22} />
                        </SkeletonPlaceholder.Item>
                        <SkeletonPlaceholder.Item marginTop={8}>
                            <SkeletonPlaceholder.Item height={16} width={300} />
                            <SkeletonPlaceholder.Item height={16} width={400} marginTop={8} />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
        </Pressable>
    );
};

export const CardToiletHoriSkeleton = () => {
    return (
        <Pressable style={{ paddingHorizontal: 12 }}>
            <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item flexDirection="row" gap={16} alignItems="center">
                    <SkeletonPlaceholder.Item flexDirection="column">
                        <SkeletonPlaceholder.Item height={22} />
                        <SkeletonPlaceholder.Item marginTop={8}>
                            <SkeletonPlaceholder.Item height={25} width={400} marginTop={8} />
                        </SkeletonPlaceholder.Item>
                        <SkeletonPlaceholder.Item marginTop={8}>
                            <SkeletonPlaceholder.Item height={25} width={400} marginTop={8} />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
        </Pressable>
    );
};
