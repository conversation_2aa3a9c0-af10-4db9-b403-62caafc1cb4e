import {configureStore} from '@reduxjs/toolkit';
import locationReducer from '../location-reducer';
import notificationReducer from '../reducers/notification/reducer';
import servicesReducer from '../reducers/cateServices/reducer';
import customerReducer from '../reducers/user/reducer';
import companyReducer from '../reducers/company/reducer';
import toiletReducer from '../reducers/toilet/reducer';
import MyFeedReducer from '../../screen/module/community/reducers/MyFeedReducer';
import followingGroupsReducer from '../../screen/module/community/reducers/followingGroupsReducer';
import groupPostsReducer from '../../screen/module/community/reducers/groupPostsReducer';
import groupReducer from '../../screen/module/community/reducers/groupReducer';
import myGroupsReducer from '../../screen/module/community/reducers/myGroupsReducer';
import postCommentsReducer from '../../screen/module/community/reducers/postCommentsReducer';
import newsFeedReducer from '../../screen/module/community/reducers/newsFeedReducer';
import productByCategoryReducer from '../reducers/ProductByCategoryReducer';
import productReducer from '../reducers/productReducer';
import {cartSlice} from '../reducers/cart/CartReducer';
import favoriteProductReducer from '../reducers/FavoriteProductReducer';
import partnerReducer from '../reducers/partner/reduce';
import orderReducer from '../reducers/order/OrderReducer';

export const store = configureStore({
  reducer: {
    location: locationReducer,
    notification: notificationReducer,
    services: servicesReducer,
    customer: customerReducer,
    company: companyReducer,
    toilet: toiletReducer,
    // community
    newsFeed: newsFeedReducer,
    group: groupReducer,
    followingGroups: followingGroupsReducer,
    myGroups: myGroupsReducer,
    postComments: postCommentsReducer,
    groupPosts: groupPostsReducer,
    myFeed: MyFeedReducer,
    productByCategory: productByCategoryReducer,
    product: productReducer,
    cart: cartSlice.reducer,
    favoriteProduct: favoriteProductReducer,
    partner: partnerReducer,
    order: orderReducer,
  },
});

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
