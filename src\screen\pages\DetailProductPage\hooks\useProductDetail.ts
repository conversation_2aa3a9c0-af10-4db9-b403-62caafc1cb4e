import {useState, useEffect, useCallback, useMemo} from 'react';
import {ProductData, ShopData} from '../types';
import {getImage} from '../../../../redux/actions/rootAction';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {DataController} from '../../../base-controller';
import productDA from '../../../module/product/productDA';
import {StatusOrder, TypeMenuPorduct} from '../../../../config/Contanst';
import ConfigAPI from '../../../../config/configApi';

interface UseProductDetailResult {
  loading: boolean;
  refreshing: boolean;
  data: ProductData | null;
  shop: ShopData | null;
  productImages: string[];
  like: boolean;
  setLike: (value: boolean) => void;
  getData: () => Promise<void>;
  onRefresh: () => void;
  setLoading: (value: boolean) => void;
}

export const useProductDetail = (productId: string): UseProductDetailResult => {
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [data, setData] = useState<ProductData | null>(null);
  const [shop, setShop] = useState<ShopData | null>(null);
  const [productImages, setProductImages] = useState<string[]>([]);
  const [like, setLike] = useState<boolean>(true);

  const customer = useSelectorCustomerState().data;

  // Data controllers - memoized to prevent recreation
  const productFavorite = useMemo(
    () => new DataController('ProductFavorite'),
    [],
  );
  const ratingController = useMemo(() => new DataController('Rating'), []);
  const productController = useMemo(() => new DataController('Product'), []);
  const orderController = useMemo(() => new DataController('Order'), []);

  // Helper function to fetch shop rating
  const fetchShopRating = useCallback(
    async (
      shopId: string,
    ): Promise<{rating: number; totalRate: number; countRate: number}> => {
      const ratingShop = await ratingController.group({
        reducers:
          'LOAD * GROUPBY 1 @ShopId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
        searchRaw: `@ShopId: {${shopId}}`,
      });

      if (ratingShop.code === 200) {
        const totalRate = parseFloat(ratingShop.data[0]?.TotalRate || '0');
        const countRate = parseFloat(ratingShop.data[0]?.CountRate || '0');
        return {
          rating: countRate > 0 ? totalRate / countRate : 0,
          totalRate,
          countRate,
        };
      }
      return {rating: 0, totalRate: 0, countRate: 0};
    },
    [ratingController],
  );

  // Helper function to fetch shop statistics (total products and orders)
  const fetchShopStats = useCallback(
    async (
      shopId: string,
    ): Promise<{totalProducts: number; totalOrder: number}> => {
      const [totalProductsResult, totalOrderResult] = await Promise.all([
        productController.group({
          reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE COUNT 0 AS CountProduct',
          searchRaw: `@ShopId: {${shopId}}`,
        }),
        orderController.group({
          reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE COUNT 0 AS CountOrder',
          searchRaw: `@ShopId: {${shopId}} @Status: [${StatusOrder.success}]`,
        }),
      ]);

      const totalProducts =
        totalProductsResult.code === 200
          ? totalProductsResult.data[0]?.CountProduct || 0
          : 0;

      const totalOrder =
        totalOrderResult.code === 200
          ? totalOrderResult.data[0]?.CountOrder || 0
          : 0;

      return {totalProducts, totalOrder};
    },
    [productController, orderController],
  );

  // Helper function to fetch related products from same shop and category
  const fetchRelatedProducts = useCallback(
    async (
      shopId: string,
      categoryId: string,
      currentProductId: string,
    ): Promise<ProductData[]> => {
      const listProduct = await productController.getListSimple({
        page: 1,
        size: 10,
        query: `@ShopId: {${shopId}} @Status: [${TypeMenuPorduct.InStock.id}] @CategoryId: {${categoryId}}`,
      });

      if (listProduct.code === 200) {
        const data = await getImage({items: listProduct.data});
        return data?.filter((item: any) => item?.Id !== currentProductId) || [];
      }
      return [];
    },
    [productController, getImage],
  );

  // Xử lý các dữ liệu của shop
  const handleDataShop = useCallback(
    async (shopData: ShopData, categoryId: string) => {
      const shopStats = await fetchShopStats(shopData.Id);
      const shopRating = await fetchShopRating(shopData.Id);
      const relatedProducts = await fetchRelatedProducts(
        shopData.Id,
        categoryId,
        productId,
      );
      const shop = {
        ...shopData,
        totalProducts: shopStats.totalProducts,
        totalOrder: shopStats.totalOrder,
        rating: shopRating.rating,
        products: relatedProducts,
      };
      return shop;
    },
    [fetchShopRating, fetchShopStats],
  );

  // Helper function to process product images
  const processProductImages = useCallback((product: ProductData): string[] => {
    if (!product?.ListImg && !product?.Img) {
      return [];
    }

    let listImg: (string | undefined)[] = product?.ListImg
      ? product.ListImg.split(',')
      : [product.Img];

    const filteredImg = listImg.filter(
      (item): item is string => item !== '' && item !== undefined,
    );

    return filteredImg.map((item: string) => `${ConfigAPI.imgUrlId}${item}`);
  }, []);

  // Helper function to fetch product rating
  const fetchProductRating = useCallback(
    async (
      productId: string,
    ): Promise<{rating: number; totalRate: number; countRate: number}> => {
      const ratingResult = await ratingController.group({
        reducers:
          'LOAD * GROUPBY 1 @ProductId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
        searchRaw: `@ProductId: {${productId}}`,
      });

      if (ratingResult.code === 200) {
        const totalRate = parseFloat(ratingResult.data[0]?.TotalRate || '0');
        const countRate = parseFloat(ratingResult.data[0]?.CountRate || '0');
        return {
          rating: countRate > 0 ? totalRate / countRate : 0,
          totalRate,
          countRate,
        };
      }
      return {rating: 0, totalRate: 0, countRate: 0};
    },
    [ratingController],
  );

  // Helper function to check if product is favorite
  const checkProductFavorite = useCallback(
    async (productId: string, customerId?: string): Promise<boolean> => {
      if (!customerId) {
        return false;
      }

      const favoriteResult = await productFavorite.getListSimple({
        page: 1,
        size: 1,
        query: `@ProductId:{${productId}} @CustomerId:{${customerId}}`,
      });

      return favoriteResult.code === 200 && favoriteResult.data.length > 0;
    },
    [productFavorite],
  );

  // Helper function to enhance product with brand and category names
  const enhanceProductWithNames = useCallback(
    (product: ProductData, result: any): void => {
      if (product?.BrandId && result?.Brand?.[0]) {
        product.BrandName = result.Brand[0].Name;
      }
      if (product?.CategoryId && result?.Category?.[0]) {
        product.CategoryName = result.Category[0].Name;
      }
    },
    [],
  );

  const getData = useCallback(async (): Promise<void> => {
    try {
      if (!productId) return;

      const result = await productDA.getProductDetail(productId);
      if (!result || !result.data?.length) return;

      const product = result.data[0];
      let shopData = null;
      if (result.Shop?.length) {
        shopData = await handleDataShop(result.Shop[0], product.CategoryId);
      }

      // Process product images
      const images = processProductImages(product);
      setProductImages(images);

      // Enhance product with brand and category names
      enhanceProductWithNames(product, result);

      // Fetch product rating and favorite status in parallel
      const [productRating, isFavorite] = await Promise.all([
        fetchProductRating(product.Id),
        checkProductFavorite(product.Id, customer?.Id),
      ]);

      // Create updated product object without mutation
      const updatedProduct = {
        ...product,
        ...productRating,
        IsFavorite: isFavorite,
      };

      setLike(isFavorite);
      setData(updatedProduct);
      setShop(shopData);
    } catch (error) {
      console.error('Error fetching product data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [
    productId,
    processProductImages,
    enhanceProductWithNames,
    fetchProductRating,
    checkProductFavorite,
    customer?.Id,
  ]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getData();
  }, [getData]);

  useEffect(() => {
    getData();
  }, [productId]);

  return {
    loading,
    refreshing,
    data,
    shop,
    productImages,
    like,
    setLike,
    getData,
    onRefresh,
    setLoading,
  };
};
