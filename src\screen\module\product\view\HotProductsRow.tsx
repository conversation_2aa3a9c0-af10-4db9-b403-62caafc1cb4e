import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Dimensions,
  Pressable,
} from 'react-native';
import SquareProductCard from '../component/SquareProductCard';
import AppButton from '../../../../component/button';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

const {width} = Dimensions.get('window');

// <PERSON><PERSON>ch thước của mỗi item sản phẩm
const ITEM_WIDTH = width * 0.32;
const ITEM_HEIGHT = ITEM_WIDTH * 1.5;
const ITEM_SPACING = 10;

// Tạo component ItemSeparator bên ngoài component chính
const ItemSeparator = React.memo(() => <View style={{width: ITEM_SPACING}} />);

interface HotProductsRowProps {
  title?: string;
  products: any[];
  onSeeAll?: () => void;
  onProductPress?: (product: any) => void;
  showRating?: boolean;
}

const HotProductsRow: React.FC<HotProductsRowProps> = ({
  title = 'Sản phẩm HOT',
  products,
  onSeeAll,
  onProductPress,
  showRating = true,
}) => {
  const renderItem = ({item}: {item: any}) => {
    return (
      <SquareProductCard
        item={item}
        onPress={onProductPress}
        width={ITEM_WIDTH}
        height={ITEM_HEIGHT}
        showRating={showRating}
      />
    );
  };

  return (
    <Pressable style={{height: 330}}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <AppButton
          title={'Xem thêm'}
          containerStyle={{
            justifyContent: 'flex-start',
            alignSelf: 'baseline',
          }}
          backgroundColor={'transparent'}
          textStyle={{
            ...TypoSkin.title5,
            fontWeight: 500,
            color: ColorThemes.light.primary_main_color,
          }}
          borderColor="transparent"
          suffixIconSize={16}
          // suffixIcon={'outline/arrows/circle-arrow-right'}
          onPress={onSeeAll}
          textColor={ColorThemes.light.secondary3_text_color}
        />
      </View>

      <FlatList
        data={products}
        renderItem={renderItem}
        keyExtractor={(item, index) => item?.Id?.toString() || index.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        snapToInterval={ITEM_WIDTH + ITEM_SPACING}
        snapToAlignment="start"
        decelerationRate="fast"
        pagingEnabled={false}
        disableIntervalMomentum={true}
        ItemSeparatorComponent={ItemSeparator}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    ...TypoSkin.subtitle1,
    fontWeight: 'bold',
  },
  listContent: {
    paddingVertical: 8,
    paddingRight: 16,
  },
});

export default HotProductsRow;
