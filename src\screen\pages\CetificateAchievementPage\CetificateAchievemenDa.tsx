import ConfigAPI from '../../../config/configApi';
import {getImage} from '../../../redux/actions/rootAction';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../base-controller';

export class CetificateAchievemenDa {
  private ToiletOperationalController: DataController;
  private ToiletCertificateController: DataController;
  private ToiletLogCateController: DataController;
  private ToiletController: DataController;
  private CompanyProfileController: DataController;
  private CustomerController: DataController;
  private ToiletServiceCriterionController: DataController;
  private ToiletServiceController: DataController;
  private ToiletLogController: DataController;
  private CatecriterionController: DataController;

  constructor() {
    this.ToiletOperationalController = new DataController('ToiletOperational');
    this.ToiletCertificateController = new DataController('ToiletCertificate');
    this.ToiletLogCateController = new DataController('ToiletLog');
    this.ToiletController = new DataController('Toilet');
    this.CompanyProfileController = new DataController('CompanyProfile');
    this.CustomerController = new DataController('Customer');
    this.ToiletServiceCriterionController = new DataController(
      'ToiletServiceCriterion',
    );
    this.ToiletServiceController = new DataController('ToiletServices');
    this.ToiletLogController = new DataController('ToiletLog');
    this.CatecriterionController = new DataController('CateCriterion');
  }
  async getOperationalData(ToiletId: any, toiletServicesId: any) {
    console.log('check-toiletServicesId', toiletServicesId);
    try {
      let query = `@ToiletServicesId: {${toiletServicesId}}`;
      const respone = await this.ToiletOperationalController.getListSimple({
        page: 1,
        size: 10000,
        query: query,
      });
      if (respone?.code === 200) {
        let ToiletId = respone.data[0].ToiletId;
        let ToiletServicesId = respone.data[0].ToiletServicesId;
        let toiletServiceCriterion =
          await this.ToiletServiceController.getListSimple({
            page: 1,
            size: 10000,
            query: `@ToiletId: {${ToiletId}} @Id: {${ToiletServicesId}}`,
          });
        let dataMap = respone.data.map((item: any) => {
          return {
            ...item,
            ToiletServices: toiletServiceCriterion.data[0],
          };
        });

        return {
          code: 200,
          data: dataMap,
        };
      }
    } catch (error) {
      console.log(`check-error`, error);
    }
  }
  async getOperationalDataByAdmin() {
    try {
      const respone = await this.ToiletOperationalController.getAll();
      if (respone?.code === 200) {
        let data = await getImage({items: respone.data});

        // Lọc ra những item có trường approver khác rỗng
        const filteredData = data.filter((item: any) => {
          return item.Status === 1;
        });
        return {
          ...respone,
          data: filteredData,
        };
      }
    } catch (error) {
      console.log(`check-error`, error);
    }
    return [];
  }
  async editOperationalData(data: any[]) {
    try {
      const respone = await this.ToiletOperationalController.edit(data);
      if (respone?.code === 200) {
        return respone;
      }
    } catch (error) {
      console.log(`check-error`, error);
    }
  }
  async getALlToiletCertificateByToiletId(toiletId: string) {
    const respone = await this.ToiletCertificateController.getPatternList({
      query: `@ToiletId: {${toiletId}}`,
      pattern: {
        CateCriterionId: ['Id', 'Name', 'Description', 'Sort', 'TypeCriterion'],
      },
    });
    if (respone?.code === 200) {
      let data = await getImage({items: respone.data});
      let dataMap = data.map((item: any) => ({
        ...item,
        CateCriterion: respone.CateCriterion.find(
          (cate: any) => cate.Id === item.CateCriterionId,
        ),
      }));
      return {
        ...respone,
        data: dataMap,
      };
    }
    return [];
  }
  async getLogByTOiletId(toiletId: string) {
    const respone = await this.ToiletLogCateController.getListSimple({
      page: 1,
      size: 1000,
      query: `@ToiletId: {${toiletId}}`,
    });
    if (respone?.code === 200) {
      return respone;
    }
    return [];
  }
  async getToiletInfoByToiletCertificate(
    toiletCertificateId: string,
    ToiletId: string,
  ) {
    const respone = await this.ToiletCertificateController.getListSimple({
      page: 1,
      size: 10000,
      query: `@Id: {${toiletCertificateId}} @ToiletId: {${ToiletId}}`,
    });
    console.log('check-respone.data', respone, toiletCertificateId, ToiletId);
    if (respone?.code === 200) {
      let arrayToiletId: string[] = [];
      respone?.data?.forEach((item: any) => {
        if (item.ToiletId) {
          arrayToiletId.push(item.ToiletId);
        }
      });
      let getDataCustomer = await this.ToiletController.getPatternList({
        query: `@Id: {${arrayToiletId.join(' | ')}}`,
        pattern: {
          CustomerId: [
            'Id',
            'Name',
            'Address',
            'Mobile',
            'Email',
            'CompanyProfileId',
          ],
        },
      });
      if (getDataCustomer?.code === 200) {
        let datamap;
        const company = getDataCustomer.Customer.map(
          (customer: any) => customer.CompanyProfileId,
        );
        const getconpanyInfp =
          await this.CompanyProfileController.getListSimple({
            page: 1,
            size: 10000,
            query: `@Id: {${company.join(' | ')}}`,
          });
        if (getconpanyInfp?.code === 200) {
          datamap = getDataCustomer.data.map((item: any) => {
            // Tìm customer trước
            const customer = getDataCustomer.Customer.find(
              (cus: any) => cus.Id === item.CustomerId,
            );

            // Tìm company dựa trên CompanyProfileId của customer
            const company = getconpanyInfp.data.find(
              (comp: any) => comp.Id === customer?.CompanyProfileId,
            );
            return {
              ...item,
              Customer: customer,
              Company: company,
            };
          });
          return {
            ...respone,
            data: datamap,
          };
        }
        return [];
      }
    }
  }
  async getUserKtx() {
    const respone = await this.CustomerController.getListSimple({
      page: 1,
      size: 10000,
      query: `@CompanyProfileId: {${ConfigAPI.ktxCompanyId}}`,
    });
    if (respone?.code === 200) {
      return respone;
    }
    return [];
  }
  async getAllstatisticsByToiletId(toiletId: string, CateCriterionId?: string) {
    let res;
    if (!CateCriterionId) {
      res = await this.ToiletOperationalController.getListSimple({
        page: 1,
        size: 10000,
        query: `@ToiletId: {${toiletId}}`,
      });
    } else {
      res = await this.ToiletOperationalController.getListSimple({
        page: 1,
        size: 10000,
        query: `@ToiletId: {${toiletId}} @CateCriterionId: {${CateCriterionId}}`,
      });
    }
    if (res?.code === 200) {
      return res;
    }
    return [];
  }
  async updateStatusToiletSerivicesCriterion(toiletId: string) {
    const res = await this.ToiletServiceCriterionController.getListSimple({
      page: 1,
      size: 10000,
      query: `@ToiletId: {${toiletId}}`,
    });
    console.log('cgheck-res', res);
    if (res?.code === 200) {
      if (res?.data.length > 0) {
        const update = await this.ToiletServiceCriterionController.edit([
          ...res.data.map((item: any) => ({
            ...item,
            Status: 2,
          })),
        ]);
        if (update?.code === 200) {
          console.log('check-update', update);
          return update;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
    return [];
  }
  async getToiletServiceByToiletId(toiletId: string) {
    const res = await this.ToiletServiceController.getListSimple({
      page: 1,
      size: 10000,
      query: `@ToiletId: {${toiletId}} @Status: [9 9]`,
    });
    if (res?.code === 200) {
      return res;
    }
    return [];
  }
  async getLogByToiletId(toiletId: string[], title: string) {
    const dataCreate = toiletId.map((item: any) => ({
      Id: randomGID(),
      DateCreated: Date.now(),
      ToiletId: item,
      Name: title,
      ProgressStatus: 1,
    }));
    const res = await this.ToiletLogController.add(dataCreate);
    if (res?.code === 200) {
      return res;
    }
    return [];
  }

  async getAllLogByToiletId(toiletId: string) {
    const res = await this.ToiletLogController.getListSimple({
      page: 1,
      size: 10000,
      query: `@ToiletId: {${toiletId}}`,
    });
    if (res?.code === 200) {
      return res;
    }
    return [];
  }

  async GetServiceName(CateCriterionId: string) {
    const res = await this.CatecriterionController.getListSimple({
      page: 1,
      size: 10000,
      query: `@Id: {${CateCriterionId}}`,
    });
    if (res?.code === 200) {
      return res;
    }
    return [];
  }
}

// Export instance để sử dụng trong các component
export default new CetificateAchievemenDa();
