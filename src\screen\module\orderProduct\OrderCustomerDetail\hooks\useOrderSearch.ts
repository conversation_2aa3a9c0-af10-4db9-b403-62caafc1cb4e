import {useState, useEffect, useCallback} from 'react';
import {DataController} from '../../../../base-controller';
import {useSelectorCustomerState} from '../../../../../redux/hooks/hooks';
import {Ult<PERSON>} from '../../../../../utils/Utils';
import {
  OrderItem,
  ApiResponse,
  PAGE_SIZE,
  UseOrderSearchReturn,
} from '../types';

export const useOrderSearch = (
  status: number,
  onSearchComplete: (data: OrderItem[]) => void,
  onLoadingChange: (loading: boolean) => void,
): UseOrderSearchReturn => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );

  const orderController = new DataController('Order');
  const ratingController = new DataController('Rating');
  const CustomerInfo = useSelectorCustomerState().data;

  const searchWithAPI = useCallback(
    async (term: string) => {
      console.log('searchWithAPI called with term:', term);
      setIsSearching(true);
      onLoadingChange(true);

      try {
        // Create search query for API
        let searchQuery = `@CustomerId: {${CustomerInfo?.Id}} @Status: [${status}]`;

        // Add search conditions
        const searchConditions = [];

        // Search by order code
        searchConditions.push(`@Code:(*${term}*)`);

        // Search by amount
        if (!isNaN(Number(term.replace(/[.,]/g, '')))) {
          const numericValue = term.replace(/[.,]/g, '');
          searchConditions.push(`@Value:[${numericValue}]`);
        }

        // Combine search conditions
        if (searchConditions.length > 0) {
          searchQuery += ` (${searchConditions.join(' | ')})`;
        }

        console.log('Search query:', searchQuery);

        const response: ApiResponse = await orderController.getPatternList({
          page: 1,
          size: PAGE_SIZE,
          query: searchQuery,
          pattern: {
            CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
            ShopId: ['Id', 'Name', 'Avatar'],
            AddressId: ['Id', 'Address'],
          },
        });

        if (response.code === 200) {
          // Process data similar to getData
          const processedData: OrderItem[] = response.data.map((item: any) => {
            item.Shop = response.Shop?.find(
              (shop: any) => shop.Id === item.ShopId,
            );
            return item;
          });

          // Handle rating if needed
          if (status === 3) {
            const resRated = await ratingController.getListSimple({
              page: 1,
              size: PAGE_SIZE,
              query: `@OrderId: {${processedData
                .map((item: any) => item.Id)
                .join(' | ')}}`,
            });

            if (resRated.code === 200) {
              resRated.data.forEach((ratingItem: any) => {
                const order = processedData.find(
                  (order: any) => order.Id === ratingItem.OrderId,
                );
                if (order) {
                  order.isRated = true;
                }
              });
            }
          }

          // Additional client-side filtering for shop name and date (since API doesn't support)
          const filteredData = processedData.filter((item: any) => {
            const searchTermLower = term.toLowerCase();

            // Check if matches API search (order code or amount)
            const codeMatch =
              item.Code?.toLowerCase().includes(searchTermLower);
            const valueMatch = item.Value?.toString().includes(
              term.replace(/[.,]/g, ''),
            );

            // Search by shop name
            const shopNameMatch =
              item.Shop?.Name?.toLowerCase().includes(searchTermLower);

            // Search by date
            let dateMatch = false;
            if (item.DateCreated) {
              const formattedDate = Ultis.formatDateTime(
                item.DateCreated,
                true,
              );
              dateMatch = formattedDate.toLowerCase().includes(searchTermLower);

              const dateOnly = new Date(item.DateCreated).toLocaleDateString(
                'vi-VN',
              );
              dateMatch = dateMatch || dateOnly.includes(term);
            }

            return codeMatch || valueMatch || shopNameMatch || dateMatch;
          });

          onSearchComplete(filteredData);
          console.log('Search results:', filteredData.length);
        } else {
          console.error('Search API error:', response);
          onSearchComplete([]);
        }
      } catch (error) {
        console.error('Error searching orders:', error);
        onSearchComplete([]);
      } finally {
        onLoadingChange(false);
        setIsSearching(false);
      }
    },
    [
      orderController,
      ratingController,
      CustomerInfo?.Id,
      status,
      onSearchComplete,
      onLoadingChange,
    ],
  );

  // Effect to handle search with API
  useEffect(() => {
    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Debounce search
    const timeoutId = setTimeout(() => {
      if (searchTerm.trim()) {
        searchWithAPI(searchTerm.trim());
      } else {
        // If no search term, this will be handled by the main component
        onSearchComplete([]);
      }
    }, 800); // Increased delay to 800ms for API call

    setSearchTimeout(timeoutId);

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [searchTerm]);

  return {
    searchTerm,
    isSearching,
    setSearchTerm,
    searchWithAPI,
  };
};
