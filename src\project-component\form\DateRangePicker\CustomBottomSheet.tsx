import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
  StyleSheet,
  TouchableWithoutFeedback,
  PanResponder,
} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorSkin} from '../../../assets/skin/colors';

interface CustomBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  onCancel?: () => void;
  onConfirm?: () => void;
  cancelText?: string;
  confirmText?: string;
  height?: number | string; // Thêm prop height để có thể truyền từ bên ngoài
  isShowConfirm?: boolean;
}

const {height: screenHeight} = Dimensions.get('window');

export const CustomBottomSheet: React.FC<CustomBottomSheetProps> = ({
  visible,
  onClose,
  title,
  subTitle,
  children,
  onCancel,
  onConfirm,
  cancelText = 'Hủy',
  confirmText = 'Xác nhận',
  height, // Nhận prop height từ bên ngoài
  isShowConfirm = true,
}) => {
  const [slideAnim] = useState(new Animated.Value(screenHeight));

  // Tính toán chiều cao động dựa trên prop height
  const getBottomSheetHeight = () => {
    if (height) {
      // Nếu height là số, sử dụng trực tiếp
      if (typeof height === 'number') {
        return height;
      }
      // Nếu height là string với %, tính theo tỷ lệ màn hình
      if (typeof height === 'string' && height.includes('%')) {
        const percentage = parseFloat(height.replace('%', '')) / 100;
        return screenHeight * percentage;
      }
      // Nếu height là string số, chuyển đổi thành number
      if (typeof height === 'string') {
        const numericHeight = parseFloat(height);
        return isNaN(numericHeight) ? screenHeight * 0.8 : numericHeight;
      }
    }
    // Giá trị mặc định nếu không có height prop
    return screenHeight * 0.8;
  };

  const dynamicHeight = getBottomSheetHeight();

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: screenHeight,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const handleClose = useCallback(() => {
    Animated.timing(slideAnim, {
      toValue: screenHeight,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  }, [slideAnim, onClose]);

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => false,
    onMoveShouldSetPanResponder: (_, gestureState) => {
      return gestureState.dy > 5 && gestureState.y0 < 60;
    },
    onPanResponderMove: (_, gestureState) => {
      if (gestureState.dy > 0) {
        slideAnim.setValue(gestureState.dy);
      }
    },
    onPanResponderRelease: (_, gestureState) => {
      if (gestureState.dy > 80 || gestureState.vy > 0.3) {
        handleClose();
      } else {
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      statusBarTranslucent
      animationType="none"
      onRequestClose={handleClose}>
      <View style={styles.overlay}>
        <TouchableWithoutFeedback onPress={handleClose}>
          <View style={styles.backdrop} />
        </TouchableWithoutFeedback>

        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [{translateY: slideAnim}],
              height: dynamicHeight, // Sử dụng chiều cao động
            },
          ]}>
          <View style={styles.handleBar} {...panResponder.panHandlers}>
            <View
              style={{
                width: 36,
                height: 3,
                backgroundColor: '#D1D5DB',
                borderRadius: 2,
              }}
            />
          </View>

          <View style={styles.header}>
            {/* Left button */}
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                onCancel?.();
                handleClose();
              }}>
              <Text
                style={[
                  TypoSkin.buttonText3,
                  {color: ColorSkin.textColorGrey2},
                ]}>
                {cancelText}
              </Text>
            </TouchableOpacity>

            {/* Centered title container */}
            <View style={styles.titleContainer}>
              {title && (
                <Text style={[TypoSkin.title3, styles.title]}>{title}</Text>
              )}
              {subTitle && (
                <Text style={[TypoSkin.subtitle3, styles.subTitle]}>
                  {subTitle}
                </Text>
              )}
            </View>

            {/* Right button - always render to maintain layout balance */}
            <TouchableOpacity
              style={[
                styles.actionButton,
                !isShowConfirm && {opacity: 0, pointerEvents: 'none'},
              ]}
              onPress={() => {
                onConfirm?.();
              }}>
              <Text style={[TypoSkin.buttonText3, {color: ColorSkin.primary}]}>
                {confirmText}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.content}>{children}</View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    // Loại bỏ maxHeight và minHeight cố định để sử dụng height động
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  handleBar: {
    width: '100%',
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E7EB',
    position: 'relative',
  },
  titleContainer: {
    position: 'absolute',
    left: 66, // Để tránh overlap với button trái (minWidth: 50 + padding)
    right: 66, // Để tránh overlap với button phải
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  title: {
    textAlign: 'center',
  },
  subTitle: {
    textAlign: 'center',
    color: ColorSkin.textColorGrey2,
  },
  actionButton: {
    paddingVertical: 6,
    paddingHorizontal: 8,
    minWidth: 50,
    alignItems: 'center',
    zIndex: 2, // Đảm bảo button có thể click được
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
});
