import { Pressable, Text, TouchableOpacity, useWindowDimensions, View } from 'react-native';
import { Ultis } from '../../../../../utils/Utils';
import { TypoSkin } from '../../../../../assets/skin/typography';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { ToiletStatus } from '../../../service/components/da';
import { SkeletonImage } from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import { Winicon } from '../../../../../component/export-component';
import WebView from 'react-native-webview';
import { useNavigation } from '@react-navigation/native';
import { RootScreen } from '../../../../../router/router';
import RenderHTML from 'react-native-render-html';

export default function TrustProductCard({ item, index, listBrand }: any) {
    const navigation = useNavigation<any>()
    const { width } = useWindowDimensions();

    return (
        <TouchableOpacity onPress={() => { navigation.navigate(RootScreen.DetailProduct, { id: item.Id }) }} style={{ gap: 16, backgroundColor: ColorThemes.light.neutral_absolute_background_color, padding: 16, borderRadius: 8, marginBottom: 16 }}>
            <View pointerEvents='none'><SkeletonImage source={{ uri: ConfigAPI.imgUrlId + item?.Img }} style={{ width: "100%", height: 160, borderRadius: 8 }} /></View>
            <View style={{ gap: 8 }}>
                <Text style={{ ...TypoSkin.title3 }} numberOfLines={3}>{`${index + 1}. ${item?.Name ?? '-'}`}</Text>
                <View style={{ flexDirection: "row", gap: 8, alignItems: "center" }}>
                    <Winicon src='outline/shopping/label' size={14} />
                    <Text style={{ ...TypoSkin.body2, color: ColorThemes.light.neutral_text_subtitle_color }}>{item.BrandsId ? listBrand.filter((e: any) => item.BrandsId.includes(e.Id)).map((e: any) => e.Name).join(" ,") : ""}</Text>
                </View>
            </View>
            {item.Description &&
                <RenderHTML
                    contentWidth={width}
                    source={{ html: item.Description ?? "" }}
                    baseStyle={{
                        lineHeight: 24,
                        fontSize: 16,
                        fontWeight: '400', color: ColorThemes.light.neutral_text_subtitle_color
                    }}
                />}
            <Text style={{ ...TypoSkin.title2 }} numberOfLines={3}>{Ultis.money(item.Price)}VNĐ</Text>
        </TouchableOpacity>
    );
}
