import React, {useState, useEffect} from 'react';
import {useForm} from 'react-hook-form';
import {
  Pressable,
  KeyboardAvoidingView,
  View,
  TouchableWithoutFeedback,
  Keyboard,
  StyleSheet,
} from 'react-native';
import {AppButton, ListTile, Winicon} from 'wini-mobile-components';
import {useDispatch} from 'react-redux';
import {useRoute} from '@react-navigation/native';
import {randomGID} from '../../../../utils/Utils';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {get} from '../../../../utils/lodash';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {navigateBack} from '../../../../router/router';
import {ColorThemes} from '../../../../assets/skin/colors';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {
  FAddressPickerForm,
  TextFieldForm,
} from '../../../../project-component/component-form';
import {validatePhoneNumber} from '../../../../utils/validate';
import {TypoSkin} from '../../../../assets/skin/typography';
import IOSSwitch from '../../../../component/IOSSwitch';
import WScreenFooter from '../../../layout/footer';
import {useTranslation} from 'react-i18next';

export default function EditAddress() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const item = route?.params?.item;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID(), DateCreated: new Date().getTime()},
  });

  const [isDefault, setIsDefault] = useState(false);

  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const addresses = useSelectorCustomerState().myAddress;

  useEffect(() => {
    if (item) {
      Object.keys(item).forEach(key => {
        methods.setValue(key, item[key]);
      });
      setIsDefault(item.IsDefault);
    }
  }, [item]);

  useEffect(() => {
    if (customer?.Id && addresses && addresses.length == 0) {
      setIsDefault(true);
      // add methods value by customer
      methods.setValue('Name', get(customer, 'Name', ''));
      methods.setValue('Mobile', get(customer, 'Mobile', ''));
      methods.setValue('Email', get(customer, 'Email', ''));
      methods.setValue('Address', get(customer, 'Address', ''));
      methods.setValue('Long', get(customer, 'Long', ''));
      methods.setValue('Lat', get(customer, 'Lat', ''));
      methods.setValue('CustomerId', customer.Id);
    }
  }, [addresses]);

  const onSubmit = () => {
    const data = methods.getValues();
    const obj = {
      ...data,
      CustomerId: customer?.Id,
      IsDefault: isDefault,
    };
    if (item) {
      data.Id = item.Id;
      data.DateCreated = item.DateCreated;
    }
    dispatch(CustomerActions.editAddress(obj, item ? false : true));
    navigateBack();
  };

  return (
    <Pressable style={styles.container}>
      <TitleHeader title="Chi tiết địa chỉ" />
      <TouchableWithoutFeedback
        style={styles.touchableContainer}
        onPress={() => Keyboard.dismiss()}>
        <KeyboardAvoidingView>
          <View style={styles.formContainer}>
            <TextFieldForm
              control={methods.control}
              name="Name"
              required
              placeholder={t('Họ và tên')}
              label={t('Họ và tên')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={styles.textField}
              register={methods.register}
              type="name-phone-pad"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Name');
                else
                  methods.setError('Name', {
                    message: 'Họ và tên không được để trống',
                  });
              }}
              prefix={
                <View style={styles.prefixIcon}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Email"
              placeholder={t('Email')}
              label={t('Email')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={styles.textField}
              register={methods.register}
              type="email-address"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Email');
                else
                  methods.setError('Email', {
                    message: 'Email không được để trống',
                  });
              }}
              prefix={
                <View style={styles.prefixIcon}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              required
              placeholder={t('Số điện thoại')}
              label={t('Số điện thoại')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={styles.mobileTextField}
              register={methods.register}
              prefix={
                <View style={styles.prefixIcon}>
                  <Winicon
                    src="fill/user interface/phone"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              type="number-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
                  return;
                }
                var mobile = ev.trim();
                // Check if the number doesn't already start with 0 or +84
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile; // Add 0 at the beginning
                }
                const val = validatePhoneNumber(mobile);
                if (val) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
              }}
            />
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              label={t('Địa chỉ')}
              placeholder={t('Địa chỉ')}
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
            />
            <ListTile
              title="Đặt làm địa chỉ mặc định"
              style={styles.listTile}
              titleStyle={[TypoSkin.heading8, {}]}
              trailing={
                <IOSSwitch
                  disabled={addresses && addresses.length == 0}
                  value={isDefault}
                  onColor={ColorThemes.light.primary_main_color}
                  onValueChange={setIsDefault}
                />
              }
            />
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
      <WScreenFooter>
        <AppButton
          containerStyle={styles.saveButton}
          borderColor="transparent"
          title={'Lưu'}
          onPress={() => {
            methods.handleSubmit(onSubmit)();
          }}
        />
      </WScreenFooter>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  touchableContainer: {
    flex: 1,
  },
  formContainer: {
    paddingHorizontal: 16,
    gap: 16,
    paddingVertical: 8,
    paddingBottom: 100,
  },
  textField: {
    height: 48,
    paddingVertical: 16,
    paddingLeft: 8,
    backgroundColor: ColorThemes.light.transparent,
  },
  mobileTextField: {
    height: 48,
    paddingLeft: 8,
    backgroundColor: ColorThemes.light.transparent,
  },
  prefixIcon: {
    flexDirection: 'row',
    height: 32,
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 100,
  },
  listTile: {
    padding: 0,
    marginTop: 16,
  },
  saveButton: {
    borderRadius: 8,
    marginHorizontal: 16,
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
  },
});
