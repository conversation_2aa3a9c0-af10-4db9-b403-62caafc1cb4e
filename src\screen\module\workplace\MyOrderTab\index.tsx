import React, {useRef} from 'react';
import {View, StyleSheet} from 'react-native';
import {StatusToiletServiceData} from '../components/card/NewWorkCard';
import {FPopup, showPopup} from 'component/popup/popup';
import {ColorThemes} from 'assets/skin/colors';
import {useMyOrderTab} from './hooks/useMyOrderTab';
import {
  SearchBar,
  FilterButton,
  WorkList,
  PopupFilter,
} from '../NewOrderWorkList/components';

export default function MyOrderTab() {
  const {
    works,
    customer,
    isLoading,
    isRefreshing,
    isLoadingMore,
    hasMore,
    searchValue,
    guests,
    filterMethods,
    setSearchValue,
    setWorks,
    getData,
    onRefresh,
    loadMore,
  } = useMyOrderTab();

  const popupRef = useRef<any>();

  const hasActiveFilters =
    filterMethods.watch('AttributeId')?.length ||
    filterMethods.watch('CateServicesId')?.length;

  const handleFilterApply = async (value: any) => {
    filterMethods.setValue('AttributeId', value.AttributeId);
    filterMethods.setValue('CateServicesId', value.CateServicesId);
    await getData(1, false);
  };

  const handleFilterPress = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupFilter
          ref={popupRef}
          filterMethods={filterMethods}
          listStatus={StatusToiletServiceData}
          selectedAttributes={filterMethods.watch('AttributeId') ?? []}
          CateServicesId={filterMethods.watch('CateServicesId') ?? []}
          onApply={handleFilterApply}
        />
      ),
    });
  };

  return (
    <View style={styles.container}>
      <FPopup ref={popupRef} />
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchValue}
          onChange={setSearchValue}
          placeholder="Tìm kiếm"
        />
        <FilterButton
          onPress={handleFilterPress}
          hasActiveFilters={hasActiveFilters}
          title="Bộ lọc"
        />
      </View>
      <WorkList
        works={works}
        isLoading={isLoading}
        isRefreshing={isRefreshing}
        isLoadingMore={isLoadingMore}
        hasMore={hasMore}
        searchValue={searchValue}
        guests={guests}
        user={customer}
        setWorks={setWorks}
        onRefresh={onRefresh}
        loadMore={loadMore}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  searchContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 16,
    gap: 8,
    paddingTop: 8,
    paddingBottom: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});
