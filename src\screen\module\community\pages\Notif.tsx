import {useNavigation, DrawerActions} from '@react-navigation/native';
import {Image, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ProfileView} from './Chat';
import {ColorThemes} from '../../../../assets/skin/colors';
import React from 'react';
import {Winicon} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import NotifCommunity from '../../notification/view/inCommunity';

export default function NotifyCommunity() {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <Winicon src="fill/user interface/apps" size={20} />
          </TouchableOpacity>
        }
        title="Notification"
        trailing={<ProfileView />}
      />
      {/* content */}
      <NotifCommunity />
    </View>
  );
}
