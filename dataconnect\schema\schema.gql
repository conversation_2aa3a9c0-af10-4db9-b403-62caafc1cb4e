# # Example schema for simple movie review app

# # Users
# # Suppose a user can leave reviews for movies
# # user  -> reviews is a one to many relationship,
# # movie -> reviews is a one to many relationship
# # movie <-> user is a many to many relationship
# type User @table {
#   id: String! @col(name: "user_auth")
#   username: String! @col(name: "username", dataType: "varchar(50)")
#   # The following are generated by the user: User! field in the Review table
#   # reviews_on_user 
#   # movies_via_Review
# }

# # Movies
# type Movie @table {
#   # The below parameter values are generated by default with @table, and can be edited manually.
#   # implies directive `@col(name: "movie_id")`, generating a column name
#   id: UUID! @default(expr: "uuidV4()")
#   title: String!
#   imageUrl: String!
#   genre: String
# }

# # Movie Metadata
# # Movie - MovieMetadata is a one-to-one relationship
# type MovieMetadata @table {
#   # @unique indicates a 1-1 relationship
#   movie: Movie! @unique 
#   # movieId: UUID <- this is created by the above reference
#   rating: Float
#   releaseYear: Int
#   description: String
# }

# # Reviews
# type Review @table(name: "Reviews", key: ["movie", "user"]) {
#   id: UUID! @default(expr: "uuidV4()")
#   user: User!
#   movie: Movie!
#   rating: Int
#   reviewText: String
#   reviewDate: Date! @default(expr: "request.time")
# }
