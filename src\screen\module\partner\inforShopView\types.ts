export interface ShopData {
  Id: string;
  Name: string;
  Img?: string;
  Mobile: string;
  Email?: string;
  OwnerName?: string;
  rating?: number;
  countRate?: number;
  totalProducts?: number;
  totalOrder?: number;
}

export interface ShopProfileProps {
  shop: ShopData | null;
  onCall: (phoneNumber: string) => void;
  onMessage: (phoneNumber: string) => void;
}

export interface ActionButtonsProps {
  onCall: () => void;
  onMessage: () => void;
}

export interface ShopDetailsProps {
  shop: ShopData | null;
  onCall: (phoneNumber: string) => void;
}

export interface ShopStatisticsProps {
  shop: ShopData | null;
  onProductPress: () => void;
}

export interface InforShopViewRouteParams {
  shop: ShopData;
}

export type ShopInfoType = 'rating' | 'product' | 'order';
