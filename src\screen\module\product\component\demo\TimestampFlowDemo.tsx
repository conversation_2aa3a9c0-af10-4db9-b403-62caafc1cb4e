import React, {useState} from 'react';
import {View, Text, StyleSheet, ScrollView, TouchableOpacity} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';

const TimestampFlowDemo = () => {
  const [demoData, setDemoData] = useState<any>(null);

  // Hàm CovertDate được cải thiện (copy từ CreatePartnerProductForm)
  const CovertDate = (date: any): number => {
    // Nếu không có dữ liệu, trả về 0
    if (!date) return 0;

    // Nếu đã là số (timestamp), trả về luôn
    if (typeof date === 'number') return date;

    // Nếu là chuỗi, xử lý chuyển đổi
    if (typeof date === 'string') {
      // Kiểm tra nếu là chuỗi số (timestamp dạng string)
      if (/^\d+$/.test(date)) {
        return parseInt(date);
      }

      // Xử lý định dạng dd/mm/yyyy
      if (date.includes('/')) {
        const [day, month, year] = date.split('/');
        if (day && month && year) {
          const getDate = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
          );
          return getDate.getTime();
        }
      }
    }

    // Nếu là Date object
    if (date instanceof Date) {
      return date.getTime();
    }

    // Trường hợp khác, thử parse trực tiếp
    try {
      const parsedDate = new Date(date);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.getTime();
      }
    } catch (error) {
      console.warn('Cannot convert date:', date, error);
    }

    // Trả về 0 nếu không thể chuyển đổi
    return 0;
  };

  // Hàm chuyển đổi từ timestamp về dd/mm/yyyy (để hiển thị)
  const CoverMiliSecondToDate = (miliSecond: number) => {
    if (!miliSecond || miliSecond === 0) return '';
    const date = new Date(miliSecond);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const simulateFormSubmit = () => {
    // Giả lập dữ liệu từ form (có thể là các định dạng khác nhau)
    const formData = {
      Name: 'Sản phẩm demo',
      Price: 100000,
      Guarantee: '25/12/2024', // Chuỗi dd/mm/yyyy
      Preserve: 1735689600000, // Timestamp số
      // Các trường khác...
    };

    console.log('=== DEMO FLOW CHUYỂN ĐỔI THỜI GIAN ===');
    console.log('1. Dữ liệu từ form:', formData);

    // Bước 2: Chuyển đổi trong onSubmit
    const dataCreate = {
      ...formData,
      Guarantee: CovertDate(formData.Guarantee),
      Preserve: CovertDate(formData.Preserve),
    };

    console.log('2. Sau khi chuyển đổi:', dataCreate);

    // Bước 3: Kiểm tra kết quả
    const result = {
      original: formData,
      converted: dataCreate,
      guaranteeCheck: {
        input: formData.Guarantee,
        output: dataCreate.Guarantee,
        isNumber: typeof dataCreate.Guarantee === 'number',
        formatted: CoverMiliSecondToDate(dataCreate.Guarantee),
      },
      preserveCheck: {
        input: formData.Preserve,
        output: dataCreate.Preserve,
        isNumber: typeof dataCreate.Preserve === 'number',
        formatted: CoverMiliSecondToDate(dataCreate.Preserve),
      },
    };

    console.log('3. Kết quả kiểm tra:', result);
    setDemoData(result);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Demo Flow Chuyển Đổi Thời Gian</Text>
      <Text style={styles.subtitle}>
        Mô phỏng quá trình chuyển đổi thời gian trong CreatePartnerProductForm
      </Text>

      <TouchableOpacity style={styles.button} onPress={simulateFormSubmit}>
        <Text style={styles.buttonText}>Mô phỏng Submit Form</Text>
      </TouchableOpacity>

      {demoData && (
        <View style={styles.resultsContainer}>
          <Text style={styles.sectionTitle}>📝 Dữ liệu gốc từ form:</Text>
          <View style={styles.dataBox}>
            <Text style={styles.dataText}>
              Guarantee: {JSON.stringify(demoData.original.Guarantee)}
            </Text>
            <Text style={styles.dataText}>
              Preserve: {JSON.stringify(demoData.original.Preserve)}
            </Text>
          </View>

          <Text style={styles.sectionTitle}>🔄 Sau khi chuyển đổi:</Text>
          <View style={styles.dataBox}>
            <Text style={styles.dataText}>
              Guarantee: {demoData.converted.Guarantee} (timestamp)
            </Text>
            <Text style={styles.dataText}>
              Preserve: {demoData.converted.Preserve} (timestamp)
            </Text>
          </View>

          <Text style={styles.sectionTitle}>✅ Kiểm tra kết quả:</Text>
          
          <View style={styles.checkBox}>
            <Text style={styles.checkTitle}>Guarantee Field:</Text>
            <Text style={styles.checkItem}>
              Input: {JSON.stringify(demoData.guaranteeCheck.input)}
            </Text>
            <Text style={styles.checkItem}>
              Output: {demoData.guaranteeCheck.output}
            </Text>
            <Text style={[
              styles.checkItem,
              demoData.guaranteeCheck.isNumber ? styles.success : styles.error
            ]}>
              Là số: {demoData.guaranteeCheck.isNumber ? '✓ Có' : '✗ Không'}
            </Text>
            <Text style={styles.checkItem}>
              Hiển thị: {demoData.guaranteeCheck.formatted || 'N/A'}
            </Text>
          </View>

          <View style={styles.checkBox}>
            <Text style={styles.checkTitle}>Preserve Field:</Text>
            <Text style={styles.checkItem}>
              Input: {JSON.stringify(demoData.preserveCheck.input)}
            </Text>
            <Text style={styles.checkItem}>
              Output: {demoData.preserveCheck.output}
            </Text>
            <Text style={[
              styles.checkItem,
              demoData.preserveCheck.isNumber ? styles.success : styles.error
            ]}>
              Là số: {demoData.preserveCheck.isNumber ? '✓ Có' : '✗ Không'}
            </Text>
            <Text style={styles.checkItem}>
              Hiển thị: {demoData.preserveCheck.formatted || 'N/A'}
            </Text>
          </View>

          <View style={styles.summaryBox}>
            <Text style={styles.summaryTitle}>📊 Tóm tắt:</Text>
            <Text style={styles.summaryText}>
              • Hàm CovertDate luôn trả về timestamp (số)
            </Text>
            <Text style={styles.summaryText}>
              • Xử lý được nhiều định dạng đầu vào khác nhau
            </Text>
            <Text style={styles.summaryText}>
              • DateSinglePicker đã được set useTimestamp={'{true}'}
            </Text>
            <Text style={styles.summaryText}>
              • Dữ liệu lưu vào database sẽ luôn là timestamp
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_darker_color,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  dataBox: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  dataText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
    color: '#333',
  },
  checkBox: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  checkTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  checkItem: {
    fontSize: 12,
    marginBottom: 4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  summaryBox: {
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#2d5a2d',
  },
  summaryText: {
    fontSize: 12,
    marginBottom: 4,
    color: '#2d5a2d',
  },
  success: {
    color: 'green',
    fontWeight: '600',
  },
  error: {
    color: 'red',
    fontWeight: '600',
  },
});

export default TimestampFlowDemo;
