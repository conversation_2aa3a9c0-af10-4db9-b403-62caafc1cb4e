// Types and interfaces for Step2 component

import {CriterionItem} from '../../../../../../types/criterionType';
import {ToiletItem} from '../../../../../../types/toiletType';

export interface Step2Data {
  selectedTab: string;
  checkedItems: Record<string, boolean>;
}

export interface HeaderProps {
  title: string;
  subtitle: string;
  onAddNew: () => void;
  onSelectExisting: () => void;
}

export interface TabNavigationProps {
  tabs: string[];
  selectedTab: string;
  onTabSelect: (tab: string) => void;
}

export interface SurveySectionProps {
  title: string;
  items: CriterionItem[];
  checkedItems: string[];
  onToggleCheck: (itemId: string) => void;
  disabled?: boolean;
}

export interface SurveyItemComponentProps {
  item: CriterionItem;
  isChecked: boolean;
  onToggle: (itemId: string) => void;
  disabled?: boolean;
}

export interface CheckboxProps {
  isChecked: boolean;
  onToggle: () => void;
  disabled?: boolean;
}

export interface UseStep2Return {
  handleAddNew: (data: ToiletItem) => void;
  setSelectedToilets: (toilets: ToiletItem[]) => void;
  cateCriterionData: any[];
  toiletCustomer: ToiletItem[];
  toiletSelected: ToiletItem[];
  toiletCriterions: any[];
  loading: boolean;
  selectedToilet: ToiletItem | null;
  handleToiletPress: (toilet: ToiletItem) => void;
  findToiletCriterionByToiletId: (toiletId: string) => any;
  refreshToiletCriterions: () => Promise<void>;
  markToiletAsUpdated: (toiletIds: string[]) => void;
}
