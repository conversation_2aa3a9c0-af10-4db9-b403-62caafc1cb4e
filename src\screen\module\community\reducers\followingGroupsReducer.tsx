import { createSlice, Dispatch, PayloadAction } from '@reduxjs/toolkit';
import { GroupDA } from '../groups/da';
import { DataController } from '../../../base-controller';
import { getDataToAsyncStorage } from '../../../../utils/AsyncStorage';
import { groupRole, StorageContanst } from '../../../../config/Contanst';
import { store } from '../../../../redux/store/store';
import { randomGID } from '../../../../utils/Utils';

interface GroupWithMemberCount {
  Id: string;
  Name: string;
  ImageUrl: string;
  Description: string;
  MemberCount: number;
}
interface FollowingGroupState {
  groups: GroupWithMemberCount[];
  isLoading: boolean;
  error: string | null;
  page: number;
  hasMore: boolean;
}

const initialState: FollowingGroupState = {
  groups: [],
  isLoading: false,
  error: null,
  page: 1,
  hasMore: true,
};

export const followingGroupsSlice = createSlice({
  name: 'followingGroups',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setGroups: (state, action: PayloadAction<{groups: GroupWithMemberCount[]; page: number}>) => {
      state.groups = action.payload.groups;
      state.page = action.payload.page;
      state.hasMore = action.payload.groups.length > 0;
    },
    appendGroups: (state, action: PayloadAction<{groups: GroupWithMemberCount[]; page: number}>) => {
      state.groups = [...state.groups, ...action.payload.groups];
      state.page = action.payload.page;
      state.hasMore = action.payload.groups.length > 0;
    },
    updateMemberCount: (state, action: PayloadAction<{groupId: string; change: number}>) => {
      state.groups = state.groups.map(group =>
        group.Id === action.payload.groupId
          ? {...group, MemberCount: group.MemberCount + action.payload.change}
          : group
      );
    },
    removeGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter(group => group.Id !== action.payload);
    },
    resetState: () => initialState,
    addFollowingGroup: (state, action: PayloadAction<GroupWithMemberCount>) => {
      state.groups = [action.payload, ...state.groups];
    },
    removeFollowingGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter(group => group.Id !== action.payload);
    },
  },
});
export default followingGroupsSlice.reducer;

const {
  setLoading,
  setGroups,
  appendGroups,
  updateMemberCount,
  removeGroup,
  resetState,
  addFollowingGroup,
  removeFollowingGroup,
} = followingGroupsSlice.actions;
async function getGroupMemberCount(groupId: string): Promise<number> {
  const groupMemberController = new DataController('Role');
  const result = await groupMemberController.getListSimple({
    query: `@GroupId:{${groupId}}`,
    size: 0,
  });
  return result.code === 200 ? result.totalCount ?? 0 : 0;
}

const groupDA = new GroupDA();
export class followingGroupsActions {
  static getFollowingGroups =
    (page: number = 1, size: number = 10) =>
    async (dispatch: Dispatch) => {
      dispatch(setLoading(true));
     const cusId = store.getState().customer.data?.Id;

      if (cusId) {
        const groupMemberController = new DataController('Role');
        const result = await groupMemberController.getListSimple({
          query: `@CustomerId:{${cusId}} @Type:[${groupRole.subadmin},${groupRole.member}]`,
          page: page,
          size: size,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (result.code === 200) {
          const groupIds = result.data.map((member: any) => member.GroupId);

          if (groupIds.length > 0) {
            const groupController = new DataController('Group');
            const groupsResult = await groupController.getListSimple({
              query: `@Id:{${groupIds.join(' | ')}}`,
              returns: ['Id', 'Name', 'Thumb', 'Description'],
            });

            if (groupsResult.code === 200) {
              // Get member count for each group
              const groupsWithMemberCount = await Promise.all(
                groupsResult.data.map(async (group: any) => {
                  const memberResult = await groupDA.getGroupMembers(group.Id);
                  return {
                    ...group,
                    MemberCount: memberResult ? memberResult.length : 0,
                  };
                })
              );

              dispatch(
                page === 1
                  ? setGroups({groups: groupsWithMemberCount, page})
                  : appendGroups({
                      groups: groupsWithMemberCount,
                      page,
                    }),
              );
            }
          } else {
            dispatch(setGroups({groups: [], page}));
          }
        }
      }
      dispatch(setLoading(false));
    };

  static leaveGroup = (groupId: string) => async (dispatch: Dispatch) => {
   const cusId = store.getState().customer.data?.Id;

    if (cusId) {
      const groupMemberController = new DataController('Role');
      const memberResult = await groupMemberController.getListSimple({
        query: `@CustomerId:{${cusId}} @GroupId:{${groupId}}`,
      });

      if (memberResult.code === 200 && memberResult.data.length > 0) {
        const deleteResult = await groupMemberController.delete([
          memberResult.data[0].Id,
        ]);

        if (deleteResult.code === 200) {
          dispatch(removeGroup(groupId));
          // Update member count for remaining groups that have this group
          dispatch(updateMemberCount({groupId, change: -1}));
        }
      }
    }
  };
  static resetGroups = () => (dispatch: Dispatch) => {
    dispatch(resetState());
  };

  static followGroup = (group: GroupWithMemberCount) => async (dispatch: Dispatch) => {
    const groupMemberController = new DataController('Role');
   const cusId = store.getState().customer.data?.Id;
    
    if (cusId) {
      const result = await groupMemberController.add([{
        Id: randomGID(),
        GroupId: group.Id,
        CustomerId: cusId,
        Type: groupRole.member,
        DateCreated: new Date().getTime(),
        Status: 1,
      }]);

      if (result.code === 200) {
        dispatch(addFollowingGroup(group));
        dispatch(updateMemberCount({ groupId: group.Id, change: 1 }));
      }
    }
  };

  static unfollowGroup = (groupId: string) => async (dispatch: Dispatch) => {
   const cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const groupMemberController = new DataController('Role');
      const memberResult = await groupMemberController.getListSimple({
        query: `@CustomerId:{${cusId}} @GroupId:{${groupId}}`,
      });

      if (memberResult.code === 200 && memberResult.data.length > 0) {
        const deleteResult = await groupMemberController.delete([
          memberResult.data[0].Id,
        ]);

        if (deleteResult.code === 200) {
          dispatch(removeFollowingGroup(groupId));
          dispatch(updateMemberCount({ groupId, change: -1 }));
        }
      }
    }
  };
}


