import {useState} from 'react';
import {useForm} from 'react-hook-form';

export const useFAQSearch = () => {
  const [searchValue, setSearchValue] = useState('');
  const filterMethods = useForm({shouldFocusError: false});

  const buildSearchQuery = () => {
    let searchQuery: string[] = [];

    if (filterMethods.getValues('AttributeId')?.length) {
      searchQuery.push(
        `@CateFAQId:{${filterMethods.getValues('AttributeId').join(' | ')}}`,
      );
    }

    if (searchValue !== '') {
      searchQuery.push(`@Name:(*${searchValue}*)`);
    }

    return searchQuery;
  };

  const resetFilters = () => {
    filterMethods.setValue('AttributeId', []);
    setSearchValue('');
  };

  const hasActiveFilters = () => {
    return filterMethods.watch('AttributeId')?.length > 0 || searchValue !== '';
  };

  return {
    searchValue,
    setSearchValue,
    filterMethods,
    buildSearchQuery,
    resetFilters,
    hasActiveFilters,
  };
};
