import {FlatList, View} from 'react-native';
import {DataController} from '../../../../../base-controller';
import {useEffect, useState} from 'react';
import ToiletSelectionCard from '../../../components/card/ToiletSelectionCard';
import ListBioTabBottomSheet from '../ListBioTabBottomSheet';
import {ToiletServiceStatus} from 'screen/module/service/components/da';

const ToiletBioList = ({
  toiletIds,
  toiletServiceStatus,
}: {
  toiletIds: string[];
  toiletServiceStatus: number;
}) => {
  const [listToilet, setListToilet] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  const [dataToilet, setDataToilet] = useState<any>({});

  useEffect(() => {
    const toiletController = new DataController('Toilet');
    toiletController.getByListId(toiletIds).then(res => {
      setListToilet(res.data);
    });
  }, [toiletIds]);

  const handleToiletCardPress = (item: any) => {
    setDataToilet({Id: item.Id, CustomerId: item.CustomerId});
    setVisible(true);
  };

  const getDisabled = () => {
    if (listToilet.length <= 0) return true;
    return toiletServiceStatus > ToiletServiceStatus.research;
  };

  return (
    <View>
      {listToilet.length > 0 && (
        <FlatList
          data={listToilet}
          scrollEnabled={false}
          renderItem={({item}) => (
            <ToiletSelectionCard
              item={item}
              showSelect={false}
              onPress={() => handleToiletCardPress(item)}
            />
          )}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          style={{marginBottom: 16}}
        />
      )}
      <ListBioTabBottomSheet
        visible={visible}
        disabled={getDisabled()}
        onClose={() => {
          setVisible(false);
          setDataToilet({});
        }}
        data={dataToilet}
        refreshing={null}
        onRefresh={null}
      />
    </View>
  );
};

export default ToiletBioList;
