import React from 'react';
import {View, Text} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ShopProfileProps} from '../types';
import {styles} from '../styles';
import ActionButtons from './ActionButtons';

const ShopProfile: React.FC<ShopProfileProps> = ({shop, onCall, onMessage}) => {
  const handleCall = () => {
    if (shop?.Mobile) {
      onCall(shop.Mobile);
    }
  };

  const handleMessage = () => {
    if (shop?.Mobile) {
      onMessage(shop.Mobile);
    }
  };

  return (
    <View style={styles.profileContainer}>
      {/* Avatar */}
      <View style={styles.avatarContainer}>
        {shop?.Img ? (
          <FastImage
            source={{uri: shop.Img}}
            style={styles.avatarImage}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Winicon
              src="fill/shopping/store"
              size={40}
              color={ColorThemes.light.primary_main_color}
            />
          </View>
        )}
      </View>

      {/* Shop Name */}
      <Text
        style={[
          TypoSkin.heading6,
          {color: ColorThemes.light.neutral_text_title_color},
          styles.shopName,
        ]}>
        {shop?.Name || 'Tên cửa hàng'}
      </Text>

      {/* Rating and Status */}
      <View style={styles.statusContainer}>
        <View style={styles.statusDot} />
        <Text style={[TypoSkin.regular3, styles.statusText]}>Online</Text>
      </View>

      {/* Action Buttons */}
      <ActionButtons onCall={handleCall} onMessage={handleMessage} />
    </View>
  );
};

export default ShopProfile;
