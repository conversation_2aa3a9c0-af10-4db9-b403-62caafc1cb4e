import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useSelectorCustomerState } from '../../../../redux/hooks/hooks';
import { DataController } from '../../../base-controller';

export const useOrderData = (select: string) => {
  const [orderDetail, setOrderDetail] = useState<any>([]);
  const customer = useSelectorCustomerState().data;
  const navigation = useNavigation<any>();
  const orderController = new DataController('Order');

  const getOrderDetail = async () => {
    if (!customer?.Id) return;
    
    try {
      const response = await orderController.getPatternList({
        query: `@CustomerId: {${customer.Id}}`,
        pattern: {
          ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
        },
      });
      
      if (response?.code === 200) {
        setOrderDetail(response.data);
        return response;
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
    }
  };

  // Effect for select change
  useEffect(() => {
    if (select === 'Cá nhân') {
      getOrderDetail();
    }
  }, [select]);

  // Effect for navigation focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', async () => {
      getOrderDetail();
    });
    return unsubscribe;
  }, [navigation]);

  return {
    orderDetail,
    getOrderDetail,
  };
};
