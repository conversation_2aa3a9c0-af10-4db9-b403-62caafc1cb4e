import {
  KeyboardAvoidingView,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  View,
} from 'react-native';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TextFieldForm} from '../../../../../project-component/component-form';
import {useForm} from 'react-hook-form';
import WScreenFooter from '../../../../layout/footer';
import AppButton from '../../../../../component/button';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import {useEffect, useRef} from 'react';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../../router/router';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import ConfigAPI from '../../../../../config/configApi';
import {CustomerRole} from '../../../../../redux/reducers/user/da';
import {DataController} from '../../../../base-controller';

interface Props {
  type: string;
  title?: string;
  subtitle?: string;
  toiletItem?: any;
  thumbUrl?: string;
  submitTitle?: string;
  placeholder?: string;
  upgrade?: boolean;
  serviceId?: string;
  customer?: any;
}

export default function DescriptionInforForm(props: Props) {
  const methods = useForm({shouldFocusError: false});
  const navigation = useNavigation<any>();
  const company = useSelectorCustomerCompanyState().data;
  const userRole = useSelectorCustomerState().role;

  useEffect(() => {
    if (props.serviceId) {
      if (
        company?.Id === ConfigAPI.ktxCompanyId &&
        userRole?.Role.includes(CustomerRole.Coordinator)
      ) {
        if (props.serviceId) {
          const toiletServicesController = new DataController('ToiletServices');
          toiletServicesController.getById(props.serviceId).then((res: any) => {
            if (res.code === 200 && res.data) {
              methods.setValue('serviceData', res.data);
              methods.setValue('guest', props.customer);
            }
          });
        }
      }
    }
  }, [props.serviceId, userRole, company]);

  return (
    <View style={{flex: 1}}>
      <ScrollView>
        <KeyboardAvoidingView
          style={{flex: 1, paddingVertical: 24, paddingHorizontal: 16}}>
          <Pressable>
            <Text
              style={[
                TypoSkin.subtitle4,
                {color: ColorThemes.light.neutral_text_subtitle_color},
              ]}>
              Bước 1
            </Text>
            <Text
              style={[
                TypoSkin.heading5,
                {
                  color: ColorThemes.light.neutral_text_title_color,
                  paddingTop: 4,
                  paddingBottom: 8,
                },
              ]}>
              {props.title}
            </Text>
            {props.subtitle && (
              <Text
                style={[
                  TypoSkin.body2,
                  {
                    color: ColorThemes.light.neutral_text_body_color,
                    paddingBottom: 24,
                  },
                ]}>
                {props.subtitle}
              </Text>
            )}
            <TextFieldForm
              required
              control={methods.control}
              name="TextArea"
              errors={methods.formState.errors}
              placeholder={props.placeholder}
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 203,
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              onBlur={value => {
                if (value == undefined || value.length == 0) {
                  methods.setError('TextArea', {
                    message: 'Vui lòng nhập thông tin mô tả',
                  });
                } else {
                  methods.clearErrors('TextArea');
                }
              }}
              register={methods.register}
            />
          </Pressable>
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
              justifyContent: 'center',
              paddingTop: 11,
            }}>
            <SkeletonImage
              source={{
                uri:
                  props.thumbUrl ??
                  'https://file-mamager.wini.vn/Upload/2024/12/media1_7d48.png',
              }}
              style={{height: 300, width: 300}}
            />
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        {props.type == 'CleanFlow' || props.type == 'ContactFlow' ? null : (
          <AppButton
            title={'Gửi thông tin'}
            backgroundColor={ColorThemes.light.neutral_main_background_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              if (
                methods.getValues('TextArea') == undefined ||
                methods.getValues('TextArea').length == 0
              ) {
                methods.setError('TextArea', {
                  message: 'Vui lòng nhập thông tin mô tả',
                });
                return;
              } else {
                methods.clearErrors('TextArea');
              }
              navigation.push(props.type, {
                upgrade: props.upgrade,
                step: 2,
                description: methods.getValues('TextArea'),
                toiletItem: props.toiletItem,
                serviceData: methods.watch('serviceData'),
                customer: methods.watch('guest'),
              });
            }}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
          />
        )}
        <AppButton
          title={props.submitTitle ?? ''}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            if (props.type == 'CleanFlow' || props.type == 'ContactFlow') {
              if (
                methods.getValues('TextArea') == undefined ||
                methods.getValues('TextArea').length == 0
              ) {
                methods.setError('TextArea', {
                  message: 'Vui lòng nhập thông tin vấn đề',
                });
                return;
              } else {
                methods.clearErrors('TextArea');
              }
            }
            navigation.push(props.type, {
              upgrade: props.upgrade,
              step: 0,
              description: methods.getValues('TextArea'),
              toiletItem: props.toiletItem,
              serviceData: methods.watch('serviceData'),
              customer: methods.watch('guest'),
            });
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </View>
  );
}
