import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Pressable,
} from 'react-native';

import {ColorThemes} from '../../../assets/skin/colors';
import {useNavigation} from '@react-navigation/native';
import {TypoSkin} from '../../../assets/skin/typography';
import {NewsItem} from '../../../types/newsType';
import NewsDa from './newsDa';
import LatestNewsCard from './card/LatestNewsCard';
import {RootScreen} from '../../../router/router';

// Component chính của ứng dụng
const ListLatestNews = ({
  isRefresh,
  isLoadMore,
  onLoadMoreEnd,
  style = {},
}: {
  isRefresh: boolean;
  isLoadMore: boolean;
  onLoadMoreEnd: (hasMore: boolean) => void;
  style?: any;
}) => {
  const [latestNews, setLatestNews] = useState<NewsItem[]>([]);
  const [page, setPage] = useState(1);
  const navigation = useNavigation<any>();
  const size = 5;

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    if (isRefresh) {
      initData();
    }
  }, [isRefresh]);

  useEffect(() => {
    if (isLoadMore) {
      loadMoreData();
    }
  }, [isLoadMore]);

  const initData = async () => {
    const response = await NewsDa.fetch({
      page: 1,
      size: size,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (response.length > 0) {
      setLatestNews(response);
      setPage(1);
    }
  };

  const loadMoreData = async () => {
    const nextPage = page + 1;
    const response = await NewsDa.fetch({
      page: nextPage,
      size: size,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });

    if (response?.data?.length > 0) {
      setLatestNews(prevNews => [...prevNews, ...response.data]);
      setPage(nextPage);
      onLoadMoreEnd(true);
    } else {
      onLoadMoreEnd(false);
    }
  };

  const onNavigateToDetail = (item: NewsItem) => {
    navigation.navigate(RootScreen.DetailNews, {id: item.Id});
  };

  return (
    <Pressable style={[style]}>
      {/* Section Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tin mới</Text>
      </View>

      {/* News List */}
      <FlatList
        data={latestNews}
        renderItem={({item}) => (
          <View style={{marginBottom: 12}}>
            <LatestNewsCard
              item={item}
              onPress={() => onNavigateToDetail(item)}
            />
          </View>
        )}
        keyExtractor={item => item.Id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />
      {isLoadMore && (
        <ActivityIndicator
          style={{marginVertical: 10}}
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      )}
    </Pressable>
  );
};

// StyleSheet để định nghĩa toàn bộ style
const styles = StyleSheet.create({
  // Header Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 12,
  },
  headerTitle: {
    ...TypoSkin.title2,
  },
  seeMore: {
    ...TypoSkin.title5,
    fontWeight: 500,
    color: ColorThemes.light.primary_main_color,
  },
});

export default ListLatestNews;
