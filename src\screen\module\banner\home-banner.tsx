import React, { useEffect } from "react";
import { ColorThemes } from "../../../assets/skin/colors";
import { SkeletonImage } from "../../../project-component/skeleton-img";
import { Pressable } from "react-native";

export default function HomeBanner() {
    return <Pressable>
        <SkeletonImage source={{ uri: "https://file-mamager.wini.vn/Upload/2024/12/Mask group_ac0f.png" }} style={{
            height: 200, borderRadius: 8, backgroundColor: ColorThemes.light.neutral_text_placeholder_color, objectFit: "fill"
        }} />
    </Pressable>
}