import React from "react"
import { Text } from "react-native-paper"
import { TypoSkin } from "../assets/skin/typography"
import { ActivityIndicator, GestureResponderEvent, TextStyle, TouchableOpacity, View } from "react-native"
import { ColorSkin } from "../assets/skin/colors"
import { FilledRemove } from "../assets/icon"
import { ComponentStatus } from "../component/component-status"

export const PrimaryButton = ({ title, status, style = {}, styleText = {}, onPress, disabled = false, isLoading = false, prefix, suffix }:
    { title: string, status?: number, disabled?: boolean, isLoading?: boolean, style?: TextStyle, styleText?: TextStyle, onPress?: (((event: GestureResponderEvent) => void) & (() => void)), prefix?: React.ReactNode, suffix?: React.ReactNode }) => {
    let _color: string = '#fff'
    let _bgColor = ColorSkin.primary
    switch (status) {
        case ComponentStatus.INFOR:
            _color = ColorSkin.inforColor
            _bgColor = ColorSkin.inforBackground
            break;
        case ComponentStatus.ERROR:
            _color = ColorSkin.errorColor
            _bgColor = ColorSkin.errorBackground
            break;
        case ComponentStatus.WARNING:
            _color = ColorSkin.warningColor
            _bgColor = ColorSkin.warningBackground
            break;
        case ComponentStatus.SUCCSESS:
            _color = ColorSkin.successColor
            _bgColor = ColorSkin.successBackground
            break;
        case 5:
            _color = ColorSkin.textColorGrey1
            _bgColor = ColorSkin.disabledBackground
            break;
        case 7:
            _color = '#898A8D'
            _bgColor = ColorSkin.backgroundGrey3
            break;
        default:
            break;
    }
    return <TouchableOpacity disabled={disabled || isLoading} onPress={onPress} style={[{ flexDirection: 'row', alignItems: 'center', backgroundColor: disabled ? ColorSkin.disabledBackground : _bgColor, borderRadius: 24, height: 40, paddingHorizontal: 24 }, style]}>
        {isLoading ? <ActivityIndicator size="small" color={_color} /> : <View>
            {prefix}
            <Text style={[TypoSkin.buttonText3, { color: disabled ? ColorSkin.textColorGrey1 : _color, fontWeight: 'bold', textAlign: 'center' }, styleText]}>{title}</Text>
            {suffix}
        </View>}
    </TouchableOpacity>
}

export const ButtonWithIcon = ({ title, style, styleText, icon, onPress, disabled = false }: { title: string, icon?: React.ReactNode, style?: TextStyle, styleText?: TextStyle, disabled?: boolean, onPress?: (((event: GestureResponderEvent) => void) & (() => void)) }) => {
    return <TouchableOpacity disabled={disabled} onPress={onPress} style={[{ flexDirection: 'row', gap: 6, alignItems: 'center', backgroundColor: disabled ? ColorSkin.disabledBackground : ColorSkin.primary, borderRadius: 24, height: 48, paddingHorizontal: 24 }, (style ?? {})]}>
        {icon ?? <FilledRemove />}
        <Text style={[TypoSkin.buttonText3, { color: disabled ? ColorSkin.textColorGrey1 : '#fff', fontWeight: 'bold', textAlign: 'center', lineHeight: undefined }, (styleText ?? {})]}>{title}</Text>
    </TouchableOpacity>
}