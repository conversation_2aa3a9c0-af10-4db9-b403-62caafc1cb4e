import {StyleSheet} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';

export const datePickerStyles = StyleSheet.create({
  // Container styles
  container: {
    marginVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.transparent,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 48,
  },

  // Icon styles
  iconContainer: {
    marginLeft: 12,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    width: 24,
    height: 24,
  },

  // Text styles
  displayText: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
  },
  placeholderText: {
    color: '#8E8E93',
  },
  errorText: {
    fontSize: 12,
    color: '#E14337',
    marginTop: 4,
    fontWeight: '400',
  },

  // Bottom sheet styles
  actionButton: {
    paddingVertical: 6,
    paddingHorizontal: 4,
  },
  bottomSheetContent: {
    paddingHorizontal: 16,
  },

  // Picker styles
  pickerContainer: {
    alignItems: 'center',
  },
  datePicker: {
    // Default date picker styles
  },

  // Tab styles (for DateRangePicker)
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    minHeight: 60,
    justifyContent: 'center',
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#000000',
  },
  tabDate: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 2,
  },
  activeTabDate: {
    color: '#007AFF',
    fontWeight: '600',
  },
});

// Color constants
export const datePickerColors = {
  background: {
    enabled: '#FFFFFF',
    disabled: '#F5F5F5',
  },
  border: {
    default: '#E5E5E7',
    error: '#E14337',
    disabled: '#D1D5DB',
  },
  text: {
    primary: '#000000',
    placeholder: '#8E8E93',
    error: '#E14337',
    active: '#007AFF',
    grey: '#8E8E93',
  },
  opacity: {
    disabled: 0.6,
    iconDisabled: 0.5,
  },
};
