import {
  ActivityIndicator,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import AppButton from '../../../../../component/button';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  FDialog,
  FNumberPicker,
  showDialog,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {randomGID, regexGetVariables, Ultis} from '../../../../../utils/Utils';
import {DataController} from '../../../../base-controller';
import {
  ToiletServiceStatus,
  TaskType,
  CateServicesType,
  ContractStatus,
  AddendumType,
  ContractType,
} from '../../../service/components/da';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../redux/hooks/hooks';
import {
  useState,
  useRef,
  useMemo,
  useEffect,
  forwardRef,
  ComponentState,
} from 'react';
import {CustomerRole} from '../../../../../redux/reducers/user/da';
import ListTile from '../../../../../component/list-tile/list-tile';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import {SkeletonImage} from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';
import {
  Fselect1Form,
  TextFieldForm,
} from '../../../../../project-component/component-form';
import ScreenHeader from '../../../../layout/header';
import {PopupSelectDevices} from '../popup/PopupSelectDevices';
import {PopupSelectMaterial} from '../popup/PopupSelectMaterial';
import WScreenFooter from '../../../../layout/footer';
import {PopupSelectTasks} from '../popup/PopupSelectTasks';
import {useForm} from 'react-hook-form';
import EmptyPage from '../../../../../project-component/empty-page';
import WebView from 'react-native-webview';
import {
  caculateTotalServicesValue,
  mapAddendumContractData,
  mapContractData,
  quoteTableModelPrint,
} from './PrintForm';
import {ButtonViewRejectReason} from '../popup/DialogCustomize';
import {LoadingUI} from '../../../../../component/Loading/FLoading';
import CetificateAchievemenDa from 'screen/pages/CetificateAchievementPage/CetificateAchievemenDa';

export default function QuoteTable({
  toilet,
  toiletServices,
  methods,
  onSubmit,
  setToiletServices,
  isRefreshing,
  onRefreshing,
  customer,
}: any) {
  const quoteTemplateId = '7bf6069a5286414ca50b850c72e969dc';
  const user = useSelectorCustomerState().data;
  const owner = useSelectorCustomerCompanyState().owner;
  const company = useSelectorCustomerCompanyState().data;
  const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
  const userRole = useSelectorCustomerState().role;
  const cateServices = useMemo(
    () =>
      methods
        ?.watch('cateServices')
        ?.filter(
          (e: any) =>
            e.Id !== CateServicesType.contact && e.Id !== CateServicesType.edu,
        ),
    [methods?.watch('cateServices')],
  );
  const [consultantTask, setConsultantTask] = useState<any>(null);

  const [quoteAddendums, setQuoteAddendums] = useState<Array<any>>([]);
  const [selectedContent, setSelectedContent] = useState<any>({});

  const isEditable = useMemo(() => {
    if (selectedContent != undefined) {
      if (
        selectedContent?.Status === ContractStatus.init ||
        selectedContent?.Status === ContractStatus.reject
      ) {
        if (user?.Id === toiletServices?.CustomerId) return true;
        else if (
          toiletServices?.CustomerId === owner?.Id &&
          ((consultantTask && consultantTask?.CustomerId === user?.Id) ||
            userRole?.Role?.includes(CustomerRole.Coordinator))
        )
          return true;
      }
    }
    return false;
  }, [
    toiletServices,
    user,
    consultantTask,
    userRole,
    selectedContent,
    quoteAddendums?.length,
  ]);

  const dialogRef = useRef<any>();
  const popupRef = useRef<any>();

  const cateServicesOptions = useMemo(() => {
    if (cateServices?.length && user) {
      if (toiletServices?.CustomerId === user.Id) {
        return cateServices?.filter(
          (e: any) =>
            !toiletServices?.CateServicesId?.includes(e?.Id) &&
            user?.CateServicesId?.includes(e.Id),
        );
      } else if (owner) {
        return cateServices?.filter(
          (e: any) =>
            !toiletServices?.CateServicesId?.includes(e?.Id) &&
            owner?.CateServicesId?.includes(e.Id),
        );
      } else return [];
    }
  }, [cateServices?.length, toiletServices, owner, user]);

  const rejectReasons = useMemo(() => {
    if (selectedContent != undefined) {
      if (selectedContent?.RejectReason) {
        return JSON.parse(selectedContent?.RejectReason);
      }
    }
    return [];
  }, [selectedContent]);

  const getQuoteAddendums = async () => {
    const controller = new DataController('Addendum');
    const res = await controller.getListSimple({
      page: 1,
      size: 100,
      query: `@ToiletServicesId:{${toiletServices.Id}} @Type:[${AddendumType.quote} ${AddendumType.quote}]`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (res.code === 200) {
      if (res.data.length) {
        setQuoteAddendums(res.data);
        if (res.data) {
          var list: Array<any> = res.data;
          const obj = {...list[0]};

          setSelectedContent(obj);
        }

        methods.setValue('quoteAddendumId', `${res.data[0]?.Id}`);
      }
    }
  };

  const onChangeToiletServices = (data: any) => {
    const controller = new DataController('ToiletServices');
    controller.edit([data]);
    setToiletServices(data);
  };

  useEffect(() => {
    if (toiletServices) {
      const taskController = new DataController('Task');
      taskController
        .aggregateList({
          page: 1,
          size: 1,
          searchRaw: `@ToiletServicesId:{${toiletServices?.Id}} @Type:[${TaskType.consultant} ${TaskType.consultant}]`,
        })
        .then(res => {
          if (res.code === 200 && res.data.length)
            setConsultantTask(res.data[0]);
        });
      getQuoteAddendums();
    }
  }, [toiletServices, isRefreshing]);

  const showEmptyPage = useMemo(() => {
    if (!isEditable && selectedContent != undefined) {
      return (
        selectedContent?.Status === ContractStatus.init ||
        selectedContent?.Status === ContractStatus.reject
      );
    } else {
      return false;
    }
  }, [isEditable, selectedContent]);

  const methodContractTime = useForm({shouldFocusError: false});

  const {width, height} = Dimensions.get('window');

  const selectQuoteAddendum = () => {
    return quoteAddendums.length > 1 ? (
      <View style={{paddingHorizontal: 16, paddingTop: 16}}>
        <Fselect1Form
          control={methods.control}
          errors={methods.formState.errors}
          name="quoteAddendumId"
          onChange={value => {
            if (value) {
              setSelectedContent(
                quoteAddendums.find((e: any) => e.Id === value.id),
              );
              methods.setValue('quoteAddendumId', value.id);
            }
          }}
          options={quoteAddendums.map((e: any) => {
            const isSigned = e.Status === ContractStatus.guestSigned;
            return {
              id: e.Id,
              name: (
                <View
                  style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
                  {isSigned ? (
                    <Winicon
                      src="fill/layout/circle-check"
                      color={ColorThemes.light.success_main_color}
                      size={16}
                    />
                  ) : null}
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: isSigned
                        ? ColorThemes.light.success_main_color
                        : ColorThemes.light.neutral_text_title_color,
                    }}>
                    {e.Name}
                  </Text>
                </View>
              ),
            };
          })}
        />
      </View>
    ) : null;
  };

  return (
    <View style={{flex: 1}}>
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      {selectQuoteAddendum()}
      {showEmptyPage ? (
        <View style={{flex: 1, justifyContent: 'center'}}>
          <EmptyPage title={'Đơn hàng đang chờ đối tác gửi báo giá'} />
        </View>
      ) : (
        <View
          style={{
            flex: 1,
            padding: !isEditable ? 0 : 16,
            paddingBottom: isEditable ? 115 : 0,
          }}>
          {!isEditable ? (
            <Pressable style={{flex: 1}}>
              {rejectReasons.length > 0 ? (
                <View style={{marginHorizontal: 16, paddingVertical: 8}}>
                  <ButtonViewRejectReason
                    customers={[customer.data]}
                    rejectReasons={rejectReasons}
                  />
                </View>
              ) : null}
              {selectedContent?.Id ? (
                <Pressable
                  style={{
                    flex: 1,
                    height: Dimensions.get('screen').height * 2,
                    width: '100%',
                  }}>
                  <WebView
                    key={`webview-${selectedContent?.Id}-${selectedContent?.Status}`}
                    renderError={() => <EmptyPage />}
                    startInLoadingState
                    onLoadEnd={() => {
                      console.log('WebView contract finished loading');
                    }}
                    textZoom={100}
                    bounces={false}
                    nestedScrollEnabled
                    limitsNavigationsToAppBoundDomains
                    renderLoading={() => (
                      <ActivityIndicator
                        style={{
                          backgroundColor: 'transparent',
                          position: 'absolute',
                          left: 0,
                          right: 0,
                          top: height / 3,
                          zIndex: 9,
                        }}
                        color={ColorThemes.light.primary_main_color}
                        size="large"
                      />
                    )}
                    style={{
                      height: Dimensions.get('screen').height * 10,
                      width: width,
                      flex: 1,
                      paddingBottom: 25,
                    }}
                    injectedJavaScript='document.body.querySelector(".innerhtml-view").style.paddingBottom = "100px";'
                    originWhitelist={['*']}
                    javaScriptEnabled={true}
                    source={{
                      uri: `${ConfigAPI.urlWeb}contract-web-view?type=addendum&id=${selectedContent?.Id}`,
                    }}
                  />
                </Pressable>
              ) : null}
            </Pressable>
          ) : null}
          {isEditable ? (
            <View style={{flex: 1, height: '100%'}}>
              {rejectReasons.length > 0 ? (
                <View style={{}}>
                  <ButtonViewRejectReason
                    customers={[customer.data]}
                    rejectReasons={rejectReasons}
                  />
                </View>
              ) : null}
              <ListTile
                style={{
                  padding: 0,
                  paddingTop: rejectReasons.length > 0 ? 16 : 0,
                }}
                title="Bảng báo giá"
                trailing={
                  isEditable &&
                  cateServicesOptions &&
                  cateServicesOptions?.length > 0 ? (
                    <TouchableOpacity
                      onPress={() => {
                        const cateList = cateServices?.filter(
                          (e: any) =>
                            !selectedContent?.CateServicesId?.includes(e.Id),
                        );
                        showPopup({
                          ref: popupRef,
                          enableDismiss: true,
                          children: (
                            <View
                              style={{
                                backgroundColor:
                                  ColorThemes.light
                                    .neutral_absolute_background_color,
                                height: Dimensions.get('window').height / 1.7,
                                borderTopLeftRadius: 12,
                                borderTopRightRadius: 12,
                              }}>
                              <ScreenHeader
                                style={{
                                  backgroundColor:
                                    ColorThemes.light.transparent,
                                  flexDirection: 'row',
                                  paddingVertical: 4,
                                }}
                                title={`Thêm loại dịch vụ`}
                                prefix={<View />}
                                action={
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      padding: 12,
                                      alignItems: 'center',
                                    }}>
                                    <Winicon
                                      src="outline/layout/xmark"
                                      onClick={() => closePopup(popupRef)}
                                      size={20}
                                      color={
                                        ColorThemes.light
                                          .neutral_text_body_color
                                      }
                                    />
                                  </View>
                                }
                              />
                              {selectedContent != undefined
                                ? cateList.map((e: any) => {
                                    return (
                                      <ListTile
                                        key={e.Id}
                                        onPress={() => {
                                          const controller = new DataController(
                                            'Addendum',
                                          );
                                          const tmp = {
                                            ...selectedContent,
                                            CateServicesId: `${selectedContent?.CateServicesId},${e.Id}`,
                                          };
                                          controller.edit([tmp]);
                                          setSelectedContent(tmp);
                                          closePopup(popupRef);
                                        }}
                                        title={e.Name}
                                      />
                                    );
                                  })
                                : null}
                            </View>
                          ),
                        });
                      }}>
                      <Winicon
                        src="fill/layout/circle-plus"
                        size={24}
                        color={ColorThemes.light.primary_main_color}
                      />
                    </TouchableOpacity>
                  ) : (
                    <View />
                  )
                }
              />
              {/* danh sách các hạng mục */}
              <ScrollView
                style={{marginTop: 8, flex: 1}}
                refreshControl={
                  <RefreshControl
                    refreshing={isRefreshing ?? false}
                    onRefresh={() => {
                      onRefreshing();
                    }}
                  />
                }>
                {selectedContent != undefined
                  ? cateServices
                      ?.filter((e: any) =>
                        selectedContent?.CateServicesId?.includes(e.Id),
                      )
                      .map((item: any, index: any) => {
                        return (
                          <View
                            key={`catetile ${item.Id} ${index}`}
                            style={{
                              marginBottom:
                                index ===
                                cateServices?.filter((e: any) =>
                                  selectedContent?.CateServicesId?.includes(
                                    e.Id,
                                  ),
                                ).length -
                                  1
                                  ? 65
                                  : 0,
                            }}>
                            <CateTile
                              item={item}
                              methods={methods}
                              toiletServices={toiletServices}
                              isEditable={isEditable}
                              popupRef={popupRef}
                              dialogRef={dialogRef}
                            />
                          </View>
                        );
                      })
                  : null}
              </ScrollView>
            </View>
          ) : null}
        </View>
      )}
      <WScreenFooter style={{gap: 8, padding: 16}}>
        {isEditable ? (
          <ListTile
            style={{padding: 0}}
            title={
              <Text
                style={[
                  TypoSkin.heading6,
                  {
                    color: ColorThemes.light.neutral_text_title_color,
                    fontWeight: 'bold',
                  },
                ]}>
                Tổng cộng:{' '}
                {Ultis.money(caculateTotalServicesValue({methods: methods}))}
              </Text>
            }
          />
        ) : null}
        <View style={{flexDirection: 'row'}}>
          {selectedContent?.Status === ContractStatus.partnerSigned &&
          user?.Id === toilet[0]?.CustomerId ? (
            <View style={{flexDirection: 'row', gap: 8, flex: 1}}>
              <AppButton
                title={'Từ chối'}
                backgroundColor={
                  ColorThemes.light.neutral_main_background_color
                }
                borderColor="transparent"
                containerStyle={{
                  height: 40,
                  flex: 1,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                onPress={() => {
                  popupReject({
                    ref: dialogRef,
                    title: 'Bạn chắc chắn muốn từ chối báo giá này?',
                    methods: methods,
                    onSubmit: async (ev: any) => {
                      const newRejectReason = {
                        DateCreated: Date.now(),
                        Content: ev,
                      };
                      const controller = new DataController('Addendum');
                      const updateAddendum = {
                        ...selectedContent,
                        Status: ContractStatus.reject,
                        RejectReason: JSON.stringify([
                          ...rejectReasons,
                          newRejectReason,
                        ]),
                      };
                      const res = await controller.edit([updateAddendum]);
                      if (res.code === 200) {
                        setQuoteAddendums(qadde =>
                          qadde.map(e =>
                            e.Id === updateAddendum.Id ? updateAddendum : e,
                          ),
                        );
                        setSelectedContent(updateAddendum);
                      }
                      if (
                        toiletServices.Status ===
                        ToiletServiceStatus.sendCompleteQuote
                      )
                        onChangeToiletServices({
                          ...toiletServices,
                          Status: ToiletServiceStatus.consultant,
                        });
                    },
                  });
                }}
                textColor={ColorThemes.light.neutral_text_subtitle_color}
              />
              <AppButton
                title={'Thống nhất báo giá'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{
                  height: 40,
                  flex: 1,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 5,
                }}
                onPress={async () => {
                  showDialog({
                    ref: dialogRef,
                    status: ComponentStatus.WARNING,
                    title: 'Bạn chắc chắn muốn thống nhất báo giá?',
                    onSubmit: async () => {
                      const controller = new DataController('Addendum');
                      const updateAddendum = {
                        ...selectedContent,
                        Status: ContractStatus.guestSigned,
                        DateSign: Date.now(),
                        Value: caculateTotalServicesValue({methods: methods}),
                      };
                      const res = await controller.edit([updateAddendum]);
                      if (res.code === 200) {
                        setQuoteAddendums(qadde =>
                          qadde.map(e =>
                            e.Id === updateAddendum.Id ? updateAddendum : e,
                          ),
                        );
                        setSelectedContent(updateAddendum);
                      }
                      var partnerData = {data: undefined, company: undefined};

                      if (
                        toiletServices.Status < ToiletServiceStatus.contract
                      ) {
                        const templateContractId =
                          '********************************';
                        const contractController = new DataController(
                          'Contract',
                        );
                        const customerController = new DataController(
                          'Customer',
                        );
                        const cusRes = await customerController.getById(
                          toiletServices.CustomerId,
                        );
                        if (cusRes.code === 200) {
                          if (cusRes.data.CompanyProfileId) {
                            const companyController = new DataController(
                              'CompanyProfile',
                            );
                            const companyRes = await companyController.getById(
                              cusRes.data.CompanyProfileId,
                            );
                            if (companyRes.code === 200)
                              var customerCompany = companyRes.data;
                          }
                          partnerData = {
                            data: cusRes.data,
                            company: customerCompany,
                          };
                        }

                        const getContractContent = await mapContractData({
                          cateServices: cateServices,
                          customer: {data: user, company: company},
                          data: toilet,
                          methods: methods,
                          partnerData: partnerData,
                          servicesData: toiletServices,
                          ktxgroup: ktxgroup,
                          payment: selectedContent.Payment,
                        });

                        const newContract = {
                          Id: randomGID(),
                          Name: 'HỢP ĐỒNG HỢP TÁC TRIỂN KHAI',
                          DateCreated: Date.now(),
                          Type: ContractType.contract,
                          Status: ContractStatus.init,
                          ToiletServicesId: toiletServices.Id,
                          DocumentsId: templateContractId,
                          Content: getContractContent,
                        };
                        contractController.add([newContract]);
                      } else {
                        const contractAddendum = await controller.getListSimple(
                          {
                            page: 1,
                            size: 1,
                            query: `@ToiletServicesId:{${toiletServices.Id}} @Type:[${AddendumType.contract} ${AddendumType.contract}] @Status:[${ContractStatus.init} ${ContractStatus.init}]`,
                          },
                        );
                        if (
                          contractAddendum.code === 200 &&
                          contractAddendum.data.length
                        ) {
                          const getContractContent =
                            await mapAddendumContractData({
                              cateServices: cateServices,
                              customer: {data: user, company: company},
                              data: toilet,
                              methods: methods,
                              partnerData: partnerData,
                              servicesData: toiletServices,
                              ktxgroup: ktxgroup,
                              addendum: contractAddendum.data[0],
                              payment: selectedContent?.Payment ?? '',
                            });
                          controller.edit(
                            contractAddendum.data.map((e: any) => ({
                              ...e,
                              Content: getContractContent,
                            })),
                          );
                        }
                      }
                      onSubmit();
                      await CetificateAchievemenDa.getLogByToiletId(
                        toiletServices?.ToiletId.split(','),
                        `Đơn hàng NetZero đã được thống nhất báo giá và được chuyển sang bước tiếp theo `,
                      );
                    },
                  });
                }}
                textColor={ColorThemes.light.neutral_absolute_background_color}
              />
            </View>
          ) : null}
          {isEditable ? (
            <AppButton
              title={'Gửi báo giá'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={{
                height: 40,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={async () => {
                showDialog({
                  ref: dialogRef,
                  status: ComponentStatus.WARNING,
                  title: 'Bạn chắc chắn muốn gửi báo giá?',
                  content: (
                    <KeyboardAvoidingView
                      behavior={'height'}
                      keyboardVerticalOffset={100}
                      style={{gap: 8, alignItems: 'flex-start'}}>
                      <Text
                        style={{
                          ...TypoSkin.subtitle3,
                          color: ColorThemes.light.neutral_text_body_color,
                          textAlign: 'center',
                        }}>
                        Báo giá sẽ được chuyển tới khách hàng để xác nhận sau
                        khi bạn điền cấu trúc thanh toán.
                      </Text>
                      <TextFieldForm
                        control={methodContractTime.control}
                        name="Payment"
                        placeholder="Nhập cấu trúc thanh toán của khách hàng"
                        labelStyle={{fontWeight: 'normal'}}
                        textFieldStyle={{
                          height: 100,
                          width: '100%',
                          paddingHorizontal: 16,
                          paddingTop: 16,
                          paddingBottom: 16,
                          justifyContent: 'flex-start',
                          backgroundColor: ColorThemes.light.transparent,
                        }}
                        textStyle={{textAlignVertical: 'top'}}
                        numberOfLines={10}
                        returnKeyType="none"
                        multiline={true}
                        onSubmit={ev => {
                          console.log('submit', ev);
                        }}
                        register={methods.register}
                        onBlur={(ev: any) => {}}
                        errors={methodContractTime.formState.errors}
                      />
                    </KeyboardAvoidingView>
                  ),
                  onSubmit: async () => {
                    const controller = new DataController('Addendum');
                    var Payment = methodContractTime.getValues('Payment');
                    const templateController = new DataController('Documents');
                    if (Payment != undefined) {
                      var temp =
                        await templateController.getById(quoteTemplateId);
                      const now = new Date();
                      var tb = quoteTableModelPrint({
                        methods: methods,
                        cateServices: cateServices,
                      });
                      const updateAddendum = {
                        ...selectedContent,
                        Status: ContractStatus.partnerSigned,
                        DateSign: Date.now(),
                        Content: temp?.data?.Content?.replace(
                          regexGetVariables,
                          (m: string, key: string) => {
                            switch (key) {
                              case 'CustomerName':
                                return (
                                  customer.company?.Name ??
                                  customer.data?.Name ??
                                  ''
                                );
                              case 'CateServicesTitle':
                                return cateServices
                                  ?.filter((e: any) =>
                                    selectedContent?.CateServicesId?.includes(
                                      e.Id,
                                    ),
                                  )
                                  .map((e: any) => e.Name)
                                  .join(', ')
                                  .toUpperCase();
                              case 'day':
                                return now.getDate();
                              case 'month':
                                return now.getMonth() + 1;
                              case 'year':
                                return now.getFullYear();
                              case 'QuoteTable':
                                return tb;
                              case 'TotalPriceByText':
                                return Ultis.to_vietnamese(
                                  caculateTotalServicesValue({
                                    methods: methods,
                                  }),
                                );
                              case 'Payment':
                                return Payment;
                              case 'PartnerName':
                                return company?.Name ?? user?.Name;
                              case 'PartnerMobile':
                                return company?.Mobile ?? user?.Mobile;
                              default:
                                return m;
                            }
                          },
                        ),
                        Payment: Payment,
                      };
                      const res = await controller.edit([updateAddendum]);

                      if (res.code === 200) {
                        setQuoteAddendums(qadde =>
                          qadde.map(e =>
                            e.Id === updateAddendum.Id ? updateAddendum : e,
                          ),
                        );
                        setSelectedContent(updateAddendum);
                        await CetificateAchievemenDa.getLogByToiletId(
                          toiletServices?.ToiletId.split(','),
                          `Đơn hàng NetZero đã gửi báo giá thành công`,
                        );
                      }
                      if (
                        toiletServices.Status <
                        ToiletServiceStatus.sendCompleteQuote
                      )
                        onChangeToiletServices({
                          ...toiletServices,
                          Status: ToiletServiceStatus.sendCompleteQuote,
                        });
                      await CetificateAchievemenDa.getLogByToiletId(
                        toiletServices?.ToiletId.split(','),
                        `Đơn hàng NetZero đã gửi báo giá thành công`,
                      );
                    } else {
                      showSnackbar({
                        message: 'Vui lòng nhập thời gian thực hiện hợp đồng',
                        status: ComponentStatus.WARNING,
                      });
                    }
                  },
                });
              }}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          ) : null}
        </View>
      </WScreenFooter>
    </View>
  );
}

const CateTile = ({
  item,
  methods,
  toiletServices,
  isEditable,
  dialogRef,
  popupRef,
}: any) => {
  const [isOpen, setIsOpen] = useState(true);
  const listDevice = useMemo(() => {
    return (
      methods
        .watch('devices')
        .filter((e: any) => e?.CateServicesId === item.Id) ?? []
    );
  }, [methods.watch('devices')]);
  const listBio = useMemo(() => {
    return (
      methods
        .watch('bioProducts')
        .filter((e: any) => e?.CateServicesId === item.Id) ?? []
    );
  }, [methods.watch('bioProducts')]);
  const listTask = useMemo(() => {
    return (
      methods
        .watch('tasks')
        .filter((e: any) => e?.CateServicesId === item.Id) ?? []
    );
  }, [methods.watch('tasks')]);
  const listMaterial = useMemo(() => {
    return (
      methods
        .watch('materials')
        .filter((e: any) => e?.CateServicesId === item.Id) ?? []
    );
  }, [methods.watch('materials')]);

  const onChangeData = (name: any, value: any) => {
    var controller = new DataController('Device');
    switch (name) {
      case 'devices':
        controller = new DataController('Device');
        break;
      case 'bioProducts':
        controller = new DataController('BioProduct');
        break;
      case 'materials':
        controller = new DataController('MaterialToilet');
        break;
      case 'tasks':
        controller = new DataController('Task');
        break;
      default:
        break;
    }
    controller.edit([value]);
    methods.setValue(
      name,
      methods.watch(name).map((e: any) => (e.Id === value.Id ? value : e)),
    );
  };

  const onDeleteData = (name: any, ids: Array<any>) => {
    var controller = new DataController('Device');
    switch (name) {
      case 'devices':
        controller = new DataController('Device');
        break;
      case 'bioProducts':
        controller = new DataController('BioProduct');
        break;
      case 'materials':
        controller = new DataController('MaterialToilet');
        break;
      case 'tasks':
        controller = new DataController('Task');
        break;
      default:
        break;
    }
    controller.delete(ids);
    var list = methods.watch(name);
    methods.setValue(
      name,
      list.filter((e: any) => ids.every(id => e.Id !== id)),
    );
  };

  const onAddTaskData = (tasks: Array<any>) => {
    const controller = new DataController('Task');
    controller.add(tasks);
    methods.setValue('tasks', [...methods.watch('tasks'), ...tasks]);
  };

  return (
    <View style={{marginVertical: 8}}>
      <ListTile
        style={{
          padding: 0,
          paddingTop: 8,
          borderColor: ColorThemes.light.neutral_main_background_color,
          borderWidth: 1,
          borderRadius: 8,
          backgroundColor: ColorThemes.light.neutral_main_background_color,
        }}
        title={item.Name ?? ''}
        onPress={() => setIsOpen(!isOpen)}
        listtileStyle={{paddingHorizontal: 8, paddingBottom: 8}}
        titleStyle={[
          TypoSkin.heading7,
          {color: ColorThemes.light.neutral_text_title_color},
        ]}
        trailing={
          <Winicon
            src={
              isOpen
                ? 'outline/arrows/arrow-sm-down'
                : 'fill/arrows/arrow-sm-right'
            }
            size={28}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        }
        bottom={
          <View
            style={{
              flex: 1,
              width: '100%',
              paddingHorizontal: 8,
              paddingVertical: isOpen ? 8 : 0,
              borderBottomLeftRadius: 8,
              borderBottomRightRadius: 8,
              backgroundColor:
                ColorThemes.light.neutral_absolute_background_color,
            }}>
            {isOpen ? (
              <View>
                {/* devices */}
                <ParentQuote
                  isEditable={isEditable}
                  list={listDevice}
                  methods={methods}
                  name={'devices'}
                  onAdd={() => {
                    const _devs = listDevice.map((dev: any) => {
                      const product = methods
                        .watch('products')
                        .find((e: any) => e.Id === dev.ProductId);
                      return {...product, _Quantity: dev.Quantity};
                    });
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <PopupSelectDevices
                          ref={popupRef}
                          devices={_devs}
                          onSubmit={(selectedProducts: any) => {
                            const controller = new DataController('Device');
                            const newListDevice = selectedProducts.map(
                              (p: any) => {
                                const dev = listDevice.find(
                                  (e: any) => e.ProductId === p.Id,
                                );
                                if (dev)
                                  return {
                                    ...dev,
                                    Quantity: p['_Quantity'] ?? dev.Quantity,
                                  };
                                else
                                  return {
                                    Id: randomGID(),
                                    Name: p.Name,
                                    DateCreated: Date.now(),
                                    ToiletServicesId: toiletServices?.Id,
                                    ProductId: p.Id,
                                    Quantity: p['_Quantity'],
                                    Price: p.Price,
                                    CateServicesId: item.Id,
                                    Description: p.Description,
                                    Specifications: p.Specifications,
                                    Vat: 10,
                                  };
                              },
                            );
                            const deleteDevices = listDevice
                              .filter((e: any) =>
                                newListDevice.every(
                                  (dev: any) => dev.Id !== e.Id,
                                ),
                              )
                              .map((e: any) => e.Id);
                            if (deleteDevices.length)
                              controller.delete(deleteDevices);
                            controller.add(newListDevice);
                            methods.setValue('devices', [
                              ...methods
                                .getValues('devices')
                                .filter(
                                  (e: any) =>
                                    !listDevice.some(
                                      (dev: any) => dev.Id === e.Id,
                                    ),
                                ),
                              ...newListDevice,
                            ]);
                            methods.setValue('products', [
                              ...methods
                                .getValues('products')
                                .filter(
                                  (e: any) =>
                                    !selectedProducts.some(
                                      (p: any) => p.Id === e.Id,
                                    ),
                                ),
                              ...selectedProducts,
                            ]);
                          }}
                        />
                      ),
                    });
                  }}
                  onChange={(ev: any) => {
                    onChangeData('devices', ev);
                  }}
                  onDelete={(id: any) => {
                    showDialog({
                      ref: dialogRef,
                      status: ComponentStatus.WARNING,
                      title: 'Bạn có chắc muốn xóa ?',
                      onSubmit: async () => {
                        onDeleteData('devices', [id]);
                      },
                    });
                  }}
                  popupRef={popupRef}
                />
                {/* bioProducts */}
                <ParentQuote
                  isEditable={isEditable}
                  list={listBio}
                  methods={methods}
                  name={'bioProducts'}
                  onAdd={() => {
                    const _bioP = listBio.map((dev: any) => {
                      const product = methods
                        .watch('products')
                        .find((e: any) => e.Id === dev.ProductId);
                      return {...product, _Quantity: dev.Quantity};
                    });
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <PopupSelectDevices
                          ref={popupRef}
                          devices={_bioP}
                          isBio={true}
                          onSubmit={(selectedProducts: any) => {
                            const controller = new DataController('BioProduct');
                            const newListBio = selectedProducts.map(
                              (p: any) => {
                                const bio = listBio.find(
                                  (e: any) => e.ProductId === p.Id,
                                );
                                if (bio)
                                  return {
                                    ...bio,
                                    Quantity: p['_Quantity'] ?? bio.Quantity,
                                  };
                                else
                                  return {
                                    Id: randomGID(),
                                    Name: p.Name,
                                    DateCreated: Date.now(),
                                    ToiletServicesId: toiletServices?.Id,
                                    ProductId: p.Id,
                                    Quantity: p['_Quantity'],
                                    Price: p.Price,
                                    CateServicesId: item.Id,
                                    Description: p.Description,
                                    Specifications: p.Specifications,
                                    Vat: 10,
                                  };
                              },
                            );
                            const deleteBio = listBio
                              .filter((e: any) =>
                                newListBio.every((dev: any) => dev.Id !== e.Id),
                              )
                              .map((e: any) => e.Id);
                            if (deleteBio.length) controller.delete(deleteBio);
                            controller.add(newListBio);
                            methods.setValue('bioProducts', [
                              ...methods
                                .getValues('bioProducts')
                                .filter(
                                  (e: any) =>
                                    !listBio.some(
                                      (dev: any) => dev.Id === e.Id,
                                    ),
                                ),
                              ...newListBio,
                            ]);
                            methods.setValue('products', [
                              ...methods
                                .getValues('products')
                                .filter(
                                  (e: any) =>
                                    !selectedProducts.some(
                                      (p: any) => p.Id === e.Id,
                                    ),
                                ),
                              ...selectedProducts,
                            ]);
                          }}
                        />
                      ),
                    });
                  }}
                  onChange={(ev: any) => {
                    onChangeData('bioProducts', ev);
                  }}
                  onDelete={(id: any) => {
                    showDialog({
                      ref: dialogRef,
                      status: ComponentStatus.WARNING,
                      title: 'Bạn có chắc muốn xóa ?',
                      onSubmit: async () => {
                        onDeleteData('bioProducts', [id]);
                      },
                    });
                  }}
                  popupRef={popupRef}
                />
                {/* materials */}
                <ParentQuote
                  isEditable={isEditable}
                  list={listMaterial}
                  methods={methods}
                  name={'materials'}
                  onAdd={() => {
                    const _mats = listMaterial.map((mat: any) => {
                      const m = methods
                        .watch('materialPartner')
                        .find((e: any) => e.Id === mat.MaterialId);
                      return {...m, _Quantity: mat.Quantity};
                    });
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <PopupSelectMaterial
                          ref={popupRef}
                          materials={_mats}
                          onSubmit={(selectedMaterials: any) => {
                            const controller = new DataController(
                              'MaterialToilet',
                            );
                            const newListMaterial = selectedMaterials.map(
                              (m: any) => {
                                const mat = listMaterial.find(
                                  (e: any) => e.MaterialId === m.Id,
                                );
                                if (mat)
                                  return {
                                    ...mat,
                                    Quantity: m['_Quantity'] ?? mat.Quantity,
                                  };
                                else
                                  return {
                                    Id: randomGID(),
                                    Name: m.Name,
                                    DateCreated: Date.now(),
                                    ToiletServicesId: toiletServices?.Id,
                                    MaterialId: m.Id,
                                    Quantity: m['_Quantity'],
                                    Price: m.Price,
                                    CateServicesId: item.Id,
                                    Description: m.Description,
                                    Vat: 10,
                                  };
                              },
                            );
                            const deleteMaterials = listMaterial
                              .filter((e: any) =>
                                newListMaterial.every(
                                  (mat: any) => mat.Id !== e.Id,
                                ),
                              )
                              .map((e: any) => e.Id);
                            if (deleteMaterials.length)
                              controller.delete(deleteMaterials);
                            controller.add(newListMaterial);
                            methods.setValue('materials', [
                              ...methods
                                .getValues('materials')
                                .filter(
                                  (e: any) =>
                                    !listMaterial.some(
                                      (mat: any) => mat.Id === e.Id,
                                    ),
                                ),
                              ...newListMaterial,
                            ]);
                            methods.setValue('materialPartner', [
                              ...methods
                                .getValues('materialPartner')
                                .filter(
                                  (e: any) =>
                                    !selectedMaterials.some(
                                      (m: any) => m.Id === e.Id,
                                    ),
                                ),
                              ...selectedMaterials,
                            ]);
                          }}
                        />
                      ),
                    });
                  }}
                  onChange={(ev: any) => {
                    onChangeData('materials', ev);
                  }}
                  onDelete={(id: any) => {
                    showDialog({
                      ref: dialogRef,
                      status: ComponentStatus.WARNING,
                      title: 'Bạn có chắc muốn xóa ?',
                      onSubmit: async () => {
                        onDeleteData('materials', [id]);
                      },
                    });
                  }}
                  popupRef={popupRef}
                />
                {/* tasks */}
                <ParentQuote
                  isEditable={isEditable}
                  list={listTask}
                  methods={methods}
                  name={'tasks'}
                  onAdd={(ev: any) => {
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            height: Dimensions.get('window').height / 3,
                            borderTopLeftRadius: 12,
                            borderTopRightRadius: 12,
                          }}>
                          <ScreenHeader
                            style={{
                              backgroundColor: ColorThemes.light.transparent,
                              flexDirection: 'row',
                              paddingVertical: 4,
                            }}
                            title={`Thêm nhân công`}
                            prefix={<View />}
                            action={
                              <TouchableOpacity
                                onPress={() => closePopup(popupRef)}
                                style={{
                                  flexDirection: 'row',
                                  padding: 12,
                                  alignItems: 'center',
                                }}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  size={20}
                                  color={
                                    ColorThemes.light.neutral_text_body_color
                                  }
                                />
                              </TouchableOpacity>
                            }
                          />
                          <ListTile
                            onPress={() => {
                              onAddTaskData([
                                {
                                  Id: randomGID(),
                                  Name: 'Công việc mới',
                                  DateCreated: Date.now(),
                                  Description: 'Công việc cần thực hiện',
                                  Day: 5,
                                  Sort: 1,
                                  CateServicesId: item?.Id,
                                  ToiletServicesId: toiletServices?.Id,
                                  Price: 500000,
                                  Vat: 10,
                                },
                              ]);
                              closePopup(popupRef);
                            }}
                            title="Thêm mới"
                          />
                          <ListTile
                            onPress={() => {
                              showPopup({
                                ref: popupRef,
                                enableDismiss: true,
                                children: (
                                  <PopupSelectTasks
                                    ref={popupRef}
                                    onSubmit={(newTasks: any) => {
                                      onAddTaskData(
                                        newTasks.map((t: any) => {
                                          return {
                                            Id: randomGID(),
                                            Name: t.Name,
                                            DateCreated: Date.now(),
                                            Day: t.Day,
                                            Vat: t.Vat ?? 10,
                                            Price: t.Price,
                                            CateServicesId: item.Id,
                                            ToiletServicesId:
                                              toiletServices?.Id,
                                            Description: t.Description,
                                            Sort: 1,
                                          };
                                        }),
                                      );
                                    }}
                                  />
                                ),
                              });
                            }}
                            title="Thêm từ mẫu công việc"
                          />
                        </View>
                      ),
                    });
                  }}
                  onChange={(ev: any) => {
                    onChangeData('tasks', ev);
                  }}
                  onDelete={(id: any) => {
                    showDialog({
                      ref: dialogRef,
                      status: ComponentStatus.WARNING,
                      title: 'Bạn có chắc muốn xóa ?',
                      onSubmit: async () => {
                        onDeleteData('tasks', [id]);
                      },
                    });
                  }}
                  popupRef={popupRef}
                />
              </View>
            ) : null}
          </View>
        }
      />
    </View>
  );
};

const ParentQuote = ({
  isEditable,
  name,
  onAdd,
  onChange,
  onDelete,
  methods,
  list,
  popupRef,
}: any) => {
  const [isOpen, setIsOpen] = useState(true);

  const title = useMemo(() => {
    switch (name) {
      case 'devices':
        return 'Thiết bị';
      case 'tasks':
        return 'Nhân công';
      case 'materials':
        return 'Vật tư';
      case 'bioProducts':
        return 'Chế phẩm sinh học';
      default:
        break;
    }
  }, [name]);
  return (
    <ListTile
      title={`${title} ${list?.length > 0 ? `(${list?.length})` : ''}`}
      leading={
        <Winicon
          src={
            isOpen
              ? 'fill/arrows/triangle-sm-down'
              : 'fill/arrows/triangle-sm-right'
          }
          size={20}
          color={ColorThemes.light.neutral_text_body_color}
        />
      }
      titleStyle={[
        TypoSkin.heading7,
        {color: ColorThemes.light.neutral_text_title_color},
      ]}
      style={{
        borderBottomColor: ColorThemes.light.neutral_main_background_color,
        borderBottomWidth: 1,
        borderBottomLeftRadius: 8,
        borderBottomRightRadius: 8,
        marginTop: 8,
        marginBottom: 4,
        padding: 0,
      }}
      onPress={() => {
        if (list?.length > 0 || !isEditable) {
          setIsOpen(!isOpen);
        } else {
          onAdd();
        }
      }}
      trailing={
        isEditable ? (
          <Winicon
            onClick={onAdd}
            src="fill/layout/circle-plus"
            size={23}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        ) : (
          <View />
        )
      }
      bottom={
        <View
          style={{
            marginTop: 8,
            alignContent: 'flex-start',
            width: '100%',
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
          }}>
          {isOpen
            ? list?.map((item: any) => {
                switch (name) {
                  case 'devices':
                    var index = methods
                      .watch(name)
                      .findIndex((e: any) => e?.Id === item?.Id);
                    const product = methods
                      .watch('products')
                      ?.find((e: any) => e?.Id === item?.ProductId);
                    var tmpItem = {
                      ...item,
                      _Img: product?.Img,
                      _Unit: product?.Unit ?? 'cái',
                      _isPublic: product?.IsPublic,
                    };
                    break;
                  case 'tasks':
                    var index = methods
                      .watch(name)
                      .findIndex((e: any) => e?.Id === item?.Id);
                    tmpItem = {
                      ...item,
                      _Img: (
                        <Winicon
                          src="outline/business/positive-judgement"
                          size={28}
                        />
                      ),
                      _Unit: item?.Unit ?? 'MD',
                    };
                    break;
                  case 'materials':
                    var index = methods
                      .watch(name)
                      .findIndex((e: any) => e?.Id === item?.Id);
                    const materialPartner = methods
                      .watch('materialPartner')
                      ?.find((e: any) => e?.Id === item?.MaterialId);
                    var tmpItem = {
                      ...item,
                      _Img: materialPartner?.Img,
                      _Unit: materialPartner?.Unit ?? 'cái',
                    };
                    break;
                  case 'bioProducts':
                    var index = methods
                      .watch(name)
                      .findIndex((e: any) => e?.Id === item?.Id);
                    const bioProduct = methods
                      .watch('products')
                      ?.find((e: any) => e?.Id === item?.ProductId);
                    var tmpItem = {
                      ...item,
                      _Img: bioProduct?.Img,
                      _Unit: bioProduct?.Unit ?? 'cái',
                      _isPublic: bioProduct?.IsPublic,
                    };
                    break;
                  default:
                    break;
                }
                if (!item) return <View />;
                return (
                  <QuoteItem
                    key={item?.Id}
                    methods={methods}
                    isTask={name === 'tasks'}
                    isEditable={isEditable}
                    item={tmpItem}
                    name={`${name}[${index}]`}
                    onChange={(ev: any) => onChange({...item, ...ev})}
                    onDelete={() => {
                      onDelete(item?.Id);
                    }}
                    popupRef={popupRef}
                  />
                );
              })
            : null}
        </View>
      }
    />
  );
};

const QuoteItem = ({
  item,
  isEditable,
  onChange,
  name,
  onDelete,
  isTask = false,
  methods,
  popupRef,
}: any) => {
  const totalItem = useMemo(() => {
    let tmp =
      item?.Price *
      (isTask ? item?.Day : item?.Quantity) *
      ((100 - (item?.Discount ?? 0)) / 100);
    if (item.Vat) tmp += tmp * (item.Vat / 100);
    return tmp;
  }, [item]);

  if (isEditable && isTask) {
    methods.setValue('Name', item?.Name);
  }

  const editQuote = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <PopupEditQuoteInfo
          ref={popupRef}
          name={name}
          methods={methods}
          item={item}
          onChange={onChange}
          isTask={isTask}
        />
      ),
    });
  };

  return (
    <ListTile
      onPress={isEditable ? editQuote : undefined}
      leading={
        typeof item['_Img'] === 'string' ? (
          <SkeletonImage
            source={{uri: ConfigAPI.imgUrlId + item['_Img']}}
            style={{width: 45, height: 45, borderRadius: 4}}
          />
        ) : (
          <Text numberOfLines={1}>{item['_Img']}</Text>
        )
      }
      style={{
        padding: 0,
        borderRadius: 0,
        paddingVertical: 8,
        flex: 1,
        borderBottomColor: ColorThemes.light.neutral_main_border_color,
        borderBottomWidth: 1,
      }}
      title={item?.Name ?? ''}
      titleStyle={[
        TypoSkin.body3,
        {color: ColorThemes.light.neutral_text_body_color, fontWeight: 'bold'},
      ]}
      subtitle={
        <View style={{width: '100%', gap: 8, paddingTop: 4}}>
          <View style={{flexDirection: 'row', gap: 4}}>
            <Text
              style={[
                TypoSkin.subtitle4,
                {color: ColorThemes.light.neutral_text_body_color},
              ]}>
              Giá (VNĐ): {`${Ultis.money(item?.Price)}`}
            </Text>
            <Text
              style={[
                TypoSkin.subtitle4,
                {color: ColorThemes.light.warning_main_color},
              ]}>
              {item.Discount ? `↓${item.Discount}%` : ''}
            </Text>
          </View>
          {item.Vat ? (
            <Text
              style={[
                TypoSkin.subtitle4,
                {color: ColorThemes.light.warning_main_color},
              ]}>
              Thuế (VAT): +{item.Vat}%
            </Text>
          ) : null}
          <Text
            style={[
              TypoSkin.subtitle4,
              {color: ColorThemes.light.neutral_text_body_color},
            ]}>
            Đơn vị: {item['_Unit']}
          </Text>
        </View>
      }
      trailing={
        <View style={{alignItems: 'flex-end', width: '100%', gap: 24}}>
          {isEditable ? (
            <AppButton
              onPress={onDelete}
              title="Xóa"
              height={'auto'}
              width={50}
              backgroundColor={'transparent'}
              borderColor="transparent"
              textStyle={{
                ...TypoSkin.subtitle3,
              }}
              textColor={ColorThemes.light.error_main_color}
            />
          ) : null}
          <FNumberPicker
            disabled={!isEditable}
            buttonStyle={{width: 20, height: 20}}
            hideMinus={isTask ? item.Day == 1 : item.Quantity == 1}
            initValue={isTask ? item.Day : item.Quantity}
            style={{}}
            onChange={ev => {
              if (ev == 0) {
                onDelete();
              }
              if (ev) onChange(isTask ? {Day: ev} : {Quantity: ev});
            }}
          />
        </View>
      }
      bottom={
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            flex: 1,
            paddingVertical: 4,
            width: '100%',
          }}>
          {isEditable ? (
            <Text
              style={[
                TypoSkin.subtitle5,
                {color: ColorThemes.light.neutral_text_subtitle_color},
              ]}>
              Chạm để sửa thêm
            </Text>
          ) : (
            <View />
          )}
          <Text
            style={[
              TypoSkin.body3,
              {color: ColorThemes.light.primary_main_color, fontWeight: 'bold'},
            ]}>
            Thành tiền: {`${Ultis.money(totalItem)}`}
          </Text>
        </View>
      }
    />
  );
};

export const popupReject = ({
  ref,
  title,
  placeHolder,
  onSubmit,
  methods,
}: any) => {
  return showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: title ?? 'Bạn chắc chắn muốn từ chối báo giá này?',
    content: (
      <KeyboardAvoidingView style={{gap: 4, alignItems: 'flex-start'}}>
        <TextFieldForm
          required
          control={methods.control}
          name="RejectReasonDialog"
          label={placeHolder ?? 'Lý do từ chối'}
          labelStyle={{fontWeight: 'normal'}}
          textFieldStyle={{paddingHorizontal: 16}}
          register={methods.register}
          onBlur={(ev: any) => {}}
          errors={methods.formState.errors}
        />
      </KeyboardAvoidingView>
    ),
    onSubmit: async () => {
      if (methods.getValues('RejectReasonDialog')) {
        onSubmit(methods.getValues('RejectReasonDialog'));
        methods.setValue('RejectReasonDialog', undefined);
      } else {
        showSnackbar({
          message: 'Vui lòng nhập lý do để tiếp tục',
          status: ComponentStatus.WARNING,
        });
        methods.setValue('RejectReasonDialog', undefined);
      }
    },
  });
};

const PopupEditQuoteInfo = forwardRef(function PopupEditQuoteInfo(
  data: {item: any; methods: any; name: any; onChange: any; isTask: any},
  ref: any,
) {
  const {item, methods, onChange, isTask, name} = data;

  useEffect(() => {
    methods.setValue('Name', `${item.Name}`);
    methods.setValue('Price', Ultis.money(item.Price));
    methods.setValue(
      isTask ? `${name}.Day` : `${name}.Quantity`,
      `${isTask ? item.Day : (item.Quantity ?? '')}`,
    );
    methods.setValue('Discount', `${item.Discount ?? ''}`);
    methods.setValue('Vat', `${item.Vat ?? ''}`);
  }, [item]);
  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 100,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={`${item.Name ?? '-'}`}
        prefix={<View />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 135 : 0}
        style={{flex: 1, height: '100%', padding: 16}}>
        <ScrollView style={{gap: 8}}>
          {isTask ? (
            <TextFieldForm
              label="Tên"
              textFieldStyle={{
                padding: 16,
                backgroundColor: ColorThemes.light.transparent,
                marginBottom: 8,
              }}
              register={methods.register}
              control={methods.control}
              returnKeyType="done"
              errors={methods.formState.errors}
              onBlur={(ev: any) => {
                if (!ev) return;
                const txt = ev.trim();
                if (txt.length) onChange({Name: txt});
                else ev = item.Name;
              }}
              name="Name"
            />
          ) : null}
          {!item['_isPublic'] ? (
            <TextFieldForm
              control={methods.control}
              name="Price"
              label="Giá (VNĐ)"
              errors={methods.formState.errors}
              textFieldStyle={{
                padding: 16,
                backgroundColor: ColorThemes.light.transparent,
                marginBottom: 8,
              }}
              register={methods.register}
              type="money"
              returnKeyType="done"
              onBlur={async (value: any) => {
                if (!value) return;
                let newPrice = parseInt(value.replaceAll(',', ''));
                if (!isNaN(newPrice)) {
                  value = Ultis.money(newPrice);
                  onChange({Price: newPrice});
                } else value = Ultis.money(item.Price);
              }}
            />
          ) : null}
          <TextFieldForm
            control={methods.control}
            name={isTask ? `${name}.Day` : `${name}.Quantity`}
            label={`Số lượng (${item._Unit})`}
            errors={methods.formState.errors}
            textFieldStyle={{
              padding: 16,
              backgroundColor: ColorThemes.light.transparent,
              marginBottom: 8,
            }}
            register={methods.register}
            type="number-pad"
            returnKeyType="done"
            onBlur={(ev: any) => {
              if (!ev) return;
              const newQ = parseInt(ev);
              if (!isNaN(newQ)) {
                ev = newQ;
                onChange(isTask ? {Day: ev} : {Quantity: ev});
              } else ev = isTask ? item.Day : item.Discount;
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Discount"
            label="Giảm giá (%)"
            errors={methods.formState.errors}
            textFieldStyle={{
              padding: 16,
              backgroundColor: ColorThemes.light.transparent,
              marginBottom: 8,
            }}
            register={methods.register}
            type="number-pad"
            returnKeyType="done"
            onBlur={(ev: any) => {
              if (!ev) return;
              const newDiscount = parseFloat(ev);
              if (!isNaN(newDiscount)) {
                ev = newDiscount;
                onChange({Discount: newDiscount});
              } else ev = item.Discount;
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Vat"
            label="Thuế (VAT)"
            errors={methods.formState.errors}
            textFieldStyle={{
              padding: 16,
              backgroundColor: ColorThemes.light.transparent,
              marginBottom: 8,
            }}
            register={methods.register}
            type="number-pad"
            returnKeyType="done"
            onBlur={(ev: any) => {
              if (!ev) return;
              const newVat = parseFloat(ev);
              if (!isNaN(newVat)) {
                ev = newVat;
                onChange({Vat: newVat});
              } else ev = item.Vat;
            }}
          />
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
        }}>
        <AppButton
          title={'Xong'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            closePopup(ref);
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
