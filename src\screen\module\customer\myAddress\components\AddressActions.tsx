import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { AddressActionsProps } from '../types';
import { DIMENSIONS, ICONS } from '../constants';

/**
 * Component for address action buttons (edit, delete)
 */
export const AddressActions: React.FC<AddressActionsProps> = ({
  item,
  chooseAddress,
  onEdit,
  onDelete,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => onEdit(item)}
      >
        <Winicon
          src={ICONS.EDIT}
          size={DIMENSIONS.ICON_SIZE}
          color={ColorThemes.light.neutral_text_title_color}
        />
      </TouchableOpacity>
      
      {!chooseAddress && (
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onDelete(item)}
        >
          <Winicon
            src={ICONS.DELETE}
            size={DIMENSIONS.ICON_SIZE}
            color={ColorThemes.light.neutral_text_title_color}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: DIMENSIONS.ACTIONS_GAP,
    flexDirection: 'row',
  },
  actionButton: {
    padding: DIMENSIONS.ACTION_BUTTON_PADDING,
  },
});
