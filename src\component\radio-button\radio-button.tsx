import React, { useEffect } from 'react'
import { RadioButton } from 'react-native-paper'
import { ColorSkin } from '../../assets/skin/colors';
import { UseFormRegister } from 'react-hook-form';

interface FRadioProps {
    value: string,
    status?: boolean,
    disabled?: boolean,
    onPress: (value: boolean) => void
    register?: UseFormRegister<{}>;
}

export default function FRadioButton(props: FRadioProps) {
    const [checked, setChecked] = React.useState(props.status);

    useEffect(() => {
        setChecked(props.status);
    }, [props.status]);

    return <RadioButton.Android
        color={ColorSkin.primary}
        uncheckedColor='#00358033'
        value={props.value}
        disabled={props.disabled}
        status={checked ? "checked" : "unchecked"}
        onPress={() => {
            const temp = !checked
            setChecked(temp)
            props.onPress(temp)
        }}
    />
}
