import {StyleSheet, Dimensions} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';

const {width} = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const CreatePartnerProductFormStyles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  error: {
    color: 'red',
    marginLeft: 15,
    fontFamily: 'roboto',
    fontSize: 12,
  },
  section: {
    marginTop: 10,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 10,
    // elevation: 2, // Tạo bóng cho Android
    // shadowColor: '#A9A9A9', // Tạo bóng cho iOS
    // shadowOffset: {width: 0, height: 1},
    // shadowOpacity: 0.1,
    // shadowRadius: 2,
    // height: 120,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: ColorThemes.light.neutral_main_reverse_border_color,
  },
  limit: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_placeholder_color,
    textAlign: 'right',
    marginTop: 5,
  },
  textHeight: {
    height: 70,
    backgroundColor: 'white',
    fontWeight: 400,
  },
  textHeightLarge: {
    height: 100,
    backgroundColor: 'white',
    fontWeight: 400,
    paddingVertical: 12,
    textAlignVertical: 'top',
  },
  textField: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  productType: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  money: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingRight: 50, // Make space for VND text
  },
  moneyText: {
    height: 40,
    marginLeft: 8,
    flex: 1,
  },
  vndText: {
    color: 'blue',
    fontSize: 14,
    fontWeight: '500',
  },
  vndContainer: {
    position: 'absolute',
    right: 10,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  option: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    marginVertical: 6, // Khoảng cách đều trên và dưới
    marginHorizontal: 10,
    backgroundColor: 'white',
    height: 55,
  },
  optionPlaceholder: {
    fontSize: 14,
    color: '#888',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  freeShip: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  Button: {
    backgroundColor: 'blue',
    width: '80%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
    margin: 'auto',
    position: 'absolute',
    bottom: 40,
    left: '11%',
  },
  actionButtonText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});
