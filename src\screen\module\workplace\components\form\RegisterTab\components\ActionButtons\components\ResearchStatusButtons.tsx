import React from 'react';
import {StyleSheet} from 'react-native';
import {FDialog, showDialog} from 'component/export-component';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';

interface ResearchStatusButtonsProps {
  dialogRef: any;
  onCompleteSurvey: () => void;
}

export const ResearchStatusButtons: React.FC<ResearchStatusButtonsProps> = ({
  dialogRef,
  onCompleteSurvey,
}) => {
  return (
    <>
      <AppButton
        title={'Hoàn thành khảo sát'}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={styles.button}
        onPress={() => {
          showDialog({
            ref: dialogRef,
            title: 'Bạn chắc chắn đã khảo sát xong?',
            content:
              '<PERSON>u khi được xác nhận từ phía khách hàng, đơn hàng sẽ tự động chuyển sang bước tư vấn báo giá.',
            onSubmit: onCompleteSurvey,
          });
        }}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
      <FDialog ref={dialogRef} />
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    ...TypoSkin.subtitle4,
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingVertical: 5,
  },
});
