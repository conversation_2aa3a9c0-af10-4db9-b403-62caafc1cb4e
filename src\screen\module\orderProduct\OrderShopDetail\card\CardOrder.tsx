import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {FDialog, Winicon} from 'wini-mobile-components';
import {Ultis} from '../../../../../utils/Utils';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {CardOrderStyles} from './CardOrderStyles';
import {Title} from '../../../../../config/Contanst';
import PopupUpdateStatusOrder from '../component/PopupUpdateStatusOrder/PopupUpdateStatusOrder';

const CardOrder = ({
  item,
  index,
  action,
  handleUpdateStatusProcessOrder,
  handleViewDetailOrder,
}: {
  item: any;
  index: number;
  action?: string;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => void;
  handleViewDetailOrder: (item: any, refundInfo: any) => void;
}) => {
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const [refundInfo, setRefundInfo] = useState({
    all: 0,
    allRefund: 0,
    detail: [],
  });

  const dialogRef = useRef<any>(null);

  const handleStatusPress = () => {
    setShowStatusPopup(true);
  };

  const handleUpdate = async (itemWithCancelReason: any, status?: string) => {
    handleUpdateStatusProcessOrder(itemWithCancelReason, status);
    setShowStatusPopup(false);
  };

  const handleToggleProducts = () => {
    setShowAllProducts(!showAllProducts);
  };

  // Lấy danh sách sản phẩm để hiển thị
  const getDisplayProducts = () => {
    if (!item?.orderDetails || item.orderDetails.length === 0) return [];
    return showAllProducts ? item.orderDetails : [item.orderDetails[0]];
  };

  const getTotalRefund = () => {
    return !item?.Refund
      ? 0
      : item.orderDetails?.reduce(
          (total: number, orderDetail: any) =>
            total +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f0 ?? 0) +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f1 ?? 0) +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f2 ?? 0),
          0,
        );
  };

  useEffect(() => {
    console.log('check-item-757575757', item);
    let totalRefund = 0;
    let refundCount = 0;
    const refundedProducts = new Set();

    if (item?.orderDetails) {
      item.orderDetails.forEach((orderDetail: any) => {
        if (orderDetail.historyReward) {
          const productHasRefund = orderDetail.historyReward.some(
            (history: any) => history.Value > 0,
          );

          if (productHasRefund) {
            refundedProducts.add(orderDetail.Id);
            orderDetail.historyReward.forEach((history: any) => {
              totalRefund += history.Value || 0;
            });
          }
        }
      });
    }

    refundCount = refundedProducts.size;

    const firstOrderDetail = item?.orderDetails;
    let arrayDetail: any[] = [];
    firstOrderDetail?.forEach((orderDetail: any) => {
      arrayDetail.push({
        id: orderDetail.Id,
        refundCustomer: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 0,
        ),
        refundF1: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 1,
        ),
        refundF2: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 2,
        ),
      });
    });

    setRefundInfo({
      all: refundCount,
      allRefund: totalRefund,
      detail: arrayDetail as any,
    });
  }, [item]);

  return (
    <TouchableOpacity onPress={() => handleViewDetailOrder(item, refundInfo)}>
      <FDialog ref={dialogRef} />
      <View style={CardOrderStyles.container}>
        <View style={CardOrderStyles.header}>
          <Text
            style={{...CardOrderStyles.orderId, gap: 2, flexDirection: 'row'}}>
            {'Đơn hàng '}
            <Text style={{color: '#000', fontWeight: 'bold'}}>
              #{item?.Code}
            </Text>
          </Text>
          <Text
            style={
              item?.Status == 3
                ? CardOrderStyles.statusDone
                : item?.Status == 2 || item?.Status == 1
                  ? CardOrderStyles.statusProcessing
                  : CardOrderStyles.status
            }>
            {item?.Status == 1 && 'Chờ xác nhận'}
            {item?.Status == 2 && 'Đang thực hiện'}
            {item?.Status == 3 && 'Hoàn thành'}
            {item?.Status == 4 && 'Hủy'}
          </Text>
        </View>
        {/* Thông tin sản phẩm */}
        {getDisplayProducts().map((productItem: any, index: number) => (
          <View
            style={CardOrderStyles.productContainer}
            key={`${index}-${productItem?.Id}`}>
            <Image
              source={{uri: productItem?.productInfo?.Img || productItem?.Img}}
              style={CardOrderStyles.productImage}
            />
            <View style={CardOrderStyles.productInfo}>
              <Text style={CardOrderStyles.productName}>
                {productItem?.orderDetails?.Name ||
                  productItem?.ProductName ||
                  productItem?.Name}
              </Text>
              <Text style={CardOrderStyles.productPrice}>
                <Text style={CardOrderStyles.productName}>
                  Số lượng:{' '}
                  {productItem?.Quantity || productItem?.ProductQuantity || 1}
                </Text>
              </Text>
              <Text style={CardOrderStyles.productPrice}>
                <Text style={CardOrderStyles.productName}>Giá: </Text>
                <Text style={{color: ColorThemes.light.error_main_color}}>
                  {Ultis.money(
                    productItem?.ProductPrice ||
                      productItem?.Price ||
                      (productItem?.orderDetails?.Price ?? 0) *
                        (1 - (productItem?.orderDetails?.Discount ?? 0) / 100),
                  )}{' '}
                  VNĐ
                </Text>
              </Text>
            </View>
          </View>
        ))}

        {/* Số lượng và tổng tiền */}
        <View style={CardOrderStyles.quantityTotal}>
          {item?.orderDetails?.length > 1 && (
            <TouchableOpacity
              style={CardOrderStyles.quantityButton}
              onPress={handleToggleProducts}>
              <Text style={CardOrderStyles.quantityText}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {showAllProducts ? 'Thu gọn' : 'Xem thêm'}
                </Text>
                <Winicon
                  src={
                    showAllProducts
                      ? 'color/arrows/arrow-sm-up'
                      : 'color/arrows/arrow-sm-down'
                  }
                  size={13}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </Text>
            </TouchableOpacity>
          )}
        </View>
        {action && action == 'Xác nhận đơn' && (
          <View style={CardOrderStyles.button}>
            <View style={{flexDirection: 'row', gap: 10}}></View>
            <View style={{flexDirection: 'row', gap: 10}}>
              <TouchableOpacity
                style={CardOrderStyles.confirmButton}
                onPress={handleStatusPress}>
                <Text style={CardOrderStyles.confirmButtonText}>
                  Xác nhận đơn hàng
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        {action &&
          action !== Title.Cancel &&
          action !== 'Xác nhận đơn' &&
          item?.Status !== 3 && (
            <View style={CardOrderStyles.button}>
              <View style={{flexDirection: 'row', gap: 10}}></View>
              <View style={{flexDirection: 'row', gap: 10}}>
                <TouchableOpacity
                  style={CardOrderStyles.confirmButton}
                  onPress={handleStatusPress}>
                  <Text style={CardOrderStyles.confirmButtonText}>
                    Cập nhật trạng thái
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}>
        <PopupUpdateStatusOrder
          visible={showStatusPopup}
          onClose={() => setShowStatusPopup(false)}
          item={item}
          handleUpdateStatusProcessOrder={handleUpdate}
        />
      </KeyboardAvoidingView>
    </TouchableOpacity>
  );
};

export default CardOrder;
