import React, {forwardRef, useRef} from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../../../component/popup/popup';
import {RootScreen} from '../../../../../../../router/router';
import {FilePreviewPopup} from '../FilePreviewPopup';
import {useTicketData} from './hooks/useTicketData';
import {TicketHeader} from './components/TicketHeader';
import {TicketDetails} from './components/TicketDetails';
import {TicketType} from 'types/ticketType';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';

interface PopupViewTicketProps {
  item: TicketType;
  fileInfor: Array<any>;
  typeLabel: string;
  relativeData: any;
  showAction?: boolean;
  onClose: () => void;
  onEdit: () => void;
}

export const PopupViewTicket = forwardRef<any, PopupViewTicketProps>(
  function PopupViewTicket(data) {
    const popupRef = useRef<any>();
    const navigation = useNavigation<any>();
    const {
      item,
      typeLabel,
      relativeData,
      showAction = false,
      onClose,
      onEdit,
    } = data;
    const {customers, files} = useTicketData(item);

    const handleFilePress = (file: any) => {
      if (file.Url) {
        showPopup({
          ref: popupRef,
          enableDismiss: true,
          children: (
            <FilePreviewPopup
              file={file}
              onClose={() => closePopup(popupRef)}
            />
          ),
        });
      }
    };

    const handleRelativeDataPress = () => {
      if (relativeData) {
        closePopup(popupRef);
        navigation.push(RootScreen.detailProject, {
          item: {
            ToiletId: relativeData.ToiletId ?? relativeData.Id,
          },
        });
      }
    };

    return (
      <SafeAreaView style={styles.container}>
        <FPopup ref={popupRef} />
        <TicketHeader item={item} onClose={onClose} />
        <ScrollView contentContainerStyle={{paddingBottom: 60}}>
          <KeyboardAvoidingView>
            <TicketDetails
              item={item}
              typeLabel={typeLabel}
              relativeData={relativeData}
              handleRelativeDataPress={handleRelativeDataPress}
              fileInfor={files}
              onFilePress={handleFilePress}
              customers={customers}
            />
          </KeyboardAvoidingView>
        </ScrollView>
        {showAction && (
          <KeyboardAvoidingView style={styles.buttonContainer}>
            <TouchableOpacity style={styles.responseButton} onPress={onEdit}>
              <Text style={styles.responseButtonText}>Phản hồi</Text>
            </TouchableOpacity>
          </KeyboardAvoidingView>
        )}
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 75,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
    position: 'relative',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 30,
    width: '100%',
  },
  responseButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 10,
    marginHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  responseButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
});
