export interface SurveyTask {
  Id: string;
  Name: string;
  Sort: number;
  Address?: string;
  Executor: string;
  DateStart: number;
  DateEnd: number;
  Description: string;
  ToiletCount?: string;
  UserCount?: string;
  SanitaryToiletCount?: string;
  CleaningProcess?: number;
  SepticTankLocation?: string;
  Capacity?: string;
  TreatmentTechnology?: number;
  Kitchen?: number;
  SeparateDrainage?: number;
  PumpingFrequency?: number;
  RenewableEnergy?: number;
  EnergyDetails?: string;
  WaterReuse?: string;
  WasteClassification?: string;
  Status?: number;
  CustomerId?: string;
  DateCreated: number;
}
