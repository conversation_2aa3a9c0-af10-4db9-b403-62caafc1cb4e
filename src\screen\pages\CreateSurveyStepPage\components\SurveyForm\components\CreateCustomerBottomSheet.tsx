import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {useForm} from 'react-hook-form';
import {TypoSkin} from '../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../assets/skin/colors';
import {
  TextFieldForm,
  FAddressPickerForm,
} from '../../../../../../project-component/component-form';
import {
  showSnackbar,
  Winicon,
} from '../../../../../../component/export-component';
import {ComponentStatus} from '../../../../../../component/component-status';
import {validatePhoneNumber} from '../../../../../../utils/validate';
import {DataController} from '../../../../../base-controller';
import {CustomerActions} from '../../../../../../redux/reducers/user/reducer';
import {randomGID, regexPassWord} from '../../../../../../utils/Utils';
import {
  CustomerStatus,
  CustomerType,
} from '../../../../../../redux/reducers/user/da';
import {getDataToAsyncStorage} from '../../../../../../utils/AsyncStorage';
import FLoading from '../../../../../../component/Loading/FLoading';
import {CustomBottomSheet} from '../../../../../../project-component/form/DateRangePicker/CustomBottomSheet';

interface CreateCustomerData {
  Name?: string;
  Mobile?: string;
  Password?: string;
  ConfirmPassword?: string;
  Address?: string;
  Lat?: number;
  Long?: number;
  Email?: string;
}

interface CreateCustomerBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onCustomerCreated: (customer: any) => void;
}

export const CreateCustomerBottomSheet: React.FC<
  CreateCustomerBottomSheetProps
> = ({visible, onClose, onCustomerCreated}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [loading, setLoading] = useState(false);
  const customerController = new DataController('Customer');

  const {
    control,
    handleSubmit,
    formState: {errors},
    register,
    watch,
    setError,
    clearErrors,
    reset,
    setValue,
  } = useForm<CreateCustomerData>({
    defaultValues: {
      Name: '',
      Mobile: '',
      Password: '',
      ConfirmPassword: '',
      Address: '',
      Lat: 0,
      Long: 0,
      Email: '',
    },
    mode: 'onBlur',
  });

  const validateAllFields = () => {
    let isValid = true;
    const values = watch();

    // Validate Name
    if (!values.Name || values.Name?.trim().length === 0) {
      setError('Name', {message: 'Họ và tên không được để trống'});
      isValid = false;
    }

    // Validate Mobile
    if (!values.Mobile || values.Mobile?.trim().length === 0) {
      setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      isValid = false;
    } else {
      var mobile = values.Mobile?.trim() || '';
      if (!/^(\+84|0)/.test(mobile)) {
        mobile = '0' + mobile;
      }
      if (!validatePhoneNumber(mobile)) {
        setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        isValid = false;
      }
    }

    // Validate Password
    if (!values.Password || values.Password?.trim().length === 0) {
      setError('Password', {message: 'Mật khẩu không được để trống'});
      isValid = false;
    } else if (!regexPassWord.test(values.Password?.trim() || '')) {
      setError('Password', {message: 'Mật khẩu sai định dạng, hãy thử lại'});
      isValid = false;
    }

    // Validate ConfirmPassword
    if (
      !values.ConfirmPassword ||
      values.ConfirmPassword?.trim().length === 0
    ) {
      setError('ConfirmPassword', {message: 'Mật khẩu không được để trống'});
      isValid = false;
    } else if ((values.Password || '') !== (values.ConfirmPassword || '')) {
      setError('ConfirmPassword', {message: 'Mật khẩu nhập lại không đúng'});
      isValid = false;
    }

    // Validate Address
    if (!values.Address || values.Address?.trim().length === 0) {
      setError('Address', {message: 'Địa chỉ không được để trống'});
      isValid = false;
    }

    // Validate Email (optional but must be valid format if provided)
    if (values.Email && values.Email?.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(values.Email?.trim() || '')) {
        setError('Email', {message: 'Email không đúng định dạng'});
        isValid = false;
      }
    }

    return isValid;
  };

  const onSubmit = async (data: CreateCustomerData) => {
    console.log('onSubmit called with data:', data);

    if (loading) return;

    setLoading(true);

    try {
      let mobile = data.Mobile?.trim() || '';
      // Check if the number doesn't already start with 0 or +84
      if (!/^(\+84|0)/.test(mobile)) {
        mobile = '0' + mobile; // Add 0 at the beginning
      }

      if (!validatePhoneNumber(mobile)) {
        setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        setLoading(false);
        return;
      }

      // Check if phone number already exists
      const resCustomers = await customerController.aggregateList({
        page: 1,
        size: 1,
        searchRaw: `@Mobile:(${mobile})`,
      });

      if (resCustomers) {
        if (resCustomers?.data?.length > 0) {
          // Check if account is locked
          if (resCustomers?.data[0]?.Status == CustomerStatus.locked) {
            showSnackbar({
              message:
                'Số điện thoại này đã bị khóa, vui lòng liên hệ với quản trị viên.',
              status: ComponentStatus.ERROR,
            });
            setLoading(false);
            return;
          }
          showSnackbar({
            message: 'Số điện thoại đã được đăng ký trước đó',
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
          return;
        }
      } else {
        showSnackbar({
          message: 'Đã có lỗi xảy ra khi kiểm tra số điện thoại',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }

      // Hash password
      if (!data.Password)
        return showSnackbar({
          message: 'Mật khẩu không được để trống',
          status: ComponentStatus.ERROR,
        });
      const hashPass = await CustomerActions.hashPassword(data.Password.trim());
      if (hashPass.code != 200) {
        showSnackbar({
          message: hashPass.message,
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }

      // Get device token
      const deviceToken = await getDataToAsyncStorage('fcmToken');

      // Create new customer
      const newCustomer = {
        Id: randomGID(),
        Name: data.Name?.trim() || '',
        DateCreated: Date.now(),
        Mobile: mobile,
        Address: data.Address?.trim() || '',
        Lat: data.Lat || 0,
        Long: data.Long || 0,
        Email: data.Email?.trim() || '',
        Status: CustomerStatus.active,
        Type: CustomerType.guest,
        Password: hashPass.data,
        DeviceToken: deviceToken,
      };

      const customerRes = await customerController.add([newCustomer]);

      if (customerRes.code == 200) {
        showSnackbar({
          message: 'Tạo khách hàng mới thành công',
          status: ComponentStatus.SUCCSESS,
        });

        // Return the created customer to parent component
        onCustomerCreated(newCustomer);
        reset();
        onClose();
      } else {
        showSnackbar({
          message: customerRes.message ?? 'Đã có lỗi xảy ra khi tạo tài khoản.',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error creating customer:', error);
      showSnackbar({
        message: 'Đã có lỗi xảy ra khi tạo khách hàng',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleAddressChange = (addressData: any) => {
    console.log('Address data received:', addressData);
    if (!addressData.geometry || !addressData.geometry.location) return;
    const lat = addressData.geometry.location.lat;
    const long = addressData.geometry.location.lng;
    setValue('Lat', lat);
    setValue('Long', long);
    return addressData.formatted_address;
  };

  const handleConfirm = () => {
    // Force validation on all fields first
    const isValid = validateAllFields();

    if (isValid && !loading) {
      handleSubmit(onSubmit)();
    }
  };

  return (
    <>
      <FLoading visible={loading} />
      <CustomBottomSheet
        visible={visible}
        height="85%"
        onClose={handleClose}
        title="Tạo mới khách hàng"
        onCancel={handleClose}
        onConfirm={handleConfirm}
        cancelText="Hủy"
        confirmText="Tạo mới">
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
          style={styles.container}>
          <ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
            contentContainerStyle={styles.scrollContentContainer}>
            <View style={styles.formContainer}>
              <TextFieldForm
                control={control}
                name="Name"
                placeholder="Họ và tên"
                label="Họ và tên"
                required
                returnKeyType="next"
                errors={errors}
                textFieldStyle={styles.textFieldStyle}
                register={register}
                type="default"
                onBlur={(ev: string) => {
                  if (ev?.length !== 0) clearErrors('Name');
                  else
                    setError('Name', {
                      message: 'Họ và tên không được để trống',
                    });
                }}
              />

              <FAddressPickerForm
                control={control}
                name="Address"
                label="Địa chỉ"
                placeholder="Nhập địa chỉ của bạn"
                required={true}
                errors={errors}
                onChange={handleAddressChange}
              />

              <TextFieldForm
                control={control}
                name="Email"
                placeholder="Nhập email"
                label="Email"
                returnKeyType="next"
                errors={errors}
                textFieldStyle={styles.textFieldStyle}
                register={register}
                type="email-address"
                onBlur={(ev: string) => {
                  if (ev && ev.trim().length > 0) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (emailRegex.test(ev.trim())) {
                      clearErrors('Email');
                    } else {
                      setError('Email', {
                        message: 'Email không đúng định dạng',
                      });
                    }
                  } else {
                    clearErrors('Email'); // Email is optional
                  }
                }}
              />

              <TextFieldForm
                control={control}
                name="Mobile"
                required
                label="Số điện thoại"
                placeholder="Nhập số điện thoại"
                returnKeyType="next"
                errors={errors}
                register={register}
                prefix={
                  <View style={styles.prefixContainer}>
                    <Text style={styles.prefixText}>+84</Text>
                    <Winicon src="outline/arrows/down-arrow" size={16} />
                  </View>
                }
                type="number-pad"
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
                    return;
                  }
                  var mobile = ev.trim();
                  if (!/^(\+84|0)/.test(mobile)) {
                    mobile = '0' + mobile;
                  }
                  const val = validatePhoneNumber(mobile);
                  if (val) clearErrors('Mobile');
                  else
                    setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
                }}
              />

              <TextFieldForm
                control={control}
                name="Password"
                secureTextEntry={isVisiblePass}
                label="Mật khẩu"
                returnKeyType="next"
                placeholder="Tạo mật khẩu"
                suffix={
                  <TouchableOpacity
                    style={styles.suffixButton}
                    onPress={() => setVisiblePass(!isVisiblePass)}>
                    <Winicon
                      src={
                        isVisiblePass
                          ? 'outline/user interface/view'
                          : 'outline/user interface/hide'
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                errors={errors}
                textFieldStyle={styles.textFieldStyle}
                register={register}
                required
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    setError('Password', {
                      message: 'Mật khẩu không được để trống',
                    });
                    return;
                  }
                  var pass = ev.trim();
                  if (!regexPassWord.test(pass))
                    return setError('Password', {
                      message: 'Mật khẩu sai định dạng, hãy thử lại',
                    });
                  clearErrors('Password');
                }}
              />

              <Text
                style={[
                  errors.Password && {marginTop: 10},
                  styles.passwordHintText,
                ]}>
                {`- Tối thiểu 8 ký tự/ Tối đa 16 ký tự \n- Gồm chữ hoa, thường và số`}
              </Text>

              <TextFieldForm
                control={control}
                name="ConfirmPassword"
                label="Nhập lại mật khẩu"
                returnKeyType="done"
                secureTextEntry={isVisiblePass2}
                required
                suffix={
                  <TouchableOpacity
                    style={styles.suffixButton}
                    onPress={() => setVisiblePass2(!isVisiblePass2)}>
                    <Winicon
                      src={
                        isVisiblePass2
                          ? 'outline/user interface/view'
                          : 'outline/user interface/hide'
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                placeholder="Nhập lại mật khẩu"
                errors={errors}
                textFieldStyle={styles.textFieldStyle}
                register={register}
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    setError('ConfirmPassword', {
                      message: 'Mật khẩu không được để trống',
                    });
                    return;
                  }
                  var rePass = ev.trim();
                  if (watch('Password') !== rePass)
                    return setError('ConfirmPassword', {
                      message: 'Mật khẩu nhập lại không đúng',
                    });
                  clearErrors('ConfirmPassword');
                }}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </CustomBottomSheet>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: 40, // Thêm padding để tránh bị che bởi keyboard
  },
  formContainer: {
    gap: 16,
    paddingBottom: 20,
  },
  textFieldStyle: {
    height: 48,
    paddingLeft: 16,
    backgroundColor: ColorThemes.light.transparent,
  },
  prefixContainer: {
    flexDirection: 'row',
    height: 46,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
  },
  prefixText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  suffixButton: {
    padding: 12,
  },
  passwordHintText: {
    ...TypoSkin.subtitle4,
    alignSelf: 'baseline',
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 8,
  },
});

export default CreateCustomerBottomSheet;
