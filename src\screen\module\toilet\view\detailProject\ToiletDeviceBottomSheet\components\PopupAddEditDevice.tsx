import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  FDialog,
  Winicon,
  showSnackbar,
} from '../../../../../../../component/export-component';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import ScreenHeader from '../../../../../../layout/header';
import {randomGID, Ultis} from '../../../../../../../utils/Utils';
import {useForm} from 'react-hook-form';
import {DeviceBioStatus} from '../../../../../service/components/da';
import {DataController} from '../../../../../../base-controller';
import {useSelectorCustomerState} from '../../../../../../../redux/hooks/hooks';
import ImageCropPicker from 'react-native-image-crop-picker';
import {SkeletonImage} from '../../../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../../../config/configApi';
import ListTile from '../../../../../../../component/list-tile/list-tile';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {TypoSkin} from '../../../../../../../assets/skin/typography';
import DatePicker from 'react-native-date-picker';
import AppButton from '../../../../../../../component/button';
import {BaseDA} from '../../../../../../baseDA';
import {CustomerType} from '../../../../../../../redux/reducers/user/da';
import {TextFieldForm} from '../../../../../../../project-component/component-form';
import {closePopup} from '../../../../../../../component/popup/popup';

interface Props {
  toiletId: any;
  id: any;
  onSubmit: () => void;
  formId: any;
}

export const PopupAddEditDevice = forwardRef(function PopupAddEditDevice(
  data: Props,
  ref: any,
) {
  const {toiletId, id, onSubmit, formId} = data;
  const dialogRef = useRef<any>();
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      ToiletId: toiletId,
      Status: formId ? DeviceBioStatus.inactive : DeviceBioStatus.active,
      DateStart: undefined,
      DateEnd: undefined,
      Quantity: 1,
      Price: undefined,
      Discount: undefined,
      Vat: undefined,
    },
  });

  const [openPurchaseDate, setOpenPurchaseDate] = useState(false);
  const [openWarrantyDate, setOpenWarrantyDate] = useState(false);
  const controller = new DataController('Device');
  const popupRef = useRef<any>();
  const [img, setImg] = useState<Array<any>>([]);
  const user = useSelectorCustomerState().data;

  useEffect(() => {
    let isMounted = true;
    if (id) {
      controller.getById(id).then(async res => {
        if (!isMounted) return;
        if (res.code === 200) {
          if (res.data.ProductId) {
            const productController = new DataController('Product');
            const product = await productController.getById(res.data.ProductId);
            if (product.code === 200) {
              res.data.Img = product.data.Img;
              res.data.Description = product.data.Specifications;
              res.data.Unit = product.data.Unit;
            }
          }
          Object.keys(res.data).forEach(key => {
            if (key === 'DateStart' || key === 'DateEnd') {
              methods.setValue(
                key,
                res.data[key]
                  ? `${Ultis.datetoString(new Date(res.data[key]))}`
                  : undefined,
              );
            } else if (key === 'Price') {
              methods.setValue(
                key,
                res.data[key] ? Ultis.money(res.data[key]) : undefined,
              );
            } else {
              methods.setValue(key, `${res.data[key] ?? ''}`);
            }
          });
          const fileIds = (res.data.Img ?? '')
            .split(',')
            .map((s: string) => s.trim())
            .filter((s: string) => s.length > 0);
          if (fileIds.length) {
            BaseDA.getFilesInfor(fileIds).then(resFile => {
              if (resFile.code === 200)
                setImg([
                  ...resFile.data.filter(
                    (e: any) => e !== undefined && e !== null,
                  ),
                ]);
            });
          }
        }
      });
    }
    return () => {
      isMounted = false;
    };
  }, [id]);

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 1,
    });
    if (image) {
      _uploadFiles({
        name: image.filename ?? 'new file img',
        type: image.mime,
        uri: image.path,
      });
    }
  };

  const _uploadFiles = async (file: {
    uri: string;
    type: string;
    name: string;
  }) => {
    const res = await BaseDA.uploadFiles([file]);
    if (res?.length) {
      setImg([res[0]]);
      methods.setValue('Img', res.map((e: any) => e.Id).join(','));
    }
  };

  const _onSubmit = async (ev: any) => {
    const item = {
      ...ev,
      Img: methods.watch('Img') ? methods.watch('Img') : undefined,
      Quantity: methods.getValues('Quantity')
        ? parseInt(methods.getValues('Quantity'))
        : undefined,
      Vat: methods.getValues('Vat')
        ? parseInt(methods.getValues('Vat'))
        : undefined,
      Discount: methods.getValues('Discount')
        ? parseInt(methods.getValues('Discount'))
        : undefined,
      Price: methods.getValues('Price')
        ? parseInt(methods.getValues('Price').replace(/,/g, ''))
        : undefined,
      DateCreated: Date.now(),
      DateStart: methods.getValues('DateStart')
        ? Ultis.stringToDate(methods.getValues('DateStart')).getTime()
        : undefined,
      DateEnd: methods.getValues('DateEnd')
        ? Ultis.stringToDate(methods.getValues('DateEnd')).getTime()
        : undefined,
    };

    if (id) {
      controller.edit([item]).then(() => {
        onSubmit();
        closePopup(ref);
      });
    } else {
      const quantityValue = Number(ev.Quantity ?? 1);
      const quantity =
        Number.isFinite(quantityValue) && quantityValue > 0 ? quantityValue : 1;
      const tmp: any[] = [];
      for (let i = 0; i < quantity; i++) {
        tmp.push({...item, Id: i ? randomGID() : ev.Id, Quantity: 1});
      }
      controller.add(tmp).then(() => {
        onSubmit();
        closePopup(ref);
      });
    }
  };

  const _onError = (ev: any) => {
    showSnackbar({
      message: 'Vui lòng kiểm tra lại dữ liệu',
      status: 'ERROR' as any,
    });
    console.log(ev);
  };

  return (
    <SafeAreaView style={styles.container}>
      <FDialog ref={dialogRef} />
      {/* Popup container controlled by parent; no local FPopup needed */}
      <ScreenHeader
        style={styles.header}
        title={id ? `Chỉnh sửa thiết bị` : 'Thêm mới thiết bị'}
        prefix={<View />}
        action={
          <View style={styles.headerAction}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => closePopup(ref)}
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </View>
        }
      />
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 75 : 0}
        style={styles.keyboardContainer}>
        <ScrollView>
          <View style={styles.contentContainer}>
            <View style={styles.sectionGap}>
              <TextFieldForm
                required
                label="Tên"
                textFieldStyle={styles.textFieldInner}
                style={styles.fullWidth}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Name"
              />

              <View style={styles.imageFieldContainer}>
                <Text numberOfLines={1} style={TypoSkin.label3}>
                  Ảnh
                </Text>
                {methods.watch('Img') && img?.length > 0 ? null : (
                  <TouchableOpacity
                    onPress={pickerImg}
                    style={styles.imagePlaceholder}>
                    <SkeletonImage
                      source={{
                        uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                      }}
                      style={styles.imagePlaceholderIcon}
                    />
                    <Text numberOfLines={1} style={styles.imagePlaceholderText}>
                      Thêm ảnh
                    </Text>
                  </TouchableOpacity>
                )}
                {methods.watch('Img') &&
                  img?.map((fileItem: any, index: number) => (
                    <ListTile
                      key={`${index}`}
                      leading={
                        <SkeletonImage
                          source={{uri: ConfigAPI.imgUrlId + fileItem.Id}}
                          style={styles.previewImage}
                        />
                      }
                      title={fileItem?.Name ?? `Ảnh ${index + 1}`}
                      titleStyle={[
                        TypoSkin.heading7,
                        {color: ColorThemes.light.neutral_text_title_color},
                      ]}
                      subtitle={`${Math.round(fileItem.Size / (1024 * 1024))}MB`}
                      listtileStyle={{gap: 16}}
                      style={styles.previewItem}
                      trailing={
                        <TouchableOpacity
                          onPress={async () => {
                            methods.setValue('Img', undefined);
                            setImg([]);
                          }}
                          style={styles.removeIconTouchable}>
                          <FontAwesomeIcon
                            icon={faMinusCircle}
                            size={20}
                            color="#D72525FF"
                            style={styles.removeIcon}
                          />
                        </TouchableOpacity>
                      }
                    />
                  ))}
              </View>

              <TextFieldForm
                required
                label="Đơn vị"
                textFieldStyle={styles.textFieldInner}
                style={styles.fullWidth}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Unit"
              />
              <TextFieldForm
                required
                label="Số lượng"
                textFieldStyle={styles.textFieldInner}
                style={styles.fullWidth}
                type="number-pad"
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Quantity"
              />
            </View>

            {user?.Type === CustomerType.partner ? (
              <View style={styles.sectionGap}>
                <Text numberOfLines={1} style={TypoSkin.label2}>
                  Thông tin bổ sung
                </Text>
                <TextFieldForm
                  required
                  label="Giá"
                  textFieldStyle={styles.textFieldInner}
                  style={styles.fullWidth}
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Price"
                  type="money"
                  returnKeyType="done"
                  onBlur={(value: any) => {
                    if (!value) return;
                    let newPrice = parseInt(String(value).replaceAll(',', ''));
                    if (!isNaN(newPrice)) {
                      value = Ultis.money(newPrice);
                    } else value = Ultis.money(methods.getValues('Price'));
                  }}
                />
                <TextFieldForm
                  label="Code"
                  textFieldStyle={styles.textFieldInner}
                  style={styles.fullWidth}
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Code"
                />
                <TextFieldForm
                  label="Vat"
                  textFieldStyle={styles.textFieldInner}
                  style={styles.fullWidth}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Vat"
                />
                <TextFieldForm
                  label="Discount"
                  textFieldStyle={styles.textFieldInner}
                  style={styles.fullWidth}
                  type="number-pad"
                  register={methods.register}
                  control={methods.control}
                  errors={methods.formState.errors}
                  name="Discount"
                />

                <View style={styles.rowGap16}>
                  <View style={styles.dateFieldContainer}>
                    <Text style={styles.dateFieldLabel}>Ngày mua</Text>
                    <TouchableOpacity
                      onPress={() => setOpenPurchaseDate(true)}
                      style={styles.dateTouchable}>
                      <Text style={styles.dateText}>
                        {methods.watch('DateStart')}
                      </Text>
                    </TouchableOpacity>
                    <DatePicker
                      modal
                      open={openPurchaseDate}
                      date={
                        methods.getValues('DateStart')
                          ? Ultis.stringToDate(methods.getValues('DateStart'))
                          : new Date()
                      }
                      title={'Ngày mua'}
                      mode="date"
                      locale="vi"
                      dividerColor={'#f2f5f8'}
                      maximumDate={
                        methods.watch('DateEnd')
                          ? Ultis.stringToDate(methods.watch('DateEnd'))
                          : undefined
                      }
                      confirmText="Xác nhận"
                      theme="light"
                      cancelText="Hủy"
                      onConfirm={date => {
                        setOpenPurchaseDate(false);
                        methods.setValue(
                          'DateStart',
                          `${Ultis.datetoString(date)}`,
                        );
                      }}
                      onCancel={() => {
                        setOpenPurchaseDate(false);
                      }}
                    />
                  </View>
                  <View style={styles.dateFieldContainer}>
                    <Text style={styles.dateFieldLabel}>
                      Ngày hết hạn bảo hành
                    </Text>
                    <TouchableOpacity
                      onPress={() => setOpenWarrantyDate(true)}
                      style={styles.dateTouchable}>
                      <Text style={styles.dateText}>
                        {methods.watch('DateEnd')}
                      </Text>
                    </TouchableOpacity>
                    <DatePicker
                      modal
                      open={openWarrantyDate}
                      mode="date"
                      locale="vi"
                      date={
                        methods.getValues('DateEnd')
                          ? Ultis.stringToDate(methods.getValues('DateEnd'))
                          : new Date()
                      }
                      title={'Ngày hết hạn bảo hành'}
                      confirmText="Xác nhận"
                      cancelText="Hủy"
                      theme="light"
                      minimumDate={
                        methods.watch('DateStart')
                          ? Ultis.stringToDate(methods.watch('DateStart'))
                          : undefined
                      }
                      dividerColor={'#f2f5f8'}
                      onConfirm={date => {
                        setOpenWarrantyDate(false);
                        methods.setValue(
                          'DateEnd',
                          `${Ultis.datetoString(date)}`,
                        );
                      }}
                      onCancel={() => {
                        setOpenWarrantyDate(false);
                      }}
                    />
                  </View>
                </View>
                <TextFieldForm
                  control={methods.control}
                  name="Description"
                  label="Mô tả"
                  errors={methods.formState.errors}
                  placeholder={'Mô tả khác'}
                  style={styles.descriptionContainer}
                  textFieldStyle={styles.descriptionInner}
                  textStyle={{textAlignVertical: 'top'}}
                  numberOfLines={10}
                  multiline={true}
                  register={methods.register}
                />
              </View>
            ) : null}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles.footer}>
        <AppButton
          title={id ? 'Sửa' : 'Thêm mới'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={styles.footerBtnContainer}
          onPress={() => {
            methods.handleSubmit(_onSubmit, _onError)();
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </View>
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 65,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  headerAction: {flexDirection: 'row', padding: 12, alignItems: 'center'},
  keyboardContainer: {height: '100%', width: '100%', paddingHorizontal: 16},
  contentContainer: {gap: 18, paddingBottom: 156},
  sectionGap: {flex: 1, gap: 18},
  textFieldInner: {padding: 16},
  fullWidth: {width: '100%'},
  imageFieldContainer: {gap: 8},
  imagePlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 0.4,
    borderColor: ColorThemes.light.neutral_main_border,
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 8,
  },
  imagePlaceholderIcon: {width: 35, height: 35, objectFit: 'cover' as any},
  imagePlaceholderText: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  previewImage: {width: 65, height: 65, objectFit: 'cover' as any},
  previewItem: {
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
    padding: 8,
  },
  removeIconTouchable: {padding: 4},
  removeIcon: {backgroundColor: '#fff', borderRadius: 20},
  rowGap16: {flexDirection: 'row', gap: 16},
  dateFieldContainer: {flex: 1, height: 65, gap: 8},
  dateFieldLabel: {
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
  },
  dateTouchable: {
    padding: 8,
    alignItems: 'flex-start',
    justifyContent: 'center',
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    width: '100%',
    flex: 1,
    borderWidth: 1,
  },
  dateText: {color: ColorThemes.light.neutral_text_title_color},
  descriptionContainer: {backgroundColor: ColorThemes.light.transparent},
  descriptionInner: {
    height: 100,
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    justifyContent: 'flex-start',
    backgroundColor: ColorThemes.light.transparent,
  },
  footer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  footerBtnContainer: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
  },
});
