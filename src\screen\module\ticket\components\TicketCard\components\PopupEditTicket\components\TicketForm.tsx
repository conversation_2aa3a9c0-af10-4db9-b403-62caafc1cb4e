import React from 'react';
import {StyleSheet} from 'react-native';
import {TextFieldForm} from '../../../../../../../../project-component/component-form';
import {ColorThemes} from '../../../../../../../../assets/skin/colors';

interface TicketFormProps {
  methods: any;
  editable: boolean;
}

export const TicketForm: React.FC<TicketFormProps> = ({methods, editable}) => {
  return (
    <TextFieldForm
      control={methods.control}
      name="Content"
      disabled={!editable}
      errors={methods.formState.errors}
      placeholder={'Nhập chi tiết xử lý'}
      style={styles.textFieldContainer}
      textFieldStyle={styles.textField}
      textStyle={styles.textFieldText}
      numberOfLines={10}
      multiline={true}
      register={methods.register}
    />
  );
};

const styles = StyleSheet.create({
  textFieldContainer: {
    backgroundColor: ColorThemes.light.transparent,
  },
  textField: {
    height: 100,
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 100,
    justifyContent: 'flex-start',
    backgroundColor: ColorThemes.light.transparent,
  },
  textFieldText: {
    textAlignVertical: 'top',
  },
});
