import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Checkbox} from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import {CartItem as CartItemType} from '../../../../types/cartTypes';
import {useCartActions} from '../../../../redux/hooks/cartHook';
import ConfigAPI from '../../../../config/configApi';
import CartItem from './CartItem';

interface StoreGroupData {
  ShopId: string;
  ShopName: string;
  ShopAvatar: string;
  items: CartItemType[];
}

interface StoreGroupProps {
  storeGroup: StoreGroupData;
  dialogRef: React.RefObject<any>;
}

const StoreGroup: React.FC<StoreGroupProps> = ({storeGroup, dialogRef}) => {
  const cartActions = useCartActions();

  const allSelected = storeGroup?.items?.every((item: any) => item.selected);

  const handleToggleStoreSelection = (value: boolean) => {
    cartActions.toggleStoreSelection(storeGroup.ShopId, value);
  };

  const getStoreAvatarUri = () => {
    return storeGroup.ShopAvatar?.startsWith('http')
      ? storeGroup.ShopAvatar
      : `${ConfigAPI.imgUrlId}${storeGroup.ShopAvatar}`;
  };

  const renderStoreHeader = () => {
    return (
      <View style={styles.storeHeader}>
        <View style={styles.storeInfo}>
          <FastImage
            style={styles.storeAvatar}
            source={{
              uri: getStoreAvatarUri(),
              priority: FastImage.priority.normal,
            }}
            resizeMode={FastImage.resizeMode.cover}
          />
          <Text style={styles.storeName}>{storeGroup.ShopName}</Text>
        </View>
        {storeGroup.ShopId && (
          <View style={styles.storeCheckbox}>
            <Checkbox
              value={allSelected}
              onChange={handleToggleStoreSelection}
            />
          </View>
        )}
      </View>
    );
  };

  const renderStoreItems = () => {
    return storeGroup.items.map((item: CartItemType) => (
      <CartItem key={item.id} item={item} dialogRef={dialogRef} />
    ));
  };

  return (
    <View style={styles.storeContainer}>
      {renderStoreHeader()}
      {renderStoreItems()}
    </View>
  );
};

const styles = StyleSheet.create({
  storeContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginVertical: 8,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  storeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storeCheckbox: {
    marginLeft: 12,
  },
  storeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storeName: {
    fontSize: 18,
    color: '#000000',
    fontWeight: '700',
  },
});

export default StoreGroup;
