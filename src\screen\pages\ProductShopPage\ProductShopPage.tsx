import {useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {useSelectorCustomerState} from '../../../redux/hooks/hooks';
import {ProductItem} from '../../../types/ProductType';
import {navigate, RootScreen} from '../../../router/router';
import {CartActions} from '../../../redux/reducers/cart/CartReducer';
import {showSnackbar} from '../../../component/export-component';
import {ComponentStatus} from 'wini-mobile-components';
import {productAction} from '../../../redux/actions/productAction';
import TitleHeader from '../../layout/headers/TitleHeader';
import {ColorThemes} from '../../../assets/skin/colors';
import ProductBestSellerCard from '../../module/product/component/card/ProductBestSellerCard';

const width = Dimensions.get('window').width;

const PAGE_SIZE = 10;

const ProductShopPage = () => {
  const dispatch = useDispatch();
  const customerHook = useSelectorCustomerState();
  const {id: shopId} = useRoute().params as {id: string};
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const customerId = customerHook.data?.Id;

  useEffect(() => {
    loadProducts(1);
  }, [shopId]);

  const handleProductPress = (item: ProductItem) => {
    navigate(RootScreen.DetailProductPage, {id: item.Id});
  };

  const handleAddToCart = (item: ProductItem) => {
    CartActions.addItemToCart(item, 1)(dispatch);
    showSnackbar({
      message: 'Đã thêm sản phẩm vào giỏ hàng',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleFavoritePress = (item: ProductItem) => {
    // dispatch(
    //   updateFavoriteProduct({...item, IsFavorite: !item.IsFavorite}) as any,
    // );
  };

  const loadProducts = async (currentPage: number, isLoadMore = false) => {
    if (loading || (isLoadMore && !hasMore)) {
      return;
    }

    setLoading(true);
    try {
      const newProducts = await productAction.find(
        {
          page: currentPage,
          size: PAGE_SIZE,
          searchRaw: `@ShopId:{${shopId}}`,
        },
        customerId,
      );

      if (newProducts && newProducts.length > 0) {
        setProducts(prev =>
          isLoadMore ? [...prev, ...newProducts] : newProducts,
        );
        setPage(currentPage);
        if (newProducts.length < PAGE_SIZE) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadProducts(page + 1, true);
    }
  };

  const renderFooter = () => {
    if (!loading || page === 1) {
      return null;
    }
    return <ActivityIndicator style={styles.loader} size="large" />;
  };

  const renderEmpty = () => {
    if (loading) {
      return (
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      );
    }
    return (
      <View style={styles.centered}>
        <Text>Chưa có sản phẩm nào.</Text>
      </View>
    );
  };

  return (
    <View style={styles.screen}>
      <TitleHeader title={'Sản phẩm'} />
      <FlatList
        data={products}
        renderItem={({item}) => (
          <ProductBestSellerCard
            item={item}
            onPress={handleProductPress}
            onAddToCart={handleAddToCart}
            onFavoritePress={handleFavoritePress}
            width={(width - 48) / 2}
            height={((width - 48) / 2) * 1.8}
          />
        )}
        keyExtractor={item => item.Id}
        numColumns={2}
        contentContainerStyle={styles.container}
        columnWrapperStyle={styles.row}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  row: {
    justifyContent: 'space-between',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '50%',
  },
  loader: {
    marginVertical: 20,
  },
});

export default ProductShopPage;
