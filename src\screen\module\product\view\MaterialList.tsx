import {FlatList, RefreshControl, View, SafeAreaView} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import {DataController} from 'screen/base-controller';
import {ColorThemes} from 'assets/skin/colors';
import {CardToiletHoriSkeleton} from 'project-component/skeletonCard';
import EmptyPage from 'project-component/empty-page';
import {FPopup} from 'component/popup/popup';
import MaterialCard from '../component/card/MaterialCard';
import TitleHeader from 'screen/layout/headers/TitleHeader';

export default function MaterialList() {
  const [managerData, setManagerData] = useState({
    data: [],
    totalCount: undefined,
  });
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const popupRef = useRef<any>();
  const [isLoading, setLoading] = useState(false);
  const [isRefreshing, setRefreshing] = useState(false);
  const [listCate, setListCate] = useState({data: [], totalCount: undefined});

  const getData = async ({page, size}: any) => {
    setLoading(true);
    const controller = new DataController('Material');
    let _query = [];
    if (userRole || user?.CompanyProfileId)
      _query.push(
        `(@CompanyProfileId:{${userRole?.CompanyProfileId}}) | (@CustomerId:{${user?.Id}}) | (@IsPublic:{true})`,
      );
    else _query.push(`(@CustomerId:{${user?.Id}}) | (@IsPublic:{true})`);

    let bodyJson = {
      page: page ?? 1,
      size: size ?? 1000,
      searchRaw: _query.length ? _query.join(' ') : '*',
    };
    const res = await controller.aggregateList(bodyJson);
    //
    if (res.code === 200) {
      setManagerData({data: res.data, totalCount: res.totalCount});
      setLoading(false);
    } else {
      showSnackbar({message: res.message, status: ComponentStatus.ERROR});
      setLoading(false);
    }
  };

  const getOthers = async () => {
    // phan loai
    const cateController = new DataController('Category');
    const res = await cateController.getListSimple({
      page: 1,
      size: 1000,
      returns: ['Id', 'Name'],
    });
    if (res.code === 200)
      setListCate({data: res.data, totalCount: res.totalCount});
  };

  useEffect(() => {
    if (user) getData({});
    getOthers();
  }, [user]);

  const [searchValue, setSearchValue] = useState('');

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <View style={{flex: 1, height: '100%', width: '100%'}}>
        <FPopup ref={popupRef} />
        <TitleHeader
          title="Quản lý vật tư"
          showSearch
          onSearch={setSearchValue}
        />
        <FlatList
          data={
            searchValue.length
              ? managerData.data.filter((e: any) =>
                  e.Name.toLowerCase().includes(searchValue.toLowerCase()),
                )
              : managerData.data
          }
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={() => {
                setRefreshing(true);
                getData({page: 1, size: 10}).then(() => {
                  setLoading(false);
                  setRefreshing(false);
                });
              }}
            />
          }
          style={{flex: 1, gap: 8, marginHorizontal: 16, marginVertical: 16}}
          ItemSeparatorComponent={() => <View style={{height: 16}} />}
          renderItem={({item, index}: any) => (
            <MaterialCard
              key={index}
              index={index}
              item={item}
              user={user}
              listCate={listCate}
              setManagerData={setManagerData}
            />
          )}
          ListEmptyComponent={() =>
            isLoading ? (
              Array.from(Array(10)).map((_, index) => (
                <View key={index} style={{gap: 16}}>
                  <CardToiletHoriSkeleton />
                </View>
              ))
            ) : (
              <EmptyPage />
            )
          }
          ListFooterComponent={() => <View style={{height: 100}} />}
        />
      </View>
    </SafeAreaView>
  );
}
