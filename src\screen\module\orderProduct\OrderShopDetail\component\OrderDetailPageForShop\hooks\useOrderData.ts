import {useState, useEffect, useCallback} from 'react';
import {OrderProductDA} from '../../../../orderProductDA';

interface OrderDetailRouteParams {
  orderId: string;
  type?: string;
  CancelReason?: string;
  refundInfo?: any;
}

export const useOrderData = (orderId: string) => {
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<any>(null);
  const [orderDetails, setOrderDetails] = useState<Array<any>>([]);
  const [refreshing, setRefreshing] = useState(false);
  const orderDA = new OrderProductDA();

  const fetchOrderData = useCallback(async () => {
    if (!orderId) {
      setLoading(false);
      return;
    }
    
    try {
      // Lấy thông tin đơn hàng
      const orderResponse = await orderDA.getOrderByOrderId(orderId);
      if (orderResponse?.code === 200 && orderResponse.data?.length > 0) {
        orderResponse.data[0].Address = orderResponse.Address.find(
          (item: any) => item.Id === orderResponse.data[0].AddressId,
        );

        setOrder(orderResponse.data[0]);
        
        // Lấy chi tiết đơn hàng
        const detailsResponse = await orderDA.getOrderDetailsByOrderId(orderId);
        if (detailsResponse?.code === 200) {
          const listOrderDetailId = detailsResponse.data.map(
            (item: any) => item.Id,
          );
          const historyRewardResponse =
            await orderDA.getMoneyDetailsByListOrderDetailId(listOrderDetailId);
          
          detailsResponse.data = detailsResponse.data.map((item: any) => {
            var product = detailsResponse.Product.find(
              (product: any) => product.Id === item.ProductId,
            );
            item.Img = product?.Img;
            item.Name = product?.Name;
            item.CategoryId = product?.CategoryId;
            item.Reward = historyRewardResponse?.data.find(
              (history: any) =>
                history.OrderDetailId === item.Id && history.Filial === 0,
            )?.Value;
            return item;
          });
          setOrderDetails(detailsResponse.data);
        }
      }
    } catch (error) {
      console.error('Error fetching order data:', error);
    } finally {
      setLoading(false);
    }
  }, [orderId]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchOrderData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [fetchOrderData]);

  useEffect(() => {
    fetchOrderData();
  }, [fetchOrderData]);

  return {
    loading,
    order,
    orderDetails,
    refreshing,
    fetchOrderData,
    handleRefresh,
  };
};
