import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import ProgressSteps from './ProgressSteps';
import {ColorThemes} from '../../../../../../assets/skin/colors';

/**
 * Example component demonstrating different configurations of ProgressSteps
 * This is for testing and demonstration purposes only
 */
export default function ProgressStepsExample() {
  const [currentStep3, setCurrentStep3] = useState(1);
  const [currentStep5, setCurrentStep5] = useState(1);
  const [currentStep7, setCurrentStep7] = useState(1);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>ProgressSteps Component Examples</Text>

      {/* Default 3 steps (backward compatible) */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Default 3 Steps</Text>
        <ProgressSteps step={currentStep3} />
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCurrentStep3(Math.max(1, currentStep3 - 1))}>
            <Text style={styles.buttonText}>Previous</Text>
          </TouchableOpacity>
          <Text style={styles.stepText}>Step {currentStep3}/3</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCurrentStep3(Math.min(3, currentStep3 + 1))}>
            <Text style={styles.buttonText}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Custom 5 steps */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom 5 Steps</Text>
        <ProgressSteps step={currentStep5} totalSteps={5} />
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCurrentStep5(Math.max(1, currentStep5 - 1))}>
            <Text style={styles.buttonText}>Previous</Text>
          </TouchableOpacity>
          <Text style={styles.stepText}>Step {currentStep5}/5</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCurrentStep5(Math.min(5, currentStep5 + 1))}>
            <Text style={styles.buttonText}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Custom 7 steps with custom colors */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom 7 Steps with Custom Colors</Text>
        <ProgressSteps
          step={currentStep7}
          totalSteps={7}
          activeColor="#FF6B35"
          inactiveColor="#F0F0F0"
        />
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCurrentStep7(Math.max(1, currentStep7 - 1))}>
            <Text style={styles.buttonText}>Previous</Text>
          </TouchableOpacity>
          <Text style={styles.stepText}>Step {currentStep7}/7</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCurrentStep7(Math.min(7, currentStep7 + 1))}>
            <Text style={styles.buttonText}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: ColorThemes.light.white,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: ColorThemes.light.text_primary_color,
  },
  section: {
    marginBottom: 40,
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: ColorThemes.light.text_primary_color,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 15,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  buttonText: {
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
  stepText: {
    fontSize: 16,
    fontWeight: '500',
    color: ColorThemes.light.text_primary_color,
  },
});
