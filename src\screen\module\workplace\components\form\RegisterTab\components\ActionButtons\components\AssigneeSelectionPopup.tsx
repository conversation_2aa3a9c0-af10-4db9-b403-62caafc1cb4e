import React from 'react';
import {View, Text, Dimensions, StyleSheet} from 'react-native';
import {ColorThemes} from 'assets/skin/colors';
import {TypoSkin} from 'assets/skin/typography';
import ConfigAPI from 'config/configApi';
import {Fselect1Form} from 'project-component/component-form';
import {closePopup} from 'component/popup/popup';
import {Winicon} from 'component/export-component';
import {SkeletonImage} from 'project-component/skeleton-img';
import AppButton from 'component/button';
import {CustomerDa} from 'screen/module/customer/customerDa';
import {
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from 'redux/hooks/hooks';
import ScreenHeader from 'screen/layout/header';

interface AssigneeSelectionPopupProps {
  methods: any;
  popupRef: any;
  onAcceptOrder: () => void;
}

export const AssigneeSelectionPopup: React.FC<AssigneeSelectionPopupProps> = ({
  methods,
  popupRef,
  onAcceptOrder,
}) => {
  const [assignees, setAssignees] = React.useState<any[]>([]);
  const company = useSelectorCustomerCompanyState().data;
  const customer = useSelectorCustomerState().data;
  React.useEffect(() => {
    if (customer) methods.setValue('AssigneeId', customer.Id);
  }, [customer, methods]);

  React.useEffect(() => {
    const fetchAssignees = async () => {
      try {
        if (!company?.Id) {
          setAssignees([]);
          return;
        }
        const companyId = company?.Id;
        if (!companyId) {
          setAssignees([]);
          return;
        }
        const da = new CustomerDa();
        const employees = await da.getAllCustomerInCompany(companyId);
        setAssignees(Array.isArray(employees) ? employees : []);
      } catch (e) {
        setAssignees([]);
      }
    };
    fetchAssignees();
  }, [company]);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <ScreenHeader
          style={styles.header}
          title={`Chọn tư vấn viên`}
          prefix={<View />}
          action={
            <View style={styles.closeContainer}>
              <Winicon
                src="outline/layout/xmark"
                onClick={() => closePopup(popupRef)}
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </View>
          }
        />
        <Text style={styles.description}>
          Đơn hàng sẽ được chuyển sang trạng thái khảo sát và tư vấn.
        </Text>
        <Text style={styles.selectText}>
          Chọn tư vấn viên tiếp nhận đơn hàng:
        </Text>
        <Fselect1Form
          control={methods.control}
          errors={methods.formState.errors}
          style={styles.select}
          name="AssigneeId"
          options={assignees.map((e: any) => ({
            id: e.Id,
            name: (
              <View style={styles.option}>
                {e?.Img ? (
                  <SkeletonImage
                    source={{
                      uri: e.Img.startsWith('https')
                        ? e.Img
                        : ConfigAPI.imgUrlId + e?.Img,
                    }}
                    style={styles.avatar}
                  />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Text style={styles.avatarText}>
                      {e?.Name?.substring(0, 1)}
                    </Text>
                  </View>
                )}
                <Text style={styles.name}>{e.Name}</Text>
              </View>
            ),
          }))}
        />
        <AppButton
          title={'Xác nhận'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={styles.confirmButton}
          onPress={onAcceptOrder}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.transparent,
    height: Dimensions.get('window').height,
    alignContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
  },
  content: {
    borderRadius: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    margin: 16,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeContainer: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  description: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  selectText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  select: {
    marginHorizontal: 16,
    paddingHorizontal: 4,
    height: 56,
  },
  option: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 50,
    objectFit: 'cover',
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ColorThemes.light.infor_main_color,
  },
  avatarText: {
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  name: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    marginRight: 8,
  },
  confirmButton: {
    height: 40,
    borderRadius: 8,
    marginHorizontal: 16,
    marginVertical: 16,
    paddingVertical: 5,
  },
});
