/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {StyleSheet, View, ActivityIndicator} from 'react-native';
import {useRoute} from '@react-navigation/native';
import {store} from 'redux/store/store';
import {Title} from 'config/Contanst';
import SearchBar from '../../partner/component/SearchBar';
import TitleHeader from 'screen/layout/headers/TitleHeader';
import OrderInfoHeader from '../components/OrderInfoHeader';
import ListOrderCard from './list/ListOrderCard';
import {
  useOrderActions,
  selectOrderInfo,
  selectOrderLoading,
} from 'redux/reducers/order/OrderReducer';
import {useSelector} from 'react-redux';

const OrderShopDetail = () => {
  const route = useRoute<any>();
  const [typeCard, setTypeCard] = useState<string>('');
  const [numberCardSearch, setNumberCardSearch] = useState<number>(0);
  const [numberCard, setNumberCard] = useState<number>(0);

  const [dataSearch, setDataSearch] = useState<string>('');
  const [dataSearchResult, setDataSearchResult] = useState<any[]>([]);

  // Sử dụng Redux
  const orderInfo = useSelector(selectOrderInfo);
  const isLoading = useSelector(selectOrderLoading);
  const orderActions = useOrderActions();
  const shopInfo = store.getState().partner.data;
  const shopId = shopInfo && shopInfo.length > 0 ? shopInfo[0].Id : null;
  // Tính tổng số đơn hàng từ Redux state
  useEffect(() => {
    if (shopId) {
      orderActions.fetchAllOrdersByShopId(shopId);
    }
  }, [shopId]);

  const handleSearch = (dataSearch: string, status: number) => {
    if (dataSearch && orderInfo) {
      let ordersData: any[] = [];
      // Lấy data theo status
      if (status === 1 && orderInfo?.NewOrder?.data) {
        ordersData = orderInfo.NewOrder.data;
      } else if (status === 2 && orderInfo?.ProcessOrder?.data) {
        ordersData = orderInfo.ProcessOrder.data;
      } else if (status === 3 && orderInfo?.DoneOrder?.data) {
        ordersData = orderInfo.DoneOrder.data;
      } else if (status === 4 && orderInfo?.CancelOrder?.data) {
        ordersData = orderInfo.CancelOrder.data;
      }

      // Filter theo product name
      const filteredData = ordersData?.filter(order => {
        // Tìm trong product name hoặc các thông tin liên quan
        const searchLower = dataSearch.toLowerCase();

        // Kiểm tra nếu order có orderDetails array
        if (order?.orderDetails && Array.isArray(order?.orderDetails)) {
          return order?.orderDetails.some(
            (orderDetail: any) =>
              orderDetail.Name?.toLowerCase().includes(searchLower) ||
              orderDetail.productInfo?.Name?.toLowerCase().includes(
                searchLower,
              ) ||
              orderDetail.title?.toLowerCase().includes(searchLower),
          );
        }

        // Kiểm tra mã đơn hàng và các thông tin khác
        return (
          order.Code?.toLowerCase().includes(searchLower) ||
          order.Customer?.Name?.toLowerCase().includes(searchLower) ||
          order.Address?.Name?.toLowerCase().includes(searchLower) ||
          order.Shop?.Name?.toLowerCase().includes(searchLower)
        );
      });
      setDataSearchResult(filteredData);
      setNumberCardSearch(filteredData.length);
    }
  };
  useEffect(() => {
    if (dataSearch) {
      orderActions.setLoading(true);
      setTimeout(() => {
        if (route?.params?.type == Title.New) {
          handleSearch(dataSearch, 1);
        } else if (route?.params?.type == Title.Processing) {
          handleSearch(dataSearch, 2);
        } else if (route?.params?.type == Title.Done) {
          handleSearch(dataSearch, 3);
        } else if (route?.params?.type == Title.Cancel) {
          handleSearch(dataSearch, 4);
        }
        orderActions.setLoading(false);
      }, 1000);
    }
  }, [dataSearch]);
  useEffect(() => {
    if (route?.params?.type) {
      setTypeCard(route?.params?.type);
    }
  }, [route?.params?.type]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <TitleHeader title={typeCard} />
      <SearchBar setDataSearch={setDataSearch} />
      {typeCard != Title.Done && (
        <OrderInfoHeader
          isLoading={isLoading}
          dataSearch={dataSearch}
          numberCardSearch={numberCardSearch ? numberCardSearch : 0}
          numberCard={numberCard ? numberCard : 0}
        />
      )}
      {isLoading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <ListOrderCard
          type={typeCard}
          setTypeCard={setTypeCard}
          dataSearchResult={dataSearchResult}
          dataSearch={dataSearch}
          orderInfo={orderInfo}
          setNumberCard={setNumberCard}
          HandleGetOrderData={() =>
            shopId && orderActions.fetchAllOrdersByShopId(shopId)
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
});

export default OrderShopDetail;
