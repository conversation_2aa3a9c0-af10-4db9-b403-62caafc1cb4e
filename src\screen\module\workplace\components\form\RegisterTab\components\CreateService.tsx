import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  ScrollView,
  Pressable,
  StyleSheet,
} from 'react-native';
import {<PERSON><PERSON><PERSON><PERSON>} from '../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../assets/skin/colors';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from '../../../../../../../redux/hooks/hooks';

import ScreenHeader from '../../../../../../layout/header';
import AllServices from '../../../../../service/services';
import {navigate, RootScreen} from '../../../../../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {closePopup, Winicon} from 'wini-mobile-components';

interface CreateServiceProps {
  serviceId: any;
  customer: any;
}

export const CreateService = forwardRef<any, CreateServiceProps>(
  function CreateService({serviceId, customer}, ref: any) {
    const [selectedCate, setSelectedCate] = useState<any>([]);
    const user = useSelectorCustomerState().data;
    const company = useSelectorCustomerCompanyState().data;
    const ktxgroup = useSelectorCustomerCompanyState().ktxgroup;
    const dispatch = useDispatch<any>();
    const navigation = useNavigation<any>();
    const [step, setStep] = useState(0);
    const [policyData, setPolicyData] = useState<any>();
    const now = new Date();

    const handleServiceSelect = (selectedServices: any) => {
      closePopup(ref);
      navigation.push(RootScreen.ServicesWorkFlow, {
        type: selectedServices[0].RouteName,
        serviceId: serviceId,
        customer: customer,
      });
    };
    useEffect(() => {
      if (selectedCate.length > 0) {
        handleServiceSelect(selectedCate);
      }
    }, [selectedCate]);

    return (
      <SafeAreaView style={styles.container}>
        <ScreenHeader
          style={styles.header}
          title={`Lựa chọn dịch vụ`}
          prefix={<View />}
          action={
            <TouchableOpacity style={styles.closeButton}>
              <Winicon
                src="outline/layout/xmark"
                onClick={() => closePopup(ref)}
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          }
        />
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
          style={styles.content}>
          <View style={styles.scrollView}>
            <Pressable style={styles.contentPressable}>
              <Text style={styles.contentTitle}>
                Lựa chọn dịch vụ mong muốn
              </Text>
              <Text style={styles.contentDescription}>
                Với hàng nghìn chuyên gia trong lĩnh vực thiết kế, xây dựng nhà
                vệ sinh. Chúng tôi tự hào cung cấp các dịch vụ tốt nhất đến
                khách hàng. Hãy lựa chọn dịch vụ bạn mong muốn, và chia sẻ các
                thông tin cần thiết trước khi đến những bước tiếp theo.
              </Text>
            </Pressable>
            <AllServices
              createService
              selectedCate={selectedCate}
              setSelectedCate={(e: any) => {
                setSelectedCate(e);
              }}
            />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: Dimensions.get('window').height - 40,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    flexDirection: 'row',
    paddingVertical: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
  },
  content: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 16,
    flex: 1,
  },
  scrollView: {
    backgroundColor: ColorThemes.light.transparent,
    flex: 1,
  },
  contentPressable: {},
  contentTitle: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
    paddingBottom: 4,
  },
  contentDescription: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
    paddingBottom: 8,
  },
});
