import {Platform} from 'react-native';

const hostMapUrl = 'https://server.wini.vn/api/data/';

export default class ConfigAPI {
  static url = 'https://dev.wini.vn/api/';
  static urlWeb = 'https://nvs.ktxgroup.com.vn/';
  // static socketUrl = 'https://redis.ktxgroup.com.vn'
  static fileManagerUrl = 'https://file-mamager.wini.vn/api/SystemFile/img/';
  static imgUrlId = 'https://dev.wini.vn/api/file/img/';
  static pid = '944b6a5577524e1eadef54ab63598daa';
  static ktxCompanyId = '08cef75a7b9c4db7a2831504f47dd4fe';
  static adminKtxId = '348ad5b7508e45c4886d5d2031767bf9';
  static regexGuid = /^[0-9a-fA-F]{32}$/;

  static googleApiKey =
    Platform.OS === 'ios'
      ? 'AIzaSyDMwLH3wGE_dAnKgJJNfVGoAYo0AINv24o'
      : 'AIzaSyBKxJEVC_2auoDUoJyvb69xtOL23UfdJEE';

  static GEOCODING_API_URL_BY_GOOGLE = (lat: any, lng: any) => {
    // return `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    return (
      hostMapUrl +
      `geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    );
  };

  static getAddressByGoogleKey = (inputText: string) => {
    return (
      hostMapUrl +
      `place/textsearch/json?&query=${encodeURIComponent(inputText)}&components=country:VN&key=${ConfigAPI.googleApiKey}`
    );
  };

  // https://api.vietqr.io/v2/banks
  static bankUrl = 'https://api.vietqr.io/v2/banks';

  static provinceUrl = 'https://esgoo.net/api-tinhthanh/1/0.htm';
  static districtUrl = (cityId: string) =>
    `https://esgoo.net/api-tinhthanh/2/${cityId}.htm`;

  static wardUrl = (districtId: string) =>
    `https://esgoo.net/api-tinhthanh/3/${districtId}.htm`;

  static getValidLink = (link: string) => {
    if (!link) return '';
    if (link.startsWith('http')) return link;
    if (ConfigAPI.regexGuid.test(link)) return ConfigAPI.imgUrlId + link;
    else return ConfigAPI.imgUrlId + link;
  };
}
