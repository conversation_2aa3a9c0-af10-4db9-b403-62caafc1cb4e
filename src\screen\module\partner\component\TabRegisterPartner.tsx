import {View} from 'react-native';
import PagerView from 'react-native-pager-view';
import StepIndicator from '../../../../component/StepIndicator';
import {useEffect, useRef, useState} from 'react';
import {TabRegisterPartnerStyles} from './styles/TabRegisterPartnerStyles';
import RegisterPartnerForm from './form/RegisterPartnerForm';
import {PartnerDa} from '../partnerDa';
import SelectServicePartnerList from './list/SelectServicePartnerList';
import TabTermsAndPoliciesPartnet from './TabTermsAndPoliciesPartnet';
import {store} from '../../../../redux/store/store';
import {randomGID} from '../../../../utils/Utils';
import {useNavigation} from '@react-navigation/native';
import {showSnackbar} from '../../../../component/export-component';
import {ComponentStatus} from 'wini-mobile-components';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {useDispatch} from 'react-redux';
import {CustomerType} from '../../../../redux/reducers/user/da';

const TabRegisterPartner: React.FC<{}> = ({}) => {
  const navigation = useNavigation();
  const [currentStep, setCurrentStep] = useState(1);
  const [dataCateService, setDataCateService] = useState<any[]>([]);
  const [policyData, setPolicyData] = useState<any>();
  const [AlldataRegister, setAlldataRegister] = useState<any>();
  const [listcateService, setListcateService] = useState<any[]>([]);
  const [isAgreeTerm, setIsAgreeTerm] = useState(false);
  const cusInfo = store.getState().customer.data;
  const dispatch = useDispatch<any>();
  const partnerDa = new PartnerDa();
  const pagerRef = useRef<PagerView>(null);
  const getPolicy = async () => {
    const res = await partnerDa.getPolicy('/partner');
    if (res.code === 200 && res.data.length) setPolicyData(res.data[0].Content);
  };
  useEffect(() => {
    getPolicy();
  }, []);
  useEffect(() => {
    if (dataCateService) {
      let listIdService = dataCateService.map((e: any) => e.Id);
      setListcateService(listIdService);
      setAlldataRegister({...AlldataRegister, CateServicesId: listIdService});
    }
  }, [dataCateService]);

  useEffect(() => {
    if (isAgreeTerm) {
      setAlldataRegister({...AlldataRegister, IsAgreeTerm: isAgreeTerm});
    }
  }, [isAgreeTerm]);

  const ChangeStep1 = async (data: any) => {
    if (data && cusInfo && cusInfo?.CompanyProfileId) {
      const currentCompanyResponse = await partnerDa.getInforCompanyById(
        cusInfo.CompanyProfileId,
      );
      const currentCompany = currentCompanyResponse?.data;
      let dataUpdate = {
        Id: cusInfo.CompanyProfileId, // Sử dụng CompanyProfileId để update
        Name: data?.Name,
        DateCreated: currentCompany?.DateCreated || new Date().getTime(),
        DateUpdated: new Date().getTime(), // Thêm timestamp update
        Email: data?.Email,
        Industry: data?.Industry,
        Mobile: data?.Mobile,
        Position: data?.Position,
        Note: data?.Note,
        Address: data?.Address,
        Lat: data?.Lat,
        Long: data?.Long,
        TaxCode: data?.TaxCode,
        Representative: data?.Representative,
        BankAccount: data?.BankAccount,
        BankAccountName: data?.BankAccountName,
        BankId: data?.BankId,
        CustomerId: cusInfo?.Id,
      };
      console.log('🔄 Updating company data:', dataUpdate);
      let response = await partnerDa.updateCompanyData(dataUpdate);
      console.log('check-response', response);
      if (response && response.code === 200) {
        showSnackbar({
          message: 'Cập nhật thông tin doanh nghiệp thành công',
          status: ComponentStatus.SUCCSESS,
        });
        setAlldataRegister(data);
        setCurrentStep(currentStep + 1);
        pagerRef.current?.setPage(1);
      } else {
        showSnackbar({
          message: response?.message || 'Có lỗi xảy ra',
          status: ComponentStatus.ERROR,
        });
      }
    } else if (data && cusInfo && !cusInfo?.CompanyProfileId) {
      let dataCreate = {
        Id: randomGID(),
        Name: data?.Name,
        DateCreated: new Date().getTime(),
        Email: data?.Email,
        Industry: data?.Industry,
        Mobile: data?.Mobile,
        Position: data?.Position,
        Note: data?.Note,
        Address: data?.Address,
        // lat, long
        Lat: data?.Lat,
        Long: data?.Long,
        Phone: data?.Phone,
        TaxCode: data?.TaxCode,
        Representative: data?.Representative,
        BankAccount: data?.BankAccount,
        BankAccountName: data?.BankAccountName,
        CompanyMail: data?.CompanyMail,
        BankId: data?.BankId,
      };
      let response = await partnerDa.CreateCompanyData(dataCreate);
      if (response && response.code === 200) {
        showSnackbar({
          message: 'Tạo mới thông tin doanh nghiệp thành công',
          status: ComponentStatus.SUCCSESS,
        });
        await CustomerActions.edit(dispatch, {
          ...cusInfo,
          CompanyProfileId: response.data[0].Id,
        }).then((res: any) => {
          if (res.code === 200) {
            setAlldataRegister(data);
            setCurrentStep(currentStep + 1);
            pagerRef.current?.setPage(1);
          }
        });
      } else {
        showSnackbar({
          message: response?.message || 'Có lỗi xảy ra',
          status: ComponentStatus.ERROR,
        });
      }
    }
  };

  const changeStep2 = async () => {
    await CustomerActions.edit(dispatch, {
      ...cusInfo,
      CateServicesId: listcateService.join(','),
    }).then((res: any) => {
      if (res.code === 200) {
        setCurrentStep(currentStep + 1), pagerRef.current?.setPage(2);
      }
    });
  };

  const Submit = async () => {
    try {
      if (isAgreeTerm) {
        let ress = await CustomerActions.edit(dispatch, {
          ...cusInfo,
          Type: isAgreeTerm ? CustomerType.partner : CustomerType.guest,
        });
        if (ress.code !== 200) return;
        // tạo shop
        const Response = await partnerDa.registerPartner({
          Id: randomGID(),
          CustomerId: cusInfo?.Id,
          DateCreated: new Date().getTime(),
          CateServicesId: AlldataRegister?.CateServicesId?.toString(),
          IsAgreeTerm: isAgreeTerm,
          TaxCode: AlldataRegister?.TaxCode,
          CompanyProfileId: cusInfo?.CompanyProfileId,
          GroupPhone: AlldataRegister?.Mobile,
          Name: AlldataRegister?.Name,
          GroupEmail: AlldataRegister?.Email,
          GroupAddress: AlldataRegister?.Address,
          BusinessAreas: AlldataRegister?.Industry,
          Description: AlldataRegister?.NoteShort,
          RepresentativeUser: AlldataRegister?.Representative,
          BankAccount: AlldataRegister?.BankAccount,
          BankAccountOwner: AlldataRegister?.BankAccountName,
          BankId: AlldataRegister?.BankId,
          RepresentativePosition: AlldataRegister?.Position,
          RepresentativeEmail: AlldataRegister?.Email,
          ShortName: AlldataRegister?.ShortName,
          RepresentativePhone: AlldataRegister?.Phone,
        });
        if (Response.code === 200) {
          showSnackbar({
            message: `Bạn đã đăng lý làm đối tác thành công`,
            status: ComponentStatus.SUCCSESS,
          });
          navigation.goBack();
        } else {
          showSnackbar({
            message: Response.message || 'Có lỗi xảy ra',
            status: ComponentStatus.ERROR,
          });
        }
      }
    } catch (error) {
      console.log('Submit error:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi đăng ký',
        status: ComponentStatus.ERROR,
      });
    }
  };
  return (
    <View style={TabRegisterPartnerStyles.container}>
      <TitleHeader title="Đăng ký trở thành đối tác" />
      <View>
        <StepIndicator currentStep={currentStep} totalSteps={3} />
      </View>
      <PagerView
        ref={pagerRef}
        style={TabRegisterPartnerStyles.pagerView}
        initialPage={0}
        scrollEnabled={false}>
        <View key="step1">
          <RegisterPartnerForm
            statusInput="add"
            data={cusInfo}
            setCurrentStep={setCurrentStep}
            currentStep={currentStep}
            pagerRef={pagerRef}
            ChangeStep1={ChangeStep1}
          />
        </View>
        <View key="step2">
          <SelectServicePartnerList
            dataCateService={dataCateService}
            setDataCateService={setDataCateService}
            setCurrentStep={setCurrentStep}
            currentStep={currentStep}
            pagerRef={pagerRef}
            changeStep2={changeStep2}
          />
        </View>

        <View key="step3" style={{flex: 1}}>
          <TabTermsAndPoliciesPartnet
            policy={policyData}
            isAgreeTerm={isAgreeTerm}
            setisAgreeTerm={setIsAgreeTerm}
            Submit={Submit}
          />
        </View>
      </PagerView>
    </View>
  );
};

export default TabRegisterPartner;
