import {
  Button,
  Dimensions,
  FlatList,
  Pressable,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {
  TaskStatus,
  TaskType,
  ToiletServiceStatus,
} from '../../../service/components/da';
import {forwardRef, useEffect, useMemo, useRef, useState} from 'react';
import EmptyPage from '../../../../../project-component/empty-page';
import {
  FCheckbox,
  showSnackbar,
  Winicon,
} from '../../../../../component/export-component';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {
  closePopup,
  FPopup,
  showPopup,
} from '../../../../../component/popup/popup';
import AppButton from '../../../../../component/button';
import {DataController} from '../../../../base-controller';
import ListTile from '../../../../../component/list-tile/list-tile';
import {differenceInDays, setMonth} from 'date-fns';
import {
  useSelectorCateServiceState,
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../../redux/hooks/hooks';
import {
  CustomerRole,
  CustomerType,
} from '../../../../../redux/reducers/user/da';
import {PopupEditTask} from './task';
import {useForm} from 'react-hook-form';
import {ComponentStatus} from '../../../../../component/component-status';
import {Tooltip} from 'react-native-paper';
import {Ultis} from '../../../../../utils/Utils';
import WScreenFooter from '../../../../layout/footer';
import ScreenHeader from '../../../../layout/header';
import {
  StatusToiletServiceData,
  StatusData,
} from '../../../workplace/components/card/NewWorkCard';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

export default function CalendarTab({
  data,
  serviceData,
}: {
  data: any;
  serviceData: any;
}) {
  const [showMonth, setShowMonth] = useState(false);
  const [today, setToday] = useState(true);
  const [focusDate, setFocusDate] = useState(new Date());
  const popupRef = useRef<any>();
  const [refreshing, onRefresh] = useState(false);

  const [assignees, setAssignees] = useState<Array<any>>([]);
  const company = useSelectorCustomerCompanyState().data;
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;

  useEffect(() => {
    filterMethods.setValue('isToday', today);
  }, [today]);

  const monthTitle = useMemo(() => {
    var monthName = '';
    switch (focusDate.getMonth()) {
      case 0:
        monthName = '01';
        break;
      case 1:
        monthName = '02';
        break;
      case 2:
        monthName = '03';
        break;
      case 3:
        monthName = '04';
        break;
      case 4:
        monthName = '05';
        break;
      case 5:
        monthName = '06';
        break;
      case 6:
        monthName = '07';
        break;
      case 7:
        monthName = '08';
        break;
      case 8:
        monthName = '09';
        break;
      case 9:
        monthName = '10';
        break;
      case 10:
        monthName = '11';
        break;
      case 11:
        monthName = '12';
        break;
      default:
        monthName = '';
        break;
    }
    return `Tháng ${monthName}/${focusDate.getFullYear()}`;
  }, [focusDate]);

  useEffect(() => {
    if (
      company &&
      (user?.CompanyProfileId === company.Id ||
        (userRole?.CompanyProfileId === company.Id &&
          userRole?.Role?.includes(CustomerRole.Coordinator)))
    ) {
      const roleController = new DataController('CustomerCompany');
      roleController
        .aggregateList({
          page: 1,
          size: 1000,
          searchRaw: `@CompanyProfileId:{${company.Id}} @Status:[1 1]`,
        })
        .then(res => {
          if (res.code === 200) {
            const customerController = new DataController('Customer');
            customerController
              .getByListId(res.data.map((e: any) => e.CustomerId))
              .then(resCustomer => {
                if (resCustomer.code === 200 && resCustomer.data.length) {
                  switch (data?.Type) {
                    case TaskType.consultant:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return (
                            _role?.Role?.includes(CustomerRole.Consultant) ||
                            _role?.Role?.includes(CustomerRole.Coordinator)
                          );
                        }),
                      );
                      break;
                    case TaskType.contract:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return _role?.Role?.includes(
                            CustomerRole.Coordinator,
                          );
                        }),
                      );
                      break;
                    case TaskType.design:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return (
                            _role &&
                            [
                              CustomerRole.SCBD,
                              CustomerRole.Coordinator,
                              CustomerRole.Owner,
                              CustomerRole.Consultant,
                            ].some(rl => _role.Role.includes(rl))
                          );
                        }),
                      );
                      break;
                    default:
                      setAssignees(
                        resCustomer.data.filter((e: any) => {
                          if (e.CompanyProfileId) return true;
                          if (e.Type === CustomerType.partner) return false;
                          const _role = res.data.find(
                            (r: any) => r.CustomerId === e.Id,
                          );
                          return _role !== undefined;
                        }),
                      );
                      break;
                  }
                } else setAssignees([user]);
              });
          }
        });
    } else return setAssignees([user]);
  }, [company, user]);

  const filterMethods = useForm({shouldFocusError: false});

  const PopupFilter = forwardRef(function PopupFilter(
    data: {
      filterMethods: any;
      onApply: (values: any) => void;
      StatusIds: [];
      CateServicesId: [];
      listStatus: any;
    },
    ref: any,
  ) {
    const {onApply, StatusIds, listStatus, filterMethods, CateServicesId} =
      data;
    const [selected, setSelected] = useState<Array<any>>([]);
    const [selectedCate, setSelectedCate] = useState<Array<any>>([]);
    const cateServices = useSelectorCateServiceState().data;

    useEffect(() => {
      if (StatusIds.length) setSelected(StatusIds);
    }, [StatusIds.length]);

    useEffect(() => {
      if (CateServicesId.length) setSelectedCate(CateServicesId);
    }, [CateServicesId.length]);

    return (
      <SafeAreaView
        style={{
          width: '100%',
          height: Dimensions.get('window').height - 146,
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          backgroundColor: '#fff',
        }}>
        <ScreenHeader
          style={{
            backgroundColor: ColorThemes.light.transparent,
            borderBottomColor: ColorThemes.light.neutral_main_background_color,
            borderBottomWidth: 0.5,
            shadowColor: 'rgba(0, 0, 0, 0.03)',
            shadowOffset: {
              width: 0,
              height: 4,
            },
            shadowRadius: 20,
            elevation: 20,
            shadowOpacity: 1,
          }}
          title={`Bộ lọc`}
          prefix={<View style={{width: 50}} />}
          action={
            <View
              style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
              <Winicon
                src="outline/layout/xmark"
                onClick={() => closePopup(ref)}
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </View>
          }
        />
        <ScrollView
          style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
          <Text style={{...TypoSkin.label3, marginBottom: 8}}>
            Loại công việc
          </Text>
          <FlatList
            data={cateServices}
            scrollEnabled={false}
            renderItem={({item, index}) => (
              <TouchableOpacity
                key={item.Id}
                onPress={() => {
                  if (!selectedCate.includes(item.Id))
                    setSelectedCate([...selectedCate, item.Id]);
                  else
                    setSelectedCate(
                      selectedCate.filter((id: any) => id !== item.Id),
                    );
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  width: '50%',
                }}>
                <FCheckbox
                  value={selectedCate.includes(item.Id)}
                  onChange={v => {
                    if (v) setSelectedCate([...selectedCate, item.Id]);
                    else
                      setSelectedCate(
                        selectedCate.filter((id: any) => id !== item.Id),
                      );
                  }}
                />
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {item?.Name ?? '-'}
                </Text>
              </TouchableOpacity>
            )}
            //Setting the number of column
            numColumns={2}
            style={{width: '100%'}}
            contentContainerStyle={{gap: 16}}
            keyExtractor={(item, index) => index.toString()}
          />
          <Text style={{...TypoSkin.label3, marginBottom: 8, marginTop: 16}}>
            Trạng thái
          </Text>
          <FlatList
            data={listStatus}
            scrollEnabled={false}
            renderItem={({item, index}) => (
              <TouchableOpacity
                key={item.id}
                onPress={() => {
                  if (!selected.includes(item.id))
                    setSelected([...selected, item.id]);
                  else
                    setSelected(selected.filter((id: any) => id !== item.id));
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  width: '50%',
                }}>
                <FCheckbox
                  value={selected.includes(item.id)}
                  onChange={v => {
                    if (v) setSelected([...selected, item.id]);
                    else
                      setSelected(selected.filter((id: any) => id !== item.id));
                  }}
                />
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {item?.name ?? '-'}
                </Text>
              </TouchableOpacity>
            )}
            //Setting the number of column
            numColumns={2}
            style={{width: '100%'}}
            contentContainerStyle={{gap: 16}}
            keyExtractor={(item, index) => index.toString()}
          />
        </ScrollView>
        <WScreenFooter
          style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
          <AppButton
            title={'Làm mới'}
            backgroundColor={ColorThemes.light.neutral_main_background_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              // filterMethods.setValue("StatusIds", [])
              // filterMethods.setValue("CateServicesId", [])
              setSelected([]);
              setSelectedCate([]);
            }}
            textColor={ColorThemes.light.neutral_text_subtitle_color}
          />
          <AppButton
            title={'Áp dụng'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              closePopup(ref);
              onApply({StatusIds: selected, CateServicesId: selectedCate});
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      </SafeAreaView>
    );
  });

  const returnView = () => {
    return today ? (
      <TodayList
        assignees={assignees}
        focusDate={focusDate}
        data={data}
        serviceData={serviceData}
        onRefresh={onRefresh}
        refreshing={refreshing}
        filterMethods={filterMethods}
      />
    ) : showMonth ? (
      <CalendarMonth
        assignees={assignees}
        focusDate={focusDate}
        data={data}
        serviceData={serviceData}
        onRefresh={onRefresh}
        refreshing={refreshing}
        filterMethods={filterMethods}
      />
    ) : (
      <CalendarWeek
        assignees={assignees}
        focusDate={focusDate}
        data={data}
        serviceData={serviceData}
        onRefresh={onRefresh}
        refreshing={refreshing}
        filterMethods={filterMethods}
      />
    );
  };

  return (
    <View style={{flex: 1, paddingHorizontal: 16}}>
      <FPopup ref={popupRef} />
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        <View
          style={{flex: 1, flexDirection: 'row', alignItems: 'center', gap: 8}}>
          <TouchableOpacity
            onPress={() => {
              if (today) return;
              if (showMonth) {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth() - 1,
                    focusDate.getDate(),
                  ),
                );
              } else {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() - 7,
                  ),
                );
              }
              setToday(false);
            }}
            style={{paddingVertical: 4}}>
            <Winicon src="outline/arrows/left-arrow" size={16} />
          </TouchableOpacity>
          <Text
            style={{
              paddingHorizontal: 4,
              color: ColorThemes.light.primary_main_color,
            }}
            onPress={() => {
              setFocusDate(new Date());
              setToday(true);
              setShowMonth(false);
            }}>
            Hôm nay
          </Text>
          <TouchableOpacity
            onPress={() => {
              if (today) return;
              if (showMonth) {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth() + 1,
                    focusDate.getDate(),
                  ),
                );
              } else {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() + 7,
                  ),
                );
              }
              setToday(false);
            }}
            style={{paddingVertical: 4}}>
            <Winicon src="outline/arrows/right-arrow" size={16} />
          </TouchableOpacity>
        </View>
        <AppButton
          onPress={() => {
            var options = [
              {id: TaskStatus.open, name: 'Mở'},
              // {id: TaskStatus.doing, name: "Đang làm"},
              {id: TaskStatus.done, name: 'Hoàn thành'},
              // { id: TaskStatus.overdue, name: "Quá hạn" },
              {id: TaskStatus.closed, name: 'Đóng'},
            ];
            showPopup({
              ref: popupRef,
              enableDismiss: true,
              children: (
                <PopupFilter
                  ref={popupRef}
                  filterMethods={filterMethods}
                  listStatus={options}
                  StatusIds={filterMethods.watch('StatusIds') ?? []}
                  CateServicesId={filterMethods.watch('CateServicesId') ?? []}
                  onApply={value => {
                    filterMethods.setValue('StatusIds', value.StatusIds);
                    filterMethods.setValue(
                      'CateServicesId',
                      value.CateServicesId,
                    );
                  }}
                />
              ),
            });
          }}
          backgroundColor={ColorThemes.light.transparent}
          borderColor="transparent"
          containerStyle={{height: 35, borderRadius: 8}}
          prefixIconSize={12}
          prefixIcon={'outline/user interface/setup-preferences'}
          textColor={
            filterMethods.watch('StatusIds')?.length ||
            filterMethods.watch('CateServicesId')?.length
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_text_subtitle_color
          }
          title={'Bộ lọc'}
          textStyle={TypoSkin.subtitle3}
        />
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingLeft: 8,
        }}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.primary_main_color,
          }}>
          {monthTitle}
        </Text>
        <AppButton
          title={today ? 'Hôm nay' : showMonth ? 'Tháng' : 'Tuần'}
          backgroundColor={ColorThemes.light.transparent}
          borderColor="transparent"
          textColor={ColorThemes.light.primary_main_color}
          prefixIcon={
            <Winicon
              src={
                showMonth
                  ? `outline/user interface/search-zoom-out`
                  : `outline/user interface/search-zoom-in`
              }
              size={14}
              color={ColorThemes.light.primary_main_color}
            />
          }
          onPress={() => {
            if (today) {
              setToday(false);
              return;
            }
            setShowMonth(!showMonth);
          }}
        />
      </View>
      {returnView()}
    </View>
  );
}

//#region Today
const TodayList = ({
  focusDate,
  data,
  serviceData,
  refreshing,
  onRefresh,
  assignees,
  filterMethods,
}: {
  focusDate: Date;
  data: any;
  serviceData: any;
  refreshing?: any;
  onRefresh?: any;
  assignees: Array<any>;
  filterMethods: any;
}) => {
  const focusWeekDay = useMemo(() => focusDate.getDay(), [focusDate]);
  const [tasks, setTasks] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);

  const getData = async () => {
    setTasks([]);
    const controller = new DataController('Task');
    const startDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth(),
      focusDate.getDate() - focusWeekDay,
    );
    const endDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth(),
      focusDate.getDate() + 7 - focusWeekDay,
      23,
      59,
      59,
      999,
    );
    let queryStatus = [];
    let queryCates: any = [];
    if (filterMethods.watch('StatusIds')?.length)
      filterMethods
        .watch('StatusIds')
        .forEach((e: any, i: number) =>
          queryStatus.push(
            `@Status:[${e} ${e}] ${i == filterMethods.watch('StatusIds').length - 1 ? '' : '|'}`,
          ),
        );
    else
      queryStatus.push(
        `(-@Status:[${TaskStatus.closed} ${TaskStatus.closed}])`,
      );
    if (filterMethods.watch('CateServicesId')?.length) {
      var lst = filterMethods.watch('CateServicesId');
      queryCates = [`@CateServicesId:{${lst.join(' | ')}}`];
    }
    const res = await controller.aggregateList({
      page: 1,
      size: 2000,
      searchRaw: `@ToiletId:{${data.Id}} ${queryStatus.join(' ')} ${queryCates.join(' ')} (@DateStart:[-inf ${endDate.getTime()}]) | (@DateEnd:[${startDate.getTime()} +inf])`,
    });
    if (res.code === 200) {
      const servicesIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }
      setTasks(res.data);
      onRefresh(false);
    }
    onRefresh(false);
  };

  useEffect(() => {
    getData();
  }, [
    focusDate,
    data,
    serviceData,
    refreshing,
    onRefresh,
    filterMethods.watch('StatusIds'),
    filterMethods.watch('CateServicesId'),
  ]);

  const [activities, setActivities] = useState<Array<any>>([]);

  const getActivities = async () => {
    const controller = new DataController('Activity');
    const taskIds = tasks
      ?.filter((t: any) => t.RequireCheckin)
      .map((e: any) => e.Id)
      .filter(
        (v: any, i: any, a: any | any[]) => v?.length && a.indexOf(v) === i,
      );
    const res = await controller.aggregateList({
      page: 1,
      size: 100,
      searchRaw: `@TaskId:{${taskIds.join(' | ')}}`,
    });
    if (res.code == 200) {
      setActivities(res.data);
    }
  };

  useEffect(() => {
    getActivities();
  }, [tasks]);

  const weekdays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  const methods = useForm({shouldFocusError: false});

  const onChangeTask = async (t: any) => {
    const controller = new DataController('Task');
    const res = await controller.edit([t]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    setTasks((ts: any) => ts?.map((e: any) => (e.Id === t.Id ? t : e)));
    showSnackbar({
      message: 'Chỉnh sửa công việc thành công',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const showAddEditTask = (item: any) => {
    let checkEditable = undefined;
    if (item.ToiletServicesId)
      var toiletServicesData = toiletServices.find(
        e => e.Id === item.ToiletServicesId,
      );
    if (toiletServicesData) {
      if (toiletServicesData.Status < ToiletServiceStatus.run) {
        checkEditable =
          (owner?.Id === toiletServicesData?.CustomerId &&
            userRole?.Role.includes(CustomerRole.Coordinator)) ||
          user?.Id === toiletServicesData?.CustomerId;
      } else checkEditable = false;
    } else {
      checkEditable =
        (owner?.Id === data?.CustomerId &&
          userRole?.Role.includes(CustomerRole.Coordinator)) ||
        user?.Id === data?.CustomerId ||
        item.CustomerId === user?.Id;
    }
    if (item.DateStart) {
      methods.setValue('dateStart', new Date(item.DateStart));
    } else {
      methods.setValue('dateStart', undefined);
    }
    if (item.DateEnd) {
      methods.setValue('dateEnd', new Date(item.DateEnd));
    } else {
      methods.setValue('dateEnd', undefined);
    }

    methods.setValue('Description', `${item?.Description ?? ''}`);
    methods.setValue(
      'CateServicesId',
      `${item?.CateServicesId ?? 'undefined'}`,
    );

    if (checkEditable && item.Type === TaskType.other) {
      methods.setValue('Name', `${item?.Name}`);
    }

    var options = [
      {id: TaskStatus.open, name: 'Mở'},
      // {id: TaskStatus.doing, name: "Đang làm"},
      {id: TaskStatus.done, name: 'Hoàn thành'},
      // { id: TaskStatus.overdue, name: "Quá hạn" },
      {id: TaskStatus.closed, name: 'Đóng'},
    ];
    if (item?.Status < TaskStatus.overdue) {
      options.filter(e => e.id < TaskStatus.overdue);
    }
    methods.setValue(
      'status',
      options.find(e => e.id === item?.Status)?.id ?? options[0].id,
    );
    // if (assignees.length) methods.setValue("assignee", assignees.find(e => e.Id === item?.CustomerId)?.Id ?? assignees[0].Id)

    showPopup({
      ref: popupRef,
      children: (
        <PopupEditTask
          methods={methods}
          options={options}
          toiletId={data.Id}
          ref={popupRef}
          item={item}
          onChange={onChangeTask}
          checkEditable={checkEditable}
        />
      ),
    });
  };

  return (
    <View style={{flex: 1, width: '100%'}}>
      <FPopup ref={popupRef} />
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={() => {
              if (onRefresh) onRefresh(true);
            }}
          />
        }
        style={{flex: 1}}>
        <Pressable style={{gap: 16, flex: 1, width: '100%', height: '100%'}}>
          {weekdays.map((_, i) => {
            const thisDate = new Date(
              focusDate.getFullYear(),
              focusDate.getMonth(),
              focusDate.getDate() + i - focusWeekDay,
              0,
              0,
              2,
            );
            const weekdayTitle = weekdays[i];
            const thisDateValue = thisDate.getDate();
            const _taskList = tasks?.filter((t: any) => {
              const _stDate = new Date(t.DateStart);
              const _endDate = new Date(t.DateEnd);
              switch (t.Repeat) {
                case 1:
                  return (
                    (_stDate.getDate() === thisDate.getDate() &&
                      Math.abs(differenceInDays(_stDate, thisDate)) < 1) ||
                    (_endDate.getDate() === thisDate.getDate() &&
                      Math.abs(differenceInDays(_endDate, thisDate)) < 1) ||
                    (_stDate.getTime() < thisDate.getTime() &&
                      _endDate.getTime() >= thisDate.getTime())
                  );
                case 2:
                  return t.RepeatValue && JSON.parse(t.RepeatValue).includes(i);
                case 3:
                  const tmpDate = new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() + i - focusWeekDay,
                  );
                  tmpDate.setDate(thisDateValue + 1);
                  return (
                    t.RepeatValue &&
                    JSON.parse(t.RepeatValue).includes(
                      tmpDate.getMonth() !== thisDate.getMonth()
                        ? 'last'
                        : thisDateValue,
                    )
                  );
                default:
                  return (
                    (_stDate.getDate() === thisDateValue &&
                      !differenceInDays(_stDate, thisDate)) ||
                    (_endDate.getDate() === thisDateValue &&
                      !differenceInDays(_endDate, thisDate))
                  );
              }
            });
            return focusWeekDay === i ? (
              <ListTile
                key={`dtwk-${i}`}
                leading={
                  <View style={{width: '100%'}}>
                    <View style={{gap: 4}}>
                      <Text
                        style={[
                          styles.heading,
                          focusWeekDay === i && {
                            color: ColorThemes.light.primary_main_color,
                          },
                        ]}>
                        {weekdayTitle}
                      </Text>
                      <Text style={{...TypoSkin.subtitle3}}>
                        {thisDateValue}/{thisDate.getMonth() + 1}
                      </Text>
                    </View>
                  </View>
                }
                style={{
                  borderColor: ColorThemes.light.neutral_bolder_border_color,
                  borderWidth: 1,
                  width: '100%',
                  height: '100%',
                  marginTop: 16,
                }}
                listtileStyle={{alignItems: 'flex-start', gap: 16}}
                title={
                  <View style={{gap: 8, paddingBottom: 8}}>
                    {refreshing ? (
                      <Pressable style={{gap: 16}}>
                        <SkeletonPlaceholder>
                          <SkeletonPlaceholder.Item
                            height={40}
                            width={'100%'}
                            borderRadius={8}
                          />
                        </SkeletonPlaceholder>
                      </Pressable>
                    ) : _taskList?.length && focusWeekDay === i ? (
                      _taskList
                        ?.filter(task => {
                          return (
                            task.DateStart <= new Date().getTime() ||
                            task.DateEnd >= new Date().getTime()
                          );
                        })
                        .map((tk: any) => {
                          const activity = activities
                            ?.filter(
                              e =>
                                e.TaskId === tk.Id &&
                                e.EndTime &&
                                thisDateValue === new Date(e.EndTime).getDate(),
                            )
                            ?.sort(
                              (a: any, b: any) => b?.EndTime - a?.EndTime,
                            )[0];

                          const checked =
                            (tk?.DeviceId?.split(',')?.length ==
                              activity?.DeviceId?.split(',').length &&
                              tk?.BioProductId?.split(',')?.length ==
                                activity?.BioProductId?.split(',').length &&
                              new Date(activity?.DateCreated).getDate() ===
                                thisDateValue) ??
                            false;
                          const _stDate = new Date(tk.DateStart);
                          const _endDate = new Date(tk.DateEnd);
                          var checkEditable = false;
                          if (tk.ToiletServicesId)
                            var toiletServicesData = toiletServices.find(
                              e => e?.Id === tk.ToiletServicesId,
                            );
                          if (toiletServicesData) {
                            if (
                              toiletServicesData.Status <
                              ToiletServiceStatus.run
                            ) {
                              checkEditable =
                                (owner?.Id === toiletServicesData?.CustomerId &&
                                  userRole?.Role.includes(
                                    CustomerRole.Coordinator,
                                  )) ||
                                user?.Id === toiletServicesData?.CustomerId;
                            } else checkEditable = false;
                          } else {
                            checkEditable =
                              (owner?.Id === data?.CustomerId &&
                                userRole?.Role.includes(
                                  CustomerRole.Coordinator,
                                )) ||
                              user?.Id === data?.CustomerId ||
                              tk.CustomerId === user?.Id;
                          }
                          return (
                            <ListTile
                              key={tk.Id}
                              disabled={!checkEditable}
                              style={{
                                gap: 8,
                                flexDirection: 'row',
                                paddingLeft: 12,
                                paddingVertical: 8,
                                alignItems: 'center',
                                borderRadius: 8,
                                borderColor:
                                  ColorThemes.light.neutral_bolder_border_color,
                                borderWidth: 1,
                                backgroundColor: !checkEditable
                                  ? ColorThemes.light
                                      .neutral_disable_background_color
                                  : ColorThemes.light
                                      .neutral_absolute_background_color,
                              }}
                              leading={
                                (checked && tk?.RequireCheckin && activity) ||
                                (tk.Status !== TaskStatus.open &&
                                  !checkEditable) ? (
                                  <Winicon
                                    src={
                                      tk.Status == TaskStatus.overdue
                                        ? 'fill/layout/circle-warning'
                                        : 'fill/user interface/c-check'
                                    }
                                    size={14}
                                    color={
                                      tk.Status == TaskStatus.overdue
                                        ? ColorThemes.light.warning_main_color
                                        : ColorThemes.light.success_main_color
                                    }
                                  />
                                ) : null
                              }
                              trailing={
                                _stDate.getDate() === thisDateValue ? (
                                  <Tooltip
                                    enterTouchDelay={0}
                                    leaveTouchDelay={1000}
                                    title="Ngày bắt đầu">
                                    <Winicon
                                      src="outline/sport/chequered-flag"
                                      style={{
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                      }}
                                      size={12}
                                    />
                                  </Tooltip>
                                ) : _endDate.getDate() === thisDateValue ? (
                                  <Tooltip
                                    enterTouchDelay={0}
                                    leaveTouchDelay={1000}
                                    title="Ngày kết thúc">
                                    <Winicon
                                      src="outline/sport/archery-target"
                                      style={{
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                      }}
                                      size={12}
                                    />
                                  </Tooltip>
                                ) : tk.Repeat ? (
                                  <Tooltip
                                    enterTouchDelay={0}
                                    leaveTouchDelay={1000}
                                    title="Công việc lặp lại">
                                    <Winicon
                                      src="outline/arrows/loop-2"
                                      style={{
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                      }}
                                      size={12}
                                    />
                                  </Tooltip>
                                ) : null
                              }
                              onPress={() => showAddEditTask(tk)}
                              title={
                                <Text
                                  style={{
                                    ...TypoSkin.body3,
                                    color:
                                      ColorThemes.light
                                        .neutral_text_title_color,
                                  }}
                                  numberOfLines={3}>
                                  {tk.Name}
                                </Text>
                              }
                            />
                          );
                        })
                    ) : (
                      <View style={{}}>
                        <EmptyPage title="Không có công việc trong hôm nay" />
                      </View>
                    )}
                  </View>
                }
              />
            ) : null;
          })}
        </Pressable>
      </ScrollView>
    </View>
  );
};

//#region Week
const CalendarWeek = ({
  focusDate,
  data,
  serviceData,
  refreshing,
  onRefresh,
  assignees,
  filterMethods,
}: {
  focusDate: Date;
  data: any;
  serviceData: any;
  refreshing?: any;
  onRefresh?: any;
  assignees: Array<any>;
  filterMethods: any;
}) => {
  const focusWeekDay = useMemo(() => focusDate.getDay(), [focusDate]);
  const [tasks, setTasks] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);
  const [activities, setActivities] = useState<Array<any>>([]);

  const getData = async () => {
    const controller = new DataController('Task');
    const startDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth(),
      focusDate.getDate() - focusWeekDay,
    );
    const endDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth(),
      focusDate.getDate() + 6 - focusWeekDay,
      23,
      59,
      59,
      999,
    );
    let queryStatus = [];
    let queryCates: any = [];
    if (filterMethods.watch('StatusIds')?.length)
      filterMethods
        .watch('StatusIds')
        .forEach((e: any, i: number) =>
          queryStatus.push(
            `@Status:[${e} ${e}] ${i == filterMethods.watch('StatusIds').length - 1 ? '' : '|'}`,
          ),
        );
    else
      queryStatus.push(
        `(-@Status:[${TaskStatus.closed} ${TaskStatus.closed}])`,
      );
    if (filterMethods.watch('CateServicesId')?.length) {
      var lst = filterMethods.watch('CateServicesId');
      queryCates = [`@CateServicesId:{${lst.join(' | ')}}`];
    }
    const res = await controller.aggregateList({
      page: 1,
      size: 2000,
      searchRaw: `@ToiletId:{${data.Id}} ${queryStatus.join(' ')} ${queryCates.join(' ')} (@DateStart:[-inf ${endDate.getTime()}]) | (@DateEnd:[${startDate.getTime()} +inf])`,
    });
    if (res.code === 200) {
      const servicesIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }
      setTasks(res.data);
      if (onRefresh) onRefresh(false);
    }
    if (onRefresh) onRefresh(false);
  };

  useEffect(() => {
    getData();
  }, [
    focusDate,
    data,
    serviceData,
    refreshing,
    onRefresh,
    filterMethods.watch('StatusIds'),
    filterMethods.watch('CateServicesId'),
  ]);

  const getActivities = async () => {
    const controller = new DataController('Activity');
    const taskIds = tasks
      .filter((t: any) => t.RequireCheckin)
      .map((e: any) => e.Id)
      .filter(
        (v: any, i: any, a: any | any[]) => v?.length && a.indexOf(v) === i,
      );
    const res = await controller.aggregateList({
      page: 1,
      size: 100,
      searchRaw: `@TaskId:{${taskIds.join(' | ')}}`,
    });
    if (res.code == 200) {
      setActivities(res.data);
    }
  };

  useEffect(() => {
    getActivities();
  }, [tasks]);

  const weekdays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  const methods = useForm({shouldFocusError: false});

  const onChangeTask = async (t: any) => {
    const controller = new DataController('Task');
    const res = await controller.edit([t]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    setTasks((ts: any) => ts?.map((e: any) => (e.Id === t.Id ? t : e)));
    showSnackbar({
      message: 'Chỉnh sửa công việc thành công',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const showAddEditTask = (item: any) => {
    let checkEditable = undefined;
    if (item.ToiletServicesId)
      var toiletServicesData = toiletServices.find(
        e => e.Id === item.ToiletServicesId,
      );
    if (toiletServicesData) {
      if (toiletServicesData.Status < ToiletServiceStatus.run) {
        checkEditable =
          (owner?.Id === toiletServicesData?.CustomerId &&
            userRole?.Role.includes(CustomerRole.Coordinator)) ||
          user?.Id === toiletServicesData?.CustomerId;
      } else checkEditable = false;
    } else {
      checkEditable =
        (owner?.Id === data?.CustomerId &&
          userRole?.Role.includes(CustomerRole.Coordinator)) ||
        user?.Id === data?.CustomerId ||
        item.CustomerId === user?.Id;
    }
    if (item.DateStart) {
      methods.setValue('dateStart', new Date(item.DateStart));
    } else {
      methods.setValue('dateStart', undefined);
    }
    if (item.DateEnd) {
      methods.setValue('dateEnd', new Date(item.DateEnd));
    } else {
      methods.setValue('dateEnd', undefined);
    }

    methods.setValue('Description', `${item?.Description ?? ''}`);
    methods.setValue(
      'CateServicesId',
      `${item?.CateServicesId ?? 'undefined'}`,
    );

    if (checkEditable && item.Type === TaskType.other) {
      methods.setValue('Name', `${item?.Name}`);
    }

    var options = [
      {id: TaskStatus.open, name: 'Mở'},
      // {id: TaskStatus.doing, name: "Đang làm"},
      {id: TaskStatus.done, name: 'Hoàn thành'},
      // { id: TaskStatus.overdue, name: "Quá hạn" },
      {id: TaskStatus.closed, name: 'Đóng'},
    ];
    if (item?.Status < TaskStatus.overdue) {
      options.filter(e => e.id < TaskStatus.overdue);
    }
    methods.setValue(
      'status',
      options.find(e => e.id === item?.Status)?.id ?? options[0].id,
    );
    // if (assignees.length) methods.setValue("assignee", assignees.find(e => e.Id === item?.CustomerId)?.Id ?? assignees[0].Id)

    showPopup({
      ref: popupRef,
      children: (
        <PopupEditTask
          methods={methods}
          options={options}
          toiletId={data.Id}
          ref={popupRef}
          item={item}
          onChange={onChangeTask}
          checkEditable={checkEditable}
        />
      ),
    });
  };

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <View style={{flexDirection: 'row', paddingBottom: 8}}>
        {weekdays.map((item, index) => {
          const thisDate = new Date(
            focusDate.getFullYear(),
            focusDate.getMonth(),
            focusDate.getDate() + index - focusWeekDay,
            0,
            0,
            2,
          );
          const thisDateValue = thisDate.getDate();
          const _taskList = tasks?.filter((t: any) => {
            const _stDate = new Date(t.DateStart);
            const _endDate = new Date(t.DateEnd);
            switch (t.Repeat) {
              case 1:
                return (
                  (_stDate.getDate() === thisDate.getDate() &&
                    Math.abs(differenceInDays(_stDate, thisDate)) < 1) ||
                  (_endDate.getDate() === thisDate.getDate() &&
                    Math.abs(differenceInDays(_endDate, thisDate)) < 1) ||
                  (_stDate.getTime() < thisDate.getTime() &&
                    _endDate.getTime() >= thisDate.getTime())
                );
              case 2:
                return (
                  t.RepeatValue && JSON.parse(t.RepeatValue).includes(index)
                );
              case 3:
                const tmpDate = new Date(
                  focusDate.getFullYear(),
                  focusDate.getMonth(),
                  focusDate.getDate() + index - focusWeekDay,
                );
                tmpDate.setDate(thisDateValue + 1);
                return (
                  t.RepeatValue &&
                  JSON.parse(t.RepeatValue).includes(
                    tmpDate.getMonth() !== thisDate.getMonth()
                      ? 'last'
                      : thisDateValue,
                  )
                );
              default:
                return (
                  (_stDate.getDate() === thisDateValue &&
                    !differenceInDays(_stDate, thisDate)) ||
                  (_endDate.getDate() === thisDateValue &&
                    !differenceInDays(_endDate, thisDate))
                );
            }
          });
          return (
            <TouchableOpacity
              style={{
                height: '100%',
                width: Dimensions.get('screen').width / 7,
                alignItems: 'flex-start',
                gap: 8,
              }}
              key={index}
              onPress={() => {}}>
              <Text
                style={{
                  ...TypoSkin.title3,
                  color:
                    focusWeekDay === index
                      ? ColorThemes.light.primary_main_color
                      : ColorThemes.light.neutral_text_title_color,
                }}>
                {item}
              </Text>
              <View>
                <Text
                  style={{
                    ...TypoSkin.subtitle4,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {thisDateValue}/{thisDate.getMonth() + 1}
                </Text>
                {_taskList?.length != 0 ? (
                  <Text
                    style={{
                      fontSize: 20,
                      color: ColorThemes.light.primary_main_color,
                    }}>
                    *
                  </Text>
                ) : null}
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={() => {
              if (onRefresh) onRefresh(true);
            }}
          />
        }
        style={{flex: 1}}>
        <Pressable style={{gap: 16}}>
          {weekdays.map((_, i) => {
            const thisDate = new Date(
              focusDate.getFullYear(),
              focusDate.getMonth(),
              focusDate.getDate() + i - focusWeekDay,
              0,
              0,
              2,
            );
            const weekdayTitle = weekdays[i];
            const thisDateValue = thisDate.getDate();
            const _taskList = tasks?.filter((t: any) => {
              const _stDate = new Date(t.DateStart);
              const _endDate = new Date(t.DateEnd);
              switch (t.Repeat) {
                case 1:
                  return (
                    (_stDate.getDate() === thisDate.getDate() &&
                      Math.abs(differenceInDays(_stDate, thisDate)) < 1) ||
                    (_endDate.getDate() === thisDate.getDate() &&
                      Math.abs(differenceInDays(_endDate, thisDate)) < 1) ||
                    (_stDate.getTime() < thisDate.getTime() &&
                      _endDate.getTime() >= thisDate.getTime())
                  );
                case 2:
                  return t.RepeatValue && JSON.parse(t.RepeatValue).includes(i);
                case 3:
                  const tmpDate = new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() + i - focusWeekDay,
                  );
                  tmpDate.setDate(thisDateValue + 1);
                  return (
                    t.RepeatValue &&
                    JSON.parse(t.RepeatValue).includes(
                      tmpDate.getMonth() !== thisDate.getMonth()
                        ? 'last'
                        : thisDateValue,
                    )
                  );
                default:
                  return (
                    (_stDate.getDate() === thisDateValue &&
                      !differenceInDays(_stDate, thisDate)) ||
                    (_endDate.getDate() === thisDateValue &&
                      !differenceInDays(_endDate, thisDate))
                  );
              }
            });
            return (
              <ListTile
                key={`dtwk-${i}`}
                leading={
                  <View style={{width: '100%'}}>
                    <View style={{gap: 4}}>
                      <Text
                        style={[
                          styles.heading,
                          {
                            color:
                              focusWeekDay === i
                                ? ColorThemes.light.primary_main_color
                                : ColorThemes.light.neutral_text_title_color,
                          },
                        ]}>
                        {weekdayTitle}
                      </Text>
                      <Text style={{...TypoSkin.subtitle3}}>
                        {thisDateValue}/{thisDate.getMonth() + 1}
                      </Text>
                    </View>
                  </View>
                }
                style={{
                  borderColor: ColorThemes.light.neutral_bolder_border_color,
                  borderWidth: 1,
                }}
                listtileStyle={{alignItems: 'flex-start', gap: 16}}
                title={
                  <View style={{gap: 8, paddingBottom: 8}}>
                    {refreshing ? (
                      <Pressable style={{gap: 16}}>
                        <SkeletonPlaceholder>
                          <SkeletonPlaceholder.Item
                            height={40}
                            width={'100%'}
                            borderRadius={8}
                          />
                        </SkeletonPlaceholder>
                      </Pressable>
                    ) : _taskList?.length ? (
                      _taskList?.map((tk: any) => {
                        const activity = activities
                          ?.filter(
                            e =>
                              e.TaskId === tk?.Id &&
                              e.EndTime &&
                              thisDateValue === new Date(e.EndTime).getDate(),
                          )
                          ?.sort(
                            (a: any, b: any) => b?.EndTime - a?.EndTime,
                          )[0];

                        const checked =
                          (tk?.DeviceId?.split(',')?.length ==
                            activity?.DeviceId?.split(',').length &&
                            tk?.BioProductId?.split(',')?.length ==
                              activity?.BioProductId?.split(',').length &&
                            new Date(activity?.DateCreated).getDate() ===
                              thisDateValue) ??
                          false;
                        const _stDate = new Date(tk.DateStart);
                        const _endDate = new Date(tk.DateEnd);
                        var checkEditable = false;
                        if (tk.ToiletServicesId)
                          var toiletServicesData = toiletServices.find(
                            e => e.Id === tk.ToiletServicesId,
                          );
                        if (toiletServicesData) {
                          if (
                            toiletServicesData.Status < ToiletServiceStatus.run
                          ) {
                            checkEditable =
                              (owner?.Id === toiletServicesData?.CustomerId &&
                                userRole?.Role.includes(
                                  CustomerRole.Coordinator,
                                )) ||
                              user?.Id === toiletServicesData?.CustomerId;
                          } else checkEditable = false;
                        } else {
                          checkEditable =
                            (owner?.Id === data?.CustomerId &&
                              userRole?.Role.includes(
                                CustomerRole.Coordinator,
                              )) ||
                            user?.Id === data?.CustomerId ||
                            tk.CustomerId === user?.Id;
                        }
                        return (
                          <ListTile
                            key={tk.Id}
                            disabled={!checkEditable}
                            style={{
                              gap: 8,
                              flexDirection: 'row',
                              paddingLeft: 12,
                              paddingVertical: 8,
                              alignItems: 'center',
                              borderRadius: 8,
                              borderColor:
                                ColorThemes.light.neutral_bolder_border_color,
                              borderWidth: 1,
                              backgroundColor: !checkEditable
                                ? ColorThemes.light
                                    .neutral_disable_background_color
                                : ColorThemes.light
                                    .neutral_absolute_background_color,
                            }}
                            leading={
                              (checked && tk?.RequireCheckin && activity) ||
                              (tk.Status !== TaskStatus.open &&
                                !checkEditable) ? (
                                <Winicon
                                  src={
                                    tk.Status == TaskStatus.overdue
                                      ? 'fill/layout/circle-warning'
                                      : 'fill/user interface/c-check'
                                  }
                                  size={14}
                                  color={
                                    tk.Status == TaskStatus.overdue
                                      ? ColorThemes.light.warning_main_color
                                      : ColorThemes.light.success_main_color
                                  }
                                />
                              ) : null
                            }
                            trailing={
                              _stDate.getDate() === thisDateValue ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  leaveTouchDelay={1000}
                                  title="Ngày bắt đầu">
                                  <Winicon
                                    src="outline/sport/chequered-flag"
                                    style={{
                                      paddingHorizontal: 12,
                                      paddingVertical: 8,
                                    }}
                                    size={12}
                                  />
                                </Tooltip>
                              ) : _endDate.getDate() === thisDateValue ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  leaveTouchDelay={1000}
                                  title="Ngày kết thúc">
                                  <Winicon
                                    src="outline/sport/archery-target"
                                    style={{
                                      paddingHorizontal: 12,
                                      paddingVertical: 8,
                                    }}
                                    size={12}
                                  />
                                </Tooltip>
                              ) : tk.Repeat ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  leaveTouchDelay={1000}
                                  title="Công việc lặp lại">
                                  <Winicon
                                    src="outline/arrows/loop-2"
                                    style={{
                                      paddingHorizontal: 12,
                                      paddingVertical: 8,
                                    }}
                                    size={12}
                                  />
                                </Tooltip>
                              ) : null
                            }
                            onPress={() => showAddEditTask(tk)}
                            title={
                              <Text
                                style={{
                                  ...TypoSkin.body3,
                                  color:
                                    ColorThemes.light.neutral_text_title_color,
                                }}
                                numberOfLines={3}>
                                {tk.Name}
                              </Text>
                            }
                          />
                        );
                      })
                    ) : (
                      <View
                        style={{
                          gap: 8,
                          paddingVertical: 4,
                        }}>
                        <Text
                          style={{
                            ...TypoSkin.body3,
                            color: ColorThemes.light.neutral_text_title_color,
                          }}>
                          Không có công việc.
                        </Text>
                      </View>
                    )}
                  </View>
                }
              />
            );
          })}
        </Pressable>
      </ScrollView>
    </View>
  );
};

//#region Month
const CalendarMonth = ({
  focusDate,
  data,
  serviceData,
  refreshing,
  onRefresh,
  assignees,
  filterMethods,
}: {
  focusDate: Date;
  data: any;
  serviceData: any;
  refreshing: any;
  onRefresh: any;
  assignees: Array<any>;
  filterMethods: any;
}) => {
  const focusWeekDay = useMemo(() => focusDate.getDay(), [focusDate]);
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);

  const firstDayOfMonth = useMemo(() => {
    return new Date(focusDate.getFullYear(), focusDate.getMonth(), 1);
  }, [focusDate]);
  const [tasks, setTasks] = useState([]);
  const popupRef = useRef<any>();
  const now = new Date();

  const getData = async () => {
    const controller = new DataController('Task');
    const endDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth() + 1,
      1,
    );
    let queryStatus = [];
    let queryCates: any = [];
    if (filterMethods.watch('StatusIds')?.length)
      filterMethods
        .watch('StatusIds')
        .forEach((e: any, i: number) =>
          queryStatus.push(
            `@Status:[${e} ${e}] ${i == filterMethods.watch('StatusIds').length - 1 ? '' : '|'}`,
          ),
        );
    else
      queryStatus.push(
        `(-@Status:[${TaskStatus.closed} ${TaskStatus.closed}])`,
      );
    if (filterMethods.watch('CateServicesId')?.length) {
      var lst = filterMethods.watch('CateServicesId');
      queryCates = [`@CateServicesId:{${lst.join(' | ')}}`];
    }
    const res = await controller.aggregateList({
      page: 1,
      size: 5000,
      searchRaw: `@ToiletId:{${data.Id}} ${queryStatus.join(' ')} ${queryCates.join(' ')} (@DateStart:[-inf ${endDate.getTime()}]) | (@DateEnd:[${firstDayOfMonth.getTime()} +inf])`,
    });

    let tmp = res.data.filter(
      (e: any) => e.DateEnd >= firstDayOfMonth.getTime(),
    );
    if (res.code === 200) {
      const servicesIds = tmp
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }
      setTasks(tmp);
      if (onRefresh) onRefresh(false);
    }
    if (onRefresh) onRefresh(false);
  };

  useEffect(() => {
    getData();
  }, [
    focusDate,
    data,
    serviceData,
    refreshing,
    filterMethods.watch('StatusIds'),
    filterMethods.watch('CateServicesId'),
  ]);

  const [activities, setActivities] = useState<Array<any>>([]);

  const getActivities = async () => {
    const controller = new DataController('Activity');
    const taskIds = tasks
      ?.filter((t: any) => t.RequireCheckin)
      .map((e: any) => e.Id)
      .filter(
        (v: any, i: any, a: any | any[]) => v?.length && a.indexOf(v) === i,
      );
    const res = await controller.aggregateList({
      page: 1,
      size: 100,
      searchRaw: `@TaskId:{${taskIds.join(' | ')}}`,
    });
    if (res.code == 200) {
      setActivities(res.data);
    }
  };

  useEffect(() => {
    getActivities();
  }, [tasks]);

  const weekdays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  const methods = useForm({shouldFocusError: false});

  const onChangeTask = async (t: any) => {
    const controller = new DataController('Task');
    const res = await controller.edit([t]);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    setTasks((ts: any) => ts?.map((e: any) => (e.Id === t.Id ? t : e)));
  };

  const showAddEditTask = (item: any) => {
    let checkEditable = undefined;
    if (item.ToiletServicesId)
      var toiletServicesData = toiletServices.find(
        e => e.Id === item.ToiletServicesId,
      );
    if (toiletServicesData) {
      if (toiletServicesData.Status < ToiletServiceStatus.run) {
        checkEditable =
          (owner?.Id === toiletServicesData?.CustomerId &&
            userRole?.Role.includes(CustomerRole.Coordinator)) ||
          user?.Id === toiletServicesData?.CustomerId;
      } else checkEditable = false;
    } else {
      checkEditable =
        (owner?.Id === data?.CustomerId &&
          userRole?.Role.includes(CustomerRole.Coordinator)) ||
        user?.Id === data?.CustomerId ||
        item.CustomerId === user?.Id;
    }
    if (item.DateStart) {
      methods.setValue('dateStart', new Date(item.DateStart));
    } else {
      methods.setValue('dateStart', undefined);
    }
    if (item.DateEnd) {
      methods.setValue('dateEnd', new Date(item.DateEnd));
    } else {
      methods.setValue('dateEnd', undefined);
    }

    methods.setValue('Description', `${item?.Description ?? ''}`);
    methods.setValue(
      'CateServicesId',
      `${item?.CateServicesId ?? 'undefined'}`,
    );

    if (checkEditable && item.Type === TaskType.other) {
      methods.setValue('Name', `${item?.Name}`);
    }

    var options = [
      {id: TaskStatus.open, name: 'Mở'},
      // {id: TaskStatus.doing, name: "Đang làm"},
      {id: TaskStatus.done, name: 'Hoàn thành'},
      // { id: TaskStatus.overdue, name: "Quá hạn" },
      {id: TaskStatus.closed, name: 'Đóng'},
    ];
    if (item?.Status < TaskStatus.overdue) {
      options.filter(e => e.id < TaskStatus.overdue);
    }
    methods.setValue(
      'status',
      options.find(e => e.id === item?.Status)?.id ?? options[0].id,
    );

    showPopup({
      ref: popupRef,
      children: (
        <PopupEditTask
          methods={methods}
          options={options}
          toiletId={data.Id}
          ref={popupRef}
          item={item}
          onChange={onChangeTask}
          checkEditable={checkEditable}
        />
      ),
    });
  };

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing ?? false}
          onRefresh={() => {
            if (onRefresh) onRefresh(true);
          }}
        />
      }
      style={{flex: 1}}>
      <FPopup ref={popupRef} />
      {Array.from({length: 42}).map((_, i) => {
        let dateNumber =
          (i % 7) + Math.floor(i / 7) * 7 - firstDayOfMonth.getDay();
        const timeValue = new Date(
          focusDate.getFullYear(),
          focusDate.getMonth(),
          dateNumber + 1,
          0,
          0,
          2,
        );
        if (timeValue.getMonth() !== focusDate.getMonth()) return null;
        const thisDateValue = timeValue.getDate();
        const _taskList = tasks.filter((t: any) => {
          const _stDate = new Date(t.DateStart);
          const _endDate = new Date(t.DateEnd);
          switch (t.Repeat) {
            case 1:
              return (
                (_stDate.getDate() === timeValue.getDate() &&
                  Math.abs(differenceInDays(_stDate, timeValue)) < 1) ||
                (_endDate.getDate() === timeValue.getDate() &&
                  Math.abs(differenceInDays(_endDate, timeValue)) < 1) ||
                (_stDate.getTime() < timeValue.getTime() &&
                  _endDate.getTime() >= timeValue.getTime())
              );
            case 2:
              return t.RepeatValue && JSON.parse(t.RepeatValue).includes(i);
            case 3:
              const tmpDate = new Date(
                focusDate.getFullYear(),
                focusDate.getMonth(),
                dateNumber + 1,
              );
              tmpDate.setDate(thisDateValue + 1);
              return (
                t.RepeatValue &&
                JSON.parse(t.RepeatValue).includes(
                  tmpDate.getMonth() !== timeValue.getMonth()
                    ? 'last'
                    : thisDateValue,
                )
              );
            default:
              return (
                _stDate.getDate() === thisDateValue ||
                _endDate.getDate() === thisDateValue
              );
          }
        });
        var focus =
          thisDateValue === now.getDate() && !differenceInDays(timeValue, now);
        return (
          <ListTile
            key={`dtwk-${i}`}
            leading={
              <View style={{width: '100%'}}>
                <View style={{}}>
                  <Text
                    style={[
                      styles.heading,
                      {
                        color: focus
                          ? ColorThemes.light.primary_main_color
                          : ColorThemes.light.neutral_text_subtitle_color,
                      },
                    ]}>
                    Ngày:{' '}
                    {thisDateValue < 10 ? `0${thisDateValue}` : thisDateValue}
                  </Text>
                </View>
              </View>
            }
            style={{
              borderColor: ColorThemes.light.neutral_bolder_border_color,
              borderWidth: 1,
              marginTop: 16,
            }}
            listtileStyle={{alignItems: 'flex-start', gap: 8}}
            title={
              <View style={{gap: 8, flex: 1}}>
                {refreshing ? (
                  <Pressable style={{gap: 16}}>
                    <SkeletonPlaceholder>
                      <SkeletonPlaceholder.Item
                        height={35}
                        width={'100%'}
                        borderRadius={8}
                      />
                    </SkeletonPlaceholder>
                  </Pressable>
                ) : _taskList.length ? (
                  _taskList.map((tk: any) => {
                    const activity = activities
                      ?.filter(
                        e =>
                          e.TaskId === tk.Id &&
                          e.EndTime &&
                          thisDateValue === new Date(e.EndTime).getDate(),
                      )
                      ?.sort((a: any, b: any) => b?.EndTime - a?.EndTime)[0];
                    const checked =
                      (tk?.DeviceId?.split(',')?.length ==
                        activity?.DeviceId?.split(',').length &&
                        tk?.BioProductId?.split(',')?.length ==
                          activity?.BioProductId?.split(',').length &&
                        new Date(activity?.DateCreated).getDate() ===
                          thisDateValue) ??
                      false;
                    const _stDate = new Date(tk.DateStart);
                    const _endDate = new Date(tk.DateEnd);
                    var checkEditable = false;
                    if (tk.ToiletServicesId)
                      var toiletServicesData = toiletServices.find(
                        e => e.Id === tk.ToiletServicesId,
                      );
                    if (toiletServicesData) {
                      if (toiletServicesData.Status < ToiletServiceStatus.run) {
                        checkEditable =
                          (owner?.Id === toiletServicesData?.CustomerId &&
                            userRole?.Role.includes(
                              CustomerRole.Coordinator,
                            )) ||
                          user?.Id === toiletServicesData?.CustomerId;
                      } else checkEditable = false;
                    } else {
                      checkEditable =
                        (owner?.Id === data?.CustomerId &&
                          userRole?.Role.includes(CustomerRole.Coordinator)) ||
                        user?.Id === data?.CustomerId ||
                        tk.CustomerId === user?.Id;
                    }
                    return (
                      <ListTile
                        key={tk.Id}
                        disabled={!checkEditable}
                        style={{
                          gap: 8,
                          flexDirection: 'row',
                          paddingLeft: 12,
                          paddingVertical: 8,
                          alignItems: 'center',
                          borderRadius: 8,
                          borderColor:
                            ColorThemes.light.neutral_bolder_border_color,
                          borderWidth: 1,
                          backgroundColor: !checkEditable
                            ? ColorThemes.light.neutral_disable_background_color
                            : ColorThemes.light
                                .neutral_absolute_background_color,
                        }}
                        leading={
                          (checked && tk?.RequireCheckin && activity) ||
                          (tk.Status !== TaskStatus.open && !checkEditable) ? (
                            <Winicon
                              src={
                                tk.Status == TaskStatus.overdue
                                  ? 'fill/layout/circle-warning'
                                  : 'fill/user interface/c-check'
                              }
                              size={14}
                              color={
                                tk.Status == TaskStatus.overdue
                                  ? ColorThemes.light.warning_main_color
                                  : ColorThemes.light.success_main_color
                              }
                            />
                          ) : null
                        }
                        trailing={
                          _stDate.getDate() === thisDateValue ? (
                            <Tooltip
                              enterTouchDelay={0}
                              leaveTouchDelay={1000}
                              title="Ngày bắt đầu">
                              <Winicon
                                src="outline/sport/chequered-flag"
                                style={{
                                  paddingHorizontal: 8,
                                  paddingVertical: 8,
                                }}
                                size={12}
                              />
                            </Tooltip>
                          ) : _endDate.getDate() === thisDateValue ? (
                            <Tooltip
                              enterTouchDelay={0}
                              leaveTouchDelay={1000}
                              title="Ngày kết thúc">
                              <Winicon
                                src="outline/sport/archery-target"
                                style={{
                                  paddingHorizontal: 8,
                                  paddingVertical: 8,
                                }}
                                size={12}
                              />
                            </Tooltip>
                          ) : tk.Repeat ? (
                            <Tooltip
                              enterTouchDelay={0}
                              leaveTouchDelay={1000}
                              title="Công việc lặp lại">
                              <Winicon
                                src="outline/arrows/loop-2"
                                style={{
                                  paddingHorizontal: 8,
                                  paddingVertical: 8,
                                }}
                                size={12}
                              />
                            </Tooltip>
                          ) : null
                        }
                        onPress={() => showAddEditTask(tk)}
                        title={
                          <Text
                            style={{
                              ...TypoSkin.body3,
                              color: ColorThemes.light.neutral_text_title_color,
                            }}
                            numberOfLines={3}>
                            {tk.Name}
                          </Text>
                        }
                      />
                    );
                  })
                ) : (
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Không có công việc
                  </Text>
                )}
              </View>
            }
          />
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  weekdayContainer: {
    flex: 1,
  },
  weekdayTitle: {
    flex: 1,
  },
  heading: {
    fontWeight: 'bold',
    fontSize: 16,
  },
});
