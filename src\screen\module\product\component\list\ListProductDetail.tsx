import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Pressable,
  RefreshControl,
} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {ManageProductDetailProps} from '../types';
import {useDispatch} from 'react-redux';
import {RootScreen} from '../../../../../router/router';
import {
  CustomSwitch,
  ReverseCustomSwitch,
} from '../../../../../component/switch/switch';
import {ColorThemes} from '../../../../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import {ListManageProductDetailStyles} from '../styles/ManageProductDetail';
import iconSvg from '../../../../../svgs/iconSvg';
import EmptyPage from '../../../../../project-component/empty-page';
import AppSvg from '../../../../../component/AppSvg';
import {ComponentStatus} from '../../../../../component/component-status';
import {
  showDialog,
  showSnackbar,
  FDialog,
  Winicon,
} from '../../../../../component/export-component';
import {Ultis} from '../../../../../utils/Utils';
import productDA from '../../productDA';
import {CustomerRole} from 'redux/reducers/user/da';
import {useSelectorCustomerState} from 'redux/hooks/hooks';
const ManageProductDetail = (props: ManageProductDetailProps) => {
  const {menu, dataShop, partnerId, getAllProductByShopId} = props;
  let [data, setData] = useState<any[]>([]);
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const userRole = useSelectorCustomerState().role;

  // const shopInfo = useSelectorShopState().data;
  const [isOn, setIsOn] = useState(true);
  const [selectSwitchData, setSelectSwitchData] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);
  const dialogRef = useRef<any>(null);
  useEffect(() => {
    if (menu === 'Còn hàng') {
      const hoatDongData = dataShop?.[0]?.data;
      setData(hoatDongData);
    } else if (menu === 'Hết hàng') {
      const hetHangData = dataShop?.[1]?.data;
      setData(hetHangData || []);
    } else if (menu === 'Chờ duyệt') {
      const choDuyetData = dataShop?.[2]?.data;
      setData(choDuyetData || []);
    } else if (menu === 'Vi phạm') {
      const viPhamData = dataShop?.[3]?.data;
      setData(viPhamData || []);
    } else if (menu === 'Ẩn') {
      const anData = dataShop?.[4]?.data;
      setData(anData || []);
    } else {
      setData([]);
    }
  }, [menu, dataShop]);

  const handleEditProduct = (data: any) => {
    if (data) {
      navigation.navigate(RootScreen.CreateProductPartnerPage, {
        dataEdit: data,
        title: 'Chỉnh sửa sản phẩm',
        Id: data.Id,
      });
    }
  };
  const handleCopyProduct = (data: any) => {
    if (data) {
      if (data) {
        navigation.navigate(RootScreen.CreateProductPartnerPage, {
          dataEdit: data,
          title: 'Sao chép sản phẩm',
          Id: data.Id,
        });
      }
    }
  };
  const handleDeleteProduct = async (data: any) => {
    console.log('check-data', data);
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn xoá sản phẩm này?',
      onSubmit: async () => {
        try {
          let response = await productDA.DeleteProductPartner(data?.Id);
          if (response && response.code === 200) {
            showSnackbar({
              message: 'Xóa sản phẩm thành công',
              status: ComponentStatus.SUCCSESS,
            });
            await getAllProductByShopId(partnerId);
          } else {
            showSnackbar({
              message: response?.message || 'Xóa sản phẩm thất bại',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error deleting product:', error);
          showSnackbar({
            message: 'Có lỗi xảy ra khi xóa sản phẩm',
            status: ComponentStatus.ERROR,
          });
        }
      },
    });
  };
  const handleViewViolation = (data: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Lỗi vi phạm',
      onSubmit: async () => {
        showSnackbar({
          message: data,
          status: ComponentStatus.SUCCSESS,
        });
      },
    });
  };
  const handleChangeStatusSwitch = async (item: any, status: number) => {
    try {
      let data = item?.item;
      data.Status = status;
      data.Img = data?.Img?.split('/').pop();
      let res = await productDA.updateStatusProduct(data);
      if (res && res.code == 200) {
        // Update the local item status immediately
        item.Status = status;
        if (status == 1) {
          showSnackbar({
            message: 'Cập nhật trạng thái sản phẩm thành công sang ẩn',
            status: ComponentStatus.SUCCSESS,
          });
          await getAllProductByShopId(partnerId);
        } else if (status == 2) {
          showSnackbar({
            message: 'Cập nhật trạng thái sản phẩm thành công hoạt động',
            status: ComponentStatus.SUCCSESS,
          });
          await getAllProductByShopId(partnerId);
        }
      }
    } catch (error) {
      console.error('Error updating product status:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi cập nhật trạng thái sản phẩm',
        status: ComponentStatus.ERROR,
      });
    }
  };
  const CustomSwitchComponent = (item: any) => {
    return (
      <CustomSwitch
        isOn={isOn}
        onPress={() => handleChangeStatusSwitch(item, 1)}
      />
    );
  };
  const ReverseCustomSwitchComponent = (item: any) => {
    return (
      <ReverseCustomSwitch
        isOn={isOn}
        onPress={() => {
          if (item?.item?.InStock == 0) {
            showSnackbar({
              message:
                'Vui lòng cập nhật số lượng tồn kho trước khi chuyển trạng thái sản phẩm',
              status: ComponentStatus.WARNING,
            });
          } else {
            handleChangeStatusSwitch(item, 2);
          }
        }}
      />
    );
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await getAllProductByShopId(partnerId);
    setRefreshing(false);
  };

  if (data && data.length > 0) {
    return (
      <>
        <FDialog ref={dialogRef} />
        <FlatList
          data={data}
          style={ListManageProductDetailStyles.flatListContainer}
          keyExtractor={item => item.Id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
              progressBackgroundColor="#ffffff"
              progressViewOffset={10}
            />
          }
          showsVerticalScrollIndicator={true}
          bounces={true}
          alwaysBounceVertical={true}
          contentContainerStyle={
            ListManageProductDetailStyles.flatListContentContainer
          }
          renderItem={({item}) => (
            <Pressable style={ListManageProductDetailStyles.productItem}>
              <View style={ListManageProductDetailStyles.productCard}>
                <View style={ListManageProductDetailStyles.imageContainer}>
                  <FastImage
                    source={{uri: item.Img}}
                    style={ListManageProductDetailStyles.productImage}
                  />
                  {item.Discount && item.Discount > 0 ? (
                    <Text style={ListManageProductDetailStyles.discountBadge}>
                      -{item.Discount ? item.Discount : 0}%
                    </Text>
                  ) : (
                    <View />
                  )}
                </View>
                <View style={ListManageProductDetailStyles.productInfo}>
                  <Text style={ListManageProductDetailStyles.productName}>
                    {item.Name}
                  </Text>
                  <View style={ListManageProductDetailStyles.productInfoRow}>
                    <View style={ListManageProductDetailStyles.ratingContainer}>
                      <Text style={ListManageProductDetailStyles.ratingText}>
                        <View style={ListManageProductDetailStyles.ratingIcon}>
                          <Winicon
                            src="fill/user interface/star"
                            size={15}
                            color="#FFD700"
                          />
                        </View>
                        <Text style={ListManageProductDetailStyles.ratingValue}>
                          {item.Star ? item.Star : 0}
                        </Text>
                      </Text>
                      <Text style={ListManageProductDetailStyles.reviews}>
                        (20)
                      </Text>
                    </View>
                    {menu === 'Còn hàng' &&
                      userRole?.Role?.includes(
                        CustomerRole.Owner || CustomerRole.Coordinator,
                      ) && <CustomSwitchComponent item={item} />}
                    {menu === 'Hết hàng' &&
                      userRole?.Role?.includes(
                        CustomerRole.Owner || CustomerRole.Coordinator,
                      ) && <ReverseCustomSwitchComponent item={item} />}
                    {menu === 'Chờ duyệt' && (
                      <View style={ListManageProductDetailStyles.action}>
                        <AppSvg SvgSrc={iconSvg.violent} size={20} />
                        <Text
                          style={ListManageProductDetailStyles.statusPending}>
                          Chờ duyệt
                        </Text>
                      </View>
                    )}
                    {menu === 'Ẩn' && (
                      <ReverseCustomSwitchComponent item={item} />
                    )}
                    {menu === 'Vi phạm' && (
                      <TouchableOpacity
                        style={ListManageProductDetailStyles.action}
                        onPress={() => handleViewViolation(item?.ViolentNote)}>
                        <AppSvg SvgSrc={iconSvg.violentTwo} size={20} />
                        <Text
                          style={ListManageProductDetailStyles.statusViolation}>
                          Xem nội dung vi phạm
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </View>
              <View style={ListManageProductDetailStyles.reviewSell}>
                <View style={ListManageProductDetailStyles.statusContainer}>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={ListManageProductDetailStyles.actionIcon}>
                      <AppSvg SvgSrc={iconSvg.instock} size={20} />
                    </View>
                    <Text style={ListManageProductDetailStyles.actionText}>
                      Tồn kho :
                      {menu === 'Hết hàng'
                        ? 0
                        : item?.InStock
                          ? item?.InStock
                          : 0}
                    </Text>
                  </View>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={ListManageProductDetailStyles.actionIcon}>
                      <AppSvg SvgSrc={iconSvg.like} size={20} />
                    </View>
                    <Text style={ListManageProductDetailStyles.actionText}>
                      Thích : {item?.Like ? item?.Like : 0}
                    </Text>
                  </View>
                </View>
                <View style={ListManageProductDetailStyles.statusContainer}>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={ListManageProductDetailStyles.actionIcon}>
                      <AppSvg SvgSrc={iconSvg.wallet} size={20} />
                    </View>
                    <Text style={ListManageProductDetailStyles.actionText}>
                      Đã bán : {item?.Sold ? item?.Sold : 0}{' '}
                    </Text>
                  </View>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={ListManageProductDetailStyles.actionIcon}>
                      <AppSvg SvgSrc={iconSvg.Price} size={20} />
                    </View>
                    <Text style={ListManageProductDetailStyles.actionText}>
                      Giá :{' '}
                      {Ultis.money(item?.Price) ? Ultis.money(item?.Price) : 0}{' '}
                      đ
                    </Text>
                  </View>
                </View>
              </View>
              {userRole?.Role?.includes(
                CustomerRole.Owner || CustomerRole.Coordinator,
              ) ? (
                <View style={ListManageProductDetailStyles.actionButtons}>
                  <TouchableOpacity
                    style={ListManageProductDetailStyles.actionButton}
                    onPress={() => handleEditProduct(item)}>
                    <Text
                      style={ListManageProductDetailStyles.actionButtonText}>
                      Sửa sản phẩm
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={ListManageProductDetailStyles.actionButton}
                    onPress={() => handleCopyProduct(item)}>
                    <Text
                      style={ListManageProductDetailStyles.actionButtonText}>
                      Sao chép
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      ListManageProductDetailStyles.actionButton,
                      ListManageProductDetailStyles.deleteButton,
                    ]}
                    onPress={() => handleDeleteProduct(item)}>
                    <Text
                      style={ListManageProductDetailStyles.actionButtonText}>
                      Xóa sản phẩm
                    </Text>
                  </TouchableOpacity>
                </View>
              ) : null}
            </Pressable>
          )}
        />
      </>
    );
  } else {
    return (
      <View style={ListManageProductDetailStyles.emptyContainer}>
        <EmptyPage />
      </View>
    );
  }
};
export default ManageProductDetail;
