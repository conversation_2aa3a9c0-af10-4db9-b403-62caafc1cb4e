import React from "react"
import { faChevronDown, faChevronLeft, faChevronRight, faChevronUp } from "@fortawesome/free-solid-svg-icons"
import { differenceInCalendarDays, differenceInMonths, differenceInYears } from "date-fns"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { StyleSheet, TextStyle, TouchableOpacity, View } from "react-native"
import { Text } from "react-native-paper"
import { TypoSkin } from "../../assets/skin/typography"
import { ColorSkin } from "../../assets/skin/colors"

const today = new Date()
const startDate = new Date(
    today.getFullYear() - 100,
    today.getMonth(),
    today.getDate()
)
const endDate = new Date(
    today.getFullYear() + 100,
    today.getMonth(),
    today.getDate()
)

export const inRangeTime = (date: Date, startDate: Date, endDate: Date) => (differenceInCalendarDays(date, startDate) > -1 && differenceInCalendarDays(endDate, date) > -1)

export const enum CalendarType {
    DATE,
MONTH,
    DATETIME,
}

interface CalendarProps {
    value?: Date,
    min?: Date,
    max?: Date,
    onSelect?: (props: Date) => void,
    disabled?: boolean,
    helperText?: string,
    helperTextColor?: string,
    placeholder?: string,
    className?: string,
    style?: TextStyle,
    type: CalendarType,
    showSidebar?: boolean,
    footer?: React.ReactNode
}

interface CalendarState {
    value: Date,
    selectDate?: Date,
    selectMonth: number,
    selectYear: number,
    selectHours: number,
    selectMinutes: number,
    selectSeconds: number,
    type: CalendarType,
}

export class FCalendar extends React.Component<CalendarProps, CalendarState> {
    state: Readonly<CalendarState> = {
        value: this.props.value ?? today,
        selectDate: this.props.value ?? today,
        selectMonth: (this.props.value ?? today).getMonth(),
        selectYear: (this.props.value ?? today).getFullYear(),
        type: CalendarType.DATE,
        selectHours: this.props.value?.getHours() ?? 0,
        selectMinutes: this.props.value?.getMinutes() ?? 0,
        selectSeconds: this.props.value?.getSeconds() ?? 0,
    }

    showDateInMonth() {
        let firstDayOfMonth = new Date(this.state.selectYear, this.state.selectMonth, 1)
        return <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, paddingHorizontal: 12, paddingBottom: 24 }}>
            {Array.from({ length: 7 }).map((_, i) => {
                switch (i) {
                    case 0:
                        var weekdayTitle = 'Sun'
                        break
                    case 1:
                        weekdayTitle = 'Mon'
                        break
                    case 2:
                        weekdayTitle = 'Tue'
                        break
                    case 3:
                        weekdayTitle = 'Wed'
                        break
                    case 4:
                        weekdayTitle = 'Thu'
                        break
                    case 5:
                        weekdayTitle = 'Fri'
                        break
                    case 6:
                        weekdayTitle = 'Sat'
                        break
                    default:
                        weekdayTitle = ''
                        break
                }
                return <Text style={[TypoSkin.title5, { color: '#161C24A3', width: 44, height: 24, textAlign: 'center' }]}>{weekdayTitle}</Text>
            })}
            {Array.from({ length: 42 }).map((_, i) => {
                let dateNumber = (i % 7) + (Math.floor(i / 7) * 7) - firstDayOfMonth.getDay()
                const timeValue = new Date(this.state.selectYear, this.state.selectMonth, dateNumber + 1, this.state.selectHours, this.state.selectMinutes, this.state.selectSeconds)
                const isInMonth = inRangeTime(timeValue, this.props.min ?? startDate, this.props.min ?? endDate) && timeValue.getMonth() === this.state.selectMonth
                const isInNextMonth = differenceInMonths(timeValue, new Date(this.state.selectYear, this.state.selectMonth)) > 0
                let isSelected = false
                let isToday = false
                if (isInMonth) {
                    if (this.state.value.valueOf() === timeValue.valueOf()) {
                        isSelected = true
                    } else if (dateNumber + 1 === today.getDate() && this.state.selectMonth === today.getMonth() && this.state.selectYear === today.getFullYear()) {
                        isToday = true
                    }
                }
                return <View key={timeValue.toString()} style={[{ height: 36, width: 44, alignItems: 'center' }, { opacity: isInMonth ? 1 : 0, display: isInNextMonth ? 'none' : 'flex' }]}>
                    <TouchableOpacity style={[styles.buttonDate, (isToday ? styles.today : isSelected ? styles.selected : {})]}
                        onPress={() => {
                            this.setState({ ...this.state, value: timeValue })
                            if (this.props.onSelect) this.props.onSelect(timeValue)
                        }} >
                        <Text style={[TypoSkin.title5, { color: isToday ? ColorSkin.primary : isSelected ? '#fff' : '#161C24E5' }]}>{timeValue.getDate()}</Text>
                    </TouchableOpacity>
                </View>
            })}
        </View>
    }

    showMonthPicker() {
        return <View style={{ flexDirection: 'row', paddingBottom: 24 }}>
            {/* <Picker
                style={{ flex: 1, height: 150 }}
                selectedValue={`${this.state.selectMonth}`}
                pickerData={Array.from({ length: 12 }).map((_, i) => {
                    switch (i) {
                        case 0:
                            var label = 'January'
                            break;
                        case 1:
                            label = 'February'
                            break;
                        case 2:
                            label = 'March'
                            break;
                        case 3:
                            label = 'April'
                            break;
                        case 4:
                            label = 'May'
                            break;
                        case 5:
                            label = 'June'
                            break;
                        case 6:
                            label = 'July'
                            break;
                        case 7:
                            label = 'August'
                            break;
                        case 8:
                            label = 'September'
                            break;
                        case 9:
                            label = 'October'
                            break;
                        case 10:
                            label = 'November'
                            break;
                        case 11:
                            label = 'December'
                            break;
                        default:
                            label = ''
                            break;
                    }
                    return {
                        label: label,
                        values: `${i}`
                    };
                })}
                onValueChange={(data: any) => { this.setState({ ...this.state, selectMonth: parseInt(data) }) }}
            />
            <Picker
                style={{ flex: 1, height: 150 }}
                selectedValue={`${this.state.selectYear - (this.props.min ?? startDate).getFullYear()}`}
                pickerData={Array.from({ length: differenceInYears(this.props.max ?? endDate, this.props.min ?? startDate) }).map((_, i) => `${(this.props.min ?? startDate).getFullYear() + i}`)}
                onValueChange={(data: any) => { this.setState({ ...this.state, selectYear: parseInt(data) }) }}
            /> */}
        </View>
    }

    getMonthTitle(m?: number) {
        switch (m ?? this.state.selectMonth) {
            case 0:
                return 'January'
            case 1:
                return 'February'
            case 2:
                return 'March'
            case 3:
                return 'April'
            case 4:
                return 'May'
            case 5:
                return 'June'
            case 6:
                return 'July'
            case 7:
                return 'August'
            case 8:
                return 'September'
            case 9:
                return 'October'
            case 10:
                return 'November'
            case 11:
                return 'December'
            default:
                return ''
        }
    }

    render(): React.ReactNode {
        return <View style={[{ gap: 4, minWidth: 382 }, (this.props.style ?? {})]}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', padding: 16 }}>
                <TouchableOpacity style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }} onPress={() => { this.setState({ ...this.state, type: this.state.type === CalendarType.DATE ? CalendarType.MONTH : CalendarType.DATE }) }}>
                    <Text style={TypoSkin.title3}>{`${this.getMonthTitle()} ${this.state.selectYear}`}</Text>
                    <FontAwesomeIcon icon={this.state.type === CalendarType.DATE ? faChevronDown : faChevronUp} color={ColorSkin.primary} size={16} />
                </TouchableOpacity>
                <View style={{ flexDirection: 'row', gap: 16 }}>
                    <TouchableOpacity style={{ width: 24, height: 24, alignItems: 'center', justifyContent: 'center' }} onPress={() => {
                        const newDataVl = new Date(this.state.selectYear, this.state.selectMonth - 1, 1)
                        this.setState({ ...this.state, selectMonth: newDataVl.getMonth(), selectYear: newDataVl.getFullYear() })
                    }}>
                        <FontAwesomeIcon icon={faChevronLeft} color={ColorSkin.primary} size={20} />
                    </TouchableOpacity>
                    <TouchableOpacity style={{ width: 24, height: 24, alignItems: 'center', justifyContent: 'center' }} onPress={() => {
                        const newDataVl = new Date(this.state.selectYear, this.state.selectMonth + 1, 1)
                        this.setState({ ...this.state, selectMonth: newDataVl.getMonth(), selectYear: newDataVl.getFullYear() })
                    }}>
                        <FontAwesomeIcon icon={faChevronRight} color={ColorSkin.primary} size={20} />
                    </TouchableOpacity>
                </View>
            </View>
            {this.state.type === CalendarType.DATE ? this.showDateInMonth() : this.showMonthPicker()}
        </View>
    }
}

const styles = StyleSheet.create({
    selected: {
        backgroundColor: ColorSkin.primary,
        borderRadius: 100,
    },
    today: {
        borderColor: ColorSkin.primary,
        borderRadius: 100,
        borderWidth: 1
    },
    buttonDate: {
        width: 24,
        height: 24,
        alignItems: 'center',
        justifyContent: 'center',
    }
})