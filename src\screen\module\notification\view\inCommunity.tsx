import {
  <PERSON><PERSON><PERSON>,
  FlatList,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import NotificationCard from '../components/card/notification-card';
import {CardNotificationSkeleton} from '../components/card/notification-shimmer';
import {useDispatch} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ComponentStatus} from '../../../../component/component-status';
import {showSnackbar} from '../../../../component/export-component';
import EmptyPage from '../../../../project-component/empty-page';
import {
  useSelectorCustomerState,
  useSelectorNotificationState,
} from '../../../../redux/hooks/hooks';
import {NotificationActions} from '../../../../redux/reducers/notification/reducer';
import {DataController} from '../../../base-controller';

export default function NotifCommunity() {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const noti = useSelectorNotificationState();
  const scrSize = Dimensions.get('screen');
  const [isRefreshing, setRefresh] = useState(false);
  const dispatch = useDispatch<any>();
  const [tab, setTab] = useState(0);

  const getData = () => {
    NotificationActions.getData(dispatch, user?.Id ?? '', {
      page: 1,
      status: tab ? 0 : undefined,
    });
  };

  useEffect(() => {
    if (user) {
      getData();
    }
  }, [user, tab]);

  const onRefresh = async () => {
    setRefresh(true);
    if (user) getData();
    if (noti.onLoading === false) {
      setRefresh(false);
    } else {
      setTimeout(() => {
        setRefresh(false);
      }, 2000);
    }
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <View
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          flex: 1,
          alignContent: 'center',
        }}>
        <View
          style={{
            marginTop: 8,
            height: 40,
            paddingVertical: 4,
            paddingHorizontal: 16,
            gap: 16,
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => {
              setTab(0);
            }}
            style={{
              paddingVertical: 4,
              paddingHorizontal: 8,
              borderRadius: 16,
              backgroundColor:
                tab === 0
                  ? ColorThemes.light.neutral_absolute_reverse_background_color
                  : ColorThemes.light.neutral_absolute_background_color,
              borderColor: ColorThemes.light.neutral_main_border_color,
            }}>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  color:
                    tab === 0
                      ? ColorThemes.light.neutral_absolute_background_color
                      : ColorThemes.light
                          .neutral_absolute_reverse_background_color,
                },
              ]}>
              Tất cả
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setTab(1);
            }}
            style={{
              paddingVertical: 4,
              paddingHorizontal: 8,
              borderRadius: 16,
              backgroundColor:
                tab === 1
                  ? ColorThemes.light.neutral_absolute_reverse_background_color
                  : ColorThemes.light.neutral_absolute_background_color,
              borderColor: ColorThemes.light.neutral_main_border_color,
            }}>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  color:
                    tab === 1
                      ? ColorThemes.light.neutral_absolute_background_color
                      : ColorThemes.light
                          .neutral_absolute_reverse_background_color,
                },
              ]}>
              Chưa đọc
            </Text>
          </TouchableOpacity>
          <View style={{flex: 1}} />
          <TouchableOpacity
            onPress={async () => {
              const controller = new DataController('Notification');
              if (user) {
                const unReadNoti = await controller.getListSimple({
                  page: 1,
                  size: 1000,
                  query: `@CustomerId:{${user.Id}} @Status:[0 0]`,
                });
                if (unReadNoti.code === 200 && unReadNoti.data.length)
                  controller
                    .edit(unReadNoti.data.map((e: any) => ({...e, Status: 1})))
                    .then(res => {
                      if (res.code === 200) getData();
                    });
                showSnackbar({
                  message: 'Đã đọc tất cả',
                  status: ComponentStatus.INFOR,
                });
              }
            }}
            style={{justifyContent: 'center'}}>
            <Text style={{color: ColorThemes.light.neutral_text_title_color}}>
              Đọc tất cả
            </Text>
          </TouchableOpacity>
        </View>
        {noti.data.length === 0 && !noti.onLoading ? (
          <View style={{flex: 1}}>
            <EmptyPage title="Bạn không có thông báo nào" />
          </View>
        ) : (
          <FlatList
            nestedScrollEnabled={true}
            refreshControl={
              <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
            }
            style={{flex: 1, height: '100%', alignContent: 'center'}}
            data={noti.data}
            renderItem={({item, index}) => {
              return <NotificationCard tab={tab} item={item} />;
            }}
            keyExtractor={(item, i) => `${i}`}
            ListEmptyComponent={() => {
              if (noti.onLoading)
                return Array.from({length: 5}).map((_, i) => (
                  <CardNotificationSkeleton key={i} />
                ));
            }}
          />
        )}
      </View>
    </View>
  );
}
