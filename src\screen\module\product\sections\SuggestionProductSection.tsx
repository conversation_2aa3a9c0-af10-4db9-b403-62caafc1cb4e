import React, {useEffect, useState, useCallback, useRef} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {useDispatch, useSelector} from 'react-redux';
import {ProductItem} from '../../../../types/ProductType';
import {Ultis} from '../../../../utils/Utils';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {RootState} from '../../../../redux/store/store';
import {productAction} from '../../../../redux/actions/productAction';
import {navigate, RootScreen} from '../../../../router/router';
import {CartActions} from '../../../../redux/reducers/cart/CartReducer';
import {favoriteProductAction} from '../../../../redux/actions/favoriteProductAction';
import {ColorThemes} from '../../../../assets/skin/colors';
import {updateArrayWithObjects} from '../../../../utils/arrayUtils';
import {get} from '../../../../utils/lodash';
import {TypoSkin} from '../../../../assets/skin/typography';

const {width: screenWidth} = Dimensions.get('window');

// ProductCard component
const ProductCard = ({
  product,
  addToCart,
  onFavorite,
}: {
  product: ProductItem;
  addToCart: (item: ProductItem) => void;
  onFavorite: (id: string) => void;
}) => {
  const getDiscount = () => {
    if (!product.Discount || product.Discount <= 0) return <View />;
    return (
      <View style={styles.discountBadge}>
        <Text style={styles.discountText}>
          -{product.Discount ? product.Discount : 0}%
        </Text>
      </View>
    );
  };

  // Fallback image for invalid URIs
  const defaultImage = 'https://via.placeholder.com/120';

  return (
    <View style={styles.productCard}>
      <View style={styles.imageContainer}>
        <Image
          source={{uri: product.Img || defaultImage}}
          style={styles.productImage}
          onError={() => console.error('Failed to load image:', product.Img)}
        />
        {getDiscount()}
      </View>
      <View style={styles.productDetails}>
        <View style={styles.productNameContainer}>
          <Text style={styles.productName} numberOfLines={1}>
            {product.Name || ''}
          </Text>
          <Text style={styles.productDescription} numberOfLines={2}>
            {product.Description || ''}
          </Text>
        </View>
        <View
          style={[
            styles.priceContainer,
            screenWidth < 375 && {alignItems: 'flex-start'},
          ]}>
          <View style={styles.prices}>
            {product.Discount && product.Discount > 0 ? (
              <View style={styles.priceGroupColumn}>
                <Text style={styles.currentPrice}>
                  {Ultis.money(
                    get(product, 'Price', 0) -
                      (get(product, 'Price', 0) * product.Discount) / 100,
                  )}{' '}
                  đ
                </Text>
                <Text style={styles.originalPrice}>
                  {Ultis.money(get(product, 'Price', 0))} đ
                </Text>
              </View>
            ) : (
              <Text style={styles.currentPrice}>
                {Ultis.money(get(product, 'Price', 0))} đ
              </Text>
            )}
          </View>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.cartButton}
              onPress={() => product.Id && onFavorite(product.Id)}
              activeOpacity={0.7}>
              {product.IsFavorite ? (
                <Winicon src="color/emoticons/heart" size={15} />
              ) : (
                <Winicon src="outline/emoticons/heart" size={15} color="#666" />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.cartButton, {marginLeft: 6}]}
              onPress={() => addToCart(product)}
              activeOpacity={0.7}>
              <Winicon src="outline/shopping/cart" size={15} color="#666" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

// Main ProductSection component
interface SuggestionProductSectionProps {
  title?: string;
  showSeeAll?: boolean;
  onSeeAllPress?: () => void;
  onRefresh?: boolean;
  categoryId?: string;
  isHot?: boolean;
  isLoadmore?: boolean;
  scrollEnabled?: boolean;
  onLoadMoreEnd?: (hasMore: boolean) => void;
}

const SuggestionProductSection: React.FC<SuggestionProductSectionProps> = ({
  title = 'Gợi ý cho bạn',
  showSeeAll = true,
  onSeeAllPress,
  onRefresh = false,
  categoryId,
  isHot = false,
  isLoadmore = false,
  scrollEnabled = true,
  onLoadMoreEnd,
}) => {
  const dispatch = useDispatch();
  const customerHook = useSelectorCustomerState();
  const customer = useSelector((state: RootState) => state.customer.data);
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const isMountedRef = useRef(true);
  const size = 5;
  const customerId = customerHook.data?.Id;

  const loadMoreData = useCallback(async () => {
    if (loading || !hasMoreData || !isMountedRef.current) return;

    setLoading(true);
    try {
      const nextPage = page + 1;

      const config: any = {
        page: nextPage,
        size: size,
      };
      if (categoryId) config.searchRaw += `@CategoryId:{${categoryId}} `;
      if (isHot) config.searchRaw += `@IsHot:{true}`;

      const data = await productAction.find({
        page: nextPage,
        size: size,
      });

      if (!isMountedRef.current) return;

      if (data?.length > 0) {
        setProducts(prevProducts => [...prevProducts, ...data]);
        setPage(nextPage);
        const hasMore = data.length === size;
        setHasMoreData(hasMore);
        if (onLoadMoreEnd) onLoadMoreEnd(hasMore);
      } else {
        setHasMoreData(false);
        if (onLoadMoreEnd) onLoadMoreEnd(false);
      }
    } catch (error) {
      console.error('Error loading more products:', error);
      if (isMountedRef.current) {
        setHasMoreData(false);
        if (onLoadMoreEnd) onLoadMoreEnd(false);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [loading, hasMoreData, page, size, onLoadMoreEnd]);

  // Cleanup effect
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    setPage(1);
    setLoading(false);
    setHasMoreData(true);
    initData();
  }, [onRefresh, categoryId]);

  useEffect(() => {
    if (isLoadmore && !loading && hasMoreData) {
      loadMoreData();
    }
  }, [isLoadmore, loading, hasMoreData, loadMoreData]);

  const initData = async () => {
    if (!isMountedRef.current) return;

    setLoading(true);
    try {
      const params: any = {
        page: 1,
        size: size,
      };

      if (categoryId) params.searchRaw = `@CategoryId:{${categoryId}}`;
      if (isHot) params.searchRaw += `@IsHot:{true}`;
      let data = await productAction.find(params);

      if (!isMountedRef.current) return;

      setProducts(data || []);
      setHasMoreData(data && data.length === size);
    } catch (error) {
      console.error('Error initializing data:', error);
      if (isMountedRef.current) {
        setHasMoreData(false);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const onProductPress = (product: ProductItem) => {
    navigate(RootScreen.DetailProductPage, {id: product.Id});
  };

  const addToCart = (product: ProductItem) => {
    CartActions.addItemToCart(product, 1)(dispatch);
  };

  const onFavoriteProduct = async (productId: string) => {
    if (!customer) {
      console.error('Cannot toggle favorite: Customer not found');
      return;
    }

    const product = products.find(item => item.Id === productId);
    if (!product) {
      console.error('Cannot toggle favorite: Product not found');
      return;
    }

    try {
      let res;
      if (product.IsFavorite) {
        // For removing favorites, we need to find the favorite record first
        // Since we don't have the favorite record ID here, we'll just toggle the UI
        // The actual removal should be handled by the favorite products page
        console.warn(
          'Remove favorite functionality needs to be implemented with favorite record ID',
        );
        res = true; // Temporary - just toggle UI
      } else {
        // Add to favorites
        res = await favoriteProductAction.create({
          ProductId: productId,
          CustomerId: customer.Id,
          Name: get(product, 'Name', ''),
        });
      }

      if (res) {
        const productUpdate = products.find(item => item.Id === productId);
        if (productUpdate) {
          productUpdate.IsFavorite = !productUpdate.IsFavorite;
          const newData = updateArrayWithObjects(products, [productUpdate]);
          setProducts(newData as ProductItem[]);
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };
  if (products.length === 0)
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text>Không có sản phẩm</Text>
      </View>
    );
  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{title}</Text>
        {showSeeAll && (
          <TouchableOpacity style={styles.seeAllButton} onPress={onSeeAllPress}>
            <Text style={styles.seeAllText}>Xem thêm</Text>
            <Text style={styles.seeAllIcon}>→</Text>
          </TouchableOpacity>
        )}
      </View>
      <FlatList
        data={products}
        scrollEnabled={scrollEnabled}
        renderItem={({item: product}) => (
          <TouchableOpacity
            key={product.Id || `product-${Math.random()}`}
            onPress={() => onProductPress(product)}>
            <ProductCard
              product={product}
              addToCart={() => addToCart(product)}
              onFavorite={(id: string) => onFavoriteProduct(id)}
            />
          </TouchableOpacity>
        )}
        keyExtractor={(item, index) => item.Id || `product-${index}`}
        ListFooterComponent={() => {
          return isLoadmore && loading ? (
            <ActivityIndicator
              style={{marginVertical: 10}}
              size="large"
              color={ColorThemes.light.primary_main_color}
            />
          ) : null;
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    color: ColorThemes.light.primary_main_color,
    marginRight: 4,
  },
  seeAllIcon: {
    color: ColorThemes.light.primary_main_color,
    fontSize: 18,
  },
  productCard: {
    flexDirection: 'row',
    borderRadius: 8,
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
    flexShrink: 0,
  },
  productImage: {
    width: screenWidth < 375 ? 100 : 120,
    height: screenWidth < 375 ? 100 : 120,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: ColorThemes.light.secondary5_main_color,
    borderRadius: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  discountText: {
    color: ColorThemes.light.secondary6_darker_color,
    fontWeight: 'bold',
    fontSize: 10,
  },
  productDetails: {
    flex: 1,
    marginLeft: 12,
    minWidth: 0,
  },
  productName: {
    fontSize: screenWidth < 375 ? 14 : 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  productDescription: {
    fontSize: screenWidth < 375 ? 11 : 12,
    color: '#666',
    lineHeight: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    flexWrap: 'wrap',
  },
  prices: {
    flex: 1,
    flexWrap: 'wrap',
  },

  priceGroupColumn: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  currentPrice: {
    fontSize: screenWidth < 375 ? 16 : 18,
    fontWeight: 'bold',
    color: '#ff4d4f',
  },
  originalPrice: {
    fontSize: screenWidth < 375 ? 11 : 12,
    color: '#999',
    marginTop: screenWidth < 375 ? 2 : 0,
    textDecorationLine: 'line-through',
  },
  actionButtons: {
    flexDirection: 'row',
    flexShrink: 0,
  },
  productNameContainer: {
    justifyContent: 'flex-start',
  },
  cartButton: {
    width: 26,
    height: 26,
    backgroundColor: ColorThemes.light.neutral_bolder_background_color,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SuggestionProductSection;
export type {SuggestionProductSectionProps};
