import React from 'react';
import {View, Text, TouchableOpacity, Image} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {AppSvg} from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import {DescriptionImageFormStyles} from '../styles/DescriptionImageFormStyles';
import iconSvg from '../../../../../svgs/iconSvg';

const DescriptionImage = (props: any) => {
  const {image, pickerImg, avataProduct, checkImage, deleteImage} = props;
  return (
    <View style={DescriptionImageFormStyles.section}>
      <Text style={DescriptionImageFormStyles.label}>
        Hình ảnh sản phẩm * ({checkImage?.length ? checkImage?.length : 0}/5)
      </Text>
      <ScrollView style={DescriptionImageFormStyles.detail} horizontal={true}>
        {image &&
          image.length > 0 &&
          image.map((item: any, index: number) => (
            <View key={index} style={{position: 'relative'}}>
              <View style={DescriptionImageFormStyles.imageContent}>
                <FastImage
                  source={{uri: item.path}}
                  style={DescriptionImageFormStyles.image}
                />
              </View>
              <TouchableOpacity
                onPress={() => deleteImage(index)}
                style={DescriptionImageFormStyles.cancelImageButton}>
                <AppSvg SvgSrc={iconSvg.exit} size={24} />
              </TouchableOpacity>
            </View>
          ))}
      </ScrollView>
      <TouchableOpacity
        style={DescriptionImageFormStyles.imagePlaceholder}
        onPress={pickerImg}>
        <Text style={DescriptionImageFormStyles.placeholderText}>Thêm ảnh</Text>
      </TouchableOpacity>
      <View style={DescriptionImageFormStyles.avata}>
        {avataProduct && (
          <View>
            <Image
              source={{uri: avataProduct}}
              style={DescriptionImageFormStyles.avataImage}
            />
            <Text style={DescriptionImageFormStyles.avataText}>
              Ảnh đại diện
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};
export default DescriptionImage;
