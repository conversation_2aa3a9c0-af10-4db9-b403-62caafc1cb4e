import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

interface OrderInfoHeaderProps {
  isLoading: boolean;
  dataSearch: string;
  numberCardSearch: number;
  numberCard: number;
}

const OrderInfoHeader: React.FC<OrderInfoHeaderProps> = ({
  isLoading,
  dataSearch,
  numberCardSearch,
  numberCard,
}) => {
  return (
    <View style={styles.orderInfo}>
      <Text style={styles.title}>Danh sách đơn hàng</Text>
      <Text style={styles.numberOrder}>
        {isLoading
          ? 'Đang tải dữ liệu...'
          : dataSearch?.length > 0
            ? numberCardSearch + ' đơn hàng'
            : numberCard + ' đơn hàng'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  orderInfo: {
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  title: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  numberOrder: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontWeight: '500',
  },
});

export default OrderInfoHeader;
