import { forwardRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { closePopup } from "../../../../../component/popup/popup";
import { Dimensions, SafeAreaView, View } from "react-native";
import { ScreenFooter } from "react-native-screens";
import { ColorThemes } from "../../../../../assets/skin/colors";
import AppButton from "../../../../../component/button";
import { Winicon } from "../../../../../component/export-component";
import ScreenHeader from "../../../../layout/header";
import SelectProductStep from "../../../service/components/form/SelectProductStep";
import SelectMaterials from "../../../service/components/form/SelectMaterials";
import WScreenFooter from "../../../../layout/footer";

export const PopupSelectMaterial = forwardRef(function PopupSelectMaterial(data: { materials: any, onSubmit: any }, ref: any) {
    const { materials, onSubmit } = data
    const methods = useForm({ shouldFocusError: false, defaultValues: { materials: [] } })

    useEffect(() => {
        if (materials.length) methods.setValue("materials", materials)
    }, [materials.length])

    const _onSubmit = (ev: any) => {
        onSubmit(ev.materials)
        closePopup(ref)
    }

    return <SafeAreaView style={{ width: '100%', height: Dimensions.get('window').height - 65, borderTopLeftRadius: 12, borderTopRightRadius: 12, backgroundColor: '#fff' }}>
        <ScreenHeader
            style={{
                backgroundColor: ColorThemes.light.transparent,
                flexDirection: 'row',
                height: 35,
                paddingTop: 8
            }}
            title={`Chọn vật tư`}
            prefix={<View />}
            action={<View style={{ flexDirection: "row", padding: 12, alignItems: "center" }}>
                <Winicon src="outline/layout/xmark" onClick={() => closePopup(ref)} size={20} color={ColorThemes.light.neutral_text_body_color} />
            </View>}
        />
        <View style={{ flex: 1, height: "100%", paddingBottom: 45 }}>
            <SelectMaterials methods={methods} />
        </View>
        <WScreenFooter style={{ flexDirection: 'row', gap: 8, paddingHorizontal: 16, paddingBottom: 16 }}>
            <AppButton
                title={'Xong'}
                backgroundColor={ColorThemes.light.primary_main_color}
                borderColor="transparent"
                containerStyle={{ height: 40, flex: 1, borderRadius: 8, paddingHorizontal: 12, paddingVertical: 5 }}
                onPress={methods.handleSubmit(_onSubmit)}
                textColor={ColorThemes.light.neutral_absolute_background_color}
            />
        </WScreenFooter>
    </SafeAreaView>
});
