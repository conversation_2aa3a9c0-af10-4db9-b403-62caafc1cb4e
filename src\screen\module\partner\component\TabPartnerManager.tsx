import {Text, View} from 'react-native';
import OrderMenu from '../../../pages/ProfilePage/components/menu/OrderMenu';
import {useSelectorCustomerState} from '../../../../redux/hooks/hooks';
import {useOrderData} from '../../../pages/ProfilePage/hooks';
import TabPartnerManagerContent from './TabPartnerManagerContent';
import {store} from '../../../../redux/store/store';
import {useCallback, useEffect, useRef, useState} from 'react';
import {dialogCheckAcc} from '../../../layout/main-layout';
import {FDialog, showSnackbar} from '../../../../component/export-component';
import TabUnregisteredPartnerPage from '../../../pages/ProfilePage/TabUnregisteredPartnerPage';
import {ComponentStatus} from '../../../../component/component-status';
import {CustomerRole, CustomerType} from '../../../../redux/reducers/user/da';
import {useFocusEffect} from '@react-navigation/native';
import {FLoading} from 'wini-mobile-components';

const TabPartnerManager: React.FC<{select: any}> = ({select}) => {
  const customer = useSelectorCustomerState();
  const dialogRef = useRef<any>(null);
  const [isPartner, setIsPartner] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const cusInfo = store.getState().customer.data;
  const userRole = useSelectorCustomerState().role;

  // Custom hooks
  const {orderDetail} = useOrderData(select);
  const cusId = store.getState().customer.data?.Id;
  const checkPartner = async () => {
    if (!cusId) return;
    if (cusInfo && cusId && cusInfo?.Type === CustomerType.partner) {
      setIsPartner(true);
    } else {
      setIsPartner(false);
    }
  };
  // Gọi checkPartner khi focus vào trang
  useFocusEffect(
    useCallback(() => {
      setIsLoading(true);
      checkPartner();
      setIsLoading(false);
    }, [checkPartner]),
  );

  useEffect(() => {
    if (cusId) {
      setIsLoading(true);
      checkPartner();
      setIsLoading(false);
    }
  }, [cusId]);
  useEffect(() => {
    if (!cusId) {
      dialogCheckAcc({ref: dialogRef});
      showSnackbar({
        message: 'Vui lòng đăng nhập để tiếp tục',
        status: ComponentStatus.WARNING,
      });
      return;
    }
  }, [cusId]);
  useEffect(() => {
    console.log('check-userRole', userRole);
  }, [userRole]);
  return (
    <View>
      <FDialog ref={dialogRef} />
      <FLoading visible={isLoading} />
      {cusId && isPartner ? (
        <>
          {userRole?.Role?.includes(
            CustomerRole.Owner || CustomerRole.Coordinator,
          ) ? (
            <OrderMenu
              orderDetail={orderDetail}
              customer={customer}
              type={'shop'}
              style={{marginVertical: 8}}
            />
          ) : null}

          <TabPartnerManagerContent />
        </>
      ) : (
        <TabUnregisteredPartnerPage />
      )}
    </View>
  );
};

export default TabPartnerManager;
