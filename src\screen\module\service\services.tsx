import {
  Text,
  TouchableOpacity,
  View,
  FlatList,
  StyleSheet,
  Image,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {showSnackbar, Winicon} from '../../../component/export-component';
import {useNavigation} from '@react-navigation/native';
import {ComponentStatus} from '../../../component/component-status';
import {
  useSelectorCateServiceState,
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../redux/hooks/hooks';
import ConfigAPI from '../../../config/configApi';
import {RootScreen} from '../../../router/router';
import {TypoSkin} from '../../../assets/skin/typography';
import {CustomerRole, CustomerType} from '../../../redux/reducers/user/da';
import {useEffect, useMemo} from 'react';
import {ServiceActions} from '../../../redux/reducers/cateServices/reducer';
import {useDispatch} from 'react-redux';
import {store} from 'redux/store/store';
import FastImage from 'react-native-fast-image';

export default function AllServices({
  createService = false,
  selectedCate = [],
  setSelectedCate = () => {},
}: any) {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const serviceData = useSelectorCateServiceState().data;
  const userRole = useSelectorCustomerState().role;
  const cusInfo = store.getState().customer.data;

  const services = useMemo(() => {
    if (createService) {
      return (
        serviceData?.filter((service: any) => service.IsService == true) || []
      );
    }
    // Kiểm tra nếu company.Id bằng với ConfigAPI.ktxCompanyId
    if (user?.CompanyProfileId === ConfigAPI.ktxCompanyId) {
      return serviceData || [];
    } else {
      // Danh sách các service cần loại bỏ (không phân biệt hoa thường)
      return serviceData?.filter((service: any) => !service.IsKtx) || [];
    }
  }, [serviceData, company, createService]);

  // Helper function để đảm bảo selectedCate luôn là array
  const safeSelectedCate = useMemo(() => {
    return Array.isArray(selectedCate) ? selectedCate : [];
  }, [selectedCate]);
  const dispatch = useDispatch<any>();
  useEffect(() => {
    if (
      setSelectedCate &&
      user?.CateServicesId?.length &&
      serviceData?.length
    ) {
      const userServices =
        serviceData?.filter((e: any) => user.CateServicesId?.includes(e.Id)) ||
        [];

      setSelectedCate(userServices);
    }
  }, [user, serviceData?.length, setSelectedCate]);

  useEffect(() => {
    if (!serviceData || serviceData.length === 0) {
      ServiceActions.getCateServices(dispatch);
    }
  }, [serviceData, dispatch]);

  const viewFeatureItems = (item: any, index: number) => {
    // Tính toán để xác định item có phải là item cuối cùng trong row không đủ 3 item
    const isLastRow =
      Math.floor(index / 3) === Math.floor((services.length - 1) / 3);
    const itemsInLastRow = services.length % 3;
    const shouldNotFlex = isLastRow && itemsInLastRow !== 0;

    return (
      <TouchableOpacity
        key={`${item.Id}`}
        style={[
          styles.item,
          {
            borderColor: safeSelectedCate.some((e: any) => e.Id === item.Id)
              ? ColorThemes.light.primary_main_color
              : 'transparent',
            borderWidth: 1,
            paddingVertical: setSelectedCate ? 8 : 0,
            borderRadius: 8,
            backgroundColor: ColorThemes.light.transparent,
            flex: shouldNotFlex ? 0 : 1,
            width: shouldNotFlex ? '30%' : undefined,
          },
        ]}
        onPress={() => {
          // Kiểm tra role Owner trước khi cho phép thay đổi trạng thái
          if (setSelectedCate) {
            // Chỉ cho phép thay đổi nếu user có role Owner
            if (
              userRole?.Role?.includes(CustomerRole.Owner) ||
              cusInfo?.Type == CustomerType.partner
            ) {
              const isSelected = safeSelectedCate.some(
                (e: any) => e.Id === item.Id,
              );

              if (isSelected) {
                // Bỏ chọn service
                const updatedServices = safeSelectedCate.filter(
                  (e: any) => e.Id !== item.Id,
                );
                setSelectedCate(updatedServices);
              } else {
                // Chọn service mới
                const updatedServices = [...safeSelectedCate, item];
                setSelectedCate(updatedServices);
              }
            }
            // Nếu không có role Owner, không làm gì cả (giữ nguyên trạng thái)
          } else {
            // Navigate to service workflow
            if (item.RouteName) {
              navigation.push(RootScreen.ServicesWorkFlow, {
                type: item.RouteName,
              });
            } else {
              showSnackbar({
                message: 'Service không khả dụng',
                status: ComponentStatus.ERROR,
              });
            }
          }
        }}>
        <View style={[styles.itemView]}>
          <View style={styles.winiIconContainer}>
            {item.Img ? (
              <Winicon src={`${item.Img}`} size={40} />
            ) : (
              <FastImage
                source={require('../../../assets/logo.png')}
                style={{width: 40, height: 40}}
                resizeMode={FastImage.resizeMode.contain}
              />
            )}
          </View>
          <Text style={styles.itemName}>{item.Name}</Text>
        </View>
        {setSelectedCate &&
          safeSelectedCate.some((e: any) => e.Id === item.Id) && (
            <View style={styles.selectedServiceCheck}>
              <Winicon
                src="fill/layout/circle-check"
                size={16}
                color={ColorThemes.light.primary_main_color}
              />
            </View>
          )}
      </TouchableOpacity>
    );
  };
  // Kiểm tra nếu không có services để hiển thị
  if (!services || services.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.noServiceContainer}>
          <Text style={styles.noServiceText}>Không có dịch vụ khả dụng</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        // key={'_key'}
        scrollEnabled={true}
        keyExtractor={(item, index) => (item?.Id ? `_${item.Id}` : `_${index}`)}
        data={services}
        style={styles.featList}
        numColumns={3}
        columnWrapperStyle={styles.columnWrapper}
        ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
        renderItem={({item, index}) => viewFeatureItems(item, index)}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  featList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  contentContainer: {
    paddingTop: 20,
    paddingBottom: 40,
    flexGrow: 1,
  },
  columnWrapper: {
    gap: 24,
    justifyContent: 'flex-start',
    marginBottom: 24,
    flex: 1,
  },
  itemSeparator: {
    height: 24,
  },
  item: {
    backgroundColor: '#DED5D5FF',
    alignItems: 'center',
    flex: 1,
    minHeight: 80,
    paddingVertical: 12,
    borderRadius: 8,
  },
  itemView: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  winiIconContainer: {
    height: 40,
    width: 40,
    alignItems: 'center',
    borderRadius: 100,
  },
  itemName: {
    ...TypoSkin.heading8,
    color: '#00474F',
  },
  selectedServiceCheck: {
    position: 'absolute',
    padding: 5,
    borderRadius: 24,
    backgroundColor: '#fff',
    right: -10,
    top: -10,
  },
  noServiceContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noServiceText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
  },
});
