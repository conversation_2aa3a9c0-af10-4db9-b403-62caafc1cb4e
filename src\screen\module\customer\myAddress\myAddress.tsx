import {useNavigation, useRoute} from '@react-navigation/native';
import {useRef} from 'react';
import {StyleSheet} from 'react-native';
import {useDispatch} from 'react-redux';
import {ColorThemes} from '../../../../assets/skin/colors';
import {
  AppButton,
  ComponentStatus,
  FDialog,
  FLoading,
  showDialog,
} from 'wini-mobile-components';
import {RootScreen} from '../../../../router/router';
import {SafeAreaView} from 'react-native-safe-area-context';
import TitleHeader from '../../../layout/headers/TitleHeader';
import {CustomerActions} from '../../../../redux/reducers/user/reducer';
import {dialogCheckAcc} from '../../../layout/main-layout';
import WScreenFooter from '../../../layout/footer';
import {useMyAddress} from './hooks/useMyAddress';
import {AddressList} from './components/AddressList';
import {AddressItem} from './types';
import {TEXTS} from './constants';

export default function MyAddress(): JSX.Element {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const route = useRoute<any>();
  const chooseAddress = route.params?.chooseAddress;
  const dialogRef = useRef<any>(null);

  // Use custom hook for address management
  const {addresses, loading, refreshing, user, deleteAddress, refresh} =
    useMyAddress();

  const handleDeleteAddress = async (item: AddressItem) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: TEXTS.DELETE_CONFIRMATION_TITLE,
      onSubmit: async () => {
        await deleteAddress(item.Id);
      },
    });
  };

  const handleEditAddress = (item: AddressItem) => {
    navigation.push(RootScreen.EditAddress, {item});
  };

  const handleSelectAddress = (item: AddressItem) => {
    dispatch(CustomerActions.editAddress({...item, IsDefault: true}, false));
    navigation.goBack();
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <FLoading
        visible={loading}
        avt={require('../../../../assets/appstore.png')}
      />
      <FDialog ref={dialogRef} />
      <TitleHeader title={TEXTS.SCREEN_TITLE} />

      <AddressList
        addresses={addresses}
        chooseAddress={chooseAddress}
        onEdit={handleEditAddress}
        onDelete={handleDeleteAddress}
        onSelect={handleSelectAddress}
        refreshing={refreshing}
        onRefresh={refresh}
      />

      {chooseAddress && addresses.length > 0 ? null : (
        <WScreenFooter>
          <AppButton
            containerStyle={styles.footerButton}
            borderColor="transparent"
            title={TEXTS.CREATE_NEW_ADDRESS}
            onPress={() => {
              if (!user?.Id) {
                dialogCheckAcc({ref: dialogRef});
                return;
              }
              navigation.push(RootScreen.EditAddress);
            }}
          />
        </WScreenFooter>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  footerButton: {
    borderRadius: 8,
    marginHorizontal: 16,
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
  },
});
