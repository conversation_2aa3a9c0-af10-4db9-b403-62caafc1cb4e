import {
  Dimensions,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {useEffect, useState} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import {DataController} from '../base-controller';
import ConfigAPI from '../../config/configApi';
import {FRating} from '../../component/export-component';
import ScreenHeader from '../layout/header';
import {TypoSkin} from '../../assets/skin/typography';
import {Ultis} from '../../utils/Utils';
import RenderHtml from 'react-native-render-html';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import FLoading from '../../component/Loading/FLoading';
import {Image} from 'react-native';

export default function DetailProduct() {
  const [prod, setProd] = useState<any>();
  const route = useRoute<any>();
  const controller = new DataController('Product');
  const [tab, setTab] = useState(0);
  const navigation = useNavigation<any>();
  const {width} = useWindowDimensions();
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    getData();
  }, []);
  const getData = () => {
    if (route.params) {
      setLoading(true);
      controller.getById(route.params.id).then(res => {
        if (res.code === 200 && res.data) setProd(res.data);
        setLoading(false);
        setRefreshing(false);
      });
    }
  };
  return (
    <View style={styles.container}>
      <ScreenHeader
        title={prod?.Name ?? '-'}
        onBack={() => navigation.goBack()}
      />
      {loading ? (
        <FLoading visible={loading} loadFullScreen={false} />
      ) : (
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => {
                setRefreshing(true);
                getData();
              }}
            />
          }>
          {prod?.Imgs ? (
            <SwiperFlatList
              autoplay
              autoplayDelay={3}
              autoplayLoop
              showPagination={true}
              paginationActiveColor={ColorThemes.light.primary_main_color}
              paginationDefaultColor={
                ColorThemes.light.neutral_main_border_color
              }
              paginationStyleItem={{width: 10, height: 10}}
              pagingEnabled
              paginationStyle={{top: 220, left: 0, right: 0}}
              style={{width: Dimensions.get('window').width, height: 250}}
              data={prod?.Imgs.split(',')}
              renderItem={({item, index}) => {
                return (
                  <View
                    key={item}
                    style={{
                      width: Dimensions.get('window').width,
                      height: '100%',
                    }}>
                    <Image
                      onError={() => {}}
                      source={{uri: ConfigAPI.imgUrlId + item}}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                      }}
                    />
                  </View>
                );
              }}
            />
          ) : null}
          <View style={styles.infoContainer}>
            <Text style={styles.title2Text}>{prod?.Name ?? '-'}</Text>
            <View style={styles.rowCenter}>
              <FRating value={5} />
              <Text style={styles.subtitle3Text}>20 đánh giá</Text>
            </View>
            <Text style={styles.body3Text}>{prod?.Description ?? '-'}</Text>
            <Text style={styles.priceText}>{Ultis.money(prod?.Price)}VNĐ</Text>
          </View>
          <View style={styles.tabContainer}>
            <View style={styles.tabHeader}>
              <TouchableOpacity
                onPress={() => setTab(0)}
                style={styles.tabButton}>
                <Text
                  style={[
                    styles.tabText,
                    tab === 0 ? styles.tabTextActive : styles.tabTextInactive,
                  ]}>
                  Chi tiết sản phẩm
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setTab(1)}
                style={styles.tabButton}>
                <Text
                  style={[
                    styles.tabText,
                    tab === 1 ? styles.tabTextActive : styles.tabTextInactive,
                  ]}>
                  Thông số kỹ thuật
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* content tab */}
          <View>
            {prod?.Specifications || prod?.Detail ? (
              <RenderHtml
                contentWidth={width}
                onHTMLLoaded={() => setLoading(false)}
                source={{html: tab ? prod?.Specifications : prod?.Detail}}
                baseStyle={styles.htmlBase}
              />
            ) : null}
          </View>
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {},
  slide1: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#9DD6EB',
  },
  slide2: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#97CAE5',
  },
  slide3: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#92BBD9',
  },
  text: {
    color: '#fff',
    fontSize: 30,
    fontWeight: 'bold',
  },
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollView: {
    flex: 1,
  },
  infoContainer: {
    flex: 1,
    gap: 8,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  title2Text: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  rowCenter: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  subtitle3Text: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  body3Text: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
  },
  priceText: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  tabContainer: {
    flex: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
    borderTopWidth: 1,
    marginTop: 16,
  },
  tabHeader: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
    borderBottomWidth: 1,
  },
  tabButton: {
    paddingVertical: 8,
    flex: 1,
    alignItems: 'center',
  },
  tabText: {
    ...TypoSkin.title3,
  },
  tabTextActive: {
    color: ColorThemes.light.neutral_text_title_color,
  },
  tabTextInactive: {
    color: ColorThemes.light.neutral_text_disabled_color,
  },
  htmlBase: {
    paddingHorizontal: 16,
    paddingTop: 8,
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '400',
    color: ColorThemes.light.neutral_text_body_color,
  },
});
