import {useEffect, useMemo, useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
} from 'redux/hooks/hooks';
import {CustomerRole} from 'redux/reducers/user/da';
import ConfigAPI from 'config/configApi';
import {ToiletServiceStatus} from 'screen/module/service/components/da';
import {DataController} from 'screen/base-controller';
import {ComponentStatus} from 'component/component-status';
import {showSnackbar} from 'component/export-component';
import {closePopup} from 'component/popup/popup';
import CetificateAchievemenDa from 'screen/pages/CetificateAchievementPage/CetificateAchievemenDa';
import {useForm} from 'react-hook-form';
import {RoleDa} from 'screen/module/role/roleDa';

interface UseActionButtonsLogicProps {
  serviceData: any;
  rejectReasons: any[];
  setServiceData: (data: any) => void;
  setSurveyData: (data: any) => void;
  setLoading: (loading: boolean) => void;
  getSurveyData: () => Promise<void>;
  onSubmit: () => void;
  dialogRef: any;
  popupRef: any;
}

export const useActionButtonsLogic = ({
  serviceData,
  setServiceData,
  setLoading,
  getSurveyData,
  onSubmit,
  dialogRef,
  popupRef,
}: UseActionButtonsLogicProps) => {
  const roleDa = new RoleDa();
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      AssigneeId: null,
    },
  });
  const [isKtxUser, setIsKtxUser] = useState<boolean>(false);

  useEffect(() => {
    console.log('methods', methods.getValues('AssigneeId'));
  }, [methods]);

  useEffect(() => {
    const checkIsKtxUser = async () => {
      const roleInfo = await roleDa.getCustomerRole(customer?.Id ?? '');
      setIsKtxUser(roleInfo.isKtx);
    };
    checkIsKtxUser();
  }, [customer]);

  const handleAcceptOrder = async () => {
    try {
      setLoading(true);
      if (serviceData?.Status === ToiletServiceStatus.register) {
        const controller = new DataController('ToiletServices');
        const assigneeId = methods.getValues('AssigneeId');
        if (!assigneeId) {
          showSnackbar({
            message: 'Vui lòng chọn tư vấn viên',
            status: ComponentStatus.ERROR,
          });
          return;
        }
        const res = await controller.edit([
          {
            ...serviceData,
            Status: ToiletServiceStatus.research,
            CustomerId: assigneeId,
          },
        ]);
        if (res.code === 200) {
          await CetificateAchievemenDa.getLogByToiletId(
            serviceData?.ToiletId.split(','),
            `Đơn hàng NetZero đã được tiếp nhận `,
          );
          setServiceData({
            ...serviceData,
            Status: ToiletServiceStatus.research,
            CustomerId: assigneeId,
          });
        }
      } else if (serviceData?.Status === ToiletServiceStatus.research) {
        const checkSurvy = new DataController('Survey');
        const surveyData = await checkSurvy.getListSimple({
          page: 1,
          size: 10000,
          query: `@ToiletId:{${serviceData.ToiletId}}`,
        });

        if (surveyData.code === 200) {
          //check data nếu có item nào mà status khác 2 thì gửi 1 thông báo lỗi vui lòng hoàn thành khảo sát trước khi chuyển sang bước tiếp theo
          const hasIncompleteItems = surveyData.data.some(
            (item: any) => item.Status !== 2,
          );
          if (hasIncompleteItems) {
            showSnackbar({
              message:
                'Vui lòng hoàn thành khảo sát trước khi chuyển sang bước tiếp theo',
              status: ComponentStatus.ERROR,
            });
            return;
          }
        }
        await onSubmit();
        await CetificateAchievemenDa.getLogByToiletId(
          serviceData?.ToiletId.split(','),
          `Đơn hàng NetZero đã được hoàn thành khảo sát và được chuyển sang bước báo giá `,
        );
      }
      if (popupRef?.current) {
        closePopup(popupRef);
      }
    } catch (error) {
      console.log('error', error);
      showSnackbar({
        message: 'Lỗi khi xác nhận đơn hàng',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteSurvey = async () => {
    const controller = new DataController('Survey');
    const formValues = methods.getValues();
    const Files = formValues.Design?.map((e: any) => e.Id).join(',');
    delete formValues.Design;

    const res = await controller.add([{...formValues, Design: Files}]);
    if (res.code !== 200) {
      setLoading(false);
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    } else {
      await getSurveyData();
    }
  };

  const handleCompleteConsulting = async () => {
    const controller = new DataController('ToiletServices');
    const res = await controller.edit([
      {...serviceData, Status: ToiletServiceStatus.run},
    ]);
    if (res.code === 200) navigation.pop();
  };

  const handleRejectOrder = async () => {
    const listToiletID = serviceData?.ToiletId?.split(',');
    const controller = new DataController('ToiletServices');

    // Tạo nhiều object tương ứng với số lượng toilet ID
    const updateData = listToiletID.map((toiletId: string) => ({
      ...serviceData,
      ToiletId: toiletId.trim(), // Sử dụng từng toilet ID riêng biệt
      Status: ToiletServiceStatus.research,
    }));

    const res = await controller.edit(updateData);
    if (res.code === 200) {
      setServiceData({
        ...serviceData,
        Status: ToiletServiceStatus.research,
      });
    }
  };

  return {
    navigation,
    dialogRef,
    popupRef,
    isKtxUser,
    methods,
    handleAcceptOrder,
    handleCompleteSurvey,
    handleCompleteConsulting,
    handleRejectOrder,
  };
};
