import {forwardRef, useRef, useState, useEffect, useMemo} from 'react';
import {
  SafeAreaView,
  Dimensions,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  RefreshControl,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import AppButton from '../../../../component/button';
import {ComponentStatus} from '../../../../component/component-status';
import {
  FCheckbox,
  FDialog,
  HashTag,
  Winicon,
  showDialog,
  showSnackbar,
} from '../../../../component/export-component';
import ListTile from '../../../../component/list-tile/list-tile';
import {closePopup, FPopup, showPopup} from '../../../../component/popup/popup';
import ConfigAPI from '../../../../config/configApi';
import {
  TextFieldForm,
  Fselect1Form,
} from '../../../../project-component/component-form';
import EmptyPage from '../../../../project-component/empty-page';
import {SkeletonImage} from '../../../../project-component/skeleton-img';
import {randomGID, Ultis} from '../../../../utils/Utils';
import {DataController} from '../../../base-controller';
import WScreenFooter from '../../../layout/footer';
import ScreenHeader from '../../../layout/header';
import {
  TaskType,
  TaskStatus,
  TaskRepeatType,
  ToiletServiceStatus,
} from '../../service/components/da';
import {useForm} from 'react-hook-form';
import {StepCircleProgress} from '../../../../component/StepProgress/StepProgress';
import {
  useSelectorCateServiceState,
  useSelectorCustomerCompanyState,
  useSelectorCustomerState,
} from '../../../../redux/hooks/hooks';
import {differenceInDays} from 'date-fns';
import {navigate} from '../../../../router/router';
import {Tooltip} from 'react-native-paper';
import {CustomerRole} from '../../../../redux/reducers/user/da';

export const PopupCheckinout = (data: {
  toiletItem: any;
  tasksData: Array<any>;
  onDone?: any;
  setStepParent: any;
  setCheckinTitle: any;
  checkinTitle: any;
}) => {
  const {
    toiletItem,
    onDone,
    tasksData,
    setStepParent,
    setCheckinTitle,
    checkinTitle,
  } = data;

  const methods = useForm({shouldFocusError: false});
  const dialogRef = useRef<any>();
  const [devices, setDevices] = useState<Array<any>>([]);
  const [bios, setBios] = useState<Array<any>>([]);

  const user = useSelectorCustomerState().data;
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState<Array<any>>([]);
  const [doneActivities, setDoneActivities] = useState<Array<any>>([]);
  const activityController = new DataController('Activity');
  const [checked, setChecked] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(undefined);
  const [step, setStep] = useState(1);
  const now = new Date();

  const activityItem = useMemo(
    () => activities.find((e: any) => e.TaskId === selectedTask?.Id),
    [selectedTask, activities],
  );

  const getActivities = async () => {
    setCheckinTitle('...');
    const startDate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      0,
      0,
      0,
      0,
    );
    const endDate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      23,
      59,
      59,
      999,
    );

    var activities = await activityController.getListSimple({
      query: `@CustomerId:{${user?.Id}} @TaskId:{${selectedTask?.Id}} @DateCreated:[${startDate.getTime()} ${endDate.getTime()}] (-@EndTime:[${startDate.getTime()} +inf])`,
    });
    var doneActivities = await activityController.getListSimple({
      query: `@CustomerId:{${user?.Id}} @TaskId:{${selectedTask?.Id}} @DateCreated:[${startDate.getTime()} ${endDate.getTime()}]`,
    });
    if (doneActivities.code === 200) setDoneActivities(doneActivities.data);
    if (activities.code === 200) {
      if (activities.data?.length) {
        setCheckinTitle('Kết thúc ca');
      }
      setActivities(activities.data);
      if (activities.data && step === 1) {
        setChecked(true);
      }
    } else {
      setCheckinTitle('Vào ca');
    }
  };

  useEffect(() => {
    if (selectedTask) getActivities();
  }, [selectedTask]);

  const onChange = async () => {
    let obj = {};
    if (activityItem)
      obj = {
        ...activityItem,
        DeviceId: methods.getValues('DeviceId')
          ? methods.getValues('DeviceId')
          : undefined,
        BioProductId: methods.getValues('BioProductId')
          ? methods.getValues('BioProductId')
          : undefined,
        EndTime: now.getTime(),
        Description: methods.getValues('Description'),
      };
    else
      obj = {
        Id: randomGID(),
        ...methods.getValues(),
        ToiletId: toiletItem.Id,
        HealthConfirm: checked,
        DateCreated: now.getTime(),
        Sort: 1,
        CustomerId: user?.Id,
        Name: tasksData.find((e: any) => e.Id === methods.getValues('TaskId'))
          .Name,
        CateServicesId: tasksData.find(
          (e: any) => e.Id === methods.getValues('TaskId'),
        ).CateServicesId,
        TaskId: methods.getValues('TaskId'),
      };
    var rs = activityItem
      ? await activityController.edit([obj])
      : await activityController.add([obj]);

    if (rs.code === 200) {
      if (onDone) onDone(true);
      showSnackbar({
        message: activityItem ? 'Kết thúc công việc' : 'Bắt đầu công việc',
        status: ComponentStatus.SUCCSESS,
      });
      methods.reset();
      setStepParent(0);
    } else {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const returnView = () => {
    if (selectedTask && step == 1) {
      return (
        <TaskView
          toiletItem={toiletItem}
          user={user}
          item={selectedTask}
          methods={methods}
          setSelectedTask={setSelectedTask}
        />
      );
    } else {
      switch (step) {
        case 1:
          return (
            <ChooseTaskViewS1
              step={step}
              setSelectedTask={setSelectedTask}
              methods={methods}
              tasks={tasksData}
              toiletItem={toiletItem}
            />
          );
        case 2:
          return (
            <CheckingHealthyViewS2
              step={step}
              methods={methods}
              setChecked={setChecked}
              checked={checked}
            />
          );
        case 3:
          return (
            <DevicesAndBiosS3
              step={step}
              item={selectedTask}
              toiletItem={toiletItem}
              methods={methods}
              activities={doneActivities}
              setDevices={setDevices}
              setBios={setBios}
              checked={checked}
            />
          );
        case 4:
          return (
            <DescriptionViewS4
              step={step}
              methods={methods}
              setChecked={setChecked}
              checked={checked}
            />
          );
        default:
          return <View />;
      }
    }
  };

  const disableNext = () => {
    switch (step) {
      case 1:
        return !methods.getValues('TaskId') || !selectedTask;
      case 2:
        return !checked;
      case 3:
        return devices.length == 0 && bios.length == 0;
      case 4:
        return false;
      default:
        return true;
    }
  };

  useEffect(() => {
    if (checkinTitle == '') {
      setCheckinTitle('Chọn công việc');
      setSelectedTask(undefined);
      setStep(1);
      methods.setValue('TaskId', undefined);
      return;
    } else {
      if (selectedTask) {
        if (activityItem) {
          setCheckinTitle('Kết thúc ca');
        } else {
          setCheckinTitle('Vào ca');
        }
      }
    }
  }, [checkinTitle, selectedTask, activities]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
        style={{flex: 1, height: '100%', width: '100%'}}>
        {step != 1 ? (
          <ListTile
            style={{
              padding: 0,
              paddingBottom: 16,
              paddingHorizontal: 16,
              marginBottom: 16,
              borderBottomColor: ColorThemes.light.neutral_main_border_color,
              borderBottomWidth: 1,
            }}
            leading={
              <StepCircleProgress
                step={step - 1}
                totalStep={activityItem ? 3 : 2}
              />
            }
            title={
              step == 1
                ? ''
                : step === 2
                  ? 'Xác nhận sức khỏe'
                  : step === 3
                    ? 'Thông tin chấm công'
                    : 'Thông tin các thiết bị'
            }
            subtitle=""
          />
        ) : null}
        <View
          style={{
            flex: 1,
            paddingHorizontal: step == 1 && !selectedTask ? 0 : 16,
          }}>
          {returnView()}
        </View>
      </KeyboardAvoidingView>
      {!selectedTask ? null : (
        <WScreenFooter
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingBottom: 24,
          }}>
          {step == 1 ||
          (step == 2 &&
            !activities.some(
              (e: any) => e.TaskId === selectedTask?.Id,
            )) ? null : (
            <AppButton
              title={'Quay lại'}
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                height: 40,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={() => {
                if (step <= 4) {
                  if (step > 0) setStep(step - 1);
                  // setDevices([])
                  // setBios([])
                  // methods.setValue("DeviceId", undefined)
                  // methods.setValue("BioProductId", undefined)
                  return;
                }
              }}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
            />
          )}
          <AppButton
            title={
              (!activities.some((e: any) => e.TaskId === selectedTask?.Id) &&
                step == 2) ||
              step == 4
                ? 'Xong'
                : 'Tiếp tục'
            }
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            disabled={disableNext()}
            containerStyle={{
              height: 40,
              flex: 1,
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={() => {
              // check in
              if (
                !activities.some((e: any) => e.TaskId === selectedTask?.Id) &&
                step == 2
              ) {
                onChange();
                return;
              }
              // check out
              if (step !== 4) {
                if (step == 1 && activityItem) {
                  // if (!checked) return;
                  setChecked(true);
                  setStep(3);
                  return;
                }
                setStep(step + 1);
                return;
              }
              onChange();
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </WScreenFooter>
      )}
    </SafeAreaView>
  );
};

// #region TaskView
const TaskView = ({toiletItem, item, user, setSelectedTask, methods}: any) => {
  const [isOpen, setIsOpen] = useState(true);
  const popupRef = useRef<any>();
  const [checkList, setCheckList] = useState({
    dev: Array<any>(),
    bio: Array<any>(),
  });

  useEffect(() => {
    if (item?.DeviceId) {
      const controllerDevice = new DataController('Device');
      controllerDevice
        .getByListId(item?.DeviceId?.split(','))
        .then(async res => {
          if (res.code === 200) {
            setCheckList((cl: any) => ({...cl, dev: res.data}));
          }
        });
    }
    if (item?.BioProductId) {
      const controllerDevice = new DataController('BioProduct');
      controllerDevice
        .getByListId(item?.BioProductId?.split(','))
        .then(async res => {
          if (res.code === 200) {
            setCheckList((cl: any) => ({...cl, bio: res.data}));
          }
        });
    }
  }, [item]);

  const returnButtonChangeStatus = (item: any) => {
    if (!item) return <View />;

    switch (item?.Status) {
      case TaskStatus.open:
        return (
          <Text
            style={{
              color: ColorThemes.light.infor_main_color,
              backgroundColor: ColorThemes.light.infor_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Đang mở
          </Text>
        );
      // case TaskStatus.doing:
      //     return <Text style={{color: ColorThemes.light.warning_main_color,backgroundColor : ColorThemes.light.warning_background}}>Đang làm</Text>
      case TaskStatus.done:
        return (
          <Text
            style={{
              color: ColorThemes.light.success_main_color,
              backgroundColor: ColorThemes.light.success_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Hoàn thành
          </Text>
        );
      case TaskStatus.overdue:
        return (
          <Text
            style={{
              color: ColorThemes.light.error_main_color,
              backgroundColor: ColorThemes.light.error_background,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Quá hạn
          </Text>
        );
      case TaskStatus.closed:
        return (
          <Text
            style={{
              color: ColorThemes.light.neutral_text_title_color,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            Đã đóng
          </Text>
        );
      default:
        return (
          <Text
            style={{
              color: ColorThemes.light.neutral_text_title_color,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
            }}>
            -
          </Text>
        );
    }
  };
  return (
    <View style={{flex: 1, height: '100%'}}>
      <FPopup ref={popupRef} />
      <ScrollView style={{flex: 1}}>
        <View style={{gap: 16, paddingBottom: 100}}>
          <ListTile
            title={item?.Name ?? '-'}
            titleStyle={[
              TypoSkin.heading6,
              {color: ColorThemes.light.neutral_text_title_color},
            ]}
            style={{
              backgroundColor: ColorThemes.light.transparent,
              paddingHorizontal: 0,
            }}
            trailing={
              <View
                style={{
                  alignItems: 'center',
                  flex: 1,
                  justifyContent: 'center',
                }}>
                {returnButtonChangeStatus(item)}
              </View>
            }
            bottom={
              <View style={{gap: 16, alignItems: 'flex-start'}}>
                <ListTile
                  style={{padding: 0, paddingTop: 16}}
                  listtileStyle={{gap: 16}}
                  leading={
                    <SkeletonImage
                      source={{
                        uri: user?.Img
                          ? user?.Img?.startsWith('https')
                            ? user?.Img
                            : ConfigAPI.imgUrlId + user?.Img
                          : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTU3FcmHH1HtWFPQqC9Z-IK4JdvSWkvswcDfA&s',
                      }} // Remote image
                      style={{
                        width: 40,
                        height: 40,
                        objectFit: 'cover',
                        borderRadius: 100,
                      }}
                    />
                  }
                  title={`Người thực hiện: ${user?.Name ?? '-'}`}
                  titleStyle={[
                    TypoSkin.heading8,
                    {
                      color: ColorThemes.light.neutral_text_title_color,
                      paddingBottom: 4,
                    },
                  ]}
                  subtitle={`Số điện thoại: ${user?.Mobile ?? '-'}`}
                  subTitleStyle={[
                    TypoSkin.subtitle4,
                    {color: ColorThemes.light.neutral_text_subtitle_color},
                  ]}
                />
                <ListTile
                  style={{
                    padding: 0,
                    paddingTop: toiletItem?.Description ? 0 : 16,
                  }}
                  leading={
                    <Winicon
                      src="outline/user interface/time-clock"
                      size={16}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                  }
                  title={
                    <View
                      style={{
                        width: '100%',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      {(() => {
                        var startValue = undefined;
                        var endValue = undefined;
                        if (!item || (!item.DateStart && !item.DateEnd))
                          return <View />;
                        if (item.DateStart)
                          startValue = new Date(item.DateStart);
                        if (item.DateEnd) endValue = new Date(item.DateEnd);
                        return (
                          <View
                            style={{
                              gap: 8,
                              width: '100%',
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={{
                                ...TypoSkin.body3,
                                color:
                                  ColorThemes.light.neutral_text_title_color,
                              }}>
                              {startValue
                                ? `${Ultis.datetoString(startValue, startValue.getSeconds() === 1 ? 'dd/mm/yyyy hh:mm' : 'dd/mm/yyyy')} - `
                                : ''}
                              {endValue
                                ? Ultis.datetoString(
                                    endValue,
                                    endValue.getSeconds() === 59
                                      ? 'dd/mm/yyyy hh:mm'
                                      : 'dd/mm/yyyy',
                                  )
                                : ''}
                            </Text>
                            {item.RepeatValue ? (
                              <Winicon src="outline/arrows/loop-2" size={12} />
                            ) : null}
                          </View>
                        );
                      })()}
                    </View>
                  }
                  titleStyle={[
                    TypoSkin.label4,
                    {color: ColorThemes.light.neutral_text_label_color},
                  ]}
                />
                <ListTile
                  style={{padding: 0}}
                  leading={
                    <Winicon
                      src="outline/user interface/calendar-date"
                      size={16}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                  }
                  title={
                    <Text
                      style={{
                        ...TypoSkin.buttonText4,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}>{`${item?.DateEnd && item?.DateStart ? differenceInDays(new Date(item.DateEnd), new Date(item.DateStart)) : '-'} ngày`}</Text>
                  }
                  titleStyle={[
                    TypoSkin.label4,
                    {color: ColorThemes.light.neutral_text_label_color},
                  ]}
                />
                {item?.Description ? (
                  <Text
                    style={[
                      TypoSkin.title3,
                      {color: ColorThemes.light.neutral_text_body_color},
                    ]}>
                    Ghi chú:
                  </Text>
                ) : null}
                {item?.Description ? (
                  <Text
                    style={[
                      TypoSkin.body3,
                      {color: ColorThemes.light.neutral_text_body_color},
                    ]}>
                    {item?.Description ?? '-'}
                  </Text>
                ) : null}
              </View>
            }
          />
          {/* activities */}
          <ListTile
            style={{
              padding: 0,
              borderTopWidth: 1,
              borderTopColor: ColorThemes.light.neutral_main_border_color,
            }}
            onPress={() => setIsOpen(!isOpen)}
            listtileStyle={{gap: 8, paddingVertical: 8}}
            title={'Xem chi tiết'}
            trailing={
              <Winicon
                src={
                  isOpen
                    ? 'outline/arrows/arrow-sm-down'
                    : 'fill/arrows/arrow-sm-right'
                }
                size={28}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
            }
            bottom={
              !isOpen ? null : (
                <View style={{flex: 1, width: '100%'}}>
                  <PopupActivityView
                    item={item}
                    checkList={checkList}
                    onlyView={true}
                    user={user}
                  />
                </View>
              )
            }
          />
        </View>
      </ScrollView>
    </View>
  );
};

const PopupActivityView = (data: {
  item: any;
  checkList: any;
  onlyView: any;
  user: any;
}) => {
  const {item, checkList, onlyView} = data;
  const [activities, setActivities] = useState<any>({
    data: Array<any>(),
    totalCount: undefined,
  });
  const activityController = new DataController('Activity');
  const user = useSelectorCustomerState().data;
  const now = new Date();

  const getData = async () => {
    const startDate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      0,
      0,
      0,
      0,
    );
    const endDate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      23,
      59,
      59,
      999,
    );
    const res = await activityController.getListSimple({
      page: 1,
      size: 1000,
      query: `@CustomerId:{${user?.Id}} @TaskId:{${item.Id}} @DateCreated:[${startDate.getTime()} ${endDate.getTime()}]`,
    });
    if (res.code === 200) {
      // const customerIds = res.data.filter((e: any) => e.CustomerId).map((e: any) => e.CustomerId).filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i)

      setActivities({data: res.data, totalCount: res.totalCount});
    }
  };

  useEffect(() => {
    if (item) getData();
  }, [item]);

  return (
    <View style={{width: '100%', flex: 1}}>
      {activities.totalCount === 0 ? (
        <EmptyPage title={`Nhà vệ sinh không có tiến độ nào`} />
      ) : (
        <View style={{flex: 1, paddingBottom: 75}}>
          {activities.data.map((act: any, i: number, arr: Array<any>) => {
            const doneActivityList = arr
              .slice(0, i + 1)
              .map((e: any) => [
                e?.DeviceId ? e?.DeviceId.split(',') : [],
                e?.BioProductId ? e?.BioProductId?.split(',') : [],
              ])
              .flat(Infinity)
              .filter(
                (v: any, i: any, a: string | any[]) => a.indexOf(v) === i,
              );
            return (
              <ActivityTile
                key={act.Id}
                act={act}
                customer={user}
                checkList={checkList}
                doneActivityList={doneActivityList}
              />
            );
          })}
        </View>
      )}
    </View>
  );
};

const ActivityTile = ({
  act,
  customer,
  checkList,
  doneActivityList,
}: {
  act: any;
  customer: any;
  checkList: any;
  doneActivityList: Array<any>;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const sumDevices = [
    ...(checkList?.dev.length ? checkList?.dev : []),
    ...(checkList?.bio.length ? checkList?.bio : []),
  ].length;
  const sumDoneDevices = doneActivityList?.length ?? 0;
  console.log('====================================');
  console.log(doneActivityList);
  console.log('====================================');

  const doneAct = sumDevices === sumDoneDevices;
  return (
    <ListTile
      style={{
        padding: 0,
        paddingVertical: 16,
        borderBottomColor: ColorThemes.light.neutral_main_border_color,
        borderBottomWidth: 1,
      }}
      onPress={() => {
        setIsOpen(!isOpen);
      }}
      leading={
        customer?.Img ? (
          <SkeletonImage
            source={{
              uri: customer.Img.startsWith('https')
                ? customer.Img
                : ConfigAPI.imgUrlId + customer?.Img,
            }}
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              objectFit: 'cover',
            }}
          />
        ) : (
          <View
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Ultis.generateDarkColorRgb(),
            }}>
            <Text
              style={{
                color: '#fff',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              {customer?.Name?.substring(0, 1)}
            </Text>
          </View>
        )
      }
      title={customer?.Name ?? '-'}
      titleStyle={{paddingBottom: 4, ...TypoSkin.label3}}
      subtitle={
        <View style={{gap: 4}}>
          <Text
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.neutral_text_title_color,
            }}>{`Thời gian: ${act.DateCreated ? Ultis.datetoString(new Date(act.DateCreated), 'dd/mm/yyyyy hh:mm') : ''} - ${act.EndTime ? Ultis.datetoString(new Date(act.EndTime), 'dd/mm/yyyyy hh:mm') : 'chưa kết ca'}`}</Text>
          <Text
            style={{
              ...TypoSkin.subtitle3,
              color:
                doneAct || act.EndTime
                  ? ColorThemes.light.success_main_color
                  : ColorThemes.light.warning_main_color,
            }}>
            {!act.EndTime
              ? `Công việc cần làm: ${sumDevices - sumDoneDevices}/${sumDevices}`
              : `Công việc hoàn thành: ${sumDoneDevices}/${sumDevices}`}
          </Text>
        </View>
      }
      trailing={
        <Winicon
          src={`outline/arrows/${isOpen ? 'down' : 'right'}-arrow`}
          size={16}
        />
      }
      bottom={
        isOpen ? (
          <View style={{flex: 1, width: '100%', paddingVertical: 16, gap: 8}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                Đủ sức khỏe làm việc:
              </Text>
              <Winicon
                src="fill/layout/circle-check"
                size={18}
                color={ColorThemes.light.primary_main_color}
              />
            </View>
            <Text
              style={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              Chi tiết công việc:
            </Text>
            {[
              ...(checkList?.dev.length ? checkList?.dev : []),
              ...(checkList?.bio.length ? checkList?.bio : []),
            ].map((e, i) => {
              const checked = doneActivityList?.includes(e?.Id);
              return (
                <ListTile
                  key={i}
                  style={{padding: 0, paddingVertical: 4}}
                  leading={
                    <Winicon
                      src={
                        checked
                          ? 'outline/layout/circle-check'
                          : 'outline/layout/circle-half-dashed-check'
                      }
                      size={16}
                      color={
                        checked
                          ? ColorThemes.light.success_main_color
                          : ColorThemes.light.warning_main_color
                      }
                    />
                  }
                  title={e.Name}
                  subtitle={`Số lượng: ${e?.Quantity ?? ''} ${e?.Unit ?? ''}`}
                  trailing={
                    <HashTag
                      title={checked ? 'Hoàn thành' : 'Chưa hoàn thành'}
                      textStyles={{
                        color: checked
                          ? ColorThemes.light.success_main_color
                          : ColorThemes.light.warning_main_color,
                      }}
                      styles={{
                        ...TypoSkin.subtitle3,
                        backgroundColor: checked
                          ? ColorThemes.light.success_background
                          : ColorThemes.light.warning_background,
                      }}
                    />
                  }
                />
              );
            })}
          </View>
        ) : null
      }
    />
  );
};

//#region step 1
const ChooseTaskViewS1 = ({
  step,
  methods,
  tasks,
  setSelectedTask,
  toiletItem,
}: {
  step: any;
  methods: any;
  setSelectedTask: any;
  tasks: Array<any>;
  toiletItem: any;
}) => {
  const [showMonth, setShowMonth] = useState(false);
  const [focusDate, setFocusDate] = useState(new Date());
  const popupRef = useRef<any>();

  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;

  const [today, setToday] = useState(true);

  const monthTitle = useMemo(() => {
    var monthName = '';
    switch (focusDate.getMonth()) {
      case 0:
        monthName = '01';
        break;
      case 1:
        monthName = '02';
        break;
      case 2:
        monthName = '03';
        break;
      case 3:
        monthName = '04';
        break;
      case 4:
        monthName = '05';
        break;
      case 5:
        monthName = '06';
        break;
      case 6:
        monthName = '07';
        break;
      case 7:
        monthName = '08';
        break;
      case 8:
        monthName = '09';
        break;
      case 9:
        monthName = '10';
        break;
      case 10:
        monthName = '11';
        break;
      case 11:
        monthName = '12';
        break;
      default:
        monthName = '';
        break;
    }
    return `Tháng ${monthName}/${focusDate.getFullYear()}`;
  }, [focusDate]);

  return (
    <View style={{flex: 1, paddingHorizontal: 16}}>
      <FPopup ref={popupRef} />
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        <View
          style={{flex: 1, flexDirection: 'row', alignItems: 'center', gap: 8}}>
          <Winicon
            src="outline/arrows/left-arrow"
            size={16}
            onClick={() => {
              if (today) return;
              if (showMonth) {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth() - 1,
                    focusDate.getDate(),
                  ),
                );
              } else {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() - 7,
                  ),
                );
              }
              setToday(false);
            }}
          />
          <Text
            style={{
              paddingHorizontal: 4,
              color: ColorThemes.light.primary_main_color,
            }}
            onPress={() => {
              setFocusDate(new Date());
              setToday(true);
              setShowMonth(false);
            }}>
            Hôm nay
          </Text>
          <Winicon
            src="outline/arrows/right-arrow"
            size={16}
            onClick={() => {
              if (today) return;
              if (showMonth) {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth() + 1,
                    focusDate.getDate(),
                  ),
                );
              } else {
                setFocusDate(
                  new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() + 7,
                  ),
                );
              }
              setToday(false);
            }}
          />
          <Text
            style={{
              ...TypoSkin.body3,
              color: ColorThemes.light.primary_main_color,
            }}>
            {monthTitle}
          </Text>
        </View>
        <View style={{flexDirection: 'row'}}>
          <AppButton
            title={showMonth ? 'Tháng' : 'Tuần'}
            backgroundColor={ColorThemes.light.transparent}
            borderColor="transparent"
            textColor={ColorThemes.light.primary_main_color}
            prefixIcon={
              <Winicon
                src={`outline/${showMonth ? 'user interface/search-zoom-out' : 'layout/circle-check'}`}
                size={16}
                color={ColorThemes.light.primary_main_color}
              />
            }
            onPress={() => {
              if (today) {
                setToday(false);
                return;
              }
              setShowMonth(!showMonth);
            }}
          />
        </View>
      </View>
      {today ? (
        <TodayList
          focusDate={focusDate}
          toiletItem={toiletItem}
          methods={methods}
          setSelectedTask={setSelectedTask}
        />
      ) : showMonth ? (
        <CalendarMonth
          focusDate={focusDate}
          toiletItem={toiletItem}
          methods={methods}
          setSelectedTask={setSelectedTask}
        />
      ) : (
        <CalendarWeek
          focusDate={focusDate}
          toiletItem={toiletItem}
          methods={methods}
          setSelectedTask={setSelectedTask}
        />
      )}
    </View>
  );
};

//#region calendar Today
const TodayList = ({
  focusDate,
  toiletItem,
  setSelectedTask,
  methods,
}: {
  focusDate: Date;
  toiletItem: any;
  setSelectedTask: any;
  methods: any;
}) => {
  const [refreshing, onRefresh] = useState(false);
  const [activities, setActivities] = useState<Array<any>>([]);

  const [tasks, setTasks] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);
  const now = new Date();
  const getData = async () => {
    const controller = new DataController('Task');
    const activityController = new DataController('Activity');

    const startDate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      0,
      0,
      0,
      0,
    );
    const endDate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      23,
      59,
      59,
      999,
    );
    // (@DateStart:[-inf ${endDate.getTime()}]) | (@DateEnd:[${startDate.getTime()} +inf])
    // (@DateStart:[${startDate.getTime()} ${startDate.getTime()}]) | (@DateEnd:[${startDate.getTime()} ${startDate.getTime()}])
    let query = `@ToiletId:{${toiletItem.Id}} @CustomerId:{${user?.Id}} @Type:[6] @Status:[1] @RequireCheckin:{true} @DateEnd:[${startDate.getTime()} +inf]`;
    const res = await controller.getListSimple({
      page: 1,
      size: 2000,
      query: query,
    });

    if (res.code === 200) {
      const servicesIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }

      const act = await activityController.getListSimple({
        query: `@CustomerId:{${user?.Id}} @DateCreated:[${startDate.getTime()} ${endDate.getTime()}]`,
      });

      if (act.code === 200) setActivities(act.data);
      if (res.data?.length > 0) {
        const data = res.data.filter(
          (task: any) =>
            new Date(task.DateStart).getDate() === now.getDate() || task.Repeat,
        );
        setTasks(data);
      }
      onRefresh(false);
    }
    onRefresh(false);
  };

  useEffect(() => {
    getData();
  }, [focusDate, toiletItem]);

  return (
    <View style={{flex: 1, width: '100%'}}>
      <FPopup ref={popupRef} />
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={() => {
              onRefresh(true);
              getData();
            }}
          />
        }
        style={{flex: 1}}>
        <Pressable style={{gap: 16, flex: 1, width: '100%'}}>
          {(() => {
            const thisDate = new Date(
              focusDate.getFullYear(),
              focusDate.getMonth(),
              focusDate.getDate(),
              0,
              0,
              2,
            );
            const thisDateValue = thisDate.getDate();
            return (
              <View style={{gap: 8, paddingBottom: 8, marginTop: 16}}>
                {tasks?.length ? (
                  tasks?.map((tk: any) => {
                    const _stDate = new Date(tk.DateStart);
                    const _endDate = new Date(tk.DateEnd);
                    var checkEditable = false;
                    if (tk.ToiletServicesId)
                      var toiletServicesData = toiletServices.find(
                        e => e.Id === tk.ToiletServicesId,
                      );
                    if (toiletServicesData) {
                      if (toiletServicesData.Status < ToiletServiceStatus.run) {
                        checkEditable =
                          (owner?.Id === toiletServicesData?.CustomerId &&
                            userRole?.Role.includes(
                              CustomerRole.Coordinator,
                            )) ||
                          user?.Id === toiletServicesData?.CustomerId;
                      } else checkEditable = false;
                    } else {
                      checkEditable =
                        (owner?.Id === toiletItem?.CustomerId &&
                          userRole?.Role.includes(CustomerRole.Coordinator)) ||
                        user?.Id === toiletItem?.CustomerId ||
                        tk.CustomerId === user?.Id;
                    }
                    // ngày khac sẽ không thể click
                    if (now.getDate() != thisDateValue) checkEditable = false;

                    var startValue = undefined;
                    var endValue = undefined;
                    if (tk.DateStart) startValue = new Date(tk.DateStart);
                    if (tk.DateEnd) endValue = new Date(tk.DateEnd);

                    return (
                      <ListTile
                        key={tk.Id}
                        onPress={
                          checkEditable
                            ? () => {
                                setSelectedTask(tk);
                                methods.setValue('TaskId', tk.Id);
                              }
                            : undefined
                        }
                        style={{
                          padding: 0,
                          borderRadius: 8,
                          borderColor:
                            ColorThemes.light.neutral_bolder_border_color,
                          borderWidth: 1,
                          backgroundColor: !checkEditable
                            ? ColorThemes.light.neutral_disable_background_color
                            : ColorThemes.light
                                .neutral_absolute_background_color,
                        }}
                        listtileStyle={{
                          paddingHorizontal: 16,
                          paddingVertical: 8,
                        }}
                        isClickLeading
                        trailing={
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              gap: 8,
                            }}>
                            <SkeletonImage
                              source={{
                                uri: user?.Img
                                  ? user?.Img?.startsWith('https')
                                    ? user?.Img
                                    : ConfigAPI.imgUrlId + user?.Img
                                  : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTU3FcmHH1HtWFPQqC9Z-IK4JdvSWkvswcDfA&s',
                              }} // Remote image
                              style={{
                                width: 24,
                                height: 24,
                                objectFit: 'cover',
                                borderRadius: 100,
                              }}
                            />
                            {_stDate.getDate() === thisDateValue ? (
                              <Tooltip
                                enterTouchDelay={0}
                                leaveTouchDelay={1000}
                                title="Ngày bắt đầu">
                                <Winicon
                                  src="outline/sport/chequered-flag"
                                  style={{
                                    paddingHorizontal: 12,
                                    paddingVertical: 8,
                                  }}
                                  size={12}
                                />
                              </Tooltip>
                            ) : _endDate.getDate() === thisDateValue ? (
                              <Tooltip
                                enterTouchDelay={0}
                                leaveTouchDelay={1000}
                                title="Ngày kết thúc">
                                <Winicon
                                  src="outline/sport/archery-target"
                                  style={{
                                    paddingHorizontal: 12,
                                    paddingVertical: 8,
                                  }}
                                  size={12}
                                />
                              </Tooltip>
                            ) : tk.Repeat ? (
                              <Tooltip
                                enterTouchDelay={0}
                                leaveTouchDelay={1000}
                                title="Công việc lặp lại">
                                <Winicon
                                  src="outline/arrows/loop-2"
                                  style={{
                                    paddingHorizontal: 12,
                                    paddingVertical: 8,
                                  }}
                                  size={12}
                                />
                              </Tooltip>
                            ) : null}
                          </View>
                        }
                        title={
                          <Text
                            style={{
                              ...TypoSkin.heading7,
                              color: ColorThemes.light.neutral_text_title_color,
                            }}>
                            {tk.Name}
                          </Text>
                        }
                        subtitle={
                          <View style={{paddingTop: 4}}>
                            {(startValue && startValue.getSeconds() === 1) ||
                            (endValue && endValue.getSeconds() === 59) ? (
                              <Text
                                style={{
                                  ...TypoSkin.label4,
                                  color:
                                    ColorThemes.light
                                      .neutral_text_subtitle_color,
                                }}>
                                Thời gian:
                                {startValue
                                  ? `${Ultis.datetoString(startValue, 'hh:mm')} - `
                                  : ''}
                                {endValue
                                  ? Ultis.datetoString(endValue, 'hh:mm')
                                  : ''}
                              </Text>
                            ) : (
                              <Text
                                style={{
                                  ...TypoSkin.label4,
                                  color:
                                    ColorThemes.light
                                      .neutral_text_subtitle_color,
                                }}>
                                Thời gian: Cả ngày
                              </Text>
                            )}
                          </View>
                        }
                        bottom={(() => {
                          const activity = activities
                            ?.filter(
                              (e: any) => e?.TaskId === tk.Id && e?.EndTime,
                            )
                            .sort((a: any, b: any) => a?.EndTime - b?.EndTime);
                          if (!activity) return null;
                          const doneActivityList = activity
                            .map((e: any) => [
                              e?.DeviceId ? e?.DeviceId.split(',') : [],
                              e?.BioProductId
                                ? e?.BioProductId?.split(',')
                                : [],
                            ])
                            .flat(Infinity)
                            .filter(
                              (v: any, i: any, a: string | any[]) =>
                                a.indexOf(v) === i,
                            );
                          const countDeviceTk = [
                            ...(tk?.DeviceId?.split(',')
                              ? tk?.DeviceId?.split(',')
                              : []),
                            ...(tk?.BioProductId?.split(',')
                              ? tk?.BioProductId?.split(',')
                              : []),
                          ].length;
                          const countDeviceActivity = doneActivityList.length;
                          const isDone = countDeviceTk === countDeviceActivity;
                          return (
                            <View
                              style={{
                                gap: 8,
                                flexDirection: 'row',
                                alignItems: 'center',
                                flex: 1,
                                paddingHorizontal: 16,
                                paddingVertical: 8,
                                alignContent: 'flex-start',
                                width: '100%',
                                backgroundColor:
                                  ColorThemes.light
                                    .neutral_disable_background_color,
                              }}>
                              <Winicon
                                src={
                                  isDone
                                    ? 'fill/layout/circle-check'
                                    : 'outline/layout/circle-check'
                                }
                                size={16}
                                color={
                                  isDone
                                    ? ColorThemes.light.primary_main_color
                                    : ColorThemes.light
                                        .neutral_text_subtitle_color
                                }
                              />
                              <Text
                                style={{
                                  ...TypoSkin.buttonText4,
                                  color:
                                    ColorThemes.light.neutral_text_title_color,
                                }}>
                                {countDeviceActivity}/{countDeviceTk} thiết bị
                              </Text>
                            </View>
                          );
                        })()}
                      />
                    );
                  })
                ) : (
                  <View
                    style={{
                      flex: 1,
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: Dimensions.get('screen').height / 2,
                    }}>
                    <EmptyPage title="Không có công việc nào trong hôm nay" />
                  </View>
                )}
              </View>
            );
          })()}
        </Pressable>
      </ScrollView>
    </View>
  );
};

//#region Calendar Week
const CalendarWeek = ({
  focusDate,
  toiletItem,
  setSelectedTask,
  methods,
}: {
  focusDate: Date;
  toiletItem: any;
  setSelectedTask: any;
  methods: any;
}) => {
  const focusWeekDay = useMemo(() => focusDate.getDay(), [focusDate]);
  const [refreshing, onRefresh] = useState(false);

  const [tasks, setTasks] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);
  const [activities, setActivities] = useState<Array<any>>([]);

  const now = new Date();
  const getData = async () => {
    const controller = new DataController('Task');
    const activityController = new DataController('Activity');

    const startDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth(),
      focusDate.getDate() - focusWeekDay,
    );
    const endDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth(),
      focusDate.getDate() + 6 - focusWeekDay,
      23,
      59,
      59,
      999,
    );
    let query = `@ToiletId:{${toiletItem.Id}} @CustomerId:{*${user?.Id}*} @Type:[6] @Status:[1] @RequireCheckin:{true} (@DateStart:[-inf ${endDate.getTime()}] | @DateEnd:[${startDate.getTime()} +inf])`;
    const res = await controller.aggregateList({
      page: 1,
      size: 2000,
      searchRaw: query,
    });
    if (res.code === 200) {
      const servicesIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }
      const act = await activityController.getListSimple({
        page: 1,
        size: 1000,
        query: `@CustomerId:{${user?.Id}} (@DateStart:[-inf ${endDate.getTime()}]) | (@EndTime:[${startDate.getTime()} +inf])`,
      });

      if (act.code === 200) setActivities(act.data);
      setTasks(res.data);
      onRefresh(false);
    }
    onRefresh(false);
  };

  useEffect(() => {
    getData();
  }, [focusDate, toiletItem]);

  const weekdays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  return (
    <View style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <View style={{flexDirection: 'row', paddingBottom: 8}}>
        {weekdays.map((item, index) => {
          const thisDate = new Date(
            focusDate.getFullYear(),
            focusDate.getMonth(),
            focusDate.getDate() + index - focusWeekDay,
            0,
            0,
            2,
          );
          const thisDateValue = thisDate.getDate();
          const _taskList = tasks.filter((t: any) => {
            const _stDate = new Date(t.DateStart);
            const _endDate = new Date(t.DateEnd);
            switch (t.Repeat) {
              case 1:
                return (
                  (_stDate.getDate() === thisDate.getDate() &&
                    Math.abs(differenceInDays(_stDate, thisDate)) < 1) ||
                  (_endDate.getDate() === thisDate.getDate() &&
                    Math.abs(differenceInDays(_endDate, thisDate)) < 1) ||
                  (_stDate.getTime() < thisDate.getTime() &&
                    _endDate.getTime() >= thisDate.getTime())
                );
              case 2:
                return (
                  t.RepeatValue && JSON.parse(t.RepeatValue).includes(index)
                );
              case 3:
                const tmpDate = new Date(
                  focusDate.getFullYear(),
                  focusDate.getMonth(),
                  focusDate.getDate() + index - focusWeekDay,
                );
                tmpDate.setDate(thisDateValue + 1);
                return (
                  t.RepeatValue &&
                  JSON.parse(t.RepeatValue).includes(
                    tmpDate.getMonth() !== thisDate.getMonth()
                      ? 'last'
                      : thisDateValue,
                  )
                );
              default:
                return (
                  (_stDate.getDate() === thisDateValue &&
                    !differenceInDays(_stDate, thisDate)) ||
                  (_endDate.getDate() === thisDateValue &&
                    !differenceInDays(_endDate, thisDate))
                );
            }
          });
          return (
            <TouchableOpacity
              style={{
                height: '100%',
                width: Dimensions.get('screen').width / 7,
                alignItems: 'flex-start',
                gap: 8,
              }}
              key={index}
              onPress={() => {}}>
              <Text
                style={{
                  ...TypoSkin.title3,
                  color:
                    focusWeekDay === index
                      ? ColorThemes.light.primary_main_color
                      : ColorThemes.light.neutral_text_title_color,
                }}>
                {item}
              </Text>
              <View>
                <Text
                  style={{
                    ...TypoSkin.subtitle4,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {thisDateValue}/{thisDate.getMonth() + 1}
                </Text>
                {_taskList.length != 0 ? (
                  <Text
                    style={{
                      fontSize: 20,
                      color: ColorThemes.light.primary_main_color,
                    }}>
                    *
                  </Text>
                ) : null}
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing ?? false}
            onRefresh={() => {
              onRefresh(true);
              getData();
            }}
          />
        }
        style={{flex: 1}}>
        <Pressable style={{gap: 16, paddingVertical: 16}}>
          {Array.from({length: 7}).map((_, i) => {
            const thisDate = new Date(
              focusDate.getFullYear(),
              focusDate.getMonth(),
              focusDate.getDate() + i - focusWeekDay,
              0,
              0,
              2,
            );
            const weekdayTitle = weekdays[i];
            const thisDateValue = thisDate.getDate();
            const _taskList = tasks.filter((t: any) => {
              const _stDate = new Date(t.DateStart);
              const _endDate = new Date(t.DateEnd);
              switch (t.Repeat) {
                case 1:
                  return (
                    (_stDate.getDate() === thisDate.getDate() &&
                      Math.abs(differenceInDays(_stDate, thisDate)) < 1) ||
                    (_endDate.getDate() === thisDate.getDate() &&
                      Math.abs(differenceInDays(_endDate, thisDate)) < 1) ||
                    (_stDate.getTime() < thisDate.getTime() &&
                      _endDate.getTime() >= thisDate.getTime())
                  );
                case 2:
                  return t.RepeatValue && JSON.parse(t.RepeatValue).includes(i);
                case 3:
                  const tmpDate = new Date(
                    focusDate.getFullYear(),
                    focusDate.getMonth(),
                    focusDate.getDate() + i - focusWeekDay,
                  );
                  tmpDate.setDate(thisDateValue + 1);
                  return (
                    t.RepeatValue &&
                    JSON.parse(t.RepeatValue).includes(
                      tmpDate.getMonth() !== thisDate.getMonth()
                        ? 'last'
                        : thisDateValue,
                    )
                  );
                default:
                  return (
                    (_stDate.getDate() === thisDateValue &&
                      !differenceInDays(_stDate, thisDate)) ||
                    (_endDate.getDate() === thisDateValue &&
                      !differenceInDays(_endDate, thisDate))
                  );
              }
            });
            return (
              <ListTile
                key={`dtwk-${i}`}
                leading={
                  <View style={{width: '100%'}}>
                    <View style={{gap: 4}}>
                      <Text
                        style={[
                          styles.heading,
                          {
                            color:
                              focusWeekDay === i
                                ? ColorThemes.light.primary_main_color
                                : ColorThemes.light.neutral_text_title_color,
                          },
                        ]}>
                        {weekdayTitle}
                      </Text>
                      <Text style={{...TypoSkin.subtitle3}}>
                        {thisDateValue}/{thisDate.getMonth() + 1}
                      </Text>
                    </View>
                  </View>
                }
                style={{
                  borderColor: ColorThemes.light.neutral_bolder_border_color,
                  borderWidth: 1,
                }}
                listtileStyle={{alignItems: 'flex-start', gap: 16}}
                title={
                  <View style={{gap: 8, paddingBottom: 8}}>
                    {_taskList.length ? (
                      _taskList.map((tk: any) => {
                        const _stDate = new Date(tk.DateStart);
                        const _endDate = new Date(tk.DateEnd);
                        var checkEditable = false;
                        if (tk.ToiletServicesId)
                          var toiletServicesData = toiletServices.find(
                            e => e.Id === tk.ToiletServicesId,
                          );
                        if (toiletServicesData) {
                          if (
                            toiletServicesData.Status < ToiletServiceStatus.run
                          ) {
                            checkEditable =
                              (owner?.Id === toiletServicesData?.CustomerId &&
                                userRole?.Role.includes(
                                  CustomerRole.Coordinator,
                                )) ||
                              user?.Id === toiletServicesData?.CustomerId;
                          } else checkEditable = false;
                        } else {
                          checkEditable =
                            (owner?.Id === toiletItem?.CustomerId &&
                              userRole?.Role.includes(
                                CustomerRole.Coordinator,
                              )) ||
                            user?.Id === toiletItem?.CustomerId ||
                            tk.CustomerId === user?.Id;
                        }
                        // ngày khac sẽ không thể click
                        if (now.getDate() != thisDateValue)
                          checkEditable = false;

                        var startValue = undefined;
                        var endValue = undefined;
                        if (tk.DateStart) startValue = new Date(tk.DateStart);
                        if (tk.DateEnd) endValue = new Date(tk.DateEnd);

                        return (
                          <ListTile
                            key={tk.Id}
                            onPress={
                              checkEditable
                                ? () => {
                                    setSelectedTask(tk);
                                    methods.setValue('TaskId', tk.Id);
                                  }
                                : undefined
                            }
                            style={{
                              padding: 0,
                              borderRadius: 8,
                              borderColor:
                                ColorThemes.light.neutral_bolder_border_color,
                              borderWidth: 1,
                              backgroundColor: !checkEditable
                                ? ColorThemes.light
                                    .neutral_disable_background_color
                                : ColorThemes.light
                                    .neutral_absolute_background_color,
                            }}
                            listtileStyle={{
                              paddingHorizontal: 16,
                              paddingVertical: 8,
                            }}
                            isClickLeading
                            trailing={
                              <View
                                style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  gap: 8,
                                }}>
                                <SkeletonImage
                                  source={{
                                    uri: user?.Img
                                      ? user?.Img?.startsWith('https')
                                        ? user?.Img
                                        : ConfigAPI.imgUrlId + user?.Img
                                      : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTU3FcmHH1HtWFPQqC9Z-IK4JdvSWkvswcDfA&s',
                                  }} // Remote image
                                  style={{
                                    width: 24,
                                    height: 24,
                                    objectFit: 'cover',
                                    borderRadius: 100,
                                  }}
                                />
                                {_stDate.getDate() === thisDateValue ? (
                                  <Tooltip
                                    enterTouchDelay={0}
                                    leaveTouchDelay={1000}
                                    title="Ngày bắt đầu">
                                    <Winicon
                                      src="outline/sport/chequered-flag"
                                      style={{
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                      }}
                                      size={12}
                                    />
                                  </Tooltip>
                                ) : _endDate.getDate() === thisDateValue ? (
                                  <Tooltip
                                    enterTouchDelay={0}
                                    leaveTouchDelay={1000}
                                    title="Ngày kết thúc">
                                    <Winicon
                                      src="outline/sport/archery-target"
                                      style={{
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                      }}
                                      size={12}
                                    />
                                  </Tooltip>
                                ) : tk.Repeat ? (
                                  <Tooltip
                                    enterTouchDelay={0}
                                    leaveTouchDelay={1000}
                                    title="Công việc lặp lại">
                                    <Winicon
                                      src="outline/arrows/loop-2"
                                      style={{
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                      }}
                                      size={12}
                                    />
                                  </Tooltip>
                                ) : null}
                              </View>
                            }
                            title={
                              <Text
                                style={{
                                  ...TypoSkin.heading7,
                                  color:
                                    ColorThemes.light.neutral_text_title_color,
                                }}>
                                {tk.Name}
                              </Text>
                            }
                            subtitle={
                              <View style={{paddingTop: 4}}>
                                {(startValue &&
                                  startValue.getSeconds() === 1) ||
                                (endValue && endValue.getSeconds() === 59) ? (
                                  <Text
                                    style={{
                                      ...TypoSkin.label4,
                                      color:
                                        ColorThemes.light
                                          .neutral_text_subtitle_color,
                                    }}>
                                    Thời gian:
                                    {startValue
                                      ? `${Ultis.datetoString(startValue, 'hh:mm')} - `
                                      : ''}
                                    {endValue
                                      ? Ultis.datetoString(endValue, 'hh:mm')
                                      : ''}
                                  </Text>
                                ) : (
                                  <Text
                                    style={{
                                      ...TypoSkin.label4,
                                      color:
                                        ColorThemes.light
                                          .neutral_text_subtitle_color,
                                    }}>
                                    Thời gian: Cả ngày
                                  </Text>
                                )}
                              </View>
                            }
                            bottom={(() => {
                              try {
                                const activity = activities
                                  ?.filter(
                                    (e: any) =>
                                      e.TaskId === tk.Id &&
                                      e.EndTime &&
                                      thisDateValue ===
                                        new Date(e.EndTime).getDate(),
                                  )
                                  .sort(
                                    (a: any, b: any) => a.EndTime - b.EndTime,
                                  );
                                const doneActivityList = activity
                                  ?.map((e: any) => [
                                    e?.DeviceId ? e?.DeviceId.split(',') : [],
                                    e?.BioProductId
                                      ? e?.BioProductId?.split(',')
                                      : [],
                                  ])
                                  ?.flat(Infinity)
                                  ?.filter(
                                    (v: any, i: any, a: string | any[]) =>
                                      a.indexOf(v) === i,
                                  );

                                const countDeviceTk = [
                                  ...(tk?.DeviceId?.split(',')
                                    ? tk?.DeviceId?.split(',')
                                    : []),
                                  ...(tk?.BioProductId?.split(',')
                                    ? tk?.BioProductId?.split(',')
                                    : []),
                                ].length;
                                const countDeviceActivity =
                                  doneActivityList?.length;
                                var isDone =
                                  countDeviceTk === countDeviceActivity;
                                return (
                                  <View
                                    style={{
                                      gap: 8,
                                      flexDirection: 'row',
                                      alignItems: 'center',
                                      flex: 1,
                                      paddingHorizontal: 16,
                                      paddingVertical: 8,
                                      alignContent: 'flex-start',
                                      width: '100%',
                                      backgroundColor:
                                        ColorThemes.light
                                          .neutral_disable_background_color,
                                    }}>
                                    <Winicon
                                      src={
                                        isDone
                                          ? 'fill/layout/circle-check'
                                          : 'outline/layout/circle-check'
                                      }
                                      size={16}
                                      color={
                                        isDone
                                          ? ColorThemes.light.primary_main_color
                                          : ColorThemes.light
                                              .neutral_text_subtitle_color
                                      }
                                    />
                                    <Text
                                      style={{
                                        ...TypoSkin.buttonText4,
                                        color:
                                          ColorThemes.light
                                            .neutral_text_title_color,
                                      }}>
                                      {countDeviceActivity}/{countDeviceTk}
                                      thiết bị
                                    </Text>
                                  </View>
                                );
                              } catch (error) {
                                console.log(
                                  '====================================',
                                );
                                console.log(error);
                                console.log(
                                  '====================================',
                                );
                              }
                            })()}
                          />
                        );
                      })
                    ) : (
                      <View
                        style={{
                          gap: 8,
                          paddingVertical: 4,
                        }}>
                        <Text
                          style={{
                            ...TypoSkin.body3,
                            color: ColorThemes.light.neutral_text_title_color,
                          }}>
                          Không có công việc.
                        </Text>
                      </View>
                    )}
                  </View>
                }
              />
            );
          })}
        </Pressable>
      </ScrollView>
    </View>
  );
};

//#region Calendar Month
const CalendarMonth = ({
  focusDate,
  toiletItem,
  methods,
  setSelectedTask,
}: {
  focusDate: Date;
  toiletItem: any;
  methods: any;
  setSelectedTask: any;
}) => {
  const focusWeekDay = useMemo(() => focusDate.getDay(), [focusDate]);
  const user = useSelectorCustomerState().data;
  const userRole = useSelectorCustomerState().role;
  const owner = useSelectorCustomerCompanyState().owner;
  const [toiletServices, setToiletServices] = useState<Array<any>>([]);
  const [refreshing, onRefresh] = useState(false);

  const firstDayOfMonth = useMemo(() => {
    return new Date(focusDate.getFullYear(), focusDate.getMonth(), 1);
  }, [focusDate]);
  const [tasks, setTasks] = useState([]);
  const popupRef = useRef<any>();
  const now = new Date();
  const [activities, setActivities] = useState<Array<any>>([]);

  const getData = async () => {
    const controller = new DataController('Task');
    const activityController = new DataController('Activity');
    const endDate = new Date(
      focusDate.getFullYear(),
      focusDate.getMonth() + 1,
      1,
    );
    let query = `@ToiletId:{${toiletItem.Id}} @CustomerId:{*${user?.Id}*} @Type:[6 6] @Status:[1 1] @RequireCheckin:{true} (@DateStart:[-inf ${endDate.getTime()}] | @DateEnd:[${firstDayOfMonth.getTime()} +inf])`;
    const res = await controller.aggregateList({
      page: 1,
      size: 5000,
      searchRaw: query,
    });
    let tmp = res.data.filter(
      (e: any) => e.DateEnd >= firstDayOfMonth.getTime(),
    );
    if (res.code === 200) {
      const servicesIds = res.data
        .filter((e: any) => e.ToiletServicesId)
        .map((e: any) => e.ToiletServicesId)
        .filter(
          (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
        );
      if (servicesIds.length) {
        const servicesController = new DataController('ToiletServices');
        servicesController.getByListId(servicesIds).then(serRes => {
          if (serRes.code === 200) setToiletServices(serRes.data);
        });
      }
      const act = await activityController.getListSimple({
        page: 1,
        size: 1000,
        query: `@CustomerId:{${user?.Id}} (@DateStart:[-inf ${endDate.getTime()}]) | (@EndTime:[${firstDayOfMonth.getTime()} +inf])`,
      });

      if (act.code === 200) setActivities(act.data);
      setTasks(tmp);
      onRefresh(false);
    }
    onRefresh(false);
  };

  useEffect(() => {
    getData();
  }, [focusDate, toiletItem]);

  const weekdays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing ?? false}
          onRefresh={() => {
            onRefresh(true);
            getData();
          }}
        />
      }
      style={{flex: 1}}>
      <FPopup ref={popupRef} />
      <View style={{flex: 1, paddingVertical: 16}}>
        {Array.from({length: 42}).map((_, i) => {
          let dateNumber =
            (i % 7) + Math.floor(i / 7) * 7 - firstDayOfMonth.getDay();
          const timeValue = new Date(
            focusDate.getFullYear(),
            focusDate.getMonth(),
            dateNumber + 1,
            0,
            0,
            2,
          );
          if (timeValue.getMonth() !== focusDate.getMonth()) return null;
          const thisDateValue = timeValue.getDate();
          const _taskList = tasks.filter((t: any) => {
            const _stDate = new Date(t.DateStart);
            const _endDate = new Date(t.DateEnd);
            switch (t.Repeat) {
              case 1:
                return (
                  (_stDate.getDate() === timeValue.getDate() &&
                    Math.abs(differenceInDays(_stDate, timeValue)) < 1) ||
                  (_endDate.getDate() === timeValue.getDate() &&
                    Math.abs(differenceInDays(_endDate, timeValue)) < 1) ||
                  (_stDate.getTime() < timeValue.getTime() &&
                    _endDate.getTime() >= timeValue.getTime())
                );
              case 2:
                return t.RepeatValue && JSON.parse(t.RepeatValue).includes(i);
              case 3:
                const tmpDate = new Date(
                  focusDate.getFullYear(),
                  focusDate.getMonth(),
                  dateNumber + 1,
                );
                tmpDate.setDate(thisDateValue + 1);
                return (
                  t.RepeatValue &&
                  JSON.parse(t.RepeatValue).includes(
                    tmpDate.getMonth() !== timeValue.getMonth()
                      ? 'last'
                      : thisDateValue,
                  )
                );
              default:
                return (
                  _stDate.getDate() === thisDateValue ||
                  _endDate.getDate() === thisDateValue
                );
            }
          });
          var focus =
            thisDateValue === now.getDate() &&
            !differenceInDays(timeValue, now);
          return (
            <ListTile
              key={`dtwk-${i}`}
              leading={
                <View style={{width: '100%'}}>
                  <View style={{}}>
                    <Text
                      style={[
                        styles.heading,
                        {
                          color: focus
                            ? ColorThemes.light.primary_main_color
                            : ColorThemes.light.neutral_text_subtitle_color,
                        },
                      ]}>
                      Ngày:{' '}
                      {thisDateValue < 10 ? `0${thisDateValue}` : thisDateValue}
                    </Text>
                  </View>
                </View>
              }
              style={{
                borderColor: ColorThemes.light.neutral_bolder_border_color,
                borderWidth: 1,
                marginTop: 16,
              }}
              listtileStyle={{alignItems: 'flex-start', gap: 16}}
              title={
                <View style={{gap: 8}}>
                  {_taskList.length ? (
                    _taskList.map((tk: any) => {
                      const _stDate = new Date(tk.DateStart);
                      const _endDate = new Date(tk.DateEnd);
                      var checkEditable = false;
                      if (tk.ToiletServicesId)
                        var toiletServicesData = toiletServices.find(
                          e => e.Id === tk.ToiletServicesId,
                        );
                      if (toiletServicesData) {
                        if (
                          toiletServicesData.Status < ToiletServiceStatus.run
                        ) {
                          checkEditable =
                            (owner?.Id === toiletServicesData?.CustomerId &&
                              userRole?.Role.includes(
                                CustomerRole.Coordinator,
                              )) ||
                            user?.Id === toiletServicesData?.CustomerId;
                        } else checkEditable = false;
                      } else {
                        checkEditable =
                          (owner?.Id === toiletItem?.CustomerId &&
                            userRole?.Role.includes(
                              CustomerRole.Coordinator,
                            )) ||
                          user?.Id === toiletItem?.CustomerId ||
                          tk.CustomerId === user?.Id;
                      }

                      var startValue = undefined;
                      var endValue = undefined;
                      if (tk.DateStart) startValue = new Date(tk.DateStart);
                      if (tk.DateEnd) endValue = new Date(tk.DateEnd);

                      // ngày khac sẽ không thể click
                      if (now.getDate() != thisDateValue) checkEditable = false;
                      return (
                        <ListTile
                          key={tk.Id}
                          onPress={
                            checkEditable
                              ? () => {
                                  setSelectedTask(tk);
                                  methods.setValue('TaskId', tk.Id);
                                }
                              : undefined
                          }
                          style={{
                            padding: 0,
                            borderRadius: 8,
                            borderColor:
                              ColorThemes.light.neutral_bolder_border_color,
                            borderWidth: 1,
                            backgroundColor: !checkEditable
                              ? ColorThemes.light
                                  .neutral_disable_background_color
                              : ColorThemes.light
                                  .neutral_absolute_background_color,
                          }}
                          listtileStyle={{
                            paddingHorizontal: 16,
                            paddingVertical: 8,
                          }}
                          isClickLeading
                          trailing={
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 8,
                              }}>
                              <SkeletonImage
                                source={{
                                  uri: user?.Img
                                    ? user?.Img?.startsWith('https')
                                      ? user?.Img
                                      : ConfigAPI.imgUrlId + user?.Img
                                    : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTU3FcmHH1HtWFPQqC9Z-IK4JdvSWkvswcDfA&s',
                                }} // Remote image
                                style={{
                                  width: 24,
                                  height: 24,
                                  objectFit: 'cover',
                                  borderRadius: 100,
                                }}
                              />
                              {_stDate.getDate() === thisDateValue ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  leaveTouchDelay={1000}
                                  title="Ngày bắt đầu">
                                  <Winicon
                                    src="outline/sport/chequered-flag"
                                    style={{
                                      paddingHorizontal: 12,
                                      paddingVertical: 8,
                                    }}
                                    size={12}
                                  />
                                </Tooltip>
                              ) : _endDate.getDate() === thisDateValue ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  leaveTouchDelay={1000}
                                  title="Ngày kết thúc">
                                  <Winicon
                                    src="outline/sport/archery-target"
                                    style={{
                                      paddingHorizontal: 12,
                                      paddingVertical: 8,
                                    }}
                                    size={12}
                                  />
                                </Tooltip>
                              ) : tk.Repeat ? (
                                <Tooltip
                                  enterTouchDelay={0}
                                  leaveTouchDelay={1000}
                                  title="Công việc lặp lại">
                                  <Winicon
                                    src="outline/arrows/loop-2"
                                    style={{
                                      paddingHorizontal: 12,
                                      paddingVertical: 8,
                                    }}
                                    size={12}
                                  />
                                </Tooltip>
                              ) : null}
                            </View>
                          }
                          title={
                            <Text
                              style={{
                                ...TypoSkin.heading7,
                                color:
                                  ColorThemes.light.neutral_text_title_color,
                              }}>
                              {tk.Name}
                            </Text>
                          }
                          bottom={(() => {
                            const activity = activities
                              ?.filter(
                                (e: any) =>
                                  e.TaskId === tk.Id &&
                                  e.EndTime &&
                                  thisDateValue ===
                                    new Date(e.EndTime).getDate(),
                              )
                              .sort((a: any, b: any) => a.EndTime - b.EndTime);

                            if (!activity.length) return null;
                            const doneActivityList = activity
                              .map((e: any) => [
                                e?.DeviceId ? e?.DeviceId.split(',') : [],
                                e?.BioProductId
                                  ? e?.BioProductId?.split(',')
                                  : [],
                              ])
                              .flat(Infinity)
                              .filter(
                                (v: any, i: any, a: string | any[]) =>
                                  a.indexOf(v) === i,
                              );
                            const countDeviceTk = [
                              ...(tk?.DeviceId?.split(',')
                                ? tk?.DeviceId?.split(',')
                                : []),
                              ...(tk?.BioProductId?.split(',')
                                ? tk?.BioProductId?.split(',')
                                : []),
                            ].length;
                            const countDeviceActivity = doneActivityList.length;
                            const isDone =
                              countDeviceTk === countDeviceActivity;

                            return (
                              <View
                                style={{
                                  gap: 8,
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  flex: 1,
                                  paddingHorizontal: 16,
                                  paddingVertical: 8,
                                  alignContent: 'flex-start',
                                  width: '100%',
                                  backgroundColor:
                                    ColorThemes.light
                                      .neutral_disable_background_color,
                                }}>
                                <Winicon
                                  src={
                                    isDone
                                      ? 'fill/layout/circle-check'
                                      : 'outline/layout/circle-check'
                                  }
                                  size={16}
                                  color={
                                    isDone
                                      ? ColorThemes.light.primary_main_color
                                      : ColorThemes.light
                                          .neutral_text_subtitle_color
                                  }
                                />
                                <Text
                                  style={{
                                    ...TypoSkin.buttonText4,
                                    color:
                                      ColorThemes.light
                                        .neutral_text_title_color,
                                  }}>
                                  {countDeviceActivity}/{countDeviceTk} thiết bị
                                </Text>
                              </View>
                            );
                          })()}
                        />
                      );
                    })
                  ) : (
                    <Text
                      style={{
                        ...TypoSkin.body3,
                        color: ColorThemes.light.neutral_text_title_color,
                      }}>
                      Không có công việc
                    </Text>
                  )}
                </View>
              }
            />
          );
        })}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  weekdayContainer: {
    flex: 1,
  },
  weekdayTitle: {
    flex: 1,
  },
  heading: {
    fontWeight: 'bold',
    fontSize: 16,
  },
});

//#region step 2
const CheckingHealthyViewS2 = ({step, methods, setChecked, checked}: any) => {
  return (
    <View>
      {step != 2 ? null : methods.watch('TaskId') ? (
        <View>
          <Text
            style={{
              ...TypoSkin.body3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}>
            Hãy luôn chủ động bảo vệ sức khỏe của bản thân và góp phần tạo nên
            môi trường làm việc an toàn, sạch sẽ cho mọi người!
          </Text>
          <ListTile
            onPress={() => setChecked(!checked)}
            style={{padding: 0, paddingVertical: 8}}
            leading={
              <FCheckbox
                value={checked}
                onChange={v => {
                  setChecked(v);
                }}
              />
            }
            title="Tôi đảm bảo đủ sức khoẻ để làm việc"
          />
        </View>
      ) : null}
    </View>
  );
};
//#region step 3
const DevicesAndBiosS3 = ({
  step,
  item,
  toiletItem,
  methods,
  setDevices,
  setBios,
  checked,
  activities,
}: any) => {
  const activityItems = useMemo(() => {
    return activities.filter(
      (e: any) =>
        e.TaskId == item.Id &&
        e.EndTime &&
        new Date(e.EndTime).getDate() == new Date().getDate(),
    );
  }, [item, activities]);
  const [tab, setTab] = useState(0);
  const cateServices = useSelectorCateServiceState().data;
  return checked && step > 2 ? (
    <KeyboardAvoidingView style={{flex: 1, height: '100%', width: '100%'}}>
      <Pressable style={{alignItems: 'center', flexDirection: 'row'}}>
        <TouchableOpacity
          onPress={() => {
            setTab(0);
          }}
          style={{
            paddingBottom: 8,
            flex: 1,
            alignItems: 'center',
            borderBottomColor: ColorThemes.light.neutral_text_title_color,
            borderBottomWidth: tab === 0 ? 1 : 0,
          }}>
          <Text
            style={[
              TypoSkin.title3,
              {
                color:
                  tab === 0
                    ? ColorThemes.light.neutral_text_title_color
                    : ColorThemes.light.neutral_text_disabled_color,
              },
            ]}>
            {`Thiết bị cần ${(cateServices.find(e => e.Id === item.CateServicesId)?.Name ?? 'kiểm tra').toLowerCase()}`}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            setTab(1);
          }}
          style={{
            paddingBottom: 8,
            flex: 1,
            alignItems: 'center',
            borderBottomColor: ColorThemes.light.neutral_text_title_color,
            borderBottomWidth: tab === 1 ? 1 : 0,
          }}>
          <Text
            style={[
              TypoSkin.title3,
              {
                color:
                  tab === 1
                    ? ColorThemes.light.neutral_text_title_color
                    : ColorThemes.light.neutral_text_disabled_color,
              },
            ]}>
            Chế phẩm sinh học
          </Text>
        </TouchableOpacity>
      </Pressable>
      <ScrollView style={{height: '100%', width: '100%', paddingTop: 16}}>
        <View style={{flex: 1, paddingBottom: 120, gap: 16}}>
          {tab == 0 ? (
            <CheckListTable
              title={`Thiết bị cần ${(cateServices.find(e => e.Id === item.CateServicesId)?.Name ?? 'kiểm tra').toLowerCase()}`}
              toiletId={toiletItem?.Id}
              taskItem={item}
              defaultValue={methods.watch('DeviceId')}
              activityItems={activityItems}
              ids={item?.DeviceId?.split(',').filter((id: any) => id?.length)}
              onChange={(ids: any) => {
                setDevices(ids);
                methods.setValue('DeviceId', ids.join(','));
              }}
            />
          ) : null}
          {tab == 1 ? (
            <CheckListTable
              controlModule="BioProduct"
              title="Kiểm tra việc sử dụng chế phẩm sinh học"
              taskItem={item}
              defaultValue={methods.watch('BioProductId')}
              toiletId={toiletItem?.Id}
              activityItems={activityItems}
              ids={item?.BioProductId?.split(',').filter(
                (id: any) => id?.length,
              )}
              onChange={(ids: any) => {
                setBios(ids);
                methods.setValue('BioProductId', ids.join(','));
              }}
            />
          ) : null}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  ) : (
    <View />
  );
};
//#region step 4
const DescriptionViewS4 = ({step, methods, setChecked, checked}: any) => {
  return checked && step > 2 ? (
    <KeyboardAvoidingView style={{flex: 1, paddingTop: 16}}>
      <View style={{height: '100%', paddingBottom: 140}}>
        <TextFieldForm
          control={methods.control}
          name="Description"
          label="Mô tả"
          errors={methods.formState.errors}
          placeholder={'Mô tả thêm...'}
          style={{backgroundColor: ColorThemes.light.transparent}}
          textFieldStyle={{
            height: 100,
            width: '100%',
            paddingHorizontal: 16,
            paddingTop: 16,
            paddingBottom: 16,
            justifyContent: 'flex-start',
            backgroundColor: ColorThemes.light.transparent,
          }}
          textStyle={{textAlignVertical: 'top'}}
          numberOfLines={10}
          multiline={true}
          onBlur={async (ev: string) => {
            methods.setValue('Description', ev);
          }}
          register={methods.register}
        />
      </View>
    </KeyboardAvoidingView>
  ) : (
    <View />
  );
};

const CheckListTable = ({
  controlModule = 'Device',
  title,
  defaultValue,
  ids = [],
  taskItem,
  activityItems,
  onChange,
  toiletId,
}: any) => {
  const controller = new DataController(controlModule);
  const [devices, setDevices] = useState<Array<any>>([]);
  const [products, setProducts] = useState<Array<any>>([]);
  const popupRef = useRef<any>();
  const [selected, setSelected] = useState<Array<any>>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  var device = useMemo(
    () =>
      controlModule !== 'Device'
        ? []
        : (activityItems
            ?.flatMap((e: any) => e.DeviceId?.split(','))
            .filter(
              (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
            ) ?? []),
    [activityItems],
  );
  var bio = useMemo(
    () =>
      controlModule == 'Device'
        ? []
        : (activityItems
            ?.flatMap((e: any) => e.BioProductId?.split(','))
            .filter(
              (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
            ) ?? []),
    [activityItems],
  );
  useEffect(() => {
    if (ids.length) {
      controller.getByListId(ids).then(async res => {
        if (res.code === 200) {
          const productIds = res.data
            .filter((e: any) => e?.ProductId)
            .map((e: any) => e?.ProductId)
            .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i);
          if (productIds.length) {
            const productController = new DataController('Product');
            const productRes = await productController.getByListId(productIds);
            if (productRes.code === 200) setProducts(productRes.data);
          }
          setDevices(res.data);
        }
      });
    } else {
      setDevices([]);
    }
  }, [ids.length]);

  useEffect(() => {
    if (activityItems) {
      console.log('===============device=====================');
      console.log(
        activityItems
          ?.flatMap((e: any) => e.DeviceId?.split(','))
          .filter(
            (id: any, i: any, arr: string | any[]) => arr.indexOf(id) === i,
          ),
      );
      console.log('====================================');
      setSelected(controlModule == 'Device' ? device : bio);
    }
    if (defaultValue) setSelected(ids => [...ids, ...defaultValue.split(',')]);
  }, [activityItems]);

  useEffect(() => {
    onChange(
      selected.filter(
        (v: any, i: any, a: string | any[]) => a.indexOf(v) === i,
      ),
    );
    if (devices.length != 0 && selected.length == devices.length)
      setSelectAll(true);
    else setSelectAll(false);
  }, [selected]);

  return (
    <View style={{}}>
      <FPopup ref={popupRef} />
      {devices.length === 0 ? (
        <EmptyPage
          title={`Nhà vệ sinh không có ${controlModule === 'Device' ? 'thiết bị' : 'chế phẩm sinh học'} nào`}
        />
      ) : (
        <View style={{flex: 1}}>
          <ListTile
            leading={
              <Winicon
                src={
                  selectAll
                    ? 'fill/user interface/c-check'
                    : 'outline/user interface/c-check'
                }
                size={20}
                color={
                  selectAll ? ColorThemes.light.success_main_color : undefined
                }
              />
            }
            onPress={() => {
              if (selectAll)
                setSelected(controlModule == 'Device' ? device : bio);
              else
                setSelected([
                  ...selected,
                  ...devices
                    .filter((e: any) => !selected.includes(e?.Id))
                    ?.map((e: any) => e?.Id),
                ]);
              setSelectAll(!selectAll);
            }}
            title={'Chọn tất cả'}
            style={{padding: 0, paddingBottom: 16}}
          />
          {devices.map((dev: any, i: any) => {
            const checked = selected
              .filter((v: any, i: any, a: string | any[]) => a.indexOf(v) === i)
              .includes(dev?.Id);
            const checkedIds = controlModule == 'Device' ? device : bio;
            const isDisabled = checkedIds.includes(dev?.Id);
            if (dev?.ProductId)
              var _product = products.find(e => e.Id === dev?.ProductId);
            return (
              <View
                key={`dev${dev.Id}${i}`}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                  justifyContent: 'space-between',
                }}>
                <View style={{flexDirection: 'row', gap: 16}}>
                  <TouchableOpacity
                    onPress={
                      isDisabled
                        ? undefined
                        : () => {
                            if (!selected.includes(dev.Id))
                              setSelected([...selected, dev.Id]);
                            else
                              setSelected(s => s.filter(id => id !== dev.Id));
                          }
                    }>
                    <Winicon
                      src={
                        checked
                          ? 'fill/user interface/c-check'
                          : 'outline/user interface/c-check'
                      }
                      size={20}
                      color={
                        checked
                          ? ColorThemes.light.success_main_color
                          : undefined
                      }
                    />
                  </TouchableOpacity>
                </View>
                <ListTile
                  style={{flex: 1}}
                  listtileStyle={{gap: 16}}
                  onPress={
                    isDisabled
                      ? undefined
                      : () => {
                          if (!selected.includes(dev.Id))
                            setSelected([...selected, dev.Id]);
                          else setSelected(s => s.filter(id => id !== dev.Id));
                        }
                  }
                  leading={
                    <SkeletonImage
                      source={{
                        uri: ConfigAPI.imgUrlId + (_product?.Img ?? dev.Img),
                      }}
                      style={{height: 46, width: 46, borderRadius: 8}}
                    />
                  }
                  title={dev.Name ?? '-'}
                  subtitle={`Số lượng: ${dev.Quantity} ${_product?.Unit ?? dev.Unit}`}
                />
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
};
