import {useState} from 'react';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {ComponentStatus, showDialog} from 'wini-mobile-components';
import {store} from 'redux/store/store';
import {OrderProductDA} from '../../../../orderProductDA';
import productDA from '../../../../../product/productDA';
import {StatusOrder, Title} from 'config/Contanst';
import {useOrderActions as useOrderReduxActions} from 'redux/reducers/order/OrderReducer';
import {RootScreen} from 'router/router';
import {handleUpdateStatusOrder} from '../../../Utils';
import {showSnackbar} from 'component/export-component';

export const useOrderActions = (
  order: any,
  orderDetails: any[],
  fetchOrderData: () => void,
) => {
  const navigation = useNavigation<any>();
  const orderActions = useOrderReduxActions();
  const dispatch = useDispatch<any>();
  const shopInfo = store.getState().partner.data;
  const shopId = shopInfo && shopInfo.length > 0 ? shopInfo[0].Id : null;
  const orderDA = new OrderProductDA();
  const [isSubmittingCancel, setSubmittingCancel] = useState(false);
  const [isSubmittingUpdateStatus, setSubmittingUpdateStatus] = useState(false);

  const handleUpdateStatusProcessOrder = async (dialogRef: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn muốn xác nhận đơn hàng này?',
      onSubmit: async () => {
        try {
          // Kiểm tra số lượng tồn kho của tất cả sản phẩm trong đơn hàng
          for (const orderDetail of orderDetails) {
            const productId = orderDetail?.ProductId;
            const orderQuantity = orderDetail?.Quantity || 1;
            if (productId) {
              const productResponse =
                await orderDA.getInfoProductById(productId);
              if (
                productResponse?.code === 200 &&
                productResponse?.data?.length > 0
              ) {
                const product = productResponse.data[0];
                const currentStock = product?.InStock || 0;

                // Kiểm tra nếu sản phẩm hết hàng
                if (currentStock <= 0) {
                  showSnackbar({
                    message: `Sản phẩm "${product?.Name}" đã hết hàng. Không thể xử lý đơn hàng này.`,
                    status: ComponentStatus.ERROR,
                  });
                  return;
                }

                // Kiểm tra nếu số lượng đặt hàng vượt quá tồn kho
                if (orderQuantity > currentStock) {
                  showSnackbar({
                    message: `Sản phẩm "${product?.Name}" chỉ còn ${currentStock} sản phẩm trong kho. Đơn hàng yêu cầu ${orderQuantity} sản phẩm.`,
                    status: ComponentStatus.ERROR,
                  });
                  return;
                }
              } else {
                showSnackbar({
                  message: 'Không tìm thấy thông tin sản phẩm',
                  status: ComponentStatus.ERROR,
                });
                return;
              }
            }
          }

          // Cập nhật số lượng tồn kho của tất cả sản phẩm
          for (const orderDetail of orderDetails) {
            const productId = orderDetail?.ProductId;
            const orderQuantity = orderDetail?.Quantity || 1;

            if (productId) {
              const productResponse =
                await orderDA.getInfoProductById(productId);
              if (
                productResponse?.code === 200 &&
                productResponse?.data?.length > 0
              ) {
                const product = productResponse.data[0];
                const currentStock = product?.InStock || 0;

                // Cập nhật số lượng tồn kho của sản phẩm
                const newStock = currentStock - orderQuantity;
                const updatedProduct = {
                  ...product,
                  InStock: newStock,
                  // Tự động cập nhật status thành 2 (hết hàng) nếu newStock = 0
                  Status: newStock === 0 ? 2 : product.Status,
                };

                try {
                  const productUpdateResponse =
                    await productDA.updateProduct(updatedProduct);

                  if (productUpdateResponse?.code !== 200) {
                    throw new Error(
                      productUpdateResponse?.message ||
                        'Cập nhật sản phẩm thất bại',
                    );
                  }
                } catch (error) {
                  console.error('Lỗi cập nhật sản phẩm:', error);
                }
              }
            }
          }

          // Cập nhật trạng thái đơn hàng
          const res = await orderDA.updateOrder([
            {
              Id: order.Id,
              CustomerId: order.CustomerId,
              ShopId: order.ShopId,
              Code: order.Code,
              Status: StatusOrder.proccess,
              DateProcess: new Date().getTime(),
            },
          ]);

          if (res?.code === 200) {
            showSnackbar({
              message: 'Xác nhận đơn hàng thành công!',
              status: ComponentStatus.SUCCSESS,
            });
            // Refresh dữ liệu đơn hàng
            fetchOrderData();
            // Dispatch action để refresh dữ liệu chung nếu có shopInfo
            if (shopInfo && shopInfo[0]?.Id) {
              orderActions.fetchAllOrdersByShopId(shopId);
            }
            navigation.navigate(RootScreen.OrderShopDetail, {
              type: Title.Processing,
              status: 2,
            });
          } else {
            showSnackbar({
              message: 'Xác nhận đơn hàng thất bại!',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error confirming order:', error);
          showSnackbar({
            message: 'Xác nhận đơn hàng thất bại!',
            status: ComponentStatus.ERROR,
          });
        }
      },
    });
  };

  const handleSubmitRejectOrder = async (reason: string) => {
    setSubmittingCancel(true);
    try {
      const cancelData = {
        Id: order.Id,
        CustomerId: order.CustomerId,
        ShopId: order.ShopId,
        Code: order.Code,
        DateUpdated: new Date().getTime(),
        Status: StatusOrder.cancel,
        IsCustomer: false, // Shop is cancelling
        CancelReason: reason,
      };
      const res = await orderDA.cancelOrder(cancelData);
      if (res?.code === 200) {
        showSnackbar({
          message: 'Đã từ chối/hủy đơn hàng thành công!',
          status: ComponentStatus.SUCCSESS,
        });
        fetchOrderData(); // Refresh data to show updated cancel reason
        navigation.navigate(RootScreen.OrderDetail, {
          type: Title.Cancel,
          status: 4,
        });
        return true;
      } else {
        showSnackbar({
          message: res?.message || 'Hủy đơn hàng thất bại!',
          status: ComponentStatus.ERROR,
        });
        return false;
      }
    } catch (error) {
      showSnackbar({
        message: 'Hủy đơn hàng thất bại!',
        status: ComponentStatus.ERROR,
      });
      return false;
    } finally {
      setSubmittingCancel(false);
    }
  };

  const handleSubmitUpdateStatus = async (item: any, status_str?: string) => {
    if (!item || !status_str) {
      return;
    }
    setSubmittingUpdateStatus(true);
    try {
      let status: number;
      switch (status_str) {
        case 'completed':
          status = StatusOrder.success;
          break;
        case 'cancelled':
          status = StatusOrder.cancel;
          break;
        case 'processing':
          status = StatusOrder.proccess;
          break;
        case 'Pending':
          status = 5;
          break;
        case 'Delivery':
          status = 6;
          break;
        default:
          const parsedStatus = parseInt(status_str, 10);
          if (isNaN(parsedStatus)) {
            showSnackbar({
              message: `Trạng thái không hợp lệ: ${status_str}`,
              status: ComponentStatus.ERROR,
            });
            return;
          }
          status = parsedStatus;
          break;
      }

      const res = await handleUpdateStatusOrder(
        item,
        status_str,
        dispatch,
        navigation,
        shopInfo,
      );

      if (res?.code === 200) {
        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng thành công!',
          status: ComponentStatus.SUCCSESS,
        });

        // Refresh dữ liệu
        fetchOrderData();

        // Tự động chuyển sang trang OrderDetail dựa trên status
        let navigationType: string;
        let navigateStatus: number;

        switch (status_str) {
          case 'processing':
            navigationType = Title.Processing;
            navigateStatus = 2;
            break;
          case 'completed':
            navigationType = Title.Done;
            navigateStatus = 3;
            break;
          case 'cancelled':
            navigationType = Title.Cancel;
            navigateStatus = 4;
            break;
          default:
            navigationType = Title.Processing;
            navigateStatus = 2;
            break;
        }

        navigation.navigate(RootScreen.OrderDetail, {
          type: navigationType,
          status: navigateStatus,
        });
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    } finally {
      setSubmittingUpdateStatus(false);
    }
  };

  return {
    handleUpdateStatusProcessOrder,
    handleSubmitRejectOrder,
    handleSubmitUpdateStatus,
    isSubmittingCancel,
    isSubmittingUpdateStatus,
    setSubmittingUpdateStatus,
  };
};
