import { Text, View } from 'react-native';
import ListTile from '../../../../../component/list-tile/list-tile';
import { Ultis } from '../../../../../utils/Utils';
import { TypoSkin } from '../../../../../assets/skin/typography';
import { FDialog, showDialog, Winicon } from '../../../../../component/export-component';
import { useMemo, useRef } from 'react';
import { ColorThemes } from '../../../../../assets/skin/colors';
import { useNavigation } from '@react-navigation/native';
import { ProductSourceStringData, ProductTypeStringData, ToiletStatus } from '../../../service/components/da';
import { ComponentStatus } from '../../../../../component/component-status';
import { CustomerRole } from '../../../../../redux/reducers/user/da';
import { useSelectorCustomerCompanyState, useSelectorCustomerState } from '../../../../../redux/hooks/hooks';
import { DataController } from '../../../../base-controller';
import { useDispatch } from 'react-redux';
import { SkeletonImage } from '../../../../../project-component/skeleton-img';
import ConfigAPI from '../../../../../config/configApi';

export default function MaterialCard({ item, index, user, setWorkData, workData, listCate }: any) {
    const dialogDelAccRef = useRef<any>()
    const navigation = useNavigation<any>()
    const owner = useSelectorCustomerCompanyState().owner;
    const userRole = useSelectorCustomerState().role;
    const dispatch = useDispatch<any>()

    const checkEditable = useMemo(() => {
        return item.CustomerId === user.Id || (item.CustomerId === owner?.Id && userRole?.Role?.includes(CustomerRole.Coordinator))
    }, [item, user, owner, userRole])

    return (
        <View>
            <FDialog ref={dialogDelAccRef} />
            <ListTile
                key={item.Id}
                onPress={() => {
                    // navigation.push(RootScreen.detailProject, { item: item })
                }}
                leading={<SkeletonImage source={{ uri: ConfigAPI.imgUrlId + item?.Img }} style={{ width: 60, height: 60, borderRadius: 8, objectFit: "cover", overflow: "hidden" }} />}
                title={<Text style={{ ...TypoSkin.title3 }} numberOfLines={3}>{`${index + 1}. ${item?.Name ?? '-'}`}</Text>}
                listtileStyle={{ gap: 8 }}
                titleStyle={{ ...TypoSkin.title3, paddingBottom: 8 }}
                bottom={<View style={{ alignContent: "flex-start", width: "100%", paddingTop: 16 }}>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Đơn vị: ${item?.Unit ?? "-"}`}</Text>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Giá bán: ${item?.Price ? Ultis.money(item.Price) : "-"}`} (VNĐ)</Text>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Phân loại: ${listCate.data.find((e: any) => e.Id === item?.CategoryId)?.Name ?? "-"}`}</Text>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Nhà cung cấp: ${item?.IsPublic ? "Sản phẩm chung" : "Cá nhân/Doanh nghiệp"}`}</Text>
                    <Text style={{ ...TypoSkin.body3, color: ColorThemes.light.neutral_text_subtitle_color }}>{`Ngày tạo: ${item?.DateCreated ? Ultis.datetoString(new Date(item.DateCreated), "dd/MM/yyyy hh:mm") : "-"}`}</Text>
                </View>}
                trailing={<View style={{ alignItems: "center", justifyContent: "flex-start", }}>
                    {checkEditable ? <Winicon src="outline/user interface/trash-can" size={16} onClick={() => {
                        showDialog({
                            ref: dialogDelAccRef,
                            status: ComponentStatus.WARNING,
                            title: "Bạn chắc chắn muốn xóa",
                            onSubmit: async () => {
                                const controller = new DataController("Toilet")
                                // controller.delete(item.Id).then(res => {
                                //     if (res.code === 200) {
                                //         showSnackbar({ message: "Xóa " + item?.Name.toLowerCase() + " thành công!", status: ComponentStatus.SUCCSESS })
                                //         setManagerData((w: any) => ({ data: w.data.filter((e: any) => e.Id !== item.Id), totalCount: w.totalCount }))
                                //     }
                                // })
                            }
                        })
                    }} /> : <Winicon src="outline/layout/ban" size={16} />}
                </View>}
            />
        </View>
    );
}


export const StatusToiletData = [
    { key: ToiletStatus.register, title: "Đăng ký", backgrColor: ColorThemes.light.neutral_text_subtitle_color, color: ColorThemes.light.neutral_absolute_background_color },
    { key: ToiletStatus.consultant, title: "Tư vấn", backgrColor: ColorThemes.light.primary_main_color, color: ColorThemes.light.neutral_absolute_background_color },
    { key: ToiletStatus.contract, title: "Hợp đồng", backgrColor: ColorThemes.light.warning_main_color, color: ColorThemes.light.neutral_absolute_background_color },
    { key: ToiletStatus.design, title: "Thiết kế", backgrColor: ColorThemes.light.success_main_color, color: ColorThemes.light.neutral_absolute_background_color },
    { key: ToiletStatus.build, title: "Thi công", backgrColor: ColorThemes.light.secondary1_main_color, color: ColorThemes.light.neutral_absolute_background_color },
    { key: ToiletStatus.liquid, title: "Thanh lý hợp đồng", backgrColor: ColorThemes.light.secondary2_main_color, color: ColorThemes.light.neutral_absolute_background_color },
    { key: ToiletStatus.run, title: "Vận hành", backgrColor: ColorThemes.light.secondary3_main_color, color: ColorThemes.light.neutral_absolute_background_color },
]


export function Status({ item, status }: any) {
    if (!status) return <View></View>
    var st = StatusToiletData.find(e => e.key === status)

    if (!st) return <View style={{ backgroundColor: ColorThemes.light.neutral_text_subtitle_color, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 20 }}>
        <Text style={{ color: ColorThemes.light.neutral_absolute_background_color }}>{status}</Text>
    </View>
    if (st) return <View style={{ backgroundColor: st.backgrColor, paddingHorizontal: 8, paddingVertical: 4, borderRadius: 20 }}>
        <Text style={{ color: st.color ?? ColorThemes.light.neutral_text_title_color }}>{st.title ?? status}</Text>
    </View>
}

