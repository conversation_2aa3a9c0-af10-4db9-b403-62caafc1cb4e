import {useState, useEffect, useCallback} from 'react';
import {useForm} from 'react-hook-form';
import {
  useSelectorCustomerState,
  useSelectorCustomerCompanyState,
  useSelectorToiletState,
} from 'redux/hooks/hooks';
import {CustomerType} from 'redux/reducers/user/da';
import {DataController} from 'screen/base-controller';
import {BaseDA} from 'screen/baseDA';
import {showSnackbar} from 'component/export-component';
import {ComponentStatus} from 'component/component-status';
import ConfigAPI from 'config/configApi';
import {Ultis} from 'utils/Utils';
import {TicketType as TicketTypeInterface} from 'types/ticketType';

export const useFetchTickets = () => {
  const user = useSelectorCustomerState().data;
  const company = useSelectorCustomerCompanyState().data;
  const {onLoading, myToilet} = useSelectorToiletState();
  const methods = useForm({shouldFocusError: false});

  const [isLoading, setLoading] = useState(true);
  const [assignees, setAssignees] = useState<Array<any>>([]);
  const [data, setData] = useState<any>({
    data: Array<any>(),
    totalCount: undefined,
  });
  const [relatives, setRelatives] = useState<Array<any>>([]);

  const ticketController = new DataController('Ticket');

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      let query = '';

      if (company?.Id === ConfigAPI.ktxCompanyId) {
        query += ` @Ktx:{true}`;
      } else {
        let servicesIds;
        if (user?.Type === CustomerType.partner) {
          const toiletServicesController = new DataController('ToiletServices');
          const toiletServiceIds = await toiletServicesController.group({
            searchRaw: `@CustomerId:{${user.Id}} @Status:[1 +inf]`,
            reducers: `LOAD * GROUPBY 0 REDUCE TOLIST 1 @Id AS ids`,
          });
          if (toiletServiceIds.code === 200)
            servicesIds = toiletServiceIds.data[0]?.ids;
        }

        if (servicesIds?.length && myToilet?.length)
          query += `((@ToiletId:{${myToilet.map(e => e.Id).join(' | ')}}) | (@ToiletServicesId:{${servicesIds.join(' | ')}}))`;
        else if (servicesIds?.length)
          query += ` @ToiletServicesId:{${servicesIds.join(' | ')}}`;
        else if (myToilet?.length)
          query += ` @ToiletId:{${myToilet.map(e => e.Id).join(' | ')}}`;
        else if (company?.Id !== ConfigAPI.ktxCompanyId)
          return setData({data: [], totalCount: undefined});
        else return setData({data: [], totalCount: undefined});
      }

      const res = await ticketController.aggregateList({
        page: 1,
        size: 1000,
        searchRaw: query.length ? query : '*',
        sortby: [{prop: 'DateCreated', direction: 'DESC'}],
      });

      if (res.code === 200) {
        const customerIds = res.data
          .map((e: any) => e.CustomerId)
          .filter(
            (id: any, i: any, arr: string | any[]) =>
              id && arr.indexOf(id) === i,
          );

        if (customerIds.length) {
          const customerController = new DataController('Customer');
          const cusRes = await customerController.getByListId(customerIds);
          if (cusRes.code === 200)
            setAssignees(
              cusRes.data.map((e: any) => ({
                ...e,
                bgColor: Ultis.generateRandomColor(),
              })),
            );
        }

        const _fileId = methods.getValues('_files') ?? [];
        const _tmpFileIds = res.data
          .map((e: any) => e?.File?.split(','))
          ?.flat(Infinity)
          .filter(
            (id: any) =>
              id?.length &&
              _fileId.every((e: any) => e.Id !== id) &&
              !id.startsWith('http'),
          );

        const resFile = await BaseDA.getFilesInfor(_tmpFileIds);
        if (resFile.code === 200)
          methods.setValue('_files', [
            ..._fileId,
            ...resFile.data.filter((e: any) => e !== undefined && e !== null),
          ]);

        setData({data: res.data, totalCount: res.totalCount});
      }
    } catch (error) {
      showSnackbar({
        message: 'Lỗi khi lấy dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  }, [company, user, myToilet, methods]);

  const fetchRelatives = useCallback(async () => {
    const toiletIds = data.data
      .map((e: any) => e.ToiletId)
      .filter(
        (id: any, i: any, arr: string | any[]) => id && arr.indexOf(id) === i,
      );
    const toiletServicesIds = data.data
      .map((e: any) => e.ToiletServicesId)
      .filter(
        (id: any, i: any, arr: string | any[]) => id && arr.indexOf(id) === i,
      );

    const toiletController = new DataController('Toilet');
    const toiletServicesController = new DataController('ToiletServices');
    const results = [];

    if (toiletIds.length) {
      const res = await toiletController.getByListId(toiletIds);
      if (res.code === 200) results.push(...res.data);
    }

    if (toiletServicesIds.length) {
      const res = await toiletServicesController.getByListId(toiletServicesIds);
      if (res.code === 200) results.push(...res.data);
    }

    setRelatives(results);
  }, [data.data]);

  const onUpdateTicket = useCallback((updatedTicket: TicketTypeInterface) => {
    setData((prevData: any) => ({
      ...prevData,
      data: prevData.data.map((ticket: any) =>
        ticket.Id === updatedTicket.Id ? updatedTicket : ticket,
      ),
    }));
  }, []);

  useEffect(() => {
    if (user && !onLoading) {
      fetchData();
    }
  }, [user, onLoading, fetchData]);

  useEffect(() => {
    if (data.data.length) {
      fetchRelatives();
    }
  }, [data.data.length, fetchRelatives]);

  return {
    isLoading,
    assignees,
    data,
    relatives,
    methods,
    onUpdateTicket,
  };
};

