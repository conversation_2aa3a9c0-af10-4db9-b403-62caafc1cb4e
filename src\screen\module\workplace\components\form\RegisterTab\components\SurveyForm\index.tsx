import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {TypoSkin} from '../../../../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../../../../assets/skin/colors';
import {TabNavigation} from './components/TabNavigation';
import ListToiletCriterion from '../SurveyInfo/components/ListToiletCriterion';
import {useSurveyForm} from './hooks/useSurveyForm';
import ToiletDeviceList from '../../../../../../toilet/view/detailProject/ToiletDeviceList';
import {
  CateServicesType,
  ToiletServiceStatus,
} from '../../../../../../service/components/da';
import ToiletBioList from '../../../../../../toilet/view/detailProject/ToiletBioList';
import ToiletOverviewList from './components/ToiletOverviewList';

interface SurveyFormProps {
  methods: any;
  serviceData: any;
  uploadFiles: (files: any[]) => Promise<void>;
  initSurvey: any;
}

export const SurveyForm: React.FC<SurveyFormProps> = ({
  methods,
  serviceData,
  uploadFiles,
  initSurvey,
}) => {
  const [listTab, setListTab] = useState([
    'Tổng quan',
    'Thiết bị',
    'C/P sinh học',
  ]);
  const {tab, setTab} = useSurveyForm({
    methods,
    uploadFiles,
    initSurvey,
  });

  useEffect(() => {
    if (serviceData.CateServicesId === CateServicesType.netzero) {
      setListTab(prev => [...prev, 'Tiêu chí NVS']);
    }
  }, [serviceData]);

  const renderTabContent = () => {
    switch (tab) {
      case 0:
        return (
          <ToiletOverviewList
            toiletIds={serviceData.ToiletId.split(',')}
            dataService={serviceData}
          />
        );
      case 1:
        return (
          <ToiletDeviceList
            toiletIds={serviceData.ToiletId.split(',')}
            toiletServiceStatus={serviceData.Status}
          />
        );
      case 2:
        return (
          <ToiletBioList
            toiletIds={serviceData.ToiletId.split(',')}
            toiletServiceStatus={serviceData.Status}
          />
        );
      case 3:
        return (
          <ListToiletCriterion
            toiletServiceId={serviceData.Id}
            toiletServiceStatus={serviceData.Status}
          />
        );
      default:
        return <View />;
    }
  };

  return (
    <View style={styles.container}>
      {/* Popup is still available via popupRef if needed by nested components */}
      <Text style={styles.title}>Thông tin khảo sát</Text>
      <TabNavigation tabs={listTab} activeTab={tab} onTabChange={setTab} />
      {renderTabContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 16,
    paddingBottom: 150,
    paddingTop: 16,
  },
  title: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
  },
});
